server:
  port: 9082
  servlet:
    context-path: /
    session:
      timeout: 14400s
  tomcat:
    relaxed-path-chars:
      - "["
      - "]"
      - "|"
      - "{"
      - "}"
      - "^"
      - "`"
      - "\""
      - "<"
      - ">"
    relaxed-query-chars:
      - "["
      - "]"
      - "|"
      - "{"
      - "}"
      - "^"
      - "`"
      - "\""
      - "<"
      - ">"
spring:
  application:
    name: supX-web-business
  main:
    allow-bean-definition-overriding: true
  web:
    resources:
      static-locations: classpath:/static/newzui/,classpath:/static/
  # 数据源配置 - 仅用于特殊场景，主要数据访问通过Dubbo调用supx_csp
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: *************************************************
    username: supX
    password: SupStarRise
    # 最小化连接池配置，因为主要通过Dubbo访问数据
    initial-size: 1              # 最小初始连接数
    max-active: 5                # 最小最大连接数
    min-idle: 1                  # 最小空闲连接数
    max-wait: 5000               # 减少等待时间
    use-unfair-lock: true
    validation-query: SELECT 1 FROM DUAL
    test-on-borrow: false
    test-on-return: false
    test-while-idle: true
    time-between-eviction-runs-millis: 300000  # 增加检查间隔
    min-evictable-idle-time-millis: 600000     # 增加空闲时间
    filters: stat
  servlet:
    multipart:
      max-file-size: 1000MB
      max-request-size: 1000MB
  mvc:
    servlet:
      path: /

# Dubbo配置 - 优化消费者连接配置
# supX_web作为Dubbo消费者，通过RPC调用supX_csp提供的数据服务
dubbo:
  application:
    name: ${spring.application.name}
    qos-enable: false
  registry:
    address: zookeeper://************:2181
    client: curator
    file: ${user.home}/output/dubbo.cache
    timeout: 30000              # 注册中心超时时间
    session: 60000
    check: false                # 启动时不检查注册中心
  # protocol部分去掉host和port，只做消费者无需配置
  consumer:
    check: false                # 启动时不检查服务提供者
    lazy: false                 # 关闭懒加载，启动时就建立连接
    timeout: 30000              # 消费者调用超时时间
    retries: 0                  # 不重试，避免重复操作
    loadbalance: leastactive    # 最少活跃数负载均衡
    connections: 2              # 每个服务提供者的连接数建议1-2
    sticky: false               # 不启用粘性连接
    # 新增连接池优化配置
    threads: 100                # 工作线程数
    queues: 500                 # 队列大小
    # 新增性能优化配置
    filter: -exception          # 移除异常过滤器，提高性能
    listener: -deprecated       # 移除废弃监听器
  # provider部分去掉host
  scan:
    # 扫描Dubbo相关注解，主要是@DubboReference
    base-packages: com.supx.web

# 日志配置 - 优化版，减少冗长输出
logging:
  level:
    # 根日志级别
    root: INFO

    # supX相关包调整为INFO级别，减少冗长日志
    com.supx: INFO
    com.supx.web: INFO
    com.supx.web.business: INFO
    com.supx.web.business.service: WARN
    com.supx.web.business.controller: INFO
    com.supx.service: INFO
    com.supx.comm: WARN

    # MyBatis相关 - 只显示错误
    org.mybatis: WARN
    org.mybatis.spring: WARN
    org.mybatis.spring.mapper: WARN
    com.ibatis: WARN

    # SQL相关 - 只显示错误
    java.sql.Connection: WARN
    java.sql.Statement: WARN
    java.sql.PreparedStatement: WARN
    java.sql.ResultSet: WARN

    # Spring相关 - 只显示重要信息，减少启动日志
    org.springframework: WARN
    org.springframework.web: WARN
    org.springframework.boot: WARN              # 减少Spring Boot启动日志
    org.springframework.context: WARN           # 减少上下文初始化日志
    org.springframework.beans: WARN             # 减少Bean创建日志

    # Dubbo相关 - 只显示重要信息
    org.apache.dubbo: WARN
    org.apache.dubbo.config: WARN               # 减少Dubbo配置日志
    org.apache.dubbo.registry: WARN             # 减少注册中心日志

    # ZooKeeper相关 - 只显示错误信息
    org.apache.zookeeper: ERROR
    org.apache.zookeeper.ClientCnxn: ERROR
    org.apache.curator: ERROR
    # 其他框架
    com.danga.MemCached: WARN                   # 减少Memcached日志

  pattern:
    # 控制台输出格式 - 增加更多信息
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}:%line] - %msg%n"
    # 文件输出格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}:%line] - %msg%n"
  file:
    name: logs/supX-web-business.log
  logback:
    rollingpolicy:
      max-file-size: 100MB
      max-history: 30

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized

# 自定义业务参数
supx:
  sys:
    num: "05"
    code: "050001"
    zcm: cA7u4E7DGaYNiZsFu0vIMAtcgwBlUVGecGuP7d+kMkLMKM53avLzu/V7QM9KR9/6      # 本机注册码
  hospname: 超星研发中心
  redis:
    ip: 127.0.0.1
    port: 6379
  cdyb:
    ip: http://*************:8082
  lis-server-ip: ***********
  sequence:
    client-id: 01
    web-req:
      begin: 100000
      end: 999999

