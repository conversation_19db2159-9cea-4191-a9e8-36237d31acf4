<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
	<title>WEB打印分页</title>
	<style type="text/css">
		.pagebreak
		{
			page-break-after:always;
		}
	</style>
	<script type="text/javascript">
		function funcPrint()
		{
			window.print();
		}
	</script>
</head>

<body>
<div>
	<div>
		<div class="pagebreak">这是第一页的内容</div>

		<div class="pagebreak">这是第二页的内容</div>


		<div class="pagebreak">这是第三页的内容</div>
	</div>
</div>

<input type="button" οnclick="funcPrint()" value="打印"/>
</body>
</html>