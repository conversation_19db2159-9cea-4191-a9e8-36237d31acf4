<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>测试</title>
		<script type="application/javascript" src="/newzui/pub/top.js"></script>
		<script type="text/javascript">
			var reportlets = "";
			var zyyjSize = "";
			var ZyPrint = "";
			function zyjlPrint(){
                reportlets = "[{reportlet: 'fpdy%2Fzygl%2Fzygl_zyyjjl.cpt',yljgbm:'000001',zyh:'2018010000',fphm:'000083'}]" ;
                zyyjSize = "120,97";
                ZyPrint = "zyyjprint";
				FrPrints(reportlets,ZyPrint,zyyjSize);
			};
			function zyjsPrint(){
                reportlets = "[{reportlet: 'fpdy%2Fzygl%2Fzygl_zyfyjspj.cpt',yljgbm:'000001',zyh:'2018000008'}]" ;
                zyyjSize = "210,80";
                ZyPrint = "PdfPrint";
				FrPrints(reportlets,ZyPrint,zyyjSize);
			};
			function FrPrintss(reportlets,printerName,paperSize) {
			    var printurl = "/FR/ReportServer";
			    //var reportlets ="[{reportlet: 'YK%2FYK_RKD.cpt',DJHM:'RKD1201811000002',yljgbm:'000001'}]";
			    var config = {
			        printUrl: printurl,
			        isPopUp: true,
			        // 是否弹出设置窗口，true为弹出，false为不弹出
			        data: {
			            reportlets: reportlets // 需要打印的模版列表
			        },
			        printType: 1, // 打印类型，0为零客户端打印，1为本地打印
			        // 以下为零客户端打印的参数，仅当 printType 为 0 时生效
			        ieQuietPrint: false,// IE静默打印设置 true为静默，false为不静默
			        // 以下为本地打印的参数，仅当 printType 为 1 时生效
			        //printerName : 'Microsoft Print to PDF', // 打印机名
			        pageType: 0, // 打印页码类型：0：所有页，1：当前页，2：指定页
			        //pageIndex: '1-3', // 页码范围。当 pageType 为 2 时有效
			        copy: 1, // 打印份数
			        //页面大小
			        //paperSizeText : "50,30",
			    };

			    if (printerName != undefined && printerName != null && printerName != ""){
			    	config.printerName = printerName;
			    }
			    if (paperSize != undefined && paperSize != null && paperSize != ""){
			    	config.paperSizeText = paperSize;
			    }
			    console.log(config);
			    if (window.FR){
			    	window.FR.doURLPrint(config);
			    	return true;
			    }else{
			    	return false;
			    }
			}

	    </script>
	</head>
	<body>
		<!--<label>请输入WebSocket参数</label><br/>
		<input id="sendText" type="text" value='' style="width: 800px;"/><br/>-->
		<input type="button" value="预交打印" onclick="zyjlPrint()"/>
		<input type="button" value="结算打印" onclick="zyjsPrint()"/>
		<pre id="incomming"></pre>
	</body>
</html>
