<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>supX_web</artifactId>
		<groupId>com.supx.web</groupId>
		<version>0.0.1</version>
	</parent>
	<artifactId>supX_web_business</artifactId>
	<packaging>war</packaging>
	<name>supX_web_business</name>
	<url>http://maven.apache.org</url>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.supx.web</groupId>
			<artifactId>supX_web_api</artifactId>
			<version>0.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.supx.csp</groupId>
			<artifactId>supX_csp_api</artifactId>
			<version>0.0.1</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3.1</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi</artifactId>
		    <version>3.9</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi-ooxml</artifactId>
		    <version>3.9</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.directory.studio</groupId>
		    <artifactId>org.dom4j.dom4j</artifactId>
		    <version>1.6.1</version>
		</dependency>

	    <dependency>
	      <groupId>org.apache.commons</groupId>
	      <artifactId>commons-pool2</artifactId>
	      <version>2.2</version>
	    </dependency>

		<!-- iReport JasperReports -->
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
			<version>5.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>2.2.2</version>
		</dependency>

		<dependency>
    		<groupId>org.fusesource</groupId>
    		<artifactId>sigar</artifactId>
    		<version>1.6.4</version>
	   </dependency>

		<!-- tike解析 -->
		<dependency>
    		<groupId>org.apache.tika</groupId>
    		<artifactId>tika-parsers</artifactId>
    		<version>1.17</version>
    		<exclusions>
    			<exclusion>
    				<groupId>org.bouncycastle</groupId>
    				<artifactId>bcprov-jdk15on</artifactId>
    			</exclusion>
    			<exclusion>
    				<groupId>org.bouncycastle</groupId>
    				<artifactId>bcmail-jdk15on</artifactId>
    			</exclusion>
    			<exclusion>
    				<groupId>org.bouncycastle</groupId>
    				<artifactId>bcpkix-jdk15on</artifactId>
    			</exclusion>
    		</exclusions>
		</dependency>

		 <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>1.17</version>
        </dependency>

        <!-- 显式添加新版本BouncyCastle依赖 -->
        <dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.70</version>
		</dependency>

        <!-- 转换文件依赖 -->
        <dependency>
   			<groupId>org.apache.pdfbox</groupId>
    		<artifactId>jbig2-imageio</artifactId>
    		<version>3.0.0</version>
		</dependency>

		<dependency>
    		<groupId>com.github.jai-imageio</groupId>
    		<artifactId>jai-imageio-core</artifactId>
    		<version>1.4.0</version>
		</dependency>

		<dependency>
    		<groupId>com.github.jai-imageio</groupId>
    		<artifactId>jai-imageio-jpeg2000</artifactId>
    		<version>1.3.0</version>
		</dependency>

		  <dependency>
		      <groupId>com.levigo.jbig2</groupId>
		      <artifactId>levigo-jbig2-imageio</artifactId>
		      <version>2.0</version>
		  </dependency>

		<dependency>
   			 <groupId>org.xerial</groupId>
   			 <artifactId>sqlite-jdbc</artifactId>
    		 <version>********</version>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
			<version>3.1.6</version>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
			<version>3.1.6</version>
		</dependency>
		<!--Alipay
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.10</version>
		</dependency>

		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>
		 <dependency>
          <groupId>com.alipay</groupId>
          <artifactId>sdk-java</artifactId>
          <version>20161213173952</version>
   		 </dependency>
   		  <dependency>
          <groupId>com.alipay</groupId>
          <artifactId>trade-sdk</artifactId>
          <version>20161215</version>
   		 </dependency>
		 <dependency>
     	 	<groupId>commons-configuration</groupId>
      		<artifactId>commons-configuration</artifactId>
      		<version>1.10</version>
    	</dependency>
   		 <dependency>
      		  <groupId>commons-logging</groupId>
      		  <artifactId>commons-logging</artifactId>
      		  <version>1.1.1</version>
    	</dependency>
    	<dependency>
     		  <groupId>com.google.zxing</groupId>
      		  <artifactId>core</artifactId>
      		  <version>2.1</version>
    	</dependency>
    	 <dependency>
      	 		<groupId>org.hamcrest</groupId>
         		<artifactId>hamcrest-core</artifactId>
        		<version>1.3</version>
    	</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.3.1</version>
		</dependency>
		-->
	</dependencies>
	<build>
		<finalName>supX_web_business</finalName>
		<plugins>
			<!-- jetty插件 -->
<!--			<plugin>-->
			<!--				<groupId>org.eclipse.jetty</groupId>-->
			<!--				<artifactId>jetty-maven-plugin</artifactId>-->
			<!--				<version>9.2.6.v20141205</version>-->
			<!--				<configuration>-->
			<!--					<httpConector>-->
			<!--					<port>9082</port>-->
			<!--					</httpConector>-->
			<!--					<webApp>-->
			<!--					<contextPath>/bg</contextPath>-->
			<!--					</webApp>-->
			<!--					<scanIntervalSeconds>10</scanIntervalSeconds>-->
			<!--					<stopKey>foo</stopKey>-->
			<!--					<stopPort>9999</stopPort>-->
			<!--				</configuration>-->
			<!--			</plugin>-->
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat7-maven-plugin</artifactId>
				<version>2.2</version>
				<configuration>
					<!-- 默认访问路径为：http://localhost:8080/${project.name} 下面指定访问路径为： http://localhost:8080 -->
					<server>tomcat8</server>
					<path>/ROOT</path>
					<username>admin</username>
					<password>admin</password>
				</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-resources-plugin</artifactId>
					<!-- <version>3.0.2</version> -->
					<configuration>
					<encoding>UTF-8</encoding>
					</configuration>
				</plugin>

				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>2.3.2</version>
                    <configuration>
                        <source>8</source>
                        <target>8</target>
                    </configuration>
                </plugin>

				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
					</configuration>
				</plugin>

		</plugins>
	</build>
</project>
