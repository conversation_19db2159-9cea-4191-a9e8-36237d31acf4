<!DOCTYPE html>
<html>
<head>
<title>test</title>
<meta content="" name="keywords" />
<meta content="" name="description" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<script type="text/javascript" src="../resource/scripts/ajax-pushlet-client.js"></script>
</head>
<body>
<script type="text/javascript">
 
  //pushlet
  if(PL.sessionId == null){
      PL.userId = '110';
      PL._init();
      PL.joinListen('/pms/bxService');//事件标识 在数据源中引用
      function onData(event){
            alert("sessionID==" + PL.sessionId + "====" + event.get('msg_110'));
      }
  }

</script>
</body>
</html>