<?xml version="1.0" encoding="UTF-8" ?>

<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
    version="2.0">
      
  <description>XWTECH 1.1 core library</description>
  <display-name>XWTECH core</display-name>
  <tlib-version>1.0</tlib-version>
  <short-name>pub</short-name>
  <uri>http://www.xwtech.com/jsp/jstl/pub</uri>
  
  <tag>
    <name>turn_page</name>
    <tag-class>com.xwtech.ecu.util.tag.TurnPageTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
      <name>intPageCurrent</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>intPageSize</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>intTotalPage</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>intTotalCount</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>strHandlePath</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>strPageParameter</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  
  
  
  <tag>
    <name>list_css</name>
    <tag-class>com.xwtech.ecu.util.tag.ListCssTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
      <name>intStatusIndex</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
 
  <tag>
    <description></description>
    <name>common_model</name>
    <tag-class>com.xwtech.ecu.util.tag.CommonModelTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
      <name>functionName</name>
      <required>true</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    
    <attribute>
      <name>functionModel</name>
      <required>true</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    
    <attribute>
      <name>idOrTeamId</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    
    <attribute>
      <name>maxNumber</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>

    <attribute>
      <name>spaceCode</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    
    <attribute>
      <name>showCurrent</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    
    <attribute>
      <name>allowBlank</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    
    <attribute>
      <name>styleCode</name>
      <required>false</required>
      <rtexprvalue>false</rtexprvalue>
    </attribute>
    
    <attribute>
      <name>paramCode</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    
  </tag>
  
</taglib>