<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE taglib PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN" "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">

<taglib>
	<tlib-version>2.0</tlib-version>
	<jsp-version>1.2</jsp-version>
	<short-name>bigpipe</short-name>
	<uri>http://www.md.com/tags/bigpipe</uri>
	<description></description>
	
	<tag>
		<name>bigpipe</name>
		<tag-class>com.md.web.api.blockoutput.taglib.BigPipeTag</tag-class>
		<body-content>JSP</body-content>
		<description></description>
		<attribute>
			<name>pageletNum</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>包含的Pagelet数量</description>
		</attribute>
		<attribute>
			<name>pipeType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>igPipe输出类型， 1：并行（默认） 2：串行</description>
		</attribute>
	</tag>
	
	<tag>
		<name>pagelet</name>
		<tag-class>com.md.web.api.blockoutput.taglib.PageletTag</tag-class>
		<body-content>empty</body-content>
		<description></description>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>Pagelet ID</description>
		</attribute>
		<attribute>
			<name>containerId</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<description>HTML容器ID</description>
		</attribute>
		<attribute>
			<name>pageletType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>Pagelet类型，取值为：1(业务) | 2(默认值，非业务)</description>
		</attribute>
	</tag>
</taglib>
