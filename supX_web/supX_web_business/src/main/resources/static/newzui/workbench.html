<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>工作台</title>
    <link href="css/iframe.css" rel="stylesheet" />
    <link href="workbench.css" rel="stylesheet" />
    <link href="css/common.css" rel="stylesheet" />
    <link href="/newzui/js/zui/zui.min.css" rel="stylesheet" type="text/css" />
    <link href="pub/new.css" rel="stylesheet">
    <script src="js/jquery.min.js"></script>
    <script src="js/iframe.min.js"></script>
    <script src="js/contabs.min.js"></script>
    <script src="/lib/vue/vue.min.js"></script>
    <script src="/lib/vue/vue-resource.min.js"></script>
    <script src="js/zui/jquery.zui.js"></script>
    <script src="js/layer/layer.js"></script>
    <script src="/newzui/pub/js/common.js"></script>
    <script src="/newzui/pub/js/components.js"></script>
    <script src="/newzui/pub/js/dictionaries.js"></script>
    <script src="/newzui/pub/js/highcharts.js"></script>
    <script src="/newzui/pub/js/echarts.min.js"></script>
    <script type="text/javascript">
        common.openloading('#workbench')
    </script>
</head>

<body style="overflow: auto">
    <div id="workbench" v-cloak>
        <div class="grid-box" style="margin-bottom: 10px;" v-show="shortcutMenu.length">
            <div class="col-xxl-12">
                <div class="common_left header_text">
                    <h1 class="commom_pb"><span>快捷菜单</span></h1>
                    <div class="commom_flex commom_canvas">
                        <div style="padding-left:10px;">
                            <button v-for="item in shortcutMenu" :key="item.ylbm" class="tong-btn-menu btn-parmary-b-menu"
                                @click="shortcutMenuClick(item)" :data-title="item.mkmc+'-'+item.ylmc">
                                <span>{{ item.mkmc }} - {{ item.ylmc }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid-box">
            <div class="col-xxl-9 col-s-9 col-l-9 col-xl-9 pd-4" v-show="roll.id076">
                <div class="common_left header_text">
                    <h1 class="commom_pb">
                        <span>入院/在院/出院统计</span>
                        <p>
                            <span class="commom_year  md_r_18" @click="actWeek(0,0),getDateNum(0),getChart(0)" :class="{'commom_active':num==0}">周</span>
                            <span class="commom_year md-r-22" @click="actWeek(1,0),getDateNum(1),getChart(1)" :class="{'commom_active':num==1}">月</span>
                        </p>
                    </h1>
                    <div class="commom_flex commom_canvas">
                        <div style="min-width: 100%">
                            <div class="canvas" id="container"></div>
                            <p class="text-center md_b_8">
                                <span class="commom_green">入院</span>
                                <span class="commom_yellwo">出院</span>
                            </p>
                            <div class="quxian_flex md_b_8">
                                <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 bg-green md_l child">
                                    <div class="col-xxl-12 col-s-12 col-l-12 col-xl-12 text-left">
                                        <p class="tb-width">当前在院&ensp;<span class="font-18">{{chartDataNum.dqzy}}</span><span
                                                class=" font-14">人</span></p>
                                    </div>
                                </div>
                                <div class="col-xxl-5 col-s-5 col-l-5 col-xl-5 green-dashed md_8 child md_l">
                                    <div class="col-xxl-6 col-s-6 col-l-6 col-xl-6 text-left">
                                        <p class="tb-width">今日入院：<span class="font-16 ysb-green">{{chartDataNum.jrry}}</span><span
                                                class="ysb-green font-14">人</span></p>
                                        <p class="tb-width">昨日入院：<span class="font-16 ysb-green">{{chartDataNum.zrry}}</span><span
                                                class="ysb-green font-14">人</span></p>
                                    </div>
                                    <div class="col-xxl-6 col-s-6 col-l-6 col-xl-6">
                                        <p class="tb-width">本周入院：<span class="font-16 ysb-green">{{chartDataNum.bzry}}</span><span
                                                class="ysb-green font-14">人</span></p>
                                        <p class="tb-width">本月入院：<span class="font-16 ysb-green">{{chartDataNum.byry}}</span><span
                                                class="ysb-green font-14">人</span></p>
                                    </div>
                                </div>
                                <div class="col-xxl-5 col-s-5 col-l-5 col-xl-5 yello-dashed child md_r_10">
                                    <div class="col-xxl-6 col-s-6 col-l-6 col-xl-6 text-left">
                                        <p class="tb-width">今日出院：<span class="font-16 ysb-yellow">{{chartDataNum.jrcy}}</span><span
                                                class="ysb-yellow font-14">人</span></p>
                                        <p class="tb-width">昨日出院：<span class="font-16 ysb-yellow">{{chartDataNum.zrcy}}</span><span
                                                class="ysb-yellow font-14">人</span></p>
                                    </div>
                                    <div class="col-xxl-6 col-s-6 col-l-6 col-xl-6">
                                        <p class="tb-width">本周出院：<span class="font-16 ysb-yellow">{{chartDataNum.bzcy}}</span><span
                                                class="ysb-yellow font-14">人</span></p>
                                        <p class="tb-width">本月出院：<span class="font-16 ysb-yellow">{{chartDataNum.bycy}}</span><span
                                                class="ysb-yellow font-14">人</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 " >
                <div v-show="false" class="col-xxl-12 col-s-12 col-l-12 col-xl-12 pd_b_10 ">
                    <div class="common_left header_text">
                        <!--<h1 class="commom_pb op-0">-->
                        <!--本周排班表-->
                        <!--</h1>-->
                        <div class="commom_flex canvasone">
                            <div id="containerone" class=""></div>
                        </div>
                    </div>
                </div>
                <div v-show="false" class="col-xxl-12 col-s-12 col-l-12 col-xl-12 ">
                    <div class="common_left header_text">
                        <h1 class="commom_pb">今日手术</h1>
                        <div class="commom_flex" style="height: 104px;">
                            <div class="text line ysb-green">
                                <p><span class="num_text">0</span>台</p>
                                <p class="commom_text">今日手术</p>
                            </div>
                            <div class="text line ysb-red">
                                <p><span class="num_text">0</span>人</p>
                                <p class="commom_text">未执行</p>
                            </div>
                            <div class="text line ysb-blue">
                                <p><span class="num_text">0</span>人</p>
                                <p class="commom_text">已执行</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--门诊收费-->
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id001">
                <div class="common_left header_text">
                    <h1>收费</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress001}" class="whiteSpace" @click="newPage(mc.mc001,toaddressObj.toaddress001,urlObj.urljrsf)"><span
                                    class="num_text">{{numText.numjrsf}}</span>人</p>
                            <p class="commom_text">今日收费</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress001}" class="whiteSpace" @click="newPage(mc.mc001,toaddressObj.toaddress001,urlObj.urljrsf)"><span
                                    class="num_text">{{numText.numjrtf}}</span>人</p>
                            <p class="commom_text">今日退费</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress001}" class="whiteSpace" @click="newPage(mc.mc001,toaddressObj.toaddress001,urlObj.urljrsf)"><span
                                    class="num_text">{{numText.numdsf}}</span>人</p>
                            <p class="commom_text">待收费</p>
                        </div>
                        <div class="text line ysb_zs">
                            <p :class="{'hongdian':!toaddressObj.toaddress001}" class="whiteSpace" @click="newPage(mc.mc001,toaddressObj.toaddress001,urlObj.urljrsf)"><span
                                    class="num_text">{{numText.numysf}}</span>人</p>
                            <p class="commom_text">已收费</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id002">
                <div class="common_center header_text">
                    <h1>收费统计</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress002}" class="whiteSpace" @click="newPage(mc.mc002,toaddressObj.toaddress002,urlObj.urljrsfzje)"><span
                                    class="num_text">{{numText.numjrsfzje}}</span>元</p>
                            <p class="commom_text">今日收费总金额</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress002}" class="whiteSpace" @click="newPage(mc.mc002,toaddressObj.toaddress002,urlObj.urljrsfzje)"><span
                                    class="num_text">{{numText.numjrtfzj}}</span>元</p>
                            <p class="commom_text">今日退费总结</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id003">
                <div class="common_center header_text">
                    <h1>门诊上缴</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress003}" class="whiteSpace" @click="newPage(mc.mc003,toaddressObj.toaddress003,urlObj.urldsjzje)"><span
                                    class="num_text">{{numText.numdjszje}}</span>元</p>
                            <p class="commom_text">待上缴总金额</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress003}" class="whiteSpace" @click="newPage(mc.mc003,toaddressObj.toaddress003,urlObj.urldsjxj)"><span
                                    class="num_text">{{numText.numdsjxj}}</span>元</p>
                            <p class="commom_text">待上缴现金</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2" v-if="roll.id004">
                <div class="common_right header_text">
                    <h1>划价</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress004}" class="whiteSpace" @click="newPage(mc.mc004,toaddressObj.toaddress004,urlObj.urljrhj)"><span
                                    class="num_text">{{numText.numjrhj}}</span>人</p>
                            <p class="commom_text">今日划价</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--挂号首页-->
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id007">
                <div class="common_left header_text">
                    <h1>挂号</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress007}" class="whiteSpace" @click="newPage(mc.mc007,toaddressObj.toaddress007,urlObj.urlgh)"><span
                                    class="num_text">{{numText.numjrgh}}</span>人</p>
                            <p class="commom_text">今日挂号</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress007}" class="whiteSpace" @click="newPage(mc.mc007,toaddressObj.toaddress007,urlObj.urlgh)"><span
                                    class="num_text">{{numText.numbzgh}}</span>人</p>
                            <p class="commom_text">本周挂号</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress007}" class="whiteSpace" @click="newPage(mc.mc007,toaddressObj.toaddress007,urlObj.urlgh)"><span
                                    class="num_text">{{numText.numbygh}}</span>人</p>
                            <p class="commom_text">本月挂号</p>
                        </div>
                        <div class="text line ysb_zs">
                            <p :class="{'hongdian':!toaddressObj.toaddress007}" class="whiteSpace" @click="newPage(mc.mc007,toaddressObj.toaddress007,urlObj.urlgh)"><span
                                    class="num_text">{{numText.numzrgh}}</span>人</p>
                            <p class="commom_text">昨日挂号</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id008">
                <div class="common_center header_text">
                    <h1>退号</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress008}" class="whiteSpace" @click="newPage(mc.mc008,toaddressObj.toaddress008,urlObj.urlth)"><span
                                    class="num_text">{{numText.numjrth}}</span>人</p>
                            <p class="commom_text">今日退号</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress008}" class="whiteSpace" @click="newPage(mc.mc008,toaddressObj.toaddress008,urlObj.urlth)"><span
                                    class="num_text">{{numText.numbzth}}</span>人</p>
                            <p class="commom_text">本周退号</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress008}" class="whiteSpace" @click="newPage(mc.mc008,toaddressObj.toaddress008,urlObj.urlth)"><span
                                    class="num_text">{{numText.numbyth}}</span>人</p>
                            <p class="commom_text">本月退号</p>
                        </div>
                        <div class="text line ysb-zs">
                            <p :class="{'hongdian':!toaddressObj.toaddress008}" class="whiteSpace" @click="newPage(mc.mc008,toaddressObj.toaddress008,urlObj.urlth)"><span
                                    class="num_text">{{numText.numzrth}}</span>人</p>
                            <p class="commom_text">昨日退号</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id009">
                <div class="common_center header_text">
                    <h1>注册</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress009}" class="whiteSpace" @click="newPage(mc.mc009,toaddressObj.toaddress009,urlObj.urlzc)"><span
                                    class="num_text">{{numText.numjrth}}</span>人</p>
                            <p class="commom_text">今日退号</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress009}" class="whiteSpace" @click="newPage(mc.mc009,toaddressObj.toaddress009,urlObj.urlzc)"><span
                                    class="num_text">{{numText.numbyzc}}</span>人</p>
                            <p class="commom_text">本月注册</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress009}" class="whiteSpace" @click="newPage(mc.mc009,toaddressObj.toaddress009,urlObj.urlzc)"><span
                                    class="num_text">{{numText.numzrzc}}</span>人</p>
                            <p class="commom_text">昨日注册</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2" v-if="roll.id010">
                <div class="common_right header_text">
                    <h1>换卡</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress010}" class="whiteSpace" @click="newPage(mc.mc010,toaddressObj.toaddress010,urlObj.urlhk)"><span
                                    class="num_text">{{numText.numbyhk}}</span>人</p>
                            <p class="commom_text">本月换卡</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress010}" class="whiteSpace" @click="newPage(mc.mc010,toaddressObj.toaddress010,urlObj.urlhk)"><span
                                    class="num_text">{{numText.numzrhk}}</span>人</p>
                            <p class="commom_text">昨日换卡</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--住院管理首页-->
        <div class="grid-box">
            <div class="col-xxl-6 col-s-6 col-l-6 col-xl-6 pd-4" v-if="roll.id012">
                <div class="common_left header_text">
                    <h1>入院</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress012}" class="whiteSpace" @click="newPage(mc.mc012,toaddressObj.toaddress012,urlObj.urlry)"><span
                                    class="num_text">{{numText.numjrry}}</span>人</p>
                            <p class="commom_text">今日入院</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress012}" class="whiteSpace" @click="newPage(mc.mc012,toaddressObj.toaddress012,urlObj.urlry)"><span
                                    class="num_text">{{numText.nummzdry}}</span>人</p>
                            <p class="commom_text">门诊待入院</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress012}" class="whiteSpace" @click="newPage(mc.mc012,toaddressObj.toaddress012,urlObj.urlry)"><span
                                    class="num_text">{{numText.nummzyry}}</span>人</p>
                            <p class="commom_text">门诊已入院</p>
                        </div>
                        <div class="text line ysb_zs">
                            <p :class="{'hongdian':!toaddressObj.toaddress012}" class="whiteSpace" @click="newPage(mc.mc012,toaddressObj.toaddress012,urlObj.urlry)"><span
                                    class="num_text">{{numText.numcgry}}</span>人</p>
                            <p class="commom_text">常规入院</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6 col-s-6 col-l-6 col-xl-6 " v-if="roll.id013">
                <div class="common_center header_text">
                    <h1>出院</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress013}" class="whiteSpace" @click="newPage(mc.mc013,toaddressObj.toaddress013,urlObj.urlcy)"><span
                                    class="num_text">{{numText.numjrcy}}</span>人</p>
                            <p class="commom_text">今日出院</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress013}" class="whiteSpace" @click="newPage(mc.mc013,toaddressObj.toaddress013,urlObj.urlcy)"><span
                                    class="num_text">{{numText.numdcy}}</span>人</p>
                            <p class="commom_text">待出院</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress013}" class="whiteSpace" @click="newPage(mc.mc013,toaddressObj.toaddress013,urlObj.urlcy)"><span
                                    class="num_text">{{numText.numycy}}</span>人</p>
                            <p class="commom_text">已出院</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id014">
                <div class="common_left header_text">
                    <h1>预交</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress014}" class="whiteSpace" @click="newPage(mc.mc014,toaddressObj.toaddress014,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrsyj}}</span>人</p>
                            <p class="commom_text">今日收预交</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress014}" class="whiteSpace" @click="newPage(mc.mc014,toaddressObj.toaddress014,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdsyj}}</span>人</p>
                            <p class="commom_text">待收预交</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress014}" class="whiteSpace" @click="newPage(mc.mc014,toaddressObj.toaddress014,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyjsyj}}</span>人</p>
                            <p class="commom_text">已经收预交</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id015">
                <div class="common_center header_text">
                    <h1>费用记账</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress015}" class="whiteSpace" @click="newPage(mc.mc014,toaddressObj.toaddress015,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrfyjz}}</span>元</p>
                            <p class="commom_text">今日费用记账</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 " v-if="roll.id016">
                <div class="common_center header_text">
                    <h1>住院上缴</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress016}" class="whiteSpace" @click="newPage(mc.mc016,toaddressObj.toaddress016,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdsjzje}}</span>元</p>
                            <p class="commom_text">待上缴总金额</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress016}" class="whiteSpace" @click="newPage(mc.mc016,toaddressObj.toaddress016,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdsjxj}}</span>元</p>
                            <p class="commom_text">待上缴现金</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--药库首页-->
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id018">
                <div class="common_left header_text">
                    <h1>入库</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress018}" class="whiteSpace" @click="newPage(mc.mc018,toaddressObj.toaddress018,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrrk}}</span>次</p>
                            <p class="commom_text">今日入库</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress018}" class="whiteSpace" @click="newPage(mc.mc018,toaddressObj.toaddress018,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdrk}}</span>次</p>
                            <p class="commom_text">待入库</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress018}" class="whiteSpace" @click="newPage(mc.mc018,toaddressObj.toaddress018,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyrk}}</span>次</p>
                            <p class="commom_text">已入库</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id019">
                <div class="common_center header_text">
                    <h1>退库</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress019}" class="whiteSpace" @click="newPage(mc.mc019,toaddressObj.toaddress019,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrtk}}</span>人</p>
                            <p class="commom_text">今日退库</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress019}" class="whiteSpace" @click="newPage(mc.mc019,toaddressObj.toaddress019,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numtkz}}</span>人</p>
                            <p class="commom_text">退库中</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress019}" class="whiteSpace" @click="newPage(mc.mc019,toaddressObj.toaddress019,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numywc}}</span>人</p>
                            <p class="commom_text">已完成</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id020">
                <div class="common_center header_text">
                    <h1>出库</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress020}" class="whiteSpace" @click="newPage(mc.mc020,toaddressObj.toaddress020,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrck}}</span>人</p>
                            <p class="commom_text">今日出库</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress020}" class="whiteSpace" @click="newPage(mc.mc020,toaddressObj.toaddress020,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdby}}</span>人</p>
                            <p class="commom_text">待摆药</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress020}" class="whiteSpace" @click="newPage(mc.mc020,toaddressObj.toaddress020,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdfy}}</span>人</p>
                            <p class="commom_text">待发药</p>
                        </div>
                        <div class="text line ysb_zs">
                            <p :class="{'hongdian':!toaddressObj.toaddress020}" class="whiteSpace" @click="newPage(mc.mc020,toaddressObj.toaddress020,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyfy}}</span>人</p>
                            <p class="commom_text">已发药</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 " v-if="roll.id021">
                <div class="common_right header_text">
                    <h1>今日调价</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress021}" class="whiteSpace" @click="newPage(mc.mc021,toaddressObj.toaddress021,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrtj}}</span>人</p>
                            <p class="commom_text">今日调价</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id022">
                <div class="common_left header_text">
                    <h1>临界值警告</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress022}" class="whiteSpace" @click="newPage(mc.mc022,toaddressObj.toaddress022,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numkcjg}}</span>人</p>
                            <p class="commom_text">库存警告</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress022}" class="whiteSpace" @click="newPage(mc.mc022,toaddressObj.toaddress022,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numxqjg}}</span>人</p>
                            <p class="commom_text">效期警告</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id023">
                <div class="common_center header_text">
                    <h1>退货</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress023}" class="whiteSpace" @click="newPage(mc.mc023,toaddressObj.toaddress023,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrth}}</span>人</p>
                            <p class="commom_text">今日退货</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress023}" class="whiteSpace" @click="newPage(mc.mc023,toaddressObj.toaddress023,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numthz}}</span>人</p>
                            <p class="commom_text">退货中</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress023}" class="whiteSpace" @click="newPage(mc.mc023,toaddressObj.toaddress023,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numywc}}</span>人</p>
                            <p class="commom_text">已完成</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 " v-if="roll.id024">
                <div class="common_center header_text">
                    <h1>采购</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress024}" class="whiteSpace" @click="newPage(mc.mc024,toaddressObj.toaddress024,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrcg}}</span>人</p>
                            <p class="commom_text">今日采购</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress024}" class="whiteSpace" @click="newPage(mc.mc024,toaddressObj.toaddress024,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numshz}}</span>人</p>
                            <p class="commom_text">审核中</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress024}" class="whiteSpace" @click="newPage(mc.mc024,toaddressObj.toaddress024,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numcgz}}</span>人</p>
                            <p class="commom_text">采购中</p>
                        </div>
                        <div class="text line ysb_zs">
                            <p :class="{'hongdian':!toaddressObj.toaddress024}" class="whiteSpace" @click="newPage(mc.mc024,toaddressObj.toaddress024,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numywc}}</span>人</p>
                            <p class="commom_text">已完成</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--财务管理-->
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id026">
                <div class="common_left header_text">
                    <h1>缴费</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress026}" class="whiteSpace" @click="newPage(mc.mc026,toaddressObj.toaddress026,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrjf}}</span>元</p>
                            <p class="commom_text">今日缴费</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress026}" class="whiteSpace" @click="newPage(mc.mc026,toaddressObj.toaddress026,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.nummz}}</span>元</p>
                            <p class="commom_text">门诊</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress026}" class="whiteSpace" @click="newPage(mc.mc026,toaddressObj.toaddress026,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzy}}</span>元</p>
                            <p class="commom_text">住院</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id027">
                <div class="common_center header_text">
                    <h1>财务上缴情况</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress027}" class="whiteSpace" @click="newPage(mc.mc027,toaddressObj.toaddress027,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.nummzysj}}</span>元</p>
                            <p class="commom_text">门诊应上缴</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress027}" class="whiteSpace" @click="newPage(mc.mc027,toaddressObj.toaddress027,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzyysj}}</span>元</p>
                            <p class="commom_text">住院应上缴</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress027}" class="whiteSpace" @click="newPage(mc.mc027,toaddressObj.toaddress027,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numghysj}}</span>元</p>
                            <p class="commom_text">挂号应上缴</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id028">
                <div class="common_center header_text">
                    <h1>上缴情况</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress028}" class="whiteSpace" @click="newPage(mc.mc028,toaddressObj.toaddress028,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzsj}}</span>人</p>
                            <p class="commom_text">门诊上缴</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress028}" class="whiteSpace" @click="newPage(mc.mc028,toaddressObj.toaddress028,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdzysj}}</span>人</p>
                            <p class="commom_text">住院上缴</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress028}" class="whiteSpace" @click="newPage(mc.mc028,toaddressObj.toaddress028,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numghsj}}</span>人</p>
                            <p class="commom_text">挂号上缴</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--病案科-->
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id030">
                <div class="common_left header_text">
                    <h1>病案接收</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress030}" class="whiteSpace" @click="newPage(mc.mc030,toaddressObj.toaddress030,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numbajs}}</span>人</p>
                            <p class="commom_text">病案接收</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress030}" class="whiteSpace" @click="newPage(mc.mc030,toaddressObj.toaddress030,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdjs}}</span>人</p>
                            <p class="commom_text">待接收</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress030}" class="whiteSpace" @click="newPage(mc.mc030,toaddressObj.toaddress030,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyjs}}</span>人</p>
                            <p class="commom_text">已接收</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id031">
                <div class="common_center header_text">
                    <h1>病案审核</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress031}" class="whiteSpace" @click="newPage(mc.mc031,toaddressObj.toaddress031,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numbash}}</span>人</p>
                            <p class="commom_text">病案审核</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress031}" class="whiteSpace" @click="newPage(mc.mc031,toaddressObj.toaddress031,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdsh}}</span>人</p>
                            <p class="commom_text">待审核</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress031}" class="whiteSpace" @click="newPage(mc.mc031,toaddressObj.toaddress031,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numysh}}</span>人</p>
                            <p class="commom_text">已审核</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id032">
                <div class="common_center header_text">
                    <h1>病案借阅</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress032}" class="whiteSpace" @click="newPage(mc.mc032,toaddressObj.toaddress032,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdshck}}</span>人</p>
                            <p class="commom_text">待审核出库</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress032}" class="whiteSpace" @click="newPage(mc.mc032,toaddressObj.toaddress032,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdghrk}}</span>人</p>
                            <p class="commom_text">待归还入库</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress032}" class="whiteSpace" @click="newPage(mc.mc032,toaddressObj.toaddress032,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrjy}}</span>人</p>
                            <p class="commom_text">今日借阅</p>
                        </div>
                        <div class="text line ysb_zs">
                            <p :class="{'hongdian':!toaddressObj.toaddress032}" class="whiteSpace" @click="newPage(mc.mc032,toaddressObj.toaddress032,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrgh}}</span>人</p>
                            <p class="commom_text">今日归还</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--药房首页-->
        <div class="grid-box">
            <div class="col-xxl-4 col-s-4 col-l-4 col-xl-4 pd-4" v-if="roll.id034">
                <div class="common_left header_text">
                    <h1>病区摆药</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress034}" class="whiteSpace" @click="newPage(mc.mc034,toaddressObj.toaddress034,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdby}}</span>人</p>
                            <p class="commom_text">待摆药</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress034}" class="whiteSpace" @click="newPage(mc.mc034,toaddressObj.toaddress034,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdfy}}</span>人</p>
                            <p class="commom_text">待发药</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress034}" class="whiteSpace" @click="newPage(mc.mc034,toaddressObj.toaddress034,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyfy}}</span>人</p>
                            <p class="commom_text">已发药</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id035">
                <div class="common_center header_text">
                    <h1>处方发药</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress035}" class="whiteSpace" @click="newPage(mc.mc035,toaddressObj.toaddress035,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdfy}}</span>人</p>
                            <p class="commom_text">待发药</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress035}" class="whiteSpace" @click="newPage(mc.mc035,toaddressObj.toaddress035,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyfy}}</span>人</p>
                            <p class="commom_text">已发药</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id036">
                <div class="common_center header_text">
                    <h1>临界值警告</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress036}" class="whiteSpace" @click="newPage(mc.mc036,toaddressObj.toaddress036,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numkcjg}}</span>人</p>
                            <p class="commom_text">库存警告</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress036}" class="whiteSpace" @click="newPage(mc.mc036,toaddressObj.toaddress036,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numxqjg}}</span>人</p>
                            <p class="commom_text">效期警告</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-1 col-s-1 col-l-1 col-xl-1 pd-4" v-if="roll.id037">
                <div class="common_center header_text">
                    <h1>划价</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress037}" class="whiteSpace" @click="newPage(mc.mc037,toaddressObj.toaddress037,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrhj}}</span>人</p>
                            <p class="commom_text">今日划价</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-1 col-s-1 col-l-1 col-xl-1 " v-if="roll.id038">
                <div class="common_center header_text">
                    <h1>退药</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress038}" class="whiteSpace" @click="newPage(mc.mc038,toaddressObj.toaddress038,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrty}}</span>人</p>
                            <p class="commom_text">今日退药</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--住院医生首页/链接没写完-->
        <div class="grid-box">
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id040">
                <div class="common_left header_text">
                    <h1>今日入院</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress040}" class="whiteSpace" @click="newPage(mc.mc040,toaddressObj.toaddress040,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrry}}</span>人</p>
                            <p class="commom_text">今日入院</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress040}" class="whiteSpace" @click="newPage(mc.mc040,toaddressObj.toaddress040,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdjz}}</span>人</p>
                            <p class="commom_text">待接诊</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress040}" class="whiteSpace" @click="newPage(mc.mc040,toaddressObj.toaddress040,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyjz}}</span>人</p>
                            <p class="commom_text">已接诊</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id041">
                <div class="common_left header_text">
                    <h1>今日医嘱</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress041}" class="whiteSpace" @click="newPage(mc.mc041,toaddressObj.toaddress041,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjryz}}</span>人</p>
                            <p class="commom_text">今日医嘱</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress041}" class="whiteSpace" @click="newPage(mc.mc041,toaddressObj.toaddress041,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwcbg}}</span>人</p>
                            <p class="commom_text">未出报告</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress041}" class="whiteSpace" @click="newPage(mc.mc041,toaddressObj.toaddress041,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numycbg}}</span>人</p>
                            <p class="commom_text">已出报告</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id042">
                <div class="common_left header_text">
                    <h1>危重病人</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress042}" class="whiteSpace" @click="newPage(mc.mc042,toaddressObj.toaddress042,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwwzbr}}</span>人</p>
                            <p class="commom_text">危重病人</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress042}" class="whiteSpace" @click="newPage(mc.mc042,toaddressObj.toaddress042,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numptbr}}</span>人</p>
                            <p class="commom_text">普通病人</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 " v-if="roll.id043">
                <div class="common_left header_text">
                    <h1>科室药占比</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress043}" class="whiteSpace" @click="newPage(mc.mc043,toaddressObj.toaddress043,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numkss}}</span></p>
                            <p class="commom_text">抗生素</p>
                        </div>
                        <div class="text line ysb-green">
                            <p>比</p>
                            <!--<p>普通</p>-->
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress043}" class="whiteSpace" @click="newPage(mc.mc043,toaddressObj.toaddress043,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numpt}}</span></p>
                            <p class="commom_text">普通</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="grid-box">
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id044">
                <div class="common_left header_text">
                    <h1>科室自费比</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress044}" class="whiteSpace" @click="newPage(mc.mc044,toaddressObj.toaddress044,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzf}}</span></p>
                            <p class="commom_text">自费</p>
                        </div>
                        <div class="text line ysb-green">
                            <p>比</p>
                            <!--<p>待入库</p>-->
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress044}" class="whiteSpace" @click="newPage(mc.mc044,toaddressObj.toaddress044,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numbx}}</span></p>
                            <p class="commom_text">保险</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id045">
                <div class="common_left header_text">
                    <h1>科室基药比</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress045}" class="whiteSpace" @click="newPage(mc.mc045,toaddressObj.toaddress045,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numkss}}</span></p>
                            <p class="commom_text">抗生素</p>
                        </div>
                        <div class="text line ysb-green">
                            <p>比</p>
                            <!--<p>待入库</p>-->
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress045}" class="whiteSpace" @click="newPage(mc.mc045,toaddressObj.toaddress045,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numpt}}</span></p>
                            <p class="commom_text">普通</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id046">
                <div class="common_left header_text">
                    <h1>抗生素使用比例</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress046}" class="whiteSpace" @click="newPage(mc.mc046,toaddressObj.toaddress046,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numkss}}</span></p>
                            <p class="commom_text">抗生素</p>
                        </div>
                        <div class="text line ysb-green">
                            <p>比</p>
                            <!--<p>待入库</p>-->
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress046}" class="whiteSpace" @click="newPage(mc.mc046,toaddressObj.toaddress046,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numpt}}</span></p>
                            <p class="commom_text">普通</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 " v-if="roll.id047">
                <div class="common_left header_text">
                    <h1>危急值病人</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress047}" class="whiteSpace" @click="newPage(mc.mc047,toaddressObj.toaddress047,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzrs}}</span>人</p>
                            <p class="commom_text">总人数</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress047}" class="whiteSpace" @click="newPage(mc.mc047,toaddressObj.toaddress047,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwcl}}</span>人</p>
                            <p class="commom_text">未处理</p>
                        </div>
                        <div class="text line blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress047}" class="whiteSpace" @click="newPage(mc.mc047,toaddressObj.toaddress047,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numycl}}</span>人</p>
                            <p class="commom_text">已处理</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--门诊医生首页/链接没写完-->
        <div class="grid-box">
            <div class="col-xxl-12 col-s-12 col-l-12 col-xl-12 pd-4" v-if="roll.id049">
                <div class="common_left header_text">
                    <h1>开具处方</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress049}" class="whiteSpace" @click="newPage(mc.mc049,toaddressObj.toaddress049,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numkjcf}}</span>人</p>
                            <p class="commom_text">开具处方</p>
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress049}" class="whiteSpace" @click="newPage(mc.mc049,toaddressObj.toaddress049,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzycf}}</span>人</p>
                            <p class="commom_text">中药处方</p>
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress049}" class="whiteSpace" @click="newPage(mc.mc049,toaddressObj.toaddress049,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numxycf}}</span>人</p>
                            <p class="commom_text">西药处方</p>
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress049}" class="whiteSpace" @click="newPage(mc.mc049,toaddressObj.toaddress049,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzl}}</span>人</p>
                            <p class="commom_text">治疗</p>
                        </div>
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress049}" class="whiteSpace" @click="newPage(mc.mc049,toaddressObj.toaddress049,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numss}}</span>人</p>
                            <p class="commom_text">手术</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="grid-box">
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id050">
                <div class="common_left header_text">
                    <h1>今日接诊</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress050}" class="whiteSpace" @click="newPage(mc.mc050,toaddressObj.toaddress050,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrzz}}</span>人</p>
                            <p class="commom_text">今日接诊</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress050}" class="whiteSpace" @click="newPage(mc.mc050,toaddressObj.toaddress050,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numdjz}}</span>人</p>
                            <p class="commom_text">待接诊</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress050}" class="whiteSpace" @click="newPage(mc.mc050,toaddressObj.toaddress050,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyjz}}</span>人</p>
                            <p class="commom_text">已接诊</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id051">
                <div class="common_left header_text">
                    <h1>检查检验</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress051}" class="whiteSpace" @click="newPage(mc.mc051,toaddressObj.toaddress051,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjcjy}}</span>人</p>
                            <p class="commom_text">检查检验</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress051}" class="whiteSpace" @click="newPage(mc.mc051,toaddressObj.toaddress051,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwcbg}}</span>人</p>
                            <p class="commom_text">未出报告</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress051}" class="whiteSpace" @click="newPage(mc.mc051,toaddressObj.toaddress051,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numycbg}}</span>人</p>
                            <p class="commom_text">已出报告</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id052">
                <div class="common_left header_text">
                    <h1>门诊入院</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress052}" class="whiteSpace" @click="newPage(mc.mc052,toaddressObj.toaddress052,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.nummzry}}</span>人</p>
                            <p class="commom_text">门诊入院</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress052}" class="whiteSpace" @click="newPage(mc.mc052,toaddressObj.toaddress052,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numlgbr}}</span>人</p>
                            <p class="commom_text">留观病人</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id053">
                <div class="common_left header_text">
                    <h1>危急值病人</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress053}" class="whiteSpace" @click="newPage(mc.mc053,toaddressObj.toaddress053,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numzrs}}</span>人</p>
                            <p class="commom_text">总人数</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress053}" class="whiteSpace" @click="newPage(mc.mc053,toaddressObj.toaddress053,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwcl}}</span>人</p>
                            <p class="commom_text">未处理</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress053}" class="whiteSpace" @click="newPage(mc.mc053,toaddressObj.toaddress053,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numycl}}</span>人</p>
                            <p class="commom_text">已处理</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--护士首页-->
        <div class="grid-box">
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id055">
                <div class="common_left header_text">
                    <h1>今日雾化</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress055}" class="whiteSpace" @click="newPage(mc.mc055,toaddressObj.toaddress055,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numjrwh}}</span>人</p>
                            <p class="commom_text">今日雾化</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress055}" class="whiteSpace" @click="newPage(mc.mc055,toaddressObj.toaddress055,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwzx}}</span>人</p>
                            <p class="commom_text">未执行</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress055}" class="whiteSpace" @click="newPage(mc.mc055,toaddressObj.toaddress055,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyzx}}</span>人</p>
                            <p class="commom_text">已执行</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id056">
                <div class="common_left header_text">
                    <h1>医嘱执行</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress056}" class="whiteSpace" @click="newPage(mc.mc056,toaddressObj.toaddress056,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyzzx}}</span>人</p>
                            <p class="commom_text">医嘱执行</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress056}" class="whiteSpace" @click="newPage(mc.mc056,toaddressObj.toaddress056,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwzx}}</span>人</p>
                            <p class="commom_text">未执行</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress056}" class="whiteSpace" @click="newPage(mc.mc056,toaddressObj.toaddress056,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyzx}}</span>人</p>
                            <p class="commom_text">已执行</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 pd-4" v-if="roll.id057">
                <div class="common_left header_text">
                    <h1>护理</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress057}" class="whiteSpace" @click="newPage(mc.mc057,toaddressObj.toaddress057,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numtj}}</span>人</p>
                            <p class="commom_text">特级</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress057}" class="whiteSpace" @click="newPage(mc.mc057,toaddressObj.toaddress057,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyj}}</span>人</p>
                            <p class="commom_text">一级</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress057}" class="whiteSpace" @click="newPage(mc.mc057,toaddressObj.toaddress057,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numej}}</span>人</p>
                            <p class="commom_text">二级</p>
                        </div>
                        <div class="text line ysb-black">
                            <p :class="{'hongdian':!toaddressObj.toaddress057}" class="whiteSpace" @click="newPage(mc.mc057,toaddressObj.toaddress057,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numsj}}</span>人</p>
                            <p class="commom_text">三级</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 pd-4" v-if="roll.id058">
                <div class="common_left header_text">
                    <h1>护理记录</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress058}" class="whiteSpace" @click="newPage(mc.mc058,toaddressObj.toaddress058,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numhljl}}</span>人</p>
                            <p class="commom_text">护理记录</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress058}" class="whiteSpace" @click="newPage(mc.mc058,toaddressObj.toaddress058,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwzx}}</span>人</p>
                            <p class="commom_text">未执行</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress058}" class="whiteSpace" @click="newPage(mc.mc058,toaddressObj.toaddress058,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyzx}}</span>人</p>
                            <p class="commom_text">已执行</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 " v-if="roll.id059">
                <div class="common_left header_text">
                    <h1>护理文书</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress059}" class="whiteSpace" @click="newPage(mc.mc059,toaddressObj.toaddress059,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numhhb}}</span>人</p>
                            <p class="commom_text">互换比</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress059}" class="whiteSpace" @click="newPage(mc.mc059,toaddressObj.toaddress059,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numchb}}</span>人</p>
                            <p class="commom_text">床互比</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress059}" class="whiteSpace" @click="newPage(mc.mc059,toaddressObj.toaddress059,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numhlss}}</span>人</p>
                            <p class="commom_text">24h护理时数</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="grid-box">
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id060">
                <div class="common_left header_text">
                    <h1>体温测试</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress060}" class="whiteSpace" @click="newPage(mc.mc060,toaddressObj.toaddress060,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numtwcs}}</span>人</p>
                            <p class="commom_text">体温测试</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress060}" class="whiteSpace" @click="newPage(mc.mc060,toaddressObj.toaddress060,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwzx}}</span>人</p>
                            <p class="commom_text">未执行</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress060}" class="whiteSpace" @click="newPage(mc.mc060,toaddressObj.toaddress060,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numyzx}}</span>人</p>
                            <p class="commom_text">已执行</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3 col-s-3 col-l-3 col-xl-3 pd-4" v-if="roll.id061">
                <div class="common_left header_text">
                    <h1>危急值</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress061}" class="whiteSpace" @click="newPage(mc.mc061,toaddressObj.toaddress061,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwjz}}</span>人</p>
                            <p class="commom_text">危急值</p>
                        </div>
                        <div class="text line ysb-red">
                            <p :class="{'hongdian':!toaddressObj.toaddress061}" class="whiteSpace" @click="newPage(mc.mc061,toaddressObj.toaddress061,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numwcl}}</span>人</p>
                            <p class="commom_text">未处理</p>
                        </div>
                        <div class="text line ysb-blue">
                            <p :class="{'hongdian':!toaddressObj.toaddress061}" class="whiteSpace" @click="newPage(mc.mc061,toaddressObj.toaddress061,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numycl}}</span>人</p>
                            <p class="commom_text">已处理</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 pd-4" v-if="roll.id062">
                <div class="common_left header_text">
                    <h1>欠费病人</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress062}" class="whiteSpace" @click="newPage(mc.mc062,toaddressObj.toaddress062,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numqfbr}}</span>人</p>
                            <p class="commom_text">欠费病人</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 pd-4" v-if="roll.id063">
                <div class="common_left header_text">
                    <h1>三天未解大便</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress063}" class="whiteSpace" @click="newPage(mc.mc063,toaddressObj.toaddress063,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numstwjdb}}</span>人</p>
                            <p class="commom_text">三天未解大便</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-2 col-s-2 col-l-2 col-xl-2 " v-if="roll.id064">
                <div class="common_left header_text">
                    <h1>发热病人</h1>
                    <div class="commom_flex">
                        <div class="text line ysb-green">
                            <p :class="{'hongdian':!toaddressObj.toaddress064}" class="whiteSpace" @click="newPage(mc.mc064,toaddressObj.toaddress064,urlObj.urlghurl)"><span
                                    class="num_text">{{numText.numfr}}</span>人</p>
                            <p class="commom_text">发热病人</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="grid-box" style="display: none;">
            <div class="col-xxl-12 col-s-12 col-l-12 col-xl-12 ">
                <div class="common_left header_text">
                    <div class="commom_flex commom_img">
                        <div class="text line commom_img_sf_one md-b-10 md_r_10 commom_img_on" v-if="roll.id005">
                            <p class="img_text" @click="topNewPage('门诊收费','page/mzsf/sfjs/mzsf/mzsf.html')">收费</p>
                            <p class="img_text" @click="topNewPage('门诊退费','page/mzsf/sfjs/mztf/mztf.html')">退费</p>
                            <p class="img_text" @click="topNewPage('门诊上缴','page/mzsf/sfjs/mzjk/mzjk.html')">门诊上缴</p>
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">划价</p>
                        </div>

                        <div class="text line commom_img_sf_two md-b-10 md_r_10 commom_img_on" v-if="roll.id066">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10 commom_img_on" v-if="roll.id011">
                            <p class="img_text" @click="topNewPage('挂号退号','page/ghgl/ghyw/brgh/brgh.html')">挂号</p>
                            <p class="img_text" @click="topNewPage('患者注册','page/ghgl/ghyw/brzc/brzc.html')">注册</p>
                            <p class="img_text" @click="topNewPage('换卡管理','page/xtwh/dazc/hkgl/hkgl.html')">换卡</p>
                        </div>
                        <div class="text line commom_img_sf_two md-b-10 md_r_10 commom_img_on" v-if="roll.id067">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10 commom_img_on" v-if="roll.id017">
                            <p class="img_text" @click="topNewPage('患者管理','page/zygl/rcygl/rydj/rydj.html')">住院管理</p>
                        </div>
                        <div class="text line commom_img_sf_three md-b-10 md_r_10 commom_img_on" v-if="roll.id017">
                            <p class="img_text">住院上缴</p>
                        </div>
                        <div class="text line commom_img_sf_two md-b-10 md_r_10 commom_img_on" v-if="roll.id068">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10  commom_img_on" v-if="roll.id025">
                            <p class="img_text"><span class="md-r" @click="topNewPage('入库管理','page/ykgl/kfyw/rkgl/rkgl.html')">入库管理</span><span
                                    @click="topNewPage('盘点管理','page/ykgl/kfyw/pdgl/pdgl.html')">盘点管理</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('出库管理','page/ykgl/kfyw/ckgl/ckgl.html')">出库管理</span><span
                                    @click="topNewPage('调价管理','page/ykgl/kfyw/tjgl/tjgl.html')">调价管理</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('退库管理','page/ykgl/kfyw/tkgl/tkgl.html')">退库管理</span><span
                                    @click="topNewPage('采购计划','page/ykgl/kfyw/cgjh/cgjh.html')">采购计划</span></p>
                            <p class="img_text "><span class="md-r" @click="topNewPage('退货管理','page/ykgl/kfyw/thgl/thgl.html')">退货管理</span><span
                                    class="op-0">退货管理</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('报损管理','page/ykgl/kfyw/bsgl/bsgl.html')">报损管理</span><span
                                    class="op-0">报损管理</span></p>
                        </div>
                        <div class="text line commom_img_sf_four md-b-10 md_r_10  commom_img_on" v-if="roll.id025">
                            <p class="img_text"><span class="md-r" @click="topNewPage('库房库位','page/ykgl/kfwh/kfkw/kfkw.html')">库房库位</span><span
                                    class="md-r" @click="topNewPage('药品统筹类别','page/ykgl/kfwh/tclb/tclb.html')">统筹类别</span><span
                                    @click="topNewPage('招标方式','page/ykgl/kfwh/zbfs/zbfs.html')">招标方式</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('药品字典','page/ykgl/kfwh/ypzd/ypzd.html')">药品字典</span><span
                                    class="md-r" @click="topNewPage('药品种类','page/ykgl/kfwh/ypzl/ypzl.html')">药品种类</span><span
                                    class="op-0">药品剂型</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('药品产地','page/ykgl/kfwh/ypcd/ypcd.html')">药品产地</span><span
                                    class="md-r" @click="topNewPage('药品剂型','page/ykgl/kfwh/ypjx/ypjx.html')">药品剂型</span><span
                                    class="op-0">药品剂型</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('供货单位','page/ykgl/kfwh/ghdw/ghdw.html')">供货单位</span><span
                                    class="md-r" @click="topNewPage('药品功效','page/ykgl/kfwh/ypgx/ypgx.html')">药品功效</span><span
                                    class="op-0">药品功效</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('限量设置','page/ykgl/kfwh/xlsz/xlsz.html')">限量设置</span><span
                                    class="md-r" @click="topNewPage('药品计量单位','page/ykgl/kfwh/jldw/jldw.html')">计量单位</span><span
                                    class="op-0">计量单位</span></p>
                        </div>
                        <div class="text line commom_img_sf_two  md-b-10 md_r_10 commom_img_on" v-if="roll.id069">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10  commom_img_on" v-if="roll.id029">
                            <p class="img_text" @click="topNewPage('门诊交款','page/mzsf/sfjs/mzjk/mzjk.html')">门诊上缴</p>
                            <p class="img_text" @click="topNewPage('住院交款','page/zygl/fygl/zyjk/zyjk.html')">住院上缴</p>
                        </div>
                        <div class="text line commom_img_sf_two md-b-10 md_r_10 commom_img_on" v-if="roll.id070">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10 commom_img_on" v-if="roll.id033">
                            <p class="img_text" @click="topNewPage('病案登记','page/bagl/sygl/sydj/sydj.html')">病案登记</p>
                            <p class="img_text" @click="topNewPage('接收管理','page/bagl/sygl/jsgl/jsgl.html')">病案接收</p>
                            <p class="img_text" @click="topNewPage('借阅管理','page/bagl/sygl/jygl/jygl.html')">借阅登记</p>
                        </div>
                        <div class="text line commom_img_sf_two md-b-10 md_r_10 commom_img_on" v-if="roll.id072">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10 commom_img_on" v-if="roll.id039">
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">处方划价</p>
                            <p class="img_text" @click="topNewPage('处方发药','page/yfgl/yfyw/cffy/cffy.html')">处方发药</p>
                            <p class="img_text" @click="topNewPage('科室退药','page/yfgl/yfyw/ksty/ksty.html')">处方退药</p>
                        </div>
                        <div class="text line commom_img_sf_eight md-b-10 md_r_10 commom_img_on" v-if="roll.id039">
                            <p class="img_text" @click="topNewPage('科室退药','page/yfgl/yfyw/ksty/ksty.html')">申领单管理</p>
                            <p class="img_text" @click="topNewPage('科室退药','page/yfgl/yfyw/fyty/fyty.html')">科室发药单</p>
                            <p class="img_text" @click="topNewPage('汇总领药','page/yfgl/yfyw/ksly/ksly.html')">汇总申领</p>
                        </div>
                        <div class="text line commom_img_sf_seven md-b-10 md_r_10 commom_img_on" v-if="roll.id039">
                            <p class="img_text"><span class="md-r" @click="topNewPage('入库管理','page/ykgl/kfyw/rkgl/rkgl.html')">入库登记</span><span
                                    @click="topNewPage('药房库位','page/yfgl/kcgl/yfkw/yfkw.html')">药房库位</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('出库管理','page/ykgl/kfyw/ckgl/ckgl.html')">出库登记</span><span
                                    @click="topNewPage('药品库位','page/yfgl/kcgl/ypkw/ypkw.html')">药品库位</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('调拨管理','page/yfgl/kcgl/dbgl/dbgl.html')">药品调拨</span><span
                                    class="op-0">药品调拨</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('领药管理','page/yfgl/kcgl/lygl/lygl.html')">药品申领</span><span
                                    class="op-0">药品申领</span></p>
                            <p class="img_text"><span class="md-r" @click="topNewPage('盘点管理','page/yfgl/kcgl/pdgl/pdgl.html')">盘点管理</span><span
                                    class="op-0">盘点管理</span></p>
                        </div>
                        <div class="text line commom_img_sf_two md-b-10 md_r_10 commom_img_on" v-if="roll.id071">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10 commom_img_on" v-if="roll.id048">
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">药品医嘱</p>
                            <p class="img_text">检查检验</p>
                            <p class="img_text">手术治疗</p>
                        </div>
                        <div class="text line commom_img_sf_nine md-b-10 md_r_10 commom_img_on" v-if="roll.id048">
                            <p class="img_text">门诊登记簿</p>
                            <p class="img_text">医生工作量</p>
                        </div>
                        <div class="text line commom_img_sf_two md-b-10 md_r_10 commom_img_on" v-if="roll.id073">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_seven md-b-10 md_r_10 commom_img_on" v-if="roll.id048">
                            <p class="img_text" @click="topNewPage('药品组合医嘱','page/zyysz/zyysz/ypzhyz/ypzhyz.html')">
                                药品组合医嘱</p>
                            <p class="img_text" @click="topNewPage('组合医疗医嘱','page/zyysz/zyysz/zhylyz/zhylyz.html')">
                                医疗组合医嘱</p>
                        </div>
                        <div class="text line commom_img_sf_one md-b-10 md_r_10 commom_img_on" v-if="roll.id054">
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">患者接诊</p>
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">电子处方</p>
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">检查检验</p>
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">患者治疗</p>
                            <p class="img_text" @click="topNewPage('处方划价','page/yfgl/yfyw/cfhj/cfhj.html')">手术方案</p>
                        </div>
                        <div class="text line commom_img_sf_ten  commom_img_on md-b-10 md_r_10" v-if="roll.id065">
                            <p class="img_text" @click="topNewPage('病员处理','page/hsz/hlyw/bygl/bygl.html')">病员处理</p>
                            <p class="img_text" @click="topNewPage('医嘱处理','page/hsz/hlyw/yzcl/yzcl_main.html')">医嘱处理</p>
                        </div>
                        <!--<div class="text line commom_img_sf_eleven  commom_img_on md_8">-->
                        <!--<p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">审核医嘱</p>-->
                        <!--<p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">执行医嘱</p>-->
                        <!--<p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">申领药品</p>-->
                        <!--<p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">打印单据</p>-->
                        <!--</div>-->
                        <!--<div class="text line commom_img_sf_twoelve  commom_img_on md_8">-->
                        <!--<p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">三测数据</p>-->
                        <!--<p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">危重护理</p>-->
                        <!--</div>-->
                        <div class="text line commom_img_sf_fourteen  commom_img_on md-b-10 md_r_10" v-if="roll.id065">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_thirteen md-b-10 md_r_10 commom_img_on" v-if="roll.id065">
                            <p class="img_text" @click="topNewPage('新生儿信息','page/hsz/hlyw/xsexx/xsexx.html')">新生儿登记</p>
                        </div>
                        <div class="text line commom_img_sf_seven  md-b-10 md_r_10 commom_img_on" v-if="roll.id054">
                            <p class="img_text" @click="topNewPage('药品组合医嘱','page/zyysz/zyysz/ypzhyz/ypzhyz.html')">
                                药品组合医嘱</p>
                            <p class="img_text" @click="topNewPage('组合医疗医嘱','page/zyysz/zyysz/zhylyz/zhylyz.html')">
                                医疗组合医嘱</p>
                        </div>
                        <div class="text line commom_img_sf_two  md-b-10 md_r_10 commom_img_on" v-if="roll.id074">
                            <p class="img_text" @click="topNewPage('我的排班','page/ghgl/pbgl/wdpb/wdpb.html')">我的排班</p>
                            <p class="img_text" @click="topNewPage('科室排班','page/ghgl/pbgl/yspb/yspb.html')">科室排班</p>
                        </div>
                        <div class="text line commom_img_sf_nine md-b-10 md_r_10 commom_img_on" v-if="roll.id054">
                            <p class="img_text">门诊登记簿</p>
                            <p class="img_text">医生工作量</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--排班表-->
        <!-- <div class="grid-box">
            <div class="col-xxl-12 col-s-12 col-l-12 col-xl-12 ">
                <div class="common_left header_text bg-fff">
                    <div class="md_b_8 user-select">
                        <h1 class="commom_pb">
                            <span>本周排班表</span>
                            <p><span class="commom_zao">早班：7:00~15:00</span><span class="commom_bai">白班：9:00~18:00</span><span
                                    class="commom_wan">晚班：15:00~23:00</span><span class="commom_ye">夜班：23:00~次日7:00</span>
                                <span class="commom_prev" :class="{'ysb-green':n==0}" v-if="t==0" @click="prvWeek">上一周</span>
                                <span class="commom_prev" :class="{'ysb-green':n==0}" v-if="t==1" @click="prvMonth">上一月</span>
                                <span class="commom_next md_r_18" :class="{'ysb-green':n==1}" v-if="t==0" @click="nextWeek">下一周</span>
                                <span class="commom_next md_r_18" :class="{'ysb-green':n==1}" v-if="t==1" @click="nextMonth">下一月</span>
                                <span class="commom_year  md_r_18" @click="actWeek(0,1)" :class="{'commom_active':index==0}">周</span>
                                <span class="commom_year md-r-22" @click="actWeek(1,1)" :class="{'commom_active':index==1}">月</span></p>
                        </h1>
                    </div>
                    <div class="commom_pb_bg md_r_10 md_l pd_b_10">
                        <ul>
                            <div class="commom_flex-pb">
                                <li class="commom_flex-title"><span class="user">姓名</span><i class="icon-border"></i><span
                                        class="date">时间</span></li>
                                <li>周一{{weekToDay(selectDay, 1)}}</li>
                                <li>周二{{weekToDay(selectDay, 2)}}</li>
                                <li>周三{{weekToDay(selectDay, 3)}}</li>
                                <li>周四{{weekToDay(selectDay, 4)}}</li>
                                <li>周五{{weekToDay(selectDay, 5)}}</li>
                                <li>周六{{weekToDay(selectDay, 6)}}</li>
                                <li>周日{{weekToDay(selectDay, 7)}}</li>
                            </div>
                            <div class="commom_flex-pd-content" v-for="(item,index) in person">
                                <li>周丽君</li>
                                <li><span :style="{background: item.bg, color: item.color}" :id="item.id" v-text="item.name"></span></li>
                            </div>
                            <div class="commom_flex-pd-content" v-for="(item,index) in optionList">
                                <li>世世代代</li>
                                <li :data-id="weekToDay(selectDay, 1)===day()" :class="{'classwww':day(1)}">
                                    <p class="pd_text" :style="{background: item.bg, color: item.color}" :id="item.id"
                                        v-text="item.name"></p>
                                </li>
                                <li :class="{'dayActive':day(2)}">
                                    <p class="pd_text" :style="{background: item.bg, color: item.color}" :id="item.id"
                                        v-text="item.name"></p>
                                </li>
                                <li :class="{'dayActive':day(3)}">
                                    <p class="pd_text" :style="{background: item.bg, color: item.color}" :id="item.id"
                                        v-text="item.name"></p>
                                </li>
                                <li :class="{'dayActive':day(4)}">
                                    <p class="pd_text" :style="{background: item.bg, color: item.color}" :id="item.id"
                                        v-text="item.name"></p>
                                </li>
                                <li :class="{'dayActive':day(5)}">
                                    <p class="pd_text" :style="{background: item.bg, color: item.color}" :id="item.id"
                                        v-text="item.name"></p>
                                </li>
                                <li :class="{'dayActive':day(6)}">
                                    <p class="pd_text" :style="{background: item.bg, color: item.color}" :id="item.id"
                                        v-text="item.name"></p>
                                </li>
                                <li :class="{'dayActive':day(7)}">
                                    <p class="pd_text" :style="{background: item.bg, color: item.color}" :id="item.id"
                                        v-text="item.name"></p>
                                </li>
                            </div>
                        </ul>
                    </div>

                </div>
            </div>
        </div> -->



    </div>

    <script src="workbench.js" type="application/javascript"></script>
</body>

</html>
