<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>控制面板</title>

</head>
   <div id="printPage">
       <input class="111"  type="checkbox"  onclick="if(this.checked){this.setAttribute('checked',this.checked)}else {this.removeAttribute('checked')}"/>
   </div>
<script type="text/javascript">
function checkedFun(obj){
    if(obj.checked){
        obj.setAttribute('checked',obj.checked)

    }else {
        obj.removeAttribute('checked')

    }
};
</script>
</html>
