var workbench = new Vue({
    el: '#workbench',
    mixins: [dragFun, mformat, baseFunc, tableBase, dic_transform],
    data: {
        chartData: {
            ryrs: [],
            cyrs: []
        },

        chartDataNum: '',
        chartShow: true,
        gh: true,
        sf: false,
        date: [],
        index: 0,
        num: 0,
        pbList: [],
        myUser: '',
        pbName: null,
        n: 0,
        optionList: [
            {id: '0001', name: '白', bg: '#45BFA6', color: '#FFFFFF', sbTime: '08:00', xbTime: '16:00'},
            {id: '0003', name: '晚', bg: '#fb8600', color: '#FFFFFF', sbTime: '16:00', xbTime: '00:00'},
            {id: '0005', name: '夜', bg: '#02221c', color: '#FFFFFF', sbTime: '00:00', xbTime: '08:00'},],
        my: {},
        currentDate: '',
        selectDay: new Date(),
        toDay: new Date(),
        d: new Date(),
        person: {},
        t: 0,
        numText: {},
        urlObj: {},
        ksbm: '',
        roll: {},
        selectDate: '',
        toaddressObj: {},
        mc: {},
        shortcutMenu: []
    },
    components: {
        'calendar': calendar,
    },
    created: function () {
    },
    mounted: function () {
        this.updateHomeShortcutMenu();
        this.getDateNum(0);
        this.getChart(0);
//        this.bintu();
        this.getInfo();
        this.$nextTick(function () {
            workbench.getJsgn()
        })
    },
    methods: {
        updateHomeShortcutMenu: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1Gyb_ylqxR&types=CzyqxQuery", function (json) {
                if (json.a == '0' && json.d) {
                    if (json.d.length) workbench.shortcutMenu = Object.freeze(json.d);
                    else workbench.shortcutMenu = Object.freeze([]);
                    if (workbench.shortcutMenu && workbench.shortcutMenu.length > 0) {
                        for (let i = 0; i < workbench.shortcutMenu.length; i++) {
                            if (workbench.shortcutMenu[i].url == '/hsz/hlyw/yzcl/yzcl_main') {
                                workbench.topNewPage(workbench.shortcutMenu[i].ylmc, "page" + workbench.shortcutMenu[i].url + ".html", workbench.shortcutMenu[i].ylbm);
                            }
                        }
                    }

                } else {
                    console.log("获取快捷菜单请求失败！");
                }
            });
        },
        shortcutMenuClick: function (list) {
            if (list.lx == 'yl') {
                if (window.top.J_tabLeft.pageLists.length >= 14) {
                    for (var i = 1; i < window.top.J_tabLeft.pageLists.length; i++) {
                        if (list.ylbm == window.top.J_tabLeft.pageLists[i].id) {
                            this.topNewPage(list.ylmc, "page" + list.url + ".html", list.ylbm)
                            return false
                        }
                    }
                    malert("你打开过多！为了不影响你的使用体验！请关闭一些模块再打开！", 'top', 'defeadted');
                    return false
                }
                if (list.fr == 1) {
                    this.topNewPage(list.ylmc, location.origin + list.url + '&yljgbm=' + this.getQueryVariable('yljgbm') + '&czybm=' + this.getQueryVariable('czybm') + '&ksbm=' + ksbm, list.ylbm)
                } else {
                    this.topNewPage(list.ylmc, "page" + list.url + ".html", list.ylbm)
                }
            }
        },
        getDateNum: function (type) {
            this.date = [];
            if (type == 0) {
                for (var i = 1; i < 8; i++) {
                    this.date.push(i);
                }
            } else {
                var d = new Date();
                var curMonthDays = new Date(d.getFullYear(), (d.getMonth() + 2), 0).getDate();
                for (var i = 1; i < curMonthDays; i++) {
                    this.date.push(i);
                }
            }
        },
        getChart: function (type) {
            var param = {
                querytype: type
            };
            this.chartData.ryrs = [];
            this.chartData.cyrs = [];
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhQxwhJs&types=querySyRyxx&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    for (var i = 0; i < json.d.list.length; i++) {
                        workbench.chartData.ryrs.push(json.d.list[i].ryrs);
                        workbench.chartData.cyrs.push(json.d.list[i].cyrs)
                    }
                    workbench.canvas();
                } else {
                    malert("查询失败：" + json.c, 'top', 'defeadted');
                }
            });
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhQxwhJs&types=querySyCryxx&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    workbench.chartDataNum = json.d.list[0]
                } else {
                    malert("查询失败：" + json.c, 'top', 'defeadted');
                }
            });
        },
        newPage: function (name, toaddres, url) {
            this.topNewPage(name, location.origin + toaddres + '&yljgbm=' + this.getQueryVariable('yljgbm') + '&czybm=' + this.getQueryVariable('czybm'))
        },
        k: function () {
            var child = document.getElementsByClassName('commom_flex');

            for (var j = 0; j < child.length; j++) {
                var childen = child[j].children;
                for (var i = 0; i < childen.length; i++) {
                    if (i < 5) {
                        if (i == childen.length - 1) {
                            childen[i].style.width = "calc(100% / " + childen.length + ")";
                            childen[i].style.minWidth = "calc(20%)"
                        } else {
                            childen[i].style.width = "calc(100% / " + childen.length + " - 10px)";
                            childen[i].style.minWidth = "calc(20% - 10px)"
                        }

                    } else {
                        var yu = childen.length % 5;
                        var b = childen.length - yu;
                        for (var z = 0; z < b; z++) {
                            childen[z].style.width = "calc(100% / " + 5 + " - 10px)";
                            childen[z].style.minWidth = "calc(20% - 10px)";
                            if ((z + 1) % 5 == 0) {
                                $(childen[z]).removeClass('md_r_10');
                                childen[z].style.width = "calc(100% / " + 5 + ")";
                                childen[z].style.minWidth = "calc(20%)"
                            }
                        }

                        if (yu > 0) {
                            var le = childen.length;
                            for (var x = 1; x < (yu + 1); x++) {
                                childen[le - x].style.width = "calc(100% / " + yu + " - 10px)";
                                childen[le - x].style.minWidth = "calc(20% - 10px)";
                                if (x == 1) {
                                    $(childen[le - x]).removeClass('md_r_10');
                                    childen[le - x].style.width = "calc(100% / " + yu + ")";
                                    childen[le - x].style.minWidth = "calc(20%)";
                                }


                            }

                        }


                        // var t = childen.length - 5;
                        // for (var l = 0; l < t; l++) {
                        //     if (l < 5) {
                        //         if (l == childen.length-1) {
                        //             childen[i+l].style.width = "calc(100% / " + childen.length + ")";
                        //             childen[i+l].style.minWidth = "20%"
                        //         } else {
                        //             childen[i+l].style.width = "calc(100% / " + t % 5 + " - 10px)";
                        //             childen[i+l].style.minWidth = "calc(20% - 10px)"
                        //             $(child[j].children[ childen.length-1]).removeClass('md_r_10')
                        //         }
                        //
                        //
                        //     }
                        // }
                    }

                    // if (child[j].children.length < 5) {
                    //     $(child[j].children[child[j].children.length - 1]).removeClass('md_r_10')
                    // } else {
                    //     var n = child[j].children.length - (child[j].children.length - 5)
                    //     $(child[j].children[n - 1]).removeClass('md_r_10')
                    // }
                }
            }
        },
        getJsgn: function () {
            workbench.mzsfFor = [];
            var json = {
                rybm: userId,
                ksbm: window.top.J_tabLeft.ksbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhQxwhJs&types=queryGnByczy&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (data.d.list.length > 0) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            workbench.$set(workbench.roll, ['id' + data.d.list[i].id], data.d.list[i].id);
                            workbench.$set(workbench.urlObj, ['url' + data.d.list[i].url], data.d.list[i].url);
                            workbench.$set(workbench.mc, ['mc' + data.d.list[i].id], data.d.list[i].mc);
                            workbench.$set(workbench.toaddressObj, ['toaddress' + data.d.list[i].id], data.d.list[i].toaddress);
                            if (data.d.list[i].rlmsg) {
                                for (var j in data.d.list[i].rlmsg) {
                                    workbench.$set(workbench.numText, ['num' + j], data.d.list[i].rlmsg[j] == '' ? 0 : data.d.list[i].rlmsg[j])
                                }
                            }
                            if (data.d.list[i].id == '077') {
                                workbench.bintu();
                            }
                        }
                        common.closeLoading()
                    } else {
                        common.closeLoading()
                    }
                    workbench.$nextTick(function () {
                        workbench.k()
                    })
                } else {
                    common.closeLoading();
                    console.log("角色加载失败！");
                }
            });
        },
        getData: function () {
            var json = {
                rybm: this.myUser.czybm,
                ksbm: this.myUser.ksbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhQxwhJs&types=queryGnByczy&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {

                } else {

                }
            });
        },
        canvas: function () {
            var date = this.date;
            var ryrs = this.chartData.ryrs;
            var cyrs = this.chartData.cyrs;
            new Highcharts.Chart({
                chart: {
                    renderTo: 'container',
                    type: 'column',
                },
                title: {
                    text: ''
                },
                subtitle: {
                    text: ''
                },
                legend: {
                    enabled: false
                },
                credits: {
                    enabled: false
                },
                xAxis: {
                    categories: date,
                    crosshair: true
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: ''
                    }
                },

                tooltip: {
                    headerFormat: '<span style="font-size:10px"></span><table>',
                    pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                        '<td style="padding:0"><b>{point.y:.1f}</b></td></tr>',
                    footerFormat: '</table>',
                    shared: false,
                    useHTML: false
                },
                plotOptions: {
                    column: {
                        borderWidth: 0,
                        pointPadding: 0.15,
                        animation: true,
                    },
                    series: {
                        cursor: 'pointer',
                        states: {
                            hover: {
                                enabled: false,
                            },
                        },
                    },
                },
                colors: ['#1abc9c', '#f3a74f'],
                series: [{
                    name: '今日入院',
                    data: ryrs
                }, {
                    name: '今日出院',
                    data: cyrs
                }]
            });
        },
        bintu: function () {
            var dom = document.getElementById('containerone');
            var mychart = echarts.init(dom);
            option = null;
            option = {
                tooltip: {
                    trigger: 'item',
                    show: false
                },
                legend: {
                    orient: 'vertical',
                    x: 'left',
                },
                series: [
                    {
                        name: '',
                        type: 'pie',
                        selectedMode: 'single',
                        radius: [0, '80%'],

                        label: {
                            normal: {
                                position: 'inner',
                                formatter: function (item) {
                                    if (item['dataIndex'] == "0") {
                                        return '' + item['name'] + '人\n危急病人'
                                    } else if (item['dataIndex'] == 1) {
                                        return '' + item['name'] + '人\n重症病人'
                                        // return '<div><p>' + this.point.name + '</p><p><br/>重症斌人</p></div>'
                                    } else {
                                        return '' + item['name'] + '人\n一般病人'
                                    }
                                }
                            }
                        },
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        data: [
                            {value: 335, name: '335'},
                            {value: 679, name: '679'},
                            {value: 548, name: '1548'}
                        ]
                    },
                    {
                        name: '',
                        type: 'pie',
                        radius: ['40%', '55%'],
                        label: {
                            normal: {
                                formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
                                backgroundColor: '#eee',
                                borderColor: '#aaa',

                                rich: {
                                    a: {
                                        color: '#999',
                                        lineHeight: 22,
                                        align: 'center'
                                    },

                                    hr: {
                                        borderColor: '#aaa',
                                        width: '100%',
                                        borderWidth: 0.5,
                                        height: 0
                                    },
                                    b: {
                                        fontSize: 16,
                                        lineHeight: 33
                                    },
                                    per: {
                                        color: '#1ABC9C',
                                        backgroundColor: '#334455',
                                        padding: [2, 4],
                                        borderRadius: 2
                                    }
                                }
                            }
                        },

                    }
                ],
                color: ['#1ABC9C', '#04A9F5', '#FF5C63'],
            };
            if (option && typeof option === 'object') {
                mychart.setOption(option, true)
            }
        },
        // 根据当前时间计算这周的时间
        weekToDay: function (selectDay, week) {
            var day = new Date(1000 * 60 * 60 * 24 * (week - selectDay.getDay()) + selectDay.getTime());
            return day.getMonth() + 1 + '月' + day.getDate() + '日';
        },
        day: function (num) {
            var day = (new Date().getMonth() + 1) + '月' + (new Date().getDate()) + '日';
            if (this.weekToDay(this.selectDay, num) == day) {
                return true
            }
        },
        // 跳转到上一周
        prvWeek: function () {
            this.selectDay = new Date(1000 * 60 * 60 * 24 * (-7) + this.selectDay.getTime());
            this.getPbInfo();
            this.n = 0
        },
        // 跳转到下一周
        nextWeek: function () {
            this.selectDay = new Date(1000 * 60 * 60 * 24 * (7) + this.selectDay.getTime());
            this.getPbInfo();
            this.n = 1
        },
        prvMonth: function () {
            this.selectDay = new Date(this.selectDay.getFullYear(), this.selectDay.getMonth() - 1, 7);
            this.getPbInfo();
            this.n = 0
        },
        nextMonth: function () {
            this.selectDay = new Date(this.selectDay.getFullYear(), this.selectDay.getMonth() + 1, 7);
            this.getPbInfo();
            this.n = 1
        },
        actWeek: function (index, i) {
            if (i == 0) {
                this.num = index;
            } else {
                this.t = index;
                this.index = index;
            }
        },
        // 获取当前操作员的信息
        getInfo: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetUserInfoAction', function (data) {
                if (data.a == 0) {
                    workbench.myUser = data.d;
                    workbench.getData()
                }
            })
        },
        // 获取排班的信息（根据是否传参来判断是增加的还是页面初始化查询）
        getPbInfo: function () {
            var week = this.selectDay.getDay();
            $(".addPb").remove();
            if (week == 0) week = 7;
            var first = new Date(1000 * 60 * 60 * 24 * (1 - week) + this.selectDay.getTime());
            var end = new Date(1000 * 60 * 60 * 24 * (7 - week) + this.selectDay.getTime());
            var firstDate = first.getFullYear() + '-' + (first.getMonth() + 1) + '-' + first.getDate();
            var endDate = end.getFullYear() + '-' + (end.getMonth() + 1) + '-' + end.getDate();
            var str_param = {
                ksbm: this.myUser.ksbm,
                page: 1,
                rows: 100,
                sort: 'sbsj',
                order: 'asc',
                beginrq: firstDate + ' 00:00:00',
                endrq: endDate + ' 00:00:00',
                rybm: workbench.myUser.czybm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GhglPbglYspb&types=query&dg=" + JSON.stringify(str_param), function (json) {
                workbench.pbList = json.d.list;
                // var pbList = json.d.list;
                // var date = null, bg = null, color = null;
                // for (var i = 0; i < pbList.length; i++) {
                //     date = new Date(pbList[i].sbsj);
                //     var el = $('#' + pbList[i].rybm).find('td').eq(date.getDay() - 1);
                //     for(var j = 0; j < option.optionList.length; j++){
                //         if(pbList[i].bcfabm == option.optionList[j].id){
                //             bg = option.optionList[j].bg;
                //             color = option.optionList[j].color;
                //         }
                //     }
                //     el.append("<div id='" + pbList[i].pbbid + "' data-type='"+pbList[i].bcfabm+"' class='optionBtu addPb'" +
                //         "style='background: "+bg+";color: "+color+"'>" + pbList[i].bcfamc +
                //         "<span class='fa fa-minus-circle'></span></div>");
                // }
                // scheduling.setToDayBg();
            });
        },
        // setToDayBg: function () {
        //     setTimeout(function () { // 这里延迟来加载今天的背景色
        //         if(scheduling.toDay.toString() == scheduling.selectDay.toString()){
        //             var a = scheduling.toDay.getDay() - 1;
        //             for (var i = 0; i < $('.toDayTable tr').length; i++) {
        //                 $('.toDayTable tr').eq(i).find('td').eq(a).addClass('toDayBg');
        //             }
        //         } else {
        //             $('.toDayTable td').removeClass('toDayBg');
        //         }
        //     }, 100);
        // },
    },
    watch: {
        pbList: function (val) {
            // workbench.getPbTime(val);
        },
        selectDate: function (val) {
            if (val == null || val == '') return false;
            var list = val.split("-");
            this.selectDay.setFullYear(list[0], list[1] - 1, list[2]);
            this.getPbInfo();
        }
    },
});

window.addEventListener("storage", function (e) {
    if (e.key == "updateHomeShortcutMenu" && e.newValue !== e.oldValue) {
        workbench.updateHomeShortcutMenu();
    }
});
