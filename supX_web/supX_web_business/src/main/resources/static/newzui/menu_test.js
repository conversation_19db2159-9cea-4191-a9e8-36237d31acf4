/**
 * 菜单改造测试脚本
 * 用于验证菜单功能是否正常工作
 */

// 测试工具函数
var MenuTester = {
    
    // 测试结果存储
    testResults: [],
    
    // 添加测试结果
    addResult: function(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed: passed,
            message: message,
            timestamp: new Date().toLocaleString()
        });
        
        var status = passed ? '✅ PASS' : '❌ FAIL';
        console.log('[菜单测试] ' + status + ' - ' + testName + ': ' + message);
    },
    
    // 测试Vue实例是否存在
    testVueInstances: function() {
        try {
            var instances = ['menuBar', 'loadModular', 'navbar', 'J_tabLeft'];
            var allExist = true;
            var missing = [];
            
            for (var i = 0; i < instances.length; i++) {
                if (typeof window[instances[i]] === 'undefined') {
                    allExist = false;
                    missing.push(instances[i]);
                }
            }
            
            if (allExist) {
                this.addResult('Vue实例检查', true, '所有必要的Vue实例都已创建');
            } else {
                this.addResult('Vue实例检查', false, '缺少Vue实例: ' + missing.join(', '));
            }
            
            return allExist;
        } catch (e) {
            this.addResult('Vue实例检查', false, '检查过程出错: ' + e.message);
            return false;
        }
    },
    
    // 测试菜单数据结构
    testMenuDataStructure: function() {
        try {
            if (typeof menuBar === 'undefined') {
                this.addResult('菜单数据结构', false, 'menuBar实例不存在');
                return false;
            }
            
            var hasAllModuleMenus = menuBar.hasOwnProperty('allModuleMenus');
            var hasGetModuleIcon = typeof menuBar.getModuleIcon === 'function';
            var hasGetModuleTitle = typeof menuBar.getModuleTitle === 'function';
            
            if (hasAllModuleMenus && hasGetModuleIcon && hasGetModuleTitle) {
                this.addResult('菜单数据结构', true, '新增的数据属性和方法都存在');
                return true;
            } else {
                var missing = [];
                if (!hasAllModuleMenus) missing.push('allModuleMenus');
                if (!hasGetModuleIcon) missing.push('getModuleIcon');
                if (!hasGetModuleTitle) missing.push('getModuleTitle');
                
                this.addResult('菜单数据结构', false, '缺少: ' + missing.join(', '));
                return false;
            }
        } catch (e) {
            this.addResult('菜单数据结构', false, '检查过程出错: ' + e.message);
            return false;
        }
    },
    
    // 测试模块图标和标题函数
    testModuleFunctions: function() {
        try {
            if (typeof menuBar === 'undefined' || typeof menuBar.getModuleIcon !== 'function') {
                this.addResult('模块函数测试', false, 'getModuleIcon函数不存在');
                return false;
            }
            
            // 测试图标函数
            var testCases = [
                { input: '1', expected: 'fa-keyboard-o' },
                { input: '2', expected: 'fa-medkit' },
                { input: '3', expected: 'fa-bar-chart-o' },
                { input: '4', expected: 'fa-desktop' },
                { input: '999', expected: 'fa-folder' }
            ];
            
            var allPassed = true;
            for (var i = 0; i < testCases.length; i++) {
                var result = menuBar.getModuleIcon(testCases[i].input);
                if (result !== testCases[i].expected) {
                    allPassed = false;
                    break;
                }
            }
            
            // 测试标题函数
            var titleTestCases = [
                { input: '1', expected: '快捷操作' },
                { input: '2', expected: '科室业务' },
                { input: '3', expected: '统计报表' },
                { input: '4', expected: '科室管理' },
                { input: '999', expected: '其他' }
            ];
            
            for (var j = 0; j < titleTestCases.length; j++) {
                var titleResult = menuBar.getModuleTitle(titleTestCases[j].input);
                if (titleResult !== titleTestCases[j].expected) {
                    allPassed = false;
                    break;
                }
            }
            
            if (allPassed) {
                this.addResult('模块函数测试', true, '图标和标题函数工作正常');
                return true;
            } else {
                this.addResult('模块函数测试', false, '函数返回值不符合预期');
                return false;
            }
        } catch (e) {
            this.addResult('模块函数测试', false, '测试过程出错: ' + e.message);
            return false;
        }
    },
    
    // 测试DOM结构
    testDOMStructure: function() {
        try {
            var sideMenu = document.getElementById('side-menu');
            var hasSubmenu = sideMenu && sideMenu.classList.contains('submenu');
            
            if (hasSubmenu) {
                this.addResult('DOM结构测试', true, '菜单DOM结构正确');
                return true;
            } else {
                this.addResult('DOM结构测试', false, '菜单DOM结构不正确');
                return false;
            }
        } catch (e) {
            this.addResult('DOM结构测试', false, '检查过程出错: ' + e.message);
            return false;
        }
    },
    
    // 运行所有测试
    runAllTests: function() {
        console.log('=== 开始菜单改造测试 ===');
        
        this.testResults = [];
        
        this.testVueInstances();
        this.testMenuDataStructure();
        this.testModuleFunctions();
        this.testDOMStructure();
        
        console.log('=== 测试完成 ===');
        this.printSummary();
        
        return this.testResults;
    },
    
    // 打印测试摘要
    printSummary: function() {
        var passed = 0;
        var failed = 0;
        
        for (var i = 0; i < this.testResults.length; i++) {
            if (this.testResults[i].passed) {
                passed++;
            } else {
                failed++;
            }
        }
        
        console.log('\n=== 测试摘要 ===');
        console.log('总测试数: ' + this.testResults.length);
        console.log('通过: ' + passed);
        console.log('失败: ' + failed);
        console.log('成功率: ' + Math.round((passed / this.testResults.length) * 100) + '%');
        
        if (failed > 0) {
            console.log('\n失败的测试:');
            for (var j = 0; j < this.testResults.length; j++) {
                if (!this.testResults[j].passed) {
                    console.log('- ' + this.testResults[j].name + ': ' + this.testResults[j].message);
                }
            }
        }
    }
};

// 页面加载完成后自动运行测试
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            MenuTester.runAllTests();
        }, 2000); // 等待2秒让Vue实例初始化完成
    });
} else {
    setTimeout(function() {
        MenuTester.runAllTests();
    }, 2000);
}

// 导出测试工具供手动调用
window.MenuTester = MenuTester;
