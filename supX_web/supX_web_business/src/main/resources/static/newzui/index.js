/**
 * Created by mash on 2017/12/21.
 */
window.J_tabLeft = new Vue({
    el: '.J_menuTabs',
    mixins: [baseFunc, tableBase, scrollOps],
    data: {
        userInfo: null,
        userId: null,
        userName: null,
        ksbm: '',
        ybdm: null,
        jszgdm: '', //技术资格代码
        pageLists: [{"name": '首页', "url": "workbench.html"}],
        isSelect: 0,
        socketCd: null,
        ifok: false,
        rs: false,
        rlmsg: null,
        heratTime2: null,
        socketCard: null,
        ifokCard: false,
        rsCard: false,
        rlmsgCard: null,
        heratTimeCard: null,
        gsjkptBm: '',
        obj: {
            frprintver: "3",
            spdUrl: 'http://************:8888',
            FrUrl: location.origin
        },
        IDCardType: '', //身份证读卡器类：01-华大 02-华旭FD3X
        socketzt: null,//卡解密状态
        param: {
            page: 1,
            rows: 10,
            sort: '',
            order: 'asc',
            parm: ''
        },
        userIpAddress: null,
        csqxparm: {
            "mac": "",
            "csbm": "",
            "cname": "",
            "Ipaddr": ""
        },
        cd014_index_obj: {},
    },
    created: function () {
        this.getUserInfo();
        //加载所有下拉信息
        this.getSelectData();
    },
    methods: {
        GetGlobalparameter: function () {
            //获取系统定义的全局参数
            var csparm = {
                "czybm": userId
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=allcsqx&parm=" + JSON.stringify(csparm), function (json) {
                console.log("获取全局参数：" + csparm);
                if (json.a == 0 && json.d != null && json.d.length > 0) {
                    for (var ii = 0; ii < json.d.length; ii++) {
                        var csjson = json.d[ii];
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400101") {
                            J_tabLeft.IDCardType = csjson.csz;//身份证读卡器类型
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400105") {
                            J_tabLeft.obj.gsjkptBm = csjson.csz;//甘肃健康平台类型
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400103") {
                            J_tabLeft.obj.gsjbpxBm = csjson.csz;//甘肃健康平台类型
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N04003002200207") {
                            J_tabLeft.obj.sfhbxtyp = csjson.csz;//药房处方发药是否合并相同药品
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N03003200102") {
                            J_tabLeft.obj.zyyzbm = csjson.csz;//药房处方发药是否合并相同药品
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400111") {
                            if (!csjson.csz) {
                                J_tabLeft.obj.hlyy = "0";
                            } else {
                                J_tabLeft.obj.hlyy = csjson.csz;//0-无,1-美康合理用药'
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400112") {
                            if (!csjson.csz) {
                                J_tabLeft.obj.ygjc = "0";
                            } else {
                                J_tabLeft.obj.ygjc = csjson.csz;//0-无,1-易欧院感监测
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400113") {
                            if (!csjson.csz) {
                                J_tabLeft.obj.ygyycx = "0";
                            } else {
                                J_tabLeft.obj.ygyycx = csjson.csz;//101','101','输入应用程序ID
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400114") {
                            J_tabLeft.obj.hyyydz = csjson.csz;//合理用药服务器地址
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400115") {
                            J_tabLeft.obj.xwpacsdz = csjson.csz || location.origin;//新网PACS接口地址
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400117") {
                            J_tabLeft.obj.frprintver = csjson.csz;//帆软报表打印版本 3-打印版本3.0，4-打印版本4.0
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400128") {
                            J_tabLeft.obj.hljkurl = csjson.csz;//护理接口url
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400129") {
                            if (csjson.csz) {
                                J_tabLeft.obj.flpxdjkurl = csjson.csz;//飞利浦心电接口url
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400126") {
                            if (csjson.csz) {
                                J_tabLeft.obj.spdUrl = csjson.csz;//spd接口url
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400130") {
                            if (csjson.csz) {
                                J_tabLeft.obj.FrUrl = csjson.csz;//帆软接口url
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400131") {
                            if (csjson.csz) {
                                J_tabLeft.obj.ssUrl = csjson.csz;//大成手术接口地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400132") {
                            if (csjson.csz) {
                                J_tabLeft.obj.icuUrl = csjson.csz;//'大成ICU接口地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400133") {
                            if (csjson.csz) {
                                J_tabLeft.obj.scZnshUrl = csjson.csz;//四川智能审核事前接口
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400134") {
                            if (csjson.csz) {
                                J_tabLeft.obj.gzyjUrl = csjson.csz;//贵州药监接口地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400135") {
                            if (csjson.csz) {
                                J_tabLeft.obj.crbsbId = csjson.csz;//传染病上报应用程序ID
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400136") {
                            if (csjson.csz) {
                                J_tabLeft.obj.cdssUrl = csjson.csz;//cdss调用地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400137") {
                            if (csjson.csz) {
                                J_tabLeft.obj.wxApiUrl = csjson.csz;//微信接口调用地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400142") {
                            if (csjson.csz) {
                                J_tabLeft.obj.hzNisUrl = csjson.csz;//杭州护理接口调用地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400147") {
                            if (csjson.csz) {
                                J_tabLeft.obj.FRorWindow = csjson.csz;//全局默认打印方式
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400202") {
                            if (csjson.csz) {
                                J_tabLeft.obj.scburl = csjson.csz;//易欧传染病确认webservice地址  默认''
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400149") {
                            if (csjson.csz) {
                                J_tabLeft.obj.dybgurl = csjson.csz;//配置在线调阅参数地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N010064001150") {
                            if (csjson.csz) {
                                J_tabLeft.obj.xddybgurl = csjson.csz;//配置在线调阅参数地址
                                continue;
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400203") {
                            if (csjson.csz == '1') {

                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400204") {
                            if (csjson.csz) {
                                J_tabLeft.obj.mtysQx = csjson.csz;//门特显示审核按钮医生编码
                            }
                        }
                        if (csjson.csqxbm != undefined && csjson.csqxbm == "N01006400205") {
                            if (csjson.csz) {
                                J_tabLeft.obj.hyxxBm = csjson.csz;
                                continue;
                            }
                        }
                    }
                }
            });
        },
        start: function () {
            var wsImpl = window.WebSocket || window.MozWebSocket;

            var hostcard = "ws://localhost:13000/";
            if (!J_tabLeft.socketCard) {
                J_tabLeft.socketCard = new wsImpl(hostcard);
                console.log(".. connection open 设备连接成功");
            } else {
                console.log(".. connection  设备已打开");
            }
            try {
                J_tabLeft.socketCard.onerror = function () {
                    J_tabLeft.socketzt = "连接异常,请刷新";
                    console.log(".. connection  错误");
                    clearInterval(J_tabLeft.heratTimeCard);
                    J_tabLeft.socketCard.close();
                };
                this.socketCard.onopen = function () {
                    J_tabLeft.heratTimeCard = setInterval(function () {
                        J_tabLeft.socketCard.send("heratBeat&" + new Date());
                    }, 15000);
                    J_tabLeft.socketCard.send("");
                    console.log(".. connection open打开");
                };
                this.socketCard.onclose = function () {
                    // J_tabLeft.socketzt = "连接异常,请刷新";
                    //malert('身份证读卡服务连接关闭', 'top', 'defeadted')
                    console.log('.. connection closed关闭');
                    clearInterval(J_tabLeft.heratTimeCard);
                    J_tabLeft.socketCard.close();
                };
                this.socketCard.onmessage = function (evt) {
                    console.log("onmessage:" + evt.data);
                    J_tabLeft.rsCard = true;
                    console.log("消息传送正常！");
                    var msg = eval('(' + evt.data + ')');
                    if (msg.ComputerIp != null) {
                        //服务正常后调参数
                        J_tabLeft.csqxparm = {
                            "mac": msg.ComputerMac,
                            "csbm": "",
                            "cname": msg.ComputerName,
                            "Ipaddr": msg.ComputerIp
                        };
                    }
                    J_tabLeft.rslmsgCard = msg.mess;
                    if (msg.code == "success") {
                        J_tabLeft.ifokCard = true;
                    } else {
                        J_tabLeft.ifokCard = false;
                        malert("身份证读卡服务未正常运行！", 'top', 'defeadted');
                    }
                };
            } catch (ex) {
                console.log(".. connection  身份证连接异常,请检查");
            }

            //取本机信息
            this.updatedAjax("http://localhost:18888/computerData", function (json) {
                J_tabLeft.csqxparm = {
                    "mac": json.ComputerMac,
                    "csbm": "",
                    "cname": json.ComputerName,
                    "Ipaddr": json.ComputerIp
                };
            });
        },
        getUserInfo: function () {
            let that = this;
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction').then(function (json) {
                console.log(json.body.d)
                if (json.body.d != null) {
                    this.userInfo = json.body.d;
                    this.userId = json.body.d.czybm;
                    this.ksbm = json.body.d.ksbm;
                    this.userName = json.body.d.czyxm;
                    userName = json.body.d.czyxm;
                    navli.userName = json.body.d.czyxm;
                    navli.userId = json.body.d.czybm;
                    pop.userId = json.body.d.czybm;
                    sessionStorage.setItem('userName' + userId, JSON.stringify(this.userName));
                    this.jszgdm = json.body.d.jszgdm;
                    loadModular.loadModule();
                    //获取全局参数  whq 2018-12-19
                    J_tabLeft.GetGlobalparameter();

                    $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + json.body.d.czybm, function (json1) {
                        //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                        console.log(json1.d.ybdm)
                        navli.ybdm = json1.d.ybdm;
                    });


                }
            });
        },
        //下拉框加载科室
        ksbmselect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm", function (json) {
                if (json.d != null) pop.ksbmList = json.d.list;
            });
        },

        //下拉框加载医疗机构
        yijgselect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yljg&dg=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) pop.yljgList = json.d.list;
            });
        },
        //下拉框加载民族
        mzselect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=mzbm&dg=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) pop.mzList = json.d.list;
            });
        },
        //下拉框加载学历
        xlselect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=xlbm", function (json) {
                if (json.d != null) pop.xlList = json.d.list;
            });
        },
        //下拉框加载所学专业
        sxzyselect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=sxzy&dg=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) pop.sxzyList = json.d.list;
            });
        },
        //下拉框加载专业类别代码
        zylbdmSelect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zylbdm&dg=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) pop.zylbdmList = json.d.list;
            });
        },
        //下拉框加载技术资格代码
        zyjszgdmselect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyjszgdm&dg=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) pop.zyjszgdmList = json.d.list;
            });
        },
        //下拉框记载职务级别
        zwjbselect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zwjb&dg=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) pop.zwjbList = json.d.list;
            });
        },
        getSelectData: function () {
            this.ksbmselect(); //下拉框加载科室
            this.yijgselect(); //下拉框加载医疗机构
            this.mzselect(); //下拉框加载民族
            this.xlselect(); //下拉框加载学历
            this.sxzyselect(); //下拉框加载所学专业
            this.zylbdmSelect(); //专业类别代码
            this.zyjszgdmselect(); //专业技术资格代码
            this.zwjbselect(); //职务级别
            this.getQhtfybUserip();//清华同方医保IP
        },
        getQhtfybUserip: function () {
            var parm = {
                czybm: userId,
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=qhtfIpSelect&json=" + JSON.stringify(parm), function (json) {
                if (json.a == '0' && json.d != null && !json.d.list) {
                    J_tabLeft.userIpAddress = json.d.list[0].ipaddr;
                    pop.zwjbList = json.d.list;
                    console.log(J_tabLeft.userIpAddress);
                } else {

                }
            });
        },
        createWebSocket: function () {
            $.ajaxSettings.async = false;
            var wsurl = null;
            var parm = {
                page: 1,
                rows: 20,
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=Wsconfig&types=query&json=" + JSON.stringify(parm), function (json) {
                if (json.a == "0" && json.d != null) {
                    wsurl = json.d.list[0];
                    console.log(wsurl)
                } else {
                    malert("消息通知未配置！", 'top', 'defeadted');
                    return false
                }
            });
            J_tabLeft.socket = new WebSocket(wsurl.address + "?userId=" + userId + "&jgbm=" + jgbm + "&yqbm=" + yqbm + "&ver=1");
            // J_tabLeft.socket = new WebSocket("ws://*************:8001/ws.do" + "?userId=" + userId + "&jgbm=" + jgbm + "&yqbm=" + yqbm + "&ver=1");
            // J_tabLeft.socket = new WebSocket("ws://*************:8001/ws.do" + "?userId=" + userId + "&jgbm=" + jgbm + "&yqbm=" + yqbm + "&ver=1");
            // J_tabLeft.socket = new WebSocket("ws://**************:9082/ws.do" + "?userId=" + userId + "&jgbm=" + jgbm + "&yqbm=" + yqbm + "&ver=1");

            J_tabLeft.socket.onopen = function () {
                //                	header.socket.send("start: " + new Date());
                //console.log("webSocket已连接");
                // 激活心跳包
                J_tabLeft.heartPack();
            };
            J_tabLeft.socket.onmessage = function (msg) {
                var noticeobj = JSON.parse(msg.data);
                if (Object.prototype.toString.call(noticeobj) === '[object Array]') {
                    navli.noticeList = noticeobj;
                } else if (noticeobj.type == 'add') {
                    // navli.noticeShow=true;
                    var ifAdd = true;
                    for (var count = 0; count < navli.noticeList.length; count++) {
                        if (navli.noticeList[count].msgtype == noticeobj.msgtype && navli.noticeList[count].sbid == noticeobj.sbid) {//不重复发消息
                            ifAdd = false;
                        }
                    }
                    if (ifAdd) {
                        navli.noticeList.push(noticeobj);
                    }


                    // navli.noticeShow = true;
                } else {
                    var length = 0;
                    var listbak = navli.noticeList;
                    for (var i = 0; i < listbak.length; i++) {
                        if (listbak[i].sbid == noticeobj.sbid) {
                            navli.noticeList.splice(i - length, 1);
                            length = length + 1;
                            break;
                        }
                    }
                }

            };
            J_tabLeft.socket.onclose = function () {
                J_tabLeft.socket.close();
                // J_tabLeft.stopHeartBeat();
                console.log("webSocket已关闭");
            };
            J_tabLeft.socket.onerror = function () {
                J_tabLeft.socket.close();
                // J_tabLeft.stopHeartBeat();
                console.log("webSocket已断开");
            };
        },


        //            心跳包发送（保活）--每15秒发送一次心跳
        heartPack: function () {
            let that = this;
            this.heartTime = setInterval(function () {
                try {
                    if (J_tabLeft.socket.readyState == WebSocket.OPEN) {
                        J_tabLeft.socket.send("heartBeat: " + new Date());
                    } else {
                        that.stopHeartBeat();
                        that.createWebSocket();
                    }

                } catch (e) {
                    that.stopHeartBeat();
                    console.log(e)
                    that.createWebSocket();
                }

            }, 15000)
            // this.heartTime = setInterval(function () {
            //                 J_tabLeft.socket.send("heartBeat: " + new Date());
            //             }, 15000)
        },
        //停止心跳包
        stopHeartBeat: function () {
            clearInterval(this.heartTime);
        },
        tab: function (i, id) {
            rightMenu.isSelect = i;
            this.isSelect = i;
            // if (rightMenu.CacheList[name]) {
            //     var _iframe1 = window.document[name];
            //     _iframe1.location.reload(true);
            // }
            $('#' + id).addClass("menuLiSelected").parents('.submenu').find('li').removeClass("menuLiSelected");
            $('#' + id).addClass("menuLiSelected");
        },
        move: function (direction) {
            if (rightMenu.isStop) return;
            this.isStop = true;
            if (direction == 'left') {
                this.$refs["pageTabs"].scrollTo({x: +100}, 500);
                rightMenu.isStop = false;
            } else if (direction == 'right') {
                this.$refs["pageTabs"].scrollTo({x: -100}, 500);
                rightMenu.isStop = false;
            } else {
                this.isStop = false;
            }
        },
        closeCurrent: function () {
            // if(this.pageLists.length<=1){
            //     layer.msg("老板,你总要留一个吗！");
            // }else{
            this.pageLists.splice(this.isSelect, 1);
            this.isSelect--;
            rightMenu.isSelect = this.pageLists.length - 1;
            // }
        },
        removeArray: function (val, index) {
            for (var i in val) {
                if (i == index) {
                    return
                } else {

                }
            }
        },
        //成都医保自动签到/签退
        cd_014_sign: function () {
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (bxjson) {
                if (bxjson.a == 0) {
                    if (bxjson.d.list.length > 0) {
                        J_tabLeft.cd014_index_obj.bxlbbm = bxjson.d.list[0].bxlbbm;
                        J_tabLeft.cd014_index_obj.bxurl = bxjson.d.list[0].url;
                        //初始化

                        $.ajax({
                            url: "http://localhost:10014/init",
                            type: "POST",
                            data: {},
                            timeout: 2000,
                            dataType: "json",
                            success: function (initjson) {
                                if (initjson.aint_appcode > 0) {
                                    //签到
                                    let jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><control><aae011>' + J_tabLeft.userInfo.czyxm + '</aae011></control>';
                                    let jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?><data><yab003>0000</yab003></data>';
                                    $.post("http://localhost:10014/call", {
                                        'jybh': '05',
                                        'jykz_xml': jykz,
                                        'jysr_xml': jysr,
                                    }, function (sign05json) {
                                        if (sign05json.aint_appcode > 0) {
                                            var res = JSON.parse(sign05json.astr_jysc_xml)[0];
                                            //成功赋值
                                            J_tabLeft.cd014_index_obj.yab003 = res.yab003; //医保经办机构
                                            J_tabLeft.cd014_index_obj.yke190 = res.yke190; //签到状态
                                            J_tabLeft.cd014_index_obj.yke189 = res.yke189; //新批次编号
                                            J_tabLeft.cd014_index_obj.yke191 = res.yke191; //签到时间
                                            J_tabLeft.sign05();
                                        }
                                    });
                                }
                            },
                            error: function (e) {
                                malert(e.statusText, 'top', 'defeadted')
                            }
                        });
                    }
                }
            });
        },
        sign05: function (data) {
            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + J_tabLeft.cd014_index_obj.bxurl + "&bxlbbm=" + J_tabLeft.cd014_index_obj.bxlbbm + "&types=cmn&method=sign05&parm="
                + JSON.stringify(J_tabLeft.cd014_index_obj),
                function (json) {
                    console.log(JSON.stringify(json));
                    if (json.a == 0 && json.d) {
                        var res = JSON.parse(json.d);
                        J_tabLeft.sign06(res)
                    }
                });
        },

        sign06: function (res) {
            let jykz = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>' +
                '<control>' +
                '<yke189>' + res.yke189 + '</yke189>' +
                '<yab003>' + res.yab003 + '</yab003>' +
                '</control>';
            let jysr = '<?xml version="1.0" encoding="GBK" standalone="yes" ?>' +
                '<data>' +
                '<jybs>' + res.jybs + '</jybs>' +
                '<sbjjzfze>' + res.sbjjzfze + '</sbjjzfze>' +
                '</data>';
            $.post("http://localhost:10014/call", {
                'jybh': '06',
                'jykz_xml': jykz,
                'jysr_xml': jysr,
            }, function (json06) {
                console.log(JSON.stringify(json06));
                if (json06.aint_appcode > 0) {
                    var str_param = {
                        id: res.id,
                        yke189: res.yke189,
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + J_tabLeft.cd014_index_obj.bxurl + "&bxlbbm=" + J_tabLeft.cd014_index_obj.bxlbbm + "&types=cmn&method=sign06&parm="
                        + JSON.stringify(str_param),
                        function (json) {
                            console.log(JSON.stringify(json));
                        });
                }
            });
        }
    },
    watch: {
        'pageLists': {
            deep: true,
            handler: function (newVal, oldVal) {
                immediate: true;
                if (J_tabLeft.pageLists.length == 0) {
                    J_tabLeft.pageLists.push({"name": '首页', "url": "workbench.html"});
                    rightMenu.isSelect = 0;
                    this.isSelect = 0;
                }
                ;
            }
        }
    }
});
var menuBar = new Vue({
    el: '.menubar',
    mixins: [baseFunc, tableBase],
    data: {
        showFa: {},
        iconName: null,
        mkbm: '',
        tree_tem: true,
        menuData: [],
        allModuleMenus: [], // 存储所有模块的菜单数据
        pageLists: [],
        isFold: null
    },

    methods: {

        loadChildMenu: function (id) {
            var _id = $("#" + id).find('ul');
            if (_id.css("display") == "none") {
                $(".menu .arrowIcon").removeClass("fa-angle-down").addClass("fa-angle-right");
                _id.slideDown(200).parent().siblings().find("ul").slideUp(200);
                _id.prev('div').addClass("menuSelected").parent().siblings().find("div").removeClass("menuSelected");
                _id.parent().find(".arrowIcon").removeClass("fa-angle-right").addClass("fa-angle-down");
            } else {
                _id.slideUp(200);
                $(".menu div").removeClass("menuSelected");
                _id.parent().find(".arrowIcon").removeClass("fa-angle-down").addClass("fa-angle-right");
            }
        }
    }
});

var loadModular = new Vue({
    el: '.modularDiv',
    data: {
        moduleList: [],
        menuTitle: '门诊挂号',
        rolemenu: false,
    },
    methods: {
        created: function () {
            //webSocket
            if (window.WebSocket == "undefined") {
                malert('您的浏览器不支持webSocket，会影响消息推送功能，建议更换更高版本的浏览器');
                return false;
            } else {
                setTimeout(function () {
                    this.rSocket = J_tabLeft.socket;
                }, 5000);
            }
        },
        showmodule: function (index) {
            this.rolemenu = true
        },
        showmoduleout: function (index) {
            this.rolemenu = false
        },
        loadModule: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=MainAction&czybm=' + userId +
                '&types=New1QueryXtmkCzy').then(function (json) {
                if (json.body.a == 1) {
                    malert("系统模块加载失败", 'top', 'defeadted');
                } else {
                    // 加载模块
                    this.moduleList = json.body.d;

                    // 加载所有模块的菜单
                    this.loadAllModuleMenus();
                }
            }, function (error) {
            });
        },

        // 新增方法：加载所有模块的菜单
        loadAllModuleMenus: function() {
            var that = this;
            var loadedCount = 0;
            var totalModules = this.moduleList.length;

            // 清空之前的菜单数据
            menuBar.allModuleMenus = [];

            // 遍历所有模块，加载每个模块的菜单
            for (var i = 0; i < this.moduleList.length; i++) {
                var module = this.moduleList[i];
                that.$http.get('/actionDispatcher.do?reqUrl=MainAction&czybm=' + userId + '&types=New1QueryMkqxCzy&mkbm=' + module.mkbm)
                    .then(function (json) {
                        loadedCount++;

                        if (json.body.d != null && json.body.d.length > 0) {
                            // 将菜单数据按types分组添加到allModuleMenus
                            for (var j = 0; j < json.body.d.length; j++) {
                                menuBar.allModuleMenus.push(json.body.d[j]);
                            }
                        }

                        // 当所有模块菜单都加载完成后，设置默认选中第一个模块
                        if (loadedCount === totalModules) {
                            // 排序菜单，确保显示顺序正确
                            menuBar.allModuleMenus.sort(function(a, b) {
                                return parseInt(a.types) - parseInt(b.types);
                            });

                            // 设置第一个模块为当前模块
                            if (that.moduleList.length > 0) {
                                that.menuTitle = that.moduleList[0].mkmc;
                                menuBar.mkbm = that.moduleList[0].mkbm;
                                navli.mkbm = that.moduleList[0].mkbm;
                            }
                        }
                    }, function (error) {
                        loadedCount++;
                        console.log('加载模块菜单失败:', error);
                    });
            }
        },
        loadMenu: function (id, title, url, type) {
            // 保持原有的loadMenu方法兼容性，但现在主要用于设置当前选中的模块
            menuBar.mkbm = id;
            navli.mkbm = id;

            if (type == false) {
                this.rolemenu = false
            }
            $('.itemList').eq(0).show();
            this.menuTitle = title;
            menuBar.showFa = {};
            menuBar.iconName = this.iconUrl + url;

            // 由于现在所有菜单都已经加载，这里只需要更新当前选中状态
            // 不再需要重新加载菜单数据

            if (menuBar.isFold == true) menuBar.menuFolding();
            $('.rolemenu1').removeClass('hiderole');
            if (!$('.menu_state').hasClass('open')) menuState('menu_state')
        }
    }
});

window.navli = new Vue({
    el: '.userbard',
    mixins: [baseFunc, tableBase],
    data: {
        noticeShow: false,
        remainShow: false,
        noticeList: [],
        mkbm: '',
        yourIp: '',
        ksrq: '',
        jsrq: '',
        userName: '',
        userId: '',
        ybdm: null,
        addrs: Object.create(null),
    },
    created: function () {
        this.getYourIP();

    },
    methods: {

        closeOther: function () {
            var arrVal = rightMenu.pageLists[rightMenu.isSelect];
            J_tabLeft.pageLists = [];
            rightMenu.pageLists = [];
            J_tabLeft.pageLists.push({"name": '首页', "url": "workbench.html"}, arrVal);
            rightMenu.pageLists.push({"name": '首页', "url": "workbench.html"}, arrVal);
            rightMenu.isSelect = 1;
            J_tabLeft.isSelect = 1
        },
        closeAll: function () {
            J_tabLeft.pageLists = [];
            // rightMenu.pageLists = [{"name": '首页', "url": "workbench.html"}];
            // this.isSelect = 0;
            // rightMenu.isSelect = 0;
        },
        getYourIP: function () {
            var RTCPeerConnection = window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection;
            if (RTCPeerConnection) (function () {
                var rtc = new RTCPeerConnection({iceServers: []});
                if (1 || window.mozRTCPeerConnection) {
                    rtc.createDataChannel('', {reliable: false});
                }
                rtc.onicecandidate = function (evt) {
                    if (evt.candidate) navli.grepSDP(`a=${evt.candidate.candidate}`);
                };
                rtc.createOffer(function (offerDesc) {
                    navli.grepSDP(offerDesc.sdp);
                    rtc.setLocalDescription(offerDesc);
                }, function (e) {
                });
                // navli.addrs['0.0.0.0'] = false;
            })();
            else {
                console.warn('请使用主流浏览器：chrome,firefox,opera,safari');
            }
        },
        updateDisplay: function (newAddr) {
            if (newAddr in navli.addrs) return;
            navli.addrs[newAddr] = true;
            var displayAddrs = Object.keys(navli.addrs).filter(function (k) {
                return navli.addrs[k];
            });
            for (let i = 0; i < displayAddrs.length; i++) {
                if (displayAddrs[i].length > 16) {
                    displayAddrs.splice(i, 1);
                    i--;
                }
            }
            navli.yourIp = 'IP:' + displayAddrs && displayAddrs[0]
        },
        grepSDP: function (sdp) {
            sdp.split('\r\n').forEach(function (line, index, arr) {
                if (~line.indexOf('a=candidate')) {
                    var parts = line.split(' '),
                        addr = parts[4],
                        type = parts[7];
                    if (type === 'host') navli.updateDisplay(addr);
                }
                // else if (~line.indexOf('c=')) {
                //     var parts = line.split(' '),
                //         addr = parts[2];
                //     navli.updateDisplay(addr);
                // }
            });
        },
        deleteWs: function (item) {
            console.log("进入删除");
            var sendmsg = {
                msgtype: '9',
                ksbm: item.ksbm,
                yljgbm: jgbm,
                yqbm: yqbm,
                msg: "delete",
                toaddress: '护士站',
                sbid: item.sbid,
                ylbm: item.ylbm,
            };
            J_tabLeft.socket.send(JSON.stringify(sendmsg));
            console.log(item);
            this.topNewPage(item.pagename, item.url);
            var Noticeobj = {
                'zyh': item.sbid.substring(0, item.sbid.lastIndexOf('_')),
                'hszUse': item.sbid.substring(0, item.sbid.lastIndexOf('_')),
                'msgtype': item.sbid.split('/')[1] || item.msgtype,
                'ksbm': item.ksbm,
            };
            sessionStorage.setItem('zyh', item.sbid.substring(0, item.sbid.lastIndexOf('_')));
            sessionStorage.setItem('hszHzlbUpdate', item.sbid.substring(0, item.sbid.lastIndexOf('_')));
            sessionStorage.setItem('NoticeObj' + userId, JSON.stringify(Noticeobj));
            if (item.msgparam && item.msgparam != 'undefined') {
                sessionStorage.setItem('messageParam', JSON.stringify(item.msgparam));
            }


        },
        deleteWsAll: function () {
            for (var i = 0; i < window.navli.noticeList.length; i++) {
                console.log("进入删除");
                var item = window.navli.noticeList[i];
                var sendmsg = {
                    msgtype: '9',
                    ksbm: item.ksbm,
                    yljgbm: jgbm,
                    yqbm: yqbm,
                    msg: "delete",
                    toaddress: '护士站',
                    sbid: item.sbid,
                    ylbm: item.ylbm,
                };
                J_tabLeft.socket.send(JSON.stringify(sendmsg));
                console.log("删除成功");
            }
        },
        tagShowOne: function () {
            this.remainShow = !this.remainShow;
            this.noticeShow = false
        },
        tagShow: function () {

            this.noticeShow = !this.noticeShow;
            this.remainShow = false
            this.$forceUpdate()
        },
        themStyle: function () {
            window.location = window.location.origin + "/theme/themeStyle.html?czybm=" + userId + "&yqbm=" + yqbm + "&yljgbm=" + jgbm
        },
        userNameCk: function () {
            //下拉框加载科室
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                pop.popContent = json.d
                pop.isShow = true
            });
        },
        updatedPwd: function () {
            window.location = "/newzui/page/changePsw/changePsw.html?czybm=" + this.userId + "&yqbm=" + yqbm + "&yljgbm=" + jgbm
            // pwd.bqcy=true
        },
        colsedelete: function () {


            sessionStorage.clear()
            this.delAllCookie()
            layer.msg("老板,正在为您注销中...");
            setTimeout(function () {
                window.location.href = "login.html";
            }, 1600);
        },
        setingIndexShortcutMenu: function () {
            setingIndexShortcutMenu.open();
        }
    },
});
var pop = new Vue({
    el: '#pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat, scrollOps],
    data: {
        isShow: false,
        mkbm: '',
        previewshow: false,
        clearNum: 0,
        index: 0,
        edit: true,
        userId: '',
        popContent: {},
        mzList: [],
        ksbm: [],
        ksbmList: [],
        sxzyList: []
    },
    methods: {
        save: function () {
            this.mkbm = '';
            window.sessionStorage.clearNum = 1
        },
        shanchu: function () {

        },
        preview: function () {
            this.previewshow = true
        },
        submit: function () {
            pop.popContent['ksmc'] = pop.listGetName(pop.ksbmList, pop.popContent.ksbm, 'ksbm', 'ksmc');
            pop.popContent['mzmc'] = pop.listGetName(pop.mzList, pop.popContent.mzbm, 'mzbm', 'mzmc');
            pop.popContent['zylbmc'] = pop.listGetName(pop.zylbdmList, pop.popContent.zylbbm, 'zylbbm', 'zylbmc');
            pop.popContent['xlmc'] = pop.listGetName(pop.xlList, pop.popContent.xl, 'xlbm', 'xlmc');
            pop.popContent['sxzymc'] = pop.listGetName(pop.sxzyList, pop.popContent.sxzydm, 'sxzybm', 'sxzymc');
            pop.popContent['jszgmc'] = pop.listGetName(pop.zyjszgdmList, pop.popContent.jszgdm, 'jszgbm', 'jszgmc');
            pop.popContent['jszwmc'] = pop.listGetName(pop.zwjbList, pop.popContent.jszwdm, 'zwbm', 'zwmc');
            //保存
            var json = JSON.stringify(this.popContent);
            this.$http.post('/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=save&',
                json).then(function (data) {
                if (data.body.a == "0" || data.a == "0") {
                    malert("保存成功！", 'top', 'success');
                    window.close();
                } else {
                    malert(data.body.c, 'top', 'defeadted');
                }
            });
        },
        previewHide: function () {
            this.previewshow = false
        },
        tabActive: function (index) {
            this.index = index
        },
    },
});
loadModular.loadModule();
window.rightMenu = new Vue({
    el: '.rightMenu',
    mixins: [baseFunc],
    components: {
        'calendar': calendar,
    },
    data: {
        search: window.location.search,
        pageLists: [{"name": '首页', "url": "workbench.html"}],
        transitionName: 'expand',
        isSelect: 0,
        isDone: false,
        isFold: false,
        conTitle: "首页",
        testDate: null,
        isStop: false,
    },
    watch: {},
    methods: {
        refresh: function (item) {
            for (var i = 0; i < item.length; i++) {
                $('#iframeCon iframe').eq(i).attr('src', item[i].url).attr('name', item[i].name);
            }
        }
    }
});
var app = new Vue({
    el: '.app',
    mixins: [tableBase, baseFunc],
    data: {
        current: '',
        changeMode: false
    },
    updated: function () {
        this.$refs.current.focus()
    },
    mounted: function () {
        document.onkeydown = function (event) {
            if (event.code == 'F1' || event.keyCode == 112) {
                app.changeMode = true;
                return false
            }
        }
    },
    methods: {
        press: function (event) {
            var me = this;
            if (event.keyCode == 13) {
                if (this.current == '') {
                    return true
                } else {
                    event.target.textContent = '='
                }
            }
            var key = event.target.textContent;

            if (
                key != '=' &&
                key != 'C' &&
                key != '*' &&
                key != '/' &&
                key != '√' &&
                key != "x 2" &&
                key != "%" &&
                key != "<=" &&
                key != "±" &&
                key != "sin" &&
                key != "cos" &&
                key != "tan" &&
                key != "log" &&
                key != "ln" &&
                key != "x^" &&
                key != "x !" &&
                key != "π" &&
                key != "e" &&
                key != "rad" &&
                key != "°"
            ) {
                me.current += key

            } else if (key === '=') {

                if ((me.current).indexOf('^') > -1) {
                    var base = (me.current).slice(0, (me.current).indexOf('^'));
                    var exponent = (me.current).slice((me.current).indexOf('^') + 1);
                    me.current = eval('Math.pow(' + base + ',' + exponent + ')')
                } else {
                    if (String(eval(me.current)).indexOf('.') != -1) {
                        me.current = eval(me.current).toFixed(1)
                    } else {
                        me.current = eval(me.current)
                    }
                }

            } else if (key === 'C') {

                me.current = ''

            } else if (key === '*') {

                me.current += '*'

            } else if (key === '/') {

                me.current += '/'

            } else if (key === '+') {

                me.current += '+'

            } else if (key === '-') {

                me.current += '-'

            } else if (key === '±') {

                if ((me.current).charAt(0) === '-') {
                    me.current = (me.current).slice(1)
                } else {
                    me.current = '-' + me.current
                }

            } else if (key === '<=') {

                me.current = me.current.substring(0, me.current.length - 1)

            } else if (key === '%') {

                me.current = me.current / 100

            } else if (key === 'π') {

                me.current = me.current * Math.PI

            } else if (key === 'x 2') {

                me.current = eval(me.current * me.current)

            } else if (key === '√') {

                me.current = Math.sqrt(me.current)

            } else if (key === 'sin') {

                me.current = Math.sin(me.current)

            } else if (key === 'cos') {

                me.current = Math.cos(me.current)

            } else if (key === 'tan') {

                me.current = Math.tan(me.current)

            } else if (key === 'log') {

                me.current = Math.log10(me.current)

            } else if (key === 'ln') {

                me.current = Math.log(me.current)

            } else if (key === 'x^') {

                me.current += '^'

            } else if (key === 'x !') {

                var number = 1;
                if (me.current === 0) {
                    me.current = '1'
                } else if (me.current < 0) {
                    me.current = NaN
                } else {
                    var number = 1;
                    for (var i = me.current; i > 0; i--) {
                        number *= i
                    }
                    me.current = number
                }

            } else if (key === 'e') {

                me.current = Math.exp(me.current)

            } else if (key === 'rad') {

                me.current = me.current * (Math.PI / 180)

            } else if (key === '°') {

                me.current = me.current * (180 / Math.PI)

            }
        },
    }
});
$('.navlid .exit').on('click', function () {
    navli.colsedelete()
});
setTimeout(function () {
    if (window.WebSocket == "undefined") {
        malert('您的浏览器不支持webSocket，会影响消息推送功能，建议更换更高版本的浏览器');
        return false;
    } else {
        J_tabLeft.createWebSocket();
        J_tabLeft.start();
    }
}, 1000);
$(document).click(function () {
    navli.noticeShow = false;
    navli.remainShow = false;
});
var Mb = new Vue({
    el: '#Mb',
    data: {
        mb: false,
    },
    methods: {
        close: function () {
            this.mb = false
        },
    },
});
window.navbar = new Vue({
    el: '.navbar',
    data: {
        mendata: '',
        index: 0
    },
    methods: {
        openTab: function (type) {
            switch (type) {
                case 'cd':
                    Mb.mb = true;
                    break;
                case 'jsj':
                    app.changeMode = true;
                    break;
                case 'txl':
                    break;
            }
        },
        navlist: function (i) {
            // 保留方法兼容性，但现在不再需要切换菜单
            // 因为所有菜单都已经在左侧显示
            this.index = i;
            console.log('导航点击:', i, '但菜单已全部显示在左侧');
        }
    },
});
var params = {
    left: 0,
    top: 0,
    currentX: 0,
    currentY: 0,
    flag: false
};

//2018/07/23危急值弹窗

var crisis = new Vue({
    el: '.crisis-box',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer, scrollOps],
    data: {
        crShow: false,
        is_state: {
            '0': '',
            '1': ''
        },
        is_result: {
            '0': '25.00g/L',
            '1': '阴性'
        },
        datas: [
            {
                name: '白蛋白白蛋白白蛋白白蛋白白蛋白白蛋白',
                result: '0',
                wxbz: '0',
                state: '0'
            },
            {
                name: '白蛋白',
                result: '1',
                wxbz: '1',
                state: '1'
            },
            {
                name: '白蛋白',
                result: '1',
                wxbz: '1',
                state: '1'
            }
        ]
    },
    methods: {
        //接收跳转到对应页面
        Receive: function () {
            var myVideo = document.getElementById("audio");
            myVideo.pause();
            this.crShow = false;
            $('.crisis-box').fadeOut();
            //需要判断当前是哪个站发起的危急值 跳到对应的危急值页面 当前以门诊医生站为例
            this.topNewPage('危急值管理', 'page/xmzysz/mzys/wjzgl/wjzgl.html');


        },
        //患者跳转
        Gopatient: function () {
            //需要判断当前是哪个站发起的危急值 跳到对应的患者页面 当前以门诊医生站为例
            $('.crisis-box').fadeOut();
            this.topNewPage('病人接诊', 'page/xmzysz/mzys/jzgl/hzzx/hzzx.html')
        }

    }

});
var crisis1 = new Vue({
    el: '.crisis-box01',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer, scrollOps],
    data: {
        crShow: false,
        is_state: {
            '0': '',
            '1': ''
        },
        is_result: {
            '0': '25.00g/L',
            '1': '阴性'
        },
        datas: [
            {
                name: '白蛋白白蛋白白蛋白白蛋白白蛋白白蛋白',
                result: '0',
                state: '0'
            },
            {
                name: '白蛋白',
                result: '1',
                state: '1'
            },
            {
                name: '白蛋白',
                result: '1',
                state: '1'
            }
        ]
    },
    mounted: function () {
        this.getNextPage();
    },
    methods: {
        getNextPage: function () {
            if (this.getQueryString('IsNextPage')) return false
            if (this.getQueryString('openChildPage')) {
                var page = unescape(this.getQueryString('openChildPage'))
                this.topNewPage('接诊管理', page);
            }
        },
        //接收跳转到对应页面
        Receive: function () {
            var myVideo = document.getElementById("audio");
            myVideo.pause();
            this.crShow = false;
            $('.crisis-box').fadeOut();
            //需要判断当前是哪个站发起的危急值 跳到对应的危急值页面 当前以门诊医生站为例
            this.topNewPage('危急值管理', 'page/xmzysz/mzys/wjzgl/wjzgl.html');


        },
        //患者跳转
        Gopatient: function () {
            //需要判断当前是哪个站发起的危急值 跳到对应的患者页面 当前以门诊医生站为例
            $('.crisis-box').fadeOut();
            this.topNewPage('病人接诊', 'page/xmzysz/mzys/jzgl/hzzx/hzzx.html')
        }

    }

});

var setingIndexShortcutMenu = new Vue({
    el: "#seting-index-shortcut-menu",
    data: {
        ifShow: false,
        listAll: [],
        listAllSimple: [],
        listSelect: [],
        listSelectYLBM: {},
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        open: function () {
            this.ifShow = true;
            this.getData();
        },
        close: function () {
            this.ifShow = false;
        },
        getData: function () {
            this.getAll();
            this.getSelect();
        },
        filterFun: function (value) {
            if (value) {
                this.listAllSimple = [];
                for (var i = 0; i < this.listAll.length; i++) {
                    if (this.listAll[i]['ylmc'].indexOf(value) == 0 || this.listAll[i]['mkmc'].indexOf(value) == 0 || this.listAll[i]['pydm'].indexOf(value) == 0) {
                        this.listAllSimple.push(this.listAll[i]);
                    }
                }
            } else {
                this.listAllSimple = this.listAll

            }
        },
        getAll: function (parm) {
            $.getJSON("/actionDispatcher.do?reqUrl=New1Gyb_ylqxR&types=qxCzyAll", function (json) {
                if (json.a == '0' && json.d.length) {
                    setingIndexShortcutMenu.listAll = Object.freeze(json.d);
                    setingIndexShortcutMenu.listAllSimple = Object.freeze(json.d);
                } else {
                    console.log("首页权限用例请求失败！");
                }
            });
        },
        getSelect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1Gyb_ylqxR&types=CzyqxQuery", function (json) {
                if (json.a == '0' && json.d) {
                    var data = json.d;
                    var selectYLBM = {};
                    setingIndexShortcutMenu.listSelect = Object.freeze(data);
                    data.forEach(function (item) {
                        selectYLBM[item.ylbm] = true;
                    });
                    setingIndexShortcutMenu.listSelectYLBM = selectYLBM;
                } else {
                    console.log("首页权限用例请求失败！");
                }
            });
        },
        lableClick: function (ylbm) {
            common.delayOpenloading({el: "#seting-index-shortcut-menu .layui-layer-content"})
            if (!this.listSelectYLBM[ylbm]) this.selectShortcutMenu(ylbm);
            else this.deleteSM(ylbm);
        },
        selectShortcutMenu: function (ylbm) {
            $.getJSON("/actionDispatcher.do?reqUrl=New1Gyb_ylqxR&types=CzyqxInsert&ylbm=" + ylbm, function (json) {
                if (json.a == '0') {
                    Vue.set(setingIndexShortcutMenu.listSelectYLBM, ylbm, true);
                    setingIndexShortcutMenu.updateHomeShortcutMenu();
                } else {
                    console.log("添加快捷菜单【" + ylbm + "】到首页失败！")
                }
            });
        },
        deleteSM: function (ylbm) {
            $.getJSON("/actionDispatcher.do?reqUrl=New1Gyb_ylqxR&types=CzyqxDelete&ylbm=" + ylbm, function (json) {
                if (json.a == '0') {
                    Vue.set(setingIndexShortcutMenu.listSelectYLBM, ylbm, false)
                    setingIndexShortcutMenu.updateHomeShortcutMenu();
                } else {
                    console.log("从首页移除快捷菜单【" + ylbm + "】失败！")
                }
            });
        },
        updateHomeShortcutMenu: function () {
            sessionStorage.setItem("updateHomeShortcutMenu", new Date().getTime());
        }
    }
});

var pwd = new Vue({
    el: '#pwd',
    data: {
        json: {},
        bqcy: false,
        isClick: false
    },
    methods: {
        save: function () {
            if (this.isClick == true) return
            this.isClick = true
            if (this.json.oldPsw == '') {
                malert('旧密码输入错误', 'top', 'defeadted');
                this.isClick = false
                return false
            }
            if (this.json.newPsw == '') {
                malert('新密码输入错误', 'top', 'defeadted');
                this.isClick = false
                return false
            }
            this.$http.post('/actionDispatcher.do?reqUrl=MainAction&types=UpdateCzykl', JSON.stringify(this.json)).then(function (json) {
                if (json.body.a == '0') {
                    pwd.bqcy = false
                    pwd.isClick = false
                    malert(json.body.c, 'top', 'success');
                    return false;
                } else {
                    pwd.isClick = false
                    malert(json.body.c, 'top', 'defeadted');
                }
            }, function (error) {
                pwd.isClick = false
                malert(error.body.c, 'top', 'defeadted');
            });
        },
        close: function () {
            this.bqcy = false
        }
    },
})
laydate.render({
    elem: '#timeVal',
    type: 'datetime',
    rigger: 'click',
    theme: '#1ab394',
    done: function (value, data) { //回调方法
        if (value != '') {
            // content.popContent.shsj = value;
        } else {
            // content.popContent.shsj = '';
        }
    }
});
// layui.router

// $('#Result').speech({
//     "speech": false,//通过点击链接播报，还是直接播报
//     "speed": 2,//语速
//     "lang": "zh", //语言 lan=en 英文
//     //"content": "这是一段测试内容" //直接播报内容

// });

// var ss=Array.from({length:20}, (v,k) => k).reduce(function (next, val) {
//      [next.push({val:parseInt(Math.random()*(3001)+2000)})]
//     return next
// }, [])
//
// console.log(ss)
