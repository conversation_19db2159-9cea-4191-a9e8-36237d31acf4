<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta http-equiv="content-type" content="text/html" charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>登录 - 院级信息管理平台</title>
    <link rel="shortcut icon" href="favicon.ico">
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <style>
        .zui-select-inline:after{
            display: none;
        }
        .phone_c{
            position: absolute;
            right: -195px;
            background: rgba(0, 0, 0, 0.75);
            color: #fff;
            top: 1px;
            border-radius: 3px;
            text-align: center;
            font-size: 12px;
            border-top-left-radius: inherit;
            border-bottom-left-radius: inherit;
            padding: 3px;
        }
        .phone_c:before{
            content: '';
            width: 0;
            height: 0;
            right: 0;
            left: -12px;
            top: -1px;
            position: absolute;
            border-top: 12px solid transparent;
            border-right: 12px solid rgba(0, 0, 0, 0.75);
            border-bottom: 12px solid transparent;
        }
        .ti-close{
            color: #333333;
        }
        #mechbox .side_head .setwin{
            right: 14px;
            top: 9px;
        }
        .si-form-group .zui-select-inline{
            width: 100%;
        }
        .login-foot {
		    text-align: right;
		    margin-top: 10px;
		    color: #aaaaaa;
		}

		.login-foot span,
		.login-foot a{
		    margin-right: 10px;
		}
    </style>
</head>
<body class="skin-default">
    <div class="sign-container">
        <div class="sign-box">
<!--            <img src="/login/img/loginBK.jpg" />-->
            <img src="/newzui/css/images/supstar.png" />
        </div>
        <div class="sign-form Ajaxloading">
            <div class="sign-form-content lefts" id="lefts" v-cloak>
                <div class="sign-logo">
                    <em class="brand fa-hospital-o"></em>
                    <h1 class="brand_name" v-text="hospital" @click="popShow"></h1>
                    <a href="javascript:" @click="popShow"><i class="fa-refresh"  ></i> 切换机构</a>
                </div>
                <div class="si-form">
                    <div class="si-form-group zui-form zui_none">
                        <!--<i class="wb-chevron-down"></i>-->
                        <select-input @change-data="resultChange" :not_empty="false"
                                      :child="yqList" :index="'yqmc'" :index_val="'yqbm'" :val="json.yqbm"
                                      :name="'json.yqbm'" :search="true" :phd="'选择院区'">
                        </select-input>
                    </div>
                    <div class="si-form-group">
                        <i class="wb-user"></i>
                        <input autofocus type="text" @keydown.enter="nextFocus($event)"  v-model="json.user" class="si-input" name="userid" placeholder="用户名/手机号" />
                    </div>
                    <div class="si-form-group">
                        <i class="wb-lock"></i>
                        <input type="password" class="si-input" @keydown.enter="submit" type="password" v-model="json.password" name="password"  placeholder="密码" />
                    </div>
                    <div class="si-form-check">
                        <input type="checkbox" name="flag" id="checkboxSuccess" v-model="json.flag" class="zui-checkbox">
                        <label for="checkboxSuccess"> 记住密码</label>
                        <a @click="show_phone=true" class="forget" href="javascript:void(0)"> 忘记密码？</a>
                        <div v-show="show_phone" class="phone_c">
                            <div>请联系管理员电话:18200234469</div>
                        </div>
                    </div>
                    <div class="si-form-button">
                        <button :disabled="disable" class="zui-btn btn-primary" @click="submit" id="btn-login">登录</button>
                        <p><span>{{sqdq}}</span><span>{{idNum}}</span></p>
                    </div>
                </div>
            </div>
        </div>
	</div>
    <div class="login-foot">
	    <span>打印控件：</span>
	    <a target="_blank" download="CLodop_Setup_for_Win32NT.exe" href="/down/CLodop_Setup_for_Win32NT.exe">控件一</a>
	    <a target="_blank" download="install_lodop32.exe" href="/down/install_lodop32.exe">控件二</a>
	    <a target="_blank" download="install_lodop64.exe" href="/down/install_lodop64.exe">控件三</a>
	</div>
    <!--侧边窗口-->
    <div class="side-form  pop" id="mechbox" :class="{'ng-hide':num==1}" v-cloak >
        <div class="side_head">
            <div class="setwin">
                <a href="javascript:" class="ti-close" @click="close()"></a>
            </div>
            <div class="form-search">
                <input type="text" class="zui-input" name="input1" v-model="param.parm" @input="getHospital()" placeholder="请输入医疗机构名称" />
                <a href="javascript:void(0)" class="fa-search"></a>
            </div>
        </div>
        <div class="side_main">
            <div class="mechlist">
                <ul>
                    <li  v-for="(item, $index) in jsonList"  ><h1 v-text="item.sort"></h1>
                        <a @click="selectHosp(index,$index)" v-for="(list,index) in item.yljgModels" v-text="list.jgmc + '('+ list.pydm +')'" href="javascript:void(0)"></a>
                    </li>
                </ul>
            </div>

        </div>
    </div>

    <script src="/newzui/login.js" type="application/javascript"></script>
</body>
</html>
