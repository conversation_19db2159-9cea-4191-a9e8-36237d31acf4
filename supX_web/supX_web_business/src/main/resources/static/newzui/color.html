<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>配色 - 超星院级信息管理平台</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="/newzui/newcss/fonts/font-awesome.css" rel="stylesheet"/>
    <link href="pub/new.css" rel="stylesheet">
    <link rel="shortcut icon" href="favicon.ico">
    <link href="css/zui.min.css" rel="stylesheet"/>
    <link href="css/iframe.css" rel="stylesheet"/>
    <link href="css/common.css" rel="stylesheet"/>
    <link href="css/main.css" rel="stylesheet"/>
    <link href="/newzui/js/zui/zui.min.css" rel="stylesheet" type="text/css"/>
    <link href="pub/css/animation.css" rel="stylesheet">
    <!--<script type="application/javascript" src="/newzui/pub/top.js"></script>-->
    <script src="js/jquery.min.js"></script>
    <script src="js/iframe.min.js"></script>
    <script src="js/content.js"></script>
    <script src="js/contabs.min.js"></script>
    <!--<script src="js/jQuery.speech.js"></script>-->
    <script src="/lib/vue/vue.js"></script>
    <script src="/lib/vue/vue-resource.js"></script>
    <script async src="/lib/marquee.js"></script>
    <script async src="js/zui/jquery.zui.js"></script>
    <!--<script src="js/layer/layer.js"></script>-->
    <script src="/newzui/js/layer/laydate/laydate.js"></script>
    <script src="/newzui/pub/js/common.js"></script>
    <script src="/newzui/pub/js/components.js"></script>
    <script src="/newzui/pub/js/dictionaries.js"></script>
</head>
<style>
    .ypStyle{
        height: 126px;
    }
</style>
<body>
    <div class="wrapper" id="wrapper">
        <div draggable="true" class="flex-container flex-align-c padd-b-10 padd-t-10">
            <span class="whiteSpace ft-14">当前药品：</span>
            <span class="whiteSpace ft-14">注射用青梅素   160万U*1支*0.96g</span>
        </div>
        <div class="flex-container padd-b-10 padd-t-10">
            <span class="whiteSpace ft-14">当前药品：</span>
            <textarea class="ypStyle zui-input">注射用青梅素   160万U*1支*0.96g</textarea>
        </div>
        <div class="flex-container flex-align-c padd-b-10 padd-t-10">
            <span class="whiteSpace ft-14">使用目的：</span>
           <div class="flex-container flex-align-c">
               <select-input @change-data="resultChange"
                             :child="symh_tran" :index="popContent.symd" :val="popContent.symd"
                             :name="'popContent.symd'">
               </select-input>
               <span class="whiteSpace ft-14 padd-l-10">手术名称</span>
               <input data-notEmpty="true" class="zui-input" id="zsmc" type="text" :value="popContent['zsmc']"
                      @keydown="changeDown($event,'searchCon','zsmc','zsbm')" @input="change1(false,$event.target.value,'zsmc')"
                      placeholder="手术名称">
               <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them"
                             :them_tran="them_tran"
                             @click-one="checkedOneOut" @click-two="selectOne1">
               </search-table>
           </div>
        </div>
        <div class="flex-container flex-align-c padd-b-10 padd-t-10">
            <span class="whiteSpace ft-14">使用情况：</span>
            <div class="flex-container flex-align-c">
                <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'联合用药'" :val="'popContent.lhyyCk'"  :new-value="popContent.lhyyCk"></vue-checkbox>
                <span class="whiteSpace ft-14 padd-r-5">联合用药联数</span>
                <select-input @change-data="resultChange"
                              :child="lhyy_tran" :index="popContent.lhyy" :val="popContent.lhyy"
                              :name="'popContent.lhyy'">
                </select-input>
            </div>
        </div>
        <div class="flex-container padd-b-10 padd-t-10">
            <span class="whiteSpace ft-14">使用原因：</span>
            <textarea class="ypStyle zui-input">注射用青梅素   160万U*1支*0.96g</textarea>
        </div>
    </div>
<script type="text/javascript">
    var  wrapper= new Vue({
        el: '#wrapper',
        mixins: [baseFunc,tableBase],
        components: {
            'search-table': searchTable,
        },
        data: {
            Content:{},
           popContent:{},
            changeVal:false,
           selectContent:{},
            searchCon: [],
            them_tran: {'jb': dic_transform.data.ssjb_tran},
            them: {'手术编码': 'ssbm', '手术名称': 'ssmc', '拼音代码': 'pydm', '手术级别': 'jb'},
            selSearch:-1,
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            symh_tran:{
               '0':'非手术预防使用',
               '1':'手术预防使用',
               '2':'治疗使用',
            },
            lhyy_tran:{
               '0':'无',
               '1':'I联',
               '2':'II联',
               '3':'III联',
               '4':'>IV联',
               '5':'>IV联',
            },
        },
        mounted:function(){

        },
        created: function () {
        },

        methods: {
            reCheckOne: function (val) {
                this.popContent.lhyyCk=val[1];
                this.$forceUpdate()
            },
            //当输入值后才触发
            change1: function (add, val,mc) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.page.parm = val;
                var str_param = {parm: this.page.parm, page: this.page.page, rows: 30};
                //手术编码
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ssbm' + '&json=' + JSON.stringify(str_param), function (data) {
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            wrapper.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        wrapper.searchCon = data.d.list;
                    }
                    wrapper.changeVal = true;
                    wrapper.page.total = data.d.total;
                    wrapper.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
            },
            //检索
            changeDown: function (event, searchCon,mc,bm,index) {
                this.inputUpDown(event, 'searchCon', 'selSearch');
                this.Content = this[searchCon][this.selSearch]
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (this.changeVal) {
                        Vue.set(this.popContent, bm, this.Content['ssbm']);
                        Vue.set(this.popContent, mc, this.Content['ssmc']);
                        this.nextFocus(event);
                        $(".selectGroup").hide();
                        this.searchCon=[];
                        this.selSearch = -1;
                        this.$forceUpdate()
                    } else {
                        this.nextFocus(event);
                    }
                }
            },
            selectOne: function (item) {
                if (item == null) {
                    this.page.page++;
                    this.change1(true, this.popContent['ssmc']);
                } else {
                    Vue.set(this.popContent, 'ssbm', item['ssbm']);
                    Vue.set(this.popContent, 'ssmc', item['ssmc']);
                    this.$forceUpdate()
                    this.searchCon=[];
                    this.selSearch = -1
                    $(".selectGroup").hide();
                }
            },
        },
    });
</script>
</body>
</html>
