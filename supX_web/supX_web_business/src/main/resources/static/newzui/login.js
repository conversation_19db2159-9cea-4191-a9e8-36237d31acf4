//前面增加的取本客户端信息
var login = new Vue({
    el: '#lefts',
    mixins: [baseFunc],
    data: {
        hospital: '医疗机构',
        hospitals: [],
        yqList: [],
        idNum: '',
        sqdq: '',
        show_phone: false,
        disable: false,
        isShow: false,
        json: {
            'yljgbm': '',
            'yqbm': '',
            flag: false,
            'user': '',
            'password': null
        },
         parm : {
            'ComputerName': '',
            'ComputerIp': '',
            'ComputerMac': '',
            'code': 'success',
            'mess': ''
        },
    },
    methods: {
        popShow: function () {
            pop.num = 0
        },
        submit: function (event) {
            if (this.json.yljgbm == '') {
                malert('请先选择医疗机构', 'top', 'defeadted');
                return false;
            } else if (this.json.yqbm == '') {
                malert('请先选择医院分院', 'top', 'defeadted');
                return false;
            } else if (this.json.user == '') {
                malert('请填写用户名', 'top', 'defeadted');
                return false;
            }
            this.login(JSON.stringify(this.parm));
        },
        login: function (parm) {
            this.disable=true
            login.delAllCookie()
            common.openloading('.Ajaxloading','登录中')
            this.$http.post('/actionDispatcher.do?reqUrl=UserInfoAction&czybm=' + this.json.user + '&password=' + this.json.password + '&yljgbm=' + this.json.yljgbm + '&yqbm=' + this.json.yqbm, parm).then(function (json) {
            // this.$http.post('http://localhost:8083/login?czybm=' + this.json.user + '&password=' + this.json.password + '&yljgbm=' + this.json.yljgbm + '&yqbm=' + this.json.yqbm, parm).then(function (json) {
                if (json.body.a == 1) {
                    common.closeLoading()
                    login.disable=false
                    malert(json.body.c, 'top', 'defeadted');
                    return false;
                }else{
                    sessionStorage.clear()
                    common.closeLoading()
                    login.disable=false
                    // 存当前医院和用户信息
                    sessionStorage.setItem("yljgOrUser"+json.body.d.czybm, JSON.stringify(json.body.d));
                    sessionStorage.setItem("yljgOrUser", JSON.stringify(json.body.d));
                    login.setCookie('user_info'+login.json.user,JSON.stringify(login.json))
                    login.setCookie('user_info',JSON.stringify(login.json))
                    if (json.body.d.ver == 1) {
                        location.href = '/newzui/index.html?czybm=' + json.body.d.czybm + '&yqbm=' + json.body.d.yqbm + '&yljgbm=' + json.body.d.yljgbm+'&ksbm='+json.body.d.ksbm;
                    } else {
                        location.href = '/index.html?czybm=' + json.body.d.czybm + '&yqbm=' + json.body.d.yqbm + '&yljgbm=' + json.body.d.yljgbm+'&ksbm='+json.body.d.ksbm;
                    }
                }
                // 存cookie
                // login.json.password = null;

            }, function (error) {
                common.closeLoading();
                login.disable=false;
                malert(error.body.c, 'top', 'defeadted');
            });
        },
        getYq: function () {
            // 院区
            console.log(this.json);
            var param = {};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryYq&types=query&yljgbm=" + this.json.yljgbm + "&json="
                + JSON.stringify(param), function (json) {
                    login.yqList = json.d.list;
                    if (cookieJson != null && cookieJson != '') {
                        for (var i = 0; i < login.yqList.length; i++) {
                            console.log(cookieJson);
                            console.log(login.yqList);
                            if (login.yqList[i]['yqbm'] == cookieJson['yqbm']) {
                                login.json.yqbm = login.yqList[i]['yqbm'];
                                login.json.user = cookieJson['user'];
                                // login.json.password = cookieJson['password'];
                                login.json.flag = cookieJson['flag'];
                                return true;
                            }
                        }
                    }
                    // 如果只有一个院区，默认选中
                    if (login.yqList.length == 1) {
                        login.json.yqbm = login.yqList[0]['yqbm'];
                    }
                });
        },
        showHosp: function () {
            pop.isShow = true;
            $(".popup-right").animate({ "width": "+40%" }, 200, 'swing');
        },
        getCookie: function (name) {
            var cookieList = document.cookie.split(';');
            for (var i = 0; i < cookieList.length; i++) {
                if (cookieList[i].split('=')[0] == name) {
                    return cookieList[i].split('=')[1]
                }
            }
        }
    }
});

var pop = new Vue({
    el: '.pop',
    data: {
        param: {
            sort: 'jgbm',
            parm: ''
        },
        num: 1,
        isShow: false,
        jsonList: []
    },
    created: function () {
        this.getHospital();
    },
    methods: {
        close: function () {
            this.num = 1
        },
        selectHosp: function (index, i) {
            login.hospital = this.jsonList[i]['yljgModels'][index]['jgmc'];
            login.json.yljgbm = this.jsonList[i]['yljgModels'][index]['jgbm'];
            login.getYq();
            this.num = 1
        },
        getHospital: function () {
            // 医疗机构
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryYljg&types=queryLog&dg=" + JSON.stringify(this.param), function (json) {
                if (json.a == 0) {
                    if (json.e === "0500010608") {
                        login.sqdq = json.c;
                        login.idNum = json.d
                        malert(json.c, 'top', 'defeadted')
                    } else {
                        pop.jsonList = json.d.list;
                        if (cookieJson != null && cookieJson != '') {
                            for (var i = 0; i < pop.jsonList.length; i++) {
                                if (pop.jsonList[i]['jgbm'] == cookieJson['yljgbm']) {
                                    login.hospital = pop.jsonList[i]['jgmc'];
                                    login.json.yljgbm = pop.jsonList[i]['jgbm'];
                                    login.getYq();
                                }
                            }
                        }
                        // 如果只有一个机构，就默认选中
                        if (pop.jsonList.length == 1) {
                            login.hospital = pop.jsonList[0]['yljgModels'][0]['jgmc'];
                            login.json.yljgbm = pop.jsonList[0]['yljgModels'][0]['jgbm'];
                            login.getYq();
                        }
                    }
                }

            });
        },
    }
});

if (login.getCookie('user_info')) {
    var cookieJson = JSON.parse(login.getCookie('user_info'));
    if (cookieJson) {
        pop.getHospital();
    }
}

$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".pop");
    if (!bol) {
        if (pop.isShow) pop.close();
    }
});
setTimeout(function () {
    login.show_phone = false
}, 10000);
