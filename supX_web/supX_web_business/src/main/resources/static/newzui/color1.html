<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>配色 - 超星院级信息管理平台</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="/newzui/newcss/fonts/font-awesome.css" rel="stylesheet"/>
    <link href="pub/new.css" rel="stylesheet">
    <link rel="shortcut icon" href="favicon.ico">
    <link href="css/zui.min.css" rel="stylesheet"/>
    <link href="css/iframe.css" rel="stylesheet"/>
    <link href="css/common.css" rel="stylesheet"/>
    <link href="css/main.css" rel="stylesheet"/>
    <link href="/newzui/js/zui/zui.min.css" rel="stylesheet" type="text/css"/>
    <link href="pub/css/animation.css" rel="stylesheet">
    <!--<script type="application/javascript" src="/newzui/pub/top.js"></script>-->
    <script src="js/jquery.min.js"></script>
    <script src="js/iframe.min.js"></script>
    <script src="js/content.js"></script>
    <script src="js/contabs.min.js"></script>
    <!--<script src="js/jQuery.speech.js"></script>-->
    <script src="/lib/vue/vue.js"></script>
    <script src="/lib/vue/vue-resource.js"></script>
    <script async src="/lib/marquee.js"></script>
    <script async src="js/zui/jquery.zui.js"></script>
    <!--<script src="js/layer/layer.js"></script>-->
    <script src="/newzui/js/layer/laydate/laydate.js"></script>
    <script src="/newzui/pub/js/common.js"></script>
    <script src="/newzui/pub/js/components.js"></script>
    <script src="/newzui/pub/js/dictionaries.js"></script>
</head>
<style>
    .ypStyle{
        height: 126px;
    }
    .color-wtg{
        color: rgb(217, 0, 27);
    }
    .padd-l-70{
        padding-left: 70px;
    }
</style>
<body>
    <div class="wrapper" id="wrapper">
        <div class="flex-container flex-align-c padd-b-10 padd-t-10">
            <span class="whiteSpace ft-14">当前药品：</span>
            <span class="whiteSpace ft-14">注射用青梅素   160万U*1支*0.96g</span>
        </div>
        <p class="font-18 font-weight color-wtg padd-l-70">您无权限使用该抗生素!</p>
        <p class="font-18 font-weight color-wtg padd-l-70 padd-b-20">您可以向医务科申请提升权限,或者临时向上级医生申请使用该药品</p>
       <div class="flex-container flex-align-c padd-l-70">
           <label for="four2" class="flex-container cursor flex-align-c">
               <div class="position padd-r-5">
                   <input type="radio" id="four2" name="two" v-model="popContent.sfsy" value="0" class="zui-radio" >
                   <label for="four2" class="padd-r-5"></label>
               </div>
               <span class="padd-r-5">放弃使用</span>
           </label>
           <label for="four3" class="flex-container cursor flex-align-c">
               <div class="position padd-r-5">
                   <input type="radio" id="four3" name="two" v-model="popContent.sfsy" value="1" class="zui-radio" >
                   <label for="four3" class="padd-r-5"></label>
               </div>
               <span class="padd-r-5">申请使用该药品</span>
           </label>
       </div>
        <div v-if="popContent.sfsy==1" class="flex-container flex-wrap-w">
            <div class="flex-container flex-align-c padd-b-10 padd-t-10 padd-r-10">
                <span class="whiteSpace ft-14">申请药品：</span>
                <input data-notEmpty="true" class="zui-input wh180" id="zsmc" type="text" :value="popContent['zsmc']"
                       @keydown="changeDown($event,'searchCon','zsmc','zsbm')" @input="change1(false,$event.target.value,'zsmc')"
                       placeholder="手术名称">
                <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them"
                              :them_tran="them_tran"
                              @click-one="checkedOneOut" @click-two="selectOne1">
                </search-table>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 padd-t-10">
                <span class="whiteSpace ft-14">药品规格：</span>
                <input class="zui-input wh120" />
            </div>
            <div class="flex-container flex-align-c  padd-b-10 padd-r-10 padd-t-10">
                <span class="whiteSpace ft-14">申请用量：</span>
                <div class="relative">
                    <input class="zui-input wh120" />
                    <span class="cm">支</span>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 padd-t-10">
                <span class="whiteSpace ft-14">使用日期：</span>
                <input class="zui-input wh120" id="time" v-model="popContent.strq"/>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 padd-t-10">
                <span class="whiteSpace ft-14">上级医师：</span>
                <select-input class="wh120" :data-notEmpty="true" @change-data="resultChange" :not_empty="true" :child="ysData"
                              :index="'ryxm'" :index_val="'rybm'" :val="popContent.shys" :name="'popContent.shys'"
                              :search="true" :phd="''">
                </select-input>
            </div>
            <div class="flex-container padd-b-10 padd-t-10 wh100MAx">
                <span class="whiteSpace ft-14">申请原因：</span>
                <textarea class="ypStyle zui-input"></textarea>
            </div>
        </div>
    </div>
<script type="text/javascript">
    var  wrapper= new Vue({
        el: '#wrapper',
        mixins: [baseFunc,tableBase],
        components: {
            'search-table': searchTable,
        },
        data: {
            ysData:[],
            Content:{},
           popContent:{
               sfsy:1,
           },
            changeVal:false,
           selectContent:{},
            searchCon: [],
            them_tran: {'jb': dic_transform.data.ssjb_tran},
            them: {'手术编码': 'ssbm', '手术名称': 'ssmc', '拼音代码': 'pydm', '手术级别': 'jb'},
            selSearch:-1,
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            json: {
                ysbz: '1',
                tybz: '0',
            },
            dg: {
                page: '1',
                rows: '',
                parm: '',
            },
        },
        mounted:function(){
            this.getYs();
            laydate.render({
                elem: '#time',
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    wrapper.popContent.strq = value;
                }
            });
        },
        created: function () {
        },

        methods: {
            getYs: function () {
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: 'rybm',
                        parm: JSON.stringify(this.json),
                        dg: JSON.stringify(this.dg),
                    }
                }).then(function (json) {
                    if (json.body.a == 0 && json.body.d && json.body.d.list.length != 0) {
                        wrapper.ysData = json.body.d.list;
                    } else {
                        malert(json.body.c, 'top', 'defeadted');
                    }
                })
            },
            reCheckOne: function (val) {
                this.popContent.lhyyCk=val[1];
                this.$forceUpdate()
            },
            //当输入值后才触发
            change1: function (add, val,mc) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.page.parm = val;
                var str_param = {parm: this.page.parm, page: this.page.page, rows: 30};
                //手术编码
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ssbm' + '&json=' + JSON.stringify(str_param), function (data) {
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            wrapper.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        wrapper.searchCon = data.d.list;
                    }
                    wrapper.changeVal = true;
                    wrapper.page.total = data.d.total;
                    wrapper.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
            },
            //检索
            changeDown: function (event, searchCon,mc,bm,index) {
                this.inputUpDown(event, 'searchCon', 'selSearch');
                this.Content = this[searchCon][this.selSearch]
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (this.changeVal) {
                        Vue.set(this.popContent, bm, this.Content['ssbm']);
                        Vue.set(this.popContent, mc, this.Content['ssmc']);
                        this.nextFocus(event);
                        $(".selectGroup").hide();
                        this.searchCon=[];
                        this.selSearch = -1;
                        this.$forceUpdate()
                    } else {
                        this.nextFocus(event);
                    }
                }
            },
            selectOne: function (item) {
                if (item == null) {
                    this.page.page++;
                    this.change1(true, this.popContent['ssmc']);
                } else {
                    Vue.set(this.popContent, 'ssbm', item['ssbm']);
                    Vue.set(this.popContent, 'ssmc', item['ssmc']);
                    this.$forceUpdate()
                    this.searchCon=[];
                    this.selSearch = -1
                    $(".selectGroup").hide();
                }
            },
        },
    });
</script>
</body>
</html>
