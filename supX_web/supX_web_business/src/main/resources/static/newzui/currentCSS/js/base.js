    var tab=new Vue({
        el: '.tabBar',
        data:{
            num:0,
        },
        methods:{
            tabBg: function (index) {
                this.num = index;
            },
        }
    });

    var selectInput = new Vue({
        el: '#selectInput',
        mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
        data:{
            ypContent:{}
            },
         methods:{

         }
    })
    var starOnImg="images/<EMAIL>";
    var starOffImg="images/<EMAIL>";
    var star=new Vue({
        el:'#star',
        data:{
            stars:[
                {
                    src:starOffImg,
                    active:false,
                    starName:'差'
                },
                {
                    src:starOffImg,
                    active:false,
                    starName:'一般'
                },
                {
                    src:starOffImg,
                    active:false,
                    starName:'专业'
                },
                {
                    src:starOffImg,
                    active:false,
                    starName:'很专业'
                },
                {
                    src:starOffImg,
                    active:false,
                    starName:'非常专业'
                },
            ],
            starNum:0,
        },
      methods:{
          //星星评分
          rating:function (index) {
              var total=this.stars.length;//星星总数
              var idx=index+1;//应该显示的星星数量
              if(this.starNum == 0){
                  this.starNum=idx;
                  for(var i = 0 ; i< idx ;i++){
                      this.stars[i].src=starOnImg;
                      this.stars[i].active=true;
                  }
              }else{
                  //如果再次点击当前选中的星星-仅取消掉当前星级，保留之前的
                  if(idx <= this.starNum){
                      for(var i = index+1; i<total;i++){
                          this.stars[i].src=starOffImg;
                          this.stars[i].active=false;

                      }
                  }
                  //如果答应当前星级
                  if(idx > this.starNum){
                      for(var i =0; i<idx;i++){
                          this.stars[i].src=starOnImg;
                          this.stars[i].active=true;
                          if( this.stars[i].active==true){
                          }
                      }

                  }
                  var count =0;
                  for(var i=0;i<total;i++){
                      if(this.stars[i].active){
                          count++;
                      }
                  }

                  this.starNum = count;
              }
          },
      }
    })
