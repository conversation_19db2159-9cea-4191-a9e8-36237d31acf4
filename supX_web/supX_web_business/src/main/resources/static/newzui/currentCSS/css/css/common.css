@import url(complete.min.css);
.zui-select-group {
    display: none;
}

#jyxm_icon .switch {
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    margin: 0 22%;
}
#jyxm_icon .switch>input {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 45px;
    height: 25px;
    margin: 0;
    cursor: pointer;
    opacity: 0;
}
#jyxm_icon .switch>label {
    display: block;
    padding: 5px 0 5px 35px;
    margin: 0;
    font-weight: 400;
    line-height: 20px;
}
#jyxm_icon .switch>label:after {
    position: absolute;
    top: 5px;
    left: 0;
    display: block;
    width: 40px;
    margin-top: auto;
    z-index: auto;
    text-align: left;
    text-indent: 3px;
    background: #dfe1e5;
    height: 20px;
    pointer-events: none;
    content: ' ';
    border: 1px solid #ddd;
    border-radius: 10px;
    -webkit-transition: all .4s cubic-bezier(.175,.885,.32,1);
    -o-transition: all .4s cubic-bezier(.175,.885,.32,1);
    transition: all .4s cubic-bezier(.175,.885,.32,1);
}
#jyxm_icon .switch>label:before{
    position: absolute;
    top: 5px;
    left: 0;
    display: block;
    line-height: 22px;
    width: 40px;
    margin-top: auto;
    z-index: auto;
    background: #dfe1e5;
    height: 20px;
    text-align: left;
    text-indent: 23px;
    pointer-events: none;
    content: '关';
    color: #fff;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 10px;
    -webkit-transition: all .4s cubic-bezier(.175,.885,.32,1);
    -o-transition: all .4s cubic-bezier(.175,.885,.32,1);
    transition: all .4s cubic-bezier(.175,.885,.32,1);
}

#jyxm_icon .switch>label:after {
    top: 6px;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border-color: #ccc;
    border-radius: 9px;
    -webkit-box-shadow: rgba(0,0,0,.05) 0 1px 4px, rgba(0,0,0,.12) 0 1px 2px;
    box-shadow: rgba(0,0,0,.05) 0 1px 4px, rgba(0,0,0,.12) 0 1px 2px;
}
#jyxm_icon .switch>input:checked+label:after {
    left: 21px;
    border-color: #fff;
}
#jyxm_icon .switch>input:checked+label:before {
    background-color: #1abc9c;
    border-color: #1abc9c;
    color: #fff;
    content:'开';
    text-align: left;
    text-indent: 3px;
    line-height: 22px;
    font-size: 12px;
}

/*下拉菜单 .dropdown*/
/** 表单样式 from: **/
.zui-row {
    width: 100%;
}
.zui-row:after,
.zui-row:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}
.zui-inline,
.zui-input-inline,
.zui-select-inline {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    /*margin-right: 10px;*/
}
.zui-form-label {
    display: block;
    padding: 6px 10px 6px 0;
    line-height: 20px;
    width: 110px;
    font-weight: 400;
    text-align: right;
    color: #a7b1c2;
    font-size: 14px;
}
.zui-input,
.zui-textarea {
    display: block;
    width: 100%;
    /*padding: 0 10px;*/
    height: 36px;
    line-height: 36px;
    border: 1px solid #e6e6e6;
    background-color: #fff;
    color: #354052;
    font-size: 14px;
    border-radius: 4px;
    /*margin-left: 5px;*/
}
.zui-input.isError,
.zui-textarea.isError {
    background-color: #fbe2e2;
    border-color: #e84d3d;
}
.zui-input.border-none,
.zui-textarea.border-none {
    border-color: transparent;
}
.zui-input:focus,
.zui-textarea:focus {
    border-color: #1ABC9C;
    background-color: #fff;
}
.zui-input:focus.border-none,
.zui-textarea:focus.border-none {
    border-color: transparent;
}
.zui-input:disabled,
.zui-textarea:disabled {
    background-color: #f8f8f8;
    color:#757c83;
}
.zui-input::-webkit-input-placeholder,
.zui-textarea::-webkit-input-placeholder {
    color: #a7b1c2;
}
.zui-input:-moz-placeholder,
.zui-textarea:-moz-placeholder {
    color: #a7b1c2;
}
.zui-input::-moz-placeholder,
.zui-textarea::-moz-placeholder {
    color: #a7b1c2;
}
.zui-input:-ms-input-placeholder,
.zui-textarea:-ms-input-placeholder {
    color: #a7b1c2;
}
.zui-input[readonly],
.zui-textarea[readonly] {
    cursor: pointer;
}
.zui-select-inline {
    width: 100%;
    /*width: 120px;*/
}
.zui-select-inline .zui-input {
    /*padding-right: 20px;*/
    cursor: pointer;
}
.iconClass{
    position: absolute;
    z-index: 20;
    zoom: 1;
    top:0;
    right: 0;
    width: 20px;
    height: 100%;
    text-align: center;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
}
.iconClass:after {
    content: "\f0d7";
    font-family: 'FontAwesome';
    z-index: 20;
    zoom: 1;
    text-align: center;
    color: #a7b1c2;
}
.zui-date .datenox {
    display: inline-block;
    position: absolute;
    z-index: 20;
    zoom: 1;
    top: 50%;
    left: 6px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    text-align: center;
    color: #1ABC9C;
    cursor: pointer;
}
.zui-date .zui-input {
    padding-left: 24px;
}
.zui-form:after,
.zui-form:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}
.zui-form .zui-inline {
    float: left;
    margin-bottom: 8px;
    padding: 0 20px 0 110px;
}
.zui-form .zui-input-inline {
    width: 100%;
}
.zui-form .zui-form-label {
    position: absolute;
    z-index: 10;
    zoom: 1;
    left: 0;
    top: 0;
}
.zui-form .select-right .zui-input-inline {
    padding-right: 50px;
}
.zui-form .select-right .zui-select-inline {
    position: absolute;
    z-index: 100;
    zoom: 1;
    top: 0;
    right: 12px;
    max-width: 48px;
}
.zui-input-item .zui-select-inline {
    float: left;
    padding-left: 2px;
}
.zui-input-item .zui-select-inline:nth-child(1) {
    padding-left: 0;
}
.validate,
.tipError {
    position: absolute;
    z-index: 10;
    zoom: 1;
    display: inline-block;
    right: -20px;
    top: 50%;
    color: #f00;
    width: 20px;
    text-align: center;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    margin-top: -10px;
}
.tipError {
    position: absolute;
    z-index: 20;
    zoom: 1;
    right: -24px;
    font-size: 14px;
    color: #f96868;
    background-color: #fff;
    cursor: pointer;
}





input[type=checkbox].green + label {
    display: inline-block;
    padding-left: 18px;
    height: 18px;
    line-height: 18px;
    position: relative;
    cursor: pointer;
    color: #a7b1c2;
    vertical-align: middle;
}

input[type=checkbox].green + label:before {
    content: " ";
    width: 16px;
    height: 16px;
    line-height: 16px;
    position: absolute;
    z-index: 2;
    zoom: 1;
    top: 50%;
    left: 0;
    margin-top: -8px;
    border: 1px #d2d2d2 solid;
    background-color: #fff;
    font-size: 12px;
    border-radius: 2px;
    text-align: center;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

input[type=checkbox].green:checked + label:before {
    content: "\f192";
    background-color: #1ABC9C;
    border-color: #1ABC9C;
    color: #fff;
}

input[type=checkbox].green {
    display: none;
}

::placeholder {
    color: rgba(53, 64, 82, .3) !important;
}
.tree_text1 {
    display: inline-block;
    padding: 0 7px;
    cursor: pointer;
    border-radius: 4px;
}
/*.tree_text1:hover{*/
    /*color: #1abc9c;*/
/*}*/
.toggleIMg {
    width: 21px;
    height: 18px;
}

.tree_tem1 ul {
    padding-left: 22px;
}

.tree_tem1 label {
    float: left;
    margin: 7px 6px 0 0;
}

#xtmktree {
    width: 20%;
    padding-top: 13px;
    border: 1px solid #eee;
    margin: 0;
}

.bold {
    color: rgba(51,51,51,0.86);
}

.xtmktreediv {
    cursor: pointer;
    /*height: 31px;*/
    line-height: 31px;
}
body .selectGroup table tr:hover {
    background: #edfaf7 !important;
    background-color: #edfaf7;
}
.zui-select-group .fixed {
    background: none !important;
    overflow: auto;
}
.zui-select-group table tr:first-child {
    background: #edf2f1;
    color: #333333;
}
.zui-select-group .fixed table {
    background: none !important;
}

.zui-select-group table {
    overflow: auto;
}
.zui-select-group:hover ::-webkit-scrollbar {
    background: rgba(47, 64, 80, 0.46);
}
body .selectGroup table tr.tableTrSelect {
    /*background-color: #ffe48d !important;*/
    /*background-color: #1abc9c !important;*/
    color: #000000 !important;
}

.patientTable tr {
    width: auto;
}

.left-radio {
    margin-left: 24px;
}

.left-radio input {
    margin-right: 9px;
    vertical-align: unset
}

.left-radio p {
    font-size: 14px;
    color: #9fa9ba;
    margin-bottom: 3px;
}

.green-radius + label {
    background: #ffffff;
    border: 1px solid #1abc9c;
    height: 20px;
    width: 20px;
    line-height: 14px;
    display: inline-block;
    margin-right: 9px;
    border-radius: 100%;
    position: relative;
}

input[type=radio] + i {
    border-radius: 100%;
    margin-right: 9px;
}

label {
    font-size: 12px;
    cursor: pointer;
}

.green-radio i {
    font-size: 12px;
    font-style: normal;
    display: inline-block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    vertical-align: middle;
    margin: -2px 2px 1px 0;
    border: #1abc9c 1px solid;
}

.green-radio input[type=radio] {
    display: none;
}

.green-radio input[type=radio]:checked + i {
    background-color: #1abc9c;
}

.green-radio input[type=radio]:disabled + i {
    border-color: #ccc;
}

.green-radio input[type=radio]:checked:disabled + i {
    background-color: #ccc;
}

.printArea {
    display: none;
}

.trendDiv {
    position: relative;
    width: calc(100% - 2px);
    height: 30px;
}

/* flex布局方案begin */
.flex-container{
    display: -ms-flexbox;
    display: -ms-flex;
    display: -moz-box;
    display: -moz-flex;
    display: -o-flex;
    display: -webkit-flex;
    display: -webkit-box;
    display: flex;
}
.flex-dir-rr{ /*主轴为横轴 顺序与文档流相反*/
    -webkit-flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse;
}
.flex-dir-c{ /*主轴为纵轴 顺序与文档流相同*/
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -moz-box-orient: vertical;
    -webkit-box-orient: vertical;
}
.flex-dir-cr{ /*主轴为纵轴 顺序与文档流相反*/
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
}
.flex-wrap-w{ /*允许子项目折行显示 顺序与文档流相同*/
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}
.flex-wrap-wr{ /*允许子项目折行显示 顺序与文档流相反*/
    -ms-flex-wrap: wrap-reverse;
    -webkit-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse;
}
.flex-jus-e{ /*子项目在主轴方向上 尾部对齐*/
    -moz-box-pack: end;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
}
.flex-jus-c{ /*子项目在主轴方向上 居中对齐*/
    -moz-box-pack: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}
.flex-jus-sp{ /*子项目在主轴方向上 均匀分布 首尾子项到父容器的距离是子项间距的一半*/
    -moz-box-pack: space-around;
    -webkit-box-pack: space-around;
    -webkit-justify-content: space-around;
    justify-content: space-around;
}
.flex-jus-sb{ /*子项目在主轴方向上 均匀分布 首尾子项到父容器的距离为0*/
    -moz-box-pack: space-between;
    -webkit-box-pack: space-between;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}
.flex-align-s{ /*子项目在侧轴上 头部对齐（宽高不被拉伸）*/
    -moz-box-align: start;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start;
}
.flex-align-e{ /*子项目在侧轴上 尾部对齐（宽高不被拉伸）*/
    -moz-box-align: end;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    align-items: flex-end;
}
.flex-align-c{ /*子项目在侧轴上 居中对齐（宽高不被拉伸）*/
    -moz-box-align: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
}
.flex-align-b{ /*子项目在侧轴上 首行文字的基线对齐（宽高不被拉伸）*/
    -moz-box-align: baseline;
    -webkit-box-align: baseline;
    -webkit-align-items: baseline;
    align-items: baseline;
}
.flex-one{
    -webkit-flex: 1;
    -ms-flex: 1;
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    flex: 1;
}
.flex-tow{
    -webkit-flex: 2;
    -ms-flex: 2;
    -moz-box-flex: 2;
    -webkit-box-flex: 2;
    flex: 2;
}
.flex-three{
    -webkit-flex: 3;
    -ms-flex: 3;
    -moz-box-flex: 3;
    -webkit-box-flex: 3;
    flex: 3;
}
.flex-four{
    -webkit-flex: 4;
    -ms-flex: 4;
    -moz-box-flex: 4;
    -webkit-box-flex: 4;
    flex: 4;
}
.flex-five{
    -webkit-flex: 5;
    -ms-flex: 5;
    -moz-box-flex: 5;
    -webkit-box-flex: 5;
    flex: 5;
}
.flex-six{
    -webkit-flex: 6;
    -ms-flex: 6;
    -moz-box-flex:6;
    -webkit-box-flex: 6;
    flex: 6;
}
.flex-seven{
    -webkit-flex: 7;
    -ms-flex: 7;
    -moz-box-flex: 7;
    -webkit-box-flex: 7;
    flex: 7;
}
.flex-eight{
    -webkit-flex: 8;
    -ms-flex: 8;
    -moz-box-flex: 8;
    -webkit-box-flex: 8;
    flex: 8;
}
.flex-nine{
    -webkit-flex: 9;
    -ms-flex: 9;
    -moz-box-flex: 9;
    -webkit-box-flex: 9;
    flex: 9;
}
/* flex布局方案end */


/*栅格系统 begin*/
/* 示例代码 没有行的概念  当一行放不下的时候会自动换行排列
<div class="grid-box">
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
</div> 
*/
.grid-box:after,
.grid-box:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}

.grid-box [class*='col-'] {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    float: left;
}

.grid-box .col-m-1,
.grid-box .col-s-1,
.grid-box .col-l-1,
.grid-box .col-xl-1,
.grid-box .col-xxl-1 {
    width: 8.33333333%;
}

.grid-box .col-m-2,
.grid-box .col-s-2,
.grid-box .col-l-2,
.grid-box .col-xl-2,
.grid-box .col-xxl-2 {
    width: 16.6666667%;
}

.grid-box .col-m-3,
.grid-box .col-s-3,
.grid-box .col-l-3,
.grid-box .col-xl-3,
.grid-box .col-xxl-3 {
    width: 25%;
}
.grid-box .col-mx-5,
.grid-box .col-sx-5,
.grid-box .col-lx-5,
.grid-box .col-xlx-5,
.grid-box .col-xxlx-5 {
    width:20%;
}
.grid-box .col-m-4,
.grid-box .col-s-4,
.grid-box .col-l-4,
.grid-box .col-xl-4,
.grid-box .col-xxl-4 {
    width: 33.33333333%;
}

.grid-box .col-m-5,
.grid-box .col-s-5,
.grid-box .col-l-5,
.grid-box .col-xl-5,
.grid-box .col-xxl-5 {
    width: 41.66666666666667%;
}

.grid-box .col-m-6,
.grid-box .col-s-6,
.grid-box .col-l-6,
.grid-box .col-xl-6,
.grid-box .col-xxl-6 {
    width: 50%;
}

.grid-box .col-m-7,
.grid-box .col-s-7,
.grid-box .col-l-7,
.grid-box .col-xl-7,
.grid-box .col-xxl-7 {
    width: 58.33333333333333%;
}

.grid-box .col-m-8,
.grid-box .col-s-8,
.grid-box .col-l-8,
.grid-box .col-xl-8,
.grid-box .col-xxl-8 {
    width: 66.66666666666667%;
}

.grid-box .col-m-9,
.grid-box .col-s-9,
.grid-box .col-l-9,
.grid-box .col-xl-9,
.grid-box .col-xxl-9 {
    width: 75%;
}

.grid-box .col-m-10,
.grid-box .col-s-10,
.grid-box .col-l-10,
.grid-box .col-xl-10,
.grid-box .col-xxl-10 {
    width: 83.33333333333333%;
}

.grid-box .col-m-11,
.grid-box .col-s-11,
.grid-box .col-l-11,
.grid-box .col-xl-11,
.grid-box .col-xxl-11 {
    width: 91.66666666666667%;
}

.grid-box .col-m-12,
.grid-box .col-s-12,
.grid-box .col-l-12,
.grid-box .col-xl-12,
.grid-box .col-xxl-12 {
    width: 100%;
}

@media only screen and (min-width: 1600px) {
    .grid-box .col-xxl-1 {
        width: 8.33333333%;
    }

    .grid-box .col-xxl-2 {
        width: 16.6666667%;
    }

    .grid-box .col-xxl-3 {
        width: 25%;
    }

    .grid-box .col-xxl-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xxlx-5 {
        width: 20%;
    }

    .grid-box .col-xxl-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-xxl-6 {
        width: 50%;
    }

    .grid-box .col-xxl-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-xxl-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-xxl-9 {
        width: 75%;
    }

    .grid-box .col-xxl-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-xxl-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-xxl-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1600px) {
    .grid-box .col-xl-1 {
        width: 8.33333333%;
    }

    .grid-box .col-xl-2 {
        width: 16.6666667%;
    }

    .grid-box .col-xl-3 {
        width: 25%;
    }

    .grid-box .col-xl-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xlx-5 {
        width: 20%;
    }
    .grid-box .col-xl-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-xl-6 {
        width: 50%;
    }

    .grid-box .col-xl-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-xl-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-xl-9 {
        width: 75%;
    }

    .grid-box .col-xl-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-xl-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-xl-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1366px) {
    .grid-box .col-x-1 {
        width: 8.33333333%;
    }

    .grid-box .col-x-2 {
        width: 16.6666667%;
    }

    .grid-box .col-x-3 {
        width: 25%;
    }

    .grid-box .col-x-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xx-5 {
        width: 20%;
    }
    .grid-box .col-x-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-x-6 {
        width: 50%;
    }

    .grid-box .col-x-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-x-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-x-9 {
        width: 75%;
    }

    .grid-box .col-x-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-x-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-x-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1024px) {
    .grid-box .col-s-1 {
        width: 8.33333333%;
    }

    .grid-box .col-s-2 {
        width: 16.6666667%;
    }

    .grid-box .col-s-3 {
        width: 25%;
    }

    .grid-box .col-s-4 {
        width: 33.33333333%;
    }
    .grid-box .col-sx-5 {
        width: 20%;
    }
    .grid-box .col-s-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-s-6 {
        width: 50%;
    }

    .grid-box .col-s-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-s-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-s-9 {
        width: 75%;
    }

    .grid-box .col-s-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-s-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-s-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 768px) {
    .grid-box .col-m-1 {
        width: 8.33333333%;
    }

    .grid-box .col-m-2 {
        width: 16.6666667%;
    }

    .grid-box .col-m-3 {
        width: 25%;
    }

    .grid-box .col-m-4 {
        width: 33.33333333%;
    }
    .grid-box .col-mx-5 {
        width: 20%;
    }
    .grid-box .col-m-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-m-6 {
        width: 50%;
    }

    .grid-box .col-m-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-m-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-m-9 {
        width: 75%;
    }

    .grid-box .col-m-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-m-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-m-12 {
        width: 100%;
    }
}

/*栅格系统end*/

/*定义绿色虚线边框的卡片式ui结构*/
/* 示例代码 
<div class="tab-card">
	<div class="tab-card-header">
		<div class="tab-card-header-title">卡片信息</div>
	</div>
	<div class="tab-card-body">
		这里放内容即可
	</div>
</div>
*/
.tab-card {
    padding: 10px;
    position: relative;
    background-color: #fff;
    margin-bottom: 2px;
}

.tab-card .tab-card-header {
    position: absolute;
    top: 10px;
    left: 20px;
    border-top: 1px solid #fff;
}

.tab-card .tab-card-header .tab-card-header-title {
    padding: 0 15px;
    font-size: 16px;
    margin-top: -11px;
    color: #1abc9c;
}

.tab-card .tab-card-body {
    border: 1px dashed rgba(26, 188, 156, 0.3) !important;
    background: rgba(26, 188, 156, 0.018) !important;
    padding: 20px 10px 10px;
}

/*定义绿色虚线边框的卡片式ui结构end*/

.left-radio {
    margin-left: 24px;
}

.left-radio input {
    margin-right: 9px;
    vertical-align: unset
}

.left-radio p {
    font-size: 14px;
    color: #9fa9ba;
    margin-bottom: 3px;
}

.green-radius + label {
    background: #ffffff;
    border: 1px solid #1abc9c;
    height: 20px;
    width: 20px;
    line-height: 14px;
    display: inline-block;
    margin-right: 9px;
    border-radius: 100%;
    position: relative;
}

input[type=radio] + i {
    border-radius: 100%;
    margin-right: 9px;
}

label {
    font-size: 12px;
    cursor: pointer;
}

.green-radio i {
    font-size: 12px;
    font-style: normal;
    display: inline-block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    vertical-align: middle;
    margin: -2px 2px 1px 0;
    border: #1abc9c 1px solid;
}

.green-radio input[type=radio] {
    display: none;
}

.green-radio input[type=radio]:checked + i {
    background-color: #1abc9c;
}

.green-radio input[type=radio]:disabled + i {
    border-color: #ccc;
}

.green-radio input[type=radio]:checked:disabled + i {
    background-color: #ccc;
}

.printArea {
    display: none;
}

.trendDiv {
    position: relative;
    width: calc(100% - 2px);
    height: 22px;
}

.iocn-top:after {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    right: 1px;
    top: 1px;
    border-radius: 2px;
}

.iocn-top:before {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    left: 1px;
    top: 1px;
    border-radius: 2px;
}

.iocn-bottom:after {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    background: #1abc9c;
    right: 1px;
    bottom: 1px;
    border-radius: 2px;
}

.iocn-bottom:before {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;

    left: 1px;
    bottom: 1px;
    border-radius: 2px;
}

.icon-table-top {
    width: 16px;
    height: 2px;
    margin-top: 1px;
    margin-bottom: 1px;
}

.icon-table-center {
    width: 16px;
    height: 2px;
    margin-bottom: 1px;
}

.icon-table-bottom {
    width: 16px;
    height: 2px;

}
.user-footer-img:hover{
    opacity: .4;
}
.margin-r-35 {
    margin-right: 35px;
}

.icon-bg-hs:after {
    background: #dfe3e9;
}

.icon-bg-hs:before {
    background: #dfe3e9;
}

.icon-bg-le:before {
    background: #1abc9c;
}

.icon-bg-le:after {
    background: #1abc9c;
}

.icon-bg-hs1 {
    background: #dfe3e9;
}

.icon-bg-le1 {
    background: #1abc9c;
}

.pop-page-content {
    width: 391px;
    height: 279px;
    background-color: #ffffff;
    position: absolute;
    right: 110px;
    z-index: 10;
}

.pop-page-content .header-page {
    background-color: #FAFAFA;
    height: 42px;
    position: relative;
    padding-left: 12px;
    font-size: 14px;
    line-height: 42px;
    text-align: left;
}

.pop-page-content .header-page:after {
    position: absolute;
    right: 50px;
    top: -10px;
    content: '';
    font-size: 0;
    line-height: 0;
    border-width: 10px;
    border-color: #ffffff;
    border-top-width: 0;
    border-style: dashed;
    border-bottom-style: solid;
    border-left-color: transparent;
    border-right-color: transparent;
}

.pop-page-content .pop-item {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
    width: 391px;
    height: 234px;
    overflow: auto;
}

.pop-page-content .pop-item .pop-list {
    color: #1abc9c;
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-se {
    background-color: #1abc9c;
    color: #ffffff;
    width: 54px;
    font-size: 12px;
    height: 23px;
    margin-left: 10px;
    cursor: pointer;
    border-radius: 4px;
    display: inline-block;
    line-height: 23px;
    text-align: center;
}

.pop-page-content .pop-item .pop-list .pop-icon {
    background-image: linear-gradient(-1deg, #1ebe9e 2%, #30edc7 98%);
    width: 16px;
    height: 16px;
    margin-right: 9px;
    display: inline-block;
    border-radius: 100%;
    margin-left: 12px;
    vertical-align: text-top;
    margin-top: 1px;
}

.pop-page-content .pop-item .pop-list {
    display: flex;
    align-items: center;
    width: 100%;
    padding-top: 15px;
    position: relative;

}

.bggGradientm {
    background: linear-gradient(to bottom, #F2F3F5 0%, #F2F3F5 100%) 20px 0px/ 1px calc((100% + 15px)) no-repeat;
}

.bgGradientGreen {
    background: linear-gradient(to bottom, #1abc9c70 0%, #F2F3F5 100%) 20px 0px/ 1px calc((100% + 15px)) no-repeat;
}

/*.pop-page-content .pop-item .pop-list:after{*/
/*position: absolute;*/
/*left: 19px;*/
/*bottom: -50%;*/
/*height: 74%;*/
/*width: 2px;*/
/*content: '';*/
/*background-color: #F2F3F5;*/
/*}*/
.pop-page-content .pop-item .pop-list .icon-not-active {
    background: #c2cad4;
    width: 16px;
    display: inline-block;
    font-size: 10px;
    text-align: center;
    margin-left: 12px;
    margin-right: 9px;
    color: #E7E9ED;
    height: 16px;
    border-radius: 100%;
}

.pop-page-content .pop-item .pop-list .pop-date {
    margin-right: 25px;
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-se, .pop-page-content .pop-item .pop-list .pop-text {
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-text {
    width: 126px;
    display: inline-block;
}

.font-14 {
    font-size: 14px;
}

/* 单独定义table列表组件单元格宽度 begin
 * why？
 * 因为table列表组件每次更新数据需要重新跑js计算单元格宽度效率低，容易造成死循环！所以放弃组件自己计算而改为手动预设！
 */
.zui-table-view .cell-m {
    width: 50px!important;
}

.zui-table-view .cell-s {
    width: 100px!important;
}

.zui-table-view .cell-l {
    width: 150px!important;
}

.zui-table-view .cell-xl {
    width: 200px!important;
}

.zui-table-view .cell-xxl {
    width: 300px!important;
}


/*单独定义table列表组件单元格宽度 end*/
.vertical-sub {
    vertical-align: sub;
}

.confirm-title {
    background-color: #1abc9c;
    font-size: 16px;
    color: #fff;
    height: 42px;
    padding-left: 20px;
    line-height: 42px;
    border-bottom: 1px solid #eee;
    overflow: hidden;
    border-radius: 2px 2px 0 0;
}

.confirm-content {
    position: relative;
}

.confirm-mad {
    padding: 15px 10px 10px;
    text-align: center;
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.confirm-row {
    width: 100%;
    text-align: center;
    display: flex;
    margin-bottom: 15px;
    justify-content: flex-end;
}

.confirm-btn {
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid transparent;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    white-space: nowrap;
    vertical-align: middle;
    text-align: center;
    font-weight: 400;
    touch-action: manipulation;
    margin: 0 10px;
    cursor: pointer;
    user-select: none;
    background-color: #fff;
    color: #767d85;
    position: relative;
    width:88px;
    height:36px;
}
.confirm-btn.confirm-primary {
    color: #fff;
    background-color: #1ABC9C;
}

.confirm-btn.confirm-primary-b {
    color: #1ABC9C;
    border: 1px solid #1ABC9C;
}
.confirm-height{
    height: auto;
}
.table-hovers-filexd-r{
    border-left: none !important;
}
.table-hovers-filexd-l{
    border-right: none !important;
}
.zui-table-view .zui-table-fixed .zui-table-header{
    /*border: none;*/
}

/*同组标记 begin*/
.tz-start,
.tz-center,
.tz-stop{
    position: relative;
}
.tz-start::before,
.tz-center::before,
.tz-stop::before{
    position: absolute;
    content: '';
    right: 5px;
    width: 5px;
    border-right: 1px solid #000000;
}
.tz-start::before{
    bottom: -7px;
    top: 14px;
    border-top: 1px solid #000000;
}
.tz-center::before{
    bottom: -7px;
    top: -7px;
}
.tz-stop::before{
    bottom: 14px;
    top: -7px;
    border-bottom: 1px solid #000000;
}
/*同组标记 end*/
/*.loadingPage {*/
    /*position: absolute;*/
    /*top: 50%;*/
    /*left: 50%;*/
    /*transform: translate(-50%,-50%);*/
/*}*/
/*.loading-page{*/
    /*position: fixed;*/
    /*top: 0;*/
    /*left: 0;*/
    /*width: 100%;*/
    /*height: 100%;*/
    /*background-color: #fff;*/
    /*z-index: 11111111111;*/
/*}*/
[v-cloak] {  display: none;}
.ft-14{
    font-size: 14px;
}
.font-12{
    font-size: 12px;
}
.font-16{
    font-size: 16px;
}
.font-18{
    font-size: 18px;
}


/*  */
.position_block{
    position: relative;
    width: 100%;
    display: block;
}

.ft-14{
    font-size: 14px;
}
.font-12 {
    font-size: 12px;
}
.zan-border {
    border: 1px solid #eee;
    line-height: 40px;
}
.font-16{
    font-size: 16px;
}
.font-18{
    font-size: 18px;
}
.justifyAround{
    justify-content: space-around;
}
.whiteSpace{
    white-space: nowrap;
}
input:-webkit-autofill{
    -webkit-box-shadow: 0 0 0px 50px #ffffff inset !important;
}
@media only screen and (max-width: 1024px) {
    .zui-table-view tr th div,.zui-table-view tr td div{
        overflow: hidden !important;
    }
}
.xtmktreediv:hover,.tree_tem1 .tree_text1:hover{
    color: #1abc9c;
    background: rgb(230,246,243);
}

.zui-textarea{
    line-height: 20px;
    padding: 7px 10px;
    resize: none;
}
.imgHover:hover{
    opacity: .9;
}
.flex_items {
    align-items: center;
}
.bdfp:before{
    content: '';
    width: 24px;
    height: 24px;
    background-image: url("/newzui/pub/image/bdfp.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
}
.butt-hover:hover *,
.butt-hover:hover::after,
.butt-hover:hover::before{
    opacity: .6;
}
.tong-search .padd-l-40{
    padding-left: 33px!important;
}
.tong-search .padd-l-54{
    padding-left: 47px!important;
}

.alignItems{
    align-items: center;
}
/*公用组件表格布局*/
/*jieduan-box*/
/*作用域父级名称*/
.jieduan-box .icon-border {
    position: absolute;
    width: 100%;
    height: 1px;
    background: #ffffff;
    transform: rotate(9deg);
    bottom: 15px;
}
.jieduan-box .fenlei {
    position: absolute;
    left: 22px;
    bottom: 0;
    z-index: 11;
    font-size:12px;
    color:#333333;
    text-align:center;
}
.jieduan-box .jieduan {
    position: absolute;
    right: 22px;
    z-index: 11;
    top: 0;
    font-size:12px;
    color:#333333;
    text-align:center;
}
.flex-box{
    display: flex;
}
.flex-box-b{
    display: flex;
    flex-direction: column;
}
.flex-one{
    flex: 1;
}
.jieduan-box .item{
    width:298px;
    padding: 13px;
    min-height:45px;
    border-right:1px solid #e9eee6;
}
.jieduan-box .item-border{
    border-top:1px solid #e9eee6;
}
.rol-title{
    width:138px;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    border-right:1px solid #e9eee6;
    border-top:1px solid #e9eee6;
}
.bg-box{
    border-left:1px solid #e9eee6;
}
.rol-bg{
    background: #edf2f1;

}
.jieduan-box .border-bottom-b{
    border-bottom:1px solid #e9eee6;
}
.jieduan-box .item-header{
    background:#edf2f1;
    height: 36px;
    padding: 0;
    line-height: 36px;
    min-height: 36px;
}