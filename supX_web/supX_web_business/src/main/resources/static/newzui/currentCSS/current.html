<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="js/top.js"></script>
    <link rel="stylesheet" href="css/main.css"/>
    <link rel="stylesheet" href="css/democss.css"/>
</head>
<body>
<div class="nav-top">
    <a href="#top1">1.头部按钮样式大小及默认间距</a>
    <a href="#top8">底部button样式属性</a>
    <a href="#top2">2.头部筛选区通用</a>
    <a href="#top3">3.边框样式</a>
    <a href="#top4">4.常用颜色值</a>
    <a href="#top5">5.背景透明度颜色</a>
    <a href="#top6">6.对齐方式</a>
    <a href="#top7">7.侧边弹窗大小样式归类</a>
    <a href="#top9">9.tab切换通用样式</a>
    <a href="#top10">10.常用icon图标</a>
    <a href="#top11">11.基本操作文本默认样式</a>
    <a href="#top12">12.分页插件样式</a>
    <a href="#top13">13.星级评分</a>
</div>
<div class="current margin20">
    <h2 class="margin20" id="top1">1.头部按钮样式大小及默认间距</h2>
    <div class="tong-top border">

    </div>
    <div class="current-box margin10">
        <h4 class="color-wtg">按钮之间默认右间距10px;按钮高度32px;</h4>
        <div style="width: 100%" class="margin10">
            <p> 选中按钮：tong-btn btn-parmary;</p>
            <button class="tong-btn btn-parmary">选中按钮</button>
        </div>
        <div style="width: 100%" class="margin10">
            <p>默认按钮:tong-btn btn-parmary-b</p>

            <button class="tong-btn btn-parmary-b">默认按钮</button>
        </div>
    </div>
    <div class="current-box">
        <h2 style="width: 100%;" class="margin20"  id="top8">底部button样式属性<i class="color-wtg">(主要用于底部操作button,侧滑底部button,弹窗button,默认最小宽度88px;高度36px;按钮之间默认右间距10px,root-btn为基础样式)</i></h2>
        <div style="width: 100%" class="margin10"><p>默认颜色样式:root-btn btn-parmary</p>
            <button class="root-btn btn-parmary">按钮</button>
        </div>
        <div style="width: 100%" class="margin10"><p>root-btn btn-parmary-f2a</p>
            <button class="root-btn btn-parmary-f2a">按钮</button>
        </div>
        <div style="width: 100%" class="margin10">
            <p>root-btn btn-parmary-d9</p>
            <button class="root-btn btn-parmary-d9">按钮</button>
        </div>
        <div style="width: 100%" class="margin10">
            <p>root-btn btn-parmary-d2</p>
            <button class="root-btn btn-parmary-d2">按钮</button>
        </div>
        <div style="width: 100%" class="margin10">
            <p>root-btn btn-parmary-71</p>
            <button class="root-btn btn-parmary-71">按钮</button>
        </div>
        <div style="width: 100%" class="margin10">
            <p>root-btn btn-parmary-9e</p>
            <button class="root-btn btn-parmary-9e">按钮</button>
        </div>
        <div style="width: 100%" class="margin10">
            <div class="c_radio">
                <input type="radio" id="1" name="radio11" checked>
                <label for="1"></label>
                <label for="1" class="lb_text">单选1</label>
            </div>
            <div class="c_radio">
                <input type="radio" id="2" name="radio11">
                <label for="2"></label>
                <label for="2" class="lb_text">单选2</label>
            </div>
        </div>

    </div>
    <h2 id="top2">2.头部筛选区通用(组件)<i class="color-wtg font14">内容区默认右间距20px;文字与文本框间距5px</i></h2>
    <div class="tong-search" id="selectInput">
        <div class="top-form">
            <label class="top-label">条件</label>
            <div class="top-zinle">
                <div class="top-zinle">
                <select-input class="wh122" @change-data="resultChange"
                              :child="istrue_tran" :index="'nbtclb'" :index_val="'nbtclb'" :val="ypContent.nbtclb"
                              :name="'ypContent.nbtclb'" :search="true" :index_mc="'nbtclb'" >
                </select-input>
            </div>
            </div>
        </div>
        <div class="top-form">
            <label class="top-label">时间段</label>
            <div class="top-zinle">
                <input type="text" class="zui-input wh240"/>
            </div>
        </div>
        <div class="top-form">
            <label class="top-label">检索</label>
            <div class="top-zinle">
                <input type="text" class="zui-input wh182"/>
            </div>
        </div>
    </div>
    <div class="current-box" >

        <div class="top-form">
            <!--<label class="top-label">条件</label>-->
            <div class="top-zinle">
                <!--<input id="ypmc" class="label-input" v-model="popContent.ypmc" @keyup="changeDown($event,'ypmc')" @input="change($event,'ypmc')"-->
                <!--data-notEmpty="true" @keydown="nextFocus($event)">-->
                <!--<search-table :page="page" :message="searchCon" :selected="selSearch" :total="total" :them="them"-->
                <!--:them_tran="them_tran" :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut"-->
                <!--@click-two="selectOne">-->
                <!--</search-table>-->
            </div>
        </div>

    </div>


    <div class="current-box background-h">
        <xmp>
          筛选区调用统一类方式(时间段icon后期补上)
            筛选框宽度针对当前有122px;240px;182px;
            <div class="tong-search">
                <div class="top-form">
                    <label class="top-label">条件</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChange"
                                          :child="istrue_tran" :index="'nbtclb'" :index_val="'nbtclb'" :val="nbtclb"
                                          :name="'ypContent.nbtclb'" :search="true" :index_mc="'nbtclb'" >
                            </select-input>
                        </div>
                </div>
                <div class="top-form">
                    <label class="top-label">时间段</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh240"/>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">检索</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh182"/>
                    </div>
                </div>
            </div>
        </xmp>
    </div>

    <h2 class="margin10" id="top3">3.边框样式:border,border-t;border-r;border-b;border-l;</h2>
   <div class="current-box">
       <div class="wh100 border">border</div>
        <div class="wh100 border-t">上边框border-t</div>
        <div class="wh100 border-r">右边框border-r</div>
        <div class="wh100 border-b">下边框border-b</div>
        <div class="wh100 border-l">左边框border-l</div>
   </div>
    <h2 class="margin10" id="top4">4.常用颜色值<i class="color-wtg">(主要用于特殊颜色字体,表格状态值)</i></h2>
    <div class="current-box">
    <h3><i class="box box0"></i>&ensp;&ensp;color-c1</h3>
    <h3><i class="box box1"></i>&ensp;&ensp;color-cfa</h3>
    <h3><i class="box box2"></i>&ensp;&ensp;color-c3</h3>
    <h3><i class="box box3"></i>&ensp;&ensp;color-c7</h3>
    <h3><i class="box box4"></i>&ensp;&ensp;color-cf9</h3>
    <h3><i class="box box5"></i>&ensp;&ensp;color-ce</h3>
    <h3><i class="box box6"></i>&ensp;&ensp;color-cf3</h3>
    <h3><i class="box box7"></i>&ensp;&ensp;color-c45</h3>
    <h3><i class="box box8"></i>&ensp;&ensp;color-cfb8</h3>
    <h3><i class="box box9"></i>&ensp;&ensp;color-cfb86</h3>
    <h3><i class="box box10"></i>&ensp;&ensp;color-cf0</h3>
    <h3><i class="box box11"></i>&ensp;&ensp;color-c04</h3>
    <h3><i class="box box12"></i>&ensp;&ensp;color-cff5</h3>
    <h3><i class="box box13"></i>&ensp;&ensp;color-ca3</h3>
    <h3><i class="box box15"></i>&ensp;&ensp;color-c00</h3>
    <h3><i class="box box16"></i>&ensp;&ensp;color-wtg</h3>
    <h3><i class="box box17"></i>&ensp;&ensp;color-yzf</h3>
    <h3><i class="box box18"></i>&ensp;&ensp;color-dlr</h3>
    <h3><i class="box box19"></i>&ensp;&ensp;color-wc</h3>
    <h3><i class="box box20"></i>&ensp;&ensp;color-008</h3>
    </div>
    <h2 class="margin10" id="top5">5.背景透明度颜色<i class="color-wtg">(主要用于弹窗蒙层)</i></h2>
    <div class="current-box margin20">
        <div class="current-width">
            <i class="bkwidth background-rgb9"></i>
            <i>0.9 &ensp;background-rgb9</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb8"></i>
            <i>0.8 &ensp;background-rgb8</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb7"></i>
            <i>0.7 &ensp;background-rgb7</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb6"></i>
            <i>0.6 &ensp;background-rgb6</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb5"></i>
            <i>0.5 &ensp;background-rgb5</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb4"></i>
            <i>0.4 &ensp;background-rgb4</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb3"></i>
            <i>0.3 &ensp;background-rgb3</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb2"></i>
            <i>0.2 &ensp;background-rgb2</i>
        </div>
        <div class="current-width">
            <i class="bkwidth background-rgb1"></i>
            <i>0.1 &ensp;background-rgb1</i>
        </div>

    </div>
    <h2 id="top6">6.对齐方式<i class="color-wtg">(!important)</i></h2>
    <div class="current-box">
        <div class="current-width text-center">居中:text-center</div>
        <div class="current-width text-left">居左:text-left</div>
        <div class="current-width text-right">居右:text-right</div>
        <div class="current-width text-justify">两端:text-justify</div>
    </div>
    <h2 id="top7">7.侧边弹窗大小样式归类(针对目前805px(pop-805),548px(pop-548),320px(pop-width))title区左右间距默认19px;内容区上下间距默认15px;左右默认20px;<i class="color-wtg">(在805px,548px下一个li内容区与li的默认下间距15px;默认右间距20px)</i></h2>
    <div style="width: 100%; clear:both;font-weight: bold">1.805px下,上下默认间距15px;左右默认间距20px;</div>
    <div class="current-side">
        <div class="current-pr fl" style="overflow: auto">
        <div class="side-form ng-hide pop-805"  id="brzcList" role="form">
        <div class="fyxm-side-top flex-between">
            <span>标题名称</span>
            <span class="fr closex ti-close">关闭icon</span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list flex-start">
                <li>
                        <i>药品名称</i>
                        <input type="text" class="zui-input  background-h" disabled="disabled"/>
                </li>
                <li>
                        <i>药品规格</i>
                        <input type="text" class="zui-input background-h" disabled="disabled"/>
                </li>
                <li>
                        <i>入库数量</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>
                        <i>生产批号</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>
                        <i>药品进价</i>
                        <input type="text" class="zui-input -h" />
                </li>
                <li>
                        <i>药品零价</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>
                        <i>生产日期</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>
                        <i>有效期至</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>
                        <i class="text-justify">产地产家</i>
                        <input type="text" class="zui-input "/>
                </li>
                <li>
                        <i>库房单位</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>
                        <i>药房单位</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>

                        <i>分装比例</i>
                        <input type="text" class="zui-input "/>

                </li>
                <li>
                        <i>供货单位</i>
                        <input type="text" class="zui-input "/>
                </li>
                <li>
                        <i>产品标准&ensp;号</i>
                        <input type="text" class="zui-input "/>
                </li>
                <li>
                        <i>批准文号</i>
                        <input type="text" class="zui-input "/>
                </li>
                <li>
                        <i>合格证号</i>
                        <input type="text" class="zui-input "/>
                </li>
                <li>
                        <i>外观质量</i>
                        <input type="text" class="zui-input "/>
                </li>
                <li>
                        <i>验收结论</i>
                        <input type="text" class="zui-input " />
                </li>
                <li>
                        <i>招标方式</i>
                        <input type="text" class="zui-input "/>
                </li>
                <li>
                        <i>备注说明</i>
                        <input type="text" class="zui-input " />
                </li>

            </ul>
        </div>
        <!--<div class="ksys-btn">-->
        <!--<p class="text-left font-weight padd-t-20 padd-l-20">底部固定样式框，主要用于侧滑弹窗</p>-->
        <!--</div>-->
    </div>

        </div>
        <div class="current-pr fr background-h" style="height:450px;overflow: auto">
            <xmp>
                <div class="side-form ng-hide pop-805"  id="brzcList" role="form">
                    <div class="fyxm-side-top flex-between">
                        <span>标题名称</span>
                        <span class="fr closex ti-close">关闭icon</span>
                    </div>
                    <div class="ksys-side">
                        <ul class="tab-edit-list flex-start">
                            <li>
                                    <i>药品名称</i>
                                    <input type="text" class="zui-input  background-h" disabled="disabled"/>
                            </li>
                            <li>
                                    <i>药品规格</i>
                                    <input type="text" class="zui-input background-h" disabled="disabled"/>
                            </li>
                            <li>
                                    <i>入库数量</i>
                                    <input type="text" class="zui-input " />
                            </li>
                        </ul>
                    </div>
                    <!--<div class="ksys-btn">-->
                    <!--<p class="text-left font-weight padd-t-20 padd-l-20">底部固定样式框，主要用于侧滑弹窗</p>-->
                    <!--</div>-->
                </div>
            </xmp>

        </div>
    </div>
    <div style="width: 100%; clear:both;font-weight: bold">2.548px下,上下默认间距15px;左右默认间距20px;</div>
    <div class="current-side">
        <div class="current-pr fl" style="overflow: auto">
            <div class="side-form ng-hide pop-548"  id="brzcList" role="form">
            <div class="fyxm-side-top flex-between">
                <span>标题名称</span>
                <span class="fr closex ti-close">关闭icon</span>
            </div>
            <div class="ksys-side">
                <ul class="tab-edit-list tab-edit2-list">
                    <li>
                            <i>药品名称</i>
                            <input type="text" class="zui-input  background-h" disabled="disabled"/>
                    </li>
                    <li>
                            <i>药品规格</i>
                            <input type="text" class="zui-input background-h" disabled="disabled"/>
                    </li>
                    <li>
                            <i>入库数量</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i>生产批号</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i>药品进价</i>
                            <input type="text" class="zui-input -h" />
                    </li>
                    <li>
                            <i>药品零价</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i>生产日期</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i>有效期至</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i class="text-justify">产地产家</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>库房单位</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i>药房单位</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i>分装比例</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>供货单位</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>产品标准&ensp;号</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>批准文号</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>合格证号</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>外观质量</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>验收结论</i>
                            <input type="text" class="zui-input " />
                    </li>
                    <li>
                            <i>招标方式</i>
                            <input type="text" class="zui-input "/>
                    </li>
                    <li>
                            <i>备注说明</i>
                            <input type="text" class="zui-input " />
                    </li>

                </ul>
            </div>
            <!--<div class="ksys-btn">-->
                <!--<p class="text-left font-weight padd-t-20 padd-l-20">底部固定样式框，主要用于侧滑弹窗</p>-->
            <!--</div>-->
        </div>
        </div>
        <div class="current-pr fr background-h" style="height:450px;overflow: auto">
                <xmp>
                    <div class="side-form ng-hide pop-548"  id="brzcList" role="form">
                        <div class="fyxm-side-top flex-between">
                            <span>标题名称</span>
                            <span class="fr closex ti-close">关闭icon</span>
                        </div>
                        <div class="ksys-side">
                            <ul class="tab-edit-list tab-edit2-list">
                                <li>
                                        <i>药品名称</i>
                                        <input type="text" class="zui-input  background-h" disabled="disabled"/>
                                </li>
                                <li>
                                        <i>药品规格</i>
                                        <input type="text" class="zui-input background-h" disabled="disabled"/>
                                </li>
                                <li>
                                        <i>入库数量</i>
                                        <input type="text" class="zui-input " />
                                </li>

                            </ul>
                        </div>
                        <!--<div class="ksys-btn">-->
                        <!--<p class="text-left font-weight padd-t-20 padd-l-20">底部固定样式框，主要用于侧滑弹窗</p>-->
                        <!--</div>-->
                    </div>

                </xmp>
        </div>
    </div>
    <div style="width: 100%; clear:both;font-weight: bold">3.320px下,上下默认间距15px;左右默认间距14px;<i class="color-wtg">(320px下,li内容区与下一个li内容区的默认下间距15px)</i></div>
    <div class="current-side">
        <div class="current-pr fl">
            <div class="side-form ng-hide pop-width"  id="brzcList" role="form">
                <div class="fyxm-side-top flex-between">
                    <span>标题名称</span>
                    <span class="fr closex ti-close">关闭icon</span>
                </div>
                <div class="ksys-side">
                    <ul class="tab-edit-list1 flex-start">
                        <li>
                                <i>药房单位</i>
                                <input type="text" class="zui-input " />
                        </li>
                        <li>
                                <i>分装比例</i>
                                <input type="text" class="zui-input "/>
                        </li>
                        <li>
                                <i>供货单位</i>
                                <input type="text" class="zui-input "/>
                        </li>
                        <li>
                                <i>产品标准号</i>
                                <input type="text" class="zui-input "/>
                        </li>
                        <li>
                                <i>批准文号</i>
                                <input type="text" class="zui-input "/>
                        </li>

                    </ul>
                </div>
                <!--<div class="ksys-btn">-->
                <!--<p class="text-left font-weight padd-t-20 padd-l-20">底部固定样式框，主要用于侧滑弹窗</p>-->
                <!--</div>-->
            </div>


        </div>
        <div class="current-pr fr background-h" style="height:450px;overflow: auto">
           <xmp>
               <div class="side-form ng-hide pop-width"  id="brzcList" role="form">
                   <div class="fyxm-side-top flex-between">
                       <span>标题名称</span>
                       <span class="fr closex ti-close">关闭icon</span>
                   </div>
                   <div class="ksys-side">
                       <ul class="tab-edit-list1 flex-start">
                           <li>
                                   <i>药房单位</i>
                                   <input type="text" class="zui-input " />
                           </li>
                           <li>
                                   <i>分装比例</i>
                                   <input type="text" class="zui-input "/>
                           </li>

                       </ul>
                   </div>
                   <!--<div class="ksys-btn">-->
                   <!--<p class="text-left font-weight padd-t-20 padd-l-20">底部固定样式框，主要用于侧滑弹窗</p>-->
                   <!--</div>-->
               </div>

           </xmp>

        </div>
    </div>

        <h2 id="top9">9.tab切换通用样式</h2>
        <div class="current-side">
            <div class="current-pr fl  tabBar">
                <div class="fyxm-tab">
                    <div><span :class="{'active':num==0}" @click="tabBg(0)">选项卡1</span></div>
                    <div><span :class="{'active':num==1}" @click="tabBg(1)">选项卡2</span></div>
                    <div><span :class="{'active':num==2}" @click="tabBg(2)">选项卡3</span></div>
                </div>
                <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==0}">
                    <p style="height: 250px">选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1选项卡1</p>
                </div>
                <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==1}">
                    <p style="height: 250px">选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2选项卡2</p>
                </div>
                <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==2}">
                    <p style="height: 250px">选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3选项卡3</p>
                </div>
            </div>
            <div class="current-pr fr background-h"  style="height:250px;overflow: auto">
                <xmp>
                    <div class="fyxm-tab">
                        <div><span :class="{'active':num==0}" @click="tabBg(0)">选项卡1</span></div>
                        <div><span :class="{'active':num==1}" @click="tabBg(1)">选项卡2</span></div>
                        <div><span :class="{'active':num==2}" @click="tabBg(2)">选项卡3</span></div>
                    </div>
                    <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==0}">
                        选项卡1
                    </div>
                    <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==1}">
                        选项卡2

                    </div>
                    <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==2}">
                        选项卡3
                    </div>
                </xmp>

            </div>
        </div>

<div class="current-box">
    <h2 id="top10" style="width: 100%;float: left" class="margin20">10.常用icon图标库(字体图标大小定义已有14px-20px 引用icon-font14~icon-font20)</h2>
    <div class="current-icon margin20">
        <span><i class="iconfont icon-iocn1 icon-c5"></i><i>iconfont icon-iocn1 icon-c5(已有颜色自定义)icon-cf(白色)</i></span>
        <span><i class="iconfont icon-iocn1"></i><i>iconfont icon-iocn1</i></span>
        <span><i class="iconfont icon-iocn1 icon-c1"></i><i>iconfont icon-iocn1 icon-c1(已有颜色自定义) icon-cf08(透明度0.8)</i></span>
        <span><i class="iconfont icon-iocn2 icon-c4"></i><i>iconfont icon-iocn2 icon-c4(已有颜色自定义)</i></span>
        <span><i class="iconfont icon-iocn2"></i><i>iconfont icon-iocn2</i></span>
        <span><i class="iconfont icon-iocn3 icon-c75"></i><i>iconfont icon-iocn3 icon-c75(已有颜色自定义)</i></span>
        <span><i class="iconfont icon-iocn3"></i><i>iconfont icon-iocn3</i></span>
        <span><i class="iconfont icon-iocn4"></i><i>iconfont icon-iocn4 icon-font14自定义大小字体图标后面引用icon-font14(表示当前字体图标大小14px,依次类推)</i></span>
        <span><i class="iconfont icon-iocn5"></i><i>iconfont icon-iocn5</i></span>
        <span><i class="iconfont icon-iocn6"></i><i>iconfont icon-iocn6</i></span>
        <span><i class="iconfont icon-iocn7"></i><i>iconfont icon-iocn7</i></span>
        <span><i class="iconfont icon-iocn8"></i><i>iconfont icon-iocn8</i></span>
        <span><i class="iconfont icon-iocn9"></i><i>iconfont icon-iocn9</i></span>
        <span><i class="iconfont icon-iocn10"></i><i>iconfont icon-iocn10</i></span>
        <span><i class="iconfont icon-iocn11"></i><i>iconfont icon-iocn11</i></span>
        <span><i class="iconfont icon-iocn12"></i><i>iconfont icon-iocn12</i></span>
        <span><i class="iconfont icon-iocn13"></i><i>iconfont icon-iocn13</i></span>
        <span><i class="iconfont icon-iocn14"></i><i>iconfont icon-iocn14</i></span>
        <span><i class="iconfont icon-iocn15"></i><i>iconfont icon-iocn15</i></span>
        <span><i class="iconfont icon-iocn16"></i><i>iconfont icon-iocn16</i></span>
        <span><i class="iconfont icon-iocn17"></i><i>iconfont icon-iocn17</i></span>
        <span><i class="iconfont icon-iocn18"></i><i>iconfont icon-iocn18</i></span>
        <span><i class="iconfont icon-iocn19"></i><i>iconfont icon-iocn19</i></span>
        <span><i class="iconfont icon-iocn20"></i><i>iconfont icon-iocn20</i></span>
        <span><i class="iconfont icon-iocn21"></i><i>iconfont icon-iocn21</i></span>
        <span><i class="iconfont icon-iocn22"></i><i>iconfont icon-iocn22</i></span>
        <span><i class="iconfont icon-iocn23"></i><i>iconfont icon-iocn23</i></span>
        <span><i class="iconfont icon-iocn24"></i><i>iconfont icon-iocn24</i></span>
        <span><i class="iconfont icon-iocn25"></i><i>iconfont icon-iocn25</i></span>
        <span><i class="iconfont icon-iocn26"></i><i>iconfont icon-iocn26</i></span>
        <span><i class="iconfont icon-iocn27"></i><i>iconfont icon-iocn27</i></span>
        <span><i class="iconfont icon-iocn28"></i><i>iconfont icon-iocn28</i></span>
        <span><i class="iconfont icon-iocn29"></i><i>iconfont icon-iocn29</i></span>
        <span><i class="iconfont icon-iocn30"></i><i>iconfont icon-iocn30</i></span>
        <span><i class="iconfont icon-iocn31"></i><i>iconfont icon-iocn31</i></span>
        <span><i class="iconfont icon-iocn32"></i><i>iconfont icon-iocn32</i></span>
        <span><i class="iconfont icon-iocn33"></i><i>iconfont icon-iocn33</i></span>
        <span><i class="iconfont icon-iocn34"></i><i>iconfont icon-iocn34</i></span>
        <span><i class="iconfont icon-iocn35"></i><i>iconfont icon-iocn35</i></span>
        <span><i class="iconfont icon-iocn36"></i><i>iconfont icon-iocn36</i></span>
        <span><i class="iconfont icon-iocn37"></i><i>iconfont icon-iocn37</i></span>
        <span><i class="iconfont icon-iocn38"></i><i>iconfont icon-iocn38</i></span>
        <span><i class="iconfont icon-iocn39"></i><i>iconfont icon-iocn39</i></span>
        <span><i class="iconfont icon-iocn40"></i><i>iconfont icon-iocn40</i></span>
        <span><i class="iconfont icon-iocn41"></i><i>iconfont icon-iocn41</i></span>
        <span><i class="iconfont icon-iocn42"></i><i>iconfont icon-iocn42</i></span>
        <span><i class="iconfont icon-iocn43"></i><i>iconfont icon-iocn43</i></span>
        <span><i class="iconfont icon-iocn44"></i><i>iconfont icon-iocn44</i></span>
        <span><i class="iconfont icon-iocn45 icon-hover"></i><i>iconfont icon-iocn45  icon-hover鼠标移上去变色</i></span>
        <span><i class="iconfont icon-iocn46"></i><i>iconfont icon-iocn46</i></span>
        <span><i class="iconfont icon-iocn47"></i><i>iconfont icon-iocn47</i></span>
        <span><i class="iconfont icon-iocn48"></i><i>iconfont icon-iocn48</i></span>
        <span><i class="iconfont icon-iocn49"></i><i>iconfont icon-iocn49</i></span>
        <span><i class="iconfont icon-iocn50"></i><i>iconfont icon-iocn50</i></span>
        <span><i class="iconfont icon-iocn51"></i><i>iconfont icon-iocn51</i></span>
        <span><i class="iconfont icon-iocn52"></i><i>iconfont icon-iocn52</i></span>
        <span><i class="iconfont icon-iocn53"></i><i>iconfont icon-iocn53</i></span>
        <span><i class="iconfont icon-iocn54"></i><i>iconfont icon-iocn54</i></span>
        <span><i class="iconfont icon-iocn55"></i><i>iconfont icon-iocn55</i></span>
        <span><i class="iconfont icon-iocn56"></i><i>iconfont icon-iocn56</i></span>
        <span><i class="iconfont icon-iocn57"></i><i>iconfont icon-iocn57</i></span>
        <span><i class="iconfont icon-iocn-58"></i><i>iconfont icon-iocn-58</i></span>
        <span><i class="iconfont icon-icon58"></i><i>iconfont icon-icon58</i></span>
        <span><i class="iconfont icon-icon59"></i><i>iconfont icon-icon59</i></span>
        <span><i class="iconfont icon-icon60"></i><i>iconfont icon-icon60</i></span>
        <span><i class="iconfont icon-icon61"></i><i>iconfont icon-icon61</i></span>
        <span><i class="iconfont icon-icon62"></i><i>iconfont icon-icon62</i></span>
        <span><i class="iconfont icon-icon63"></i><i>iconfont icon-icon63</i></span>
        <span><i class="iconfont icon-icon64"></i><i>iconfont icon-icon64</i></span>
        <span><i class="iconfont icon-icon65"></i><i>iconfont icon-icon65</i></span>
        <span><i class="iconfont icon-icon66"></i><i>iconfont icon-icon66</i></span>
        <span><i class="iconfont icon-icon67"></i><i>iconfont icon-icon67</i></span>
        <span><i class="iconfont icon-icon68"></i><i>iconfont icon-icon68</i></span>
        <span><i class="iconfont icon-icon69"></i><i>iconfont icon-icon69</i></span>
        <span><i class="iconfont icon-icon70 icon-c75"></i><i>iconfont icon-icon70</i></span>
        <span><i class="iconfont icon-icon71"></i><i>iconfont icon-icon71</i></span>
        <span><i class="iconfont icon-icon72"></i><i>iconfont icon-icon72</i></span>
        <span><i class="iconfont icon-icon73"></i><i>iconfont icon-icon73</i></span>
        <span><i class="iconfont icon-icon74"></i><i>iconfont icon-icon74</i></span>
        <span><i class="iconfont icon-icon75"></i><i>iconfont icon-icon75</i></span>
        <span><i class="iconfont icon-icon76"></i><i>iconfont icon-icon76</i></span>
        <span><i class="iconfont icon-Artboard-1"></i><i>iconfont icon-Artboard-1</i></span>
        <span><i class="iconfont icon-Artboard-12 flex-start"><em class="path1"></em><em class="path2"></em></i><i>该图标生成有些特别,引用方式为<xmp><i class="iconfont icon-Artboard-12 flex-start"><em class="path1"></em><em class="path2"></em></xmp></i></span>
    </div>


</div>
<div class="current-box">
    <h2 class="margin20" id="top11">11.内容区与边框默认间距<i class="color-wtg font14">10px 20px 15px 20px(上右下左)</i></h2>
    <div class="jbxx">
        <div class="jbxx-size">
            <div class="jbxx-position">
                <span class="jbxx-top"></span>
                <span class="jbxx-text">基本信息</span>
                <span class="jbxx-bottom"></span>
            </div>
            <div class="jbxx-box">
            <div class="top-form">
                <label class="top-label">条件</label>
                <div class="top-zinle">
                    <input type="text" class="zui-input wh122"/>
                </div>
            </div>
                <div class="top-form">
                    <label class="top-label">条件</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh122"/>
                    </div>
                </div>
                <div class="top-form">
                    <label class="top-label">条件</label>
                    <div class="top-zinle">
                        <input type="text" class="zui-input wh122"/>
                    </div>
                </div>
            </div>
        </div>

    </div>

</div>
<div class="current-box">
    <div class="current-side fl">
        <h2 id="top12">12.分页插件样式</h2></div>
    <div class="current-side fr background-h">
        <xmp>
            <div class="zui-table-tool">
                <div class="zui-table-page">
                    <button  class="page-btn jump-btn" type="button"
                             @click="goPage(page, null, null)">跳转</button>
                    <input type="number" min="1" value="1" v-model="page" class="page-input jump"/>
                    <span class="page-count">共 <i class="color-green">{{totlePage}}</i> 页,每页显示多少条</span>
                    <span class="page-limits">
                            <select lay-ignore="" v-model="param.rows" @change="getData()">
                                <option value="10">10 条</option>
                                <option value="20">20 条</option>
                                <option value="30">30 条</option>
                                <option value="40">40 条</option>
                                <option value="50">50 条</option>
                                <option value="60">60 条</option>
                                <option value="70">70 条</option>
                                <option value="80">80 条</option>
                                <option value="90">90 条</option>
                            </select>
                            <em class="dot-bottom"></em>
                        </span>
                    <div class="page-right">
                        <a href="javascript:;" class="page-prev" @click="goPage(1, null, null)"
                           :class="page<=1?'disabled':''">
                            <i class="page-more"></i>
                        </a>
                        <a href="javascript:;" class="page-prev"
                           :class="page<=1?'disabled':''"
                           @click="goPage(page, 'prev','getData')">
                            <i class="page-prev"></i>
                        </a>
                        <a :class="{'page-curr': param.page == 1}"
                           @click="goPage(1, null, null)">
                            <em>1</em>
                        </a>
                        <a class="page-spr" v-show="prevMore">···</a>
                        <a href="javascript:;" data-page=""
                           v-for="(item, $index) in totlePage"
                           v-text="item"
                           :class="{'page-curr': param.page == item}"
                           @click="goPage(item, null, null)"
                           v-show="showLittle(item)"></a>
                        <a class="page-spr" v-show="nextMore">···</a>
                        <a class=""
                           :class="{'page-curr': param.page == totlePage}"
                           @click="goPage(totlePage, null, null)"
                           v-text="totlePage"
                           v-show="totlePage > 1"></a>
                        <a href="javascript:;" class="page-next"
                           :class="param.page >= totlePage?'disabled':''"
                           @click="goPage(page, 'next','getData')">
                            <i class="page-next"></i>
                        </a>
                        <a href="javascript:;" class="page-next" @click="goPage(totlePage, null, null)"
                           :class="param.page >= totlePage?'disabled':''">
                            <i class="page-nextMore"></i>
                        </a>
                    </div>
                </div>
        </xmp>
    </div>
</div>
<div class="current-box" id="star">
    <h2 id="top13" class="margin20">13.星级评分通用</h2>
    <div class="wxdj-star">
                        <span v-for="(star,index) in stars"  track-by="index" @click="rating(index)">
                        <img v-bind :src="star.src" class="star-item"/>
                        <em v-text="star.starName" :class="{'color-cf3':star.active==true}"></em>
      </span>
    </div>
</div>

</div>
</body>
<script src="js/base.js"></script>
</html>