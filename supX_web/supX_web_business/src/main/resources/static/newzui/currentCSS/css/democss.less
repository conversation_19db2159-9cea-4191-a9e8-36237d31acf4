@import "baseColor";
i{
  display: block;
}
.current{
  box-sizing: border-box;
}
a{
  text-decoration: none;
}
.nav-top{
  width:200px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  box-sizing: border-box;
  background: @ceee;
  font-size: 16px;
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  z-index: 9999;
  overflow: auto;

a{
  width: 100%;
  cursor: pointer;
  font-weight: bold;
  color: #2e88e3;
  padding: 0 10px;
  box-sizing: border-box;
}
}

.current{
  margin-left: 240px;
}

.wh100{
  width:@wh80;
  height: @height36;
  margin: @size15;
  padding: @size15;
  float: left;
}
.current-box{
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}
h3{
  width: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.box{
  width: @size15;
  height: @size15;
  margin: @size5;
}
.box0{
  background:@c1abc9c;
}
.box1{
  background:@cfafafa;
}
.box2{
  background: @c333;
}
.box3{
  background: @cf9fbfc;
}
.box4{
  background: @cf9fbfc;
}
.box5{
  background: @ceee;
}
.box6{
  background: @cf3b169;
}
.box7{
  background: @c45e135;
}
.box8{
  background: @cfb86000f;
}
.box9{
  background: @cfb8600;
}
.box10{
  background:@cf0fafe;
}
.box11{
  background: @c04a9f5;
}
.box12{
  background: @cff5c63;
}
.box13{
  background: @ca389d4;
}

.box15{
  background: @c000;
}
.box16{
  background: @cff5c63;
}
.box17{
  background: @coloryzf;
}
.box18{
  background: @color2e;
}
.box19{
  background: @color35;
}
.box20{
  background: @color008;
}
.current-width{
  width: 150px;
  height: 100px;
  margin: @size20;
  i{
    display: block;
    font-style: normal;
  }
  .bkwidth{
    width: 150px;
    height: 100px;
  }
}
.current-side{
  width: 100%;
}
.fl{
  float: left;
}
.fr{
  float: right;
}
.current-pr{
  width: calc(~"(100% - 40px)/2");
}
.current-icon{
  width: 100%;
  float: left;
  span{
    float: left;
    margin: 0 20px 25px 0;
  }
}