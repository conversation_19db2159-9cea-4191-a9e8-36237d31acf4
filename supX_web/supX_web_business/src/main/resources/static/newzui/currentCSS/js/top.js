//由于用iframe引用的页面所以在实际环境中要减去60px的菜单宽度，故响应式css不应涉及到框架的样式
document.write('<link href="css/fonts/font-awesome.css" rel="stylesheet" type="text/css"/>'+
    '<link href="css/css/animation.css" rel="stylesheet" type="text/css"/>' +
    '<link href="css/css/common.css" rel="stylesheet" type="text/css"/>' +
    '<link href="css/css/login.min.css" rel="stylesheet" type="text/css"/>' +
    '<script type="text/javascript" src="js/jquery.min.js"></script>' +
    '<script type="text/javascript" src="js/vue/vue.js"></script>' +
    '<script type="text/javascript" src="js/vue/vue-resource.js"></script>' +
    '<script type="text/javascript" src="js/pub/dictionaries.js"></script>' +
    '<script type="text/javascript" src="js/time/WdatePicker.js"></script>' +
    '<script type="text/javascript" src="js/pub/common.js"></script>' +
    '<script type="text/javascript" src="js/pub/components.js"></script>' +
    '<script type="text/javascript" src="js/pub/content.js"></script>' +
    '<script type="text/javascript" src="js/pub/printTemplets.js"></script>'+
    '<script type="text/javascript" src="js/pub/jsg.min.js"></script>');
