@import url('reset.min.css');
@import url('plugins.min.css');

/** Resets */
body,
html {
    min-height: 100%;
    color: #767d85;
    background-color: #f3f3f3;
    font-size: 14px;
    min-width: 1024px;
}
body::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
body::-webkit-scrollbar-thumb {
    -webkit-border-radius: 1px;
    -moz-border-radius: 1px;
    -ms-border-radius: 1px;
    -o-border-radius: 1px;
    border-radius: 1px;
    background: rgba(47, 64, 80, 0.46);
    border: none;
}
body::-webkit-scrollbar-track {
    background: #f9f9f9;
    border: none;
}
body::-webkit-scrollbar-button {
    display: none;
}
body::-webkit-scrollbar-corner {
    background: #f9f9f9;
}
a,
li,
button,
input,
i {
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
/** global布局 */
.wrapper {
    padding: 15px;
    position: relative;
}
.contact-box {
    background-color: #fff;
    border: 1px solid #eee;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
    min-height: 200px;
}
.text-left {
    text-align: left;
}
.text-right {
    text-align: right;
}
.text-center {
    text-align: center;
}
.float-left {
    float: left;
}
.float-right {
    float: right;
}
.tool-center {
    display: inline-block;
    margin: 0 auto;
}
.tool-left {
    display: inline-block;
    float: left;
    margin-right: 4px;
}
.tool-right {
    float: right;
    display: inline-block;
}
.pd0 {
    padding: 0 !important;
}
.b10 {
    margin-bottom: 10px;
}
.border-bottom {
    border-bottom: 1px #eee solid;
}
.toolbar {
    padding: 12px 0;
}
.primary {
    color: #1ABC9C;
}
.blue {
    color: #62a8ea;
}
.success {
    color: #46be8a;
}
.info {
    color: #57c7d4;
}
.warning {
    color: #f2a654;
}
.danger {
    color: #f96868;
}
.zui-select-group {
    display: none;
}
/*面板结构*/
.panel {
    background-color: #fff;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
}
.panel div:first-of-type{
    border-top: none;
}
.panel-head {
    padding: 0  0 0;
    background-color: #fff;
    z-index: 100;
    /*position: relative;*/
}
.panel-head .panel-title {
    font-size: 16px;
    color: #333;
    font-weight: 500;
}
.panel-head.background {
    background-color: #f9f9f9;
}
.panel-body {
    padding: 12px 15px;
}
.panel-body.background {
    background-color: #fcfcfc;
}
.panel-table {
    display: table;
}
.panel-table.background {
    background-color: #fcfcfc;
    /*.gradient(180deg,#fbfbfb, #fff);*/
}
.panel-table > .panel-cell {
    display: table-cell;
    vertical-align: top !important;
}
/** tabs选项卡 **/
.zui-tabs {
    background: #f8f8f8;
    border: 0;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -ms-border-radius: 3px 3px 0 0;
    -o-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
}
.zui-tabs:before {
    display: table;
    content: " ";
}
.zui-tabs:after {
    content: ' ';
    display: block;
    height: 0;
    clear: both;
}
.zui-tabs li {
    float: left;
    margin-bottom: -1px;
    position: relative;
    display: inline-block;
    -webkit-transition: none;
    -moz-transition: none;
    -ms-transition: none;
    -o-transition: none;
    transition: none;
}
.zui-tabs li a {
    display: block;
    border: 0;
    padding: 12px 18px;
    line-height: 1.42857143;
    color: #666;
    max-width: 181px;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -ms-border-radius: 3px 3px 0 0;
    -o-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
    margin-right: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border: 1px solid transparent;
    border-bottom: 0;
    -webkit-transition: none;
    -moz-transition: none;
    -ms-transition: none;
    -o-transition: none;
    transition: none;
}
.zui-tabs li.active a {
    background-color: #fff;
    border-color: #eee;
    font-weight: bold;
}
/* 标题栏 */
.zui-panel-title {
    display: block;
    padding: 16px 20px;
    font-size: 16px;
    color: #767d85;
    font-weight: 500;
    margin: 0;
}
.zui-field-title {
    display: block;
    clear: both;
    width: 100%;
    height: 20px;
    margin-bottom: 20px;
    border-bottom: 1px #eee solid;
    position: relative;
}
.zui-field-title label {
    display: inline-block;
    line-height: 20px;
    padding: 0 10px;
    background-color: #fff;
    position: absolute;
    z-index: 1;
    zoom: 1;
    bottom: 0;
    left: 20px;
    margin-bottom: -10px;
    font-size: 16px;
    color: #c7cacd;
    font-weight: normal;
}
/** btn按钮样式 **/
.zui-btn {
    /*display: inline-block;*/
    /*padding: 4px 12px 4px 6px;*/
    font-size: 14px;
    /*line-height: 1.6;*/
    display: flex;
    padding: 0px 12px 0px 12px;
    font-size: 14px;
    justify-content: center;
    align-items: center;
    float: left;
    height: 32px;
    border: 1px solid transparent;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
    white-space: nowrap;
    vertical-align: middle;
    text-align: center;
    font-weight: 400;
    touch-action: manipulation;
    cursor: pointer;
    user-select: none;
    background-color: #fff;
    color: #767d85;
    position: relative;
    /*默认*/
    /*描边 .btn-outline*/
    /*圆角按钮 .btn-radius*/
    /*质感按钮 .btn-raised*/
    /*图标 .btn-icon*/
    /*无背景*/
}
.zui-btn.active:focus,
.zui-btn:active:focus,
.zui-btn:focus {
    outline: 0;
}
.zui-btn.active,
.zui-btn:active {
    -webkit-box-shadow: none;
    box-shadow: none;
}
.zui-btn.btn-lg {
    padding: 6px 14px;
    font-size: 16px;
    line-height: 1.3333333;
    border-radius: 4px;
}
.zui-btn.btn-sm {
    padding: 3px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 2px;
}
.zui-btn.btn-xs {
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 2px;
}
.zui-btn.btn-pa {
    padding: 4px;
}
.zui-btn i,
.zui-btn [class^="ti-"],
.zui-btn [class*=" ti-"],
.zui-btn [class^="md-"],
.zui-btn [class*=" md-"],
.zui-btn [class^="ion-"],
.zui-btn [class*=" ion-"],
.zui-btn [class^="pe-"],
.zui-btn [class*=" pe-"],
.zui-btn [class^="wb-"],
.zui-btn [class*=" wb-"],
.zui-btn [class^="fa-"],
.zui-btn [class*=" fa-"] {
    width: 1em;
    margin: 0 3px;
    line-height: inherit;
    text-align: center;
    line-height: 1em;
}
.zui-btn.btn-default {
    color: #767d85;
    background-color: #e4eaec;
}
.zui-btn.btn-default:hover {
    /*background-color: #ccd5db;*/
    color:rgba(118,125,133,0.6)
}
.zui-btn.btn-default:active {
    background-color: #76838f;
    color: #fff;
}
.zui-btn.btn-primary {
    color: #fff;
    background-color: #1ABC9C;
}
.zui-btn.btn-primary:hover {
    /*background-color: #1ed7b2;*/
    color: rgba(255,255,255,0.6);
}
.zui-btn.btn-primary:active {
    background-color: #16a186;
}
.zui-btn.btn-blue {
    color: #fff;
    background-color: #62a8ea;
}
.zui-btn.btn-blue:hover {
    background-color: #7db7ee;
}
.zui-btn.btn-blue:active {
    background-color: #4799e6;
}
.zui-btn.btn-success {
    color: #fff;
    background-color: #46be8a;
}
.zui-btn.btn-success:hover {
    background-color: #5dc698;
}
.zui-btn.btn-success:active {
    background-color: #3caa7a;
}
.zui-btn.btn-info {
    color: #fff;
    background-color: #57c7d4;
}
.zui-btn.btn-info:hover {
    background-color: #6fcfda;
}
.zui-btn.btn-info:active {
    background-color: #3fbfce;
}
.zui-btn.btn-warning {
    color: #fff;
    background-color: #f2a654;
}
.zui-btn.btn-warning:hover {
    background-color: #f4b570;
}
.zui-btn.btn-warning:active {
    background-color: #f09738;
}
.zui-btn.btn-danger {
    color: #fff;
    background-color: #f96868;
}
.zui-btn.btn-danger:hover {
    background-color: #fa8585;
}
.zui-btn.btn-danger:active {
    background-color: #f84b4b;
}
.zui-btn.btn-outline {
    background-color: transparent;
    border-color: #ccd5db;
}
.zui-btn.btn-outline.btn-default {
    color: #767d85;
    background-color: #fff;
}
.zui-btn.btn-outline.btn-default:hover {
    background-color: #ccd5db;
}
.zui-btn.btn-outline.btn-default:active {
    background-color: #76838f;
    border-color: #76838f;
    color: #fff;
}
.zui-btn.btn-outline.btn-primary {
    color: #1ABC9C;
    border-color: #1ABC9C;
}
.zui-btn.btn-outline.btn-primary:hover {
    background-color: #1ABC9C;
    color: #fff;
}
.zui-btn.btn-outline.btn-primary:active {
    background-color: #15987e;
    border-color: #15987e;
    color: #fff;
}
.zui-btn.btn-outline.btn-blue {
    color: #62a8ea;
    border-color: #62a8ea;
}
.zui-btn.btn-outline.btn-blue:hover {
    background-color: #62a8ea;
    color: #fff;
}
.zui-btn.btn-outline.btn-blue:active {
    background-color: #3e94e5;
    border-color: #3e94e5;
    color: #fff;
}
.zui-btn.btn-outline.btn-success {
    color: #46be8a;
    border-color: #46be8a;
}
.zui-btn.btn-outline.btn-success:hover {
    background-color: #46be8a;
    color: #fff;
}
.zui-btn.btn-outline.btn-success:active {
    background-color: #39a275;
    border-color: #39a275;
    color: #fff;
}
.zui-btn.btn-outline.btn-info {
    color: #57c7d4;
    border-color: #57c7d4;
}
.zui-btn.btn-outline.btn-info:hover {
    background-color: #57c7d4;
    color: #fff;
}
.zui-btn.btn-outline.btn-info:active {
    background-color: #37bccc;
    border-color: #37bccc;
    color: #fff;
}
.zui-btn.btn-outline.btn-warning {
    color: #f2a654;
    border-color: #f2a654;
}
.zui-btn.btn-outline.btn-warning:hover {
    background-color: #f2a654;
    color: #fff;
}
.zui-btn.btn-outline.btn-warning:active {
    background-color: #ef922e;
    border-color: #ef922e;
    color: #fff;
}
.zui-btn.btn-outline.btn-danger {
    color: #f96868;
    border-color: #f96868;
}
.zui-btn.btn-outline.btn-danger:hover {
    background-color: #f96868;
    color: #fff;
}
.zui-btn.btn-outline.btn-danger:active {
    background-color: #f74141;
    border-color: #f74141;
    color: #fff;
}
.zui-btn.btn-radius {
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    -ms-border-radius: 100px;
    -o-border-radius: 100px;
    border-radius: 100px;
}
.zui-btn.btn-raised {
    -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.21);
    -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.21);
    -ms-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.21);
    -o-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.21);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.21);
}
.zui-btn.btn-icon {
    padding: 8px 6px;
    line-height: 1em;
}
.zui-btn.btn-none,
.zui-btn.btn-none:hover,
.zui-btn.btn-none:active {
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;
}
/*徽章*/
.zui-badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
    color: #76838f;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    background-color: #e4eaec;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -ms-border-radius: 15px;
    -o-border-radius: 15px;
    border-radius: 15px;
    padding: 2px 6px;
    /*圆角*/
    /*大小*/
    /*定位*/
}
.zui-badge.badge-primary {
    color: #fff;
    background-color: #1ABC9C;
}
.zui-badge.badge-blue {
    color: #fff;
    background-color: #62a8ea;
}
.zui-badge.badge-success {
    color: #fff;
    background-color: #46be8a;
}
.zui-badge.badge-info {
    color: #fff;
    background-color: #57c7d4;
}
.zui-badge.badge-warning {
    color: #fff;
    background-color: #f2a654;
}
.zui-badge.badge-danger {
    color: #fff;
    background-color: #f96868;
}
.zui-badge.badge-dark {
    color: #fff;
    background-color: #2f4050;
}
.zui-badge.badge-radius {
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    border-radius: 3px;
}
.zui-badge.badge-sm {
    padding: 2px 5px;
    font-size: 10px;
}
.zui-badge.up {
    position: relative;
    top: -10px;
    margin: 0 -0.8em;
    border-radius: 15px;
}
/*下拉菜单 .dropdown*/
/** 表单样式 from: **/
.zui-row {
    width: 100%;
}
.zui-row:after,
.zui-row:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}
.zui-inline,
.zui-input-inline,
.zui-select-inline {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    /*margin-right: 10px;*/
}
.zui-form-label {
    display: block;
    padding: 6px 10px 6px 0;
    line-height: 20px;
    width: 110px;
    font-weight: 400;
    text-align: right;
    color: #a7b1c2;
    font-size: 14px;
}
.zui-input,
.zui-textarea {
    display: block;
    width: 100%;
    padding: 0 10px;
    height: 36px;
    line-height: 36px;
    border: 1px solid #e6e6e6;
    background-color: #fff;
    color: #354052;
    font-size: 14px;
    border-radius: 4px;
    /*margin-left: 5px;*/
}
.zui-input.isError,
.zui-textarea.isError {
    background-color: #fbe2e2;
    border-color: #e84d3d;
}
.zui-input.border-none,
.zui-textarea.border-none {
    border-color: transparent;
}
.zui-input:focus,
.zui-textarea:focus {
    border-color: #1ABC9C;
    background-color: #fff;
}
.zui-input:focus.border-none,
.zui-textarea:focus.border-none {
    border-color: transparent;
}
.zui-input:disabled,
.zui-textarea:disabled {
    background-color: #f8f8f8;
    color:#757c83;
}
.zui-input::-webkit-input-placeholder,
.zui-textarea::-webkit-input-placeholder {
    color: #a7b1c2;
}
.zui-input:-moz-placeholder,
.zui-textarea:-moz-placeholder {
    color: #a7b1c2;
}
.zui-input::-moz-placeholder,
.zui-textarea::-moz-placeholder {
    color: #a7b1c2;
}
.zui-input:-ms-input-placeholder,
.zui-textarea:-ms-input-placeholder {
    color: #a7b1c2;
}
.zui-input[readonly],
.zui-textarea[readonly] {
    cursor: pointer;
}
.zui-select-inline {
    width: 100%;
    /*width: 120px;*/
}
.zui-select-inline .zui-input {
    padding-right: 20px;
    cursor: pointer;
}
.iconClass{
    position: absolute;
    z-index: 20;
    zoom: 1;
    top:0;
    right: 0;
    width: 20px;
    height: 100%;
    text-align: center;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
}
.iconClass:after {
    content: "\f0d7";
    font-family: 'FontAwesome';
    z-index: 20;
    zoom: 1;
    text-align: center;
    color: #a7b1c2;
}
.zui-date .datenox {
    display: inline-block;
    position: absolute;
    z-index: 20;
    zoom: 1;
    top: 50%;
    left: 6px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    text-align: center;
    color: #1ABC9C;
    cursor: pointer;
}
.zui-date .zui-input {
    padding-left: 24px;
}
.zui-form:after,
.zui-form:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}
.zui-form .zui-inline {
    float: left;
    margin-bottom: 8px;
    padding: 0 20px 0 110px;
}
.zui-form .zui-input-inline {
    width: 100%;
}
.zui-form .zui-form-label {
    position: absolute;
    z-index: 10;
    zoom: 1;
    left: 0;
    top: 0;
}
.zui-form .select-right .zui-input-inline {
    padding-right: 50px;
}
.zui-form .select-right .zui-select-inline {
    position: absolute;
    z-index: 100;
    zoom: 1;
    top: 0;
    right: 12px;
    max-width: 48px;
}
.zui-input-item .zui-select-inline {
    float: left;
    padding-left: 2px;
}
.zui-input-item .zui-select-inline:nth-child(1) {
    padding-left: 0;
}
.validate,
.tipError {
    position: absolute;
    z-index: 10;
    zoom: 1;
    display: inline-block;
    right: -20px;
    top: 50%;
    color: #f00;
    width: 20px;
    text-align: center;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    margin-top: -10px;
}
.tipError {
    position: absolute;
    z-index: 20;
    zoom: 1;
    right: -24px;
    font-size: 14px;
    color: #f96868;
    background-color: #fff;
    cursor: pointer;
}
/** checkbox **/
input[type=checkbox].zui-checkbox {
    display: none;
}
input[type=checkbox].zui-checkbox + label {
    display: inline-block;
    padding-left: 18px;
    height: 18px;
    line-height: 18px;
    position: relative;
    cursor: pointer;
    color: #a7b1c2;
    vertical-align: middle;
}
input[type=checkbox].zui-checkbox + label:before {
    content: " ";
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-family: 'WebIcons';
    position: absolute;
    z-index: 2;
    zoom: 1;
    top: 50%;
    left: 0;
    margin-top: -8px;
    border: 1px #d2d2d2 solid;
    background-color: #fff;
    font-size: 12px;
    border-radius: 2px;
    text-align: center;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
input[type=checkbox].zui-checkbox + label:hover:before {
    border-color: #1ABC9C;
}
input[type=checkbox].zui-checkbox:checked + label:before {
    content: "\f192";
    background-color: #1ABC9C;
    border-color: #1ABC9C;
    color: #fff;
}
/** 栅格系统 **/
.col-fm-1,
.col-fm-2,
.col-fm-3,
.col-fm-4,
.col-fm-5,
.col-fm-6,
.col-fm-7,
.col-fm-8,
.col-fm-9,
.col-fm-10,
.col-fm-11,
.col-fm-12,
.col-x-1,
.col-x-2,
.col-x-3,
.col-x-4,
.col-x-5,
.col-x-6,
.col-x-7,
.col-x-8,
.col-x-9,
.col-x-10,
.col-x-11,
.col-x-12 {
    float: left;
}
.col-fm-12,
.col-x-12 {
    width: 100%;
}
.col-fm-11,
.col-x-11 {
    width: 91.66666667%;
}
.col-fm-10,
.col-x-10 {
    width: 83.33333333%;
}
.col-fm-9,
.col-x-9 {
    width: 75%;
}
.col-fm-8,
.col-x-8 {
    width: 66.66666667%;
}
.col-fm-7,
.col-x-7 {
    width: 58.33333333%;
}
.col-fm-6,
.col-x-6 {
    width: 50%;
}
.col-fm-5,
.col-x-5 {
    width: 41.66666667%;
}
.col-fm-4,
.col-x-4 {
    width: 33.33333333%;
}
.col-fm-3,
.col-x-3 {
    width: 25%;
}
.col-fm-2,
.col-x-2 {
    width: 16.66666667%;
}
.col-fm-1,
.col-x-1 {
    width: 8.33333333%;
}
@media only screen and (max-width: 1600px) {
    .col-fm-11,
    .col-fm-10 {
        width: 100%;
    }
    .col-fm-9 {
        width: 90%;
    }
    .col-fm-8 {
        width: 80%;
    }
    .col-fm-7 {
        width: 70%;
    }
    .col-fm-6 {
        width: 60%;
    }
    .col-fm-5 {
        width: 50%;
    }
    .col-fm-4 {
        width: 40%;
    }
    .col-fm-3 {
        width: 30%;
    }
    .col-fm-2 {
        width: 20%;
    }
    .col-fm-1 {
        width: 10%;
    }
}
@media only screen and (max-width: 1366px) {
    .col-fm-11,
    .col-fm-10,
    .col-fm-9,
    .col-fm-8 {
        width: 100%;
    }
    .col-fm-7 {
        width: 87.5%;
    }
    .col-fm-6 {
        width: 75%;
    }
    .col-fm-5 {
        width: 62.5%;
    }
    .col-fm-4 {
        width: 50%;
    }
    .col-fm-3 {
        width: 37.5%;
    }
    .col-fm-2 {
        width: 25%;
    }
    .col-fm-1 {
        width: 12.5%;
    }
}
@media only screen and (max-width: 1024px) {
    .col-fm-11,
    .col-fm-10,
    .col-fm-9,
    .col-fm-8,
    .col-fm-7,
    .col-fm-6 {
        width: 100%;
    }
    .col-fm-5 {
        width: 83.33333333%;
    }
    .col-fm-4 {
        width: 66.66666667%;
    }
    .col-fm-3 {
        width: 50%;
    }
    .col-fm-2 {
        width: 33.3333333%;
    }
    .col-fm-1 {
        width: 16.6666667%;
    }
}
@media only screen and (max-width: 768px) {
    .col-fm-11,
    .col-fm-10,
    .col-fm-9,
    .col-fm-8,
    .col-fm-7,
    .col-fm-6,
    .col-fm-5,
    .col-fm-4 {
        width: 100%;
    }
    .col-fm-3 {
        width: 75%;
    }
    .col-fm-2 {
        width: 50%;
    }
    .col-fm-1 {
        width: 25%;
    }
}
@media only screen and (max-width: 480px) {
    .col-fm-1,
    .col-fm-2,
    .col-fm-3,
    .col-fm-4,
    .col-fm-5,
    .col-fm-6,
    .col-fm-7,
    .col-fm-8,
    .col-fm-9,
    .col-fm-10,
    .col-fm-11,
    .col-fm-12,
    .col-x-1,
    .col-x-2,
    .col-x-3,
    .col-x-4,
    .col-x-5,
    .col-x-6,
    .col-x-7,
    .col-x-8,
    .col-x-9,
    .col-x-10,
    .col-x-11,
    .col-x-12 {
        width: 100%;
    }
    .zui-form .col-x-1,
    .zui-form .col-x-2,
    .zui-form .col-x-3,
    .zui-form .col-x-4,
    .zui-form .col-x-5,
    .zui-form .col-x-6,
    .zui-form .col-x-7,
    .zui-form .col-x-8,
    .zui-form .col-x-9,
    .zui-form .col-x-10,
    .zui-form .col-x-11,
    .zui-form .col-x-12 {
        margin-bottom: 6px;
        padding: 0;
    }
}
/*工具搜索框*/
.tool-search {
    display: inline-block;
    width: 300px;
    position: relative;
}
.tool-search.select {
    width: 360px;
}
.tool-search.select .zui-input {
    padding: 0 60px 0 90px;
}
.tool-search button {
    display: inline-block;
    position: absolute;
    z-index: 100;
    zoom: 1;
    top: 0;
    right: 0;
    border: 0;
    margin: 0;
    width: 60px;
    text-align: center;
    height: 32px;
    background-color: #1ABC9C;
    color: #fff;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border: 1px solid #18aa8d;
}
.tool-search .zui-select-inline {
    width: 80px;
    position: absolute;
    z-index: 100;
    zoom: 1;
    top: 1px;
    left: 1px;
}
.tool-search .zui-select-inline:after {
    right: 0;
}
.tool-search .zui-select-inline .zui-input {
    border: none;
    height: 30px;
    padding: 0 10px 0 4px;
    text-align: center;
}
/*操作栏*/
.action-bar {
    padding-left: 10px;
}
.action-bar button,
.action-bar a {
    float: right;
    margin-right: 10px;
}
.action-bar.fixed {
    position: fixed;
    background-color: #fff;
    z-index: 990;
    bottom: 10px;
    right: 10px;
    left: 10px;
    border: 1px #eee solid;
    padding-top: 16px;
    padding-bottom: 16px;
    height: 68px;
}
.action-bar ul {
    width: 100%;
}
.action-bar ul li span {
    display: inline-block;
    line-height: 32px;
    margin-right: 20px;
}
.action-bar ul li:last-child {
    text-align: right;
}
.action-bottom:after {
    content: '';
    display: block;
    clear: both;
    height: 50px;
    width: 100%;
}
.hidden {
    display: none;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none!important;
    margin: 0;
}
.emptyError {
    border: 1px solid #be486d !important;
    background-color: #ffe6e2;
}

#jyxm_icon .switch {
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    margin: 0 22%;
}
#jyxm_icon .switch>input {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 45px;
    height: 25px;
    margin: 0;
    cursor: pointer;
    opacity: 0;
}
#jyxm_icon .switch>label {
    display: block;
    padding: 5px 0 5px 35px;
    margin: 0;
    font-weight: 400;
    line-height: 20px;
}
#jyxm_icon .switch>label:after {
    position: absolute;
    top: 5px;
    left: 0;
    display: block;
    width: 40px;
    margin-top: auto;
    z-index: auto;
    text-align: left;
    text-indent: 3px;
    background: #dfe1e5;
    height: 20px;
    pointer-events: none;
    content: ' ';
    border: 1px solid #ddd;
    border-radius: 10px;
    -webkit-transition: all .4s cubic-bezier(.175,.885,.32,1);
    -o-transition: all .4s cubic-bezier(.175,.885,.32,1);
    transition: all .4s cubic-bezier(.175,.885,.32,1);
}
#jyxm_icon .switch>label:before{
    position: absolute;
    top: 5px;
    left: 0;
    display: block;
    line-height: 22px;
    width: 40px;
    margin-top: auto;
    z-index: auto;
    background: #dfe1e5;
    height: 20px;
    text-align: left;
    text-indent: 23px;
    pointer-events: none;
    content: '关';
    color: #fff;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 10px;
    -webkit-transition: all .4s cubic-bezier(.175,.885,.32,1);
    -o-transition: all .4s cubic-bezier(.175,.885,.32,1);
    transition: all .4s cubic-bezier(.175,.885,.32,1);
}

#jyxm_icon .switch>label:after {
    top: 6px;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border-color: #ccc;
    border-radius: 9px;
    -webkit-box-shadow: rgba(0,0,0,.05) 0 1px 4px, rgba(0,0,0,.12) 0 1px 2px;
    box-shadow: rgba(0,0,0,.05) 0 1px 4px, rgba(0,0,0,.12) 0 1px 2px;
}
#jyxm_icon .switch>input:checked+label:after {
    left: 21px;
    border-color: #fff;
}
#jyxm_icon .switch>input:checked+label:before {
    background-color: #1abc9c;
    border-color: #1abc9c;
    color: #fff;
    content:'开';
    text-align: left;
    text-indent: 3px;
    line-height: 22px;
    font-size: 12px;
}


/** 预设宽度width: **/
.wh800 {
    width: 800px;
}
.wh700 {
    width: 700px;
}
.wh600 {
    width: 600px;
}
.wh500 {
    width: 500px;
}
.wh400 {
    width: 400px;
}
.wh383{
    width: 383px;
}
.wh300 {
    width: 300px;
}
.wh280 {
    width: 280px;
}
.wh250 {
    width: 323px;
}
.wh200 {
    width: 200px;
}
.wh180 {
    width: 180px;
}
.wh150 {
    width: 150px;
}
.wh120 {
    width: 120px;
}
.wh100 {
    width: 100px;
}
.wh80 {
    width: 80px;
}
.wh274{
    width: 274px;
}
/*.zui-table-view{*/
    /*overflow: inherit !important;*/
/*}*/
.ybshgl-height{
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
}

#ice > tr.highlight{
    background: #1abc9c;
}
.cell_dropdown{
    margin-top: 8px;
}

.tab-message a{
    font-size: 16px;
}

