//if (self == top) {
//    //var src = document.currentScript.src;
//    location.href = "about:blank";   
//}   
var currentLinetb = -1;
var objArrtb = '';
var userAgent = navigator.userAgent,
    ie6 = (/msie\s*(\d+)\.\d+/g.exec(userAgent.toLowerCase()) || [0, "0"])[1] == "6",
    ie7 = userAgent.indexOf('MSIE 7.0') > -1;
if (ie6 || ie7) {
     window.location.href = '/newzui/login.html';
}

var getFilePath = function () {
    var e = document.scripts,
        t = e[e.length - 1],
        n = t.src;
    if (!t.getAttribute("merge")) return n.substring(0, n.lastIndexOf("/") + 1)
};

var g_File = getFilePath(),
    g_FileJs = [
        // "zui/jquery.zui.js",
        "layer/laydate/laydate.js",
        "layer/layer.js",
        "plugins/jquery.sidefrom.js"
    ];


$(g_FileJs).each(function (index, value) {
    switch (value.substr(value.lastIndexOf(".") + 1)) {
        case "css":
            document.write('<link href="' + g_File + value + '?v=5.303" rel="stylesheet" />');
            break;
        case "js":
            document.write('<script type="text/javascript" src="' + g_File + value + '?v=5.303"></script>');
            break;
    }
});
$(document).on('mouseover mouseout', '.title', function (e) {
    if (e.type == 'mouseover') {
        this.myTitle = this.dataset.gettitle || this.innerHTML || this.value;
        if (this.myTitle != '') {
            var tooltip = "<div id='divlink'>" + this.myTitle + "</div>";
            $(this).parent().append(tooltip);
            var $menu = $('#divlink');
            inOffsetRight = $(window).width() - $('#divlink').offset().left - $menu.width();
            if ($menu.width() < inOffsetRight) {
                $menu.addClass("linkleft");
            } else {
                $menu.addClass("linkdrop");
            }
            ;

        }
    } else if (e.type == 'mouseout') {
        $("#divlink").remove();
    }
})
$(function () {
    //禁用鼠标右键菜单栏
    // $(document).bind("contextmenu", function (event) {
    //     return false;
    // });
    // setTimeout(function () {
    //     $('.fieldlist').css('marginTop', $('.background_top').innerHeight() + 'px')
    // }, 1500);
    if ($(".action-bar.fixed").length) {
        $("body").css("padding-bottom", "50px");
    }

    //给tablebody加一个鼠标悬浮参数  来控制滚动时不让其他滚动区域相互出发滚动事件
    $(document).on('mouseenter mouseleave', '.zui-table-body', function (e) {
        if (e.type == 'mouseenter') {
            $(this).attr('data-ishover', true);
        } else if (e.type == 'mouseleave') {
            $(this).attr('data-ishover', false);
        }
    });

    $(document).on('mousemove mouseout', '[data-title]', function (e) {
        if (e.type == 'mousemove') {
            $("#tplink").remove();
            this.myTitle = this.title || this.getAttribute('data-title');
            var sbX = e.clientX - 5,
                sbY = e.clientY + 25;
            if (this.myTitle != '') {
                var tooltip = "<div id='tplink' style='left:" + sbX + "px;top:" + sbY + "px;'>" + this.myTitle + "</div>";
                $('body').append(tooltip);
                // this.title = this.myTitle;
            }
        } else if (e.type == 'mouseout') {
            $("#tplink").remove();
        }
    });

    // title('.title');
    // function title(obj) {

    // }
    var gign = 10
    $(document).scroll(function () {
        var t = $(document).scrollTop()
        if (t > gign) {
            $('.background').css('top', '0px')
        } else {
            $('.background').css('top', '10px')
        }
    })
});
topcss()

function topcss() {
    setTimeout(function () {
        return $('.top_size').css('marginTop', $('.background,.panel').innerHeight() + 'px');
    }, 10)
}

var currentLine = -1;
var objArr = '';
flag = null;

function Yconhover(obj) {
    if (flag == true) {
        currentLine = currentLine <= 0 ? 0 : currentLine - 1;
    } else if (flag == false) {
        currentLine = currentLine >= $(obj).parent().find('li').length ? 0 : currentLine + 1;
    } else {
        currentLine = $(obj).index()
    }
    objArr = obj;
    flag = null;
    $(obj).parent().find('li').eq(currentLine).addClass('table-hovers');
    $(obj).parent().find('li').eq(currentLine).siblings().removeClass('table-hovers');
    objArr.onkeydown = function (e) {
        switch (e.keyCode) {
            case 38:
                flag = true;
                Yconhover(objArr)
                break;
            case 40:
                flag = false;
                console.log(objArr)
                Yconhover(objArr)
                break;
            case  116:
                e.keyCode = 0;
                e.preventDefault(); //阻止默认刷新
                window.location.reload();
                return false;
                break;
            // case  83 :
            //     return false
            // break;
            case e.ctrlKey:
                return false;
                break;

        }
    }
}

// $(window).keydown(function (e) {
//     switch (e.keyCode) {
//         case 38:
//             flag = true;
//             // changeItem();
//             if (objArrtb != '') {
//                 currentLinetb = currentLinetb - 1;
//                 changeItem();
//             } else if (objArr != '') {
//                 console.log(objArr)
//                 Yconhover(objArr)
//             }
//             break;
//         case 40:
//             flag = false;
//             if (objArrtb != '') {
//                 currentLinetb = currentLinetb + 1;
//                 changeItem();
//             } else if (objArr != '') {
//                 console.log(objArr)
//                 Yconhover(objArr)
//             }
//             break;
//         case  116:
//             e.keyCode = 0;
//             e.preventDefault(); //阻止默认刷新
//             window.location.reload();
//             return false;
//             break;
//         // case  83 :
//         //     return false
//         // break;
//         case e.ctrlKey:
//             return false;
//             break;
//
//     }
// })

function changeItem(obj) {
    if ($(obj).attr('tabindex')) {  //这里加这个判断是为了不影响之前已经写好的table组件  后面将所有table组件改成新的之后要把这个if判断以及if后面的东西删掉 只留下执行方法
        var tableView = $(obj).parents('.zui-table-view')
        tableBodyArray = $('.zui-table-body', tableView),
            tableBodyArrayLength = tableBodyArray.length;
        for (var i = 0; i < tableBodyArrayLength; i++) {
            var trArray = $('tr', tableBodyArray[i]),
                tr = trArray[$(obj).attr('tabindex')];
            $('.table-hovers', tableBodyArray[i]).removeClass('table-hovers');
            $(tr).addClass('table-hovers');
        }
        return;
    }
    var t = obj != undefined ? obj : objArrtb;
    var rowsLength = $(t).parent('tbody').find('tr')
    var rows = $(t).parent().parents('.zui-table-view')
    currentLinetb = currentLinetb > $(rowsLength).length ? 0 : currentLinetb <= -1 ? 0 : currentLinetb
    $(rows).find(".table-fixed-l .zui-table-body .zui-table  tr").siblings().removeClass('table-hovers table-hovers-filexd-l');
    $(rows).find(".table-fixed-r .zui-table-body .zui-table  tr").siblings().removeClass('table-hovers table-hovers-filexd-r');
    $(t).parent('tbody').find('tr.table-hovers').removeClass('table-hovers');
    $(rows).find(".table-fixed-r .zui-table-body .zui-table  tr").siblings().find('td').removeClass('table-hovers-filexd-r-child');
    $(rows).find(".table-fixed-l .zui-table-body .zui-table  tr").eq(currentLinetb).addClass('table-hovers table-hovers-filexd-l');
    $(rows).find(".table-fixed-r .zui-table-body .zui-table  tr").eq(currentLinetb).addClass('table-hovers table-hovers-filexd-r');
    $(rows).find(".table-fixed-r .zui-table-body .zui-table  tr").eq(currentLinetb).find('td').addClass('table-hovers-filexd-r-child');
    $(t).parent('tbody').find('tr').eq(currentLinetb).addClass("table-hovers");
}

function shenhe(obj) {
    var that = $(obj).find("input[type='hidden']").val();
    var inputs = $(obj).parents('tr').find($("input[name='edit']"));

    // alert(that);
    if (that == 0) {
        inputs.each(function () {
            $(this).attr("disabled", false)
            $(this).addClass("disableds");
        });
        $(obj).find("input[type='hidden']").val(1);
        $(obj).addClass('icon-hs-hover');
    } else {
        inputs.each(function () {
            $(this).attr("disabled", false)
            $(this).removeClass("disableds");
        });
        $(obj).find("input[type='hidden']").val(0);
        $(obj).removeClass('icon-hs-hover');
    }


}

function baocun() {
    // alert(23565);
    var inputs = $("input[name='edit']");
    inputs.each(function () {
        $(this).attr("disabled", true)
        $(this).removeClass("disableds");

    });
}

$(window).resize(function () {
    conhover(objArr)
    changeWin();
    changeW();
    topcss()
})

function conhover(obj) {
    currentLinetb = $(obj).index();
    // if( !ifLoop ){
    changeItem(obj);
    // }
    objArrtb = obj;
    $(obj).addClass('table-hovers');
    $(obj).siblings().removeClass('table-hovers');
    objArrtb.onkeydown = function (e) {
        switch (e.keyCode) {
            case 38:
                flag = true;
                // changeItem();
                currentLinetb = currentLinetb - 1;
                changeItem();
                break;
            case 40:
                flag = false;
                currentLinetb = currentLinetb + 1;
                changeItem();
                break;
            case  116:
                e.keyCode = 0;
                e.preventDefault(); //阻止默认刷新
                window.location.reload();
                return false;
                break;
            // case  83 :
            //     return false
            // break;
            case e.ctrlKey:
                return false;
                break;

        }
    }
}

function changeWin() {
    var tablebodyy = $('.zui-table-body');
    for (var i = 0; i < tablebodyy.length; i++) {
        if ($(tablebodyy[i]).offset().top > 0 && $(tablebodyy[i]).attr('data-no-change') === undefined) { //如果有data-no-change属性则不计算
            $(tablebodyy[i]).attr('data-no-change');
            var zuiheight = $('body').outerHeight() - $(tablebodyy).eq(i).offset().top - 10;
            if ($(tablebodyy).eq(i).offset().top < $('body').outerHeight()) {
                if ( $(tablebodyy).eq(i).parent('.zui-table-view').find('.zui-table-tool')) {
                    zuiheight = zuiheight - $(tablebodyy).eq(i).parent('.zui-table-view').find('.zui-table-tool').outerHeight()
                }
                if ($('.action-bar.fixed')&&!$('.action-bar.fixed').find('.side-form')) {
                    zuiheight = zuiheight - $('.action-bar.fixed').outerHeight()
                }
                if ($(tablebodyy).eq(i).parent('.zui-table-fixed').length > 0) { // 给浮动的区域减去滚动条宽度！防止滚动到最底时错位情况发生
                    zuiheight -= 6;
                }
                if (!$(tablebodyy[i]).attr('data-no-attr')) {
                    if ($('.addList')) {
                        zuiheight = zuiheight - $('.addList').outerHeight()
                    }
                }
                $(tablebodyy[i]).css({
                    'height': zuiheight,
                    'overflow-y': 'auto'
                });
            } else {
                $(tablebodyy[i]).css({
                    'overflow-y': 'auto'
                });
            }

        }
    }
    //判断滚动区域横向滚动条是否出现
    var tabBodys = $('.zui-table-view > .zui-table-header');
    for (var x = 0; x < tabBodys.length; x++) {
        var table = $('table', tabBodys[x]);
        if ($(tabBodys[x]).width() < table.width()) {
            $(tabBodys[x]).siblings(".zui-table-fixed").show();
        } else {
            $(tabBodys[x]).siblings(".zui-table-fixed").hide();
        }
    }
}

function changeW() {
    var contentList = $('.content-right-list');
    if (contentList.length != 0) {
        var zuiheight = $('body').outerHeight() - $(contentList).offset().top;
        if ($('.zui-table-tool')) {
            zuiheight = zuiheight - $('.zui-table-tool').outerHeight()
        }
        $('.content-right-list').css({
            'height': zuiheight,
        });
    }
}

setTimeout(function () {
    changeWin();
    changeW();
}, 300)