var dic_transform = {
    data: {
        /*状态*/
        stopSign: {
            "0": "启用",
            "1": "停用"
        },
        /*是or否*/
        istrue_tran: {
            "1": "是",
            "0": "否"
        },
        /*病案是or否*/
        isbatrue_tran: {
            "1": "是",
            "2": "否"
        },
        //病案有无
        isbaYesNo_tran: {
            "1": "无",
            "2": "有"
        },
        /*审核状态*/
        isshzh_tran: {
            "0": "未审",
            "1": "已审",
            "2": "作废"
        },
        /*科室类型*/
        kslx_tran: {
            "1": "临床",
            "2": "药剂",
            "3": "医技",
            "4": "行政",
            "5": "后勤",
            "9": "其他"
        },
        /*重点科室标志*/
        zdksbz_tran: {
            "1": "部级重点",
            "2": "省级重点",
            "3": "市级重点",
            "4": "院内重点 ",
            "5": "普通科室"
        },
        /*病人性别GB/2261.1-2003*/
        brxb_tran: {
            "1": "男",
            "2": "女",
            "9": "未知"
        },
        tbsm_tran: {
            'null': '无',
            '入院': '入院',
            '出院': '出院',
            '手术': '手术',
            '分娩': '分娩',
            '出生': '出生',
            '请假': '请假',
            '返回': '返回',
            '检查': '检查',
            '转入': '转入',
            '转出': '转出',
            '死亡': '死亡'
        },
        wcyy_tran: {
            'null': '无',
            '拒测': '拒测',
            '请假': '请假',
            '外出': '外出',
            '手术': '手术',
        },
        /*政治面貌 GB/T4762-1984政治面貌代码*/
        zzmm_tran: {
            "01": "中共党员",
            "02": "中共预备党员",
            "03": "共青团员",
            "04": "民革会员",
            "05": "民盟盟员",
            "06": "民建会员",
            "07": "民进会员",
            "08": "农工党党员",
            "09": "致公党党员",
            "10": "九三学社社员",
            "11": "台盟盟员",
            "12": "无党派民主人士",
            "13": "群众"
        },
        /*患者人员身份 GB/T2261.4-2003从业状况（个人身份）代码*/
        hzrysf_tran: {
            "11": "国家公务员",
            "13": "专业技术人员",
            "17": "职员",
            "21": "企业管理人员",
            "24": "工人",
            "27": "农民",
            "31": "学生",
            "37": "现役军人",
            "51": "自由职业者",
            "54": "个体经营者",
            "70": "无业人员",
            "80": "退(离)休人员",
            "90": "其他"
        },
        /*学位代码 GB/T6864-2003*/
        xwdm_tran: {
            "1": "名誉博士",
            "2": "博士",
            "3": "硕士",
            "4": "学士"
        },
        /*执业类别代码*/
        zylb_tran: {
            "1": "临床",
            "2": "口腔",
            "3": "公共卫生",
            "4": "中医"
        },
        /*管理职务代码*/
        glzwdm_tran: {
            "1": "党委（副）书记",
            "2": "院（所、站）长",
            "3": "副院（所、站）长",
            "4": "科室主任",
            "5": "科室副主任"
        },
        /*身份证件类别代码 CV02.01.101*/
        sfzjlx_tran:{
            "1": "居民身份证",
            "2": "居民户口簿",
            "3": "护照",
            "4": "军官证",
            "5": "驾驶证",
            "6": "港澳居民来往内地通行证",
            "7": "台湾居民来往内地通行证",
            "19": "母亲身份证",
            "99": "其他法定有效证件"
        },
        /*编制情况*/
        bzqk_tran: {
            "1": "编制内",
            "2": "合同制",
            "3": "临聘人员",
            "4": "返聘"
        },
        /*人员身份*/
        rysf_tran: {
            "01": "在职",
            "02": "退休",
            "03": "离休",
            "04": "其他"
        },
        /*患者类型代码表 CV09.00.404*/
        hzlx_tran: {
            "1": "门诊",
            "2": "急诊",
            "3": "住院",
            "4": "其他"
        },
        /*统筹类别*/
        tclb_tran: {
            "1": "甲类",
            "2": "乙类",
            "3": "丙类"
        },
        /*药品出入方式*/
        ypcrfs_tran: {
            "01": "入库",
            "02": "退库",
            "03": "盘盈入库",
            "04": "出库",
            "05": "退货",
            "06": "盘亏出库",
            "07": "报损出库"
        },
        /*年龄单位代码 CV02.03.01*/
        nldw_tran: {
            "1": "岁",
            "2": "月",
            "3": "周",
            "4": "天"
        },
        //新生年龄
        newnldw_tran:{
            "1": "小时",
            "2": "天",
            "3": "分钟",
        },
        /*机构分类管理类型*/
        jgflgllx_tran: {
            "1": "非营利性医疗机构",
            "2": "营利性医疗机构",
            "9": "其他卫生机构"
        },
        /*机构主办单位*/
        jgzbdw_tran: {
            "11": "卫生行政部门",
            "12": "其他行政部门",
            "13": "企业",
            "14": "事业单位",
            "15": "社会团体",
            "16": "其他社会组织",
            "17": "个人",
            "21": "村办",
            "22": "乡卫生院设点",
            "23": "联合办",
            "24": "私人办",
            "29": "其他"
        },
        /*机构政府办隶属关系*/
        jgzfdsgx_tran: {
            "1": "中央属",
            "2": "省、自治区、直辖市属",
            "3": "省辖市(地区、州、盟、直辖市区)属",
            "4": "县级市(省辖市区)属",
            "5": "县(旗)属",
            "6": "街道属",
            "7": "镇属",
            "8": "乡属"
        },
        /*机构级别*/
        jgjb_tran: {
            "1": "一级",
            "2": "二级",
            "3": "三级",
            "9": "未定级"
        },
        /*机构等次*/
        jgdj_tran: {
            "1": "甲等",
            "2": "乙等",
            "3": "丙等",
            "9": "未定等"
        },
        /*行医方式*/
        xyfs_tran: {
            "1": "西医为主",
            "2": "中医为主",
            "3": "中西医结合",
            "4": "民族医"
        },
        /*管理类别*/
        gllb_tran: {
            "1": "市直属医院",
            "2": "区直属医院",
            "3": "企事业医院",
            "4": "部队医院",
            "5": "民营医院"
        },
        /*医院性质*/
        yyxz_tran: {
            "1": "公立",
            "2": "非公立"
        },
        /*统筹类别*/
        tclb_tran: {
            "1": "甲类",
            "2": "乙类",
            "3": "自费"
        },

        /*病人类型*/
        brlx_tran: {
            "0": "门诊",
            "1": "住院"
        },

        /*医嘱检查分类*/
        yzjcfl_tran: {
            "0": "无",
            "1": "检查",
            "2": "检验",
            "3": "治疗",
            "4": "手术",
            "5": "会诊",
            "6": "输血",
            "7": "护理"
        },
        /*医嘱分类*/
        yzfl_tran: {
            "0": "普通",
            "1": "术前",
            "2": "术中",
            "3": "术后",
            "4": "转科",
            "5": "出院",
            "6": "自带药",
            "7": "转入",
            "8": "领药",
            "9": "产后"
        },
        /*护理等级CV06.00.220*/
        hldj_tran: {
            "0": "无",
            "1": "特级",
            "2": "一级",
            "3": "二级",
            "4": "三级"
        },

        /*处方类别*/
        cflb_tran: {
            "1": "西医",
            "2": "中医",
            "3": "中西医"
        },

        /*其他支付项目*/
        qtzfxm_tran: {
            "01": "其他支付1",
            "02": "其他支付2",
            "03": "其他支付3",
            "04": "其他支付4",
            "05": "其他支付5",
            "06": "其他支付6",
            "07": "其他支付7",
            "08": "其他支付8",
            "09": "其他支付9",
            "10": "其他支付10",
            "11": "其他支付11",
            "12": "其他支付12",
            "13": "其他支付13",
            "14": "其他支付14",
            "15": "其他支付15",
            "16": "其他支付16",
            "17": "其他支付17",
            "18": "其他支付18",
            "19": "其他支付19",
            "20": "其他支付20"

        },

        /*医疗卡类型*/
        ylklx_tran: {
            "00": "社保卡",
            "01": "医保卡",
            "02": "统一自费卡",
            "03": "非统一自费卡",
            "05": "新农合卡",
            "06": "健康卡",
            "07": "医疗机构内部就诊卡",
            "08": "省管干部和在编军人卡",
            "09": "医疗机构内部患者唯一索引码",
            "10": "医院联盟就诊卡",
            "20": "区域就诊卡",
            "21": "区域联盟就诊卡",
            "88": "身份证",
            "99": "其他卡"
        },
        /*执行单分类别*/
        zxdfl_tran: {
            "0": "无",
            "1": "口服",
            "2": "注射",
            "3": "输液",
            "4": "护理",
            "5": "治疗"
        },
        /*接转科类型*/
        jzklx_tran: {
            "0": "新",
            "1": "转"
        },
        /*联系电话类别代码*/
        lxrdhlx_tran: {
            "01": "本人电话",
            "02": "配偶电话",
            "03": "监护人电话",
            "04": "家庭电话",
            "05": "本人工作单位电话",
            "06": "居委会电话",
            "99": "其他"
        },
        /*病情情况,取医嘱信息*/
        bqdj_tran: {
            "0": "一般",
            "1": "病重",
            "2": "病危",
            "3": "死亡"
        },
        /*医嘱类型*/
        yzlx_tran: {
            "1": "长期",
            "0": "临时"
        },
        /*医嘱类型*/
        yzlx_tran002: {
            "0": "长期",
            "1": "临时"
        },
        /*医嘱类型*/
        yzlx_tran01: {
            "2": "全部",
            "1": "长期",
            "0": "临时"
        },

        /*医嘱类型*/
        yzlx_tran02: {
            "0": "全部",
            "1": "长期",
            "2": "临时"
        },
        /*费用类型*/
        fylx_tran: {
            "0": "公用",
            "1": "门诊",
            "2": "住院",
        },
        /*费用类别*/
        fylb_tran: {
            "1": "挂号费用",
            "2": "药品费用",
            "3": "检查费用",
            "4": "检验费用",
            "5": "治疗费用"
        },
        /*药品费用*/
        ypfy_tran: {
            "0": "非药品",
            "1": "药品"
        },
        /*是否组合费用*/
        sfzhfy_tran: {
            "0": "一般费用",
            "1": "组合费用"
        },
        /*入库方式*/
        rkfs_tran: {
            "01": "入库",
            "02": "退库",
            "03": "盘点入库"
        },
        /*出库方式*/
        ckfs_tran: {
            "01": "出库",
            "02": "退货",
            "03": "报损",
            "04": "盘亏"
        },
        //入院患者类型
        ryhzlx_tran: {
            "0": "本地人员",
            "1": "外地人员",
            "2": "境外人员",
            "9": "其他"
        },

        //入院情况
        ryqk_tran: {
            "1": "危",
            "2": "急",
            "3": "一般"
        },

        //入院途径
        rytj_tran: {
            "1": "门诊",
            "2": "急诊",
            "3": "转院"
        },

        //操作员标志
        czybz_tran: {
            "0": "非操作员",
            "1": "操作员"
        },
        //医师标志
        ysbz_tran: {
            "0": "非医师",
            "1": "医师"
        },
        //护士标志
        hsbz_tran: {
            "0": "非护士",
            "1": "护士"
        },
        //一级科目类型
        yjkmlx_tran: {
            "0": "医疗",
            "1": "药品"
        },

        //药库使用类型
        yksylx_tran: {
            "0": "药品",
            "1": "医用材料"
        },

        //药房四舍五入至
        sswrz_tran: {
            "0": "无",
            "1": "分",
            "2": "角",
            "3": "元"
        },
        //号别收费项目的费用类型
        hbsffylx_tran: {
            "0": "挂号费",
            "1": "诊疗费"
        },
        //执行单类型
        zxdlx_tran: {
            "0": "无",
            "1": "口服",
            "2": "注射",
            "3": "输液",
            "4": "护理",
            "5": "治疗"
        },
        //执行单类型
        zxdlx_tran2: {
            "all": "全部",
            "kf": "口服",
            "zs": "注射",
            "sy": "输液",
            "hl": "护理",
            "zl": "治疗",
            "syt":"输液贴",
        },
        //门诊附加方式
        fjfsmz_tran: {
            "0": "不附加",
            "1": "按天计算(天数*数量*单价)",
            "2": "输液费附加",
            "3": "按频次附加",
            "4": "按组附加"
        },
        //住院附加方式
        fjfszy_tran: {
            "0": "不附加",
            "1": "按天计算(天数*数量*单价)",
            "2": "输液费附加",
            "3": "按频次附加",
            "4": "按组附加"
        },
        //附加项目类型
        fjxmlx_tran: {
            "1": "材料",
            "2": "药品"
        },
        //病案相关
        //手术级别代码表	CV05.10.024
        ssjb_tran: {
            "1": "一级手术",
            "2": "二级手术",
            "3": "三级手术",
            "4": "四级手术"
        },
        //手术切口类别代码表 CV05.10.022
        ssqklb_tran: {
            "1": "0类切口",
            "2": "I类切口",
            "3": "Ⅱ类切口",
            "4": "Ⅲ类切口"
        },
        //手术切口愈合等级代码表  CV05.10.023
        ssqkdj_tran: {
            "1": "甲",
            "2": "乙",
            "3": "丙",
            "4": "其他"
        },
        //离院方式  CV06.00.226
        lyfs_tran: {
            "1": "医嘱离院",
            "2": "医嘱转院",
            "3": "医嘱转社区卫生服务机构/乡镇卫生院",
            "4": "非医嘱离院",
            "5": "死亡",
            "9": "其他"
        },
        //输液反应
        syfy_tran: {
            "0": "未输",
            "1": "无",
            "2": "有"
        },
        //变异原因 CV06.00.999
        lcljbyyy_tran: {
            "1": "系统原因",
            "12": "服务人员因素",
            "13": "病人因素",
            "21": "正性变异",
            "22": "负性变异",
            "31": "可控变异",
            "32": "不可控变异",
            "40": "其他退出"
        },
        //入院病情代码
        rybqdm_tran: {
            "1": "有",
            "2": "临床未确定",
            "3": "情况不明",
            "4": "无"
        },
        //出院情况
        cyqk_tran: {
            "1": "治愈",
            "2": "好转",
            "3": "未愈",
            "4": "死亡",
            "5": "其他"
        },
        //血型编码
        xxbm_tran: {
            "1": "A型",
            "2": "B型",
            "3": "O型",
            "4": "AB型",
            "5": "不详",
            "6": "未查"
        },
        //TNM分期
        tnmfq_tran: {
            "1": "Ⅰ期",
            "2": "Ⅱ期",
            "3": "Ⅲ期",
            "4": "Ⅳ期"
        },
        //RH血型
        rhxx_tran: {
            "1": "阴",
            "2": "阳",
            "3": "不详",
            "4": "未查"
        },
        //病案质量
        bazl_tran: {
            "1": "甲",
            "2": "乙",
            "3": "丙"
        },
        //行政区划类型
        xzqhlx_tran: {
            "1": "省",
            "2": "市",
            "3": "县",
            "4": "镇"
        },

        //操作员标志
        czybz_tran: {
            "0": "非操作员",
            "1": "操作员"
        },
        //医师标志
        ysbz_tran: {
            "0": "非医师",
            "1": "医师"
        },
        //护士标志
        hsbz_tran: {
            "0": "非护士",
            "1": "护士"
        },
        //一级科目类型
        yjkmlx_tran: {
            "0": "医疗",
            "1": "药品"
        },

        //药库使用类型
        yksylx_tran: {
            "0": "药品",
            "1": "医用材料"
        },
        //号别收费项目的费用类型
        hbsffylx_tran: {
            "0": "挂号费",
            "1": "诊疗费"
        },
        //执行单类型
        zxdlx_tran: {
            "0": "无",
            "1": "口服",
            "2": "注射",
            "3": "输液",
            "4": "护理",
            "5": "治疗"
        },
        //门诊附加方式
        fjfsmz_tran: {
            "0": "不附加",
            "1": "按天计算(天数*数量*单价)",
            "2": "输液费附加",
            "3": "按频次附加",
            "4": "按组附加"
        },
        //住院附加方式
        fjfszy_tran: {
            "0": "不附加",
            "1": "按天计算(天数*数量*单价)",
            "2": "输液费附加",
            "3": "按频次附加",
            "4": "按组附加"
        },
        //附加项目类型
        fjxmlx_tran: {
            "1": "材料",
            "2": "药品"
        },
        //手术级别代码表	CV05.10.024
        ssjb_tran: {
            "1": "一级手术",
            "2": "二级手术",
            "3": "三级手术",
            "4": "四级手术"
        },
        //临床路径实施标志
        lcljssbz_tran: {
            "1": "中医",
            "2": "西医",
            "3": "否"
        },
        //操作员标志
        czybz_tran: {
            "0": "非操作员",
            "1": "操作员"
        },
        //医师标志
        ysbz_tran: {
            "0": "非医师",
            "1": "医师"
        },
        //护士标志
        hsbz_tran: {
            "0": "非护士",
            "1": "护士"
        },
        //一级科目类型
        yjkmlx_tran: {
            "0": "医疗",
            "1": "药品"
        },

        //药库使用类型
        yksylx_tran: {
            "0": "药品",
            "1": "医用材料"
        },
        //号别收费项目的费用类型
        hbsffylx_tran: {
            "0": "挂号费",
            "1": "诊疗费"
        },
        //执行单类型
        zxdlx_tran: {
            "0": "无",
            "1": "口服",
            "2": "注射",
            "3": "输液",
            "4": "护理",
            "5": "治疗"
        },
        //门诊附加方式
        fjfsmz_tran: {
            "0": "不附加",
            "1": "按天计算(天数*数量*单价)",
            "2": "输液费附加",
            "3": "按频次附加",
            "4": "按组附加"
        },
        //住院附加方式
        fjfszy_tran: {
            "0": "不附加",
            "1": "按天计算(天数*数量*单价)",
            "2": "输液费附加",
            "3": "按频次附加",
            "4": "按组附加"
        },
        //附加项目类型
        fjxmlx_tran: {
            "1": "材料",
            "2": "药品"
        },
        //手术级别代码表	CV05.10.024
        ssjb_tran: {
            "1": "一级手术",
            "2": "二级手术",
            "3": "三级手术",
            "4": "四级手术"
        },
        // 药品类型
        yplx_tran: {
            "0": "西药",
            "1": "中成药",
            "2": "中草药",
            "3": "材料",
            "4": "其他",
        },
        //治疗类别
        zllb_tran: {
            "1": "中医",
            "1.1": "中医",
            "1.2": "民族医",
            "2": "中西医",
            "3": "西医"
        },
        //诊断符合率
        zdfhqk_tran: {
            "0": "未做",
            "1": "符合",
            "2": "不符合",
            "3": "不确定"
        },
        //检查情况
        jcqk_tran: {
            "1": "阳性",
            "2": "阳性",
            "3": "未做"
        },
        //抗菌药物使用情况
        kjywsyqk_tran: {
            "1": "I联",
            "2": "Ⅱ联",
            "3": "Ⅲ联",
            "4": "Ⅳ联"
        },
        //住院有无跌倒或坠床及伤害程度:
        ddzcshcd_tran: {
            "1": "一级",
            "2": "二级",
            "3": "三级",
            "4": "未造成伤害",
            "5": "无"
        },
        //跌倒或坠床的原因
        ddzcyy_tran: {
            "1": "健康原因",
            "2": "治疗、药物、麻醉原因",
            "3": "环境因素",
            "9": "其他原因"
        },
        //抗生素级别
        kssjb_tran: {
            "1": "非抗生素",
            "2": "非限制使用抗生素",
            "3": "限制使用抗生素",
            "4": "特殊使用抗生素"
        },
        //临床路径相关
        //路径病例分型
        ljblfx_tran: {
            "1": "单纯普通型",
            "2": "单纯急症型",
            "3": "复杂疑难型",
            "4": "复杂危重型"
        },
        //路径病种适用病情
        ljbzsybq_tran: {
            "0": "不区分",
            "1": "危",
            "2": "重",
            "3": "一般"
        },
        //路径病种分类
        ljbzfl_tran: {
            "0": "卫生部",
            "1": "院内"
        },
        //路径病种支付方式
        ljbzzffs_tran: {
            "0": "定额",
            "1": "限额",
            "2": "普通"
        },
        //路径标准阶段日
        ljbzjd_tran: {
            "0": "住院日",
            "1": "手术日",
            "2": "分娩日",
            "3": "出院日"
        },
        //路径病种适用性别
        ljbzsysb_tran: {
            "0": "不区分",
            "1": "男",
            "2": "女"
        },
        //路径阶段时间单位
        ljdwsjdw_tran: {
            "1": "天",
            "2": "小时",
            "3": "分钟"
        },
        //路径表单项目大类
        ljbdxmdl_tran: {
            "0": "主要诊疗工作",
            "1": "重点医嘱",
            "2": "主要护理工作"
        },
        //路径表单项目类型
        ljbdxmlx_tran: {
            "0": "医嘱类",
            "1": "病历类",
            "2": "其他类"
        },
        //路径执行方式
        ljbdzxfs_tran: {
            "1": "无需执行",
            "2": "每天执行",
            "3": "至少执行一次同一北阶段",
            "4": "必要时执行"
        },
        //路径表单选择方式
        ljbdxzfs_tran: {
            "0": "可选",
            "1": "必选"
        },
        //医疗卡锁状态
        lockzt_tran: {
            "0": "允许使用",
            "1": "不允许使用"
        },
        //中医疾病类型
        bazyjblx_tran: {
            "1": "中医证候",
            "2": "中医病证"
        },
        //体温部位
        twbw_tran: {
            "1": "腋温",
            "2": "口温",
            "3": "肛温"
        },
        //人工肛门
        rygm_tran: {
            "0": "否",
            "1": "人工肛门",
            "2": "大便失禁"
        },
        //灌肠
        gc_tran: {
            "0": "否",
            "1": "是",
            "2": "二次灌肠"
        },
        //生产方式
        scfs_tran: {
            "1": "顺产",
            "2": "剖腹产",
            "4": "加胎吸助产",
            "5": "臀位助娩",
            "6": "头位难产",
            "7": "产钳助产"
        },
        //单位类型
        dwlx_tran: {
            "0": "药品",
            "1": "物资",
            "3": "耗材"
        },
        //临床路径病种适用性别
        lcljsyxb_tran: {
            "0": "不区分",
            "1": "男",
            "2": "女"
        },
        //门诊医生分组号
        fzh_tran: {
            "0": "0",
            "1": "1",
            "2": "2",
            "3": "3",
            "4": "4",
            "5": "5",
            "6": "6",
            "7": "7",
            "8": "8",
            "9": "9",
            "10": "10"
        },
        //票据类型
        pjlx_tran: {
            "01": "门诊挂号发票",
            "02": "门诊收费发票",
            "03": "门诊预交发票",
            "04": "住院记帐票",
            "05": "住院预交票",
            "06": "住院结算票",
            "07": "门诊收据发票"
        },
        //领票方式
        lpfs_tran: {
            "0": "个人",
            "1": "工作站"
        },
        //票据状态
        pjzt_tran: {
            "0": "待用",
            "1": "正用",
            "2": "已用",
            "3": "作废",
            "4": '消号'
        },
        //组合医嘱类型
        zhyzlx_tran: {
            "0": "全院",
            "1": "个人",
            "2": "科室"
        },
        //组合医嘱中类型‘
        zhyzBylx_tran: {
            "1": "门诊",
            "2": "住院"
        },
        //输液速度
        sydw_tran: {
            "滴/分": "滴/分",
            "毫升/小时": "毫升/小时",
            "gtt/min": "gtt/min"
        },
        fpfs_tran: {
            "0": "一费一张",
            "1": "执行科室分票",
            "2": "指定科室分票"
        },
        pc_tran: {
            "1": "一天一次",
            "2": "一天二次",
            "3": "一天三次"
        },
        nhtclb_tran: {
            "0": "保外",
            "1": "保内"
        },
        mznh_tran: {
            "1": "普通门诊",
            "2": "意外伤害（无责）",
            "3": "意外伤害（有责）",
            "4": "大型门诊检查",
            "5": "大型门诊补偿",
            "6": "合医办二次补偿",
            "7": "门诊疫苗",
            "8": "慢性病补偿",
            "9": "HIV筛查",
            "A0": "重大疾病普通类型",
            "A1": "重大疾病自动出院",
            "A2": "重大疾病自动转院",
            "A3": "重大疾病死亡",
            "A4": "门诊留管",
            "A5": "门诊急诊",
            "A6": "病理口腔",
            "A7": "门诊输液",
            "A8": "门诊输血"
        },
        gznhry_tran: {
            "0": "普通住院",
            "1": "儿童住院",
            "11": "大病住院",
            "12": "上级转下级医疗机构起付县优惠",
            "13": "乡级转县级优惠",
            "14": "乡级转市级优惠",
            "15": "乡级转市外优惠",
            "16": "县级转市级优惠",
            "17": "县级转市外优惠",
            "18": "市级转市外优惠",
            "19": "意外伤害上级转下级医疗机构起付县优惠",
            "2": "意外伤害（无责）",
            "21": "意外伤害乡级转县级优惠",
            "22": "意外伤害乡级转市级优惠",
            "23": "意外伤害乡级转市外优惠",
            "24": "意外伤害县级转市级优惠",
            "25": "意外伤害县级转市外优惠",
            "26": "意外伤害市级转市外优惠",
            "27": "艾滋病患者",
            "28": "麻风病患者",
            "29": "精神病患者",
            "3": "意外伤害（有责）",
            "31": "下级转上级",
            "32": "意外伤害（车祸）",
            "33": "乡级I类—上级",
            "34": "乡级II类—上级",
            "35": "县级I类—上级",
            "36": "县级II类—上级",
            "37": "县外市内—上级",
            "38": "市外省内—上级",
            "39": "普通住院上级-下级",
            "4": "意外伤害（责任不明确）",
            "41": "意外伤害无责乡级I类—上级",
            "42": "意外伤害无责乡级II类—上级",
            "43": "意外伤害无责县级I类—上级",
            "44": "意外伤害无责县级II类—上级",
            "45": "意外伤害无责市外省内—上级",
            "46": "意外伤害无责上级-下级",
            "47": "意外伤害有责上级-下级",
            "48": "意外伤害有责乡级I类—上级",
            "49": "意外伤害有责乡级II类—上级",
            "5": "大型门诊检查",
            "50": "意外伤害有责县级I类—上级",
            "51": "意外伤害有责县级II类—上级",
            "52": "意外伤害有责县外市内—上级",
            "53": "意外伤害有责市外省内—上级",
            "54": "新生儿住院",
            "55": "县级特殊补偿",
            "56": "乡级转省外优惠",
            "57": "县级转省外优惠",
            "58": "市外转省外优惠",
            "6": "合医办二次补偿",
            "7": "平产分娩",
            "8": "难产",
            "9": "剖腹产/康复科",
            "A0": "重大疾病普通类型",
            "A1": "重大疾病自动出院",
            "A2": "重大疾病自动转院",
            "A3": "重大疾病死亡",
            "A4": "单病种患者",
            "A5": "起付线优惠减免人员",
            "A6": "外伤单病种患者"
        },
        gznhcy_tran: {
            "0": "普通住院",
            "1": "儿童住院",
            "11": "大病住院",
            "12": "上级转下级医疗机构起付县优惠",
            "13": "乡级转县级优惠",
            "14": "乡级转市级优惠",
            "15": "乡级转市外优惠",
            "16": "县级转市级优惠",
            "17": "县级转市外优惠",
            "18": "市级转市外优惠",
            "19": "意外伤害上级转下级医疗机构起付县优惠",
            "2": "意外伤害（无责）",
            "21": "意外伤害乡级转县级优惠",
            "22": "意外伤害乡级转市级优惠",
            "23": "意外伤害乡级转市外优惠",
            "24": "意外伤害县级转市级优惠",
            "25": "意外伤害县级转市外优惠",
            "26": "意外伤害市级转市外优惠",
            "27": "艾滋病患者",
            "28": "麻风病患者",
            "29": "精神病患者",
            "3": "意外伤害（有责）",
            "31": "下级转上级",
            "32": "意外伤害（车祸）",
            "33": "乡级I类—上级",
            "34": "乡级II类—上级",
            "35": "县级I类—上级",
            "36": "县级II类—上级",
            "37": "县外市内—上级",
            "38": "市外省内—上级",
            "39": "普通住院上级-下级",
            "4": "意外伤害（责任不明确）",
            "41": "意外伤害无责乡级I类—上级",
            "42": "意外伤害无责乡级II类—上级",
            "43": "意外伤害无责县级I类—上级",
            "44": "意外伤害无责县级II类—上级",
            "45": "意外伤害无责市外省内—上级",
            "46": "意外伤害无责上级-下级",
            "47": "意外伤害有责上级-下级",
            "48": "意外伤害有责乡级I类—上级",
            "49": "意外伤害有责乡级II类—上级",
            "5": "大型门诊检查",
            "50": "意外伤害有责县级I类—上级",
            "51": "意外伤害有责县级II类—上级",
            "52": "意外伤害有责县外市内—上级",
            "53": "意外伤害有责市外省内—上级",
            "54": "新生儿住院",
            "55": "县级特殊补偿",
            "56": "乡级转省外优惠",
            "57": "县级转省外优惠",
            "58": "市外转省外优惠",
            "6": "合医办二次补偿",
            "7": "平产分娩",
            "8": "难产",
            "9": "剖腹产/康复科",
            "A0": "重大疾病普通类型",
            "A1": "重大疾病自动出院",
            "A2": "重大疾病自动转院",
            "A3": "重大疾病死亡",
            "A4": "单病种患者",
            "A5": "起付线优惠减免人员",
            "A6": "外伤单病种患者"
        },
        gznhryqk_tran: {
            "1": "危",
            "2": "急",
            "3": "一般",
            "9": "其他"
        },
        gznhcyqk_tran: {
            "1": "治愈",
            "2": "好转",
            "3": "未愈",
            "4": "死亡",
            "9": "其他"
        },
        zyzt_tran: {
            '0': '在院',
            '1': '出院'
        },
        cfz_tran: {
            '0': '初诊',
            '1': '复诊'
        },
        psjg_tran: {
            "0": '阴性',
            "1": '阳性'
        },
        psjg2_tran: {
            "0": '-',
            "1": '+'
        },
        nhrysx_tran: {
            '6': '特殊户',
            '7': '计生户',
            '5': '特困户',
            '1': '一般农户',
            '2': '五保户',
            '3': '贫困户',
            '4': '烈军属',
            '9': '其他',
            '8': '残疾人',
            '11': '二女绝育户',
            '10': '独生女儿户',
            '12': '独生子女户',
            '13': '重点优抚人员',
            '14': '低保户',
            '15': '精简人员',
            '17': '孤儿',
            '23': '特教学生',
            '24': 'HIV患者',
            '25': '麻风病人',
            '26': '精神病人',
            '27': '20世纪60年精减老职工',
            '28': '低收入重病患者',
            '29': '自然灾害人群',
            '30': '临、突发性困难人群',
            '31': '无力承担的患者自付费用过高的患者',
            '32': '三无人员',
            '33': '精准扶贫80岁以上',
            '34': '抚恤优抚对象',
            '35': '城乡低保对象',
            '36': '其他特殊困难人群',
            '41': '计生“两户”家庭成员',
            '42': '精准扶贫建档立卡贫困人口中的重大疾病患者',
            '43': '特困供养人员',
            '44': '最低生活保障对象',
            '45': '家庭经济困难的精神障碍患者、肇事肇祸的精神病障碍患者',
            '46': '低收入家庭中的重症患者、重度残疾人及老年人',
            '47': '失独家庭',
            '49': '易地扶贫搬迁移民',
            '18': '重度残疾人I级',
            '19': '重度残疾人II级',
            '37': '抗美援朝老复员军人',
            '40': '重度残疾人',
            '48': '精准扶贫建档立卡人员',
            '38': '一类低保户',
            '39': '二类低保户',
            '16': '被征地农户',
            '20': '两参人员',
            '21': '复员退伍军人',
            '22': '医疗保障对象',
            '51': '建档立卡贫困人口（已脱贫）',
            '50': '健康扶贫保障对象'
        },
        jydjlx_tran: {//类型:0-门诊,1-住院,2-体检,3-免费
            '0': '门诊',
            '1': '住院',
            '2': '体检',
            '3': '免费'
        },
        jydjly_tran: {//检验登记来源_yq
            '0': '门诊明细',
            '1': '门诊组合',
            '2': '住院医嘱',
            '3': '住院收费',
            '4': '体检'
        },
        jydjyblx_tran: {//样本类型：N-常规，Q-急诊，F-质控
            'N': '常规',
            'Q': '急诊',
            'F': '质控'
        },
        jydjzklx_tran: {//质控类型
            '0': '低值',
            '1': '中值',
            '2': '高值',
            '3': '定性'
        },
        jydjjsyy_tran: {
            '样本量过少': '样本量过少',
            '样本污染': '样本污染',
            '样本不合格': '样本不合格',
            '样本疑稀释': '样本疑稀释',
            '样本量过多': '样本量过多',
            '样本有凝块': '样本有凝块',
            '样本抗凝剂选择错误': '样本抗凝剂选择错误',
            '样本类型错误': '样本类型错误',
            '样本容器错误': '样本容器错误',
        },
        zbbmsjlx_tran: {
            '1': '数值',
            '2': '文本',
            '3': '选择',
            '4': '计算'
        },
        zbbmcklx_tran: {
            '0': '未明确',
            '1': '通用',
            '2': '性别',
            '3': '年龄'
        },
        wjzpdfs_tran: {
            '0': '无',
            '1': '通用',
            '2': '性别',
            '3': '年龄',
            '4': '样本类型',
            '5': '样本类型+性别',
            '6': '样本类型+年龄'
        },

        jysbisfile_tran: {
            '1': '串口型',
            '2': '读文件',
            '3': '读数据库',
            '4': '网络型',
            '5': '其他方式',
            '6': '串口方式1',
            '7': '读文件1',
        },
        jysbisbin_tran: {
            '1': '服务器',
            '2': '客户端'
        },
        jysbbaudrate_tran: {
            '300': '300',
            '600': '600',
            '1200': '1200',
            '2400': '2400',
            '4800': '4800',
            '9600': '9600',
            '19200': '19200',
            '38400': '38400',
            '56000': '56000',
            '57600': '57600',
            '115200': '115200',
            '128000': '128000'
        },
        jysbstopbit_tran: {
            '1': '1',
            '1.5': '1.5',
            '2': '2'
        },
        jysbdatabit_tran: {
            '8': '8',
            '7': '7',
            '6': '6',
            '5': '5',
            '4': '4'
        },
        jysbflowcontrol_tran: {
            '无流控制(NONE)': '无流控制(NONE)',
            '允许发送/请求发送(CTS/RTS)': '允许发送/请求发送(CTS/RTS)',
            '允许发送/终端就绪(CTS/DTR)': '允许发送/终端就绪(CTS/DTR)',
            '仪器就绪/请求发送(DSR/RTS)': '仪器就绪/请求发送(DSR/RTS)',
            '设置就绪/终端就绪(DSR/DTR)': '设置就绪/终端就绪(DSR/DTR)',
            '软件流控制(XON/XOFF)': '软件流控制(XON/XOFF)'
        },
        jysboutbuffer_tran: {
            '64字节': '64字节',
            '128字节': '128字节',
            '256字节': '256字节',
            '512字节': '512字节',
            '1024字节': '1024字节',
            '2056字节': '2056字节',
            '4096字节': '4096字节',
            '8192字节': '8192字节',
            '16384字节': '16384字节',
            '32768字节': '32768字节',
            '65536字节': '65536字节'
        },
        jysbverify_tran: {
            '无(NONE)': '无(NONE)',
            '奇校验(ODD)': '奇校验(ODD)',
            '偶校验(EVEN)': '偶校验(EVEN)',
            '标志校验(MARK)': '标志校验(MARK)'
        },
        jysbstartchar_tran: {
            '': '无符号',
            '0': '0空白-NUL(0x00)',
            '1': '1标题开始-SOH(0x01)',
            '2': '2正文开始-STX(0x02)',
            '3': '3正文结束-ETX(0x03)',
            '4': '4传输结束-EOT(0x04)',
            '5': '5询问-ENQ(0X05)',
            '6': '6承认-ACK(0x06)',
            '7': '7告警-BEL(0X07)',
            '8': '8退格BS(0x08)',
            '9': '9横向列表HT(0x09)',
            '10': '10换行LF(0x10)',
            '11': '11垂直列表VT(0x11)',
            '12': '12换页FF(0x12)',
            '13': '13回车CR(0x13)',
            '14': '14移位输出SO(0x14)',
            '15': '15移位输出SI(0x15)',
            '16': '16转义-DLE(0x16)',
            '17': '17仪器控制1DC1(0x17)',
            '18': '18仪器控制2DC2(0x18)',
            '19': '19仪器控制3DC1(0x19)',
            '20': '20仪器控制4DC4(0x20)',
            '21': '21否认-NAK(0x21)',
            '22': '22空转同步-SYN(0x22)',
            '23': '23传送结束-ETB(0x23)',
            '24': '24作废-CAN(0x24)',
            '25': '25纸尽EM(0x25)',
            '26': '26取代-SUB(0x26)',
            '27': '27扩展-ESC(0x27)',
            '28': '28文字分隔符FS(0x28)',
            '29': '29组分隔符GS(0x29)',
            '30': '30记录分隔符RS(0x30)',
            '31': '31单元分隔符US(0x31)',
            '32': '32空格-SPACE(0x31)',

        },
        jysbybhscfs_tran: {
            '0': '样本号每天变化',
            '1': '样本号连续编码'
        },
        jysbybhws_tran: {
            '1': '连续流水号',
            '2': '等长2位',
            '3': '等长3位',
            '4': '等长4位',
            '5': '等长5位',
            '6': '等长6位',
            '7': '等长7位',
            '8': '等长8位',
            '9': '等长9位',
            '10': '等长10位',
            '11': '等长11位',
            '12': '等长12位'
        },
        jysbbgdygsfs_tran: {
            '0': '取仪器固定',
            '1': '取项目维护'
        },
        jysbfssbybhyz_tran: {
            '0': '一致',
            '1': '不一致'
        },
        xtwhnldw_tran: {
            '1': '岁',
            '2': '月',
            '3': '天',
            '4': '小时',
            '5': '分钟'
        },
        xtwhxb_tran: {
            '0': '通用',
            '1': '男性',
            '2': '女性'
        },
        zf_tran: {
            '0': '未',
            '1': '已'
        },
        hosly: {
            '0': 'His',
            '1': 'Lis'
        },
        //过滤功能参数----------
        //样本核收管理
        xmybgl_tran: {
            'LX': '类型',
            'BAH': '住院号/门诊号',
            'BRXM': '病员姓名',
            'XB': '性别',
            'KSBM': '科室',
            'SQYS': '医生',
            'NL': '年龄',
            'YBLX': '类别',
            'YBHSRQ': '核收时间',
            'FYMC': '扣费名称',
            'XZ': '选择'
        },
        tjybgl_tran: {
            '=': '等于',
            '!=': '不等于',
            //'':'左包含',
            //'':'右包含',
            //'':'全包含',
            '&gt;': '大于',
            '&gt;=': '大于等于',
            '&lt;': '小于',
            '&lt;=': '小于等于'
        },
        ljtjybgl_tran: {
            'and': '并且',
            'or': '或者'
        },
        tran_dataType: {
            '1': '数值',
            '2': '文本',
            '3': '选择',
            '4': '计算',
        },
        tran_czkType: {
            '0': '未明确',
            '1': '通用',
            '2': '性别',
            '3': '年龄',
            '4': '性别+年龄',
            '5': '样本类型',
            '6': '样本类型+性别',
            '7': '样本类型+年龄',
        },
        ljbdyzpd_tran: {
            '1': '医疗遗嘱',
            '2': '药品遗嘱',
            '3': '嘱托遗嘱',
        },
        bglx_tran: {
            "1": '通用报告',
            '2': 'sysmex xs-1000i',
            '3': '三分类血球仪(图片gif*3)',
            '4': 'sysmex uf-500i',
            '5': 'KES-900B血流变检测仪',
            '6': '酶标仪',
            '7': '微生物',
            '8': '大文本报告',
            '9': 'AVE-763A尿沉渣',
            '10': '迈瑞BC5500',
            '11': 'sysmex xs-800i',
            '12': 'AC920E0+血球仪',
            '13': '龙鑫LX-3000i尿沉渣分析仪',
            '14': 'ABX PENTRA60血球仪',
            '15': '瑞典Medonic血球仪',
            '16': '文本类型参考值报告',
            '17': '三分类血球仪(描点256*3)',
            '18': 'ST_360酶标仪',
            '19': '百特1300三分类血球仪(描点128*3)',
            '20': 'ABX MICROS 60 三分类血球仪',
            '21': '迈瑞BC5180血球仪',
            '22': '骨髓检查报告单',
            '23': '迈瑞BC5500血球仪(双栏)',
            '24': '迈瑞BC5300血球仪',
            '25': 'AVE-763B尿沉渣',
            '26': 'ABX PENTRA80血球仪',
            '27': '赛科希德SA-5600血流变',
            '28': 'sysmex xs-800i_双栏',
            '29': '通用报告1',
            '30': '迈瑞BC5180血球仪_双栏',
            '31': '贝克曼LH750血球仪',
            '32': '雅培CELL-DYN1800血球仪',
            '33': '通用报告单栏',
            '34': 'SYSMEX 2000I血球仪',
            '35': '迈瑞EH-2050A',
            '36': '乙肝五项',
            '37': '免疫定量结果显示定性',
            '38': '雷杜RT7600三分类',
            '39': '迈瑞EH-2080C',
            '40': '珠海科域KU-500尿沉渣',
            '41': 'LTS-E100 粪便分析仪6图',
            '42': '珠海科域KU-1000尿沉渣',
            '43': '杭州天创微生物',
            '44': 'Mejer1600美桥尿沉渣'
        },
        //外观质量
        wgzlList: {
            "0": '完好',
            "1": '轻微损伤',
            "3": '破损'
        },

        ysjlList: {
            "0": '合格',
            "1": '不合格',
            "3": '未验收'
        },
        rydjZT_tran:{
	    	'ryrq':'在院',
	    	'cyrq':'出院'
	   	},
	    rydjZT_tran2:{
	    	'0':'在院',
	    	'1':'出院'
	   	},
        jsks_tran:{
            '0002':'全科',
            '0003':'中医科',
            '0006':'妇产科',
        },
        zyType_tran:{
          'zy':'在院',
          'cy':'出院',
          'zk':'转科',
        },
        brgl_tran:{
            'zgbr':'主管病人',
            'qk':'全科病人',
        },
        //病况状态
        bkType_tran:{
            '0':'一般',
            '1':'病重',
            '2':'病危',
        },
        //在院状态
        zyYzclType_tran:{
            'dry':'待入院',
            'zy':'在院',
            'cy':'出院',
            'zk':'转院',
        },

        zyYzclHszType_tran:{
            'zy':'在院',
            'cy':'出院',
            'zk':'转院',
        },
        /*yzType_tran:{
            '0':'草稿',
            '1':'执行中',
            '2':'待审核',
            '3':'已停嘱'
        },*/
        yzgl_tran:{
            '%':'全部',
            '0':'未停',
            '1':'已停',
            '2':'作废',
            '3':'停嘱未审',
            '4':'未审',
        },
        qdType_tran:{
            '0':'汇总清单',
            '1':'明细清单',
        },
 /*--------------------------------用于前端展示---------------------------------------------*/
 		//医嘱状态
 		yzzt_tran:{
 			'0': '待审核',
 			'1': '已停嘱',
 			'2': '已作废',
 			'3': '待停嘱',
 			'4': '草稿',
 		},
 		//检验报告状态（0.待执行，1.待出报告，2.已出报告，3.已打印）
 		jybgzt_tran:{
 			'0':'待执行',
 			'1':'待出报告',
 			'2':'已出报告',
 			'3':'.已打印',
 		},
 	   /*收费状态*/
        sfzt_tran: {
            "0": "未收费",
            "1": "已收费",
            "2":"已退费",
        },
        //挂号种类
        ghzl_tran: {
            "0": "未收费",
            "1": "已收费",
            "2":"已退费",
        },
        //门诊交款
        mzjk_tran:{
            '0':'全部',
            '1':'已作废',
            '2':'未作废',
//            '3':'未上交',
//            '4':'已上交',
        },
        //接诊状态
        jzType_tran:{
            '9':'全部',
            '0':'未接诊',
            '1':'接诊中',
            '2':'接诊完成'
        },
        //接诊状态
        jslb_tran:{
           '0':'业务类',
           '1':'管理类'
        },
        bxlb_tran:{
        	'01':'贵州移动农合',
        	'02':'贵州五险合一',
        	'03':'无',
        	'04':'遂宁清华同方',
        },
        hszlyd_tran:{
        	'0':'明细',
        	'1':'汇总'
        },
        sfsp_tran:{
            '0':'未审批',
            '1':'已审批',
            '2':'拒绝审批'
        },
        ybjsfs_tran:{
            '0':'医保单独结算',
            '1':'住院结算同步结算',
        },
        snqhtfdj_tran:{
        	'01':'中药方',
        	'02':'西药方',
        	'03':'检查单',
        	'04':'治疗单',
        	'05':'手术单',
        	'06':'化验单',
        	'07':'注射单',
        },
        snqhtfyllb_tran:{
        	'21':'普通住院',
        	'26':'外伤住院'
        },
        snlxrgx_tran:{
        	'0':'',
        	'1':''
        },
        snzylx_tran:{
        	'1':'普通住院',
        	'2':'急诊或抢救住院',
        	'3':'转院住院',
        	'4':'外伤住院',
        	'5':'生育住院',
        	
        },
        mtbr_tran:{
            '0':'全科病人',
            '1':'门特病人',
            '2':'非门特病人',
        },
    },
};


