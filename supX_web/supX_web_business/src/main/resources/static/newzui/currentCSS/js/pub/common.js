Vue.http.options.emulateJSON = true;
// table的基本方法
var tableBase = {
    data: {
        param: {
            page: 1,
            rows: 10,
            sort: '',
            order: 'asc'
        },
        totlePage: 0,
        isChecked: [],
        isCheckAll: false,
        page: 1,
        prevMore: false,
        nextMore: false,
        showPageList: [],
        queryName: '',
        ckChecked: 0,
        arrObj: [],
        activeIndex: undefined,
        hoverIndex: undefined,
    },
    created: function () {
    },
    watch: {
        totlePage: function (val) {
            this.page = 1;
            // if (val > 0) {
            //当列表为空的时候不调这个方法！
            // this.goPage(1, null, this.queryName);
            // }
            this.hidePage();
        }
    },
    methods: {
        switchIndex: function ( key, type, index) {
            this[ key ] = type ? index : undefined;
            console.log("测试数据",key,'-->',type,'-->'+index)
        },
        hoverMouse: function (type ,index) {
            this.hoverIndex = type ? index : undefined;
        },
        recursion: function (list, mkbm, ylbm, yl, key, child, type) {
            var arrObj = this.arrObj;
            for (var u = 0; u < arrObj.length; u++) {
                arrObj[u] = JSON.stringify(arrObj[u]);
            }
            if (list[yl] == 'mk') {
                key = JSON.stringify({mkbm: list[mkbm]});
                if (type) {
                    arrObj = arrObj.join(',');
                    arrObj = arrObj.replace(key, '');
                    arrObj = arrObj.replace(',,', ',');
                    arrObj = arrObj.split(',');
                    if (!arrObj[arrObj.length - 1]) {
                        arrObj.pop();
                    }
                    if (!arrObj[0]) {
                        arrObj.shift();
                    }
                    arrObj.push(key)
                }
                else {
                    arrObj = arrObj.join(',');
                    arrObj = arrObj.replace(key, '');
                    arrObj = arrObj.replace(',,', ',');
                    arrObj = arrObj.split(',');
                    if (!arrObj[arrObj.length - 1]) {
                        arrObj.pop();
                    }
                    if (!arrObj[0]) {
                        arrObj.shift();
                    }
                }
            }
            if (list[yl] == 'yl') {
                key = JSON.stringify({ylbm: list[ylbm]});
                if (type) {
                    arrObj = arrObj.join(',');
                    arrObj = arrObj.replace(key, '');
                    arrObj = arrObj.replace(',,', ',');
                    arrObj = arrObj.split(',');
                    if (!arrObj[arrObj.length - 1]) {
                        arrObj.pop();
                    }
                    if (!arrObj[0]) {
                        arrObj.shift();
                    }
                    arrObj.push(key)
                }
                else {
                    arrObj = arrObj.join(',');
                    arrObj = arrObj.replace(key, '');
                    arrObj = arrObj.replace(',,', ',');
                    arrObj = arrObj.split(',');
                    if (!arrObj[arrObj.length - 1]) {
                        arrObj.pop();
                    }
                    if (!arrObj[0]) {
                        arrObj.shift();
                    }
                }
            }
            list[child] && arrFun(list[child]);

            function arrFun(arr) {
                for (var i = 0; i < arr.length; i++) {
                    if (arr[i][yl] == 'mk') {
                        key = JSON.stringify({mkbm: arr[i][mkbm]});
                        if (type) {
                            arrObj = arrObj.join(',');
                            arrObj = arrObj.replace(key, '');
                            arrObj = arrObj.replace(',,', ',');
                            arrObj = arrObj.split(',');
                            if (!arrObj[arrObj.length - 1]) {
                                arrObj.pop();
                            }
                            if (!arrObj[0]) {
                                arrObj.shift();
                            }
                            arrObj.push(key)
                        }
                        else {
                            arrObj = arrObj.join(',');
                            arrObj = arrObj.replace(key, '');
                            arrObj = arrObj.replace(',,', ',');
                            arrObj = arrObj.split(',');
                            if (!arrObj[arrObj.length - 1]) {
                                arrObj.pop();
                            }
                            if (!arrObj[0]) {
                                arrObj.shift();
                            }
                        }
                    }
                    if (arr[i][yl] == 'yl') {
                        key = JSON.stringify({ylbm: arr[i][ylbm]});
                        if (type) {
                            arrObj = arrObj.join(',');
                            arrObj = arrObj.replace(key, '');
                            arrObj = arrObj.replace(',,', ',');
                            arrObj = arrObj.split(',');
                            if (!arrObj[arrObj.length - 1]) {
                                arrObj.pop();
                            }
                            if (!arrObj[0]) {
                                arrObj.shift();
                            }
                            arrObj.push(key)
                        }
                        else {
                            arrObj = arrObj.join(',');
                            arrObj = arrObj.replace(key, '');
                            arrObj = arrObj.replace(',,', ',');
                            arrObj = arrObj.split(',');
                            if (!arrObj[arrObj.length - 1]) {
                                arrObj.pop();
                            }
                            if (!arrObj[0]) {
                                arrObj.shift();
                            }
                        }
                    }
                    if (arr[i][child] && arr[i][child].length > 0) {
                        arrFun(arr[i][child]);
                    }
                }
            }

            for (var k = 0; k < arrObj.length; k++) {
                arrObj[k] = JSON.parse(arrObj[k]);
            }
            this.arrObj = arrObj;
            console.timeEnd('start:');
            console.log('this.arrObj', this.arrObj);
            return this.arrObj
        },
        //val是一个数组
        //event 当前dom结构
        checkSelect: function (val, event, type) {
            if (val[1] !== 'all') this.activeIndex = val[0];
            // console.log('checkSelect')
            // var _lebal = $('label');
            // for (var i = 0; i < _lebal.length; i++) {
            //     if (event.toElement == _lebal[i]) {
            //         return
            //     }
            // }
            // if (val[1] == 'some') {
            //     Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
            //     if (!val[2]) this.isCheckAll = false;
            //     console.log(this.isChecked)
            // } else if (val[1] == 'one') {
            //     this.isCheckAll = false;
            //     this.isChecked = [];
            //     Vue.set(this.isChecked, val[0], !this.isChecked[val[0]]);
            // } else if (val[1] == 'all') {
            //     this.isCheckAll = !this.isChecked[val[0]];
            //     console.log(this.isCheckAll)
            //     if (val[2] == null) val[2] = "jsonList";
            //     if (this.isCheckAll) {
            //         for (var i = 0; i < this[val[2]].length; i++) {
            //             Vue.set(this.isChecked, i, true);
            //         }
            //     } else {
            //         this.isChecked = [];
            //     }
            // }
            // if (type == undefined) {
            //     var that = this;
            //     this.ckChecked = val[0]
            //     event.currentTarget.onkeydown = function (e) {
            //         if (e.keyCode == 40) {
            //             that.isChecked = []
            //             that.ckChecked = that.ckChecked > that[val[2]].length ? 0 : that.ckChecked + 1;
            //             that.$set(that.isChecked, that.ckChecked, true)
            //         } else if (e.keyCode == 38) {
            //             that.isChecked = []
            //             that.ckChecked = that.ckChecked < 0 ? that[val[2]].length : that.ckChecked - 1;
            //             that.$set(that.isChecked, that.ckChecked, true)
            //         }
            //     }
            // }
        },
        checkOne: function (index) {
            this.isCheckAll = false;
            this.isChecked = [];
            this.isChecked[index] = true;
        },
        checkAll: function (list) {
            // 介于大量未修改的list为jsonList，如果不传值默认的就是jsonList
            if (list == null) list = "jsonList";
            if (this.isCheckAll) {
                for (var i = 0; i < this[list].length; i++) this.isChecked[i] = true;
            } else {
                this.isChecked = [];
            }
        },
        checkSome: function (index) {
            if (!this.isChecked[index]) {
                this.isCheckAll = false;
                Vue.set(this.isChecked, index, false);
            } else {
                Vue.set(this.isChecked, index, true);
            }
        },
        // 适配新版input-checkbox组件
        reCheckBox: function (val) {
            if (val[1] !== 'all') this.activeIndex = val[0];
            if (val[0] == 'some') {
                Vue.set(this.isChecked, val[1], val[2]);
                if (!val[2]) this.isCheckAll = false;
                console.log(this.isChecked)
            } else if (val[0] == 'one') {
                this.isCheckAll = false;
                this.isChecked = [];
                Vue.set(this.isChecked, val[1], val[2]);
            } else if (val[0] == 'all') {
                this.isCheckAll = val[2];
                console.log(this.isCheckAll);
                if (val[1] == null) val[1] = "jsonList";
                if (this.isCheckAll) {
                    for (var i = 0; i < this[val[1]].length; i++) {
                        Vue.set(this.isChecked, i, true);
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                }
            }
        },
        goPage: function (index, type, funcName) {
            this.queryName = funcName;
            if (type) {
                this.changePage(type);
            } else {
                if (index > this.totlePage || index < 1) {
                    console.log(this.totlePage);
                    malert('请输入正确的页数', 'top', 'defeadted');
                    return false;
                }
                this.page = index;
                this.param.page = index;
                this.hidePage();
                if (funcName) {
                    this[funcName];
                } else {
                    this.getData();
                }
            }
        },
        changePage: function (type) {
            if (type == 'next') {
                if (this.param.page >= this.totlePage) {
                    malert('已经是最后一页了', 'top', 'defeadted');
                    return false;
                }
                this.param.page++;
                this.page++;
                this.getData();
            } else if (type == 'prev') {
                if (this.param.page <= 1) {
                    malert('这是第一页', 'top', 'defeadted');
                    return false;
                }
                this.param.page--;
                this.page--;
                this.getData();
            } else {
                this.getData();
            }
            this.hidePage();
        },
        hidePage: function () {
            if (this.totlePage < 8) {
                this.prevMore = false;
                this.nextMore = false;
                return false;
            }
            var page = parseInt(this.param.page);
            var total = parseInt(this.totlePage);
            this.showPageList = [page - 2, page - 1, page, page + 1, page + 2];
            if (this.page > 4) {
                this.prevMore = true;
            } else {
                this.showPageList = [1, 2, 3, 4, 5];
                this.prevMore = false;
            }
            if (this.page < this.totlePage - 2) {
                this.nextMore = true;
            } else {
                this.showPageList = [total - 4, total - 3, total - 2, total - 1, total];
                this.nextMore = false;
            }
        },
        showLittle: function (index) {
            if (index == 1) return false;
            if (index == this.totlePage) return false;
            if (this.totlePage < 8) return true;
            if (this.totlePage > 7) {
                for (var i = 0; i < this.showPageList.length; i++) {
                    if (this.showPageList[i] == index) return true;
                }
                return false;
            }
        },
        tableHeadSize: function (name) {
            var th_2 = $("." + name).find("tr").eq(1).find("th");
            var th_1 = $("." + name).find("tr").eq(0).find("th");
            for (var i = 0; i < th_2.length; i++) {
                // console.log(th_2.eq(i).css("width"));
                th_1.eq(i).css("min-width", th_2.eq(i).css("width"));
            }
        }
    }
};
// 光标跳转，光标选择相关
window.baseFunc = {
        data: {
            selSearch: -1,
            searchCon: [],
            total: null,
            socket: null,
            socketconnection: true,
            wsImpl: window.WebSocket || window.MozWebSocket,
            parm: {
                'ComputerName': '',
                'ComputerIp': '',
                'ComputerMac': '',
                'code': 'error',
                'mess': ''
            },
        },
//$options.filters['formDate'](value) 作为方法调用格式化时间
// value | formDate html过滤
        filters: {
            formDate: function (value) {
                var d = new Date(value);
                return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
            }
        }
        ,
        methods: {
            start: function () {
                var that = this;
                var host = "ws://localhost:13000/";
                var wsImpl = window.WebSocket || window.MozWebSocket;
                if (this.socket == null) {
                    this.socket = new wsImpl(host);
                    console.log(".. connection open 设备连接成功");
                } else {
                    console.log(".. connection  设备已打开");
                }
                try {
                    this.socket.onerror = function () {
                        console.log(".. connection  请安装驱动");
                        that.socketconnection = false;
                    };
                    this.socket.onopen = function () {
                        console.log(".. connection open打开");
                    };
                    this.socket.onclose = function () {
                        console.log('.. connection closed关闭');
                        that.socketconnection = false;
                    };
                    this.socket.onmessage = function (evt) {
                        console.log("onmessage:" + evt.data);
                        login.login(evt.data);
                        doLogin()
                    };
                }
                catch (ex) {
                    console.log(".. connection  连接异常,请检查");
                }
            },
//格式化身份证
//elmObj 代表当前实栗
//tpye 代表类型
            setAge: function (event, type, city) {
                //判断是否已存在该病人信息如果已经存在则直接赋值
                var elmObj = this;
                if (this[type].sfzjhm != null && this[type].sfzjhm != "") {
                    var str_param = {
                        sfzjlx: this[type].sfzjlx,
                        sfzjhm: this[type].sfzjhm
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=GhglGhywBrzc&types=queryCard&parm=" + JSON.stringify(str_param), function (json) {
                        // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                        if (json.a == 0) {
                            if (json.d.list != null && json.d.list != "") {
                                elmObj[type] = json.d.list[0];
                                elmObj[type].csrq = elmObj['fDate'](elmObj[type].csrq, "date");
                                if (elmObj[type].brid != null) {
                                    //禁用医疗卡信息部分
                                    type['ylkxx'] = true;
                                }
                            }
                        } else {
                            malert("查询失败：" + json.c, 'top', 'defeadted')
                        }
                    });
                }
                //根据身份证号码查询出居住地省市县
                if (this[type].sfzjlx == '01') {
                    if (this[type].sfzjhm != null && this[type].sfzjhm != "") {
                        var shengbm = this[type].sfzjhm.substring(0, 2);
                        var shibm = this[type].sfzjhm.substring(0, 4);
                        var xianbm = this[type].sfzjhm.substring(0, 6);
                        this[type].jzdsheng = null;
                        this[type].jzdshengmc = null;
                        this[type].hjdsheng = null;
                        this[type].hjdshengmc = null;
                        var mc = this.listGetName(this[city], shengbm, 'xzqhbm', 'xzqhmc');
                        Vue.set(this[type], "jzdsheng", shengbm);
                        this[type].jzdshengmc = mc;
                        Vue.set(this[type], "hjdsheng", shengbm);
                        this[type].hjdshengmc = mc;
                        this[type].jzdmc = this[type].jzdshengmc;
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"2","sjbm":"' + shengbm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                elmObj.hjdshiList = [];
                                elmObj.jzdshiList = [];
                                elmObj.hjdshiList = json.d.list;
                                elmObj.jzdshiList = json.d.list;
                                var mc = elmObj.listGetName(elmObj.hjdshiList, shibm, 'xzqhbm', 'xzqhmc');
                                elmObj[type].jzdshi = null;
                                elmObj[type].jzdshimc = null;
                                elmObj[type].hjdshi = null;
                                elmObj[type].hjdshimc = null;
                                if (mc) {
                                    Vue.set(elmObj[type], "jzdshi", shibm);
                                    elmObj[type].jzdshimc = mc;
                                    Vue.set(elmObj[type], "hjdshi", shibm);
                                    elmObj[type].hjdshimc = mc;
                                    elmObj[type].jzdmc = elmObj[type].jzdshengmc + elmObj[type].jzdshimc;
                                }
                            } else {
                                malert("市编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                                return false;
                            }
                        });
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"3","sjbm":"' + shibm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                            if (json.a == 0) {
                                elmObj.hjdxianList = [];
                                elmObj.jzdxianList = [];
                                elmObj.hjdxianList = json.d.list;
                                elmObj.jzdxianList = json.d.list;
                                var mc = elmObj.listGetName(elmObj.hjdxianList, xianbm, 'xzqhbm', 'xzqhmc');
                                elmObj[type].jzdxian = null;
                                elmObj[type].jzdxianmc = null;
                                elmObj[type].hjdxian = null;
                                elmObj[type].hjdxianmc = null;
                                elmObj[type].hjdxzqh = null;
                                if (mc) {
                                    Vue.set(elmObj[type], "jzdxian", xianbm);
                                    elmObj[type].jzdxianmc = mc;
                                    Vue.set(elmObj[type], "hjdxian", xianbm);
                                    elmObj[type].hjdxianmc = mc;
                                    elmObj[type].jzdmc = elmObj[type].jzdshengmc + elmObj[type].jzdshimc + elmObj[type].jzdxianmc;
                                    elmObj[type].hjdxzqh = elmObj[type].hjdxian;
                                }
                            } else {
                                malert("县编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                                return false;
                            }
                        });
                    }
                }
                //计算年龄
                if (this[type].sfzjlx == '01') {
                    Vue.set(this[type], 'csrq', this.sfzhtodate(this[type].sfzjhm));
                    this[type] = Object.assign({}, this[type]);
                    if (event && this[type].sfzjhm != null) this.nextFocus(event, 2);
                    else this.nextFocus(event);
                } else {
                    if (event) this.nextFocus(event);
                }
            }
            ,
            scrollTable: function (event) { //私有鼠标滚动事件
            // @scroll="scrollTable"
                if ($(event.target).attr('data-ishover') == "true") {
                    var top = $(event.target).scrollTop(),
                        left = $(event.target).scrollLeft(),
                        header = $(event.srcElement.previousElementSibling.children),
                        fixedBodys = $(event.target).siblings(".zui-table-fixed"),
                        fixedBodyTop = $('.zui-table-body', fixedBodys).scrollTop();
                    $('.zui-table-body', fixedBodys).scrollTop(top);
                    header.css("margin-left", "-" + left + "px");
                }
            }
            ,
            scrollTableFixed: function (event) {
                if ($(event.target).attr('data-ishover') == "true") {
                    var top = $(event.target).scrollTop(),
                        fixedDom = $(event.target).parents('.zui-table-fixed').siblings(".zui-table-fixed"),
                        bodyDom = $(event.target).parents('.zui-table-fixed').siblings(".zui-table-body"),
                        fixedBody = $('.zui-table-body', fixedDom);
                    if (fixedBody.scrollTop() != top) {
                        fixedBody.scrollTop(top);
                    }
                    if (bodyDom.scrollTop() != top) {
                        bodyDom.scrollTop(top);
                    }
                }
            }
            ,
//在最后一个输入框回车保存
            saveBc: function (event, type) {
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    this[type]();
                }
            }
            ,
// @click="handover('isflow')"
            handover: function (event) {
                this[event] = !this[event]
            }
            ,
            getQueryVariable: function (variable) {
                var query = window.location.search.substring(1);
                var vars = query.split("&");
                for (var i = 0; i < vars.length; i++) {
                    var pair = vars[i].split("=");
                    if (pair[0] == variable) {
                        return pair[1];
                    }
                }
                return (false);
            }
            ,
            topNewPage: function (text, url,bm) { //新增导航栏新开页面，
                for (var j = 0; j < window.top.J_tabLeft.pageLists.length; j++) {
                    if (window.top.J_tabLeft.pageLists[j].url.indexOf(url) != -1) {
                        window.top.J_tabLeft.pageLists[j].name = text;
                        window.top.rightMenu.isSelect = j;
                        window.top.J_tabLeft.isSelect = j;
                        return false;
                    }
                }
                window.top.J_tabLeft.pageLists.push({name: text, url: url,id:bm});
                window.top.rightMenu.pageLists = window.top.J_tabLeft.pageLists;
                window.top.rightMenu.isSelect = window.top.J_tabLeft.pageLists.length - 1;
                window.top.J_tabLeft.isSelect = window.top.J_tabLeft.pageLists.length - 1;
                setTimeout(function () {
                    window.top.J_tabLeft.move("left");
                },10)
                /*setTimeout(function () {
                    $('.page-tabs').scrollLeft($('.bgChange').offset().left)
                }, 10)*/
            },
            /**
             * \关闭一个导航栏页面并打开一个页面
             * @param closeUrl 要关闭的页面的url（从page开始的完整地址）
             * @param toUrl 要显示的页面的url（从page开始的完整地址）若不传则显示最后一个打开的页面
             * @param text 若要显示的页面没有被打开过则需要传这个值显示title
             */
            topClosePage: function (closeUrl,toUrl,text) {
                var closeUrlIndex,toUrlIndex;
                for (var i = 0; i < window.top.J_tabLeft.pageLists.length; i++) {
                    if (window.top.J_tabLeft.pageLists[i].url.indexOf(closeUrl) != -1 && closeUrl) {
                        closeUrlIndex = i;
                    }
                    if (window.top.J_tabLeft.pageLists[i].url.indexOf(toUrl) != -1 && toUrl) {
                        toUrlIndex = i;
                    }
                }
                if( closeUrlIndex !== undefined ){
                    window.top.J_tabLeft.pageLists.splice(closeUrlIndex,1);
                }
                if(toUrlIndex !== undefined){
                    window.top.rightMenu.isSelect = toUrlIndex;
                    window.top.J_tabLeft.isSelect = toUrlIndex;
                }else {
                    if(text){
                        this.topNewPage(text,toUrl);
                    }else {
                        window.top.rightMenu.isSelect = window.top.J_tabLeft.pageLists.length - 1;
                        window.top.J_tabLeft.isSelect = window.top.J_tabLeft.pageLists.length - 1;
                    }
                }
            },
            // jq控件取值转化为vue取值   event: 当前对象 name: 要赋值的名字（String）
            dateForVal: function (event, name) {
                var list = name.split('.');
                if (list.length == 1) {
                    this[name] = event.target.value;
                } else {
                    Vue.set(this[list[0]], list[1], event.target.value);
                }
            },
            // 根据名称生成拼音代码 value:名称内容 content:json对象名  py:content对象的拼音代码属性
            setPYDM: function (value, content, py) {
                if (value != null) {
                    value = value.substr(0, 9);
                    Vue.set(this[content], py, JSG.PY.HZ2PYOne(value));
                }
            },
            // 判断字段为空处理
            isNotEmpty: function (value, event) {
                var el = $(event.target);
                var isEmpty = event.target.attributes['data-notEmpty'].value;
                if ((value == null || value == "") && isEmpty) {
                    el.addClass("emptyError");
                    // el.before('<span class="wb-alert-circle tipError" title="不能为空"></span>');
                } else {
                    el.removeClass("emptyError");
                    // el.parent().find('.tipError').remove()
                }
            },
            prevFocus: function (event) {
                var _input = $("input").not(":disabled,input[type=checkbox],input[type=date]");
                if (event.keyCode == 37) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            if (num) {
                                _input.eq(i - num).focus();
                                console.log(i - num);
                            } else {
                                _input.eq(i - 1).focus();
                            }
                            break;
                        }
                    }
                    return false
                } else if (event.keyCode == 39) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            if (num) {
                                _input.eq(i + num).focus();
                                console.log(i + num);
                            } else {
                                _input.eq(i + 1).focus();
                            }
                            break;
                        }
                    }
                    return false
                }
            }
            ,
// 回车跳转到下一个包含data-notEmpty属性的input,会跳过disable属性的input；
//tpye是否必填项，可为随意值、
//如果有type，那么num也必须传，否则会报错，num建议为空
            nextFocus: function (event, num, tpye) {
                var _input = $("input").not(":disabled,input[type=checkbox],input[type=date]");
                if (event.keyCode == 13) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            if (num) {
                                _input.eq(i + num).focus();

                            } else {
                                if (tpye != undefined) {
                                    if (event.target.value == "") {
                                        return false;
                                    } else {
                                        _input.eq(i + 1).focus();
                                    }
                                } else {
                                    _input.eq(i + 1).focus();
                                }
                            }
                            break;
                        }
                    }
                    return false
                } else if (event.keyCode == 37) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            if (num) {
                                _input.eq(i - num).focus();
                                console.log(i - num);
                            } else {
                                _input.eq(i - 1).focus();
                            }
                            break;
                        }
                    }
                    return false
                } else if (event.keyCode == 39) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            if (num) {
                                _input.eq(i + num).focus();
                                console.log(i + num);
                            } else {
                                _input.eq(i + 1).focus();
                            }
                            break;
                        }
                    }
                    return false
                }
            }
            ,
            nextFocusLeft: function (event, num, type) {
                var _input = $("input").not(":disabled,input[type=checkbox],input[type=date]");
                if (event.keyCode == 13) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            if (num) {
                                $(this.$refs.body).animate({
                                    scrollLeft: 0
                                })
                            } else {
                                $(this.$refs.body).animate({
                                    scrollLeft: $(event.currentTarget).offset().left / 2

                                })
                            }
                            break;
                        }
                    }
                } else if (event.keyCode == 37) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            _input.eq(i - 1).focus();
                            break;
                        }
                    }
                    return false
                } else if (event.keyCode == 39) {
                    for (var i = 0; i < _input.length; i++) {
                        if (_input.eq(i)[0] == event.currentTarget) {
                            _input.eq(i + 1).focus();
                            break;
                        }
                    }
                    return false
                }
            }
            ,
// 下拉table的键盘监听事件
            keyCodeFunction: function (event, content, searchCon) {
                if (event.keyCode == 13) {
                    if (this.selSearch > -1) {
                        this[content] = this[searchCon][this.selSearch];
                        this.selSearch = -1;
                        // this.nextFocus(event);
                        $(".selectGroup").hide();
                        return true;
                    } else {
                        // this.nextFocus(event);
                        $(".selectGroup").hide();
                        return false;
                    }
                } else if (event.keyCode == 40) {  // 下移
                    if ((this[searchCon].length - 1) == this.selSearch) {
                        this.selSearch = 0;
                    } else {
                        this.selSearch++;
                    }
                    return false;
                } else if (event.keyCode == 38) {  // 上移
                    if (this.selSearch == 0) {
                        this.selSearch = this[searchCon].length - 1;
                    } else {
                        this.selSearch--;
                    }
                    return false;
                } else if (window.event.type == 'dblclick') {
                    if (this.selSearch > -1) {
                        this[content] = this[searchCon][this.selSearch];
                        this.selSearch = -1;
                        // this.nextFocus(event);
                        $(".selectGroup").hide();
                        return true;
                    } else {
                        // this.nextFocus(event);
                        $(".selectGroup").hide();
                        return false;
                    }
                }
                //return true;
            }
            ,
// 下拉框select组件的通用接受值的方法，如果接受值不符合数据处理要求请重新命名该类方法
            resultChange: function (val) {
                if (val[2].length > 1) {
                    if (Array.isArray(this[val[2][0]])) {
                        Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    } else {
                        Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                        if (val[3] != null) {
                            Vue.set(this[val[2][0]], val[3], val[4]);
                        }
                    }
                } else {
                    this[val[2][0]] = val[0];
                }
                if (val[1] != null) {
                    this.nextFocus(val[1]);
                }
            }
            ,
//常规检验 下拉框 自定义赋值 --yq
            cgjyResultChange: function (val) {
                if (val[2].length > 1) {
                    Vue.set(val[3], 'valueT', val[0])

                } else {
                    this[val[2][0]] = val[0];
                }
                if (val[1] != null) {
                    this.nextFocus(val[1]);
                }
            }
            ,
            checkedOneOut: function (index) {
                this.selSearch = index;
            }
            ,
            resCalendar: function (val) {
                this[val[1]] = val[0];
            }
            ,
// 根据id获取list里面对应的name（不局限于名称，可以是任何你要取得值）
//【list:传入的数组,val:传入的值,id:传入的值名称(String),name:要取出值得名称(String)】
            listGetName: function (list, val, id, name) {
                for (var i = 0; i < list.length; i++) {
                    if (val == list[i][id]) return list[i][name];
                }
            }
            ,
// 下拉table翻页
            pageType: function (val) {
                if (val == 1) {
                    this.dg.page = 1;
                    return true;
                }
                if (val == 2) {
                    if (this.dg.page > 1) {
                        this.dg.page--;
                        return true;
                    } else {
                        malert('已经是第一页了', 'top', 'defeadted');
                        return false;
                    }
                }
                if (val == 3) {
                    if (this.dg.page < this.total / this.dg.rows) {
                        this.dg.page++;
                        return true;
                    } else {
                        malert('没有下一页了', 'top', 'defeadted');
                        return false;
                    }
                }
                if (val == 4) {
                    this.dg.page = Math.ceil(this.total / this.rows);
                    return true;
                }
            }
            ,
// 生成住院号 str:输入住院号，scfs:生成方式
            tozyh: function (str, scfs) {
                var years = this.getyear();
                var len = str.length;
                var zyh = "";
                if (scfs == "1") {//8位流水号
                    zyh = 100000000 + str;
                    zyh = zyh.substring(zyh.length - 8);
                } else if (scfs == "3") {//年度+4位
                    zyh = 10000 + str;
                    zyh = years + zyh.substring(zyh.length - 4);
                } else { //年度+6位
                    zyh = 1000000 + str;
                    zyh = years + zyh.substring(zyh.length - 6);
                }
                return zyh;
            }
            ,
// 生成挂号序号    str:输入挂号序号，scfs:生成方式
            toghxh: function (str, scfs) {
                var len = str.length;
                var ghxh = "";
                if (scfs == "1") {//年+10位流水号
                    ghxh = 10000000000 + str;
                    ghxh = this.getyear() + ghxh.substring(ghxh.length - 10);
                } else if (scfs == "2") {//年月+8位
                    ghxh = 100000000 + str;
                    ghxh = this.getyearmonth() + ghxh.substring(ghxh.length - 8);
                } else {//年月日+6位
                    ghxh = 1000000 + str;
                    ghxh = this.getyearmd() + ghxh.substring(ghxh.length - 6);
                }
                return ghxh;
            }
            ,
// 获取年
            getyear: function () {
                var date = new Date();
                return date.getFullYear();
            }
            ,
// 取年月
            getyearmonth: function () {
                var date = new Date();
                return date.getFullYear().toString() + (add0(date.getMonth() + 1)).toString();
            }
            ,
// 取年月日
            getyearmd: function () {
                var date = new Date();
                return date.getFullYear().toString() + (add0(date.getMonth() + 1)).toString() + add0(date.getDate()).toString();
            }
            ,
            begdrag: function (event) {
                var panel = event.currentTarget;
                //光标按下时，光标和面板的相对距离
                event = event || window.event;
                var reX = event.clientX - panel.offsetLeft;
                var reY = event.clientY - panel.offsetTop;
                //当按下鼠标就应该加上在元素内部移动的事件了也就是move,因为是在整个页面移动所以是document
                //这里的event是面板移动时候的事件
                //获得left和top的最大值
                var MX = (document.documentElement.clientWidth || document.body.clientWidth) - panel.offsetWidth;
                var MY = (document.documentElement.clientHeight || document.body.clientHeight) - panel.offsetHeight;
                console.log(MX);
                document.onmousemove = function (event) {
                    event = event || window.event;
                    var X = event.clientX - reX;
                    var Y = event.clientY - reY;
                    if (X < 0) {
                        X = 0;
                    } else if (X > MX) {
                        X = MX;
                    }
                    if (Y < 0) {
                        Y = 0;
                    } else if (Y > MY) {
                        Y = MY;
                    }
                    panel.style.position = 'absolute';
                    panel.style.cursor = 'pointer';
                    panel.style.left = X + 'px';
                    panel.style.top = Y + 'px';
                };
                // 释放鼠标
                document.onmouseup = function () {
                    document.onmousemove = null;
                }
            }
            ,
            getcookie: function () {
                var cookieList = document.cookie.split(';');
                for (var i = 0; i < cookieList.length; i++) {
                    if (cookieList[i].split('=')[0] == name) {
                        return cookieList[i].split('=')[1]
                    }
                }
            }
            ,
            delCookie: function (name) {
                var exp = new Date();
                exp.setTime(exp.getTime() - 1);
                var cval = this.getcookie(name);
                if (cval != null) {
                    document.cookie = name + '=' + cval + ';expires=' + exp.toGMTString()
                }
            }
            ,
        }
        ,
// 钩子函数（实例被创建完成的时候调用）
        created: function () {
            var allInput = $("input[data-notEmpty=true],select[data-notEmpty=true]");
            for (var i = 0; i < allInput.length; i++) {
                allInput.eq(i).before("<span class='validate'>*</span>");
                allInput.eq(i).parent().css("position", "relative");
            }
        }
    }
;

var printer = {
    data: {
        isClearArea: false,
        drawList: [],
        canvas: null,
        context: null,
        printPage: null,
        printTotal: null
    },
    methods: {
        // 清除打印区域
        clearArea: function (json) {
            $(".printArea").html('');
            $(".printArea").append(json['content']);
            $(".printArea").append('<div style="page-break-before: avoid"></div>');
            this.isClearArea = true;
        },
        setPage: function (printList) {
            this.printTotal = Math.ceil(printList.length / 10);
        },
        // 打印静态字段
        printContent: function (printList) {

            for (var i = 0; i < $(".values").length; i++) {
                var list = printList;
                var text = $(".values").eq(i).text();
                var reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
                if (reg.test(text)) {
                    $(".values").eq(i).text(text);
                    continue;
                }
                if (text.indexOf('_') == -1) {
                    if (list[text] == null || list[text] == 'null') {
                        $(".values").eq(i).text('');
                    } else {
                        $(".values").eq(i).text(list[text]);
                    }
                } else {
                    // 处理日期
                    var rqList = text.split('_');
                    var rq;
                    if (rqList[0] == 'today') {
                        rq = new Date();
                    } else {
                        rq = new Date(list[rqList[0]]);
                    }
                    if (rqList[1] == 'y') {//年
                        $(".values").eq(i).text(rq.getFullYear());
                    } else if (rqList[1] == 'm') {//月
                        $(".values").eq(i).text(rq.getMonth() + 1);
                    } else if (rqList[1] == 'd') {//日
                        $(".values").eq(i).text(rq.getDate());
                    } else if (rqList[1] == 'hms') {//时
                        $(".values").eq(i).text(rq.getHours() + ':' + rq.getMinutes() + ':' + rq.getSeconds());
                    } else if (rqList[1] == 'upper') {        // 处理大小写
                        $(".values").eq(i).text(numToCn(list[rqList[0]]));
                    }
                }
            }
        },
        // 打印静态字段
        printContentNew: function (printList) {
            for (var i = 0; i < $(".values").length; i++) {
                var list = printList;
                var text = $(".values").eq(i).text();
                var reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
                if (reg.test(text)) {
                    $(".values").eq(i).text(text);
                    continue;
                }
                if (text.indexOf('_') == -1) {
                    if (list[text] == null || list[text] == 'null') {
                        $(".values").eq(i).text('');
                    } else {
                        $(".values").eq(i).text(list[text]);
                    }
                } else {
                    // 处理日期
                    var rqList = text.split('_');
                    var rq;
                    if (rqList[0] == 'today') {
                        rq = new Date();
                    } else {
                        rq = new Date(list[rqList[0]]);
                    }
                    if (rqList[1] == 'y') {//年
                        $(".values").eq(i).text(rq.getFullYear() + '-' + (rq.getMonth() + 1) + '-' + rq.getDate() + '  ' + rq.getHours() + ':' + rq.getMinutes());
                    }
                }
            }
        },
        // 打印动态字段
        printTrend: function (printList) {
            var htmlText = $(".trendAll").html();
            for (var j = 0; j < printList.length; j++) {
                if (j != 0) $(".trendAll").append(htmlText);
                var val = $('.trendDiv').eq(j).find($(".t-values"));
                for (var k = 0; k < val.length; k++) {
                    var text = val.eq(k).text();
                    if (printList[j][text] == null || printList[j][text] == 'null') {
                        $('.trendDiv').eq(j).find($(".t-values")).eq(k).text('');
                    } else {
                        $('.trendDiv').eq(j).find($(".t-values")).eq(k).text(printList[j][text]);
                    }
                }
            }
        },
        // 根据其他字段处理打印文字
        printById: function (list, id, name) {
            for (var j = 0; j < $(".values").length; j++) {
                var text = $(".values").eq(j).text();
                if (text.indexOf('_') != -1) {
                    var _list = text.split('_');
                    console.log(list.length);
                    for (var i = 0; i < list.length; i++) {
                        if (name == _list[0] && list[i][id] == _list[1]) {
                            $(".values").eq(j).text(list[i][name]);
                        } else if (name == _list[0] && $(".values").eq(j).text().indexOf('_') != -1) {
                            $(".values").eq(j).text('');
                        }
                    }
                }
            }
        },
        creatCanvas: function () {
            this.canvas = document.getElementById("table_cvs");
            this.context = this.canvas.getContext("2d");
            this.canvas.width = parseInt($('#tem').css('width'));
            this.canvas.height = parseInt($('#tem').css('height'));
        },
        reDraw: function () {
            this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
            var sx, sy, ex, ey;
            for (var i = 0; i < this.drawList.length; i++) {
                sx = this.drawList[i]['sx'];
                sy = this.drawList[i]['sy'];
                ex = this.drawList[i]['ex'];
                ey = this.drawList[i]['ey'];
                this.line(this.context, sx, sy, ex, ey, '#555', this.drawList[i]['lw'], 1);
            }
        },
        line: function (context, fromX, formY, toX, toY, strokeStyle, lineWeight, py) {
            context.save();
            if (py == 1) context.translate(0.5, 0.5);
            context.lineWidth = lineWeight;
            context.beginPath();
            context.strokeStyle = strokeStyle;
            context.moveTo(fromX, formY);
            context.lineTo(toX, toY);
            context.stroke();
            context.restore();
            context.save();
        },
    }
};

// 关于验证数据的操作可以调用这个实例
var checkData = {
    data: {},
    methods: {
        // 提交前的为空判断，elName为提交数据框的父class
        empty_sub: function (elName) {
            var error = false;
            var _input = $('.' + elName).find('input[data-notEmpty=true]');
            for (var i = 0; i < _input.length; i++) {
                if (_input.eq(i).val() == null || _input.eq(i).val() == '') {
                    _input.eq(i).addClass("emptyError");
                    _input.eq(i).before('<span class="wb-alert-circle tipError" title="不能为空"></span>');
                    error = true;
                }
            }
            if (error) {
                $('.emptyError')[0].focus();
                malert("数据填写不完整", 'top', 'defeadted');
                return false;
            } else {
                return true;
            }
        }
    }
};

// 元素的拖拉
var dragFun = {
    data: {
        diffX: 0,
        diffY: 0,
        eventId: '',
        dragEvent: []
    },
    methods: {
        beginDrag: function (e, id) {
            this.eventId = id;
            this.dragEvent = $("#" + id);
            var el = this.dragEvent[0];
            this.diffX = e.clientX - el.offsetLeft;
            this.diffY = e.clientY - el.offsetTop;
            $(".dragCSS").css("cursor", "-webkit-grabbing");
            this.dragEvent[0].style.left = el.offsetLeft;
            this.dragEvent[0].style.top = el.offsetTop;
            this.dragEvent.addClass("drawPopInfo");
            document.addEventListener("mousemove", this.move);
        },
        move: function (e) {
            this.dragEvent[0].style.left = (e.clientX - this.diffX) + 'px';
            this.dragEvent[0].style.top = (e.clientY - this.diffY) + 'px';
        },
        endDrag: function () {
            document.removeEventListener("mousemove", this.move);
            $(".dragCSS").css("cursor", "-webkit-grab");
        }
    }
};

var treeFun = {
    data: {},
    methods: {
        // 获取树组件的值,idName为父元素的id名称,event为当前点击的对象
        getTreeVal: function (idName, event) {
            var name = event.target.className;
            var tagName = event.target.tagName;
            if ((name.indexOf('tree_text1') != -1 && name.indexOf('bold') == -1) || (tagName == 'INPUT')) {
                var list = [];
                // 使用了js的dom操作,性能不高.若使用vue的ref属性,遍历树状组件对象将变得麻烦
                var selector = document.querySelector("#" + idName);
                var _input = $(selector).find('input');
                for (var i = 0; i < _input.length; i++) {
                    if (_input[i].checked) list.push(_input[i].getAttribute("id"));
                }
                return list;
            } else {
                return false;
            }
        }
    }
};

// 提示相关
var mConfirm = {
    data: {
        confirmIsShow: false,
        confirmText: '可以的'
    },
    methods: {
        cancel: function () {
            alert("cancel");
            mconfirm(null, false)
        },
        ensure: function () {
            alert("ensure");
            mconfirm(null, true)
        }
    }
};

// 格式化相关
var mformat = {
    methods: {
        //格式数据值小位数，d-输入参数，len-小数位数
        fDec: function (d, len) {
            var f = parseFloat(d);
            if (isNaN(f)) {
                return "";
            }
            var f = Math.round(d * 10000) / 10000;
            var s = f.toString();
            var rs = s.indexOf('.');
            if (rs < 0) {
                rs = s.length;
                s += '.';
            }
            while (s.length <= rs + len) {
                s += '0';
            }
            if (s == 0.00) {
                s = "";
            }
            return s;
        },
        //日期 转换为 Unix时间戳
        toUnix: function (value) {
            var f = value.split(' ', 2);
            var d = (f[0] ? f[0] : '').split('-', 3);
            var t = (f[1] ? f[1] : '').split(':', 3);
            return (new Date(
                parseInt(d[0], 10) || null,
                (parseInt(d[1], 10) || 1) - 1,
                parseInt(d[2], 10) || null,
                parseInt(t[0], 10) || null,
                parseInt(t[1], 10) || null,
                parseInt(t[2], 10) || null
            )).getTime() / 1000;
        },

        //时间格式化(data转字符串) value: data时间，types:datetime|date|time
        fDate: function (value, types) {
            if (value == null) {
                return "";
            }
            var date = new Date(value);
            Y = date.getFullYear(),
                m = date.getMonth() + 1,
                d = date.getDate(),
                H = date.getHours(),
                i = date.getMinutes(),
                s = date.getSeconds();
            if (m < 10) {
                m = '0' + m;
            }
            if (d < 10) {
                d = '0' + d;
            }
            if (H < 10) {
                H = '0' + H;
            }
            if (i < 10) {
                i = '0' + i;
            }
            if (s < 10) {
                s = '0' + s;
            }
            var t = null;
            if (types == "date") {
                t = Y + '-' + m + '-' + d;
            } else if (types == "time") {
                t = H + ':' + i;
            } else if (types == "times") {
                t = H + ':' + i + ':' + s;
            } else if (types == "year") {
                t = Y;
            } else if (types == "month") {
                t = m;
            } else if (types == "day") {
                t = d;
            }
            else if (types == "shortY") {
                t = m + '-' + d + ' ' + H + ':' + i;
            }else if (types == "shortYe") {
                t = Y + '-' +m + '-' + d + ' ' + H + ':' + i;
            }
            else if (types == "AllDate") {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            else {
                t = Y + '-' + m + '-' + d + ' ' + H + ':' + i + ':' + s;
            }
            return t;
        },
        // 根据身份证号返回出生日期
        sfzhtodate: function (strInput) {
            if (strInput == null || strInput == '') {
                return '';
            }
            if (strInput.length != 18) {
                malert("身份证长度非法!", 'top', 'defeadted');
                return '';
            }
            strInput = strInput.substr(6, 8);
            var strInputDate = strInput.substr(0, 4) + "-" + strInput.substr(4, 2) + "-" + strInput.substr(6, 4);
            if (CheckDate(strInputDate)) {
                return strInputDate;
            } else {
                malert("身份证号有误，出生日期非法!", 'top', 'defeadted');
                return '';
            }
        },
        // 判断日期是否合法,返回boolean
        CheckDate: function (strInputDate) {
            var d = new Date(strInputDate);
            if (isNaN(d)) return false;
            var arr = strInputDate.split("-");
            return ((parseInt(arr[0], 10) == d.getFullYear()) && (parseInt(arr[1], 10) == (d.getMonth() + 1)) && (parseInt(arr[2], 10) == d.getDate()));
        },
        // 身份证号返回年龄
        sfzhtonl: function (strInput) {
            if (strInput == null || strInput == '') {
                return '';
            }

            if (strInput.length != 18) {
                malert("身份证长度非法!", 'top', 'defeadted');
                return '';
            }
            var myDate = new Date();
            var month = myDate.getMonth() + 1;
            var day = myDate.getDate();

            var age = myDate.getFullYear() - strInput.substring(6, 10) - 1;
            if (strInput.substring(10, 12) < month || strInput.substring(10, 12) == month && strInput.substring(12, 14) <= day) {
                age++;
            }
            return age;
        },
        // 根据生日计算年龄 比如:2017-02-06
        datetoage: function (strInput) {

            if (strInput == null || strInput == undefined) {
                return '';
            }
            // if (!CheckDate(strInput)) {
            //     malert("出生日期非法!");
            //     return '';
            // }
            var myDate = new Date();
            var month = myDate.getMonth() + 1;
            var day = myDate.getDate();

            var age = myDate.getFullYear() - strInput.substring(0, 4) - 1;
            if (strInput.substring(5, 7) < month || strInput.substring(5, 7) == month && strInput.substring(8, 10) <= day) {
                age++;
            }
            return age;
        },
        // 出生日期转年龄【返回年龄和年龄单位】（参数 date类型/字符串:2017-02-06[0可忽略]）
        // 此方法暂不被使用：有闰年和大小月和2月的特殊情况没有考虑到位，还需修改。
        toAge: function (tdate) {
            var date1 = new Date();
            var d = new Date(tdate);
            if (isNaN(d)) return "";
            var year1 = date1.getFullYear();
            var month1 = date1.getMonth() + 1;
            var day1 = date1.getDate();
            var year2 = d.getFullYear();
            var month2 = d.getMonth() + 1;
            var day2 = d.getDate();
            var nl = "";
            var nldw = "";
            var dwmc = "";
            if (year2 < year1) {
                nl = year1 - year2;
                nldw = "1";
                if (month2 > month1) {//未过生日减一岁
                    nl = nl - 1;
                    if (nl == 0) {
                        nl = 12 - (month2 - month1);
                        nldw = "2";
                        dwmc = "月";
                    }
                } else if (month1 == month2) {//生日当月
                    if (day2 > day1) {
                        nl = nl - 1;
                        if (nl == 0) {
                            nl = 12;
                            nldw = "2";
                            dwmc = "月";
                        }
                    }
                }
            } else if (year2 = year1) {//当年
                if (month2 < month1) {
                    nl = 12 - (month2 - month1);
                    nldw = "2";
                    dwmc = "月";
                } else if (month1 == month2) {//生日当月
                    nl = day1 - day2;
                    if (nl >= 7) {//周
                        nl = parseInt(nl / 7);
                        nldw = "3";
                        dwmc = "周";
                    } else {
                        if (nl <= 0) {
                            nl = "";
                            nldw = "";
                        } else {
                            nldw = "4";
                            dwmc = "天";
                        }
                    }
                }
            }
            return {age: nl, unit: dwmc, unitNum: nldw};
        },
        //根据年龄算出生日期
        AgetoBrnl: function (brnl, dw) {
            if (brnl <= 0) {
                return "";
            }
            if (dw != "1" && dw != "2" && dw != "3" && dw != "4") {
                return "";
            }
            var date1 = new Date();
            var sDate = null;
            var year1 = null;
            var month1 = null;
            var day1 = null;
            if (dw == "1") {//岁
                year1 = date1.getFullYear() - brnl;
                month1 = date1.getMonth() + 1;
                day1 = date1.getDate() - 2;
                month1 = (month1 < 10 ? "0" + month1 : month1);
                if (day1 <= 0) {
                    day1 = 1;
                }
                day1 = (day1 < 10 ? "0" + day1 : day1);
                sDate = (year1.toString() + '-' + month1.toString() + '-' + day1.toString());
            } else if (dw == "2") {//月
                date1.setMonth(date1.getMonth() - 2);
                year1 = date1.getFullYear();
                month1 = date1.getMonth() + 1;
                day1 = date1.getDate() - 2;
                month1 = (month1 < 10 ? "0" + month1 : month1);
                if (day1 <= 0) {
                    day1 = 1;
                }
                day1 = (day1 < 10 ? "0" + day1 : day1);
                sDate = (year1.toString() + '-' + month1.toString() + '-' + day1.toString());
            } else if (dw == "3") {//周
                var DayCount = brnl * 7;
                date1.setDate(date1.getDate() - DayCount);
                year1 = date1.getFullYear();
                month1 = date1.getMonth() + 1;
                day1 = date1.getDate();
                month1 = (month1 < 10 ? "0" + month1 : month1);
                if (day1 <= 0) {
                    day1 = 1;
                }
                day1 = (day1 < 10 ? "0" + day1 : day1);
                sDate = (year1.toString() + '-' + month1.toString() + '-' + day1.toString());
            } else if (dw == "4") {//天
                date1.setDate(date1.getDate() - brnl);
                year1 = date1.getFullYear();
                month1 = date1.getMonth() + 1;
                day1 = date1.getDate();
                month1 = (month1 < 10 ? "0" + month1 : month1);
                if (day1 <= 0) {
                    day1 = 1;
                }
                day1 = (day1 < 10 ? "0" + day1 : day1);
                sDate = (year1.toString() + '-' + month1.toString() + '-' + day1.toString());
            }
            return sDate;
        }
    }
};

// 绝对定位的拖拉(主要用作弹出层的拖拉)
var diffX = 0;
var diffY = 0;
var eventId = '';
var dragEvent = null;
window.drag = function (e, id) {
    if (realTh != null) return false;
    eventId = id;
    dragEvent = $("#" + eventId);
    var el = dragEvent[0];
    diffX = e.clientX - el.offsetLeft;
    diffY = e.clientY - el.offsetTop;
    $(".dragCSS").css("cursor", "-webkit-grabbing");
    dragEvent[0].style.left = el.offsetLeft;
    dragEvent[0].style.top = el.offsetTop;
    dragEvent.addClass("drawPopInfo");
    document.addEventListener("mousemove", move);
};

function move(e) {
    dragEvent[0].style.left = (e.clientX - diffX) + 'px';
    dragEvent[0].style.top = (e.clientY - diffY) + 'px';
}

window.stopDrag = function (event) {
    document.removeEventListener("mousemove", move);
    $(".dragCSS").css("cursor", "-webkit-grab");
};

// table的th宽度拖拉
var el;
var num;
var realTh = null;
window.drawWidth = function (e) {
    el = e.target.parentElement;
    num = e.target.parentElement.cellIndex;
    realTh = e.target.parentElement.parentElement.parentElement.nextElementSibling.children[0].children[num];
    document.addEventListener("mousemove", moveWidth);
};

// 适用于打印设计的table拖拉
window.changeWidth = function (e) {
    realTh = e.target.parentElement;
    el = e.clientX - e.path[1].offsetWidth;
    num = e.clientY - e.path[1].offsetHeight;
    document.addEventListener("mousemove", changeSize);
};

function changeSize(e) {
    realTh.style.width = (e.clientX - el) + 'px';
    realTh.style.height = (e.clientY - num) + 'px';
}

function moveWidth(e) {
    realTh.style.minWidth = el.style.minWidth = (e.clientX - el.offsetLeft) + 'px';
    realTh.style.maxWidth = el.style.maxWidth = (e.clientX - el.offsetLeft) + 'px';
}

document.addEventListener("mouseup", function () {
    document.removeEventListener("mousemove", moveWidth);
    document.removeEventListener("mousemove", changeSize);
    realTh = null;
    el = null;
    num = null;
});


// mconfirm的确认提示
function mconfirm(text) {
    // mConfirm.data.confirmText = text;
    // mConfirm.isShow = true;
    // return result;
    if (confirm(text)) {
        return true;
    } else {
        return false;
    }
}

// 获取当前日期 格式:yyyy-mm-dd
function getTodayDate() {
    var nowtime = new Date();
    var year = nowtime.getFullYear();
    var month = add0(nowtime.getMonth() + 1);
    var day = add0(nowtime.getDate());

    return year + "-" + month + "-" + day;
}

// 获取当前时间 格式:yyyy-mm-dd hh:mm:ss
function getTodayDateTime() {
    var nowtime = new Date();
    var year = nowtime.getFullYear();
    var month = add0(nowtime.getMonth() + 1);
    var day = add0(nowtime.getDate());
    var hour = add0(nowtime.getHours());
    var minute = add0(nowtime.getMinutes());
    var second = add0(nowtime.getSeconds());
    var millisecond = nowtime.getMilliseconds();
    millisecond = millisecond.toString().length == 1 ? "00" + millisecond : millisecond.toString().length == 2 ? "0" + millisecond : millisecond;
    return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
}

// 返回当天开始时间 yyyy-mm-dd 00:00:00
function getTodayDateBegin() {
    var date = new Date();
    return date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate() + " 00:00:00";
}

// 返回当天结束时间 yyyy-mm-dd 23:59:59
function getTodayDateEnd() {
    var date = new Date();
    return date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate() + " 23:59:59";
}

//获取两个时间相差天数
function dateDiff(sDate1, sDate2) {
    var aDate, oDate1, oDate2, iDays;
    aDate = sDate1.split("-");
    oDate1 = new Date(aDate[1] + '-' + aDate[2] + "-" + aDate[0]);//转化为 yyyy-mm-dd
    aDate = sDate2.split("-");
    oDate2 = new Date(aDate[1] + '-' + aDate[2] + "-" + aDate[0]);//转化为 yyyy-mm-dd
    iDays = parseInt(Math.abs(oDate1 - oDate2) / 1000 / 60 / 60 / 24); //把相差的毫秒数转换为天数

    return iDays; //返回相差天数

}


// 改为vue组件调用  mformat
/*//根据身份证号返回出生日期
 function sfzhtodate (strInput) {
 if(strInput == null || strInput == undefined){
 return "";
 }
 if (strInput.length != 18){
 malert("身份证长度非法!");
 return "";
 }
 strInput = strInput.substr(6,8);
 var strInputDate = strInput.substr(0,4) + "-" + strInput.substr(4,2) + "-" + strInput.substr(6,4);
 if (CheckDate(strInputDate)){
 return strInputDate;
 }else{
 malert("身份证号有误，出生日期非法!")
 return "";
 }
 }

 //根据身份证号返回年龄
 function sfzhtonl (strInput) {
 if(strInput == null || strInput == undefined){
 return "";
 }

 if (strInput.length != 18 ){
 malert("身份证长度非法!");
 return "";
 }
 var myDate = new Date();
 var month = myDate.getMonth() + 1;
 var day = myDate.getDate();

 var age = myDate.getFullYear() - strInput.substring(6, 10) - 1;
 if(strInput.substring(10, 12) < month || strInput.substring(10, 12) == month && strInput.substring(12, 14) <= day) {
 age++;
 }
 return age ;
 }

 //根据生日计算年龄
 function datetoage (strInput) {
 if(strInput == null || strInput == undefined){
 return "";
 }

 var myDate = new Date();
 var month = myDate.getMonth() + 1;
 var day = myDate.getDate();

 var age = myDate.getFullYear() - strInput.substring(0, 4) - 1;
 if(strInput.substring(5, 7) < month || strInput.substring(5, 7) == month && strInput.substring(8, 10) <= day) {
 age++;
 }
 return age ;
 }*/

// 判断日期是否合法
function CheckDate(strInputDate) {
    var d = new Date(strInputDate);
    if (isNaN(d)) return false;
    var arr = strInputDate.split("-");
    return ((parseInt(arr[0], 10) == d.getFullYear()) && (parseInt(arr[1], 10) == (d.getMonth() + 1)) && (parseInt(arr[2], 10) == d.getDate()));
}

// 时间戳转换为日期格式yyyy-MM-dd hh:mm:ss
function add0(m) {
    return m < 10 ? '0' + m : m
}

function formatTime(timestamp, type) {
    //type  datetime返回yyyy-mm-dd hh:mm:ss , date返回 yyyy-mm-dd
    //timestamp是整数，否则要parseInt转换,不会出现少个0的情况

    if (timestamp == null || timestamp == "" || timestamp == undefined) {
        return "";
    }

    var time = new Date(timestamp);
    var year = time.getFullYear();
    var month = time.getMonth() + 1;
    var date = time.getDate();
    var hours = time.getHours();
    var minutes = time.getMinutes();
    var seconds = time.getSeconds();

    if (type == "datetime") {
        return year + '-' + add0(month) + '-' + add0(date) + ' ' + add0(hours) + ':' + add0(minutes) + ':' + add0(seconds);
    } else if (type == "date") {
        return year + '-' + add0(month) + '-' + add0(date);
    } else if (type == "yyyy-MM-dd HH:mm") {
        return year + '-' + add0(month) + '-' + add0(date) + ' ' + add0(hours) + ':' + add0(minutes);
    }
}

// 根据key遍历过滤jsonlist对象, data:jsonlist对象, key:过虑key条件字段,val:过滤的值
function jsonFilter(data, key, val) {
    var list = [];
    for (var i = 0; i < data.length; i++) {
        if (data[i][key] == val) {
            list.push(data[i]);
        }
    }
    return list;
}

/** 数字金额大写转换(可以处理整数,小数,负数) */
function numToCn(n) {
    var fraction = ['角', '分'];
    var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    var unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
    var head = n < 0 ? '负' : '';
    n = Math.abs(n);

    var s = '';

    for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    n = Math.floor(n);

    for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = '';
        for (var j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
}

//截取首尾空格
function trimStr(str) {
    return str.replace(/(^\s*)|(\s*$)/g, "");
}

function addClass() {
    return $(window.parent.document).find('#pophide').addClass('show')
}

function removeClass() {
    return $(window.parent.document).find('#pophide').removeClass('show')
}

/****************监听公共的ajax请求***************/
// 获取登录后的userId
var userId = '';
var jszgdm = '';
var yqbm = '';
var jgbm = '';
var url = window.top.location.search.toString().replace("?", "");
if (url == null || url == "") {
    url = $(window.parent.document.body).find('#this_user_id').val().toString().replace("?", "");
}

var urlList = url.split('&');
for (var i = 0; i < urlList.length; i++) {
    if (urlList[i].indexOf('czybm') != -1) userId = urlList[i].toString().replace('czybm=', '');
    if (urlList[i].indexOf('jszgdm') != -1) jszgdm = urlList[i].toString().replace('jszgdm=', '');
    if (urlList[i].indexOf('yqbm') != -1) yqbm = urlList[i].toString().replace('yqbm=', '');
    if (urlList[i].indexOf('yljgbm') != -1) jgbm = urlList[i].toString().replace('yljgbm=', '');
}

// 登录的方法
window.doLogin = function () {
    console.log(userId);
    var password = $("#loginPop input[type=password]").val();
    this.Vue.http.post("/actionDispatcher.do?reqUrl=UserInfoAction&czybm=" + userId + "&password=" + password + '&yljgbm=' + jgbm + '&yqbm=' + yqbm + '&jszgdm=' + jszgdm, JSON.stringify(baseFunc.data.parm)).then(function (data) {
        if (data.body.a == 0) {
            $("#loginPop").hide();
            malert("登录成功！", 'top', 'success');
            window.location.reload()
        } else {
            malert(data.c, 'top', 'defeadted');
        }
    })
};

// jQuery监听ajax请求
$.ajaxSettings.beforeSend = function (xhr) {
    xhr.setRequestHeader("userId", userId);
    xhr.setRequestHeader("jszgdm", jszgdm);
    xhr.setRequestHeader("jgbm", jgbm);
    xhr.setRequestHeader("yqbm", yqbm);
};
$(document).ajaxComplete(function (event, xhr, settings) {
    if (xhr.responseJSON != null && xhr.responseJSON.a == "1" && xhr.responseJSON.e == "-0500010104") {
        if (window.top.document.getElementById('loginPop') != null) {
            return;
        }
        if(document.getElementById('loginPop')!=null){
            return;
        }
        toLogin();
    }
});
// vue-resource监听ajax请求
Vue.http.interceptors.push(function (request, next) {
    request.headers.set("userId", userId);
    request.headers.set("jszgdm", jszgdm);
    request.headers.set("jgbm", jgbm);
    request.headers.set("yqbm", yqbm);
    next(function (response) {
        console.log();
        if (response.body.a == "1" && response.body.e == "-0500010104") {
            if (window.top.document.getElementById('loginPop') != null) {
                return;
            } if(document.getElementById('loginPop')!=null){
                return;
            }
            toLogin()
        }
        return response;
    });
});
$('.fieldlist').css('marginTop', $('#toolbar').innerHeight() + 'px');

// 弹出登录窗口
function toLogin() {
    $("body").append("<div id='loginPop'" +
        "<div class='loginDiv'>" +
        "<div class='login'>" +
        "<h2>会话已过期</h2>" +
        "<p>由于长时间未操作，确保用户的安全性，请重新登录！</p>" +
        "<div class='loginContent' onkeydown='doLogin()'>" +
        "<p style='margin-bottom: 20px'><span>用户名：</span>" +
        "<input disabled='disabled' value="+userId+"></p>" +
        "<p style='margin-bottom: 20px'><span>密码：</span>" +
        "<input  autofocus='autofocus' type='password'></p>" +
        "</div>" +
        "<button onclick='doLogin()'>登录</button>" +
        "</div>" +
        "</div>" +
        "</div>");
    console.log('1')
}


window.common = {
    /*
     * 加载一个html片段
     */
    loadDebrisHTML: function (el) {
        this.stateMap = {};
        this.el = $(el);
        this.show = function () {
            if (arguments.length === 1) {
                this.el.load(arguments[0]).fadeIn(300);
            } else {
                this.el.load(arguments[0], '', arguments[1]).fadeIn(300);
            }
        };
        this.saveData = function (key, data) {
            this.stateMap[key] = data;
        };
        this.delete = function () {
            var argType = Object.prototype.toString.call(arguments[0]);
            if (argType === '[object String]') {
                delete this.statsMap[arguments[0]];
            } else if (argType === '[object Array]') {
                var length = arguments[0].length;
                for (var i = 0; i < length; i++) {
                    delete this.statsMap[arguments[0][i]];
                }
            }
        }
    },
    openConfirm: function (text, sess, cb) {
        var html = '';
        html += '<div class="pop openConfirm printHide"><div  class="pophide printHide show"></div><div class="zui-form confirm-height podrag show openConfirm pop-width">';
        html += '<div class="confirm-title">提示信息</div>';
        html += '<div class="confirm-content"><div class="confirm-mad confirm-height">' + text + '</div>';
        html += '<div class="confirm-row ">';
        html += '<button class="confirm-btn confirm-primary-b cancel">取消</button><button class="confirm-btn confirm-primary  submit">确定</button></div></div></div>';
        $('body').append(html).fadeIn(600);
        $('.submit').on('click', function () {
            if (typeof sess == 'function') {
                sess();
                $('.pophide').animate({
                    opacity: 0,
                }, function () {
                    $('.openConfirm').remove()
                })
            }
        });
        $('.cancel').on('click', function () {
            if (cb != undefined) {
                if (typeof  cb == 'function') {
                    cb()
                } else {
                    malert('cd is not function', 'top', 'defeadted');
                }
            }
            $('.pophide').animate({
                opacity: 0,
            }, function () {
                $('.openConfirm').remove()
            })
        })
    },
    openloading: function (element) {
     var html = '<div class="loading-page" style=" position: absolute;top: 0;left: 0;width: 100%;height: 100%;background-color: #fff;z-index: 11111111111;"><div class="loadingPage" style=" position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);"> <svg version="1.1" id="loader-1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"\n' +
         'width="40px" height="40px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">\n' +
         '<path opacity="0.2" fill="#000" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946\n' +
         's14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634\n' +
         'c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"/>\n' +
         '<path fill="#000" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0\n' +
         'C22.32,8.481,24.301,9.057,26.013,10.047z">\n' +
         '<animateTransform attributeType="xml"attributeName="transform"type="rotate"from="0 20 20"to="360 20 20"dur="0.5s"repeatCount="indefinite"/></path></svg></div></div>';
     $(element).prepend(html).fadeIn(600)
    },
    closeLoading: function () {
     $('.loading-page').animate({
         opacity: 0,
     }, function () {
         $('.loading-page').remove()
     })
    },
    position: function (el) {
        var that = $(el.target);
        var $menu = $(el.target.parentNode.lastChild);
        inOffsetTop = that.offset().top - $(window).scrollTop();
        inOffsetBot = $(window).height() - inOffsetTop - that.height() - 20;
        inOffsetRight = $(window).width() - that.offset().left;
        if ($menu.height() > inOffsetBot) {
            $menu.addClass("dropup");
        } else {
            $menu.removeClass("dropup");
        }
        if ($menu.width() > inOffsetRight) {
            $menu.addClass("inright");
        } else {
            $menu.removeClass("inright");
        }
    }
};

