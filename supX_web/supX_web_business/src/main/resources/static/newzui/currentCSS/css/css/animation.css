/* 等待加载 */
.loading{
    display: table;
    position: fixed;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: 999;
}

.loader-inner{
    margin: 200px 0 0 48%;
}

.loader-inner div{
    border: 0 !important;
    background-color: red;
}

.loadText{
    color: green;
    margin-top: 36%;
    font-size: 14px;
}

@-webkit-keyframes ball-triangle-path-1 {
    33% {
        -webkit-transform: translate(25px, -50px);
        transform: translate(25px, -50px); }

    66% {
        -webkit-transform: translate(50px, 0px);
        transform: translate(50px, 0px); }

    100% {
        -webkit-transform: translate(0px, 0px);
        transform: translate(0px, 0px); } }

@keyframes ball-triangle-path-1 {
    33% {
        -webkit-transform: translate(25px, -50px);
        transform: translate(25px, -50px); }

    66% {
        -webkit-transform: translate(50px, 0px);
        transform: translate(50px, 0px); }

    100% {
        -webkit-transform: translate(0px, 0px);
        transform: translate(0px, 0px); } }

@-webkit-keyframes ball-triangle-path-2 {
    33% {
        -webkit-transform: translate(25px, 50px);
        transform: translate(25px, 50px); }

    66% {
        -webkit-transform: translate(-25px, 50px);
        transform: translate(-25px, 50px); }

    100% {
        -webkit-transform: translate(0px, 0px);
        transform: translate(0px, 0px); } }

@keyframes ball-triangle-path-2 {
    33% {
        -webkit-transform: translate(25px, 50px);
        transform: translate(25px, 50px); }

    66% {
        -webkit-transform: translate(-25px, 50px);
        transform: translate(-25px, 50px); }

    100% {
        -webkit-transform: translate(0px, 0px);
        transform: translate(0px, 0px); } }

@-webkit-keyframes ball-triangle-path-3 {
    33% {
        -webkit-transform: translate(-50px, 0px);
        transform: translate(-50px, 0px); }

    66% {
        -webkit-transform: translate(-25px, -50px);
        transform: translate(-25px, -50px); }

    100% {
        -webkit-transform: translate(0px, 0px);
        transform: translate(0px, 0px); } }

@keyframes ball-triangle-path-3 {
    33% {
        -webkit-transform: translate(-50px, 0px);
        transform: translate(-50px, 0px); }

    66% {
        -webkit-transform: translate(-25px, -50px);
        transform: translate(-25px, -50px); }

    100% {
        -webkit-transform: translate(0px, 0px);
        transform: translate(0px, 0px); } }

.ball-triangle-path {
    position: relative;
    -webkit-transform: translate(-25px, -25px);
    -ms-transform: translate(-25px, -25px);
    transform: translate(-25px, -25px); }
.ball-triangle-path > div:nth-child(1) {
    -webkit-animation-name: ball-triangle-path-1;
    animation-name: ball-triangle-path-1;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite; }
.ball-triangle-path > div:nth-child(2) {
    -webkit-animation-name: ball-triangle-path-2;
    animation-name: ball-triangle-path-2;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite; }
.ball-triangle-path > div:nth-child(3) {
    -webkit-animation-name: ball-triangle-path-3;
    animation-name: ball-triangle-path-3;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite; }
.ball-triangle-path > div {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    width: 12px;
    height: 12px;
    border-radius: 100%;
    border: 1px solid #fff; }
.ball-triangle-path > div:nth-of-type(1) {
    top: 50px; }
.ball-triangle-path > div:nth-of-type(2) {
    left: 25px; }
.ball-triangle-path > div:nth-of-type(3) {
    top: 50px;
    left: 50px; }



.expand-enter,.expand-leave-active{
    opacity: 0;
    transform: translateX(-20px);
}

.expand-leave-active {
    position: absolute;
}


/* 修改弹出层的过渡效果，由外向内 */
.prompt:active{
    opacity: 0;
}
.pop-fade-enter {
    opacity: 0;
}
.pop-fade-leave-to {
    opacity: 0;
}
.pop-fade-enter-active{
    transition: all .3s ease;
}
.pop-fade-leave-active {
    transition: all .3s;
}
.pop-fade-enter{
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
}

/* 由内向外弹出 */
.notice-fade-enter {
    opacity: 0;
}
.notice-fade-leave-to {
    opacity: 0;
}
.notice-fade-enter-active{
    transition: all .3s ease;
}
.notice-fade-leave-active {
    transform: translateX(-20px);
    transition: all .3s;
}
.notice-fade-enter{
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
}

/* 向左展开弹出 */
.left-fade-enter-active {
    transition: all .3s ease;
}
.left-fade-leave-active {
    transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}
.left-fade-enter{
    transform: translateX(30px);
    opacity: 1;
}
.left-fade-leave-to {
    transition: all .0s ease;
    opacity: 1;
}

/* 向右展开弹出 */
.right-fade-enter-active {
    transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}
.right-fade-leave-active {
    transition: all .3s ease;
}
.right-fade-enter{
    opacity: 1;
}
.right-fade-leave-to {
    transform: translateX(10px);
    opacity: 1;
}