var $sidefrom = $(".side-form");

//打开侧边窗口
$(document).on("click", "[data-toggle]", function (e) {
    e = window.event || e;
    e.stopPropagation();
    e.cancelBubble = true; 
    var that = $(this).data("target");
    if ($(that).hasClass("ng-hide")) {
        $(that).removeClass("ng-hide");
        $(that).after("<div class='side-form-bg'></div>");
        $("body").css("overflow", "hidden");
    } else {
        $(that).addClass("ng-hide");
        $(".side-form-bg").remove();
        $(document).css("overflow", "initial");
    }
    return false;
});


//侧边窗关闭按钮
$(document).on("click", ".side-form a.closex", function () {
    var $sideform = $(this).parent().parent().parent("[role=form]");
    $sideform.addClass("ng-hide");
    $(".side-form-bg").remove();
    $("body").css("overflow", "initial");
});

$(document).on("click", ".side-form-bg", function () {
    $(".side-form-bg").prev("[role=form]").addClass("ng-hide");
    $(".side-form-bg").remove();
    $("body").css("overflow", "initial");
});

//$(document).on("click", ".side-form", function (e) {
//    e = window.event || e;
//    e.stopPropagation();
//    e.cancelBubble = true;
//    return false;
//});