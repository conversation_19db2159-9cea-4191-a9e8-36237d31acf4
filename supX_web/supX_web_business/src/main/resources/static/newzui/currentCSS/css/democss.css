input[type="submit"],
input[type="reset"],
input[type="button"],
input[type="text"],
input[type="password"] {
  -webkit-appearance: none;
  outline: 0;
}
i {
  display: block;
}
.current {
  box-sizing: border-box;
}
a {
  text-decoration: none;
}
.nav-top {
  width: 200px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  box-sizing: border-box;
  background: #eee;
  font-size: 16px;
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  z-index: 9999;
  overflow: auto;
}
.nav-top a {
  width: 100%;
  cursor: pointer;
  font-weight: bold;
  color: #2e88e3;
  padding: 0 10px;
  box-sizing: border-box;
}
.current {
  margin-left: 240px;
}
.wh100 {
  width: 80px;
  height: 36px;
  margin: 15px;
  padding: 15px;
  float: left;
}
.current-box {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}
h3 {
  width: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.box {
  width: 15px;
  height: 15px;
  margin: 5px;
}
.box0 {
  background: #1abc9c;
}
.box1 {
  background: #fafafa;
}
.box2 {
  background: #333333;
}
.box3 {
  background: #f9fbfc;
}
.box4 {
  background: #f9fbfc;
}
.box5 {
  background: #eee;
}
.box6 {
  background: #f3b169;
}
.box7 {
  background: #45e135;
}
.box8 {
  background: #fb86000f 0f;
}
.box9 {
  background: rgba(251, 134, 0, 0.06);
}
.box10 {
  background: #f0fafe;
}
.box11 {
  background: #04a9f5;
}
.box12 {
  background: #ff5c63;
}
.box13 {
  background: #a389d4;
}
.box15 {
  background: #000;
}
.box16 {
  background: #ff5c63;
}
.box17 {
  background: #7d848a;
}
.box18 {
  background: #2e88e3;
}
.box19 {
  background: #354052;
}
.box20 {
  background: #00B83F;
}
.current-width {
  width: 150px;
  height: 100px;
  margin: 20px;
}
.current-width i {
  display: block;
  font-style: normal;
}
.current-width .bkwidth {
  width: 150px;
  height: 100px;
}
.current-side {
  width: 100%;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.current-pr {
  width: calc((100% - 40px)/2);
}
.current-icon {
  width: 100%;
  float: left;
}
.current-icon span {
  float: left;
  margin: 0 20px 25px 0;
}
