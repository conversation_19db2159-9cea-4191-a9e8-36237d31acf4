@c1abc9c:#1abc9c;
@cfafafa:#fafafa;
@cfff:#fff;
@c333:#333333;
@c757c83:#757c83;
@cf9fbfc:#f9fbfc;
@ceee:#eee;
@cf3b169:#f3b169;
@c45e135:#45e135;
@cfb86000f:#fb86000f;
@cfb8600:rgba(251,134,0,0.06);
@cf0fafe:rgba(240, 250, 254,1);
@c04a9f5:#04a9f5;
@c7f8fa4:#7f8fa4;
@cff5c63:#ff5c63;
@ca389d4:#a389d4;
@c8e9694:#8e9694;
@ce4eaec:#e4eaec;
@c000:#000;
@coloryzf:#7d848a;
@color2e:#2e88e3;
@colorf9:#f9f9f9;
@color35:#354052;
@colorf2a:#f2a654;;
@colorRgbf2a:rgba(242,166,84,0.08);
@color008:#00B83F;
@colord9:#d9dddc;
@color81:#81878e;
@color718dc7:#718dc7;
@color2e:#2e88e3;
@colorBlack05:rgba(0,0,0,.5);
@color31:#313131;
@colorRgb08:rgb(237, 250, 247) !important;//hover颜色
@colorRgb03:rgba(26,188,156,0.3) !important;
@colorRgb06:rgba(26,188,156,0.03) !important;
@colorRgb01:rgba(26,188,156,0.1) !important;
@colorRgb043:rgba(26,188,156,0.43);
@colorRgb080:rgba(26,188,156,0.8);
@colorRgb060:rgba(26,188,156,0.6);
@colorRgb045:rgba(26,188,156,0.45);
@colorRgbd906:rgba(142, 150, 148,0.6);
//颜色透明值
@c0009:rgba(0,0,0,0.9);
@c0008:rgba(0,0,0,0.8);
@c0007:rgba(0,0,0,0.7);
@c0006:rgba(0,0,0,0.6);
@c0005:rgba(0,0,0,0.5);
@c0004:rgba(0,0,0,0.4);
@c0003:rgba(0,0,0,0.3);
@c0002:rgba(0,0,0,0.2);
@c0001:rgba(0,0,0,0.1);
@cfff1:rgba(255,255,255,0.1);
@cfff2:rgba(255,255,255,0.2);
@cfff3:rgba(255,255,255,0.3);
@cfff4:rgba(255,255,255,0.4);
@cfff5:rgba(255,255,255,0.5);
@cfff6:rgba(255,255,255,0.6);
@size4:4px;
@size5:5px;
@size8:8px;
@size10:10px;
@size15:15px;
@size20:20px;
@size25:25px;
@size30:30px;
@size35:35px;
@size40:40px;
@size45:45px;
@font12:12px;
@font13:13px;
@font14:14px;
@font15:15px;
@font16:16px;
@font17:17px;
@font18:18px;
@width88:88px;
@width80:80px;
@height32:32px;
@height36:36px;
@wh50:50px;
@wh60:60px;
@wh70:70px;
@wh80:80px;
@wh90:90px;
@wh100:100px;
@wh110:110px;
@wh120:120px;
@wh125:125px;
@wh130:130px;
@wh182:180px;
@wh240:240px;
@border:1px solid @ceee;
input[type="submit"], input[type="reset"], input[type="button"], input[type="text"], input[type="password"] {
  -webkit-appearance: none;
  outline: 0;
}