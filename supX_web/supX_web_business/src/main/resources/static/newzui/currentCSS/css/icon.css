.icon-icon75:before {
  content: "\e94e";
  color: #757c83;
}
.icon-icon74:before {
  content: "\e94c";
  color: #757c83;
}
.icon-icon69:before {
  content: "\e94d";
  color: #757c83;
}
.icon-icon73:before {
  content: "\e949";
  color: #757c83;
}
.icon-icon72:before {
  content: "\e94a";
  color: #757c83;
}
.icon-icon71:before {
  content: "\e94b";
  color: #757c83;
}
.icon-icon70:before {
  content: "\e948";
  color: #fff;
}
.icon-icon63:before {
  content: "\e945";
  color: #757c83;
}
.icon-icon64:before {
  content: "\e946";
  color: #757c83;
}
.icon-icon65:before {
  content: "\e947";
  color: #757c83;
}
.icon-icon68:before {
  content: "\e942";
  color: #7f8fa4;
}
.icon-icon67:before {
  content: "\e943";
  color: #7f8fa4;
}
.icon-icon66:before {
  content: "\e944";
  color: #7f8fa4;
}
.icon-icon60:before {
  content: "\e93c";
  color: #5e5e5e;
}
.icon-icon61:before {
  content: "\e93a";
  color: #5f5f5f;
}
.icon-icon62:before {
  content: "\e93b";
  color: #5f5f5f;
}
.icon-iocn-58:before {
  content: "\e939";
  color: #5f5f5f;
}
.icon-iocn1:before {
  content: "\e900";
  color: #5f5f5f;
}
.icon-iocn2:before {
  content: "\e901";
  color: #5f5f5f;
}
.icon-iocn3:before {
  content: "\e902";
  color: #5e5e5f;
}
.icon-iocn4:before {
  content: "\e903";
  color: #5f5f5f;
}
.icon-iocn5:before {
  content: "\e904";
  color: #5f5f5f;
}
.icon-iocn6:before {
  content: "\e905";
  color: #5e5e5f;
}
.icon-iocn7:before {
  content: "\e906";
  color: #5f5f5f;
}
.icon-iocn8:before {
  content: "\e907";
  color: #5e5e5f;
}
.icon-iocn9:before {
  content: "\e908";
  color: #5e5e5f;
}
.icon-iocn10:before {
  content: "\e909";
  color: #5f5f5f;
}
.icon-iocn11:before {
  content: "\e90a";
  color: #5f5f5f;
}
.icon-iocn12:before {
  content: "\e90b";
  color: #5f5f5f;
}
.icon-iocn13:before {
  content: "\e90c";
  color: #5f5f5f;
}
.icon-iocn14:before {
  content: "\e90d";
  color: #5f5f5f;
}
.icon-iocn15:before {
  content: "\e90e";
  color: #5f5f5f;
}
.icon-iocn16:before {
  content: "\e90f";
  color: #5f5f5f;
}
.icon-iocn17:before {
  content: "\e910";
  color: #5f5f5f;
}
.icon-iocn18:before {
  content: "\e911";
  color: #5e5e5f;
}
.icon-iocn19:before {
  content: "\e912";
  color: #5f5f5f;
}
.icon-iocn20:before {
  content: "\e913";
  color: #5f5f5f;
}
.icon-iocn21:before {
  content: "\e914";
  color: #5e5e5f;
}
.icon-iocn22:before {
  content: "\e915";
  color: #5f5f5f;
}
.icon-iocn23:before {
  content: "\e916";
  color: #5f5f5f;
}
.icon-iocn24:before {
  content: "\e917";
  color: #5f5f5f;
}
.icon-iocn25:before {
  content: "\e918";
  color: #5f5f5f;
}
.icon-iocn26:before {
  content: "\e919";
  color: #5f5f5f;
}
.icon-iocn27:before {
  content: "\e91a";
  color: #5f5f5f;
}
.icon-iocn28:before {
  content: "\e91b";
  color: #5f5f5f;
}
.icon-iocn29:before {
  content: "\e91c";
  color: #5f5f5f;
}
.icon-iocn30:before {
  content: "\e91d";
  color: #5f5f5f;
}
.icon-iocn31:before {
  content: "\e91e";
  color: #5f5f5f;
}
.icon-iocn32:before {
  content: "\e91f";
  color: #5f5f5f;
}
.icon-iocn33:before {
  content: "\e920";
  color: #5e5e5f;
}
.icon-iocn34:before {
  content: "\e921";
  color: #5f5f5f;
}
.icon-iocn35:before {
  content: "\e922";
  color: #5f5f5f;
}
.icon-iocn36:before {
  content: "\e923";
  color: #5f5f5f;
}
.icon-iocn37:before {
  content: "\e924";
  color: #5f5f5f;
}
.icon-iocn38:before {
  content: "\e925";
  color: #5f5f5f;
}
.icon-iocn39:before {
  content: "\e926";
  color: #5f5f5f;
}
.icon-iocn40:before {
  content: "\e927";
  color: #5f5f5f;
}
.icon-iocn41:before {
  content: "\e928";
  color: #5f5f5f;
}
.icon-iocn42:before {
  content: "\e929";
  color: #5f5f5f;
}
.icon-iocn43:before {
  content: "\e92a";
  color: #5f5f5f;
}
.icon-iocn44:before {
  content: "\e92b";
  color: #5f5f5f;
}
.icon-iocn45:before {
  content: "\e92c";
  color: #5f5f5f;
}
.icon-iocn46:before {
  content: "\e92d";
  color: #5f5f5f;
}
.icon-iocn47:before {
  content: "\e92e";
  color: #5f5f5f;
}
.icon-iocn48:before {
  content: "\e92f";
  color: #5f5f5f;
}
.icon-iocn49:before {
  content: "\e930";
  color: #5f5f5f;
}
.icon-iocn50:before {
  content: "\e931";
  color: #5f5f5f;
}
.icon-iocn51:before {
  content: "\e932";
  color: #5f5f5f;
}
.icon-iocn52:before {
  content: "\e933";
  color: #5f5f5f;
}
.icon-iocn53:before {
  content: "\e934";
  color: #5f5f5f;
}
.icon-iocn54:before {
  content: "\e935";
  color: #5f5f5f;
}
.icon-iocn55:before {
  content: "\e936";
  color: #5f5f5f;
}
.icon-iocn56:before {
  content: "\e937";
  color: #5f5f5f;
}
.icon-iocn57:before {
  content: "\e938";
  color: #5f5f5f;
}
.icon-Artboard-12 .path1:before {
  content: "\e93e";
  color: rgb(95, 95, 95);
}
.icon-Artboard-12 .path2:before {
  content: "\e93f";
  margin-left: -0.9443359375em;
  color: rgb(94, 94, 94);
}
.icon-Artboard-1:before {
  content: "\e93d";
  color: #5f5f5f;
}
.icon-icon58:before {
  content: "\e940";
  color: #5f5f5f;
}
.icon-icon59:before {
  content: "\e941";
  color: #5f5f5f;
}