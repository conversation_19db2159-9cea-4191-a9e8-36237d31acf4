// import baseFunc from 'common.js';
newValdata = [];
//下拉查询table的组件
var searchTable = Vue.extend({
    props: ['message', 'selected', 'current', 'them', 'them_tran', 'selectVal', 'page', 'total', 'rows'],
    template:
    // '<div class=searchDiv>' +
    // '<div class=searchTableDiv>' +
    // '<table class=searchTable style="table-layout: auto !important;" >' +
    // '<tr>' +
    // '<th v-for="(title, $index) in them" v-text="$index"></th>' +
    // '</tr>' +
    // '<tr v-for="(item,$index) in message" :class="{tableTrSelect:selected==$index}" @click.stop="checkedOne($index)" @dblclick="changeVal(item)">' +
    // '<td v-for="v in them" v-text="tranIsEmpty(v) ? item[v] : them_tran[v][item[v]]"></td>' +
    // '</tr>' +
    // '</table>' +
    // '<div v-show="message.length < totalIsOld" @click.stop="getMore()">加载更多</div>' +
    // '<div v-show="message.length >= totalIsOld">没有更多信息</div>' +
    // '</div>' +
    // '</div>',
    '<div class="zui-select-group selectGroup"  role="listbox">' +
    '<div class=fixed>' +
    '<table  style="table-layout: auto !important;" >' +
    '<tr>' +
    '<th v-for="(title, $index) in them" v-text="$index"></th>' +
    '</tr>' +
    '<tr v-for="(item,$index) in message" :class="{tableTrSelect:selected==$index}" @click.stop="checkedOne($index)" @dblclick="changeVal(item,$event)">' +
    '<td v-for="v in them" v-text="tranIsEmpty(v) ? item[v] : them_tran[v][item[v]]"></td>' +
    '</tr></table></div>' +
    '<div v-show="message.length < totalIsOld" @click.stop="getMore()" class="center">加载更多</div>' +
    '<div v-show="message.length >= totalIsOld" class="center">没有更多信息</div>' +
    '</div>',
    data: function () {
        return {loaded: null};
    },
    watch: {
        message: function (newVal) {
            if (this.page) {
                this.loaded = (this.page.page - 1) * this.page.rows + newVal.length;
            } else {
                this.loaded = (this.page - 1) * this.rows + newVal.length;
            }
        }
    },
    computed: {
        totalIsOld: function () {
            if (this.page) {
                return this.page.total;
            } else {
                return this.total;
            }
        }
    },
    methods: {
        //由于每次会循环遍历，还可以持续优化
        tranIsEmpty: function (v) {
            var i, srt = '';
            for (i in this.them_tran) srt += i + ",";
            // console.log(srt+"----"+srt.indexOf(v));
            if (srt.indexOf(v) == -1)
                return true;
            else
                return false;
        },
        checkedOne: function (index) {
            this.$emit('click-one', index);
        },
        changeVal: function (val, event) {
            this.$emit('click-two', val, event);
        },
        getMore: function () {
            this.$emit('click-two', null);
        }
    }
});

//表格下拉组件
//Xactive
//等待加载的效果组件
Vue.component('loading', {
    props: ['text'],
    template: '<div class=loading @click.stop="stop($event)">' +
    '<div class="loader-inner ball-triangle-path">' +
    '<div></div>' +
    '<div></div>' +
    '<div></div>' +
    '</div>' +
    '<div class=loadText>{{text}}</div>' +
    '</div>',
    methods: {
        stop: function (e) {
            e.stopPropagation();
        }
    }
});

//select组件,注意num是当前选中的索引，index是要展示值的索引
Vue.component('select-input', {
    props: ['child', 'index', 'index_val', 'val', 'not_empty', 'name', 'search', 'index_mc', 'disable', 'phd', 'cs'],
    template: '<div class="zui-select-inline":class="{wh120:cs}"  >' +
    '<input type="text" class="zui-input" ref="inputFu" @input="listenVal()" @keydown="key($event)" ' +
    '@focus="setLiShow($event),setLiFirst()" @blur="close($event);isNotEmpty()" v-model="changeVal" :data-notEmpty="not_empty" ' +
    ':readonly="!search" :disabled="disable" :class="{emptyError: isEmpty}" :placeholder="phd"/><span @click="inputFocus" :class="[{iconClass:!disable},{bgNone:disable}]"></span>' +
    '<div class="zui-select-group" style="display: block" v-show="isShow && !disable" role="listbox">' +
    '<ul class="inner">' +
    '<li v-show="liShow[$index]" :class="{active: num == $index}" @click="selected($index)" ' +
    'v-for="(item, $index) in child"   :value="$index" v-text="childIsArray?item[index]:item"></li>' +
    '</ul>' +
    '</div>' +
    '</div>',
    data: function () {
        return {num: null, liShow: [], isShow: false, changeVal: null, event: null, isEmpty: false};
        // num是选中过后的索引或下标，index是才进来的索引
    },
    computed: {
        childIsArray: function () {
            return Array.isArray(this.child)
        },
        indexList: function () {
            var list = [];
            for (var i in this.child) {
                if (this.child[i]) list.push(i);
            }
            return list;
        },
        indexObj: function () {
            var obj = {}, j = 0;
            for (var i in this.child) {
                this.childIsArray ? obj[this.child[i][this.index_val]] = j : obj[i] = j;
                j++;
            }
            return obj;
        },
        obj: function () {
            var obj = {}, j = 0;
            for (var i in this.child) {
                obj[j] = i;
                j++;
            }
            return obj;
        },
        firstLi: function () {
            if (this.childIsArray) {
                for (var i = 0; i < this.liShow.length; i++) {
                    if (this.liShow[i]) return i;
                }
            } else {
                for (var i in this.liShow) {
                    if (this.liShow[i]) return i;
                }
            }
        },
        lastLi: function () {
            if (this.childIsArray) {
                for (var i = this.liShow.length; i >= 0; i--) {
                    if (this.liShow[i]) return i;
                }
            } else {
                for (var i = this.indexList.length; i >= 0; i--) {
                    if (this.liShow[this.indexList[i]]) return this.indexList[i];
                }
            }
        },
        haveVal: function () {
            if (this.childIsArray) {
                for (var i = 0; i < this.child.length; i++) {
                    if (this.child[i][this.index_val] == this.val) return true;
                }
            } else {
                for (var i in this.child) {
                    if (i == this.val) return true;
                }
            }
            return false;
        }
    },
    created: function () {
        if (this.val) this.valToChangeVal();
    },
    watch: {
        val: function (newVal) {
            this.isNotEmpty('twice');
            if (newVal == null || newVal == '') return this.changeVal = null;
            this.valToChangeVal();
        },
        child: function (newVal) {
            if (newVal.length == 0) {
                this.changeVal = '';
                this.isShow = false;
                return false;
            }
            this.setLiFirst();
            this.valToChangeVal();
        }
    },
    methods: {
        inputFocus: function () {
            if (!this.isShow) {
                this.$refs.inputFu.focus();
                this.setLiShow(this.$refs.inputFu);
                this.setLiFirst();
                this.isShow = true
            }
        }
        ,
        valToChangeVal: function () {
            if (this.childIsArray) {
                for (var i = 0; i < this.child.length; i++) {
                    if (this.child[i][this.index_val] == this.val) {
                        this.changeVal = this.child[i][this.index];
                        return false;
                    } else {
                        this.changeVal = '';
                    }
                }
            } else {
                this.changeVal = this.child[this.val];
            }
        },
        isNotEmpty: function (num) {
            if (num && (this.val == null || this.val == '')) return false;
            if ((this.val == null || this.val == "") && this.not_empty) {
                this.isEmpty = true;
            } else {
                if (this.event) {
                    // 原生js刪除className的方法
                    $(this.event.target).parent().find('.tipError').remove()
                    var reg = new RegExp('(\\s|^)' + 'emptyError' + '(\\s|$)');
                    this.event.target.className = this.event.target.className.replace(reg, ' ');
                }
                this.isEmpty = false;
            }
        },
        setLiShow: function (event) {
            this.isShow = false
            if (!this.isShow) {
                $('.zui-select-group').hide();//打开之前先关闭所有的下拉弹出框
                this.event = event;
                if (this.childIsArray) {
                    for (var a = 0; a < this.child.length; a++) this.liShow[a] = true;
                } else {
                    this.liShow = {};
                    for (var i in this.child) this.liShow[i] = true;
                }
                this.isShow = true
                common.position(event)
                $(this.$el.lastChild).show()
            } else {
                this.isShow = false
            }
        },
        setLiFirst: function (event) {
            if (event != null) event.srcElement.previousSibling.focus();
            if (!this.haveVal) {
                this.num = this.indexList[0];
            } else {
                this.childIsArray ? this.num = this.indexObj[this.val] : this.num = this.val;
            }
        },
        hide: function (e) {
            if (!this.event.target.contains(e.target) && !this.event.target.nextElementSibling.contains(e.target)) {
                this.isShow = false;
                document.removeEventListener('click', this.hide);
            }
        },
        close: function (event) {
            this.event = event;
            document.addEventListener('click', this.hide);
            // this.hide(document);   之前这样写是因为想当input失去焦点时关闭下拉，但是这里会影响到选择时input失去焦点而触发不了li的click事件
        },
        selected: function (num) {
            var arr = this.name.split(".");
            this.isShow = false;
            if (this.childIsArray)
                this.$emit("change-data", [this.child[num][this.index_val], event.target.parentElement.parentElement.parentElement.firstChild, arr, this.index_mc, this.child[num][this.index]]);
            else
                this.$emit("change-data", [num, event.target.parentElement.parentElement.parentElement.firstChild, arr]);
            //[当前选中的值，当前input对象（如不跳转为null），要赋值的参数数组]
        },
        key: function (event) {
            var arr = this.name.split(".");
            if (event.keyCode == 13 || event.keyCode == 9) {
                this.valToChangeVal();
                if (this.childIsArray)
                    this.$emit("change-data", [this.child[this.num][this.index_val], event, arr, this.index_mc, this.child[this.num][this.index]]);
                else
                    this.$emit("change-data", [this.num, event, arr]);
                this.isShow = false;
            } else if (event.keyCode == 40) {
                if (this.num == this.lastLi) {
                    this.num = this.firstLi;
                    return
                }
                if (this.childIsArray) {
                    for (this.num++; this.num < this.child.length; this.num++) {
                        if (this.liShow[this.num]) break;
                    }
                } else {
                    this.num = this.obj[this.indexObj[this.num] + 1];
                    for (var i = 0; i < this.indexList.length - this.indexObj[this.num]; i++) {
                        if (this.liShow[this.num])
                            break;
                        else
                            this.num = this.obj[this.indexObj[this.num] + 1];
                    }
                }
            } else if (event.keyCode == 38) {
                if (this.num == this.firstLi) {
                    this.num = this.lastLi;
                    return;
                }
                if (this.childIsArray) {
                    for (this.num--; this.num >= 0; this.num--) {
                        if (this.liShow[this.num]) break;
                    }
                } else {
                    this.num = this.obj[this.indexObj[this.num] - 1];
                    for (var i = this.indexList.length - this.indexObj[this.num]; i >= 0; i--) {
                        if (this.liShow[this.num])
                            break;
                        else
                            this.num = this.obj[this.indexObj[this.num] - 1];
                    }
                }
            }
        },
        listenVal: function () {
            this.isShow = true;
            var first = true;
            if (this.childIsArray && this.changeVal != null && this.changeVal != '') {
                for (var i = 0; i < this.child.length; i++) {
                    var py = -1;
                    if (this.child[i]['pydm']) {
                        if (this.child[i]['pydm'].toLowerCase().indexOf(this.changeVal) != -1)
                            py = 0;
                        else if (this.child[i]['pydm'].toUpperCase().indexOf(this.changeVal) != -1)
                            py = 0;
                    } else if (this.child[i]['pyjm']) {
                        if (this.child[i]['pyjm'].toLowerCase().indexOf(this.changeVal) != -1)
                            py = 0;
                        else if (this.child[i]['pyjm'].toUpperCase().indexOf(this.changeVal) != -1)
                            py = 0;
                    }
                    if (this.child[i][this.index].indexOf(this.changeVal) == -1 && py == -1) {
                        Vue.set(this.liShow, i, false);
                    } else {
                        Vue.set(this.liShow, i, true);
                        if (first) this.num = i;
                        first = false;
                    }
                }
            } else if (this.changeVal != null && this.changeVal != '') {
                for (var i in this.child) {
                    if (this.child[i].indexOf(this.changeVal) == -1) {
                        Vue.set(this.liShow, i, false);
                    } else {
                        Vue.set(this.liShow, i, true);
                        if (first) this.num = i;
                        first = false;
                    }
                }
                //为(object)this.liShow创建同名的对象并赋值,作用在于可以生成get、set监听
                this.liShow = Object.assign({}, this.liShow);
            }
        }
    }
});


//树tree_tem1的组件
Vue.component('tree_tem1', {
    props: ['list', 'id', 'name', 'child', 'isTrue', 'checked', 'childname', 'type', 'index', '$index', 'childid', 'isnck'],
    template: '<div class="tree_tem1" >' +
    '<ul>' +
    '<li><div class="xtmktreediv">' +
    '<input class="green"  :id="xzList" v-model="isChecked" type="checkbox"><label :for="xzList" @click="doCheck"></label><img class="toggleIMg" @click.stop.prevent="toggle($event,list)" :src="list[child] != null?listing03:listing01"/>' +
    '<div @click.stop.prevent="toggle($event,list)"    :class="{bold: list[child] != null}" v-text="listName" class=tree_text1></div></div>' +
    '<tree_tem1 v-for="(item,$index) in childList" :key="item" :list="item" :checked="checked"' +
    ':id="id" :name="name" :isTrue="isChecked" :isnck="isnck" :$index="$index" @changea="checkedValo" :childid="childid" :child="child" :type="type" :childname="childname"  v-show="isShow"></tree_tem1>' +
    '</li></ul></div>',
    data: function () {
        return {
            isShow: false,
            isChecked: false,
            listName: null,
            childList: this.list[this.child],
            listing01: '/newzui/pub/image/toggle01.png',
            listing02: '/newzui/pub/image/toggle02.png',
            listing03: '/newzui/pub/image/toggle03.png',
        }
    },
    watch: {
        isTrue: function (newVal, oVal) {
            this.isChecked = newVal;
        },
        checked: function (newVal) {
            if (this.isnck) {
                this.isChecked = false;
            }
            if (newVal.length != 0) {
                newValdata = newVal
                var that = this
                var arrObj = [];
                funFor(newVal)

                function funFor(arr) {
                    for (var i = 0; i < arr.length; i++) {
                        if (arr[i]['lx'] == 'mk') {
                            key = JSON.stringify({mkbm: arr[i][that.id]})
                            arrObj.push(key)
                        } else {
                            key = JSON.stringify({mkbm: arr[i][that.childid]})
                            arrObj.push(key)
                        }
                        if (arr[i]['item'] && arr[i]['item'].length > 0) {
                            funFor(arr[i]['item']);
                        }
                    }
                }

                this.isChecked = false;
                for (var i = 0; i < arrObj.length; i++) {
                    if (this.list['lx'] == 'mk') {
                        if (JSON.parse(arrObj[i]).mkbm == this.list[this.id]) {
                            this.isChecked = true;
                            arrObj.splice(i, 1)
                            return true;
                        }
                    } else {
                        if (JSON.parse(arrObj[i]).mkbm == this.list[this.childid]) {
                            this.isChecked = true;
                            arrObj.splice(i, 1)
                            return true;
                        }
                    }
                }
            }

        },
        isChecked: function (newVal, oVal) {
            if (newValdata != 0) {
                for (var n = 0; n < newValdata.length; n++) {
                    if (newVal) {
                        this.$emit("changea", [true, newValdata[n]]);
                    } else {
                        this.$emit("changea", [false, newValdata[n]]);
                    }
                }
            } else {
                if (newVal) {
                    this.$emit("changea", [true, this.list]);
                } else {
                    this.$emit("changea", [false, this.list]);
                }
            }

        }
    },
    mounted: function () {
        for (var i = 0; i < this.checked.length; i++) {
            if (this.checked[i][this.id] == this.list[this.id]) this.isChecked = true;
        }
    },
    created: function () {
        // this.listName = this.list[this.name];
        this.listName = this.list[this.type] == 'mk' ? this.list[this.name] : this.list[this.childname];
    },
    computed: {
        xzList: function () {
            return this.list[this.type] == 'mk' ? this.list[this.id] : this.list[this.childid]
        },
        isFolder: function () {
            if (this.list[this.child] != null) return this.list[this.child].length
        }
    },
    methods: {
        doCheck: function () {
            newValdata = []
        },
        toggle: function (event, list) {
            if (this.isFolder) {
                this.isShow = !this.isShow
                if (!this.isShow) {
                    this.listing03 = '/newzui/pub/image/toggle03.png'
                } else {
                    this.listing03 = '/newzui/pub/image/toggle02.png'
                }
            }
            else {
                this.isChecked = !this.isChecked;
                this.$emit("changea", [this.isChecked, list]);
            }
        },
        checkedValo: function (val) {
            this.$emit("changea", val);
        }
    }
});

var calendar = Vue.extend({
    props: ['model', 'model_name', 'time', 'section', 'phd'],
    template: '<div class="calendar"><input v-model="model" @blur="close($event)" @focus="isShow = true" type="text" :placeholder="phd" readonly="readonly"/>' +
    '<img src="/pub/image/calendar.png" @click="isShow = true"/>' +
    '<div class="calendarDiv" v-show="isShow"><div class="header">' +
    '<span style="color: #CCCCCC" class="pre-btn" @click="preYear">上年</span>' +
    '<span class="pre-btn" @click="preMonth">上月</span>' +
    '<span class="now-y-m">{{nowYear}}/{{formatNowMonth}}</span>' +
    '<span style="color: #CCCCCC" class="next-btn" @click="nextYear">下年</span>' +
    '<span class="next-btn" @click="nextMonth">下月</span></div><div class="title">' +
    '<span v-for="week in weeks">{{week}}</span></div>' +
    '<div class="content"><ul>' +
    '<li v-for="(day,index) in days" @click="showDate(day, index)" ' +
    ':class="[{today: index === today}, {selected: obj[index] == nowYear.toString()+nowMonth.toString()+day.toString()}, {begin: begin == date}, ' +
    '{end: end == date}]" :key="index">{{day}}</li>' +
    '</ul></div><div class="content time" v-if="time"><div class="timeIpu">' +
    '<span>时 间</span>' +
    '<input type="number"/><span>:</span>' +
    '<input type="number"/><span>:</span>' +
    '<input type="number"/></div>' +
    '<div class="timeBtu" @click="doOk()">确定</div>' +
    '<div class="timeBtu" @click="nowDay(),init()">今天</div>' +
    '<div class="timeBtu" @click="clear()">清空</div>' +
    '</div></div></div>',
    data: function () {
        return {
            isShow: false,
            weeks: ['日', '一', '二', '三', '四', '五', '六'],
            days: [],
            obj: {},
            nowMonth: null,
            nowYear: null,
            nowDate: null,
            begin: null,
            end: null,
            date: "",
            event: null
        };
    },
    computed: {
        today: function () {
            var date = new Date();
            if (this.nowYear === date.getFullYear() && this.nowMonth === date.getMonth()) {
                return (this.getFirstDay(this.nowYear, this.nowMonth) + this.nowDate - 1);
            }
            return false;
        },
        formatNowMonth: function () {
            if (this.nowMonth < 9) return '0' + (this.nowMonth + 1);
            return this.nowMonth + 1;
        }
    },
    created: function () {
        this.nowDay();
        this.init();
    },
    methods: {
        // 获取当月天数
        getDates: function (year, month) {
            var date = new Date(year, month + 1, 0);
            return date.getDate();
        },
        // 获取当月第一天是星期几
        getFirstDay: function (year, month) {
            var date = new Date();
            var firstDay = new Date(year, month, 1);
            return firstDay.getDay();
        },
        nowDay: function () {
            var date = new Date();
            this.nowYear = date.getFullYear();
            this.nowMonth = date.getMonth();
            this.nowDate = date.getDate();
        },
        hide: function (e) {
            if (!this.event.target.contains(e.target) && !this.event.target.nextElementSibling.nextElementSibling.contains(e.target) && !this.event.target.nextElementSibling.contains(e.target)) {
                this.isShow = false;
                document.removeEventListener('click', this.hide);
            }
        },
        close: function (event) {
            this.event = event;
            document.addEventListener('click', this.hide);
        },
        init: function () {
            this.days = [];
            var date = new Date();
            var dates = this.getDates(this.nowYear, this.nowMonth);
            var firstDay = this.getFirstDay(this.nowYear, this.nowMonth);
            var totalLength = 42;
            if (firstDay != 0) {
                for (var i = 0; i < firstDay; i++) this.days.push('');
            }
            for (var i = 0; i < dates; i++) this.days.push(i + 1);
            var daysLength = this.days.length;
            if (daysLength < totalLength) {
                for (var i = 0; i < totalLength - daysLength; i++)
                    this.days.push('');
            }
        },
        preMonth: function () {
            if (this.nowMonth <= 0) {
                this.nowYear -= 1;
                this.nowMonth = 11;
            } else {
                this.nowMonth -= 1;
            }
            this.init();
        },
        nextMonth: function () {
            if (this.nowMonth >= 11) {
                this.nowYear += 1;
                this.nowMonth = 0;
            } else {
                this.nowMonth += 1;
            }
            this.init();
        },
        preYear: function () {
            this.nowYear -= 1;
            this.init();
        },
        nextYear: function () {
            this.nowYear += 1;
            this.init();
        },
        showDate: function (day, index) {
            if (day === '') return;
            if (this.section) {
                if (this.begin) {
                    this.end = this.nowYear + "" + this.nowMonth + "" + day;
                } else {
                    this.begin = this.nowYear + "" + this.nowMonth + "" + day;
                }
                return;
            } else {
                this.obj = {};
                Vue.set(this.obj, index, this.nowYear + "" + this.nowMonth + "" + day);
                this.isShow = false;
                // console.log(this.obj);
            }
            this.$emit("result", [this.nowYear + '-' + (this.nowMonth + 1) + '-' + (day), this.model_name]);
        },
        doOk: function () {
            if (this.section) {
                this.$emit("result", [this.nowYear + '-' + (this.nowMonth + 1) + '-' + (day), this.model_name]);
            }
        },
        clear: function () {
            this.list = [];
            this.begin = null;
            this.end = null;
            this.isShow = false;
            this.$emit("result", [null, this.model_name])
        }
    }
});

// 复选框组件
Vue.component('input-checkbox', {
    props: ['list', 'type', 'which', 'isChecked', 'val'],
    // template: '<div style="width:50px" class="zui-table-cell cell-1-0" cell="cell-1-0">' +
    template: '<div class="cell-m zui-table-cell text-center">' +
    '<input class="green" type="checkbox"  v-model="myVal">' +
    '<label @click="doCheck" @dblclick.stop></label>' +
    '</div>',
    data: function () {
        return {
            myVal: this.val
        }
    },
    watch: {
        'val': function (ov, nv) {
            this.myVal = ov
        }
    },
    methods: {
        doCheck: function () {
            if (this.type == 'some') {
                this.$emit("result", ['some', this.which, !this.myVal]);
            } else if (this.type == 'one') {
                this.$emit("result", ['one', this.which, !this.myVal]);
            } else if (this.type == 'all') {
                this.$emit("result", ['all', this.list, !this.myVal]);
            }
        }
    },
});
// date 时间字段
// type 类型字段
// text 文字字段
// <pop-page  :date="'date'" :text="'text'" :type="'type'"  :lsit="objData" @reslut="reslut"></pop-page>
Vue.component('pop-page', {
    props: ['date', 'text', 'type', 'lsit'],
    template: '<div class="pop-page-content"><p class="header-page">状态查看</p>' +
    '<ul class="pop-item"><li ref="popCs" class="pop-list " v-for="(list,index) in lsit" ><span class="pop-icon" v-if="list[type]!=0"></span><span class="icon-not-active" v-else>&#10004;</span><span class="pop-date">{{list["date"]|formDate}}</span><span class="pop-text">{{list["text"]}}</span><span class="pop-se" @click="goto(list)" v-if="index==0">去完成</span></li></ul></div>',
    data: function () {
        return {}
    },
    created: function () {
        this.$nextTick(function () {
            this.to()
        })
    },
    filters: {
        formDate: function (value) {
            var d = new Date(value);
            return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
        }
    },
    methods: {
        to: function () {
            for (var i = 0; i < this.lsit.length; i++) {
                if (this.lsit[i].type == 1) {
                } else {
                    $(this.$refs.popCs[i]).addClass('bggGradientm')
                }
            }
        },
        goto: function (item) {
            this.$emit('reslut', item)
        }
    },
});
//树tree_tem2的组件
Vue.component('tree_tem2', {
    mixins:[baseFunc],
    name: 'stact-overflow',
    props: ['list', 'id', 'name', 'child', 'isTrue', 'checked', 'childname', 'type', 'index', 'childid'],
    template: '<li  :id="xzList">' +
    '<a  @click="loadchild(list.mkbm,list,$event)"  class="list_tab"  :data-getTitle="listName" :class="[{childList: list[child] == null},{title:listName.length>6}]">' +
    '<span  class=spantext v-text="listName" ></span><span v-show="list[child] != null" class="fa fa-angle-right arrowIcon"></span></a><ul style="display: none;" class=nav-second-level itemList>' +
    '<tree_tem2 v-for="(item,index) in childList" :key="item" :list="item" :checked="checked"' +
    ':id="id" :name="name" :tabindex="index+1" :index="index" :type="type" :childname="childname" :childid="childid" :child="child" ></tree_tem2></ul>' +
    '</li>',
    data: function () {
        return {
            isShow: false,
            isChecked: false,
            isSelect: undefined,
            listName: null,
            num: 0,
            flag: false,
            childList: this.list[this.child],
        }
    },
    created: function () {
        this.listName = this.list[this.type] == 'mk' ? this.list[this.name] : this.list[this.childname];
    },
    computed: {
        isFolder: function () {
            if (this.list[this.child] != null) return this.list[this.child].length
        },
        xzList: function () {
            return this.list[this.type] == 'mk' ? this.list[this.id] : this.list[this.childid]
        },
    },
    methods: {
        getQueryVariable: function (variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return (false);
        },
        loadchild: function (id, list) {
            if (this.isFolder) {
                var _id = $(this.$el).find('ul')[0];
                if (!$(_id).hasClass('show')) {
                    // 折叠其他同级菜单
                    $(_id).parent().siblings().find("ul").removeClass('show').slideUp(200);
                    $(_id).parent().siblings().find(".arrowIcon").removeClass("fa-angle-down").addClass("fa-angle-right");

                    // 展开当前菜单
                    $(_id).addClass('show').slideDown(200);
                    $(_id).parent().find(".arrowIcon").removeClass("fa-angle-right").addClass("fa-angle-down");
                } else {
                    // 折叠当前菜单
                    $(_id).removeClass('show').slideUp(200);
                    $(_id).parent().find(".arrowIcon").removeClass("fa-angle-down").addClass("fa-angle-right");
                }
            }
            if (list.lx == 'yl') {
                $(this.$el).addClass("menuLiSelected").parents('.submenu').find('li').removeClass("menuLiSelected");
                $(this.$el).addClass("menuLiSelected");
                if (J_tabLeft.isSelect >= 8) {
                    malert("你打开过多！为了不影响你的使用体验！请关闭一些模块在打开", 'top', 'defeadted');
                    return false
                }
                window.navbar.index = list.types
                // for (var j = 0; j < window.top.J_tabLeft.pageLists.length; j++) {
                //     if (window.top.J_tabLeft.pageLists[j].url.indexOf(list.url) != -1) {
                //         window.top.rightMenu.isSelect = j;
                //         window.top.J_tabLeft.isSelect = j;
                //         return false;
                //     }
                // }
                if (list.fr == 1) {
                    window.top.J_tabLeft.pageLists.push({
                        "name": list.ylmc,
                        "url": location.origin + list.url + '&yljgbm=' + this.getQueryVariable('yljgbm') + '&czybm=' + this.getQueryVariable('czybm')
                    });
                    this.topNewPage(list.ylmc,location.origin + list.url + '&yljgbm=' + this.getQueryVariable('yljgbm') + '&czybm=' + this.getQueryVariable('czybm'))
                } else {
                    this.topNewPage(list.ylmc,"page" + list.url + ".html",list.ylbm)
                //     window.top.J_tabLeft.pageLists.push({
                //         "name": list.ylmc,
                //         "url": "page" + list.url + ".html",
                //         'id': list.ylbm
                //     });
                //     window.top.rightMenu.pageLists = window.top.J_tabLeft.pageLists
                }
                // window.top.rightMenu.isSelect = window.top.J_tabLeft.pageLists.length - 1
                // window.top.J_tabLeft.isSelect = window.top.J_tabLeft.pageLists.length - 1
                // window.top.J_tabLeft.move("left");
                // setTimeout(function () {
                //     $('.page-tabs').scrollLeft($('.bgChange').offset().left)
                // }, 10)
            }
        },
    }
});

// <select-input @change-data="resultLxChange"
// :child="YFJson" :index="'yfmc'" :index_val="'yfbm'" :val="serchContent.yfbm"
// :name="'serchContent.yfbm'">
//     </select-input>
Vue.component('fyp-input', {
    props: ['text', 'type', 'list', 'index', 'yfmc', 'json', 'yfbm', 'name', 'ksbm'],
    //props: ['text','type','list'],
    template: '<div class="fytyp"><ul><li v-for="(item,index) in list"><span class="padd-r-10">{{item["text"]}}</span>' +
    '<span v-if="item[type]==0"><input  class="zui-input height36 text-left" v-model="item.t" /></span>' +
    '<span v-if="item[type]==1" class="height36">' +
    '<select-input style="height:36px !important;" @change-data="resultChange" :child="json"  :val="popConetent.yfbm" :index="\'yfmc\'"  :index_val="\'yfbm\'"  :name="\'popConetent.yfbm\'" :search="true" :index_mc="\'yfmc\'"  ></select-input></span>' +
    '<span v-if="item[type]==2"><textarea class="zui-input zui-textarea" v-model="item.t" @keydown.enter="saveText"></textarea></span></li></ul></div>',
    data: function () {
        return {
            popConetent: {},
            modelinput: ''
        }
    },
    methods: {
        resultRydjChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            Vue.set(this[val[2][0]], val[3], val[4]);
        },
        saveText: function () {
            this.$emit('savego', this.list);

        }

    },
})

// Vue.component('fyp-input',{
//     props: ['text','type','list','yfmc','json','yfbm','name','ksbm'],
//     //props: ['text','type','list'],
//     template:'<div class="fytyp"><ul><li v-for="(item,index) in list" @click="goto(item)"><span class="padd-r-10">{{item["text"]}}</span>' +
//     '<span v-if="item[type]==0"><input type="text" class="zui-input height36" v-model="modelinput"/></span>' +
//     '<span v-if="item[type]==2"><textarea class="zui-input zui-textarea"></textarea></span></li></ul></div>',
//     data: function () {
//         return {
//             popConetent:{},
//             modelinput:''
//         }
//     },
//     created: function () {
//         this.$nextTick(function () {
//             this.goto()
//         })
//     },
//     methods: {
//         goto: function (list) {
//             this.$emit('goBack', [list,this.modelinput])
//         },
//         // resultRydjChange:function (val) {
//         //     Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
//         //     Vue.set(this[val[2][0]], val[3], val[4]);
//         // }
//
//     },
// })
Vue.component('td-template', {
    props: ['iscon', 'contextmenuone', 'contextmenutwo', 'optionlist'],
    template: '<div class="grid-box jieduan-box">\n' +
    '    <div class="col-xxl-12 col-s-12 col-l-12 col-xl-12 ">\n' +
    '        <div class="common_left header_text bg-fff">\n' +
    '            <div class="md_b_8 user-select">\n' +
    '            </div>\n' +
    '            <div class=" md_r_10 md_l pd_b_10">\n' +
    '                <ul class="bg-box">\n' +
    '                    <li >\n' +
    '                        <ul class="flex-box">\n' +
    '                            <li class="rol-title rol-bg"><span class="fenlei">分类</span><i class="icon-border"></i><span class="jieduan">阶段</span></li>\n' +
    '                            <li class="flex-one">\n' +
    '                                <ul class="flex-box">\n' +
    '                                    <li @contextmenu="show(true,$event,\'\',\'\',index)" class="item  item-border item-header" v-for="(item,index) in optionlist">\n' +
    '                                        <span class="text-center flex-checked" v-if="ischeck">\n' +
    '                                            <input class="green" type="checkbox" v-model="item.isCheckAll">\n' +
    '                                        <label @click="checkSelectSh(index,\'\',true,\'\')" ></label>\n' +
    '                                        </span>\n' +
    '                                        <span class="text-center flex-day">\n' +
    '                                            {{ item.date }}\n' +
    '                                        </span>\n' +
    '                                    </li>\n' +
    '                                </ul>\n' +
    '                            </li>\n' +
    '                        </ul>\n' +
    '                    </li>\n' +
    '                    <li>\n' +
    '                        <ul class="flex-box">\n' +
    '                            <li class="rol-title">主要诊疗工作</li>\n' +
    '                            <li class="flex-one">\n' +
    '                                <ul class="flex-box-b">\n' +
    '                                    <li v-for="(hsdata,hsindex) in hs.gz">\n' +
    '                                        <ul class="flex-box">\n' +
    '                                            <!---->\n' +
    '                                            <li @contextmenu="show(false,$event,hsindex,\'gz\',dayindex)" :class="{\'item-border\': daydata.gz[ hsindex ] || hsindex == daydata.gz.length}"  class="item" v-for=" ( daydata, dayindex ) in optionlist">\n' +
    '                                                <span class="padd-r-10 item-checked" v-if="daydata.gz[ hsindex ] && ischeck">\n' +
    '                                                    <input class="green" type="checkbox"  v-model="daydata.gz[ hsindex ].isChecked" >\n' +
    '                                                    <label @click="checkSelectSh(hsindex,dayindex,false,\'gz\')" ></label>\n' +
    '                                                </span >\n' +
    '                                                <span v-if="daydata.gz[ hsindex ]"  class="item-backgroundImage" :data-id="daydata.gz[ hsindex ].type" :style="{backgroundImage: \'url(\'+imgage(daydata.gz[ hsindex ].type)+\')\'}"></span>\n' +
    '                                               <span class="middle item-text">{{daydata.gz[ hsindex ]? daydata.gz[ hsindex ].title: \'\'}}</span>\n' +
    '                                            </li>\n' +
    '                                        </ul>\n' +
    '                                    </li>\n' +
    '                                </ul>\n' +
    '                            </li>\n' +
    '                        </ul>\n' +
    '                    </li>\n' +
    '                    <li>\n' +
    '                        <ul class="flex-box">\n' +
    '                            <li class="rol-title">重点医嘱</li>\n' +
    '                            <li class="flex-one">\n' +
    '                                <ul class="flex-box-b">\n' +
    '                                    <li v-for="(hsdata,hsindex) in hs.zdyz">\n' +
    '                                        <ul class="flex-box">\n' +
    '                                            <li @contextmenu="show(false,$event,hsindex,\'zdyz\',dayindex)" :class="{\'item-border\': daydata.zdyz[ hsindex ] || hsindex == daydata.zdyz.length}"  class="item" v-for=" ( daydata, dayindex ) in optionlist">\n' +
    '                                                 <span class="padd-r-10 item-checked" v-if="daydata.zdyz[ hsindex ] && ischeck">\n' +
    '                                                    <input class="green" type="checkbox"  v-model="daydata.zdyz[ hsindex ].isChecked" >\n' +
    '                                                    <label @click="checkSelectSh(hsindex,dayindex,false,\'zdyz\')" ></label>\n' +
    '                                                </span>\n' +
    '                                                <span v-if="daydata.zdyz[ hsindex ]"  class="item-backgroundImage" :data-id="daydata.zdyz[ hsindex ].type" :style="{backgroundImage: \'url(\'+imgage(daydata.zdyz[ hsindex ].type)+\')\'}"></span>\n' +
    '                                                <span class="middle item-text">{{daydata.zdyz[ hsindex ]? daydata.zdyz[ hsindex ].title: \'\'}}</span>\n' +
    '                                            </li>\n' +
    '                                        </ul>\n' +
    '                                    </li>\n' +
    '                                </ul>\n' +
    '                            </li>\n' +
    '                        </ul>\n' +
    '                    </li>\n' +
    '                    <li>\n' +
    '                        <ul class="flex-box">\n' +
    '                            <li class="rol-title">主要护理工作</li>\n' +
    '                            <li class="flex-one">\n' +
    '                                <ul class="flex-box-b">\n' +
    '                                    <li v-for="(hsdata,hsindex) in hs.hlgz">\n' +
    '                                        <ul class="flex-box">\n' +
    '                                            <li @contextmenu="show(false,$event,hsindex,\'hlgz\',dayindex)" :class="{\'item-border\': daydata.hlgz[ hsindex ] || hsindex == daydata.hlgz.length}"  class="item" v-for=" ( daydata, dayindex ) in optionlist">\n' +
    '                                               <span class="padd-r-10 item-checked" v-if="daydata.hlgz[ hsindex ] && ischeck">\n' +
    '                                                    <input class="green" type="checkbox"  v-model="daydata.hlgz[ hsindex ].isChecked" >\n' +
    '                                                    <label @click="checkSelectSh(hsindex,dayindex,false,\'hlgz\')" ></label>\n' +
    '                                                </span>\n' +
    '                                                <span v-if="daydata.hlgz[ hsindex ]"  class="item-backgroundImage" :data-id="daydata.hlgz[ hsindex ].type" :style="{backgroundImage: \'url(\'+imgage(daydata.hlgz[ hsindex ].type)+\')\'}"></span>\n' +
    '                                                <span class="middle item-text">{{daydata.hlgz[ hsindex ]? daydata.hlgz[ hsindex ].title: \'\'}}</span>\n' +
    '                                            </li>\n' +
    '                                        </ul>\n' +
    '                                    </li>\n' +
    '                                </ul>\n' +
    '                            </li>\n' +
    '                        </ul>\n' +
    '                    </li>\n' +
    '                    <li>\n' +
    '                        <ul class="flex-box">\n' +
    '                            <li class="rol-title border-bottom-b">病情变异记录</li>\n' +
    '                            <li class="flex-one">\n' +
    '                                <ul class="flex-box-b">\n' +
    '                                    <li v-for="(hsdata,hsindex) in hs.bqby">\n' +
    '                                        <ul class="flex-box">\n' +
    '                                            <li @contextmenu="show(false,$event,hsindex,\'bqby\',dayindex)" :class="{\'item-border\': daydata.bqby[ hsindex ] || hsindex == daydata.bqby.length,\'border-bottom-b\':hsindex==hs.bqby-1}"  class="item" v-for=" ( daydata, dayindex ) in optionlist">\n' +
    '                                                 <span class="padd-r-10 item-checked" v-if="daydata.bqby[ hsindex ] && ischeck">\n' +
    '                                                    <input class="green" type="checkbox"  v-model="daydata.bqby[ hsindex ].isChecked" >\n' +
    '                                                    <label @click="checkSelectSh(hsindex,dayindex,false,\'bqby\')" ></label>\n' +
    '                                                </span>\n' +
    '                                                <span v-if="daydata.bqby[ hsindex ]"  class="item-backgroundImage" :data-id="daydata.bqby[ hsindex ].type" :style="{backgroundImage: \'url(\'+imgage(daydata.bqby[ hsindex ].type)+\')\'}"></span>\n' +
    '                                                <span class="middle item-text">{{daydata.bqby[ hsindex ]? daydata.bqby[ hsindex ].title: \'\'}}</span>\n' +
    '                                            </li>\n' +
    '                                        </ul>\n' +
    '                                    </li>\n' +
    '                                </ul>\n' +
    '                            </li>\n' +
    '                        </ul>\n' +
    '                    </li>\n' +
    '                </ul>\n' +
    '            </div>\n' +
    '\n' +
    '        </div>\n' +
    '    </div><menu-list @menu-change="menu" :left="left" :top="top" :objList="objList" :hide="hide"></menu-list></div>',
    data: function () {
        return {
            objList: [],
            left: null,
            top: null,
            ischeck: true,
            bximg: '/newzui/pub/image/swjx.png',
            nobximg: '/newzui/pub/image/kwjx.png',
            hide: false,
            flag: null,
            hsIndex: '',
            dayIndex: '',
            xmmc: '',
            hs: {
                gz: 0,
                zdyz: 0,
                hlgz: 0,
                bqby: 0,
            },
        }
    },
    created: function () {
        this.isOverClick();
        this.hsList();
    },
    mounted: function () {
        $(document).on('click', function () {
            hzList.hide = false
        })
    },
    methods: {
        /**
         *
         * @param flag flag==true 判断是天数还是行数
         * @param hsIndex 如果flag==false 那么这就是行数索引
         * @param dayIndex 如果flag==false 那么这就是天数索引
         * @param xmmc 如果flag==false 那么这就是单个项目名称
         */
        menu: function (item) {
            this.$emit('menu-click', [item, this.flag, this.hsIndex, this.dayIndex, this.xmmc])
            this.hide = false
        },
        hsList: function () {
            var that = this
            this.optionlist.forEach(function (br) {
                if (that.hs.gz < br.gz.length) that.hs.gz = br.gz.length
                if (that.hs.zdyz < br.zdyz.length) that.hs.zdyz = br.zdyz.length
                if (that.hs.hlgz < br.hlgz.length) that.hs.hlgz = br.hlgz.length
                if (that.hs.bqby < br.bqby.length) that.hs.bqby = br.bqby.length
            });
        },
        imgage: function (falg) {
            return falg ? this.bximg : this.nobximg
        },
        isOverClick: function (isOver) {
            var that = this
            this.optionlist.forEach(function (br) {
                br.isCheckAll = false;
                br.gz.forEach(function (yz) {
                    yz.isChecked = false;
                });
                br.zdyz.forEach(function (yz) {
                    yz.isChecked = false;
                });
                br.hlgz.forEach(function (yz) {
                    yz.isChecked = false;
                });
                br.bqby.forEach(function (yz) {
                    yz.isChecked = false;
                });
            });
        },
        business: function (event, index) {
        },
        show: function (type, eve, hs, xmmc, day) {
            if (eve.button == 2) {
                eve.preventDefault()
            }
            if (!type) {
                this.objList = this.contextmenutwo
            } else {
                this.objList = this.contextmenuone
            }
            this.flag = type
            this.hsIndex = hs
            this.dayIndex = day
            this.xmmc = xmmc
            this.left = eve.clientX
            this.top = eve.clientY
            this.hide = true
        },
        /**
         *
         * @param xmIndex  type==true 代表天数索引 否则代表项目索引
         * @param dayIndex type==false 代表天数索引
         * @param type type==true 全选，反之单选
         * @param childText 项目名称
         */
        checkSelectSh: function (xmIndex, dayIndex, type, childText) {
            if (type) {
                var isCheckAll = this.optionlist[xmIndex].isCheckAll ? false : true;
                yzshInfo = this.optionList[xmIndex];

                // this.optionList[parentIndex].isCheckAll = isCheckAll;
                Vue.set(this.optionlist[xmIndex], 'isCheckAll', isCheckAll)
                for (var i = 0; i < yzshInfo.gz.length; i++) {
                    this.optionlist[xmIndex].gz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.zdyz.length; i++) {
                    this.optionlist[xmIndex].zdyz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.hlgz.length; i++) {
                    this.optionlist[xmIndex].hlgz[i].isChecked = isCheckAll;
                }
                for (var i = 0; i < yzshInfo.bqby.length; i++) {
                    this.optionlist[xmIndex].bqby[i].isChecked = isCheckAll;
                }
            } else {
                var yzStatus = !this.optionlist[dayIndex][childText][xmIndex].isChecked;
                this.optionlist[dayIndex][childText][xmIndex].isChecked = yzStatus;
                if (yzStatus) {
                    var yzIsOverCk = true;
                    for (var t = 0; t < this.optionlist[dayIndex].gz.length; t++) {
                        if (!this.optionlist[dayIndex]['gz'][t].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var u = 0; u < this.optionlist[dayIndex].zdyz.length; u++) {
                        if (!this.optionlist[dayIndex]['zdyz'][u].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var y = 0; y < this.optionlist[dayIndex].hlgz.length; y++) {
                        if (!this.optionlist[dayIndex]['hlgz'][y].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }
                    for (var r = 0; r < this.optionlist[dayIndex].bqby.length; r++) {
                        if (!this.optionlist[dayIndex]['bqby'][r].isChecked) {
                            yzIsOverCk = false;
                            break;
                        }
                    }

                    this.optionlist[dayIndex].isCheckAll = yzIsOverCk;


                } else {
                    this.optionlist[dayIndex].isCheckAll = false;
                }
            }
            this.$forceUpdate();
        },
    },
})
Vue.component('menu-list', {
    props: ['left', 'top', 'objList', 'hide'],
    template: '<ul v-if="hide" class="absolute" :style="{left: left+\'px\',top:top+\'px\',}">\n' +
    '<li v-for="item in objList" @click="listclick(item)"><span class="icon-Image" :style="{backgroundImage: \'url(\'+item.icon+\')\'}"></span>\n' +
    '<span>{{item.name}}</span></li></ul>',
    data: function () {
        return {}
    },
    methods: {
        listclick: function (item) {
            this.$emit('menu-change', item)
        }
    },
})

Vue.component('table-hover', {
    mixins:[baseFunc],
    props: ['them', 'jsonlist', 'zxlist', 'headtextobj','mc'],
    template: '<div class="box-hover">\n' +
    '        <div class="position skin-default padd-l-10 padd-r-10 padd-t-10">\n' +
    '            <div class="header-title  flex-box">\n' +
    '                <span class="item-backgroundImage" :style="{backgroundImage: \'url(\'+imgage(ischeck)+\')\'}"></span>\n' +
    '                <span class="title-bold">{{jsonlist[headtextobj.title]}}</span>\n' +
    '                <span class="font-14 title-color">{{jsonlist[headtextobj.zlgz]}}</span>\n' +
    '                <span class="font-14 title-color">{{jsonlist[headtextobj.yzl]}}</span>\n' +
    '                <span class="font-14 title-color">{{jsonlist[headtextobj.cqyz]}}</span>\n' +
    '                <span class="font-14 title-color">{{jsonlist[headtextobj.zx]}}</span>\n' +
    '            </div>\n' +
    '            <p class="title-color padd-l-10">{{jsonlist[headtextobj.sm]}}</p>\n' +
    '            <span class="header-cqyz">{{jsonlist[headtextobj.cqyz]}}</span>\n' +
    '        </div>\n' +
    '        <div class="fyxm-tab">\n' +
    '                 <div class="input-border"><span :class="{\'active\':num==0}" @click="tabBg(0)">西药医嘱</span></div>\n' +
    '                   <div class="input-border"><span :class="{\'active\':num==1}" @click="tabBg(1)">中药医嘱</span></div>\n' +
    '                 <div class="input-border"><span :class="{\'active\':num==2}" @click="tabBg(2)">医疗医嘱</span></div>\n' +
    '               <div class="input-border"><span :class="{\'active\':num==3}" @click="tabBg(3)">嘱托医嘱</span></div>\n' +
    '          </div><div class="zui-table-view padd-l-10 padd-r-10">\n' +
    '            <div class="zui-table-header">\n' +
    '                <table class="zui-table">\n' +
    '                    <thead>\n' +
    '                    <tr>\n' +
    '                        <th class="cell-m">\n' +
    '                            <div class="zui-table-cell cell-m"><span>序号</span></div>\n' +
    '                        </th>\n' +
    '                        <th v-for="(item,index) in them">\n' +
    '                            <div class="zui-table-cell cell-s" :class="isClass(item)">{{index}}</div>\n' +
    '                        </th>\n' +
    '                    </tr>\n' +
    '                    </thead>\n' +
    '                </table>\n' +
    '            </div>\n' +
    '            <div class="zui-table-body" style="height: 200px" data-no-change="undefined" @scroll="scrollTable($event)">\n' +
    '                <table class="zui-table">\n' +
    '                    <tbody>\n' +
    '                    <tr v-for="(item, $index) in jsonlist[zxlist]" @mouseenter="hoverMouse(true,$index)"\n' +
    '                        @mouseleave="hoverMouse()" :class="[{\'table-hover\':$index === hoverIndex}]">\n'+
    '                        <td class="cell-m">\n' +
    '                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>\n' +
    '                        </td>\n' +
    '                        <td v-for="v in them">\n' +
    '                            <div class="zui-table-cell cell-s" :class="isClass(v)" v-text="item[v]"></div>\n' +
    '                        </td>\n' +
    '                    </tr>\n' +
    '                    </tbody>\n' +
    '                </table>\n' +
    '            </div><div  class="zui-table-fixed table-fixed-l"> \n' +
    '                <div class="zui-table-header">\n' +
    '                    <table class="zui-table">\n' +
    '                        <thead>\n' +
    '                        <tr><div  class="zui-table-cell cell-m"><span>序号</span></div>\n' +
    '                            </th>\n' +
    '                        </tr>\n' +
    '                        </thead>\n' +
    '                    </table>\n' +
    '                </div>\n' +
    '                <div class="zui-table-body" style="height: 194px"  data-no-change="undefined" @scroll="scrollTableFixed($event)"><table class="zui-table">\n' +
    '                        <tbody>\n' +
    '                        <tr @mouseenter="hoverMouse(true,$index)"\n' +
    '                            @mouseleave="hoverMouse()" v-for="(item, $index) in jsonlist[zxlist]"\n' +
    '                            :class="[{\'table-hover\':$index === hoverIndex}]"\n' +
    '                            class="tableTr2 table-hovers-filexd-l">\n' +
    '                            <td  class="cell-m">\n' +
    '                                <div cell="cell-2-0"  class="zui-table-cell cell-m">{{$index}}</div>\n' +
    '                            </td>\n' +
    '                        </tr>\n' +
    '                        </tbody>\n' +
    '\n' +
    '                    </table>\n' +
    '                </div>\n' +
    '            </div>\n' +
    '        </div>\n' +
    '    </div>',
    data: function () {
        return {
            num: 0,
            hoverIndex: undefined,
            ischeck: true,
            bximg: '/newzui/pub/image/swjx.png',
            nobximg: '/newzui/pub/image/kwjx.png',
        }
    },
    methods: {
        isClass: function (v) {
            return v.indexOf(this.mc) != -1 ? 'text-left' : ''
        },
        hoverMouse: function (type, index) {
            this.hoverIndex = type ? index : undefined;
        },

        imgage: function (falg) {
            return falg ? this.bximg : this.nobximg
        },
        tabBg: function (i) {
            this.num = i
            this.$emit('change-tag', i)
        }
    },
})
