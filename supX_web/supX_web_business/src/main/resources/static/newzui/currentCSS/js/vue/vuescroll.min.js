!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("vue")):"function"==typeof define&&define.amd?define(["vue"],t):e.vuescroll=t(e.Vue)}(this,function(e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function i(e,o){for(var s in o="object"===(void 0===o?"undefined":t(o))&&o||{},e)o[s]="object"===t(e[s])?i(e[s],o[s]={}):e[s];return o}function o(e,s){for(var l in s=s||{},e)"object"===t(e[l])?void 0===s[l]?(s[l]={},i(e[l],s[l])):o(e[l],s[l]):void 0===s[l]&&(s[l]=e[l]);return s}function s(e,t,i,o){var s=null;(i[t]||"function"==typeof i)&&(o=o||t,"function"==typeof i&&(s=i),Object.defineProperty(e,t,{get:s||function(){return i[o]},configurable:!0}))}var l=void 0;var r={refreshDomStyle:!1,loadDomStyle:!1,hide:!1};var n={};function a(e){if(!r[e]){r[e]=!0;var t=document.createElement("style");t.type="text/css",t.innerHTML=n[e],document.getElementsByTagName("HEAD").item(0).appendChild(t)}}function c(e,t,i){var o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"on"==(arguments.length>4&&void 0!==arguments[4]?arguments[4]:"on")?e.addEventListener(t,i,o):e.removeEventListener(t,i,o)}n.refreshDomStyle="\n.vuescroll-refresh {\n    position:absolute;\n    width: 100%;\n    color: black;\n    height: 50px;\n    text-align: center;\n    font-size: 16px;\n    line-height: 50px;\n}\n.vuescroll-refresh svg {\n    margin-right: 10px;\n    width: 25px;\n    height: 25px;\n    vertical-align: sub;\n}\n.vuescroll-refresh svg path,\n.vuescroll-refresh svg rect{\nfill: #FF6700;\n}\n",n.loadDomStyle="\n.vuescroll-load {\n    position:absolute;\n    width: 100%;\n    color: black;\n    height: 50px;\n    text-align: center;\n    font-size: 16px;\n    line-height: 50px;\n}\n.vuescroll-load svg {\n    margin-right: 10px;\n    width: 25px;\n    height: 25px;\n    vertical-align: sub;\n}\n.vuescroll-load svg path,\n.vuescroll-load svg rect{\nfill: #FF6700;\n}\n";var h=function(e){console.error("[vuescroll] "+e)},u=function(e){console.warn("[vuescroll] "+e)};function _(e,t){var i=!1;if(!e||!t)return i;for(;e.parentNode!==t&&9!==e.parentNode.nodeType&&!e.parentNode._isVuescroll;)e=e.parentNode;return e.parentNode==t&&(i=!0),i}var d=/(.*?)px/;function p(e){var t=d.exec(e);return t&&t[1]}function f(){return"ontouchstart"in window}function m(e){var t,i=document.documentElement.style;return e.opera&&"[object Opera]"===Object.prototype.toString.call(opera)?t="presto":"MozAppearance"in i?t="gecko":"WebkitAppearance"in i?t="webkit":"string"==typeof navigator.cpuClass&&(t="trident"),{trident:"ms",gecko:"moz",webkit:"webkit",presto:"O"}[t]}function v(e,t){var i="-"+m(window)+"-"+t,o=document.createElement("div");return o.style[e]=i,o.style[e]==i&&i}function g(){var e=navigator.userAgent.toLowerCase();return-1!==e.indexOf("msie")||-1!==e.indexOf("trident")||-1!==e.indexOf(" edge/")}function b(e,t){return function(e,t){if(e.hasResized)return;var i="display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; padding: 0; margin: 0; opacity: 0; z-index: -1000; pointer-events: none;",o=document.createElement("div");o.style.cssText=i;var s=document.createElement("object");s.style.cssText=i,s.type="text/html",s.tabIndex=-1,s.onload=function(){c(s.contentDocument.defaultView,"resize",t)},g()||(s.data="about:blank");o.isResizeElm=!0,o.appendChild(s),e.appendChild(o),g()&&(s.data="about:blank");return function(){s.contentDocument&&c(s.contentDocument.defaultView,"resize",t,"off"),e.removeChild(o),e.hasResized=!1}}(e,t)}var S=["slide","native","pure-native"],y=function(){},T=["mergedOptions.vuescroll.pullRefresh.tips","mergedOptions.vuescroll.pushLoad.tips","mergedOptions.rail","mergedOptions.bar"],L={vuescroll:{mode:"native",sizeStrategy:"percent",pullRefresh:{enable:!1,tips:{deactive:"Pull to Refresh",active:"Release to Refresh",start:"Refreshing...",beforeDeactive:"Refresh Successfully!"}},pushLoad:{enable:!1,tips:{deactive:"Push to Load",active:"Release to Load",start:"Loading...",beforeDeactive:"Load Successfully!"}},paging:!1,zooming:!0,snapping:{enable:!1,width:100,height:100},scroller:{bouncing:!0,locking:!0,minZoom:.5,maxZoom:3,speedMultiplier:1,penetrationDeceleration:.03,penetrationAcceleration:.08,preventDefault:!0}},scrollPanel:{initialScrollY:!1,initialScrollX:!1,scrollingX:!0,scrollingY:!0,speed:300,easing:void 0},scrollContent:{tag:"div",padding:!1,props:{},attrs:{}},rail:{vRail:{width:"6px",pos:"right",background:"#01a99a",opacity:0},hRail:{height:"6px",pos:"bottom",background:"#01a99a",opacity:0}},bar:{showDelay:500,vBar:{background:"#00a650",keepShow:!1,opacity:1,hover:!1},hBar:{background:"#00a650",keepShow:!1,opacity:1,hover:!1}}};var x={data:function(){return{shouldStopRender:!1,mergedOptions:{vuescroll:{},scrollPanel:{},scrollContent:{},rail:{},bar:{}}}},created:function(){(function(){var e=this;if("vueScroll"===e.$options.name){var t=o(e.$vuescrollConfig,{}),i=o(L,t);e.$options.propsData.ops=e.$options.propsData.ops||{},Object.keys(e.$options.propsData.ops).forEach(function(t){s(e.mergedOptions,t,e.$options.propsData.ops)}),o(i,e.mergedOptions),s(e.mergedOptions.scrollContent,"paddPos",function(){return"padding-"+e.mergedOptions.rail.vRail.pos}),s(e.mergedOptions.scrollContent,"paddValue",function(){return e.mergedOptions.rail.vRail.width})}}).call(this),this.renderError=function(e){var t=!1,i=e.vuescroll,o=e.scrollPanel;~S.indexOf(i.mode)||(h('The vuescroll\'s option "mode" should be one of the '+S),t=!0),i.paging==i.snapping.enable&&i.paging&&(i.pullRefresh||i.pushLoad)&&h("paging, snapping, (pullRefresh with pushLoad) can only one of them to be true.");var s=o.initialScrollY,l=o.initialScrollX;return s&&!String(s).match(/^\d+(\.\d+)?(%)?$/)&&h("The prop `initialScrollY` should be a percent number like 10% or an exact number that greater than or equal to 0 like 100."),l&&!String(l).match(/^\d+(\.\d+)?(%)?$/)&&h("The prop `initialScrollX` should be a percent number like 10% or an exact number that greater than or equal to 0 like 100."),t}(this.mergedOptions)}};function w(e,t){return function(i){return t(e,i)}}function D(e,t){var i=null;return"easeInQuad"===e&&(i=t*t),"easeOutQuad"===e&&(i=t*(2-t)),"easeInOutQuad"===e&&(i=t<.5?2*t*t:(4-2*t)*t-1),"easeInCubic"===e&&(i=t*t*t),"easeOutCubic"===e&&(i=--t*t*t+1),"easeInOutCubic"===e&&(i=t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1),"easeInQuart"===e&&(i=t*t*t*t),"easeOutQuart"===e&&(i=1- --t*t*t*t),"easeInOutQuart"===e&&(i=t<.5?8*t*t*t*t:1-8*--t*t*t*t),"easeInQuint"===e&&(i=t*t*t*t*t),"easeOutQuint"===e&&(i=1+--t*t*t*t*t),"easeInOutQuint"===e&&(i=t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t),i||t}var z=Date.now||function(){return+new Date},O={},P=1,C={effect:{}},E=null;E="undefined"!=typeof window?window:{},C.effect.Animate={requestAnimationFrame:function(e){var t=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||e.oRequestAnimationFrame,i=!!t;if(t&&!/requestAnimationFrame\(\)\s*\{\s*\[native code\]\s*\}/i.test(t.toString())&&(i=!1),i)return function(e,i){t(e,i)};var o={},s=1,l=null,r=+new Date;return function(e){var t=s++;return o[t]=e,requestCount++,null===l&&(l=setInterval(function(){var e=+new Date,t=o;for(var i in o={},requestCount=0,t)t.hasOwnProperty(i)&&(t[i](e),r=e);e-r>2500&&(clearInterval(l),l=null)},1e3/60)),t}}(E),stop:function(e){var t=null!=O[e];return t&&(O[e]=null),t},isRunning:function(e){return null!=O[e]},start:function(e,t,i,o,s,l){var r=z(),n=r,a=0,c=0,h=P++;if(l||(l=document.body),h%20==0){var u={};for(var _ in O)u[_]=!0;O=u}return O[h]=!0,C.effect.Animate.requestAnimationFrame(function u(_){var d=!0!==_,p=z();if(!O[h]||t&&!t(h))return O[h]=null,void(i&&i(60-c/((p-r)/1e3),h,!1));if(d)for(var f=Math.round((p-n)/(1e3/60))-1,m=0;m<Math.min(f,4);m++)u(!0),c++;o&&(a=(p-r)/o)>1&&(a=1);var v=s?s(a):a;!1!==e(v,p,d)&&1!==a||!d?d&&(n=p,C.effect.Animate.requestAnimationFrame(u,l)):(O[h]=null,i&&i(60-c/((p-r)/1e3),h,1===a||null==o))},l),h}};var A={};function B(e,t){var i=void 0;return i=(i=/(-?\d+(?:\.\d+?)?)%$/.exec(e))?t*(i=i[1]-0)/100:e-0}var M={mounted:function(){A[this._uid]=this},beforeDestroy:function(){delete A[this._uid]},methods:{scrollTo:function(e){var t=e.x,i=e.y,o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];t=void 0===t?this.vuescroll.state.internalScrollLeft||0:B(t,this.scrollPanelElm.scrollWidth),i=void 0===i?this.vuescroll.state.internalScrollTop||0:B(i,this.scrollPanelElm.scrollHeight),this.internalScrollTo(t,i,o,s)},scrollBy:function(e){var t=e.dx,i=void 0===t?0:t,o=e.dy,s=void 0===o?0:o,l=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=this.vuescroll.state,n=r.internalScrollLeft,a=void 0===n?0:n,c=r.internalScrollTop,h=void 0===c?0:c;i&&(a+=B(i,this.scrollPanelElm.scrollWidth)),s&&(h+=B(s,this.scrollPanelElm.scrollHeight)),this.internalScrollTo(a,h,l)},zoomBy:function(e,t,i,o,s){"slide"==this.mode?this.scroller.zoomBy(e,t,i,o,s):u("zoomBy and zoomTo are only for slide mode!")},zoomTo:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments[2],o=arguments[3],s=arguments[4];"slide"==this.mode?this.scroller.zoomTo(e,t,i,o,s):u("zoomBy and zoomTo are only for slide mode!")},getCurrentPage:function(){if("slide"==this.mode&&this.mergedOptions.vuescroll.paging)return this.scroller.getCurrentPage();u("getCurrentPage and goToPage are only for slide mode and paging is enble!")},goToPage:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];"slide"==this.mode&&this.mergedOptions.vuescroll.paging?this.scroller.goToPage(e,t):u("getCurrentPage and goToPage are only for slide mode and paging is enble!")},triggerRefreshOrLoad:function(e){if("slide"==this.mode){var t=this.mergedOptions.vuescroll.pullRefresh.enable,i=this.mergedOptions.vuescroll.pushLoad.enable;if("refresh"!=e||t)if("load"!=e||i)if("refresh"===e||"load"===e){if("start"!=this.vuescroll.state[e+"Stage"])return this.scroller.triggerRefreshOrLoad(e),!0}else u("param must be one of load and refresh!");else u("load must be enabled!");else u("refresh must be enabled!")}else u("You can only use triggerRefreshOrLoad in slide mode!")},getCurrentviewDom:function(){for(var e=this,t=("slide"==this.mode||"pure-native"==this.mode?this.scrollPanelElm:this.scrollContentElm).children,i=[],o=function(t){var i=t.getBoundingClientRect(),o=i.left,s=i.top,l=i.width,r=i.height,n=e.$el.getBoundingClientRect(),a=n.left,c=n.top,h=n.height,u=n.width;return o-a+l>0&&o-a<u&&s-c+r>0&&s-c<h},s=0;s<t.length;s++){var l=t.item(s);o(l)&&!l.isResizeElm&&i.push(l)}return i},internalScrollTo:function(e,t,i,o){var s=this;if("native"==this.mode||"pure-native"==this.mode)if(i){!function(e,t,i,o,s,l){var r=e.scrollTop,n=e.scrollLeft,a=n,c=r;r+i<0&&(i=-r);var h=e.scrollHeight;r+i>h&&(i=h-r),n+t<0&&(t=-n),n+t>e.scrollWidth&&(t=e.scrollWidth-n);var u=w(s,D);C.effect.Animate.start(function(o){a=n+t*o,c=r+i*o,e.scrollTop=Math.floor(c),e.scrollLeft=Math.floor(a)},function(){return Math.abs(c-r)<=Math.abs(i)||Math.abs(a-n)<=Math.abs(t)},l,o,u)}(this.$refs.scrollPanel.$el,e-this.$refs.scrollPanel.$el.scrollLeft,t-this.$refs.scrollPanel.$el.scrollTop,this.mergedOptions.scrollPanel.speed,this.mergedOptions.scrollPanel.easing,function(){s.updateBarStateAndEmitEvent("handle-scroll-complete")})}else this.$refs.scrollPanel.$el.scrollTop=t,this.$refs.scrollPanel.$el.scrollLeft=e;else"slide"==this.mode&&this.scroller.scrollTo(e,t,i,void 0,o)},scrollIntoView:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.$el;if("string"==typeof e&&(e=i.querySelector(e)),_(e,i)){var o=this.$el.getBoundingClientRect(),s=o.left,l=o.top,r=e.getBoundingClientRect(),n=s-r.left,a=l-r.top;this.scrollBy({dx:-n,dy:-a},t)}else u("The element or selector you passed is not the element of Vuescroll, please pass the element that is in Vuescroll to scrollIntoView API. ")},refresh:function(){this.refreshInternalStatus()}}},k={methods:{updateNativeModeBarState:function(){var e=this.scrollPanelElm,t=this.$el,i="percent"==this.mergedOptions.vuescroll.sizeStrategy,o=i?t.clientWidth:p(this.vuescroll.state.width),s=i?t.clientHeight:p(this.vuescroll.state.height),l=100*s/e.scrollHeight,r=100*o/e.scrollWidth;this.bar.vBar.state.posValue=100*e.scrollTop/s,this.bar.hBar.state.posValue=100*e.scrollLeft/o,this.bar.vBar.state.size=l<100?l+"%":0,this.bar.hBar.state.size=r<100?r+"%":0}}},$=null,R=null;function X(e,t){for(var i in this.__callback=e,this.options={scrollingX:!0,scrollingY:!0,animating:!0,animationDuration:250,bouncing:!0,locking:!0,paging:!1,snapping:!1,zooming:!1,minZoom:.5,maxZoom:3,speedMultiplier:1,scrollingComplete:y,animatingEasing:"easeOutCubic",noAnimatingEasing:"easeInOutCubic",penetrationDeceleration:.03,penetrationAcceleration:.08},t)this.options[i]=t[i];$=w(this.options.animatingEasing,D),R=w(this.options.noAnimatingEasing,D)}var H={__isSingleTouch:!1,__isTracking:!1,__didDecelerationComplete:!1,__isGesturing:!1,__isDragging:!1,__isDecelerating:!1,__isAnimating:!1,__clientLeft:0,__clientTop:0,__clientWidth:0,__clientHeight:0,__contentWidth:0,__contentHeight:0,__snapWidth:100,__snapHeight:100,__refreshHeight:null,__loadHeight:null,__refreshActive:!1,__refreshActivate:null,__refreshBeforeDeactivate:null,__refreshDeactivate:null,__refreshStart:null,__loadActive:null,__loadActivate:null,__loadBeforeDeactivate:null,__loadDeactivate:null,__loadStart:null,__zoomLevel:1,__scrollLeft:0,__scrollTop:0,__maxScrollLeft:0,__maxScrollTop:0,__scheduledLeft:0,__scheduledTop:0,__scheduledZoom:0,__currentPageX:null,__currentPageY:null,__totalXPage:null,__totalYPage:null,__disable:!1,__lastTouchLeft:null,__lastTouchTop:null,__lastTouchMove:null,__positions:null,__minDecelerationScrollLeft:null,__minDecelerationScrollTop:null,__maxDecelerationScrollLeft:null,__maxDecelerationScrollTop:null,__decelerationVelocityX:null,__decelerationVelocityY:null,setDimensions:function(e,t,i,o){var s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:trye;e===+e&&(this.__clientWidth=e),t===+t&&(this.__clientHeight=t),i===+i&&(this.__contentWidth=i),o===+o&&(this.__contentHeight=o),this.__computeScrollMax(),this.scrollTo(this.__scrollLeft,this.__scrollTop,s)},setPosition:function(e,t){this.__clientLeft=e||0,this.__clientTop=t||0},setSnapSize:function(e,t){this.__snapWidth=e,this.__snapHeight=t},activatePullToRefresh:function(e,t){var i=t.activateCallback,o=t.deactivateCallback,s=t.startCallback,l=t.beforeDeactivateCallback;this.__refreshHeight=e,this.__refreshActivate=i,this.__refreshBeforeDeactivate=l,this.__refreshDeactivate=o,this.__refreshStart=s},activatePushToLoad:function(e,t){var i=t.activateCallback,o=t.deactivateCallback,s=t.startCallback,l=t.beforeDeactivateCallback;this.__loadHeight=e,this.__loadActivate=i,this.__loadBeforeDeactivate=l,this.__loadDeactivate=o,this.__loadStart=s},triggerRefreshOrLoad:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"refresh",t=this.__isDecelerating;t&&(C.effect.Animate.stop(t),this.__isDecelerating=!1),"refresh"==e?(this.__publish(this.__scrollLeft,-this.__refreshHeight,this.__zoomLevel,!0),this.__refreshStart&&(this.__refreshStart(),this.__refreshActive=!0)):(this.__publish(this.__scrollLeft,this.__maxScrollTop+this.__loadHeight,this.__zoomLevel,!0),this.__loadStart&&(this.__loadStart(),this.__loadActive=!0))},finishRefreshOrLoad:function(){var e=this;e.__refreshBeforeDeactivate&&e.__refreshActive?(e.__refreshActive=!1,e.__refreshBeforeDeactivate(function(){e.__refreshDeactivate&&e.__refreshDeactivate(),e.scrollTo(e.__scrollLeft,e.__scrollTop,!0)})):e.__refreshDeactivate&&e.__refreshActive&&(e.__refreshActive=!1,e.__refreshDeactivate(),e.scrollTo(e.__scrollLeft,e.__scrollTop,!0)),e.__loadBeforeDeactivate&&e.__loadActive?(e.__loadActive=!1,e.__loadBeforeDeactivate(function(){e.__loadDeactivate&&e.__loadDeactivate(),e.scrollTo(e.__scrollLeft,e.__scrollTop,!0)})):e.__loadDeactivate&&e.__loadActive&&(e.__loadActive=!1,e.__loadDeactivate(),e.scrollTo(e.__scrollLeft,e.__scrollTop,!0))},getValues:function(){return{left:this.__scrollLeft,top:this.__scrollTop,zoom:this.__zoomLevel}},getScrollMax:function(){return{left:this.__maxScrollLeft,top:this.__maxScrollTop}},zoomTo:function(e,t,i,o,s){if(!this.options.zooming)throw new Error("Zooming is not enabled!");s&&(this.__zoomComplete=s),this.__isDecelerating&&(C.effect.Animate.stop(this.__isDecelerating),this.__isDecelerating=!1);var l=this.__zoomLevel;null==i&&(i=this.__clientWidth/2),null==o&&(o=this.__clientHeight/2),e=Math.max(Math.min(e,this.options.maxZoom),this.options.minZoom),this.__computeScrollMax(e);var r=(i+this.__scrollLeft)*e/l-i,n=(o+this.__scrollTop)*e/l-o;r>this.__maxScrollLeft?r=this.__maxScrollLeft:r<0&&(r=0),n>this.__maxScrollTop?n=this.__maxScrollTop:n<0&&(n=0),this.__publish(r,n,e,t)},zoomBy:function(e,t,i,o,s){this.zoomTo(this.__zoomLevel*e,t,i,o,s)},scrollTo:function(e,t,i,o,s){if(this.__isDecelerating&&(C.effect.Animate.stop(this.__isDecelerating),this.__isDecelerating=!1),null!=o&&o!==this.__zoomLevel){if(!this.options.zooming)throw new Error("Zooming is not enabled!");e*=o,t*=o,this.__computeScrollMax(o)}else o=this.__zoomLevel;this.options.scrollingX||s?this.options.paging?e=Math.round(e/this.__clientWidth)*this.__clientWidth:this.options.snapping&&(e=Math.round(e/this.__snapWidth)*this.__snapWidth):e=this.__scrollLeft,this.options.scrollingY||s?this.options.paging?t=Math.round(t/this.__clientHeight)*this.__clientHeight:this.options.snapping&&(t=Math.round(t/this.__snapHeight)*this.__snapHeight):t=this.__scrollTop,e=Math.max(Math.min(this.__maxScrollLeft,e),0),t=Math.max(Math.min(this.__maxScrollTop,t),0),e===this.__scrollLeft&&t===this.__scrollTop&&(i=!1),this.__isTracking||this.__publish(e,t,o,i)},scrollBy:function(e,t,i){var o=this.__isAnimating?this.__scheduledLeft:this.__scrollLeft,s=this.__isAnimating?this.__scheduledTop:this.__scrollTop;this.scrollTo(o+(e||0),s+(t||0),i)},getCurrentPage:function(){return this.__computePage(),{x:this.__currentPageX,y:this.__currentPageY}},goToPage:function(e,t){var i=e.x,o=e.y;isNaN(i)&&(i=1),isNaN(o)&&(o=1),this.scrollTo((i-1)*this.__clientWidth,(o-1)*this.__clientHeight,t)},doMouseZoom:function(e,t,i,o){var s=e>0?.97:1.03;return this.zoomTo(this.__zoomLevel*s,!1,i-this.__clientLeft,o-this.__clientTop)},doTouchStart:function(e,t){if(null==e.length)throw new Error("Invalid touch list: "+e);if(t instanceof Date&&(t=t.valueOf()),"number"!=typeof t)throw new Error("Invalid timestamp value: "+t);var i,o;this.__interruptedAnimation=!0,this.__isDecelerating&&(C.effect.Animate.stop(this.__isDecelerating),this.__isDecelerating=!1,this.__interruptedAnimation=!0),this.__isAnimating&&(C.effect.Animate.stop(this.__isAnimating),this.__isAnimating=!1,this.__interruptedAnimation=!0);var s=1===e.length;s?(i=e[0].pageX,o=e[0].pageY):(i=Math.abs(e[0].pageX+e[1].pageX)/2,o=Math.abs(e[0].pageY+e[1].pageY)/2),this.__initialTouchLeft=i,this.__initialTouchTop=o,this.__zoomLevelStart=this.__zoomLevel,this.__lastTouchLeft=i,this.__lastTouchTop=o,this.__lastTouchMove=t,this.__lastScale=1,this.__enableScrollX=!s&&this.options.scrollingX,this.__enableScrollY=!s&&this.options.scrollingY,this.__isTracking=!0,this.__didDecelerationComplete=!1,this.__isDragging=!s,this.__isSingleTouch=s,this.__positions=[]},doTouchMove:function(e,t,i){if(null==e.length)throw new Error("Invalid touch list: "+e);if(t instanceof Date&&(t=t.valueOf()),"number"!=typeof t)throw new Error("Invalid timestamp value: "+t);if(this.__isTracking){var o,s;2===e.length?(o=Math.abs(e[0].pageX+e[1].pageX)/2,s=Math.abs(e[0].pageY+e[1].pageY)/2):(o=e[0].pageX,s=e[0].pageY);var l=this.__positions;if(this.__isDragging){var r=o-this.__lastTouchLeft,n=s-this.__lastTouchTop,a=this.__scrollLeft,c=this.__scrollTop,h=this.__zoomLevel;if(null!=i&&this.options.zooming){var u=h;if(h=h/this.__lastScale*i,u!==(h=Math.max(Math.min(h,this.options.maxZoom),this.options.minZoom))){var _=o-this.__clientLeft,d=s-this.__clientTop;a=(_+a)*h/u-_,c=(d+c)*h/u-d,this.__computeScrollMax(h)}}if(this.__enableScrollX){a-=r*this.options.speedMultiplier;var p=this.__maxScrollLeft;(a>p||a<0)&&(this.options.bouncing?a+=r/2*this.options.speedMultiplier:a=a>p?p:0)}if(this.__enableScrollY){c-=n*this.options.speedMultiplier;var f=this.__maxScrollTop;(c>f||c<0)&&(this.options.bouncing?(c+=n/2*this.options.speedMultiplier,this.__enableScrollX||null==this.__refreshHeight&&null==this.__loadHeight||(!this.__refreshActive&&c<=-this.__refreshHeight?(this.__refreshActive=!0,this.__refreshActivate&&this.__refreshActivate()):this.__refreshActive&&c>-this.__refreshHeight?(this.__refreshActive=!1,this.__refreshDeactivate&&this.__refreshDeactivate()):!this.__loadActive&&c>=this.__maxScrollTop+this.__loadHeight?(this.__loadActive=!0,this.__loadActivate&&this.__loadActivate()):this.__refreshActive&&c<this.__maxScrollTop+this.__loadHeight&&(this.__loadActive=!1,this.__loadDeactivate&&this.__loadDeactivate()))):c=c>f?f:0)}l.length>60&&l.splice(0,30),l.push(a,c,t),this.__publish(a,c,h)}else{var m=this.options.locking?3:0,v=Math.abs(o-this.__initialTouchLeft),g=Math.abs(s-this.__initialTouchTop);this.__enableScrollX=this.options.scrollingX&&v>=m,this.__enableScrollY=this.options.scrollingY&&g>=m,l.push(this.__scrollLeft,this.__scrollTop,t),this.__isDragging=(this.__enableScrollX||this.__enableScrollY)&&(v>=5||g>=5),this.__isDragging&&(this.__interruptedAnimation=!1)}this.__lastTouchLeft=o,this.__lastTouchTop=s,this.__lastTouchMove=t,this.__lastScale=i}},doTouchEnd:function(e){if(e instanceof Date&&(e=e.valueOf()),"number"!=typeof e)throw new Error("Invalid timestamp value: "+e);if(this.__isTracking){if(this.__isTracking=!1,this.__isDragging)if(this.__isDragging=!1,this.__isSingleTouch&&this.options.animating&&e-this.__lastTouchMove<=100){for(var t=this.__positions,i=t.length-1,o=i,s=i;s>0&&t[s]>this.__lastTouchMove-100;s-=3)o=s;if(o!==i){var l=t[i]-t[o],r=this.__scrollLeft-t[o-2],n=this.__scrollTop-t[o-1];this.__decelerationVelocityX=r/l*(1e3/60),this.__decelerationVelocityY=n/l*(1e3/60);var a=this.options.paging||this.options.snapping?4:1;Math.abs(this.__decelerationVelocityX)>a||Math.abs(this.__decelerationVelocityY)>a?this.__refreshActive||this.__loadActive||this.__startDeceleration(e):this.__scrollComplete()}else this.__scrollComplete()}else e-this.__lastTouchMove>100&&this.__scrollComplete();this.__isDecelerating||(this.__refreshActive&&this.__refreshStart?(this.__publish(this.__scrollLeft,-this.__refreshHeight,this.__zoomLevel,!0),this.__refreshStart&&this.__refreshStart()):this.__loadActive&&this.__loadStart?(this.__publish(this.__scrollLeft,this.__maxScrollTop+this.__loadHeight,this.__zoomLevel,!0),this.__loadStart&&this.__loadStart()):((this.__interruptedAnimation||this.__isDragging)&&this.__scrollComplete(),this.scrollTo(this.__scrollLeft,this.__scrollTop,!0,this.__zoomLevel),this.__refreshActive?(this.__refreshActive=!1,this.__refreshDeactivate&&this.__refreshDeactivate()):this.__loadActive&&(this.__loadActive=!1,this.__loadDeactivate&&this.__loadDeactivate()))),this.__positions.length=0}},onScroll:y,stop:function(){this.__disable=!0},start:function(){self.__disable=!0},__publish:function(e,t,i,o){var s=this;if(!s.__disable){isNaN(e)&&(e=this.__scrollLeft),isNaN(t)&&(t=this.__scrollTop);var l=s.__isAnimating;if(l&&(C.effect.Animate.stop(l),s.__isAnimating=!1),o&&s.options.animating){s.__scheduledLeft=e,s.__scheduledTop=t,s.__scheduledZoom=i;var r=s.__scrollLeft,n=s.__scrollTop,a=s.__zoomLevel,c=e-r,h=t-n,u=i-a;s.__isAnimating=C.effect.Animate.start(function(e,t,i){i&&(s.__scrollLeft=r+c*e,s.__scrollTop=n+h*e,s.__zoomLevel=a+u*e,s.__callback&&(s.__callback(s.__scrollLeft,s.__scrollTop,s.__zoomLevel),s.onScroll()))},function(e){return s.__isAnimating===e},function(e,t,i){t===s.__isAnimating&&(s.__isAnimating=!1),(s.__didDecelerationComplete||i)&&s.__scrollComplete(),s.options.zooming&&(s.__computeScrollMax(),s.__zoomComplete&&(s.__zoomComplete(),s.__zoomComplete=null))},s.options.animationDuration,l?$:R)}else s.__scheduledLeft=s.__scrollLeft=e,s.__scheduledTop=s.__scrollTop=t,s.__scheduledZoom=s.__zoomLevel=i,s.__callback&&(s.__callback(e,t,i),s.onScroll()),s.options.zooming&&(s.__computeScrollMax(),s.__zoomComplete&&(s.__zoomComplete(),s.__zoomComplete=null))}},__computeScrollMax:function(e){null==e&&(e=this.__zoomLevel),this.__maxScrollLeft=Math.max(this.__contentWidth*e-this.__clientWidth,0),this.__maxScrollTop=Math.max(this.__contentHeight*e-this.__clientHeight,0)},__computePage:function(){var e=this.__clientWidth,t=this.__clientHeight,i=this.__scrollLeft,o=this.__scrollTop;this.__totalXPage=Math.ceil(this.__contentWidth/e),this.__currentPageX=Math.ceil(i/e+1),this.__totalYPage=Math.ceil(this.__contentHeight/t),this.__currentPageY=Math.ceil(o/t+1)},__scrollComplete:function(){this.options.scrollingComplete()},__startDeceleration:function(){var e=this;if(e.options.paging){var t=Math.max(Math.min(e.__scrollLeft,e.__maxScrollLeft),0),i=Math.max(Math.min(e.__scrollTop,e.__maxScrollTop),0),o=e.__clientWidth,s=e.__clientHeight;e.__minDecelerationScrollLeft=Math.floor(t/o)*o,e.__minDecelerationScrollTop=Math.floor(i/s)*s,e.__maxDecelerationScrollLeft=Math.ceil(t/o)*o,e.__maxDecelerationScrollTop=Math.ceil(i/s)*s}else e.__minDecelerationScrollLeft=0,e.__minDecelerationScrollTop=0,e.__maxDecelerationScrollLeft=e.__maxScrollLeft,e.__maxDecelerationScrollTop=e.__maxScrollTop;var l=e.options.snapping?4:.001;e.__isDecelerating=C.effect.Animate.start(function(t,i,o){e.__stepThroughDeceleration(o)},function(){var t=Math.abs(e.__decelerationVelocityX)>=l||Math.abs(e.__decelerationVelocityY)>=l;return t||(e.__didDecelerationComplete=!0),t},function(){e.__isDecelerating&&(e.__isDecelerating=!1,e.__didDecelerationComplete&&e.__scrollComplete(),e.scrollTo(e.__scrollLeft,e.__scrollTop,e.options.snapping))})},__stepThroughDeceleration:function(e){var t=this.__scrollLeft+this.__decelerationVelocityX,i=this.__scrollTop+this.__decelerationVelocityY;if(!this.options.bouncing){var o=Math.max(Math.min(this.__maxDecelerationScrollLeft,t),this.__minDecelerationScrollLeft);o!==t&&(t=o,this.__decelerationVelocityX=0);var s=Math.max(Math.min(this.__maxDecelerationScrollTop,i),this.__minDecelerationScrollTop);s!==i&&(i=s,this.__decelerationVelocityY=0)}if(e?this.__publish(t,i,this.__zoomLevel):(this.__scrollLeft=t,this.__scrollTop=i),!this.options.paging){this.__decelerationVelocityX*=.95,this.__decelerationVelocityY*=.95}if(this.options.bouncing){var l=0,r=0,n=this.options.penetrationDeceleration,a=this.options.penetrationAcceleration;t<this.__minDecelerationScrollLeft?l=this.__minDecelerationScrollLeft-t:t>this.__maxDecelerationScrollLeft&&(l=this.__maxDecelerationScrollLeft-t),i<this.__minDecelerationScrollTop?r=this.__minDecelerationScrollTop-i:i>this.__maxDecelerationScrollTop&&(r=this.__maxDecelerationScrollTop-i),0!==l&&(l*this.__decelerationVelocityX<=0?this.__decelerationVelocityX+=l*n:this.__decelerationVelocityX=l*a),0!==r&&(r*this.__decelerationVelocityY<=0?this.__decelerationVelocityY+=r*n:this.__decelerationVelocityY=r*a)}}};for(var Y in H)X.prototype[Y]=H[Y];function V(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var W=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])}return e};var N={vertical:{bar:{size:"height",opsSize:"width",posName:"top",opposName:"bottom",page:"pageY",scroll:"scrollTop",scrollSize:"scrollHeight",offset:"offsetHeight",client:"clientY"},axis:"Y"},horizontal:{bar:{size:"width",opsSize:"height",posName:"left",opposName:"right",page:"pageX",scroll:"scrollLeft",scrollSize:"scrollWidth",offset:"offsetWidth",client:"clientX"},axis:"X"}};function I(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}var j={},q=/rgb\(/,Z=/rgb\((.*)\)/;function F(e,t,i){var o="vertical"===i?"Y":"X",s=i.charAt(0)+"Bar",l=i.charAt(0)+"Rail";return!t.bar[s].state.size||!t.mergedOptions.scrollPanel["scrolling"+o]||"pure-native"==t.mode||t.refreshLoad&&"vertical"!==i&&"slide"===t.mode?null:e("bar",{props:{type:i,ops:{bar:t.mergedOptions.bar[s],rail:t.mergedOptions.rail[l]},state:t.bar[s].state},on:{setBarClick:t.setBarClick},ref:i+"Bar"})}function Q(t,i){var o={ref:"scrollPanel",style:{position:"relative",height:"100%"},nativeOn:{scroll:i.handleScroll},props:{ops:i.mergedOptions.scrollPanel}};if("native"==i.mode){i.mergedOptions.scrollPanel.scrollingY?o.style.overflowY=i.bar.vBar.state.size?"scroll":"":o.style.overflowY="hidden",i.mergedOptions.scrollPanel.scrollingX?o.style.overflowX=i.bar.hBar.state.size?"scroll":"":o.style.overflowX="hidden";var s=function(){if(e.prototype.$isServer)return 0;if(void 0!==l)return l;var t=document.createElement("div");t.style.visibility="hidden",t.style.width="100px",t.style.position="absolute",t.style.top="-9999px",document.body.appendChild(t);var i=t.offsetWidth;t.style.overflow="scroll";var o=document.createElement("div");o.style.width="100%",t.appendChild(o);var s=o.offsetWidth;return t.parentNode.removeChild(t),l=i-s}();s?(i.bar.vBar.state.size&&i.mergedOptions.scrollPanel.scrollingY&&(o.style.marginRight="-"+s+"px"),i.bar.hBar.state.size&&i.mergedOptions.scrollPanel.scrollingX&&(o.style.height="calc(100% + "+s+"px)")):(!function(){if(!r.hide){r.hide=!0;var e=document.createElement("style");e.type="text/css",e.innerHTML=".vuescroll-panel::-webkit-scrollbar{width:0;height:0}",document.getElementsByTagName("HEAD").item(0).appendChild(e)}}(),o.style.height="100%"),o.style.transformOrigin="",o.style.transform=""}else if("slide"==i.mode){o.style.transformOrigin="left top 0px",o.style.userSelect="none",o.style.height="",o.style["box-sizing"]="border-box",o.style["min-width"]="100%",o.style["min-height"]="100%";var n=v("width","fit-content");n?o.style.width=n:o.display="inline-block"}else"pure-native"==i.mode&&(o.style.width="100%",i.mergedOptions.scrollPanel.scrollingY?o.style.overflowY="auto":o.style.overflowY="hidden",i.mergedOptions.scrollPanel.scrollingX?o.style.overflowX="auto":o.style.overflowX="hidden");return t("scrollPanel",o,[function(e,t){if("native"==e.mode)return[function(e,t){return e("scrollContent",{props:{ops:t.mergedOptions.scrollContent}},[[t.$slots.default]])}(t,e)];if("slide"==e.mode){var i=[e.$slots.default];if(e.mergedOptions.vuescroll.pullRefresh.enable){a("refreshDomStyle");var o=null;o=G(t,e,"refresh"),i.unshift(t("div",{class:"vuescroll-refresh",ref:"refreshDom",key:"refshDom"},[[o,e.pullRefreshTip]]))}if(e.mergedOptions.vuescroll.pushLoad.enable){a("loadDomStyle");var s=null;s=G(t,e,"load"),i.push(t("div",{class:"vuescroll-load",ref:"loadDom",key:"loadDom"},[[s,e.pushLoadTip]]))}return i}if("pure-native"==e.mode)return[e.$slots.default]}(i,t)])}function G(e,t,i){var o=t.vuescroll.state[i+"Stage"],s=null;if(s=t.$slots[i+"-"+o])return s[0];switch(o){case"deactive":s=e("svg",{attrs:{version:"1.1",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",x:"0px",y:"0px",viewBox:"0 0 1000 1000","enable-background":"new 0 0 1000 1000",xmlSpace:"preserve"}},[e("metadata",[" Svg Vector Icons : http://www.sfont.cn "]),e("g",[e("g",{attrs:{transform:"matrix(1 0 0 -1 0 1008)"}},[e("path",{attrs:{d:"M10,543l490,455l490-455L885,438L570,735.5V18H430v717.5L115,438L10,543z"}})])])]);break;case"start":if(g()){s=null;break}s=e("svg",{attrs:{version:"1.1",id:"loader-1",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",x:"0px",y:"0px",viewBox:"0 0 50 50",xmlSpace:"preserve"},style:"enable-background:new 0 0 50 50;"},[e("path",{attrs:{fill:"#000",d:"M43.935,25.145c0-10.318-8.364-18.683-18.683-18.683c-10.318,0-18.683,8.365-18.683,18.683h4.068c0-8.071,6.543-14.615,14.615-14.615c8.072,0,14.615,6.543,14.615,14.615H43.935z"}},[e("animateTransform",{attrs:{attributeType:"xml",attributeName:"transform",type:"rotate",from:"0 25 25",to:"360 25 25",dur:"0.6s",repeatCount:"indefinite"}})])]);break;case"active":s=e("svg",{attrs:{version:"1.1",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",x:"0px",y:"0px",viewBox:"0 0 1000 1000","enable-background":"new 0 0 1000 1000",xmlSpace:"preserve"}},[e("metadata",[" Svg Vector Icons : http://www.sfont.cn "]),e("g",[e("g",{attrs:{transform:"matrix(1 0 0 -1 0 1008)"}},[e("path",{attrs:{d:"M500,18L10,473l105,105l315-297.5V998h140V280.5L885,578l105-105L500,18z"}})])])])}return s}var J={name:"vueScroll",components:{bar:{name:"bar",props:{ops:{type:Object,required:!0},state:{type:Object,required:!0},type:{type:String,required:!0}},computed:{bar:function(){return N[this.type].bar},axis:function(){return N[this.type].axis},parent:function(){return this.$parent.$refs}},render:function(e){var t,i,o=this,s=o.$parent.$refs,l=function(e,t){var i=e+"&"+t;if(j[i])return j[i];var o=document.createElement("div");o.style.background=e,document.body.appendChild(o);var s=window.getComputedStyle(o).backgroundColor;return document.body.removeChild(o),q.test(s)?j[i]="rgba("+Z.exec(s)[1]+", "+t+")":e}(o.ops.rail.background,o.ops.rail.opacity),r={style:(I(t={},o.bar.posName,0),I(t,o.bar.opsSize,"100%"),I(t,o.bar.size,o.state.size),I(t,"borderRadius","inherit"),I(t,"background",o.ops.bar.background),I(t,"opacity",o.state.opacity),I(t,"transform","translate"+N[o.type].axis+"("+o.state.posValue+"%)"),I(t,"cursor","pointer"),I(t,"position","relative"),I(t,"transition","opacity .5s"),I(t,"userSelect","none"),t),class:"vuescroll-"+o.type+"-bar",ref:"inner",on:{}};return o.ops.bar.hover&&(r.on.mouseenter=function(){o.$el.style.background=o.ops.hover},r.on.mouseleave=function(){o.$el.style.background=o.ops.background}),f()?r.on.touchstart=function(e){function t(t){if(e.axisStartPos){var i=(t.touches[0][e.bar.client]-e.$el.getBoundingClientRect()[e.bar.posName]-e.axisStartPos)/e.$el[e.bar.offset];e.$parent.scrollTo(I({},e.axis.toLowerCase(),e.parent.scrollPanel.$el[e.bar.scrollSize]*i),!1)}}function i(){e.$emit("setBarClick",!1),document.onselectstart=null,e.$parent.hideBar(),e.axisStartPos=0,c(document,"touchmove",t,!1,"off"),c(document,"touchend",i,!1,"off")}return function(o){o.stopImmediatePropagation(),o.preventDefault(),document.onselectstart=function(){return!1},e.axisStartPos=o.touches[0][e.bar.client]-e.$refs.inner.getBoundingClientRect()[e.bar.posName],e.$emit("setBarClick",!0),c(document,"touchmove",t),c(document,"touchend",i)}}(this):r.on.mousedown=function(e){function t(t){if(e.axisStartPos){var i=(t[e.bar.client]-e.$el.getBoundingClientRect()[e.bar.posName]-e.axisStartPos)/e.$el[e.bar.offset];e.$parent.scrollTo(I({},e.axis.toLowerCase(),e.parent.scrollPanel.$el[e.bar.scrollSize]*i),!1)}}function i(){e.$emit("setBarClick",!1),document.onselectstart=null,e.$parent.hideBar(),e.axisStartPos=0,c(document,"mousemove",t,!1,"off"),c(document,"mouseup",i,!1,"off")}return function(o){o.stopImmediatePropagation(),document.onselectstart=function(){return!1},e.axisStartPos=o[e.bar.client]-e.$refs.inner.getBoundingClientRect()[e.bar.posName],e.$emit("setBarClick",!0),c(document,"mousemove",t),c(document,"mouseup",i)}}(this),e("div",{class:"vuescroll-"+o.type+"-rail",style:(i={position:"absolute",zIndex:1,borderRadius:o.ops.rail[o.bar.opsSize],background:l},I(i,o.bar.opsSize,o.ops.rail[o.bar.opsSize]),I(i,o.bar.posName,"2px"),I(i,o.bar.opposName,"2px"),I(i,o.ops.rail.pos,"2px"),i),on:{click:function(e){!function(e,t,i,o,s){var l=t.client,r=t.offset,n=t.posName,a=t.scrollSize,c=i[o+"Bar"].$refs.inner[r],h=(e[l]-e.currentTarget.getBoundingClientRect()[n]-c/2)/e.currentTarget[r],u=i.scrollPanel.$el[a]*h;s.scrollTo(I({},N[o].axis.toLowerCase(),u))}(e,o.bar,s,o.type,o.$parent)}}},[e("div",r)])}},scrollContent:{name:"scrollContent",functional:!0,props:{ops:{type:Object},state:{type:Object,default:function(){return{}}}},render:function(e,t){var i=t.props,s=t.slots,l=o(i.state.style,{});l.position="relative",l["min-width"]="100%",l["min-height"]="100%";var r=v("width","fit-content");return r?l.width=r:l.display="inline-block",l.boxSizing="border-box",i.ops.padding&&(l[i.ops.paddPos]=i.ops.paddValue),e(i.ops.tag,{style:l,ref:"scrollContent",class:"vuescroll-content",props:i.ops.props,attrs:i.ops.attrs},s().default)}},scrollPanel:{name:"scrollPanel",props:{ops:{type:Object,required:!0}},methods:{updateInitialScroll:function(){var e=0,t=0;this.ops.initialScrollX&&(e=this.ops.initialScrollX),this.ops.initialScrollY&&(t=this.ops.initialScrollY),(e||t)&&this.$parent.scrollTo({x:e,y:t})}},mounted:function(){var e=this;setTimeout(function(){e._isDestroyed||e.updateInitialScroll()},0)},render:function(e){return e("div",{class:["vuescroll-panel"]},[[this.$slots.default]])}}},props:{ops:{type:Object}},mixins:[x,M,k,{methods:{updateScroller:function(){var e=this.$el.clientWidth,t=this.$el.clientHeight,i=this.scrollPanelElm.scrollWidth,o=this.scrollPanelElm.scrollHeight,s=0;if(this.mergedOptions.vuescroll.pullRefresh.enable){var l=this.$refs.refreshDom.elm||this.$refs.refreshDom;s=l.offsetHeight,l.style.marginTop||(l.style.marginTop=-s+"px")}this.mergedOptions.vuescroll.pushLoad.enable&&(o-=(this.$refs.loadDom.elm||this.$refs.loadDom).offsetHeight);this.scroller.setDimensions(e,t,i,o,!1)},registryScroller:function(){var e=this,t=this.mergedOptions.vuescroll.scroller.preventDefault,i=this.mergedOptions.vuescroll.paging,o=this.mergedOptions.vuescroll.snapping.enable,s=!this.refreshLoad&&!i&&!o&&this.mergedOptions.vuescroll.zooming,l=this.mergedOptions.scrollPanel,r=l.scrollingY,n=l.scrollingX;this.scroller=new X(function(e,t,i,o){var s=null,l=null;"string"==typeof e&&(l="vertical"==e?(s=0)||o:(s=o)&&0);var r=m(t),n=document.createElement("div"),a=r+"Perspective";return void 0!==n.style[a]?"string"==typeof e?V({},"transform","translate3d("+s+i+","+l+i+",0)"):function(t,o,s){e.style.transform="translate3d("+-t+i+","+-o+i+",0) scale("+s+")"}:void 0!==n.style.transform?"string"==typeof e?V({},"transform","translate("+s+i+","+l+i+")"):function(t,o,s){e.style.transform="translate("+-t+i+","+-o+i+") scale("+s+")"}:void 0}(this.scrollPanelElm,window,"px"),W({},this.mergedOptions.vuescroll.scroller,{zooming:s,scrollingY:r,scrollingX:n&&!this.refreshLoad,animationDuration:this.mergedOptions.scrollPanel.speed,paging:i,snapping:o,scrollingComplete:function(){e.updateBarStateAndEmitEvent("handle-scroll-complete")}})),o&&this.scroller.setSnapSize(this.mergedOptions.vuescroll.snapping.width,this.mergedOptions.vuescroll.snapping.height);var a=this.$el.getBoundingClientRect();this.scroller.setPosition(a.left+this.$el.clientLeft,a.top+this.$el.clientTop);var c=function(e,t,i,o,s){var l=null;function r(e){e.touches[0]&&e.touches[0].target&&e.touches[0].target.tagName.match(/input|textarea|select/i)||(i("mousedown"),t.doTouchStart(e.touches,e.timeStamp),s&&e.preventDefault(),document.addEventListener("touchmove",n,{passive:!1}))}function n(e){i("mousemove"),t.doTouchMove(e.touches,e.timeStamp,e.scale),e.preventDefault()}function a(e){i("mouseup"),t.doTouchEnd(e.timeStamp),document.removeEventListener("touchmove",n)}function c(e){t.doTouchEnd(e.timeStamp)}function h(e){e.target.tagName.match(/input|textarea|select/i)||(i("mousedown"),t.doTouchStart([{pageX:e.pageX,pageY:e.pageY}],e.timeStamp),s&&e.preventDefault(),p=!0)}function u(e){p&&(i("mousemove"),t.doTouchMove([{pageX:e.pageX,pageY:e.pageY}],e.timeStamp),p=!0)}function _(e){p&&(i("mouseup"),t.doTouchEnd(e.timeStamp),p=!1)}function d(e){t.doMouseZoom(e.detail?-120*e.detail:e.wheelDelta,e.timeStamp,e.pageX,e.pageY)}if("ontouchstart"in window)e.addEventListener("touchstart",r,!1),document.addEventListener("touchend",a,!1),document.addEventListener("touchcancel",c,!1),l=function(){e.removeEventListener("touchstart",r,!1),document.removeEventListener("touchend",a,!1),document.removeEventListener("touchcancel",c,!1)};else{var p=!1;e.addEventListener("mousedown",h,!1),document.addEventListener("mousemove",u,!1),document.addEventListener("mouseup",_,!1),o&&e.addEventListener(navigator.userAgent.indexOf("Firefox")>-1?"DOMMouseScroll":"mousewheel",d,!1),l=function(){e.removeEventListener("mousedown",h,!1),document.removeEventListener("mousemove",u,!1),document.removeEventListener("mouseup",_,!1),e.removeEventListener(navigator.userAgent.indexOf("Firefox")>-1?"DOMMouseScroll":"mousewheel",d,!1)}}return t.onScroll=function(){i("onscroll")},l}(this.$el,this.scroller,function(t){switch(t){case"mousedown":e.vuescroll.state.isDragging=!0;break;case"onscroll":e.handleScroll(!1);break;case"mouseup":e.vuescroll.state.isDragging=!1}},s,t);return this.mergedOptions.vuescroll.pullRefresh.enable&&this.registryEvent("refresh"),this.mergedOptions.vuescroll.pushLoad.enable&&this.registryEvent("load"),this.updateScroller(),c},updateSlideModeBarState:function(){var e,t,i=this.$el,o=this.scroller,s=0,l=0,r=this.$el.clientHeight,n=this.$el.clientHeight,a=r+this.scroller.__maxScrollLeft,c=n+this.scroller.__maxScrollTop,h=r<a&&this.mergedOptions.scrollPanel.scrollingX,u=n<c&&this.mergedOptions.scrollPanel.scrollingY;h&&(o.__scrollLeft<0?s=-o.__scrollLeft:o.__scrollLeft>o.__maxScrollLeft&&(s=o.__scrollLeft-o.__maxScrollLeft)),u&&(o.__scrollTop<0?l=-o.__scrollTop:o.__scrollTop>o.__maxScrollTop&&(l=o.__scrollTop-o.__maxScrollTop)),e=100*n/(c+l),t=100*r/(a+s);var _=Math.min(Math.max(0,o.__scrollTop),o.__maxScrollTop),d=Math.min(Math.max(0,o.__scrollLeft),o.__maxScrollLeft);this.bar.vBar.state.posValue=100*(_+l)/i.clientHeight,this.bar.hBar.state.posValue=100*(d+s)/i.clientWidth,o.__scrollLeft<0&&(this.bar.hBar.state.posValue=0),o.__scrollTop<0&&(this.bar.vBar.state.posValue=0),this.bar.vBar.state.size=e<100?e+"%":0,this.bar.hBar.state.size=t<100?t+"%":0},registryEvent:function(e){var t="refresh"==e?"refreshDom":"loadDom",i="refresh"==e?this.scroller.activatePullToRefresh:this.scroller.activatePushToLoad,o="refresh"==e?"refreshStage":"loadStage",s=this.$refs[t].elm||this.$refs[t],l=function(e,t,i,o){var s=i.$listeners,l=function(){i.vuescroll.state[t]="active"},r=function(){i.vuescroll.state[t]="deactive"},n=function(){i.vuescroll.state[t]="start",setTimeout(function(){i.scroller.finishRefreshOrLoad()},2e3)},a=function(e){i.vuescroll.state[t]="beforeDeactive",setTimeout(function(){e()},500)};return s[e+"-activate"]&&(l=function(){i.vuescroll.state[t]="active",i.$emit(e+"-activate",i,o)}),s[e+"-before-deactivate"]&&(a=function(s){i.vuescroll.state[t]="beforeDeactive",i.$emit(e+"-before-deactivate",i,o,s.bind(i.scroller))}),s[e+"-deactivate"]&&(r=function(){i.vuescroll.state[t]="deactive",i.$emit(e+"-deactivate",i,o)}),s[e+"-start"]&&(n=function(){i.vuescroll.state[t]="start",i.$emit(e+"-start",i,o,i.scroller.finishRefreshOrLoad.bind(i.scroller))}),{activateCallback:l,deactivateCallback:r,startCallback:n,beforeDeactivateCallback:a}}(e,o,this,s),r=s.offsetHeight;i.bind(this.scroller)(r,l)}}}],mounted:function(){var e=this;this.renderError||(this.initVariables(),this.initWatchOpsChange(),this.refreshInternalStatus(),this.$nextTick(function(){e._isDestroyed||(e.updateBarStateAndEmitEvent(),e.scrollToAnchor())},0))},beforeDestroy:function(){this.destroyParentDomResize&&(this.destroyParentDomResize(),this.destroyParentDomResize=null),this.destroyResize&&(this.destroyResize(),this.destroyResize=null)},data:function(){return{vuescroll:{state:{isDragging:!1,isClickingBar:!1,pointerLeave:!0,internalScrollTop:0,internalScrollLeft:0,posX:null,posY:null,refreshStage:"deactive",loadStage:"deactive",height:"100%",width:"100%"}},bar:{vBar:{state:{posValue:0,size:0,opacity:0}},hBar:{state:{posValue:0,size:0,opacity:0}},renderError:!1}}},render:function(e){var t=this;if(t.renderError)return e("div",[[t.$slots.default]]);var i={style:{position:"relative",height:t.vuescroll.state.height,width:t.vuescroll.state.width,padding:0,overflow:"hidden"},class:"vue-scroll"};return f()?i.on={touchstart:function(){t.vuescroll.state.pointerLeave=!1,t.updateBarStateAndEmitEvent()},touchend:function(){t.vuescroll.state.pointerLeave=!0,t.hideBar()},touchmove:function(){t.vuescroll.state.pointerLeave=!1,t.updateBarStateAndEmitEvent()}}:i.on={mouseenter:function(){t.vuescroll.state.pointerLeave=!1,t.updateBarStateAndEmitEvent()},mouseleave:function(){t.vuescroll.state.pointerLeave=!0,t.hideBar()},mousemove:function(){t.vuescroll.state.pointerLeave=!1,t.updateBarStateAndEmitEvent()}},e("div",i,[Q(e,t),F(e,t,"vertical"),F(e,t,"horizontal")])},computed:{scrollPanelElm:function(){return this.$refs.scrollPanel.$el},scrollContentElm:function(){return this.$refs.scrollContent._isVue?this.$refs.scrollContent.$el:this.$refs.scrollContent},mode:function(){return this.mergedOptions.vuescroll.mode},pullRefreshTip:function(){return this.mergedOptions.vuescroll.pullRefresh.tips[this.vuescroll.state.refreshStage]},pushLoadTip:function(){return this.mergedOptions.vuescroll.pushLoad.tips[this.vuescroll.state.loadStage]},refreshLoad:function(){return this.mergedOptions.vuescroll.pullRefresh.enable||this.mergedOptions.vuescroll.pushLoad.enable}},methods:{updateBarStateAndEmitEvent:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("native"==this.mode||"pure-native"==this.mode)this.updateNativeModeBarState();else if("slide"==this.mode){if(!this.scroller)return;this.updateSlideModeBarState()}e&&this.emitEvent(e,t),this.showAndDefferedHideBar()},updateMode:function(){var e=this.vuescroll.state.internalScrollLeft,t=this.vuescroll.state.internalScrollTop;this.destroyScroller&&(this.scroller.stop(),this.destroyScroller(),this.destroyScroller=null),"slide"==this.mode?this.destroyScroller=this.registryScroller():"native"!=this.mode&&"pure-native"!=this.mode||(this.scrollPanelElm.style.transform="",this.scrollPanelElm.style.transformOrigin=""),this.scrollTo({x:e,y:t},!1,!0)},handleScroll:function(e){this.recordCurrentPos(),this.updateBarStateAndEmitEvent("handle-scroll",e)},setBarClick:function(e){this.vuescroll.state.isClickingBar=e},showAndDefferedHideBar:function(){var e=this;this.showBar(),this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0),this.timeoutId=setTimeout(function(){e.timeoutId=0,e.hideBar()},this.mergedOptions.bar.showDelay)},emitEvent:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=this.scrollPanelElm,o=i.scrollHeight,s=i.scrollWidth,l=i.clientHeight,r=i.clientWidth,n=i.scrollTop,a=i.scrollLeft,c={type:"vertical"},h={type:"horizontal"};"slide"==this.mode&&(o=this.scroller.__contentHeight,s=this.scroller.__contentWidth,n=this.scroller.__scrollTop,a=this.scroller.__scrollLeft,l=this.$el.clientHeight,r=this.$el.clientWidth),c.process=Math.min(n/(o-l),1),h.process=Math.min(a/(s-r),1),c.barSize=this.bar.vBar.state.size,h.barSize=this.bar.hBar.state.size,c.scrollTop=n,h.scrollLeft=a,c.directionY=this.vuescroll.state.posY,h.directionX=this.vuescroll.state.posX,this.$emit(e,c,h,t)},showBar:function(){this.bar.vBar.state.opacity=this.mergedOptions.bar.vBar.opacity,this.bar.hBar.state.opacity=this.mergedOptions.bar.hBar.opacity},hideBar:function(){this.vuescroll.state.isDragging||(this.mergedOptions.bar.vBar.keepShow||this.vuescroll.state.isClickingBar||!this.vuescroll.state.pointerLeave||(this.bar.vBar.state.opacity=0),this.mergedOptions.bar.hBar.keepShow||this.vuescroll.state.isClickingBar||!this.vuescroll.state.pointerLeave||(this.bar.hBar.state.opacity=0))},registryResize:function(){var e=this;this.destroyResize&&this.destroyResize();var t=null;"slide"==this.mode||"pure-native"==this.mode?t=this.scrollPanelElm:"native"==this.mode&&(t=this.scrollContentElm);var i=function(){e.updateBarStateAndEmitEvent(),"slide"==e.mode&&e.updateScroller()};window.addEventListener("resize",i,!1);var o=b(t,function(){var t={};"slide"==e.mode?(e.updateScroller(),t.width=e.scroller.__contentWidth,t.height=e.scroller.__contentHeight):"native"!=e.mode&&"pure-native"!=e.mode||(t.width=e.scrollPanelElm.scrollWidth,t.height=e.scrollPanelElm.scrollHeight),e.updateBarStateAndEmitEvent("handle-resize",t)});this.destroyResize=function(){window.removeEventListener("resize",i,!1),o()}},registryParentResize:function(){this.destroyParentDomResize=b(this.$el.parentNode,this.useNumbericSize)},useNumbericSize:function(){var e=this.$el.parentNode,t=e.style.position;t&&"static"!=t||(this.$el.parentNode.style.position="relative"),this.vuescroll.state.height=e.offsetHeight+"px",this.vuescroll.state.width=e.offsetWidth+"px"},usePercentSize:function(){this.vuescroll.state.height="100%",this.vuescroll.state.width="100%"},setVsSize:function(){"number"==this.mergedOptions.vuescroll.sizeStrategy?(this.useNumbericSize(),this.registryParentResize()):"percent"==this.mergedOptions.vuescroll.sizeStrategy&&(this.destroyParentDomResize&&(this.destroyParentDomResize(),this.destroyParentDomResize=null),this.usePercentSize())},recordCurrentPos:function(){var e=this.mode;this.mode!==this.lastMode&&(e=this.lastMode,this.lastMode=this.mode);var t=this.vuescroll.state,i=function(e,t){var i={};switch(e){case"native":case"pure-native":i={x:t.scrollPanelElm.scrollLeft,y:t.scrollPanelElm.scrollTop};break;case"slide":i={x:t.scroller.__scrollLeft,y:t.scroller.__scrollTop}}return i}(e,this),o=t.internalScrollLeft,s=t.internalScrollTop;t.posX=o-i.x>0?"right":o-i.x<0?"left":null,t.posY=s-i.y>0?"up":s-i.y<0?"down":null,t.internalScrollLeft=i.x,t.internalScrollTop=i.y},refreshInternalStatus:function(){this.setVsSize(),this.registryResize(),this.updateMode(),this.updateBarStateAndEmitEvent()},initWatchOpsChange:function(){var e=this,t={deep:!0,sync:!0};this.$watch("mergedOptions",function(){e.recordCurrentPos(),setTimeout(function(){if(1==e.isSmallChangeThisTick)return e.isSmallChangeThisTick=!1,void e.updateBarStateAndEmitEvent();e.refreshInternalStatus()},0)},t),T.forEach(function(i){e.$watch(i,function(){e.isSmallChangeThisTick=!0},t)})},scrollToAnchor:function(){var e=window.location.hash;if(e&&(!(e=e.slice(e.lastIndexOf("#")))||function(e){return/^#[a-zA-Z_]\d*$/.test(e)}(e))){var t=document.querySelector(e);!_(t,this.$el)||this.mergedOptions.scrollPanel.initialScrollY||this.mergedOptions.scrollPanel.initialScrollX||this.scrollIntoView(t)}},initVariables:function(){this.lastMode=this.mode,this.$el._isVuescroll=!0}}},K={install:function(e){e.component(J.name,J),e.prototype.$vuescrollConfig=o(L,{})},version:"4.6.5",refreshAll:function(){for(var e in A)A[e].refresh()}};return"undefined"!=typeof window&&window.Vue&&e.use(K),K});