/*!
 * j<PERSON><PERSON><PERSON> Plugin v1.4.1 
 */
(function (c) { "function" === typeof define && define.amd ? define(["jquery"], c) : "object" === typeof exports ? c(require("jquery")) : c(jQuery) })(function (c) {
    function n(a) { a = e.json ? JSON.stringify(a) : String(a); return e.raw ? a : encodeURIComponent(a) } function m(a, d) { var b; if (e.raw) b = a; else a: { 0 === a.indexOf('"') && (a = a.slice(1, -1).replace(/\\"/g, '"').replace(/\\\\/g, "\\")); try { a = decodeURIComponent(a.replace(l, " ")); b = e.json ? JSON.parse(a) : a; break a } catch (k) { } b = void 0 } return c.isFunction(d) ? d(b) : b } var l = /\+/g, e = c.cookie =
    function (a, d, b) {
        if (void 0 !== d && !c.isFunction(d)) { b = c.extend({}, e.defaults, b); if ("number" === typeof b.expires) { var k = b.expires, g = b.expires = new Date; g.setTime(+g + 864E5 * k) } return document.cookie = [e.raw ? a : encodeURIComponent(a), "=", n(d), b.expires ? "; expires=" + b.expires.toUTCString() : "", b.path ? "; path=" + b.path : "", b.domain ? "; domain=" + b.domain : "", b.secure ? "; secure" : ""].join("") } b = a ? void 0 : {}; for (var k = document.cookie ? document.cookie.split("; ") : [], g = 0, l = k.length; g < l; g++) {
            var f = k[g].split("="), h; h = f.shift();
            h = e.raw ? h : decodeURIComponent(h); f = f.join("="); if (a && a === h) { b = m(f, d); break } a || void 0 === (f = m(f)) || (b[h] = f)
        } return b
    }; e.defaults = {}; c.removeCookie = function (a, d) { if (void 0 === c.cookie(a)) return !1; c.cookie(a, "", c.extend({}, d, { expires: -1 })); return !c.cookie(a) }
});

