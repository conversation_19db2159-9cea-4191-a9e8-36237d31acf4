<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单折叠功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            color: #1abc9c;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin: 8px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        button {
            background: #1abc9c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #16a085;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #1abc9c;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #2c3e50;">菜单折叠功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">🎯 修复说明</div>
            <div class="info status">
                <strong>问题：</strong>默认所有菜单都展开显示，导致菜单非常多且混乱。<br>
                <strong>解决方案：</strong>确保所有菜单默认折叠状态，只有点击时才展开。
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 修复内容</div>
            <div class="code-block">
1. 修改tree_tem2组件模板：
   - 将 v-show="isShow" 改为 style="display: none;"
   - 确保所有子菜单默认隐藏

2. 添加CSS样式：
   - .nav-second-level { display: none !important; }
   - .nav-second-level.show { display: block !important; }

3. 修改loadchild方法：
   - 使用class切换 (show/hide) 而不是直接操作display
   - 确保同级菜单互斥展开
   - 保持动画效果 (slideDown/slideUp)
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 功能测试</div>
            <button onclick="testMenuCollapse()">测试菜单折叠状态</button>
            <button onclick="testMenuExpansion()">测试菜单展开功能</button>
            <button onclick="checkMenuStyles()">检查CSS样式</button>
            <div id="testOutput"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📋 测试检查项</div>
            <div class="test-result">
                <strong>✅ 应该通过的测试：</strong>
                <ul>
                    <li>页面加载后，所有子菜单都应该是隐藏状态</li>
                    <li>点击一级菜单时，对应的子菜单应该展开</li>
                    <li>点击另一个一级菜单时，之前展开的菜单应该折叠</li>
                    <li>再次点击已展开的菜单时，应该折叠</li>
                    <li>菜单展开/折叠应该有平滑的动画效果</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🛠 手动测试步骤</div>
            <div class="warning status">
                <strong>请按以下步骤测试：</strong>
                <ol>
                    <li>刷新页面，观察所有菜单是否都是折叠状态</li>
                    <li>点击任意一级菜单，观察是否只展开该菜单的子项</li>
                    <li>点击另一个一级菜单，观察之前的菜单是否自动折叠</li>
                    <li>再次点击已展开的菜单，观察是否折叠</li>
                    <li>检查箭头图标是否正确切换（右箭头→下箭头）</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">💡 预期效果</div>
            <div class="success status">
                <strong>修复后的菜单行为：</strong>
                <ul>
                    <li><strong>默认状态</strong>：所有菜单折叠，只显示一级菜单标题</li>
                    <li><strong>点击展开</strong>：点击一级菜单展开其子菜单</li>
                    <li><strong>互斥展开</strong>：同时只能展开一个一级菜单的子菜单</li>
                    <li><strong>再次点击</strong>：点击已展开的菜单可以折叠它</li>
                    <li><strong>视觉反馈</strong>：箭头图标和动画效果提供清晰的交互反馈</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function testMenuCollapse() {
            const output = document.getElementById('testOutput');
            let html = '<div class="code-block">';
            let passed = 0;
            let failed = 0;
            
            try {
                // 测试1: 检查所有nav-second-level元素是否隐藏
                const subMenus = document.querySelectorAll('.nav-second-level');
                html += `找到 ${subMenus.length} 个子菜单\n`;
                
                let hiddenCount = 0;
                subMenus.forEach((menu, index) => {
                    const isHidden = window.getComputedStyle(menu).display === 'none';
                    if (isHidden) {
                        hiddenCount++;
                    }
                    html += `子菜单 ${index + 1}: ${isHidden ? '✅ 隐藏' : '❌ 显示'}\n`;
                });
                
                if (hiddenCount === subMenus.length) {
                    html += `\n✅ 测试通过: 所有 ${subMenus.length} 个子菜单都是隐藏状态\n`;
                    passed++;
                } else {
                    html += `\n❌ 测试失败: ${subMenus.length - hiddenCount} 个子菜单未隐藏\n`;
                    failed++;
                }
                
                // 测试2: 检查CSS样式是否正确应用
                const style = document.querySelector('style');
                if (style && style.textContent.includes('.nav-second-level')) {
                    html += `✅ CSS样式已正确添加\n`;
                    passed++;
                } else {
                    html += `❌ CSS样式未找到\n`;
                    failed++;
                }
                
            } catch (e) {
                html += `❌ 测试过程出错: ${e.message}\n`;
                failed++;
            }
            
            html += `\n📊 测试结果: ${passed} 通过, ${failed} 失败\n`;
            html += '</div>';
            output.innerHTML = html;
        }
        
        function testMenuExpansion() {
            const output = document.getElementById('testOutput');
            let html = '<div class="code-block">';
            
            try {
                // 查找第一个一级菜单
                const firstMenu = document.querySelector('.tree_prent > li > a');
                if (firstMenu) {
                    html += `找到第一个一级菜单: ${firstMenu.textContent.trim()}\n`;
                    
                    // 模拟点击
                    html += `模拟点击菜单...\n`;
                    firstMenu.click();
                    
                    // 检查是否展开
                    setTimeout(() => {
                        const subMenu = firstMenu.nextElementSibling;
                        if (subMenu && subMenu.classList.contains('show')) {
                            html += `✅ 菜单成功展开\n`;
                        } else {
                            html += `❌ 菜单未展开\n`;
                        }
                        output.innerHTML = html + '</div>';
                    }, 300);
                    
                } else {
                    html += `❌ 未找到一级菜单\n`;
                }
            } catch (e) {
                html += `❌ 测试过程出错: ${e.message}\n`;
            }
            
            if (!html.includes('模拟点击菜单')) {
                html += '</div>';
                output.innerHTML = html;
            }
        }
        
        function checkMenuStyles() {
            const output = document.getElementById('testOutput');
            let html = '<div class="code-block">';
            
            try {
                // 检查关键CSS规则
                const rules = [
                    '.nav-second-level',
                    '.nav-second-level.show',
                    '.tree_prent > .tree_li > .tree_div'
                ];
                
                html += `检查CSS样式规则:\n\n`;
                
                rules.forEach(rule => {
                    const elements = document.querySelectorAll(rule);
                    html += `${rule}: 找到 ${elements.length} 个元素\n`;
                    
                    if (elements.length > 0) {
                        const firstElement = elements[0];
                        const styles = window.getComputedStyle(firstElement);
                        html += `  display: ${styles.display}\n`;
                        html += `  background: ${styles.backgroundColor}\n`;
                    }
                    html += '\n';
                });
                
            } catch (e) {
                html += `❌ 检查过程出错: ${e.message}\n`;
            }
            
            html += '</div>';
            output.innerHTML = html;
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            setTimeout(testMenuCollapse, 1000);
        });
    </script>
</body>
</html>
