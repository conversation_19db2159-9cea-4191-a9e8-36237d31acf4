<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv='pragma' content='no-cache'>
    <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
    <meta http-equiv="expires" content="0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<!--    <meta http-equiv="content-type" content="text/html" charset="utf-8">-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <!--<meta name="renderer"  content="ie-stand">-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--<meta name="renderer" content="webkit"/>-->
    <meta name="renderer" content="ie-comp|ie-stand">
    <title>院级信息管理平台</title>
    <link href="/newzui/newcss/fonts/font-awesome.css" rel="stylesheet"/>
    <link href="pub/new.css" rel="stylesheet">
    <link rel="shortcut icon" href="favicon.ico">
    <link href="css/zui.min.css" rel="stylesheet"/>
    <link href="css/iframe.css" rel="stylesheet"/>
    <link href="css/common.css" rel="stylesheet"/>
    <link href="css/main.css" rel="stylesheet"/>
    <link href="/newzui/js/zui/zui.min.css" rel="stylesheet" type="text/css"/>
    <link href="pub/css/animation.css" rel="stylesheet">
    <!--<script type="application/javascript" src="/newzui/pub/top.js"></script>-->
    <script src="js/jquery.min.js"></script>
    <script src="js/iframe.min.js"></script>
    <script src="js/content.js"></script>
    <script src="js/contabs.min.js"></script>
    <!--<script src="js/jQuery.speech.js"></script>-->
    <script src="/lib/vue/vue.min.js"></script>
    <script src="/lib/vue/vue-resource.min.js"></script>
    <script async src="/lib/marquee.js"></script>
    <script async src="js/zui/jquery.zui.js"></script>
    <!--<script src="js/layer/layer.js"></script>-->
    <script src="/newzui/js/layer/laydate/laydate.js"></script>
    <script src="/newzui/pub/js/common.js"></script>
    <script src="/newzui/pub/js/components.js"></script>
    <script src="/newzui/pub/js/dictionaries.js"></script>
    <script src="/newzui/currentCSS/js/vue/vuescroll.min.js"></script>
    <style>
        body {
            background: #fff;
        }

        #Result {
            border: none;
            margin: 0 auto;
            text-align: center;
            width: auto;
            background: none;
        }

        .crisis-box {
            width: 100%;
            position: fixed;
            z-index: 999999;
            top: 0;
            height: 100%;
            background: rgba(0, 0, 0, .5);
        }

        .crisis-width {
            width: 360px;
            background: #fff;
            position: absolute;
            z-index: 9999999;
            top: calc((100vh - 454px) / 2);
            left: calc((100vw - 360px) / 2);
            border-radius: 4px;
            height: 454px;
            box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.40);

        }

        .crisis-top {
            width: 100%;
            height: 206px;
            position: relative;
            background: url("pub/image/wj_bg.png") center no-repeat;
            background-size: 350px 205px;
        }

        .top-position {
            position: absolute;
            right: 0;
            z-index: 99;
            width: 100px;
            background: url("pub/image/wj_top.png") center no-repeat;
            background-size: 101px 76px;
            height: 76px;
            top: 0;
        }

        .right-photo {
            position: absolute;
            right: 10px;
            bottom: 0;
            width: 55px;
            height: 52px;
            background: url("pub/image/jb.gif") center no-repeat;
            background-size: 55px 52px;
        }

        .crisis-content {
            width: 100%;
            padding: 9px 10px 55px 10px;
            box-sizing: border-box;
        }

        .crisis-color {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-size: 16px;
            color: #fe3f3f;
            background: rgba(244, 178, 107, 0.20);
            border: 1px solid rgba(244, 178, 107, 0.32);
            height: 38px;
            flex-wrap: nowrap;
        }

        .crisis-text {
            display: flex;
            justify-content: flex-end;
            align-content: center;
            padding: 9px 0;
        }

        .crisis-text a {
            color: #1abc9c;
            font-size: 14px;
            text-decoration: underline;
        }

        .crisis-title {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 34px;
            width: 100%;
            color: #333333;
            background: #edf2f1;
            border: 1px solid #e9eee6;
        }

        .crisis-list {
            width: 100%;
            max-height: 82px;
        }

        .crisis-list li {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 40px;
            background: #fff;
            border: 1px solid #e9eee6;
            border-top: none;
            width: 340px;
        }

        .crisis-list li:nth-child(2n) {
            background: #fdfdfd;
        }

        .crisis-list li:hover {
            background: #edfaf7;
        }

        .crisis-title .crisis-span, .crisis-list li .crisis-span {
            width: calc((100% - 80px) / 2);
            text-align: center;
            flex-wrap: nowrap;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .crisis-title .crisis-span:nth-child(3), .crisis-list li .crisis-span:nth-child(3) {
            width: 80px;
            text-align: center;
        }

        .crisis-danger {
            background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);
            border: 1px solid #f46161;
            border-radius: 38px;
            width: 16px;
            height: 16px;
            font-size: 12px;
            color: #ffffff;
            display: block;
            float: left;
            margin: 2px 4px 0 20px;
        }

        /*检测结果颜色值*/
        .crisis-blue {
            color: #2885e2;
        }

        .crisis-red {
            color: #ff6151;
        }

        .HeadPortrait {
            padding-right: 5px;
        }

        .padd-l-r-10 {
            padding: 0 10px 24px 20px;
        }

        .HeadPortrait .headImg {
            width: 36px;
            border-radius: 100%;
            height: 36px;
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
        }

        .HeadSuccess:before {
            width: 14px;
            height: 14px;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: contain;
            background-image: url("/newzui/pub/image/2018072101.png");
        }

        .HeadPortrait .font12 {
            font-size: 12px;
        }

        .crisis-bottom {
            height: 42px;
            background: #1abc9c;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            color: #ffffff;
            position: absolute;
            bottom: 0;
            z-index: 11;
            right: 0;
            left: 0;
            cursor: pointer;
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        .text-left {
            text-align: left !important;
        }

        .text-indent20 {
            text-indent: 40px !important;
        }

        small {
            transform: scale(0.75);
            font-size: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 11px;
        }

        .crisis-jyk {
            min-width: 80px;
        }

        .crisis-color .container {
            overflow: hidden;
            box-sizing: border-box;
            position: relative;
            cursor: pointer;
            width: 250px;
            height: 20px;
            background: none;
        }

        .crisis-color .container li {
            list-style: none;
            width: 100%;
        }

        .marquee, *[class^="marquee"] {
            display: inline-block;
            white-space: nowrap;
            position: absolute;
        }

        .marquee {
            margin-left: 25%;
        }

        .wj_x {
            width: 10px;
            height: 14px;
            background: url("/newzui/css/images/wj_x.png") center no-repeat;
            background-size: 10px 14px;
        }

        .wj_s {
            width: 10px;
            height: 14px;
            background: url("/newzui/css/images/wj_s.png") center no-repeat;
            background-size: 10px 14px;
        }

        .crisis-box01 {
            width: 100%;
            position: fixed;
            z-index: 999999;
            top: 0;
            height: 100%;
            background: rgba(0, 0, 0, .5);
        }

        .crisis-box01 .crisis-width {
            width: 360px;
            background: #fff;
            position: absolute;
            z-index: 9999999;
            top: calc((100vh - 454px) / 2);
            left: calc((100vw - 360px) / 2);
            border-radius: 4px;
            height: auto;
            box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.40);

        }

        .crisis-box01 .crisis-top {
            width: 100%;
            height: 206px;
            position: relative;
            background: url("pub/image/2018072115.png") center no-repeat;
            background-size: 350px 205px;
        }

        .crisis-box01 .top-position {
            position: absolute;
            right: 0;
            z-index: 99;
            width: 100px;
            background: url("pub/image/2018072116.png") center no-repeat;
            background-size: 101px 76px;
            height: 76px;
            top: 0;
        }

        .wjz-zw {
            border-radius: 38px;
            width: 27px;
            height: 16px;
            font-size: 12px;
            color: #fff;
            display: block;
            float: left;
            margin: 2px 13px 0 0;
        }

        .crisis-box01 .right-photo {
            position: absolute;
            right: 10px;
            bottom: 0;
            width: 55px;
            height: 52px;
            background: url("pub/image/jb.gif") center no-repeat;
            background-size: 55px 52px;
        }

        .crisis-box01 .crisis-content {
            width: 100%;
            padding: 9px 10px 0 10px;
            box-sizing: border-box;
        }

        .crisis-box01 .crisis-color {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-size: 16px;
            color: #fe3f3f;
            background: rgba(244, 178, 107, 0.20);
            border: 1px solid rgba(244, 178, 107, 0.32);
            height: 38px;
            flex-wrap: nowrap;
        }

        .crisis-box01 .crisis-text {
            display: flex;
            justify-content: space-between;
            align-content: center;
            padding: 9px 0 5px 10px;
            color: #000000;
        }

        .crisis-box01 .crisis-text a {
            color: #1abc9c;
            font-size: 14px;
            text-decoration: underline;
        }

        .crisis-box01 .crisis-danger {
            background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);
            border: 1px solid #f46161;
            border-radius: 38px;
            width: 16px;
            height: 16px;
            font-size: 12px;
            color: #ffffff;
            display: block;
            float: left;
            margin: 2px 4px 0 20px;
        }

        /*检测结果颜色值*/
        .crisis-box01 .crisis-blue {
            color: #2885e2;
        }

        .crisis-box01 .crisis-red {
            color: #ff6151;
        }

        .crisis-box01 .crisis-bottom {
            height: 42px;
            background: #1abc9c;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            color: #ffffff;
            position: static;
            bottom: 0;
            z-index: 11;
            right: 0;
            left: 0;
            cursor: pointer;
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        .crisis-box01 .text-left {
            text-align: left !important;
        }

        .crisis-box01 .text-indent20 {
            text-indent: 40px !important;
        }

        .crisis-box01 small {
            transform: scale(0.75);
            font-size: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 11px;
        }

        .crisis-box01 .crisis-jyk {
            min-width: 80px;
        }

        .crisis-box01 .crisis-color .container {
            overflow: hidden;
            box-sizing: border-box;
            position: relative;
            cursor: pointer;
            width: 250px;
            height: 20px;
            background: none;
        }

        .crisis-box01 .crisis-color .container li {
            list-style: none;
            width: 100%;
        }

        .crisis-box01 .marquee, *[class^="marquee"] {
            display: inline-block;
            white-space: nowrap;
            position: absolute;
        }

        .crisis-box01 .marquee {
            margin-left: 25%;
        }

        .crisis-box01 .wj_x {
            width: 10px;
            height: 14px;
            background: url("/newzui/css/images/wj_x.png") center no-repeat;
            background-size: 10px 14px;
        }

        .crisis-box01 .wj_s {
            width: 10px;
            height: 14px;
            background: url("/newzui/css/images/wj_s.png") center no-repeat;
            background-size: 10px 14px;
        }

        .bqcydj_model {
            width: auto;
            padding: 15px 15px 15px 15px;
            background: rgba(245, 246, 250, 1);
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        .slimscroll,.tabmenu{
            height: 100%;
            overflow: hidden;
        }
        .submenu{
            height: 100%;
        }
    </style>
</head>

<body id="body"  class="skin-default mini-navbar0" >
<nav class="navbar" role="navigation">
    <!--医院信息-->
    <div class="nav_header">
        <img src="css/images/sup.png" class="brand" title="超星院级信息管理平台"/>
        <div class="hos-menu">
            <em class="wb-more-horizontal"></em>
        </div>
    </div>
    <!--导航菜单-->
    <div class="slimscroll">
        <ul class="navlist navli rolemenu2" role="menuitem">
            <li @click="navlist(1)" :class="{'active':1==index}"><a href="javascript:" data-tooltip="快捷操作"><i
                    class="fa-keyboard-o"></i><span>快捷</span>
            </a></li>
            <li @click="navlist(2)" :class="{'active':2==index}"><a href="javascript:" data-tooltip="科室业务"><i
                    class="fa-medkit"></i><span>业务</span></a></li>
            <li @click="navlist(3)" :class="{'active':3==index}"><a href="javascript:" data-tooltip="统计报表"><i
                    class="fa-bar-chart-o"></i><span>报表</span></a></li>
            <li @click="navlist(4)" :class="{'active':4==index}"><a href="javascript:" data-tooltip="科室管理"><i
                    class="fa-desktop"></i><span>管理</span></a></li>
        </ul>
    </div>
    <!--系统管理-->
    <div class="nav_footer navli" id="navlid">
        <ul>
            <li data-tooltip="隐藏/显示菜单"><span class="menu_state" onclick="menuState('menu_state')"></span></li>
            <li><a @click="openTab('cd')" href="javascript:" data-tooltip="菜单"><i class="ti-view-grid"></i></a></li>
            <li><a @click="openTab('jsj')" href="javascript:" data-tooltip="计算器"><i class="ti-jsj"></i></a></li>
            <li><a @click="openTab('txl')" href="javascript:" data-tooltip="通讯录"><i class="ti-txl"></i></a></li>
        </ul>
    </div>
</nav>
<div id="Mb" v-cloak>
    <transition name="left-left-fade" mode="out-in">
        <div class="left-mb" v-show="mb">
            <a href="javascript:" @click="close" class="fr closex ti-close" style="margin: 10px 10px 0 0"></a>
            <ul>
                <li><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>便民服务系统</span></li>
                <li class="padd-l-r"><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>医疗服务系统</span>
                </li>
                <li><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>医疗管理系统</span></li>
                <li><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>运营管理系统</span></li>
                <li class="padd-l-r"><img src="/newzui/pub/image/<EMAIL>"
                                          class="  content_img select-img"><span>科研管理系统</span></li>
                <li><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>后勤管理系统</span>
                </li>
                <li><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>教学管理系统</span>
                </li>
                <li class="padd-l-r"><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>人资管理系统</span>
                </li>
                <li><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>医疗协同系统</span>
                </li>
                <li><img src="/newzui/pub/image/<EMAIL>" class=" content_img select-img"><span>系统维护系统</span>
                </li>
                <li><img class=" content_img select-img"><span></span>
                </li>
                <li><img class=" content_img select-img"><span></span>
                </li>
            </ul>
        </div>
    </transition>
</div>
<nav class="menubar" role="navigation">
    <div class="open fa"></div>
    <!-- 项目菜单 -->
    <div class="slimscroll nav_tabs" role="treeitem">
        <!--快捷操作.菜单-->
        <div class="tabmenu">
            <ul id="side-menu" class="submenu over-auto">
                <tree_tem2 v-if="tree_tem" class="tree_prent" v-for="(item,$index) in menuData.item" :index="$index"
                           :tabindex="$index+1" :childid="'ylbm'" :childname="'ylmc'" :type="'lx'" :name="'mkmc'"
                           :list="item"
                           :child="'item'" :id="'mkbm'" :checked="[]"></tree_tem2>
            </ul>
        </div>
    </div>
</nav>
<header class="headerbar flex-container flex-align-c" role="heading">
        <!--一级菜单（模块）-->
        <div class="hosbar topbar modularDiv">
            <div class="nav-section" @mouseover="showmodule()" @mouseleave="showmoduleout">
                <div class="flex-container flex-align-c flex-jus-c"><span class="icon-but icon-title"></span>
                    <span v-text="menuTitle"></span>
                </div>
                <ul class="role-nav rolemenu1" :class="{'showRole':rolemenu}">
                    <li v-for="item in moduleList" :key="item.mkbm">
                        <a href="javascript:" @click="loadMenu(item.mkbm, item.mkmc, item.iconame,false)"
                           v-text="item.mkmc"></a>
                    </li>
                </ul>
            </div>
        </div>
    <div class="flex-container allWh80 flex-jus-sb">
        <!--iframe选项卡tabs-->
        <div class="content-tabs J_menuTabs" v-cloak>
            <button class="roll-nav roll-left J_tabLeft" @click="move('right')" onselectstart="return false;">
                <i class="fa-backward"></i>
            </button>
            <nav class="page-tabs pageTabs" >
                <vue-scroll :ops="pageScrollOps" ref="pageTabs">
                <tabs :num="isSelect" @remove-page="removePage" :remove-show="true" :tab-child="pageLists" @tab-active="tab"></tabs>
                </vue-scroll >
            </nav>
            <button class="roll-nav roll-right J_tabRight zIndex" @click="move('left')" onselectstart="return false;">
                <i class="fa-forward"></i>
            </button>
        </div>

        <!--用户信息-->
        <div class="userbar userbard flex-container flex-align-c " v-cloak style="width:400px;">
            <div class="tdbar">

                <div class="roll-dropdown J_tabClose roll-nav roll-right">
                    <button class="dropdown " data-toggle="dropdown">
                        关闭操作 <span class="fa-caret-down"></span>
                    </button>
                    <ul class="role-menu" role="menu">
                        <li class="J_tabShowActive">定位当前选项卡</li>
                        <li class="divider"></li>
                        <li class="J_tabCloseOther" @click="closeOther()">关闭其他选项卡</li>
                        <li class="J_tabCloseAll" @click="closeAll">关闭全部选项卡</li>
                    </ul>
                </div>
                <div class="roll-nav padd-l-5"  @click.stop="tagShow">
                    <div class="fa-bell-o item"><em class="zui-badge up badge-warning" v-if="noticeList.length!=0">{{noticeList.length}}</em><span>通知</span></div>
                    <div class="triangle-notice" v-show="noticeShow"></div>
                    <transition name="top-bottom-fade">
                        <div class="notice flex-dir-c flex-container" v-show="noticeShow">
                            <p class="noticeTitle">
                                <img src="pub/image/notice.png">通知
<!--                                <span>更多</span>-->
                            </p>
                            <!--<div v-for="item in noticeList" v-text="item.title" @click="deleteWs(item)"></div>-->
                            <ul class="over-auto">
                                <li v-for="item in noticeList" v-text="item.title" @click="deleteWs(item)"></li>
                            </ul>
                            <img v-show="noticeList.length == 0" src="/pub/image/notNotice.png">
                        </div>
                    </transition>
                </div>
                <div class="roll-nav userid">
                    <div class=" flex-container flex-align-c userName padd-l-10">
                        <em class="img-circle md-account"></em>
                        <span class="padd-l-5">{{userName}} </span>
                        <span class="font-12" >&nbsp;{{ybdm}}</span>
                    </div>
                    <ul class="role-menu">
                        <li @click="userNameCk()"><i class="ti-user"></i> 个人资料</li>
                        <li @click="updatedPwd"><i class="ti-unlock"></i> 密码管理</li>
                        <!-- <li @click="themStyle()"><i class="fa fa-photo"></i> 切换版本</li> -->
                        <li @click="setingIndexShortcutMenu"><i class="fa fa-cog"></i> 配置首页快捷按钮</li>
                        <li @click="colsedelete"><i class="ti-power-off"></i> 注销用户</li>
                        <li @click="deleteWsAll"><i class="fa fa-cog"></i>清除所有通知</li>
                        <li @click="fenzu" v-if="mkbm=='015'"><i class="ti-power-off"></i> 分组选择</li>
                    </ul>
                </div>
                <div class="roll-nav userid">
                    <div class=" flex-container flex-align-c userName padd-l-10">
                        <span class="padd-l-5">V 0.12.2 </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
<div id="page-wrapper" class="gray-bg rightMenu">
    <!--iframe选项卡页面区-->
    <input id="this_user_id" type="hidden" :value="search">
        <iframe class="J_iframe iframe0" :name="item.name" :id="item.name"  v-for="(item,$index) in pageLists" :key="$index" v-show="isSelect == $index" :src="item.url"></iframe>
</div>
<div class="app" v-if="changeMode" @mousedown="begdrag" @mouseup="begdrag" v-cloak>
    <div class="calculator">
        <a href="javascript:" @click="changeMode=false" style="position: absolute; right: 10px;top: 3px;font-size: 14px !important;"
           class="fr closex ti-close"></a>
        <button class="toggle-button">
            <p>计算器</p>
        </button>
        <div class="results">
            <input class="input" ref="current" @keyup.esc="changeMode=false" @keyup.enter="press" v-model="current"/>
        </div>
        <div class="mode" v-if="changeMode">
            <button class="button" @click="press">7</button>
            <button class="button" @click="press">8</button>
            <button class="button" @click="press">9</button>
            <button class="button" @click="press">*</button>
            <button class="button" @click="press"><=</button>
            <button class="button" @click="press">C</button>
            <button class="button" @click="press">4</button>
            <button class="button" @click="press($event)">5</button>
            <button class="button" @click="press">6</button>
            <button class="button" @click="press">/</button>
            <button class="button" @click="press">(</button>
            <button class="button" @click="press">)</button>
            <button class="button" @click="press">1</button>
            <button class="button" @click="press">2</button>
            <button class="button" @click="press">3</button>
            <button class="button" @click="press">-</button>
            <button class="button" @click="press">x 2</button>
            <button class="button" @click="press">±</button>
            <button class="button" @click="press">0</button>
            <button class="button" @click="press">.</button>
            <button class="button" @click="press">%</button>
            <button class="button" @click="press">+</button>
            <button class="button equal-sign" @click="press">=</button>
        </div>
    </div>
</div>
<!--2018/07/23危急值自动弹出必须接收才可以关闭当前跳转到对应界面-->

<div class="crisis-box" v-if="crShow" v-cloak>
    <div class="crisis-width">
        <div class="crisis-top">
            <div class="top-position"></div>
            <div class="right-photo">
                <!--绑数据放开该行警报声音-->
                <audio id="audio" src="pub/救护车.wav" autoplay="true" loop="loop"></audio>
            </div>
        </div>
        <div class="crisis-content">
                <span class="crisis-color">
                    <em class="crisis-jyk">「检验科」</em>
                    <div class="container">
                        <div class="marquee">
                            <li>李欢医生发起危机值，请处理…</li>
                        </div>
                    </div>
                </span>
            <span class="crisis-text"><a @click="Gopatient" style="cursor: pointer">患者:阿杰</a></span>

            <div class="crisis-title">
                <span class="crisis-span text-left text-indent20">检查项目</span>
                <span class="crisis-span">检查结果</span>
                <span class="crisis-span">状态</span>
            </div>
            <vue-scroll :ops="pageScrollOps">
                <ul class="crisis-list">
                    <li v-for="(item,$index) in datas" :class="{'table-active':item.wxbz==0}">
                            <span class="crisis-span text-left">
                                <i class="crisis-danger" v-show="item.wxbz==0"><small>危</small></i>
                                <em class="wjz-zw" v-show="item.wxbz!=0"></em>
                                <em v-text="item.name"></em>
                            </span>
                        <span class="crisis-span"
                              :class=" item.result=='0' ? 'crisis-blue':item.result=='1' ? 'crisis-red' : '' "
                              v-text="is_result[item.result]">
                            </span>
                        <span class="crisis-span" :class=" item.state=='0' ? 'wj_x':item.state=='1' ? 'wj_s' : '' "
                              v-text="is_state[item.state]"></span>
                    </li>

                </ul>
            </vue-scroll>
        </div>
        <div class="crisis-bottom" @click="Receive">接收</div>
    </div>
</div>
<!--end-->
<div class="crisis-box01" v-cloak v-if="crShow">
    <div class="crisis-width">
        <div class="crisis-top">
            <div class="top-position"></div>
            <div class="right-photo">
                <!--绑数据放开该行警报声音-->
                <!--<audio id="audio01" src="pub/救护车.wav"  autoplay="true" loop="loop"></audio>-->
            </div>
        </div>
        <div class="crisis-content">
            <span class="crisis-color">「125床」<i id="Result01">李欢医生发起危重病人抢救…</i></span>
            <span class="crisis-text"><span>参加人员</span><a>患者:阿杰</a></span>
        </div>
        <div class="flex-container padd-l-r-10">
            <div class="Headimg HeadPortrait HeadStart" v-for="(list,$index) in 6" :id="list">
                <p class="headImg"
                   style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                <p class="color-757c83 font12">刘医生</p>
            </div>
        </div>
        <div class="crisis-bottom" @click="Receive">接受</div>
    </div>
</div>
<div id="pop" v-cloak>
    <transition enter-active-class="animated zoomIn" leave-active-class="animated zoomOut">
        <div v-show="isShow" class="pop-open">
            <div class="pophide show"></div>
            <div class=" podrag  show bcsz-layer padd-b-15" style="width: auto">
                <div class="layui-layer-title ">个人资料</div>
                <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close"
                                                    @click="isShow=false"></a></span>
                <div class="layui-layer-content">
                    <div class=" layui-mad latui-mad layui-height" style="height: 612px">
                        <div class="nameTab flex-container ">
                            <div class="nameList" :class="{'active':index==0}" @click="tabActive(0)">基本信息</div>
                            <div class="nameList" :class="{'active':index==1}" @click="tabActive(1)">专业技术档案</div>
                        </div>
                        <div class="flex-container" style="height: calc(100% - 87px)">
                            <vue-scroll class="flex-container" :ops="pageScrollOps">
                                <div v-show="index==0">
                                    <div class="flex-container flex-align-c mad-top-10 padd-b-5">
                                        <span class="color-767d84" style="margin-right: 18px">人员账号</span>
                                        <span class="color-ff7800">{{userId}}</span>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">姓&emsp;&emsp;名</span>
                                            <input class="zui-input wh180" v-model="popContent.ryxm" readonly="readonly"
                                                   type="text">
                                        </div>
                                        <div class="flex-container padd-b-9">
                                            <i>性&ensp;&ensp;&ensp;别</i>
                                            <select-input @change-data="resultChange" class="wh180" :not_empty="true"
                                                          :child="brxb_tran" :index="popContent.xb" :val="popContent.xb"
                                                          readonly="readonly" :name="'popContent.xb'"></select-input>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">年&emsp;&emsp;龄</span>
                                            <input class="zui-input" v-model="popContent.age" readonly="readonly"
                                                   style="width: 104px;margin-right: 5px" type="text">
                                        </div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">婚&emsp;&emsp;姻</span>
                                            <input class="zui-input wh180" v-model="popContent.hyzk" readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">民&emsp;&emsp;族</span>
                                            <select-input @change-data="resultChange" class="wh180"
                                                          :data-notEmpty="false"
                                                          :child="mzList" :index="'mzmc'" :index_val="'mzbm'"
                                                          :val="popContent.mzbm"
                                                          :name="'popContent.mzbm'" readonly="readonly" :search="true">
                                            </select-input>
                                        </div>
                                        <span class="padd-r-5">政治面貌</span>
                                        <select-input @change-data="resultChange" class="wh180" :data-notEmpty="false"
                                                      :child="zzmm_tran" :index="popContent.zzmm" readonly="readonly"
                                                      :val="popContent.zzmm"
                                                      :name="'popContent.zzmm'">
                                        </select-input>
                                    </div>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container padd-b-9 padd-r-18">
                                            <span class="padd-r-5">身份证号</span>
                                            <input type="text" class="zui-input wh180" v-model="popContent.sfzhm"
                                                   readonly="readonly"/>
                                        </div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">身&emsp;&emsp;高</span>
                                            <div class="position">
                                                <input class="zui-input wh180 padd-r-30" v-model="popContent.sg"
                                                       readonly="readonly" type="text">
                                                <span class="cm color-0991e9">cm</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-14">鞋&emsp;&emsp;码</span>
                                            <span style="padding-right: 7px">左</span>
                                            <input class="zui-input" v-model="popContent.zxm"
                                                   style="width: 62px;margin-right: 5px"
                                                   readonly="readonly" type="text">
                                            <span style="padding-right: 7px">右</span>
                                            <input class="zui-input" v-model="popContent.yxm" style="width: 62px"
                                                   readonly="readonly" type="text">
                                        </div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">衣&emsp;&emsp;码</span>
                                            <input class="zui-input wh180" v-model="popContent.ym" readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">联系电话</span>
                                            <input type="text" class="zui-input wh180" v-model="popContent.sjhm"
                                                   readonly="readonly" data-notEmpty="false"/></div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">住&emsp;&emsp;址</span>
                                            <input class="zui-input wh180" v-model="popContent.address"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">应&nbsp;&nbsp;急&ensp;人<br/>姓&emsp;&ensp;名</span>
                                            <input class="zui-input wh180" v-model="popContent.yjrxm"
                                                   readonly="readonly"
                                                   placeholder="请输入联系电话" type="text">
                                        </div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">应&nbsp;&nbsp;急&nbsp;人<br/>关&emsp;&ensp;系</span>
                                            <input class="zui-input wh180" readonly="readonly" type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">应&nbsp;&nbsp;急&ensp;人<br/>电&emsp;&ensp;话</span>
                                            <input class="zui-input wh180" v-model="popContent.yjrdh"
                                                   readonly="readonly"
                                                   placeholder="请输入联系电话" type="text">
                                        </div>
                                    </div>
                                    <h3 style="margin: 15px 0 12px" class="text-left color-354052 font-weight">组织关系</h3>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">所属科室</span>
                                            <select-input @change-data="resultChange" class="wh180" :child="ksbmList"
                                                          :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ksbm"
                                                          readonly="readonly"
                                                          :name="'popContent.ksbm'" :search="true"></select-input>
                                        </div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">工作年限</span>
                                            <div class="position">
                                                <input class="zui-input wh180 padd-r-30" v-model="popContent.gznx"
                                                       readonly="readonly" type="text">
                                                <span class="cm color-0991e9">年</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">入职时间</span>
                                            <div class="position zui-date">
                                                <i class="datenox icon-rl"></i>
                                                <input class="zui-input wh180 timeVal" readonly="readonly" id="timeVal"
                                                       type="text">
                                            </div>
                                        </div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">行政职务</span>
                                            <select-input @change-data="resultChange" class="wh180" readonly="readonly"
                                                          :data-notEmpty="false" :child="glzwdm_tran"
                                                          :index="popContent.glzwdm"
                                                          :val="popContent.glzwdm" :name="'popContent.glzwdm'">
                                            </select-input>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">人事关系</span>
                                            <select-input @change-data="resultChange" readonly="readonly" class="wh180"
                                                          :data-notEmpty="false" :child="bzqk_tran"
                                                          :index="popContent.bzqk"
                                                          :val="popContent.bzqk" :name="'popContent.bzqk'">
                                            </select-input>
                                        </div>
                                    </div>
                                </div>
                                <div v-show="index==1">
                                    <div class="flex-container flex-align-c mad-top-10">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">毕业院校</span>
                                            <input class="zui-input wh180" v-model="popContent.byyx" readonly="readonly"
                                                   type="text">
                                        </div>
                                        <div class="flex-container flex-align-c padd-b-9">
                                            <span class="padd-r-5">专&emsp;&emsp;业</span>
                                            <select-input @change-data="resultChange" class="wh180"
                                                          :data-notEmpty="false"
                                                          :child="sxzyList" :index="'sxzymc'" :index_val="'sxzybm'"
                                                          :val="popContent.sxzydm"
                                                          :name="'popContent.sxzydm'" readonly="readonly"
                                                          :search="true">
                                            </select-input>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">毕业时间</span>
                                            <input class="zui-input wh180" v-model="popContent.bysj" readonly="readonly"
                                                   type="text">
                                        </div>
                                        <div class="flex-container padd-b-9">
                                            <span class="padd-r-5">专&emsp;&emsp;业<br/>技术职称</span>
                                            <select-input @change-data="resultChange" class="wh180"
                                                          :data-notEmpty="false"
                                                          :child="sxzyList" :index="'sxzymc'" :index_val="'sxzybm'"
                                                          :val="popContent.sxzydm"
                                                          :name="'popContent.sxzydm'" readonly="readonly"
                                                          :search="true">
                                            </select-input>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">聘任时间</span>
                                            <input class="zui-input wh180" v-model="popContent.prsj" readonly="readonly"
                                                   type="text">
                                        </div>
                                        <div class="flex-container padd-b-9">
                                            <span class="padd-r-5">取得专业<br/>资格时间</span>
                                            <input class="zui-input wh180" v-model="popContent.qdzyzgsj"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">资格证号</span>
                                            <input class="zui-input wh180" v-model="popContent.zyzgzsh"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                        <div class="flex-container padd-b-9">
                                            <span class="padd-r-5">医&emsp;&emsp;师<br/>执业证号</span>
                                            <input class="zui-input wh180" v-model="popContent.zyzgzsh"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">取&ensp;得&ensp;执<br/>业证时间</span>
                                            <input class="zui-input wh180" v-model="popContent.qdzyzsj"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">第&emsp;&emsp;一<br/>注册单位</span>
                                            <input class="zui-input wh180" v-model="popContent.dyzcdw"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                        <div class="flex-container padd-b-9">
                                            <span class="padd-r-5">注册时间</span>
                                            <input class="zui-input wh180" v-model="popContent.dyzcsj"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container flex-align-c padd-b-9 padd-r-18">
                                            <span class="padd-r-5">第&emsp;&emsp;二<br/>注册单位</span>
                                            <input class="zui-input wh180" v-model="popContent.dezcdw"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                        <div class="flex-container padd-b-9">
                                            <span class="padd-r-5">注册时间</span>
                                            <input class="zui-input wh180" v-model="popContent.dezcsj"
                                                   readonly="readonly"
                                                   type="text">
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class="flex-container  padd-b-9 ">
                                            <span class="padd-r-5">备&emsp;&emsp;注</span>
                                            <!--<input type="text" class="zui-input " v-model="popContent.ryjj"@keydown="nextFocus($event)" data-notEmpty="false" readonly="readonly" style="height: 68px"/>-->
                                            <textarea @keydown="nextFocus($event)" data-notEmpty="false"
                                                      readonly="readonly"
                                                      class="zui-input" v-model="popContent.ryjj"
                                                      style="background:#ffffff;border:1px solid #dfe3e9;border-radius:4px;width:439px;height:70px;"></textarea>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class=" padd-b-9 padd-r-18">
                                            <p class="padd-r-5 padd-b-5"><span>专业证书</span><span class="color-fe8111">（单张图片最大支持10MB）</span>
                                            </p>
                                            <ul class="flex-container" style="padding-left: 61px">
                                                <li class="uploadNameImg">
                                                    <i class="deleteImg" v-if="!edit" @click="shanchu"></i>
                                                    <p @click="preview" :class="{'hoverZyzs':edit}"
                                                       class="zyzs iconfont icon-iocn42"
                                                       style="background-image: url(/newzui/pub/image/2018072103.png)"></p>
                                                    <p class="whiteSpace ellipsis">专业技术资格专业技术资格</p>
                                                </li>
                                                <label v-if="!edit">
                                                    <li class="upload iconfont flex-container flex-align-c flex-jus-c icon-iocn42"></li>
                                                    <input type="file" accept="image/*" class="hide">
                                                </label>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class=" padd-b-9 padd-r-18">
                                            <p class="padd-r-5 padd-b-5"><span>执业证书</span><span class="color-fe8111">（单张图片最大支持10MB）</span>
                                            </p>
                                            <ul class="flex-container" style="padding-left: 61px">
                                                <li class="uploadNameImg">
                                                    <i class="deleteImg" v-if="!edit" @click="shanchu"></i>
                                                    <p @click="preview" :class="{'hoverZyzs':edit}"
                                                       class="zyzs iconfont icon-iocn42"
                                                       style="background-image: url(/newzui/pub/image/2018072103.png)"></p>
                                                    <p class="whiteSpace ellipsis">专业技术资格专业技术资格</p>
                                                </li>
                                                <label v-if="!edit">
                                                    <li class="upload iconfont flex-container flex-align-c flex-jus-c icon-iocn42"></li>
                                                    <input type="file" accept="image/*" class="hide">
                                                </label>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="flex-container flex-align-c ">
                                        <div class=" padd-b-9 padd-r-18">
                                            <p class="padd-r-5 padd-b-5"><span>聘任证书</span><span class="color-fe8111">（单张图片最大支持10MB）</span>
                                            </p>
                                            <ul class="flex-container" style="padding-left: 61px">
                                                <li class="uploadNameImg">
                                                    <i class="deleteImg" v-if="!edit" @click="shanchu"></i>
                                                    <p @click="preview" :class="{'hoverZyzs':edit}"
                                                       class="zyzs iconfont icon-iocn42"
                                                       style="background-image: url(/newzui/pub/image/2018072103.png)"></p>
                                                    <p class="whiteSpace ellipsis">专业技术资格专业技术资格</p>
                                                </li>
                                                <label v-if="!edit">
                                                    <li class="upload iconfont flex-container flex-align-c flex-jus-c icon-iocn42"></li>
                                                    <input type="file" accept="image/*" class="hide">
                                                </label>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="userImg" v-if="edit">
                                    <div class="img" :class="{'userHover':!edit}"
                                         style="background-image: url(/newzui/pub/image/2018072103.png)"></div>
                                </div>
                                <label class="userImg" v-if="!edit">
                                    <div class="img" :class="{'userHover':!edit}"
                                         style="background-image: url(/newzui/pub/image/2018072103.png)"></div>
                                    <input type="file" accept="image/*" class="hide">
                                </label>
                            </vue-scroll>
                        </div>
                    </div>
                </div>
                <div class="zui-row buttonbox flex-container flex-align-c userNameflexd">
                    <button class="zui-btn xmzb-db table_db_esc margin-r-10 btn-default" @click="isShow=false">取消
                    </button>
                    <button class="zui-btn xmzb-db btn-primary table_db_save" v-if="edit" @click="edit,edit=false">编辑
                    </button>
                    <button class="zui-btn xmzb-db btn-primary table_db_save" v-if="!edit" @click="submit">确定</button>
                </div>
            </div>
        </div>
    </transition>
    <preview :preview-update="previewshow"
             :src="'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532503254326&di=a2175d25c9f4ccc582e3ae37531ec0a4&imgtype=0&src=http%3A%2F%2Fwww.wallcoo.com%2Fcartoon%2FKitsunenoir_Design_Illustration_V%2Fwallpapers%2F2560x1440%2Fkim-holtermand-reflections.jpg'"
             @preview-hide="previewHide"></preview>

</div>
<div id="seting-index-shortcut-menu" v-cloak>
    <model  :model-show="false"  @result-close="ifShow=false"
           v-if="ifShow" :title="'配置首页快捷按钮'">
        <div class="menucydj_model">
            <input @input="filterFun($event.target.value)" class="zui-input margin-b-10" placeholder="请输入过滤条件-文字或者拼音简码"/>
            <div class="flex-container flex-wrap-w">
                <div style="display: inline-block; width: 33.33%;overflow:hidden;"
                     v-for="(item,$index) in listAllSimple" :key="item.ylbm">
                    <input type="checkbox" :id="'radio-'+item.ylbm" v-model="listSelectYLBM[item.ylbm]"
                           class="h-checkbox">
                    <label :for="'radio-'+item.ylbm" @click.prevent="lableClick(item.ylbm)">{{ item.mkmc }} - {{
                        item.ylmc }}</label>
                </div>
            </div>
        </div>
    </model>
<!--    <transition enter-active-class="animated zoomIn" leave-active-class="animated zoomOut">-->
<!--        <div v-show="ifShow" class="pop-open">-->
<!--            <div class="podrag show bcsz-layer padd-b-15" style="width: auto">-->
<!--                <div class="layui-layer-title ">配置首页快捷按钮</div>-->
<!--                <span class="layui-layer-setwin"><a href="javascript:" class="closex ti-close"-->
<!--                                                    @click="ifShow=false"></a></span>-->
<!--                <div class="layui-layer-content">-->
<!--                    <div style="padding: 5px 20px;"><input @input="getAll($event.target.value)" class="zui-input"-->
<!--                                                           placeholder="请输入过滤条件-文字或者拼音简码"/></div>-->
<!--                    <div class="layui-mad latui-mad layui-height text-left"-->
<!--                         style="height: 500px;width: 800px;overflow: auto;">-->
<!--                        <div style="display: inline-block; width: 33.33%;overflow:hidden;"-->
<!--                             v-for="(item,$index) in listAllSimple" :key="item.ylbm">-->
<!--                            <input type="checkbox" :id="'radio-'+item.ylbm" v-model="listSelectYLBM[item.ylbm]"-->
<!--                                   class="h-checkbox">-->
<!--                            <label :for="'radio-'+item.ylbm" @click.prevent="lableClick(item.ylbm)">{{ item.mkmc }} - {{-->
<!--                                item.ylmc }}</label>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </transition>-->
</div>
<div id="pwd">
    <model :s="'保存'" :c="'取消'" @default-click="save" :model-show="true" @result-clear="close" @result-close="close"
           v-if="bqcy" :title="'修改登陆密码'">
        <div class="bqcydj_model">
            <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                <span class="padd-r-5">旧密码&emsp;</span>
                <div class="zui-input-inline wh120">
                    <input class="zui-input" v-model="json.oldPsw" type="password"/>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-r-20 ">
                <span class="padd-r-5">新密码&emsp;</span>
                <div class="zui-input-inline wh120">
                    <input class="zui-input" v-model="json.newPsw" type="password"/>
                </div>
            </div>
        </div>
    </model>
</div>
</body>

<script src="index.js" type="application/javascript"></script>
</html>
