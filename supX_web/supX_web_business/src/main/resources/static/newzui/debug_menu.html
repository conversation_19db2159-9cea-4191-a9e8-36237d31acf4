<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 0 auto;
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .debug-title {
            color: #1abc9c;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        button {
            background: #1abc9c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #16a085;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="text-align: center; color: #2c3e50;">菜单调试工具</h1>
        
        <div class="debug-section">
            <div class="debug-title">🔍 当前状态检查</div>
            <button onclick="checkMenuStatus()">检查菜单状态</button>
            <button onclick="checkVueInstances()">检查Vue实例</button>
            <button onclick="checkMenuData()">检查菜单数据</button>
            <div id="statusOutput"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">📊 菜单数据结构</div>
            <button onclick="showMenuStructure()">显示菜单结构</button>
            <div id="menuStructureOutput"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">🛠 问题诊断</div>
            <button onclick="diagnoseIssues()">诊断问题</button>
            <div id="diagnosisOutput"></div>
        </div>

        <div class="debug-section">
            <div class="debug-title">💡 解决方案</div>
            <div class="info status">
                <strong>当前已知问题和解决方案：</strong>
                <ul>
                    <li><strong>菜单名称显示问题</strong>：检查lx字段值，确保mkmc和ylmc字段正确</li>
                    <li><strong>菜单默认折叠</strong>：这是正常行为，isShow默认为false</li>
                    <li><strong>界面美观问题</strong>：已移除模块标题，简化显示</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function checkMenuStatus() {
            const output = document.getElementById('statusOutput');
            let html = '<div class="code-block">';
            
            try {
                // 检查Vue实例
                if (typeof window.menuBar !== 'undefined') {
                    html += '✅ menuBar实例存在\n';
                    html += `📊 allModuleMenus长度: ${window.menuBar.allModuleMenus ? window.menuBar.allModuleMenus.length : 0}\n`;
                } else {
                    html += '❌ menuBar实例不存在\n';
                }
                
                // 检查DOM元素
                const sideMenu = document.getElementById('side-menu');
                if (sideMenu) {
                    html += '✅ side-menu DOM元素存在\n';
                    html += `📊 菜单项数量: ${sideMenu.querySelectorAll('li').length}\n`;
                } else {
                    html += '❌ side-menu DOM元素不存在\n';
                }
                
                // 检查用户ID
                if (typeof userId !== 'undefined') {
                    html += `✅ 用户ID: ${userId}\n`;
                } else {
                    html += '❌ 用户ID未定义\n';
                }
                
            } catch (e) {
                html += `❌ 检查过程出错: ${e.message}\n`;
            }
            
            html += '</div>';
            output.innerHTML = html;
        }
        
        function checkVueInstances() {
            const output = document.getElementById('statusOutput');
            let html = '<div class="code-block">';
            
            const instances = ['menuBar', 'loadModular', 'navbar', 'J_tabLeft'];
            
            instances.forEach(instance => {
                if (typeof window[instance] !== 'undefined') {
                    html += `✅ ${instance} 实例存在\n`;
                    if (instance === 'menuBar' && window[instance].allModuleMenus) {
                        html += `   📊 allModuleMenus: ${window[instance].allModuleMenus.length} 项\n`;
                    }
                } else {
                    html += `❌ ${instance} 实例不存在\n`;
                }
            });
            
            html += '</div>';
            output.innerHTML = html;
        }
        
        function checkMenuData() {
            const output = document.getElementById('statusOutput');
            let html = '<div class="code-block">';
            
            try {
                if (window.menuBar && window.menuBar.allModuleMenus) {
                    html += `📊 总菜单组数: ${window.menuBar.allModuleMenus.length}\n\n`;
                    
                    window.menuBar.allModuleMenus.forEach((menuGroup, index) => {
                        html += `组 ${index + 1}:\n`;
                        html += `  types: ${menuGroup.types}\n`;
                        html += `  菜单项数: ${menuGroup.item ? menuGroup.item.length : 0}\n`;
                        
                        if (menuGroup.item && menuGroup.item.length > 0) {
                            html += `  示例菜单项:\n`;
                            const sample = menuGroup.item[0];
                            html += `    lx: ${sample.lx}\n`;
                            html += `    mkmc: ${sample.mkmc}\n`;
                            html += `    ylmc: ${sample.ylmc}\n`;
                        }
                        html += '\n';
                    });
                } else {
                    html += '❌ 菜单数据不存在\n';
                }
            } catch (e) {
                html += `❌ 检查菜单数据出错: ${e.message}\n`;
            }
            
            html += '</div>';
            output.innerHTML = html;
        }
        
        function showMenuStructure() {
            const output = document.getElementById('menuStructureOutput');
            let html = '<div class="code-block">';
            
            try {
                if (window.menuBar && window.menuBar.allModuleMenus) {
                    html += JSON.stringify(window.menuBar.allModuleMenus, null, 2);
                } else {
                    html += '菜单数据不存在';
                }
            } catch (e) {
                html += `显示菜单结构出错: ${e.message}`;
            }
            
            html += '</div>';
            output.innerHTML = html;
        }
        
        function diagnoseIssues() {
            const output = document.getElementById('diagnosisOutput');
            let html = '';
            let issues = [];
            let solutions = [];
            
            // 检查各种可能的问题
            if (typeof window.menuBar === 'undefined') {
                issues.push('menuBar Vue实例未创建');
                solutions.push('检查index.js是否正确加载');
            }
            
            if (window.menuBar && (!window.menuBar.allModuleMenus || window.menuBar.allModuleMenus.length === 0)) {
                issues.push('菜单数据为空');
                solutions.push('检查loadAllModuleMenus方法是否正确执行');
            }
            
            if (!document.getElementById('side-menu')) {
                issues.push('菜单DOM元素不存在');
                solutions.push('检查HTML结构是否正确');
            }
            
            if (typeof userId === 'undefined') {
                issues.push('用户ID未定义');
                solutions.push('确保用户已正确登录');
            }
            
            if (issues.length === 0) {
                html += '<div class="success status">✅ 未发现明显问题</div>';
            } else {
                html += '<div class="error status"><strong>发现的问题:</strong><ul>';
                issues.forEach(issue => {
                    html += `<li>${issue}</li>`;
                });
                html += '</ul></div>';
                
                html += '<div class="warning status"><strong>建议的解决方案:</strong><ul>';
                solutions.forEach(solution => {
                    html += `<li>${solution}</li>`;
                });
                html += '</ul></div>';
            }
            
            output.innerHTML = html;
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            setTimeout(checkMenuStatus, 1000);
        });
    </script>
</body>
</html>
