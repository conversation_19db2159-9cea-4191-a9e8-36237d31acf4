body {
    padding: 10px;
}

#workbench .commom_flex {
    display: flex;
    justify-content: flex-start;
    height: 95px;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #eeeeee;
    border-top: none;
}

#workbench .commom_img_on {
    height: 178px!important;
    background-color: transparent;
}
#workbench .commom_img{
    height: auto;
    flex-wrap: wrap;
    background-color: transparent;
}
#workbench .commom_img .commom_img_on {
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    height: 100%;
    display: flex;
    padding-top: 30px;
    align-items: center;
    border-radius: 5px;
    flex-direction: column;
}
.tb-width{
    width: 51%;
    margin: auto;
    white-space: nowrap;
}
#workbench .commom_img .commom_img_sf_one {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_two {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_three {
    background-image: url("/newzui/pub/image/12.png");
}

#workbench .commom_img .commom_img_sf_four {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_five {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_seven {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_eight {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_nine {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_ten {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_eleven {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_twoelve {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_fourteen {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .commom_img_sf_thirteen {
    background-image: url("/newzui/pub/image/<EMAIL>");
}

#workbench .commom_img .img_text {
    font-family: PingFangSC-Regular;
    font-size: 16px;
    color: #ffffff;
    align-items: center;
    text-align: center;
    cursor: pointer;
    margin-bottom: 3px;
}
#workbench .commom_img .img_text:hover{
    opacity: .4;
}
#workbench .header_text h1 {
    background: #fbfbfb;
    border: 1px solid #eeeeee;
    border-radius: 4px 4px 0 0;
    height: 43px;
    font-size: 16px;
    color: #333333;
    display: flex;
    font-weight: bolder;
    font-family: PingFangSC-Semibold;
    align-items: center;
    padding-left: 17px;
}

#workbench .text {
    text-align: center;
}

.pd-4 {
    padding: 0 10px 0 0;
    margin-bottom: 10px;
}
.md-b-10 {
    margin-bottom: 10px;
}

.md_8 {
    margin: 0 8px 0 0;
}
.md_b_8{
    margin: 0 0 8px  0;
}
.pd-r {
    padding-right: 12px;
}
.md_l{
    margin-left: 10px;
}
.md_r_10{
    margin-right: 10px;
}
.md-r {
    margin-right: 33px;
}
.pd_b_10{
    padding-bottom: 10px;
}
.op-0 {
    opacity: 0;
}

.num_text {
    cursor: pointer;
    font-family: arial;
    font-size: 30px;
}

.commom_text {
    font-size: 14px;
    color: #757c83;
}

.commom_pb {
    display: flex;
    justify-content: space-between;
}

.commom_pb p {
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #333333;
    font-weight: normal;
}

.commom_pb .commom_zao, .commom_pb .commom_bai, .commom_pb .commom_ye, .commom_pb .commom_wan,.commom_green ,.commom_yellwo,.commom_blue{
    position: relative;
    margin-right: 30px;
}

.commom_pb .commom_zao:before,.commom_green:before {
    background-image: linear-gradient(-1deg, #1abc9c 2%, #41d9bb 98%);
}

.commom_pb .commom_bai:before,.commom_yellwo:before {
    background-image: linear-gradient(-180deg, #fdbb00 0%, #fb8600 100%);
}

.commom_pb .commom_wan:before,.commom_blue:before {
    background-image: linear-gradient(-180deg, #0ad4fb 0%, #04a9f5 100%);
}

.commom_pb .commom_ye:before {
    background-image: linear-gradient(-180deg, #05473c 0%, #02221c 100%);
}

.commom_pb .commom_bai:before, .commom_pb .commom_zao:before, .commom_pb .commom_wan:before, .commom_pb .commom_ye:before ,.commom_green:before,.commom_blue:before,.commom_yellwo:before{
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 100%;
    left: -21px;
    top: 50%;
    transform: translate(50%, -50%);
}
.commom_pb .commom_prev:hover, .commom_pb .commom_next:hover{
    color: rgba(26, 188, 156, .6);
}
.commom_pb .commom_prev, .commom_pb .commom_next {
    background: #e5eae9;
    border-radius: 31px;
    width: 68px;
    text-align: center;
    line-height: 24px;
    margin-right: 10px;
    cursor: pointer;
    display: inline-block;
    height: 24px;
}

.commom_pb .commom_year {
    cursor: pointer;
    font-size: 14px;
    color: #1abc9c;
    width: 22px;
    display: inline-block;
    text-align: center;
    line-height: 22px;
    height: 22px;
    vertical-align: bottom;
}
.commom_pb .commom_year:hover{
    background: #1abc9c;
    border-radius: 4px;
    color: #ffffff;
}
.commom_pb .md_r_18 {
    margin-right: 8px;
}

.commom_pb .md-r-22 {
    margin-right: 22px;
}

.commom_pb .commom_active {
    background: #1abc9c;
    border-radius: 4px;
    color: #ffffff;
}

.commom_flex-pb, .commom_flex-pd-content {
    display: flex;

}

.commom_flex-pb li, .commom_pb_bg ul li {
    width: calc(100% / 8);
}

.commom_pb_bg {
    background-color: #ffffff;
}

.commom_pb_bg .commom_flex-pb li {
    background: #edf2f1;
    border: 1px solid #e9eee6;
    height: 34px;
    border-right: none;
    border-top: none;
    text-align: center;
    line-height: 34px;
    color:#333333;
}

.commom_pb_bg .commom_flex-pd-content li {
    background: #fcfcfc;
    border: 1px solid #e6eaee;
    height: 40px;
    border-right: none;
    border-top: none;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-sizing: initial;
}

.commom_pb_bg .commom_flex-pd-content li .pd_text {
    width: 24px;
    height: 24px;
    border-radius: 100%;
    text-align: center;
    font-size: 12px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.commom_pb_bg .commom_flex-title {
    display: flex;
    justify-content: space-evenly;
}
.icon-border{
    position: absolute;
    width: 100%;
    height: 1px;
    background: #ffffff;
    transform: rotate(9deg);
    bottom: 15px;
}
/*.commom_flex-title:before {*/
    /*position: absolute;*/
    /*top: 0;*/
    /*right: 0;*/
    /*left: 0;*/
    /*bottom: 0;*/
    /*border-bottom: 36px solid #ffffff;*/
    /*border-right: 234px solid transparent;*/
    /*content: '';*/
/*}*/

/*.commom_flex-title:after {*/
    /*position: absolute;*/
    /*top: 0;*/
    /*left: 0;*/
    /*right: 1px;*/
    /*bottom: 0;*/
    /*border-bottom: 36px solid #edf2f1;*/
    /*border-right: 234px solid transparent;*/
    /*content: '';*/
/*}*/

.commom_flex-title {
    border: solid 1px red;
    position: relative;
    background: transparent
}
.commom_flex-title .user{
    position: absolute;
    left: 34px;
    bottom: -6px;
    z-index: 11;
}
.commom_flex-title .date{
    position: absolute;
    right: 34px;
    z-index: 11;
    top: -6px;
}
.canvas{
    width: 100%;
    height: 254px;
}
#workbench .canvasone,#containerone{
    width: 100%;
    height:244px;
    border:1px solid #eeeeee;
    border-radius:4px;
}
#workbench  .commom_canvas{
    height: auto;
    padding-top: 10px;
}
.green-dashed{
    border: 1px dashed rgba(26, 188, 156, 0.3);
    background:rgba(26,188,156,0.06);
    border-radius:2px;
    height:58px;
}
.yello-dashed{
    border: 1px dashed rgba(251,134,0,0.20);
    border-radius:2px;
    height:58px;
    background:rgba(251,134,0,0.06);
}
.blue-dashed{
    border: 1px dashed rgba(4,169,245,0.20);
    background:rgba(4,169,245,0.06);
    border-radius:2px;
    height:58px;
}
.quxian_flex{
    display: flex;
    justify-content: center;
}
.quxian_flex .child{
    display: flex;
    align-items: center;
}
.highcharts-tooltip-box{
    fill: #454545;
    stroke: #454545;
}
.highcharts-tooltip-box ~text{
    color: #ffffff !important;
    fill: #ffffff!important;
}
.highcharts-text-outline{
    stroke: none!important;
}
.user-select{
    user-select: none;
}
.commom_pb_bg .commom_flex-pd-content li.dayActive{
    background:rgba(251,134,0,0.08);
}
.hongdian{
    position: relative;
}
.hongdian:before{
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 5px;
    height: 5px;
    display: inline-block;
    background-color: rgba(56, 3, 3, 0.4196078431372549);
    border-radius: 100%;
}
.bg-green{
    color: #ffffff;
    background-color: #1abc9c;
}
.tong-btn-menu {
    min-width: 72px;
    padding: 5px 11px;
    border-radius: 4px;
    font-size: 14px;
    height: 32px;
    margin-right: 10px;
    margin-bottom: 10px;
    width: 240px;
    line-height: 22px;
    word-wrap: normal;
    overflow: hidden;
    text-align: left;
}
.btn-parmary-b-menu {
    border: 1px solid #1abc9c;
    color: #1abc9c;
    position: relative;
    background: #fff;
}
.btn-parmary-b-menu:hover {
    color: rgba(26, 188, 156, 0.6);
}