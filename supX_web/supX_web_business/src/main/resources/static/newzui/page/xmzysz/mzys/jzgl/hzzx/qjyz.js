var fyxmTab = new Vue({
    el: '.fyxm-tab',
    data: {
        num: 0,
        fypcfShow: '1',
        showNewTab:false,
    },
    created: function () {
        this.$nextTick(function () {
            this.tabBg('brPage/qjdj')
        })

    },
    methods: {
        tabBg: function (page) {
            $(".loadPage").load(page + ".html").fadeIn(300);
        }

    },
});

var userNameBg = new Vue({
    el: '.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        Brxx_List: {},
        page: '',
        qxks:'',

    },
    mounted: function () {
        if (sessionStorage.getItem('brPage1')) {

            if(JSON.parse(sessionStorage.getItem('brPage1')).pop() == '抢救') {
                Vue.set(fyxmTab,'showNewTab',true);
            }
            Vue.set(this, 'Brxx_List', JSON.parse(sessionStorage.getItem('brPage1'))[2]);
            Vue.set(fyxmTab, 'fypcfShow', this.Brxx_List.fypcfShow);
            Vue.set(fyxmTab, 'num', JSON.parse(sessionStorage.getItem('brPage1'))[1]);
            Vue.set(this,'qxks',JSON.parse(sessionStorage.getItem('brPage1'))[2].ghks);
            fyxmTab.tabBg(JSON.parse(sessionStorage.getItem('brPage1'))[0]);
        }
        // window.addEventListener('setItemEvent', function (e) {
        //     if (e.key == 'brPage1') {
        //         Vue.set(userNameBg, 'Brxx_List', JSON.parse(e.newValue)[2]);
        //         Vue.set(fyxmTab, 'num', JSON.parse(e.newValue)[1]);
        //         fyxmTab.tabBg(JSON.parse(e.newValue)[0]);
        //     }
        // });
        window.addEventListener('storage', function (e) {
            if (e.key == 'brPage1') {
                Vue.set(userNameBg, 'Brxx_List', JSON.parse(e.newValue)[2]);
                Vue.set(fyxmTab, 'num', JSON.parse(e.newValue)[1]);
                fyxmTab.tabBg(JSON.parse(e.newValue)[0]);
            }

        });
    },
    methods: {},
});

function tabBg(page, index, event,num) {
    if(num){
        userNameBg.page=page
    }
    if( userNameBg.page == page &&pageList==page){
    }else{
        pageList=page;
        userNameBg.page=''
        $('.isative').removeClass('active')
        fyxmTab.num = index;
        $(event).addClass('active')
        $(".loadPage").load(page + ".html",'',function () {
            if(num==1){
                setTimeout(function () {
                    xyz()
                },1000)
            }
        }).fadeIn(300);
    }
}

// var orignalSetItem = sessionStorage.setItem;
// sessionStorage.setItem = function (key, newValue) {
//     var setItemEvent = new Event('setItemEvent');
//     setItemEvent.newValue = newValue;
//     window.dispatchEvent(setItemEvent);
//     orignalSetItem.apply(this, arguments);
// };
