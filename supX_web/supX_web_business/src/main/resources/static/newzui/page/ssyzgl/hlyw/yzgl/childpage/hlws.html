<link rel="stylesheet" href="childpage/hlws.css">
<div class="hlws-box flex-one flex-container flex-dir-c" id="hlws-box">
	<div class="panel printHide">
		<div class="tong-top">
			<button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="getData()">刷新</button>
			<button class="tong-btn btn-parmary-b icon-ff" @click="saveData()">保存</button>
			<button class="tong-btn btn-parmary-b icon-ff" @click="print()">打印</button>
		</div>
	</div>

	<div class="tong-search printHide">
		<div class="zui-form">
			<div class="zui-inline">
				<label class="zui-form-label">文书类型</label>
				<div class="zui-input-inline wh138">
					<select-input @change-data="resultChange" :data-notEmpty="true" :child="Jhhlws_tran" :index="wslx" :val="wslx"
					 :name="'wslx'" :search="true"></select-input>
				</div>
			</div>
		</div>
	</div>

	<div class="show-ws-box flex-one over-auto">
		<div id="box0">
			<div class="ryhlpgd show-ws">
				<div class="header-top print-inside-avoid">{{BrxxJson.yljgmc}}</div>
				<h1 class="ws-title print-inside-avoid">入院护理评估单</h1>
				<div class="grid-box">
					<div class="col-xxl-3 print-inside-avoid">病区：<span v-text="BrxxJson.ryksmc"></span></div>
					<div class="col-xxl-3 print-inside-avoid">床号：<span v-text="BrxxJson.rycwbh"></span></div>
					<div class="col-xxl-6 print-inside-avoid">住院号：<span v-text="BrxxJson.zyh"></span></div>
					<div class="col-xxl-3 print-inside-avoid">姓名：<span v-text="BrxxJson.brxm"></span></div>
					<div class="col-xxl-3 print-inside-avoid">性别：<span v-text="brxb_tran[BrxxJson.brxb]"></span></div>
					<div class="col-xxl-3 print-inside-avoid">民族：<span v-text="BrxxJson.brmzmc"></span></div>
					<div class="col-xxl-3 print-inside-avoid">年龄：<span v-text="BrxxJson.nl+nldw_tran[BrxxJson.nldw]"></span></div>
					<div class="col-xxl-3 print-inside-avoid">联系人：<span v-text="BrxxJson.lxrxm"></span></div>
					<div class="col-xxl-3 print-inside-avoid">关系：<span v-text="BrxxJson.lxrgx"></span></div>
					<div class="col-xxl-6 print-inside-avoid">电话：<span v-text="BrxxJson.sjhm"></span></div>
					<div class="col-xxl-3 print-inside-avoid">婚姻状况：<span v-text="BrxxJson.hyzkmc"></span></div>
					<div class="col-xxl-3 print-inside-avoid">职业：<span v-text="BrxxJson.zylxmc"></span></div>
					<div class="col-xxl-6 print-inside-avoid ">文化程度：<input class="zui-input" class="text" style="width: 75mm;" @keyup="nextFocus($event)" v-model="modelContent.dyxm1" /></div>
					<div class="col-xxl-3 print-inside-avoid">入院形式：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 25mm;" v-model="modelContent.dyxm2" /></div>
					<div class="col-xxl-9 print-inside-avoid">入院日期：<span v-text="fDate(BrxxJson.ryrq,'date')"></span></div>
					<div class="col-xxl-12 print-inside-avoid">入院诊断：<span v-text="BrxxJson.ryzdmc"></span></div>
					<div class="col-xxl-3 print-inside-avoid">体温：<input @keyup="nextFocus($event)" class="zui-input" class="number" style="width: 15mm;" v-model="modelContent.dyxm3" />°C</div>
					<div class="col-xxl-3 print-inside-avoid">脉搏：<input @keyup="nextFocus($event)" class="zui-input" class="number" style="width: 15mm;" v-model="modelContent.dyxm4" />次/分</div>
					<div class="col-xxl-3 print-inside-avoid">呼吸：<input @keyup="nextFocus($event)" class="zui-input" class="number" style="width: 15mm;" v-model="modelContent.dyxm5" />次/分</div>
					<div class="col-xxl-3 print-inside-avoid">血压：<input @keyup="nextFocus($event)" class="zui-input xy" class="number" v-model="modelContent.dyxm21" />/<input
						 class="zui-input xy" class="number" @keyup="nextFocus($event)" v-model="modelContent.dyxm6" />mmHg</div>
					<div class="col-xxl-3 print-inside-avoid">意识：<input @keyup="nextFocus($event)" class="zui-input" class="number" v-model="modelContent.dyxm7" /></div>
					<div class="col-xxl-3 print-inside-avoid">GCS评分：<input @keyup="nextFocus($event)"@keyup="nextFocus($event)" class="zui-input" class="number" v-model="modelContent.dyxm8" /></div>
					<div class="col-xxl-6 print-inside-avoid">过敏史：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 78mm;" v-model="modelContent.dyxm9" /></div>
					<div class="col-xxl-4 print-inside-avoid">沟通能力：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 40mm;" v-model="modelContent.dyxm10" /></div>
					<div class="col-xxl-4 print-inside-avoid">自理能力：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 40mm;" v-model="modelContent.dyxm11" /></div>
					<div class="col-xxl-4 print-inside-avoid">疼痛评估：<input @keyup="nextFocus($event)" class="zui-input" class="number" style="width: 40mm;"
						 v-model="modelContent.dyxm12" />分</div>
					<div class="col-xxl-12 print-inside-avoid">皮肤状况：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 170mm;"
						 v-model="modelContent.dyxm13" /></div>
					<div class="col-xxl-6 print-inside-avoid">压疮评估危险：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 65mm;"
						 v-model="modelContent.dyxm14" /></div>
					<div class="col-xxl-6 print-inside-avoid">跌倒/坠床评估危险：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 58mm;"
						 v-model="modelContent.dyxm15" /></div>
					<div class="col-xxl-3 print-inside-avoid">睡眠：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 35mm;" v-model="modelContent.dyxm16" /></div>
					<div class="col-xxl-3 print-inside-avoid">饮食：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 35mm;" v-model="modelContent.dyxm17" /></div>
					<div class="col-xxl-3 print-inside-avoid">大便：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 35mm;" v-model="modelContent.dyxm18" /></div>
					<div class="col-xxl-3 print-inside-avoid">小便：<input @keyup="nextFocus($event)" class="zui-input" class="text" style="width: 35mm;" v-model="modelContent.dyxm19" /></div>
					<div class="col-xxl-12">
						<div class="grid-box">
							<div class="col-xxl-3 print-inside-avoid">入院介绍/指导：</div>
							<div class="col-xxl-9">
								<div class="grid-box">
									<div class="col-xxl-4 print-inside-avoid">
										<input type="checkbox" id="checkbox-1" v-model="ryjszd"  true-value="1" false-value="0"  class="h-checkbox">
										<label for="checkbox-1">病室环境</label>
									</div>
									<div class="col-xxl-4 print-inside-avoid">
										<input type="checkbox" id="checkbox-2" v-model="ryjszd" true-value="1" false-value="0"   class="h-checkbox">
										<label for="checkbox-2">医护人员</label>
									</div>
									<div class="col-xxl-4 print-inside-avoid">
										<input type="checkbox" id="checkbox-3" v-model="ryjszd" true-value="1" false-value="0"   class="h-checkbox">
										<label for="checkbox-3">探视制度</label>
									</div>
									<div class="col-xxl-4 print-inside-avoid">
										<input type="checkbox" id="checkbox-4" v-model="ryjszd" true-value="1" false-value="0"   class="h-checkbox">
										<label for="checkbox-4">膳食安排</label>
									</div>
									<div class="col-xxl-4 print-inside-avoid">
										<input type="checkbox" id="checkbox-5" v-model="ryjszd" true-value="1" false-value="0"   class="h-checkbox">
										<label for="checkbox-5">离院须知</label>
									</div>
									<div class="col-xxl-4 print-inside-avoid">
										<input type="checkbox" id="checkbox-6" v-model="ryjszd" true-value="1" false-value="0"   class="h-checkbox">
										<label for="checkbox-6">安全教育</label>
									</div>
									<div class="col-xxl-4 print-inside-avoid">
										<input type="checkbox" id="checkbox-7" v-model="ryjszd" true-value="1" false-value="0"   class="h-checkbox">
										<label for="checkbox-7"><label for="checkbox-7">特别指导</label></label>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xxl-12 print-inside-avoid">专科情况：<textarea style="width: 170mm; height: 20mm;vertical-align: top;"
						 class="zui-input" v-model="modelContent.dyxm20"></textarea></div>
				</div>
				<div class="grid-box" style="margin-top: 40mm;">
					<div class="col-xxl-6 print-inside-avoid text-right">签名：<span v-text="modelContent.creatusername"></span></div>
					<div class="col-xxl-6 print-inside-avoid text-right" style="padding-right: 35mm;">手工签名：</div>
					<div class="col-xxl-8 text-right print-inside-avoid">记录时间：<span v-text="fDate(modelContent.zjxgsj,'date')"></span></div>
					<div class="col-xxl-8 print-inside-avoid"></div>
				</div>
			</div>
		</div>
	</div>
</div>
<script src="childpage/hlws.js" type="text/javascript"></script>
