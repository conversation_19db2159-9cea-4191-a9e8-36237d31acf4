<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室申领</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="dbgl.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd()">开单</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="flex-container flex-align-c padd-b-10">
                <div class="flex-container flex-align-c padd-r-10">
                    <span class="ft-14 padd-r-10 whiteSpace ">当前科室</span>
                        <select-input @change-data="resultChanges"  class="wh122"
                                      :not_empty="false" :child="yfList"
                                      :index="'yfmc'" :index_val="'yfbm'"
                                      :val="popContent.yfbm" :search="true" :name="'popContent.yfbm'"
                                      id="yfbm" :index_mc="'yfbm'">
                        </select-input>
                </div>
                <div class="flex-container flex-align-c padd-r-10">
                    <span class="ft-14 padd-r-10 whiteSpace ">审核标志</span>
                    <select-input @change-data="resultChange"
                                  :child="ckglzt_tran"
                                  class="wh122"
                                  :index="param.zt"
                                  :val="param.zt"
                                  :name="'param.zt'" @keydown="nextFocus($event)" >
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-r-10">
                    <span class="ft-14 padd-r-10 whiteSpace ">时间段</span>
                    <div class="  flex-container flex-align-c margin-f-l5">
                        <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="flex-container flex-align-c">
                    <span class="ft-14 padd-r-10 whiteSpace ">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="param.parm" @keydown.enter="goToPage(1)"/>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view   "  >
        <!--列表-->
        <div class="zui-table-header" >
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>申请单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>开单时间</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>开单员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>调入科室</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>调出科室</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>申请人</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>申请日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in dbdList" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','dbdList'],$event)" @dblclick="showDetail(item)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ckdh">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zdrxm">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.dbyfmc">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfmc">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jbrmc">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')"></div>
                    </td>
                    <td>
                        <!--v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.sfzfbz=='1' ? 'color-ysh' : item.sfzfbz=='2' ? 'color-yzf' : '' "-->
                        <div class="zui-table-cell cell-s" >
                            <span v-if="item.yfbm==popContent.yfbm" v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : '' "></span>
                            <span v-else-if="item.shzfbz== 0 && item.dbyf == popContent.yfbm" v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : '' " ></span>
							<span v-else v-text="queren[item.qrbz]" :class="item.qrbz=='0' ? 'color-wc':item.qrbz=='1'? 'color-dsh' :''" ></span>
                        </div>
                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                            <span class="flex-center padd-t-5">
                                <em  class="width30"  v-if="item.shzfbz== 0 && item.dbyf != popContent.yfbm"><i class="icon-sh"  data-title="审核" @click="showDetail(item)"></i></em>
                                <em  class="width30"><i class="icon-js" v-if="item.shzfbz == 0" data-title="作废" @click="invalidData($index)"></i></em>
                                <em  class="width30"><i class="icon-bj" v-show="item.shzfbz != 1 && item.shzfbz != 2" data-title="编辑" @click="showDetail(item)"></i></em>
                               </span>

                        </div>
                    </td>
                    <p v-show="dbdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
</div>
<script src="dbgl.js"></script>
</body>

</html>
