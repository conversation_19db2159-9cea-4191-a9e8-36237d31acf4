    //权限科室编码
    var qxksbm = '';
    //盘点生成页面vm
    var toolMenu_0 = new Vue({
        el: '.toolMenu_0',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            //分页信息
            dg: {
                page: 1,
                rows: 2000,
                sort: "",
                order: "asc",
            },
        },
        methods: {
            //生成盘点表
            add: function () {
                //清空库存明细列表
                enter.jsonList = [];
                if (enter.hasWhpdb != '') {
                    malert(enter.hasWhpdb,'top','defeadted');
                    return;
                }
                //库房非空判断
                if (enter.pdbContent.kfbm == 0) {
                    malert("请先选择库房！",'top','defeadted');
                    return;
                }
                //材料种类非空
                if (enter.pdbContent.pdfs == 1 && (enter.pdbContent.ypzl == null || enter.pdbContent.ypzl == undefined || enter.pdbContent.ypzl == '')) {
                    malert("请先选择材料种类！",'top','defeadted');
                    return;
                }
                //单种材料非空
                if (enter.pdbContent.pdfs == 2 && (enter.pdbContent.ypmc == null || enter.pdbContent.ypmc == undefined || enter.pdbContent.ypmc == '')) {
                    malert("请先选择单种材料！",'top','defeadted');
                    return;
                }
                malert("正在生成盘点表，请稍后！",'top','success');
                //准备参数
                var bean = {
                    "kfbm": enter.pdbContent.kfbm,
                    //盘点标志 盘点标志为1显示0库存
                    'pdbz': 1
                };
                //按种类
                if (enter.pdbContent.pdfs == 1) {
                    bean.zlbm = enter.pdbContent.zlbm;
                } else if (enter.pdbContent.pdfs == 2) {
                    bean.ypbm = enter.pdbContent.ypbm;
                }
                //获取库存明细列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' + '&dg=' + JSON.stringify(this.dg) +
                    "&json=" + JSON.stringify(bean),
                    function (json) {

                        if (json != null || json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                //计算零价金额
                                json.d.list[i].ljje = Math.round(json.d.list[i].yplj * json.d.list[i].kcsl * 100) / 100;
                                //设置实存数量
                                json.d.list[i].scsl = 0;
                            }
                            enter.jsonList = json.d.list;
                        } else {
                            malert("材料信息获取失败！",'top','defeadted');
                            return;
                        }
                    });
            },
            //保存盘点表
            save: function () {
                //库房非空判断
                if (enter.pdbContent.kfbm == 0) {
                    malert("请先选择库房！",'top','defeadted');
                    return;
                }
                //准备盘点表参数
                var pdb = {
                    'kfbm': enter.pdbContent.kfbm,
                    'bzms': '盘点管理'
                };
                var json = {
                    list: {
                        'pdb': pdb,
                        'pdbmx': enter.jsonList
                    }
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=save', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("数据保存成功",'top','success');
                        } else {
                            if (data.body.d == 0) {
                                malert(data.body.c,'top','success');
                            } else {
                                malert("数据提交失败",'top','defeadted');
                            }
                        }
                        //清空页面
                        toolMenu_0.clearAll();
                    }, function (error) {
                        console.log(error);
                    });
            },

            //清空盘点表生成页面
            clearAll: function () {
                enter.jsonList = [];
//              enter.pdbContent = [];
                enter.selectPdfs = '0';
                enter.ypzlShow = false;
                enter.ypmcShow = false;
            }
        }
    });

    var enter = new Vue({
        el: '.enter',
        mixins: [tableBase],
        data: {
            pdWay: 0,
            //是否存在未审核盘点表
            hasWhpdb: '',
            jsonList: [],
            //库房列表
            KFList: [],
            //参数属性
            csParm: {},
            //盘点单
            pdbContent: {},
            //显示材料种类下拉框
            ypzlShow: false,
            ypmcShow: false,
            //库存明细列表复选框
            isChecked: [],
            //盘点方式
            selectPdfs: '0',
            //盘点方式下拉框
            options: [{
                text: '全部材料',
                value: '0'
            },
                {
                    text: '种类材料',
                    value: '1'
                },
                {
                    text: '单种材料',
                    value: '2'
                },
            ],
            //盘点类型子菜单列表
            infoList: [],
        },

        //启动加载
        mounted: function () {
            //加载时获取库房信息
            this.getKFData();
            //设置库房默认值
            this.pdbContent.kfbm = 0;
        },
        methods: {
            //获取科室权限
            getCsqx: function () {
                var parm = {
                    "ksbm": qxksbm,
                    "ylbm": "N040100011006"
                };
                //获取科室权限
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        if (json.d.length > 0) {
                            //一级库房盘点表生成权限，1-有，0-无
                            for (var i = 0; i < json.d.length; i++) {
                                if (json.d[i].csqxbm == 'N04010001100601') {
                                    if (json.d[i].csz == '1') {
                                        //代码未添加
                                    } else {
                                        //代码未添加
                                    }
                                }
                                //一级库房盘点表作废权限，1-有，0-无
                                if (json.d[i].csqxbm == 'N04010001100602') {
                                    if (json.d[i].csz == '0') {
                                        //代码未添加
                                    } else {
                                        //代码未添加
                                    }
                                }
                                //一级库房盘点数据录入权限，1-有 0-无
                                if (json.d[i].csqxbm == 'N04010001100603') {
                                    if (json.d[i].csz == '0') {
                                        //代码未添加
                                    } else {
                                        //代码未添加
                                    }
                                }
                                //一级库房盘点录入增加材料权限，1-有 0-无
                                if (json.d[i].csqxbm == 'N04010001100604') {
                                    if (json.d[i].csz == '0') {
                                        //代码未添加
                                    } else {
                                        //代码未添加
                                    }
                                }
                                //一级库房盘点录入单审核权限，1-有 0-无
                                if (json.d[i].csqxbm == 'N04010001100605') {
                                    if (json.d[i].csz == '0') {
                                        //代码未添加
                                    } else {
                                        //代码未添加
                                    }
                                }
                                //一级库房盘点录入单作废权限，1-有 0-无
                                if (json.d[i].csqxbm == 'N04010001100606') {
                                    if (json.d[i].csz == '0') {
                                        //代码未添加
                                    } else {
                                        //代码未添加
                                    }
                                }
                                //一级库房盘点表确认权限，1-有 0-无
                                if (json.d[i].csqxbm == 'N04010001100607') {
                                    if (json.d[i].csz == '0') {
                                        //代码未添加
                                    } else {
                                        //代码未添加
                                    }
                                }
                            }
                        }
                    }
                });
            },
            //库房改变后重新获取权限科室编码，保存盘点表后刷新库房列表也可触发此方法
            opChange: function (event, type) {
                if (type == 'ksbm') {
                    //设置库房编码
                    document.getElementById('_kfbm').value = enter.pdbContent.kfbm;
                    var obj = event.currentTarget;
                    var selected = $(obj).find("option:selected");
                    qxksbm = selected.attr(type) == undefined ? qxksbm : selected.attr(type); //获取科室编码
                    this.getCsqx(); //选中库房之后再次请求获取参数权限

                    //判断是否存在未审核的盘点表
                    var parm = {
                        'kfbm': enter.pdbContent.kfbm,
                        'qrzfbz': 0
                    };
                    //查询盘点表
                    $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywPdb&types=pdbList&parm=' + JSON.stringify(parm), function (json) {
                        if (json != null && json.a == 0) {
                            var str = '';
                            if (json.d.length > 0) {
                                for (var i = 0; i < json.d.length; i++) {
                                    str += ('【' + json.d[i].pdpzh + '】 ');
                                    enter.hasWhpdb = '盘点表' + str + '未审核';
                                }
                            } else {
                                enter.hasWhpdb = '';
                            }
                        } else {
                            malert('查询未审核盘点表失败！','top','defeadted')
                        }
                    });
                    //判断是否存在未审核的盘点表end

                } else if (type == 'zlbm') {
                    //获取材料种类编码
                    enter.pdbContent.zlbm = event.currentTarget.value;
                } else if (type == 'ypbm') {
                    //获取材料编码
                    enter.pdbContent.ypbm = event.currentTarget.value;
                }
            },

            //判断是否有操作权限
            hasCx: function (cx) {
                if (!cx) {
                    malert("用户没有操作权限！",'top','defeadted');
                    return true;
                }
            },

            //获取库房信息
            getKFData: function () {
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011006"}',
                    function (data) {
                        if (data.a == 0) {
                            //库房下拉框集合赋值
                            enter.KFList = data.d;
                            //默认获取该用例第一个科室的权限科室编码
                            qxksbm = data.d[0].ksbm;
                            //启动获取权限参数
                            enter.getCsqx();
                        } else {
                            malert("一级库房获取失败",'top','defeadted');
                        }
                    });
            },

            //盘点类型改变时获取响应的子菜单信息,参数：分页信息/查询类型/查询参数/错误信息
            getInfo: function (dg, type, bean, errInfo) {
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=' + type + '&dg=' + JSON.stringify(dg) +
                    "&json=" + JSON.stringify(bean),
                    function (json) {
                        if (json != null || json.d.list.length > 0) {
                            enter.infoList = json.d.list;
                        } else {
                            malert(errInfo,'top','defeadted');
                            return;
                        }
                    });
            },

            //盘点类型改变
            selChange: function (val) {
                //库房非空判断
                if (enter.pdbContent.kfbm == null || enter.pdbContent.kfbm == undefined || enter.pdbContent.kfbm == '') {
                    malert("请先选择库房！",'top','defeadted');
                    //清空盘点方式
                    val.currentTarget.value = 0;
                    return;
                }

                //根据盘点方式显示子菜单
                if (val.currentTarget.value == 1) {
                    //盘点方式赋值
                    enter.pdbContent.pdfs = 1;
                    enter.ypzlShow = true;
                    enter.ypmcShow = false;
                } else if (val.currentTarget.value == 2) {
                    //盘点方式赋值
                    enter.pdbContent.pdfs = 2;
                    enter.ypzlShow = false;
                    enter.ypmcShow = true;
                } else {
                    //盘点方式赋值
                    enter.pdbContent.pdfs = 0;
                    enter.ypzlShow = false;
                    enter.ypmcShow = false;
                }
                //准备参数
                var bean = {
                    "kfbm": enter.pdbContent.kfbm
                };
                //根据盘点方式不同，生成相应的字菜单
                if (enter.selectPdfs == 1) {
                    //获取材料种类。参数：分页信息/查询类型/查询参数/错误信息
                    enter.getInfo(toolMenu_0.dg, 'ypzl', bean, "材料种类获取失败！");

                } else if (enter.selectPdfs == 2) {
                    //查询单个材料库存。参数：分页信息/查询类型/查询参数/错误信息
                    enter.getInfo(toolMenu_0.dg, 'ypzd', bean, "材料信息获取失败！");
                }
            },
        },
    })
