<style  type="text/css" >
    @media print{
        @page{
            size:70mm 40mm;
            
        }
    }
	
    .loadingPrint{
        position: absolute;
    }
    .print-box{
        width: 60mm;
        
        page-break-after: always;
        page-break-inside:avoid;
        /* padding-top:10px; */
        padding-left: 0.3mm;
        padding-right: 0.3mm;
        font-size: 3.3mm;
        color: #000;
        font-family: '宋体';
    }
    .lyjdy:first-child{
			margin-top: -2.51768mm;
        }
    .xmmc_box{
        height: 20mm;
    }
    .header{
        display: flex;
    }
    .header div{
        margin: 0 1mm 0 2mm;
    }
    .cont{
        display: flex;
        
        border-bottom: 0.1mm solid #000;
    }
    .cont div{
        margin: 0 1mm 0 2mm;
    }
	.xmmc_box{
		margin: 1mm 0 0 2mm;
	}
</style>
<div id="print-box" class="" v-cloak >
    <div :data-index="index" class="lyjdy print-box printShow" v-for="(item,index) in printChecked">
        <div class="header">
            <div>{{item.rycwbh}}床</div>
            <div>{{item.brxm}}</div>
            <div>{{brxb_tran[item.brxb]}}</div>
            <div>{{item.nl}}岁</div>
            <div>{{item.zyh}}</div>
        </div>
        <div class="cont">
            <div>{{item.yzlxmc.slice(0,-1)}}</div>
            <div>{{item.pcmc}}</div>
            <div>{{item.yyffmc}}</div>
            <div>{{item.yyrq}}</div>
            <div v-if="item.sysd!=0">{{item.sysd}}{{item.sysddw}}</div>
        </div>
        <div  class="xmmc_box" style="">
            <div class="flex-container   flex-jus-sb" v-for="(childItem,index) in item.yzxx">
                <div>{{childItem.xmmc}}</div>
                <div>{{childItem.jl}}{{childItem.jldwmc}}</div>
            </div>
        </div>
    </div>
</div>
<!--    <div :data-index="index" class="print-box printShow" v-for="(item,index) in printChecked">-->
<!--        <div class="flex-container   flex-jus-sb border-bottom">-->
<!--            <div class="font-weight">{{item.ksmc}}</div>-->
<!--            <div class="font-weight">{{fDate(item.yyrq,'date')}}</div>-->
<!--            <div class="font-weight">{{item.yysj}}</div>-->
<!--        </div>-->
<!--        <div class="flex-container   flex-jus-sb border-bottom">-->
<!--            <div class="font-weight" style="font-size: 14px">{{item.brxm}}</div>-->
<!--            <div class="font-weight" style="font-size: 14px">{{item.rycwbh}}床</div>-->
<!--            <div class="font-weight">{{item.zyh}}</div>-->
<!--        </div>-->
<!--        <div class="flex-container   flex-jus-sb border-bottom">-->
<!--            <div>药品名称</div>-->
<!--            <div>剂量</div>-->
<!--        </div>-->
<!--        <div  class=" border-bottom xmmc_box">-->
<!--            <div class="flex-container   flex-jus-sb" v-for="(childItem,index) in item.yzxx">-->
<!--                <div>{{childItem.xmmc}}({{childItem.ypgg}})</div>-->
<!--                <div>{{childItem.jl}}{{childItem.jldwmc}}</div>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="flex-container   flex-jus-sb border-bottom">-->
<!--            <div>{{item.yyffmc}}</div>-->
<!--            <div>{{item.pcmc}}</div>-->
<!--            <div>{{item.sysd}} {{item.sysddw}}</div>-->
<!--        </div>-->
<!--        <div>配液/时间:</div>-->
<!--        <div>执行/时间:</div>-->
<!--       <div class="height-50">备注说明:{{item.bzsm}}早餐。中餐前12单位/次，晚餐钱10单位/次</div>-->
<!--    </div>-->

<!-- </div> -->
<script type="text/javascript">
    window.printBox=new Vue({
        el:'#print-box',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            isShow:false,
            printHide:false,
            printChecked:[],
			brxb_tran: {
			    "1": "男",
			    "2": "女",
			    "9": "未知"
			},
        },
        mounted:function () {
            console.log(this.printChecked)
        }
    })
</script>
