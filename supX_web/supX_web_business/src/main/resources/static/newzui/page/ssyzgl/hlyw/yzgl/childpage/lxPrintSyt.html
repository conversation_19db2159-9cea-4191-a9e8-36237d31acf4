<style type="text/css" >
    @media print{
        @page{
            size:80mm 50mm;
            /*margin:0 0.5mm 0;*/
            margin:0.5mm
        }
    }
    .print-box{
        width: 77mm;
        /*height: 60mm;  !*去掉了高度展示，渲染不会出错，不知道打印如何*!*/
        padding-top:10px;
        padding-left: 0.8mm;
        padding-right: 0.8mm;
        /*overflow: hidden;*/
        page-break-after: always;
        font-size: 2mm;
        color: #000;
        font-family: '微软雅黑 宋体 Arial';
        /*position: absolute;*/
    }
    .print-box:nth-child(1){
        padding-top: 0;
    }
    .xmmc_box{
        height: 25mm;
        overflow: hidden;
    }
    .printLeft{
        width: 70%;
        float: left;
    }
    .printRight{
        float: right;
    }
</style>
<div id="print-box"  class="over-auto" v-cloak >

    <div :data-index="index" class="print-box printShow flex-container flex-jus-sb" v-for="(item,index) in printChecked">
        <div class="printLeft">
            <div class="flex-container   flex-jus-sb ">
                <div class="font-weight">长</div>
                <div class="font-weight">{{item.rycwbh}}</div>
                <div class="font-weight font-14">{{item.brxm}}</div>
                <div class="font-weight">{{item.nl}}{{nldw_tran[item.nldw]}}</div>
            </div>
            <div class="flex-container   flex-jus-sb ">
                <div>组号</div>
                <div>1组</div>
            </div>
            <div  class="  xmmc_box">
                <div class="flex-container   flex-jus-sb" v-for="(childItem,index) in item.yzxx">
                    <div>{{childItem.xmmc}}({{childItem.ypgg}})</div>
                    <div>{{childItem.jl}}{{childItem.jldwmc}}</div>
                </div>
            </div>
            <div class="flex-container   flex-jus-sb border-bottom">
                <div>{{item.yyffmc}}</div>
                <div>{{item.pcmc}}</div>
                <div>{{item.sysd}} {{item.sysddw}}</div>
            </div>
        </div>
        <div class="printRight" :class="'qrcode'+index" :data-code="setClassCode(index,item)"></div>
    </div>
</div>
<script src="/newzui/js/plugins/jquery.qrcode.min.js"></script>
<script type="text/javascript">
    window.printBox=new Vue({
        el:'#print-box',
        mixins: [dic_transform, tableBase, baseFunc, mformat],
        data:{
            isShow:false,
            printHide:false,
            printChecked:[],
        },
        methods:{
            setClassCode:function (index,item) {
                this.$nextTick(function () {
                        jQuery('.qrcode'+index).qrcode({
                            render: "canvas",
                            id:"qrcode"+index,
                            width: 60,
                            height: 60,
                            text: item.zyh
                        });
                })
            }
        },
        mounted:function () {
            console.log(this.printChecked)
        }
    })
</script>
