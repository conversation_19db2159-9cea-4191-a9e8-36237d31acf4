var bgdList = new Vue({
    el: '#bgdList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        djList: [],
        isChecked: [],
        csqxContent:{},
        hisbgdybz:'2',
        IsPrint_tran: {
            '2':'全部',
            '0': '未打印',
            '1': '已打印',
        },
    },
    mounted: function () {
        this.getData()
    },
    methods: {
        resultChangeData: function (val) {
            this.hisbgdybz=val[0]
            this.getData()
        },
        getData() {
            this.djList = [];
            this.getJyData();
            this.getJcData();31
        },
        getReCheckBox: function (val) {
            if(val[0] =='all' ){
                this.isCheckAll = val[2];
                if (this.isCheckAll) {
                    for (var i = 0; i < this.djList.length; i++) {
                        if(!this.djList[i].ifJcbgd){
                            Vue.set(this.isChecked, i, true);
                            Vue.set(jydy.jyxhs, i, this.djList[i].jyxh);
                        }
                    }
                }else {
                    this.isChecked=[];
                    jydy.jyxhs=[];
                }
            } else if(val[0] =='some' ){
                Vue.set(this.isChecked, val[1], val[2]);
                if (val[2]) {
                    Vue.set(jydy.jyxhs, val[1], this.djList[val[1]].jyxh);
                } else {
                    Vue.delete(jydy.jyxhs, val[1]);
                }
            }
            jcbgd.close();
            jydy.open();
            var jyxhs = jydy.jyxhs;
            jydy.xqxx = [];
            jydy.getJyPrint(jyxhs.join(','))
        },
        // 查询检验列表
        getJyData: function () {
            //后台查询数据
            var parm = {
                brid: userNameBg.Brxx_List.brid,
                bah: userNameBg.Brxx_List.ghxh,
                hisbgdybz:this.hisbgdybz =='2'?'':this.hisbgdybz
            };
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_bgcx&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        bgdList.djList = bgdList.djList.concat(data.d.list);
                    }
                } else {
                    malert("检验报告单列表查询失败" + data.c, "top", "defeadted");
                }
            });
        },
        // 查询检查列表
        getJcData: function () {
            if (fyxmTab.csqx.N03001200163){
                //翼展pacs配置不为空，调用翼展pacs接口
                var parm = {
                    //http://192.168.201.6:8010/PACSService.asmx/GetPACSExamRequestList
                    serverUrl:fyxmTab.csqx.N03001200163 + '/PACSService.asmx/GetPACSExamRequestList',
                    zyh:userNameBg.Brxx_List.ghxh,
                };
                this.$http.get("/actionDispatcher.do", {
                    params: {
                        reqUrl: 'YzPacsNew',
                        types: 'queryYzPacs',
                        parm: JSON.stringify(parm)
                    }
                }).then(function (data) {
                    if (data.body.a == 0){
                        this.getYzPacsData(data.body.d);
                    } else {
                        malert("查询失败:" + data.body.c, "top", "defeadted");
                    }
                });
            }else {
                var pram = {
                    CLINICNO: userNameBg.Brxx_List.ghxh,
                    hisbgdybz:this.hisbgdybz =='2'?'':this.hisbgdybz
                };
                var _url = "/actionDispatcher.do";
                this.$http.get(_url, {
                    params: {
                        reqUrl: 'YzPacsNew',
                        types: 'yzpacs_queryReport',
                        parm: JSON.stringify(pram)
                    }
                }).then(function (data) {
                    if (data.body.a == 0) {
                        if (data.body.d != null && data.body.d.list.length > 0) {
                            var _list = data.body.d.list;
                            for (var i = 0; i < _list.length; i++) {
                                _list[i].fymc = _list[i].convertHtml;//"检查报告单" + (Number(i) + 1);
                                _list[i].ifJcbgd = true;
                                bgdList.djList.push(_list[i]);
                            }
                        }
                    } else {
                        malert("检查报告单列表查询失败" + data.body.c, "top", "defeadted");
                    }

                })
            }
        },
        //解析翼展pacs查询报告
        getYzPacsData: function(data) {

            var parse = new DOMParser();
            var xml = parse.parseFromString(data, "text/xml");
            var resultJsonStr = xml.documentElement.textContent;
            var resultJson = JSON.parse(resultJsonStr);
            for (var i=0; i<resultJson.RecordCount; i++){
                var jcbgObj = {};
                jcbgObj.ifJcbgd = true;
                $(resultJson.Result[i]).find('Field').each(function () {
                    var field = $(this);
                    if (field.attr('Name') == 'ExamRequestID') { //检查id
                        jcbgObj.jcid = field.text();
                    }
                    if (field.attr('Name') == 'HISExamItemName') { //项目名称
                        jcbgObj.fymc = field.text();
                    }
                    if (field.attr('Name') == 'HISID') { //住院号
                        jcbgObj.zyh = field.text();
                    }
                    if (field.attr('Name') == 'ExamTime') { //检查时间
                        jcbgObj.shrq = field.text();
                    }
                    if (field.attr('Name') == 'SysType') { //检查类型
                        jcbgObj.sysType = field.text().split("_")[1];
                    }
                    if (field.attr('Name') == 'PACSExamRequestStatus') { //报告状态
                        jcbgObj.reportType = field.text();
                    }
                });
                if (jcbgObj.reportType == 'Reported') {
                    bgdList.djList.push(jcbgObj);
                }
            }
        },
        showDj: function (index) {
            if (this.djList[index].ifJcbgd) { // 检查
                if (fyxmTab.csqx.N03001200163) {
                    var jcid = this.djList[index].jcid;
                    var sysType = this.djList[index].sysType;
                    var param = {
                        jcid:jcid,
                        sysType:sysType
                    };
                    this.isChecked = [];
                    jydy.close();
                    jybgd.close();
                    jcbgd.open(param);
                }else {
                    var param = {
                        src: this.djList[index].html,
                        ptnid: this.djList[index].ptnid,
                    };
                    this.isChecked = [];
                    jydy.close();
                    jcbgd.open(param);
                }
            } else { // 检验
                // jcbgd.close();
                // jydy.open();
                // this.getReCheckBox(['some',index,!this.isChecked[index],this.djList])
                // jybgd.open(this.djList[index]);
            }
        }
    }
});

var jcbgd = new Vue({
    el: '#jcbgd',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifShow: false,
        ifYzPacs: false,
        serveIp: "************",
        ptnid: "",
        iframeSrc: "",
        jcid: "",
        sysType: "",
        jcbgdxq: []
    },
    computed: {
        bgdImageHref: function () {
            return "http://" + this.serveIp + "/DicomWeb/DicomWeb.dll/OpenImage?User=1&Password=1&PTNID=" + this.ptnid;
        },
        yzPacsBgUrl: function () {
            return fyxmTab.csqx.N03001200163 +"/Report.html?HISExamRequestID="+ this.jcid + "&ExamRequestID=" + this.jcid + "&SysType=" + this.sysType;
        },
        yzPacsTpUrl: function () {
            return fyxmTab.csqx.N03001200163 +"/poly2/app/viewer.html?HISExamRequestID="+ this.jcid + "&ExamRequestID=" + this.jcid + "&SysType=" + this.sysType;
        }
    },
    methods: {
        open: function (param) {
            if (fyxmTab.csqx.N03001200163){
                this.ifYzPacs = true;
                this.jcid = param.jcid;
                this.sysType = param.sysType;
            }else {
                if (!param.ptnid) console.error("检查报告单的pinid错误！请检查。");
                else this.ptnid = param.ptnid;

                if (!param.src) console.error("检查报告单的src错误！请检查。");
                else this.iframeSrc = param.src;
            }
            this.ifShow = true;
        },
        close: function () {
            this.ifShow = false;
            this.ptnid = "";
            this.iframeSrc = "";
        }
    }
});

var jybgd = new Vue({
    el: '#jybgd',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifShow: false,
        jybgd: {},
        jybgdmx: [],
        yljgmc: '',
    },
    created: function () {
        if (sessionStorage.getItem('yljgOrUser' + userId)) {
            this.yljgmc = JSON.parse(sessionStorage.getItem('yljgOrUser' + userId)).yljgmc;
        }
    },
    methods: {
        open: function (jybgd) {
            this.jybgd = jybgd;
            this.getJymx(jybgd.jyxh);
            this.ifShow = true;
        },
        close: function () {
            this.ifShow = false;
            this.jybgd = {};
            this.jybgdmx = [];
        },
        getJymx: function (jyxh) {
            var parm = {
                jyxh: jyxh,
            };
            jybgd.jybgdmx = []
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_bgmxcx&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    jybgd.jybgdmx = data.d.list;
                } else {
                    malert("检验明细查询失败！" + data.c, "top", "defeadted");
                }
            });
        },
    }
});

var jydy = new Vue({
    el: '#jydy',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        yljgmc: '',
        ifShow: false,
        showJy: false,
        jyxhs: [],
        ysData: [],
        csqxContent:{},
        jbxx: {}, // 基本信息
        xqxx: {}, // 详情信息
        json: {
            ysbz: '1',
            tybz: '0',
        },
        dg: {
            page: '1',
            rows: '',
            parm: '',
        },
    },
    created: function () {
        if (sessionStorage.getItem('yljgOrUser' + userId)) {
            this.yljgmc = JSON.parse(sessionStorage.getItem('yljgOrUser' + userId)).yljgmc;
        }
        this.getYs()
        this.getCsqx()
    },
    methods: {
        setImg:function(bm){
            return  jydy.csqxContent.N03001200131.replace('12222','12223')+ '/images/ysqm/'+bm+'.jpg'
        },
        getCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N030012001',
                "ksbm": ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d && json.d.length > 0) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N03001200131": //排队叫号请求地址
                                if (csjson.csz) {
                                    jydy.csqxContent.N03001200131 = csjson.csz;
                                }
                                break;
                        }
                    }
                    console.log(jydy.csqxContent.N03001200131)
                }
            })
        },
        close: function () {
            this.ifShow = false;
            this.xqxx = [];
            // this.ptnid = "";
            // this.iframeSrc = "";
        },
        open: function () {
            this.ifShow = true;
        },
        sjys: function (bm) {
            if (bm != '' || bm != undefined) {
                for (var i = 0; i < this.ysData.length; i++) {
                    if (bm == this.ysData[i].rybm) {
                        return this.ysData[i].ryxm
                    }
                }
            }
        },
        getYs: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(this.json) + "" + "&dg=" + JSON.stringify(this.dg), function (data) {
                if (data.a == '0' && data.d.list.length != 0) {
                    jydy.ysData = data.d.list;
                }
            });
        },
        printUpdated: function () {
            var saveList = []
            for (var i = 0; i < bgdList.isChecked.length; i++) {
                if (bgdList.isChecked[i] == true) {
                    saveList.push({
                        jyxh: bgdList.djList[i].jyxh,
                        dybz: '1'
                    })
                }
            }
            var json = '{"list":' + JSON.stringify(saveList) + '}';
            this.postAjax('/actionDispatcher.do?reqUrl=YzPacsNew&types=updateHisbgdybz',
                json,function (data) {
                    if (data.a == 0) {
                        jydy.getData()
                        malert("更新成功", 'top', 'success');
                    } else {
                        malert("更新失败", 'top', 'defeadted')
                    }
                });
        },
        getJyPrint: function (jyxh) {
            if (jyxh.length == 0) return;
            // TODO 勾选检验获取检验序号
            var parm = {jyxhs: jyxh};
            this.updatedAjax("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_dysjtb&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    var d = data.d;
                    if (d.length > 0) {
                        //jydy.jbxx = d[0].jydj;
                        //删除检验明细为空的数据
                        for (var i = d.length - 1; i >= 0; i--) {
                            if (d[i].jymx.length == 0) {
                                d.splice(i, 1);
                            }
                        }
                        // 组装数据
                        for (var i = 0; i < d.length; i++) {
                            jydy.jbxx[i] = d[i].jydj;
                            var jyxmArr = d[i].jymx;
                            var a = Math.max.apply(Math, jyxmArr.map(function(o) {return o.sjlx}))
                            if(a == 999){ //微生物报告
                                for(var x=jyxmArr.length-1;x>=0;x--){
                                    if(jyxmArr[x].sjlx != '999'){
                                        jyxmArr.splice(x,1);
                                    }
                                }
                            }
                            var leftnum = Math.ceil(jyxmArr.length / 2)
                            var rightnum = jyxmArr.length - leftnum;
                            var arr = [];
                            for (var j = 0; j < leftnum; j++) {
                                var obj = {};
                                obj.zwmc = jyxmArr[j].zwmc;
                                obj.valueN = jyxmArr[j].valueN;
                                obj.valueT = jyxmArr[j].valueT;
                                obj.dw = jyxmArr[j].dw;
                                obj.ckzT = jyxmArr[j].ckzT;
                                obj.zxz = jyxmArr[j].zxz;
                                obj.zdz = jyxmArr[j].zdz;
                                obj.sjlx = jyxmArr[j].sjlx;
                                obj.xzjgmc = jyxmArr[j].xzjgmc;
                                if (j + leftnum < jyxmArr.length) {
                                    obj.zwmcL = jyxmArr[j + leftnum].zwmc;
                                    obj.sjlxL = jyxmArr[j + leftnum].sjlx;
                                    obj.xzjgmcL = jyxmArr[j + leftnum].xzjgmc;
                                    obj.valueNL = jyxmArr[j + leftnum].valueN;
                                    obj.valueTL = jyxmArr[j + leftnum].valueT;
                                    obj.zxzL = jyxmArr[j + leftnum].zxz;
                                    obj.zdzL = jyxmArr[j + leftnum].zdz;
                                    obj.zdzL = jyxmArr[j + leftnum].zdz;
                                    obj.sjlxL = jyxmArr[j + leftnum].sjlx;
                                    obj.ckzTL = jyxmArr[j + leftnum].ckzT;
                                    obj.dwL = jyxmArr[j + leftnum].dw;
                                }
                                arr.push(obj);
                            }
                            d[i].jymx = arr;
                        }
                        jydy.xqxx = d;
                    }

                } else {
                    malert("检验明细查询失败！" + data.c, "top", "defeadted");
                }
            });
        },
        print: function () {
            setTimeout(function () {
                window.print();
                jydy.printUpdated()
            }, 100)
        },
    }
});
