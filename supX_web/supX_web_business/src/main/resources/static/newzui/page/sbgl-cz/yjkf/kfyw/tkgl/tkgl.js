var qxksbm = '';
var rksh = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
        //打印数据
        printData: {},
        isShowkd: true,
        isShow: false,
        rkdList: [], //入库单集合
        jsonList: [],
        json: {},
        csParm: {},
        tkdList: [],
        tkdDetail: [],
        rkd: {}, //入库单对象
        TjShow: true,
        dyShow: false,
        ShShow: false,
        zfShow: true,
        dg: {
            page: 1,
            rows: 20,
            sort: "",
            order: "asc",
            parm: ""
        },
        isCheck: null,
        thdDetail: [], //退货单明细集合
        dateBegin: null,//getTodayDateBegin(),
        dateEnd: null,//getTodayDateEnd(),
        time: {
            test: 'hello!'
        },
        t: {},
        zhuangtai: {
            "0": "未审核",
            "1": "已审核",
            "2": "已作废",
            "3": "未通过",
        },
        tkd: null,
        param: {

            rows: 10,
            page: 1,
            beginrq: null,
            endrq: null
        },
        isUpdate: 0,
        totlePage: 0,
        modifyIndex: null,
        mxShShow: true,
    },
    computed: {
        money: function () {
            var reducers = {
                totalInEuros: function (state, item) {
                    return state.jjzj += item.ypjj * parseFloat(item.rksl);
                },
                totalInYen: function (state, item) {
                    return state.ljzj += item.yplj * parseFloat(item.rksl);
                }
            };
            var manageReducers = function (reducers) {
                return function (state, item) {
                    return Object.keys(reducers).reduce(function (nextState, key) {
                        reducers[key](state, item);
                        return state;
                    }, {})
                }
            }
            var bigTotalPriceReducer = manageReducers(reducers);
            this.jsonList.reduce(bigTotalPriceReducer, this.json = {
                jjzj: 0,
                ljzj: 0,
            });
        }
    },
    mounted: function () {
    },
    updated: function () {
        changeWin()
    },
    methods: {
        loadNum: function () {
            this.num = wrapper.num;
        },
        //判断是否有操作权限
        hasCx: function (cx) {
            if (!cx) {
                malert("用户没有操作权限！", 'top', 'defeadted');
                return true;
            }
        },
        //进入页面加载单据列表信息
        getData: function () {
            common.openloading('.zui-table-view');
            //清空退货明细信息
            rksh.thdDetail = [];
            rksh.tkd = null;
            rksh.isUpdate = 0;
            //是否选择库房
            if (wrapper.popContent.kfbm == undefined || wrapper.popContent.kfbm == null | wrapper.popContent.kfbm == "") {
                malert("请先择库房!", 'top', 'defeadted');
                return;
            }
            //拼接参数对象
            this.dateBegin = rksh.param.beginrq;
            this.dateEnd = rksh.param.endrq;

            Vue.set(rksh.param, 'kfbm', wrapper.popContent.kfbm);
            Vue.set(rksh.param, 'rkfs', '02');
            Vue.set(rksh.param, 'parm', wrapper.search);
            if (wrapper.popContent.zt != '9') {
                Vue.set(rksh.param, 'shzfbz', wrapper.popContent.zt);
            } else {
                Vue.set(rksh.param, 'shzfbz', null);
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywtkd&types=queryBySH' +
                '&parm=' + JSON.stringify(rksh.param),
                function (data) {
                    if (data.a == 0) {
                        rksh.tkdList = data.d.list;
                        for (var i = 0; i < rksh.tkdList.length; i++) {
                            rksh.tkdList[i].tkksmc = rksh.listGetName(wap.KSList, data.d.list[i].tkksbm, 'ksbm', 'ksmc');
                            rksh.tkdList[i].tkyfmc = rksh.listGetName(wap.YFList, data.d.list[i].tkyfbm, 'yfbm', 'yfmc');
                        }
                        rksh.totlePage = Math.ceil(data.d.total / rksh.param.rows);
                        common.closeLoading()
                    } else {
                        malert(data.c, 'top', 'defeadted');
                        common.closeLoading()
                    }
                });
        },
        //入库审核
        passData: function () {
            if (!wrapper.ifClick) return;
            wrapper.ifClick = false;
            var json = this.tkdList[this.isCheck];
            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywtkd&types=passTkd', JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.ifClick = true;
                        var mes = confirm('是否打印退库单');
                        if (mes == true) {
                            //帆软打印
                            rksh.updateDycsAndPrint(json.kfbm, json.rkdh);
                        } else {

                        }
                        wrapper.isShow = false;
                        wrapper.isShowkd = true;
                        rksh.isShow = false;
                        rksh.isShowkd = true;
                        this.TjShow = false;
                        wrapper.TjShow = false;
                        wrapper.yshShow = true;//刷新按钮状态显示2018/07/04
                        rksh.zfShow = false;
                        rksh.getData();
                        malert("审核成功！", 'top', 'success')
                    } else {
                        wrapper.ifClick = true;
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        print: function () {
            //帆软打印
            var kfbm = wrapper.popContent.kfbm
            var rkdh = wrapper.popContent.rkdh
            this.updateDycsAndPrint(kfbm, rkdh);
        },

        updateDycsAndPrint: function (kfbm, rkdh) {
            var parm = {
                'rkdh': rkdh
            };
            //更新打印次数
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywRkd&types=updateDycs' + '&parm=' + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        //调用帆软打印
                        console.log(kfbm + "||" + rkdh);
                        var frpath = "";
                        if (window.top.J_tabLeft.obj.frprintver == "3") {
                            frpath = "%2F";
                        } else {
                            frpath = "/";
                        }
                        var reportlets = "[{reportlet: 'fpdy" + frpath + "yjkf" + frpath + "ykgl_tkd.cpt',yljgbm:'" + jgbm + "',kfbm:'" + kfbm + "',djh:'" + rkdh + "'}]";
                        if (!FrPrint(reportlets, null)) {
                            return null;
                        }
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        //作废退库单 2018/07/04二次作废弹窗提示
        invalidData: function (num) {
            if (num != null && num != undefined) {
                this.isCheck = num;
            }
            //判断权限
            if (rksh.hasCx(wap.csParm.zf)) {
                malert("权限不足！", 'top', 'defeadted');
                return;
            }
            //库房非空
            if (!wrapper.popContent.kfbm) {
                malert("请选择库房！", 'top', 'defeadted');
                return;
            }
            if (common.openConfirm("确认作废该条信息吗？", function () {
                var json = {
                    "rkdh": rksh.tkdList[rksh.isCheck]['rkdh'],
                    "kfbm": wrapper.popContent.kfbm
                };
                rksh.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywtkd&types=invalidTkd', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == "0") {
                        rksh.getData();
                        malert("作废成功！", 'top', 'success');
                        rksh.cancel();
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
            })) {
                return false;
            }
        },
        //显示退库单细节
        showDetail: function (index) {
            common.openloading('.zui-table-view');
            this.isCheck = index;
            wrapper.isShow = true;
            wrapper.isShowkd = false;
            wrapper.isShowpopL = false;
            rksh.isShow = true;
            rksh.isShowkd = false;
            $('#bzms').attr('disabled', true);
            this.TjShow = false;
            wrapper.TjShow = false;
            this.ShShow = true;
            rksh.zfShow = false;
            if (this.tkdList[index].shzfbz == '1' || this.tkdList[index].shzfbz == '2') {
                if (this.tkdList[index].shzfbz == '1') {
                    rksh.dyShow = true;
                } else {
                    rksh.dyShow = false;
                }
                this.mxShShow = false;
                wrapper.jyinput = true;
                this.ShShow = false;
                wrapper.yshShow = false;//刷新按钮状态显示2018/07/04
            } else {
                rksh.dyShow = false;
                this.mxShShow = false;
                wrapper.jyinput = false;
                this.ShShow = true;
                wrapper.yshShow = true;//刷新按钮状态显示2018/07/04
            }
            wrapper.zdyxm = this.tkdList[index].zdyxm;
            wrapper.zdrq = rksh.fDate(this.tkdList[index].zdrq, 'date');
            wrapper.popContent.kfbm = this.tkdList[index].kfbm;
            wrapper.popContent.bzms = this.tkdList[index].bzms;
            wrapper.popContent.rkdh = this.tkdList[index]['rkdh'];
            // 测试打印
            // this.printDJ();
            this.getMx(this.tkdList[index]['rkdh']);
            rksh.jsonList = [];
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywtkd&types=queryTkdMxByTkd' + '&dg=' + JSON.stringify(rksh.dg), function (data) {
                if (data.a == 0) {
                    rksh.jsonList = data.d.list;
                    common.closeLoading()
                } else {
                    malert(data.c, 'top', 'defeadted');
                    common.closeLoading()
                }
            });

        },
        getMx: function (ckdh) {
            rksh.jsonList = [];
            this.dg['parm'] = ckdh;
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywtkd&types=queryTkdMxByTkd' + '&dg=' + JSON.stringify(rksh.dg), function (data) {
                if (data.a == 0) {
                    rksh.jsonList = data.d.list;
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },
        //编辑
        editIndex: function (index, vals) {
            wrapper.isUpdate = 1;
            this.isCheck = index;
            wrapper.isShowkd = false;
            wrapper.isShow = true;
            wrapper.isShowpopL = true;
            rksh.isShow = true;
            rksh.isShowkd = false;
            rksh.TjShow = true;
            rksh.ShShow = false;
            wrapper.yshShow = true;//刷新按钮状态显示2018/07/04
            wrapper.cgry = vals;
            wrapper.TjShow = false;
            rksh.zfShow = true;
            this.mxShShow = true;
            wrapper.jyinput = false;
            wrapper.zdyxm = this.tkdList[index].zdyxm;
            wrapper.zdrq = rksh.fDate(this.tkdList[index].zdrq, 'date');
            wrapper.popContent.kfbm = this.tkdList[index].kfbm;
            wrapper.popContent.bzms = this.tkdList[index].bzms;
            this.tkd = this.tkdList[index];
            wap.tkdContent = rksh.tkd;
            rksh.getMx(this.tkd.rkdh);
        },


        //提交所有材料
        submitAll: function () {
            //是否禁止提交
            if (this.isSubmited) {
                malert("数据提交中，请稍候！", 'top', 'defeadted');
                return false;
            }
            //判断提交数据正确性
            if (this.jsonList.length <= 0) {
                malert("没有可提交的数据", 'top', 'defeadted');
                return false;
            }
            //是否禁止提交
            this.isSubmited = true;
            wap.tkdContent.cgry = userId; //设置退库人
            wap.tkdContent.kfbm = wrapper.popContent.kfbm;
            wap.tkdContent.bzms = wrapper.popContent.bzms;
            //准备数据，包括退货单对象和退货单明细对象
            var json = {
                "list": {
                    "rkd": wap.tkdContent,
                    "rkdmx": this.jsonList
                }
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywtkd&types=modify',
                JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == 0) {
                        malert("数据更新成功", 'top', 'success');
                        this.jsonList = [];
                        this.isShow = false;
                        this.isShowkd = true;
                        wrapper.isShowkd = true;
                        wrapper.isShow = false;
                        wrapper.isShowpopL = false;
                        wrapper.yshShow = true;//刷新按钮状态显示2018/07/04
                        rksh.getData();
                    } else {
                        malert("数据提交失败" + data.body.c, 'top', 'defeadted');
                    }
                    //是否禁止提交
                    rksh.isSubmited = false;
                }, function (error) {
                    //是否禁止提交
                    rksh.isSubmited = false;
                });
        },
        //删除2018/07/04二次删除弹窗提示
        scmx: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                rksh.jsonList.splice(index, 1);
            })) {
                return false;
            }
            // this.jsonList.splice(index, 1);
        },
        //双击修改
        edit: function (num) {
            rksh.isUpdate = 1;
            rksh.modifyIndex = num;
            //时间格式化
            Vue.set(wap.popContent, 'scrq', wap.popContent.scrq);
            Vue.set(wap.popContent, 'yxqz', wap.popContent.yxqz);
            if (wrapper.isUpdate == 1) {
                wap.tkdContent = rksh.tkd;//出库单
            }
            wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            Vue.set(wap.popContent, 'text', rksh.jsonList[num].ypmc);//材料名称
            if (rksh.tkd) {
                Vue.set(wap.tkdContent, 'lyks', rksh.tkd.tkksbm);//退库科室
                //来源科室
                rksh.getKslyXx(rksh.tkd.tkksbm);
            }
            //库存
            rksh.getKc(this.jsonList[num].ypbm);
            wap.title = "编辑材料";
            wap.open();
        },
        //来源科室
        getKslyXx: function (lyks) {
            //请求后台获取到出库二级库房
            this.dg.parm = lyks;
            if (this.dg.parm == null || this.dg.parm == '') {
                return false;
            }
            this.dg.rows = 20000;
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=findYFByKS' +
                '&dg=' + JSON.stringify(this.dg),
                function (data) {
                    if (data.a == 0) {
                        if (data.d == null) {
                            Vue.set(wap.tkdContent, "lyyfmc", "科室出库");
                        } else {
                            Vue.set(wap.tkdContent, "lyks", data.d.KSBM);
                            Vue.set(wap.tkdContent, "lyyf", data.d.YFBM);
                            Vue.set(wap.tkdContent, "tkyfbm", data.d.YFBM);
                            Vue.set(wap.tkdContent, "lyyfmc", data.d.YFMC);
                        }
                    } else {
                        malert("出库二级库房查询失败", 'top', 'defeadted')
                    }
                });
        },
        //库存
        getKc: function (ypbm) {
            var json = {
                ypbm: ypbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (data.a == 0) {
                        if (data.d != null && data.d.list != null) {
                            //实际库存
                            Vue.set(wap.popContent, 'ykkc', data.d.list[0].sjkc);
                        }
                    }

                }
            });
        },
        //取消
        cancel: function () {
            wrapper.isShow = false;
            wrapper.isShowkd = true;
            wrapper.isShowpopL = false;
            wrapper.yshShow = true;//刷新按钮状态显示2018/07/04
            rksh.isShow = false;
            rksh.isShowkd = true;
            rksh.getData();
        }


    }
});

var wrapper = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],

    data: {
        ifClick:true,
        isShowpopL: false,
        isTabelShow: false,
        isShowkd: true,
        isShow: false,
        keyWord: '',
        csParm: {},
        zdrq: getTodayDateTime(), //获取制单日期
        zdyxm: '',
        jyinput: false, //禁用输入框
        TjShow: true,
        tkdContent: {
            rkfs: "02",  //01-入库，02-退库，03-盘点入库
        },
        zhuangtai: {
            "0": "未审核",
            "1": "已审核",
            "2": "已作废",
            "3": "未通过",
        },
        cgry: '',
        jsonList: [],
        rkdList: [], //入库单集合
        KFList: [], //库房
        title: '',
        totle: '',
        rkd: {}, //入库单对象
        num: 0,
        param: {
            shzfbz:'0',
            page: '',
            rows: '',
            total: ''
        },
        yshShow: true,//刷新按钮状态显示2018/07/04
        isUpdate: 0,
        popContent: {zt: '9'},
        search: '',
    },
    watch: {
        'popContent.zt': function (newval, oldval) {
            if (newval != oldval) {
                rksh.getData();
            }
        },
        'search': function () {
            rksh.getData();
        }
    },
    mounted: function () {
        var myDate = new Date();
        rksh.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        rksh.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: rksh.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                rksh.param.beginrq = value;
                rksh.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: rksh.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                rksh.param.endrq = value;
                rksh.getData();
            }
        });
    },
    methods: {
        kd: function (index) {
            this.num = index;
            setTimeout(function () {
                wap.$refs.autofocus.$refs.inputFu.focus();
                wap.$refs.autofocus.setLiShow(wap.$refs.autofocus.$refs.inputFu)
            }, 1000);
            rksh.loadNum();
            switch (wrapper.num) {
                case 0:
                    wap.tkdContent = {};
                    this.isShowkd = false;
                    this.isShow = true;
                    this.isShowpopL = true;
                    rksh.isShow = true;
                    rksh.isShowkd = false;
                    rksh.TjShow = true;
                    rksh.ShShow = false;
                    $('#bzms').attr('disabled', false);
                    wrapper.isUpdate = 0;
                    rksh.jsonList = [];
                    rksh.zfShow = false;
                    rksh.mxShShow = true;
                    var reg = /^[\'\"]+|[\'\"]+$/g;
                    wrapper.zdyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');
                    break;
                case 1:
                    wap.open();
                    wap.title = '添加材料';
                    wap.popContent = {};
                    wrapper.isUpdate = 0;
                    break;
            }

        },
        sx: function () {
            rksh.getData();
        },
        getKFData: function () {
            //下拉框获取库房
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011004"}',
                function (data) {
                    if (data.a == 0) {
                        wrapper.KFList = data.d;
                        if (data.d.length > 0) {
                            qxksbm = data.d[0].ksbm;
                            Vue.set(wrapper.popContent, 'kfbm', data.d[0].kfbm);
                            wap.getCsqx(); //加载完库房再次加载参数权限
                        }
                    } else {
                        malert("一级库房获取失败", 'top', 'defeadted');
                    }
                });
            //下拉框获取科室编码
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                function (data) {
                    if (data.a == 0) {
                        wap.KSList = data.d.list;
                    } else {
                        malert("科室获取失败!", 'top', 'defeadted');
                    }
                });
            //下拉框获取二级库房编码
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                function (data) {
                    if (data.a == 0) {
                        wap.YFList = data.d.list;
                    } else {
                        malert("二级库房获取失败!", 'top', 'defeadted');
                    }
                });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "kfbm":
                    Vue.set(this.popContent, 'kfbm', val[0]);
                    Vue.set(this.popContent, 'kfmc', val[4]);

                    for (var i = 0; i < wrapper.KFList.length; i++) {
                        if (wrapper.KFList[i].kfbm == val[0]) {
                            qxksbm = wrapper.KFList[i].ksbm;
                            break;
                        }
                    }
                    wap.getCsqx();
                    break;
                default:
                    break;
            }
        },


    }
});


var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        isShowpopL: false,
        iShow: false,
        isTabelShow: false,
        flag: false,
        jsShow: false,
        title: '',
        added: false,
        lyks: null,
        tkyf: null,
        tkyfBM: null,
        zdrq: getTodayDateTime(), //获取制单日期
        tkdContent: {
            rkfs: "02", //01-入库，02-退库，03-盘点入库
        }, //退货单对象
        cgryList: [], //退库人员
        KSList: [],
        YFList: [],
        KFList: [],
        lyr: null,
        ryList: [],
        ghdwList: [],
        //材料信息对象
        popContent: {},
        popContents: {},
        //参数权限对象
        csParm: {},
        dg: {
            page: 1,
            rows: 30,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '生产批号': 'scph',
            '材料编号': 'ypbm',
            '材料名称': 'ypmc',
            '库存数量': 'ykkc',
            '有效期至': 'yxqz',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'ykjj',
            '零价': 'yklj',
            '库房单位': 'kfdwmc',
            '二级库房单位': 'yfdwmc',
            '效期': 'yxqz',
            '材料剂型': 'jxmc'
        }
    },
    mounted: function () {
        // this.getJzData();
    },
    methods: {
        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');

        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
        },
        //获取参数权限
        getCsqx: function () {
            var parm = {
                "ksbm": qxksbm,
                "ylbm": "N040100011004"
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        //退库开单打印单据，1-开单保存后打印单据，0-开单保存后不打印单据
                        for (var i = 0; i < json.d.length; i++) {
                            if (json.d[i].csqxbm == 'N04010001100401') {
                                if (json.d[i].csz == '0') {
                                    wap.csParm.kddy = true;
                                } else {
                                    wap.csParm.kddy = false;
                                }
                            }
                            //退库开单审核，0-有开单审核  1-有开单 2-有审核
                            if (json.d[i].csqxbm == 'N04010001100402') {
                                if (json.d[i].csz == '0') {
                                    wap.csParm.kd = true;
                                    wap.csParm.sh = true;
                                } else if (json.d[i].csz == '1') {
                                    wap.csParm.kd = true;
                                    wap.csParm.sh = false;
                                } else {
                                    wap.csParm.sh = true;
                                    wap.csParm.kd = false;
                                }
                            }
                            //退库单作废权限，1-有 0-无
                            if (json.d[i].csqxbm == 'N04010001100403') {
                                if (json.d[i].csz == '0') {
                                    wap.csParm.zf = false;
                                } else {
                                    wap.csParm.zf = true;
                                }
                            }
                        }
                    }
                    rksh.getData();
                } else {
                    malert(json.c, "参数权限获取失败", 'top', 'defeadted');
                }
            });
        },
        //
        //领用科室加载下拉框回调
        resultKsChange: function (val) {
            //回车跳转
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }

            //初始化科室下拉框
            var types = val[2][val[2].length - 1]; //方便获取到是什么类型

            //清空数据录入区的二级库房/科室信息
            this.tkdContent.lyks = '';
            this.tkdContent.lyyf = '';
            this.tkdContent.tkyfbm = '';
            //设置科室信息
            if (types == 'lyks') {
                Vue.set(this.tkdContent, 'lyks', val[0]);
                this.tkdContent.tkksbm = val[0];
                //请求后台获取到出库二级库房
                this.dg.parm = this.tkdContent.lyks;
                if (this.dg.parm == null || this.dg.parm == '') {
                    return false;
                }
                this.dg.rows = 20000;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=findYFByKS' +
                    '&dg=' + JSON.stringify(this.dg),
                    function (data) {
                        if (data.a == 0) {
                            if (data.d == null) {
                                Vue.set(wap.tkdContent, "lyyfmc", "科室出库");
                            } else {
                                Vue.set(wap.tkdContent, "lyks", data.d.KSBM);
                                Vue.set(wap.tkdContent, "lyyf", data.d.YFBM);
                                Vue.set(wap.tkdContent, "tkyfbm", data.d.YFBM);
                                Vue.set(wap.tkdContent, "lyyfmc", data.d.YFMC);
                            }
                        } else {
                            malert("出库二级库房查询失败", 'top', 'defeadted')
                        }
                    });

            }
            //回车跳转
            if (val[1] != null) {
                this.keyCodeFunction(val[1], val[2]);
            }
        },

        //材料名称下拉table检索数据
        changeDown: function (event, type) {
            //库存判断
            if (type == 'kcsl') {
                if (this.popContent.rksl > this.popContent.kc) {
                    malert('库存不足！', 'top', 'defeadted');
                    return;
                } else {
                    this.addData();
                    this.nextFocus(event);
                    return;
                }
            }
            this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            //选中之后的回调操作
            this.nextFocus(event);
        },
        //当输入值后才触发
        change: function (add, val) {
            if (!this.tkdContent.lyyfmc) {
                malert("请先选择科室!", 'top', 'defeadted');
                return;
            }
            if (!add) this.dg.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.dg.parm = val;
            var bean = {
                "yfbm": this.tkdContent.lyyf,
                "ksbm": this.tkdContent.lyks//,
                //"ksbm":"0002",
                //"ckbz": "bs" //出库不过虑过期材料
            };
            //分页参数
            //按二级库房/科室查询库存,二者编码同时存在时按二级库房查询
            if (this.tkdContent.lyyf != null && this.tkdContent.lyks != null) {
                var temp = 'yfpckc';
            }
            //二级库房为空，按科室查询
            if (this.tkdContent.lyyf == undefined || this.tkdContent.lyyf == null || this.tkdContent.lyyf == '') {
                var temp = 'kspckc';
            }
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=' + temp +
                '&dg=' + JSON.stringify(this.dg) + '&json=' + JSON.stringify(bean),
                function (data) {
                    if (add) {
                        for (var j = 0; j < data.d.list.length; j++) {
                            wap.searchCon.push(data.d.list[j]);
                        }
                    } else {
                        wap.searchCon = data.d.list;
                    }
                    for (var i = 0; i < data.d.list.length; i++) {
                        data.d.list[i]['yxqz'] = data.d.list[i]['yxqz'];
                        data.d.list[i]['scrq'] = data.d.list[i]['scrq'];
                        //判断可用库存数量
                        if (wap.tkdContent.lyyf != '') {
                            data.d.list[i]['kc'] = data.d.list[i]['ykkc'];
                            data.d.list[i]['ykkc'] = data.d.list[i]['ykkc'] + '（未审核：' +
                                (data.d.list[i]['wshcks'] == undefined ? 0 : data.d.list[i]['wck']) + '）';
                        }
                        data.d.list[i]['kykc'] = data.d.list[i]['ykkc'] - (data.d.list[i]['wck'] == undefined ? 0 : data.d.list[i]['wshcks']);

                    }
                    wap.total = data.d.total;
                    wap.selSearch = 0;
                    if (data.d.list.length != 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                        return false;
                    }
                });
        },
        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                this.dg.page++;
                this.change(true, 'ypmc')
            }
            this.popContent = item;
            $(".selectGroup").hide();
        },

        //添加入库信息
        addData: function () {
            //非空判断
            //判断权限
            if (rksh.hasCx(this.csParm.kd)) {
                return;
            }
            var haveError = false;

            if (!this.popContent.ypbm) haveError = true;
            if (!this.popContent.rksl) haveError = true;

            this.popContent['yxqz'] = $('#yxqz').val();
            this.popContent['scrq'] = $('#scrq').val();

            if (haveError) {
                malert("录入区数据不完整", 'top', 'defeadted');
                return false;
            }
            if (this.popContent.rksl == undefined || this.popContent.rksl == null || this.popContent.rksl <= 0) {
                malert("请填写数量！", 'top', 'defeadted');
                return false;
            }
            //判断可用库存数量是否大于0（同时判断未审核库存数）
            this.popContent.wck = this.popContent.wck == null ? 0 : this.popContent.wck;
            if (this.popContent.rksl > (this.popContent.kc - this.popContent.wck)) {
                malert("可用库存不足", 'top', 'defeadted');
                return;
            }

            if (rksh.isUpdate == 0) {
                wap.popContent.ypjj = wap.popContent.ykjj;
                wap.popContent.yplj = wap.popContent.yklj;
                //添加
                if (rksh.jsonList.length > 0) {
                    for (var i = 0; i < rksh.jsonList.length; i++) {
                        if (rksh.jsonList[i].ypbm == this.popContent.ypbm) {
                            malert("输入重复，请双击修改！", 'top', 'defeadted');
                            return;
                        }
                    }
                    //将输入区的材料信息加入提交区
                    rksh.jsonList.push(this.popContent);
                    this.popContent = {};
                    $("#ypmc").focus();
                } else {
                    //将输入区的材料信息加入提交区
                    rksh.jsonList.push(this.popContent);
                    this.popContent = {};
                    $("#ypmc").focus();
                }
            } else {
                //修改
                rksh.$set(rksh.jsonList, rksh.modifyIndex, this.popContent);
                this.popContent = {};
            }
        },

    }


});

//弹出框保存路径全局变量
wrapper.getKFData();
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});






