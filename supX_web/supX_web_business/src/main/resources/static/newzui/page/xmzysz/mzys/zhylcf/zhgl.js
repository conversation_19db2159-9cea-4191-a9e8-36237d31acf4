var panel = new Vue({
    el: '.panel',
    data: {},
    methods: {
        addData: function () {
            brzcList.title = '新增组合医疗医嘱';
            brzcList.index = 0
            brzcList.popContent = {}
        },
        getData: function () {
            hzList.getData()
        },
        remove: function () {
            hzList.remove()
        }
    },
})
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 1,
        index: 1,
        popContent: {},
        ypContent: {},
        title: '',
        yqList: [],
        ksbmList: [],//科室集合
        rybmList: [],//人员集合
        cflxList: [],//处方类型集合
        yfList: [],  //药房集合
        jsonList: [],//组合药品医嘱列表
        pcList: [],//频次下拉框
        yyffList: [], //用药方法下拉框

        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
    },
    created: function () {
        this.yyksSelect()
        this.yyrSelect()
        this.cflxSelect()
        this.yfSelect()
    },
    methods: {
        saveHc: function (event) {
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                this.saveData();
            }
        },
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'ypContent', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == "text") {
                    Vue.set(this.ypContent, 'text', this.ypContent['ypmc']);
                    this.popContent.ypbm = this.ypContent.ypbm;
                    this.popContent.jldw = this.ypContent.jldw;
                    this.popContent.jldwmc = this.ypContent.jldwmc;
                    this.popContent.yyff = this.ypContent.yyff;
                    this.nextFocus(event);
                }
            }
        },
        closes: function () {
            this.index = 1
        },
        saveData: function () {
            if (this.popContent.cflxbm == null || this.popContent.cflxbm == "") {
                malert("处方类型不能为空！", "top", "defeadted");
                return;
            }
            if (this.popContent.yfbm == null || this.popContent.yfbm == "") {
                malert("药房不能为空！", "top", "defeadted");
                return;
            }
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=save', JSON.stringify(this.popContent)).then(function (data) {
                if (data.body.a == 0) {
                    malert("数据更新成功", 'top', 'success');
                    brzcList.popContent = {};
                    hzList.getData();
                    brzcList.index = 1
                } else {
                    malert("数据失败", 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        //下拉框科室加载
        yyksSelect: function () {
            this.param.rows = 20000;
            this.param.sort = '';
            this.param.order = '';
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify(this.param), function (json) {
                brzcList.ksbmList = json.d.list;
            });
        },
        //下拉框人员加载
        yyrSelect: function () {
            this.param.rows = 20000;
            this.param.sort = '';
            this.param.order = '';
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg=" + JSON.stringify(this.param), function (json) {
                brzcList.rybmList = json.d.list;
            });
        },
        //下拉框处方类型加载
        cflxSelect: function () {
            this.param.rows = 20000;
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=cflx&dg=" + JSON.stringify(this.param), function (json) {
                brzcList.cflxList = json.d.list;
            });
        },

        //下拉框药房加载
        yfSelect: function () {
            this.param.rows = 20000;
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf&dg=" + JSON.stringify(this.param), function (json) {
                brzcList.yfList = json.d.list;
            });
        },
    },
})
var hzList = new Vue({
    el: '.hzList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 0,
        jsonList: [],
    },
    created: function () {
        this.getData()
    },
    methods: {
        tabBg: function (index) {
            this.getData()
            this.num = index
        },
        //初始化页面加载列表
        getData: function () {
            this.param.rows = 20;
            this.param.sort = 'zhyzbm';
            this.param.order = 'desc';
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=query&parm=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) {
                    hzList.totlePage = Math.ceil(json.d.total / hzList.param.rows);
                    hzList.jsonList = json.d.list;
                }
            });
        },

        edit: function (num) {
            if (num == null) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if (num == null) {
                    malert('请选中你要修改的数据', 'top', 'defeadted');
                    return false;
                }
            }
            brzcList.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            brzcList.num = this.num;
            brzcList.index = 0;
            brzcList.title = '编辑组合药品医嘱';
        },
        remove: function () {
            var zhyzList = [];
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    var zhyz = {};
                    zhyz.zhyzbm = this.jsonList[i].zhyzbm;
                    zhyzList.push(zhyz);
                }
            }
            if (zhyzList.length == 0) {
                // malert('保存成功','top','success');
                malert('请选中您要删除的数据', 'top', 'defeadted');
                return false;
            }
            if (!confirm("请确认是否删除")) {
                return false;
            }
            var json = '{"list":' + JSON.stringify(zhyzList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=delete&', json).then(function (data) {
                hzList.getData();
                if (data.body.a == 0) {
                    malert('删除成功', 'top', 'success');
                } else {
                    malert('删除失败', 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
    }
})
