<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>退库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <link type="text/css" href="bsgl.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel" v-cloak>
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-if="isShowkd">开单</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="isShowpopL">添加体外诊断试剂</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="searchHc" v-show="isShowkd || isShowpopL">刷新</button>
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" v-show="!isShowkd && !isShowpopL" @click="searchHc">刷新</button>
        </div>
        <div class="tong-search" :class="{'tong-padded':isShow}">
            <div class="zui-form" v-show="isShowkd">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="KFList" :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm"
                                      :name="'popContent.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline margin-f-l5 flex-container flex-align-c">
                        <input class="zui-input todate wh182 " autocomplete="off" v-model="param.beginrq" placeholder="请选择申请开始日期" id="timeVal"/>
                        <span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh182 " autocomplete="off" v-model="param.endrq" placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" autocomplete="off" placeholder="请输入关键字" type="text" @keydown.enter="searchHc()" v-model="search"/>
                    </div>
                </div>
            </div>
            <div class="jbxx fyxm-hide" :class="{'btn-show':isShow}">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                        <div class="zui-form padd-l24 padd-t-20 grid-box">
                                <div class="zui-inline">
                                    <label class="zui-form-label ">库房</label>
                                    <div class="zui-input-inline wh122 margin-f-l25">
                                        <select-input @change-data="resultRydjChange"
                                                      :child="KFList" :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm"
                                                      :name="'popContent.kfbm'" :search="true" :index_mc="'kfmc'" :disable="jyinput">
                                        </select-input>
                                    </div>
                                </div>
                                <div class="zui-inline" style="width:50%;">
                                    <label class="zui-form-label">备注</label>
                                    <div class="zui-input-inline margin-f-l20">
                                        <input autocomplete="off" class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="popContent.bzms" :disabled="jyinput"/>
                                    </div>
                                </div>
                        </div>


                    <div class="rkgl-kd">
                        <span>开单日期:<i v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view ybglTable padd-l-10 padd-r-10" v-cloak >
        <!--入库列表-->
        <div class="zui-table-header" v-show="isShowkd">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>报损单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库方式</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>SPD单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " v-show="isShowkd" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in bsdList" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index"
                	@dblclick="showDetail($index,item)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.ckdh">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="ckfs_tran[item.ckfs]">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zdyxm">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="fDate(item.zdrq,'AllDate')">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.spdno">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                        <i v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : item.shzfbz=='3' ? 'color-wtg':'' "></i></div>
                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                            <span class="flex-center padd-t-5">
                                <em class="width30"  v-if="item.shzfbz == '0'"><i class="icon-sh" data-title="审核" @click="showDetail($index,item)"></i></em>
                                <em  class="width30"  v-if="item.shzfbz == '0' " ><i class="icon-js" data-title="作废" @click="invalidData($index)"></i></em>
                                <em  class="width30"  v-if="item.shzfbz != '1' && item.shzfbz !='2'"><i class="icon-bj" data-title="编辑" @click="editIndex($index,item.zdyxm)"></i></em>
                               </span>
                        </div>
                    </td>
                    <p v-show="bsdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>

                </tr>
                </tbody>
            </table>
        </div>
        <!--添加体外诊断试剂-->
        <div class="zui-table-header fyxm-hide" v-show="isShow" :class="{'fyxm-show':isShow}">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>供货单位</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>体外诊断试剂名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>体外诊断试剂规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>报损数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>体外诊断试剂进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>体外诊断试剂零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>体外诊断试剂批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    <!--<th><div class="zui-table-cell cell-s"><span>操作</span></div></th>-->
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body   fyxm-hide" v-show="isShow" :class="{'fyxm-show':isShow}" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>

                <tr v-for="(item,$index) in jsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','jsonList'],$event)" :tabindex="$index">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ghdwmc">序号</div>
                        </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypgg">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <i v-text="item.cksl"></i>
                            <i v-text="item.kfdwmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.scph">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc">状态</div>
                    </td>

                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-show="jsonList.length==0" class=" flex noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l" v-show="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','jsonList'],$event)">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="zui-table-fixed table-fixed-r" v-show="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','jsonList'],$event)">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" v-show="isShowkd" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="rkgl-position" v-show="isShow">
           <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print()" v-if="dyShow">打印</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-show="zfShow">作废</button>
                <!-- <button class="tong-btn btn-parmary-f2a" @click="jujue" v-show="ShShow">拒绝</button> -->
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow">审核</button>
           </span>
        </div>
    </div>
</div>
</div>
    <!--侧边窗口-->
<div class="side-form ng-hide pop-548"  style="padding-top: 0;"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--编辑体外诊断试剂-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>制单日期</i>
                <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input autocomplete="off" class="zui-input text-indent20" disabled="disabled" v-model="zdrq" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>制单员</i>
                    <select-input @change-data="resultChange" ref="autofocus" :child="ryList" :index="'ryxm'" :index_val="'rybm'" :val="zdy"
                                  :search="true" :name="'zdy'" @keydown="nextFocus($event)">
                    </select-input>
            </li>
            <li>
                    <i>体外诊断试剂名称</i>
                    <input autocomplete="off" id="ypmc" class="zui-input" v-model="popContent.ypmc" @keyup="changeDown($event,'ypmc')" @input="change($event,'ypmc')">
                    <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>

                   <!-- <input id="ypmc" class="zui-input" v-model="popContent.ypmc" @keyup="changeDown($event,'ypmc')" @input="change($event,'ypmc')"
                           data-notEmpty="false">
                    <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                                  :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>-->
            </li>
            <li>
                    <i>供货单位</i>
                    <!--<select-input @change-data="resultChange"  :child="ghdwList"
                                  :index="'dwmc'" :index_val="'dwbm'" :val="popContents.ghdw" :search="true"
                                  :name="'popContents.ghdw'"  :search="true"  @keydown="nextFocus($event)" >
                    </select-input>
                    <input type="hidden" v-model="popContent.ghdwm" data-skip/>-->
                    <input class="zui-input" disabled="disabled" v-model="popContent.ghdwmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>报损数量</i>
                    <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent type="number" id="bssl" class="zui-input" v-model="popContent.cksl" @keyup="changeDown($event,'cksl')" @keydown="nextFocus($event)"/>
                    <em style="position:absolute;top:10px;right:10px;z-index:111;" v-text="popContent.kfdwmc"></em>
                </li>
            <li>
                    <i>体外诊断试剂规格</i>
                    <input class="zui-input" disabled="disabled" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>库存数量</i>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" disabled="disabled" v-model="popContent.kcsl"  @keydown="nextFocus($event)" />
                    <em style="position:absolute;top:10px;right:10px;z-index:111;" v-text="popContent.kfdwmc"></em>
                </li>
            <li>
                    <i>可用库存</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.kykc"  @keydown="nextFocus($event)" />
            </li>
            <li>
                    <i>体外诊断试剂进价</i>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" disabled="disabled" :value="fDec(popContent.ypjj,2)" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>体外诊断试剂零价</i>
                    <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" disabled="disabled" :value="fDec(popContent.yplj,2)" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>体外诊断试剂批号</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.scph" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>有效期至</i>
                <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="zui-input text-indent20" id="yxqz" disabled="disabled" :value="fDate(popContent.yxqz,'date')" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>产地</i>
                    <input type="text" class="zui-input" disabled="disabled"  v-model="popContent.cdmc" @keydown.enter="addData">
            </li>

            <!-- <li>
                    <i>二级库房单位</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.yfdwmc" @keydown="nextFocus($event)">
            </li> -->
            <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.fzbl" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.cpbzh" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>批准文号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.pzwh" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>生产日期</i>
                <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input type="text" class="zui-input text-indent20" id="scrq" disabled="disabled" :value="fDate(popContent.scrq,'date')" @keydown="nextFocus($event)">
            </li>
            <li>
                    <i>系统批号</i>
                    <input type="text" class="zui-input " disabled="disabled" v-model="popContent.xtph" @keydown="nextFocus($event)">
            </li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">添加</button>
    </div>
</div>

<script src="bsgl.js"></script>
</body>

</html>

