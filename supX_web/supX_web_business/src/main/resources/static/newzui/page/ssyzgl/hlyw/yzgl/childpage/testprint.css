
/*设置纸张大小和横向*/
@page {
    size: A4 portrait;
    page-break-after: always;
}
.printHide {
    display: none !important;
}

.printShow {
    display: block !important;
}
.t-values {
    border: 0;
}

.tem {
    border: 0;
}

.drag {
    display: none;
}

body{
    height: auto;
}
body,
html{
    height: auto;
    width: auto;
    min-height: auto;
    min-width: auto;
}

.pop{
    position: absolute;
    height: auto;
}

/*隐藏页脚和页眉*/
nav, aside {
    display: none;
}

button {
    display: none !important;
}
.pophide{
		position: relative;
	}
	tr{
		page-break-inside: avoid;
		page-break-after: always;
		page-break-before: always;
	}

.cw_blank{
    background-color: #f84b4b;
}
.pagePrintBefore{page-break-before:always;}
.pagePrintAfter{page-break-after:always;}

.hzxm{
		width: 2.5cm;
		max-width: 2.5cm;
	}
	.hzxb{
		width: 2cm;
		max-width: 2cm;
	}
	.hznl{
		width: 2cm;
		max-width: 2cm;
	}
	.hzbs{
		width: 5.7cm;
		max-width: 5.7cm;
	}
	.hzch{
		width: 2.5cm;
		max-width: 2.5cm;
	}
	.hzzyh{
		width: 5cm;
		max-width: 5cm;
	}
	
	.xdyz{
		width:3.3cm;
		max-width: 4cm;
	}
	.xdyzrq{
		width:1.1cm;
		max-width: 1.1cm;
	}
	.xdyzsj{
		width:1cm;
		max-width: 1cm;
	}
	.xdyzqm{
		width:1.3cm;
		max-width: 1.3cm;
	}
	.yznr{
		width: 5.7cm;
		max-width: 5.7cm;
	}
	.yzcdqm{
		width: 1.35cm;
		max-width: 1.35cm;
	}
	.tzsj{
		width: 1.35cm;
		max-width: 1.35cm;
	}
	.zxrqsjqm{
		width: 7.35cm;
		max-width: 7.35cm;
	}
	.dybt{
		font-size: 6.8mm;
		color: #000;
		font-weight: 900;
		font-family: '宋体';
	}
	.yzd-brInfo {
	    display: flex;
	    width: 19cm;
	    margin: 0;
		margin-left: 1.1cm;
		font-size: 3.7mm;
		color: red;
		font-weight: 900;
		font-family: '宋体';
	}
	
	.yz-tables{
		width: 19cm;
		margin-left: 1.4cm;
		margin-top: 0.3cm;
	}
	.yz-tables td {
		border: 0.1mm solid #000 ;
		font-weight: 500;
		height: 12.45mm;
		white-space: nowrap;
		text-align: center;
		font-size: 3.5mm;
		color: red;
		font-family: '宋体';
	}
	.yz-tables th {
		border: 0.1mm solid #000;
		font-weight: 500;
		height: 0.9cm;
		white-space: nowrap;
		text-align: center;
		font-size: 3.5mm;
		color: red;
		font-family: '宋体';
	}
	.yz-tables td .yzd-name {
	    font-size: 3.5mm;
	    text-align: left;
	    margin-left: 1mm;
	    word-break: break-all;
	    white-space: normal;
	    word-wrap: break-word;
	    overflow: hidden;
	    text-overflow: ellipsis;
	    -webkit-line-clamp: 2;
	    -webkit-box-orient: vertical;
	}
	.zsorsyss{
		max-height: 3.75cm;
		overflow: hidden;
	}
	.zsorsy {
	    height: 3.75cm;
	    max-height: 3.75cm;
	    overflow: hidden;
	}
	.yzd-name {
	    float: left;
	}
	
	
	.tz-start::before {
	    bottom: -1mm;
	    top: 2mm;
		right:-0.2mm;
	    border-top: 0.1mm solid #000000;
	}
	
	.tz-center::before {
	    bottom: -1mm;
	    top: -2mm;
		right:-0.2mm;
	}
	
	.tz-stop::before {
	    bottom: 2mm;
	    top: 0.5mm;
		right:-0.2mm;
	    border-bottom: 0.1mm solid #000000;
	}
	.tz-start, .tz-center, .tz-stop {
	    width: 5.4cm;
	    position: relative;
	}
	.yzyysm{
		padding-top: 0.2cm;
	}
	
	.kfyz-tables{
		width: 19cm;
		margin-left: 1.4cm;
		margin-top: 0.3cm;
	}
	.kfyz-tables td {
		border: 0.1mm solid #000 ;
		font-weight: 500;
		height: 0.8cm !important;
		max-height: 0.8cm !important;
		white-space: nowrap;
		text-align: center;
		font-size: 3.5mm;
		color: red;
		font-family: '宋体';
	}
	.kfyz-tables th {
		border: 0.1mm solid #000;
		font-weight: 500;
		height: 0.65cm !important;
		max-height: 0.65cm !important;
		white-space: nowrap;
		text-align: center;
		font-size: 3.5mm;
		color: red;
		font-family: '宋体';
	}
	.kfyz-tables td .yzd-name {
	    font-size: 3.5mm;
	    text-align: left;
	    margin-left: 1mm;
	    word-break: break-all;
	    white-space: normal;
	    word-wrap: break-word;
	    overflow: hidden;
	    text-overflow: ellipsis;
	    -webkit-line-clamp: 2;
	    -webkit-box-orient: vertical;
	}
	.kfxdyz{
		width:3.3cm;
		max-width: 3.3cm;
	}
	.kfxdyzrq{
		width:1.05cm;
		max-width: 1.05cm;
	}
	.kfxdyzsj{
		width:1.05cm;
		max-width: 1.05cm;
	}
	.kfxdyzqm{
		width:1.3cm;
		max-width: 1.3cm;
	}
	.kfyznr{
		width: 5.65cm;
		max-width: 5.65cm;
	}
	.kfyzcdqm{
		width: 2.25cm;
		max-width: 2.25cm;
	}
	.kftzsj{
		width: 1.4cm;
		max-width: 1.4cm;
	}
	.kfrq{
		width: 1cm;
		max-width: 1cm;
	}
	.kfzxrqsjqm{
		width: 5.4cm;
		max-width: 5.4cm;
	}
	.kfzao{
		width: 1.55cm;
		max-width: 1.55cm;
	}
	.kfzhong{
		width: 1.4cm;
		max-width: 1.4cm;
	}
	.kfwan{
		width: 1.35cm;
		max-width: 1.35cm;
	}
	.kfwu{
		width: 1.15cm;
		max-width: 1.15cm;
	}
