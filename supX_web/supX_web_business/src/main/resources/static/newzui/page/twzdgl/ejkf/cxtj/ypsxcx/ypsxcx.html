<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药品失效查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b ">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search padd-b-10 flex-container flex-align-c">
            <div class="flex-container flex-align-c padd-r-10">
                <span class="ft-14 whiteSpace padd-r-5">药房</span>
                <select-input class="wh100" @change-data="commonResultChange"
                              :child="yfkfList" :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm"
                              :name="'param.yfbm'" :search="true" :index_mc="'yfmc'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="ft-14 whiteSpace padd-r-5">时间段</span>
                <input class="zui-input  wh240 " placeholder="请选择申请日期" id="timeVal"/>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="ft-14 whiteSpace padd-r-5">近效期</span>
                <select-input :search="true" class="wh150" @change-data="commonResultChange" :not_empty="false"
                              :child="ypxqList" :index="param.ypxqMessage"
                              :val="param.ypxqMessage" :name="'param.ypxqMessage'" id="ypxqMessage">
                </select-input>
            </div>
            <div class="flex-container flex-align-c">
                <span class="ft-14 whiteSpace padd-r-5">检索</span>
                <input class="zui-input  wh240 " placeholder="请输入关键字" v-model="param.parm" @keyup.13="goToPage(1)"/>
            </div>

        </div>
    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">序号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药品编码</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl">药品名称</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药品规格</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药品种类</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">库存数量</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药品进价</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药品零价</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">零价金额</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药品批号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">有效期至</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药品产地</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">库房单位</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">药房单位</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">分装比例</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-heights" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)"
                    :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">001</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypbm"></div>
                    </td>
                    <td>
                        <div class=" zui-table-cell cell-xl" v-text="item.ypmc"></div>
                    </td>
                    <td>
                        <div class=" zui-table-cell cell-s" v-text="item.ypgg"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypzlmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kcsl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.kcsl,2)"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.scph"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData zan-border text-center">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
              :next-more="nextMore"></page>


    </div>

</div>

<script src="ypsxcx.js"></script>
</body>

</html>
