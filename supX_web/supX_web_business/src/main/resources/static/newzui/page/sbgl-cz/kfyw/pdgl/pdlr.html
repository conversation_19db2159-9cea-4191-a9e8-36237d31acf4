<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="pdlr">
    <div class="toolMenu toolMenu_2" style="display: block;">
        <button @click="save"><span class="fa fa-save"></span>保存</button>
        <button @click="autoGen"><span class="fa fa-plus"></span>自动生成</button>
        <button @click="addNew()"><span class="fa fa-plus"></span>新增</button>
        <span style="margin-left: 10px">凭证号</span>
        <select v-model="pdb" @change="clearAll">
            <option value="0">-请选择-</option>
            <option v-for="item in pzhList" v-text="item.pdpzh" :value="item"></option>
        </select>
        <span style="margin-left: 10px">物资检索</span>
        <input @keydown.enter="searchYP" placeholder="输入内容后按回车查询" type="text" v-model="ypjs">
    </div>

    <div class="enter_tem1 enter_pdlrlr">
        <div class="toolMenu button0" style="display: block;">
            <button @click="add"><span class="fa fa-save"></span>添加</button>
            <button @click="clear"><span class="fa fa-plus"></span>清除</button>
        </div>
        <div class="enter_tem1_title">数据录入</div>

        <div class="enter_tem1_item">
            <span>物资名称</span>
            <input id="ypmc" v-model="popContent.wzmc" @keyup="changeDown($event,'ypmc')" @input="change($event,'ypmc')"
                   data-notEmpty="true">
            <search-table :page="page" :message="searchCon" :selected="selSearch" :total="total" :them="them"
                          :them_tran="them_tran" :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut"
                          @click-two="selectOne">
            </search-table>
        </div>
        <div class="enter_tem1_item">
            <span>物资编码</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.wzbm" data-notEmpty="true">
        </div>
        <div class="enter_tem1_item">
            <span>物资规格</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.wzgg" data-notEmpty="true">
        </div>

        <div class="enter_tem1_item">
            <span>分装比例</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.fzbl" data-notEmpty="true">
        </div>
        <div class="enter_tem1_item">
            <span>生产批号</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.scph" data-notEmpty="true">
        </div>

        <div class="enter_tem1_item">
            <span>药房单位</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.yfdwmc" data-notEmpty="true">
        </div>
        <!--<div class="enter_tem1_item">
            <span>生产日期</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.scrq">
        </div>-->
        <div class="enter_tem1_item">
            <span>有效期至</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.yxqz">
        </div>

        <div class="enter_tem1_item">
            <span>物资零价</span>
            <input class="_inputData" disabled="disabled" v-model="popContent.yplj">
        </div>
        <div class="enter_tem1_item">
            <span>库存数量</span>
            <input type="number"  class="_inputData" disabled="disabled" type="number" v-model="popContent.kcsl">
        </div>
        <div class="enter_tem1_item">
            <span>实存数量</span>
            <input type="number"  class="_inputData" @keyup.enter="add" type="number" v-model="popContent.scsl">
        </div>
    </div>

    <div class="enter_tem1 enter_pdlrxs">
        <div class="toolMenu button1" style="display: block;">
            <button @click="del"><span class="fa fa-scissors"></span>删除</button>
            <button @click="clear"><span class="fa fa-plus"></span>清除</button>
        </div>
        <div class="enter_tem1_title">数据显示区</div>
        <div class="table_tem2" style="height: calc(50% - 60px);">
            <table>
                <tr>
                    <th>物资编码</th>
                    <th>物资名称</th>
                    <th>规格</th>
                    <th>库存数量</th>
                    <th>实存数量</th>
                    <th>单位</th>
                    <th>生产批号</th>
                    <th>有效期至</th>
                    <th>库房单位</th>
                    <th>分装比例</th>
                    <th>产地</th>
                    <th>供货单位</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index),getIndex($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <td v-text="item.wzbm"></td>
                    <td v-text="item.wzmc"></td>
                    <td v-text="item.wzgg"></td>
                    <td v-text="item.kcsl"></td>
                    <td><input v-model="item.scsl" type="number"
                               style="width: 70px;height: inherit;text-align: center;"/></td>
                    <td v-text=" item.yfdwmc"></td>
                    <td v-text="item.scph "></td>
                    <td v-text="item.yxqz "></td>
                    <td v-text="item.kfdwmc "></td>
                    <td v-text="item.fzbl "></td>
                    <td v-text="item.cd "></td>
                    <td v-text="item.ghdw "></td>
                </tr>
            </table>
        </div>
    </div>
</div>

<!--新增物资弹出框-->
<div id="_addNew" class="enter_tem1 addNew" v-show="isShow"
     style="background: gainsboro; top:8%;left:25%;width: 600px;height: 300px;position: absolute;z-index: 100px;">
    <div class="enter_tem1_item">
        <span>物资名称</span>
        <input v-model="addContent.wzmc" @keyup.up.down.enter="changeDownAdd($event)" @input="changeAdd($event)" data-notEmpty="true">
        <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                      :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut" @click-two="selectOneAdd">
        </search-table>
    </div>
    <div class="enter_tem1_item">
        <span>物资编码</span>
        <input class="_addData" disabled="disabled" v-model="addContent.wzbm" data-notEmpty="true">
    </div>
    <div class="enter_tem1_item">
        <span>物资规格</span>
        <input class="_addData" disabled="disabled" v-model="addContent.wzgg" data-notEmpty="true">
    </div>

    <div class="enter_tem1_item">
        <span>分装比例</span>
        <input class="_addData" disabled="disabled" v-model="addContent.fzbl" data-notEmpty="true">
    </div>
    <div class="enter_tem1_item">
        <span>生产批号</span>
        <input class="_addData"  @keydown="nextFocus($event)" v-model="addContent.scph" data-notEmpty="true">
    </div>
    <!--<div class="enter_tem1_item">
        <span>系统批号</span>
        <input class="_addData" v-model="addContent.xtph" data-notEmpty="true">
    </div>-->

    <div class="enter_tem1_item">
        <span>库房单位</span>
        <input class="_addData"  disabled="disabled" v-model="addContent.kfdwmc" data-notEmpty="true">
    </div>
    <!--<div class="enter_tem1_item">
        <span>生产日期</span>
        <input type="date" class="_addData" v-model="addContent.scrq">
    </div>-->
    <div class="enter_tem1_item">
        <span>有效期至</span>
        <input type="date" class="_addData" v-model="addContent.yxqz">
    </div>

    <div class="enter_tem1_item">
        <span>物资零价</span>
        <input type="number"  class="_addData" v-model="addContent.dj">
    </div>
    <div class="enter_tem1_item">
        <span>供货单位</span>
        <select-input @change-data="resultChange" :data-notEmpty="true" :child="ghdwList" :index="'dwmc'"
                      :index_val="'dwbm'" :val="addContent.ghdw" :search="true" :name="'addContent.ghdw'">
        </select-input>
    </div>

    <!--<div class="enter_tem1_item">
        <span>实存数量</span>
        <input type="number" class="_addData" @keyup.enter="addNewYp" type="number" v-model="addContent.scsl" data-notEmpty="true">
    </div>-->
    <div>
        <button @click="addNewYp"><span class="fa fa-plus"></span>保存</button>
        <button @click="clear"><span class="fa fa-plus"></span>清除</button>
        <button @click="close"><span class="fa fa-plus"></span>关闭</button>
    </div>
</div>
<script type="text/javascript " src="pdlr.js "></script>