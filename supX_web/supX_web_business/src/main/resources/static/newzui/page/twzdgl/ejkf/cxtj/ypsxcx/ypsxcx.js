var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        yfkfList: [],
        yfkf: 0, //药房库房信息
        param: {
            'page': 1,
            'rows': 10,
            'sort': 'rkd.rkdh',
            'order': 'desc',
            'ypxqMessage': 'all',
            'shzfbz': 1,
            'kfbm': '',
            'rkfs': '01',//01-出库
            'beginrq': null,
            'endrq': null,
            'parm': ''
        },
        ypxqList: {
            "all": "全部",
            "3": "三个月过期",
        },
    },
    mounted:function(){
        this.getYfData()
    },
    methods: {
        commonResultChange: function (val) {
            var type = val[2][1];
            switch (type) {
                case "ypxqMessage":
                    Vue.set(this.param, "ypxqMessage", val[0]);
                    this.getData();
                    this.$forceUpdate();
                    break;
                case "yfbm":
                    Vue.set(this.param, "yfbm", val[0]);
                    this.getData();
                    this.$forceUpdate();
                    break;
            }
        },
        getYfData: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf', function (data) {
                    if (data.a == 0) {
                        wrapper.yfkfList = data.d.list;
                        wrapper.param.yfbm=data.d.list[0]['yfbm']
                        wrapper.getData();
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
            //获取列表
            if (sessionStorage.getItem("messageParam") && sessionStorage.getItem("messageParam") != 'undefined') {
                var messageParam = JSON.parse(sessionStorage.getItem("messageParam"));
                this.param.yfbm = messageParam.yfbm;
                this.param.ypxqMessage = "3";
                sessionStorage.removeItem("messageParam");
            }
        },
        getData: function () {
            if (!this.param.yfbm) {
                malert('请选择药房', 'top', 'defeadted');
                return;
            }
            this.param.sfpdkcsl = 1;
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=sxyp&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                }
            });
        }
    }
});
laydate.render({
    elem: '#timeVal'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        wrapper.param.beginrq = value.slice(0, 10);
        wrapper.param.endrq = value.slice(13, 23);
        wrapper.getData();
    }
});





