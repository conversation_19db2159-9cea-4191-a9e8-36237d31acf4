<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>病人接诊</title>
    <link href="brPage/user.css" rel="stylesheet">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>

</head>
<body>
<div class="printArea printShow"></div>
<div class="printHide" v-cloak id="jbxx">
    <div class="jbxxcontent tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">基本信息</div>
        </div>
        <div class="tab-card-body padd-t-10">
            <div class=" flex-container flex-wrap-w padd-t-15 ">
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right ">姓名</div>
                    <input class="zui-input wh182  " placeholder="姓名" :disabled="saveShow" name="text"
                           v-model="zcxx.brxm" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right">性别</div>
                    <select-input class="wh182" :disable="saveShow" @change-data="resultChange" :not_empty="false"
                                  :child="brxb_tran"
                                  :index="zcxx.brxb" :val="zcxx.brxb" :name="'zcxx.brxb'">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right">年龄</div>
                   <div class="wh182 flex-container">
                       <input @mousewheel.prevent type="number" :disabled="saveShow" class="zui-input wh50 "
                              placeholder="年龄" v-model="zdxx.brnl" @keydown="keyDownEnterNext($event)"
                              data-notEmpty="false"/>
                       <select-input class="wh50" :disable="saveShow" @change-data="resultChange"
                                     :not_empty="false"
                                     :child="nldw_tran" :index="zdxx.nldw" :val="zdxx.nldw" :name="'zdxx.nldw'">
                       </select-input>
                       <input @mousewheel.prevent type="number" :disabled="saveShow" class="zui-input wh50 "
                              placeholder="年龄2" v-model="zdxx.nl2" @keydown="keyDownEnterNext($event)"
                              data-notEmpty="false"/>
                       <select-input class="wh50" :disable="saveShow" @change-data="resultChange"
                                     :not_empty="false"
                                     :child="nldw_tran" :index="zdxx.nldw2" :val="zdxx.nldw2" :name="'zdxx.nldw2'">
                       </select-input>
                   </div>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                    <div class="padd-r-5 ft-14 wh66 text-right">国籍</div>
                    <select-input class="wh182" @change-data="resultChange" :disable="saveShow" :not_empty="true"
                                  :child="gjList"
                                  :index="'gjmc'"
                                  :index_val="'gjbm'" :val="zcxx.brgj" :name="'zcxx.brgj'" :search="false"
                                  :disable="false"
                                  :phd="''" id="gj">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10"
                     v-if="gsjbpxBm!=0 && gsjbpxBm!=undefined || csqxContent.N05001200255 == '1'">
                    <div class="padd-r-5 ft-14 wh66 text-right">民族</div>
                    <select-input class="wh182" @change-data="resultChange" :disable="saveShow" :not_empty="true"
                                  :child="mzList"
                                  :index="'mzmc'"
                                  :index_val="'mzbm'" :val="zcxx.brmz" :name="'zcxx.brmz'" :search="true"
                                  :disable="false"
                                  :phd="''" id="mz">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10" v-if="hyxxBm!=0">
                    <div class="padd-r-5 ft-14 wh66 text-right">婚姻状况</div>
                    <select-input class="wh182" @change-data="resultzcChange" :disable="saveShow" :not_empty="false"
                                  :child="hyList"
                                  :index="'hyzkmc'"
                                  :index_val="'hyzkbm'" :val="zcxx.hyzk" :name="'zcxx.hyzk'" :search="false"
                                  :disable="false" :index_mc="'hyzkmc'"
                                  :phd="''" id="hyzk">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                    <div class="padd-r-5 ft-14">生育状况</div>
                    <select-input class="wh182" @change-data="resultChange" :disable="saveShow" :not_empty="false"
                                  :child="syzk_tran" :index="zcxx.syzk"
                                  :val="zcxx.syzk" :name="'zcxx.syzk'">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                    <div class="padd-r-5 ft-14 wh66 text-right">身份证号</div>
                    <input class=" zui-input  wh182" type="text" :disable="saveShow" v-model="zcxx.sfzjhm"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right">职业</div>
                    <select-input class="wh182" @change-data="resultzcChange" :disable="saveShow" :not_empty="false"
                                  :child="zyList" :index="'zymc'" :index_val="'zybm'"
                                  :val="zcxx.zybm" :name="'zcxx.zybm'" :index_mc="'zybmmc'" :search="true">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10" v-if="gsjbpxBm!=0">
                    <div class="padd-r-5 ft-14 wh66 text-right">文化程度</div>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow" :not_empty="true"
                                  :child="whcd_tran"
                                  :index="zcxx.whcd"
                                  :val="zcxx.whcd" :name="'zcxx.whcd'" :search="false" :disable="false" :phd="''">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-4 ft-14 wh66 text-right">身高(cm)</div>
                    <input class="zui-input  wh182  " :disabled="saveShow" @mousewheel.prevent placeholder="身高(cm)"
                           type="number" v-model="zcxx.sg" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-4 ft-14 wh66 text-right">体重(Kg)</div>
                    <input class="zui-input   wh182 " :disabled="saveShow" @mousewheel.prevent placeholder="体重(Kg)"
                           type="number" v-model="zcxx.tz" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                    <div class="padd-r-5 ft-14 wh66 text-right">联系电话</div>
                    <input class="zui-input  wh182 " :disabled="saveShow" @mousewheel.prevent placeholder="联系人电话"
                           type="number" v-model="zcxx.sjhm" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <P class="padd-r-5 ft-14 wh66 text-right">联系人姓名</p>
                    <input class="zui-input wh182" :disabled="saveShow" placeholder="联系人姓名" name="text"
                           v-model="zcxx.lxrxm"
                           data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <P class="padd-r-5 ft-14 wh66 text-right">联系人关系</p>
                    <select-input class="wh182" :disable="saveShow" @change-data="resultzcChange" :not_empty="false"
                                  :child="lxrgxList" :index="'lxrgxmc'"
                                  :index_val="'lxrgxbm'" :val="zcxx.lxrgx" :search="true" :name="'zcxx.lxrgx'">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right">电话</div>
                    <input @mousewheel.prevent type="number" class="zui-input wh182   " placeholder="联系人电话"
                           :disabled="saveShow" name="text" v-model="zcxx.lxrdh" data-notEmpty="false"
                           @keydown="keyDownEnterNext($event)"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right">证件号码</div>
                    <input @mousewheel.prevent type="text" class="zui-input wh182   "  :disabled="saveShow" placeholder="联系人证件号"
                           name="text" v-model="zcxx.sfzjhm" data-notEmpty="false" v-model="zdxx.lxrzjh"
                           @keydown="keyDownEnterNext($event)"/>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 ft-14 wh66 text-right font-bolder ">籍贯</div>
                    <select-input class="wh182" @change-data="resultzcChange" :disable="saveShow" :not_empty="true"
                                  :child="provinceList" :index="'xzqhmc'"
                                  :index_val="'xzqhbm'" :val="zcxx.jzdsheng" :search="true" :name="'zcxx.jzdsheng'"
                                  :index_mc="'jzdshengmc'" :phd="'省'" id="sheng">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right">市</div>
                    <select-input class="wh182" @change-data="resultzcChange" :disable="saveShow"
                                  :data-notEmpty="false"
                                  :child="cityList" :index="'xzqhmc'"
                                  :index_val="'xzqhbm'" :val="zcxx.jzdshi" :search="true" :name="'zcxx.jzdshi'"
                                  :index_mc="'jzdshimc'"
                                  :phd="'市'" id="shi">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <div class="padd-r-5 ft-14 wh66 text-right">区</div>
                    <select-input class="wh182" @change-data="resultzcChange" :disable="saveShow"
                                  :data-notEmpty="false"
                                  :child="countyList"
                                  :index="'xzqhmc'" :index_val="'xzqhbm'" :val="zcxx.jzdxian" :search="true"
                                  :name="'zcxx.jzdxian'"
                                  :index_mc="'jzdxianmc'" :phd="'区/县'" id="xian">
                    </select-input>
                </div>
                <div class="flex-container flex-jus-c flex-align-c padd-b-10 padd-r-10">
                    <p class="padd-r-5 ft-14 wh66 text-right">居住地</p>
                    <input @mousewheel.prevent class="zui-input wh453 " :disabled="saveShow" placeholder="现住址/学校/单位"
                           v-model="zcxx.jzdmc" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                </div>
                <div class="flex-align-c padd-r-10   flex-container margin-b-10">
                    <p class="padd-r-5 ft-14 wh66 text-right">学校/工作单位</p>
                    <input class="zui-input wh453" v-model="zcxx.gzdw" :disabled="saveShow" type="text"
                           data-notEmpty="false"
                           @keydown="nextFocus($event)">
                </div>
                <!-- <div class="zui-inlines zui-inlines-w ">
                    <div class="zui-form-labels"><p  class="letter-spacing">现住址</p><p style="width: 62px;">学校/单位</p></div>
                    <div class="zui-input-inlines zui-input-inlines-w">
                        <input class="zui-input wh990 " :disable="saveShow" placeholder="现住址/学校/单位"  name="text" v-model="zcxx.jzdmc" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
                    </div>
                </div>-->
            </div>
        </div>
    </div>
    <div class="jbxxcontent tab-card">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">诊断信息</div>
        </div>
        <div class="tab-card-body padd-t-10 flex-container flex-wrap-w  ">
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5 whiteSpace wh66 text-right">历史就诊</span>
                <!--<select-input :disable="saveShow" @change-data="lsghxxChange" :not_empty="false"-->
                <!--:child="lsghxxList" :index="'ghxh'"  :index_val="'ghxh'" -->
                <!--:val="ghxh" :search="true" :name="'ghxh'">-->
                <!--</select-input>-->

                <select-div-input style="min-width:180px" class="wh182" :disable="saveShow" @change-data="lsghxxChange"
                                  :styles="'nzp'" :not_empty="false" :child="lsghxxList"
                                  :rymc="'ghxh'" :position="'jbmc'" :phone="'jzsj1'" :index_val="'ghxh'" :val="ghxh"
                                  :name="'ghxh'" :search="true">
                </select-div-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5  wh66 text-right">初/复诊</span>
                <select-input class="wh182" @change-data="resultChange" :not_empty="false" :disable="saveShow"
                              :child="cfz_tran"
                              :index="zdxx.sffz" :val="zdxx.sffz" :name="'zdxx.sffz'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5 wh66 text-right">患者去向</span>
                <input @mousewheel.prevent class="zui-input wh182 " :disabled="saveShow" v-model="zdxx.hzqx"
                       @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 wh66 text-right font-bolder ">发病时间</span>
                <div class=" flex-container wh182">
                    <input autofocus v-if="csqxContent.N03001200147 != '1'" class="zui-input wh100    "
                           :data-notEmpty="!csqxContent.N05001200271 ? true : false" placeholder="请选择时间" id="timeVal" :disabled="saveShow" name="text"
                           v-model="zdxx.fbrq" data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
                    <input autofocus v-if="csqxContent.N03001200147 == '1'" class="zui-input wh100    "
                           placeholder="请选择时间" id="timeVal" :disabled="saveShow" name="text" v-model="zdxx.fbrq"
                           data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
                   <div class="position">
                       <input :disabled="saveShow" type="number" @mousewheel.prevent @keydown.up.prevent
                              class="zui-input " v-model="zdxx.fbts" @input="fbts"/>
                       <span class="cm">天</span>
                   </div>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5 wh66 text-right">发病地点</span>
                <select-input class="wh182" @change-data="resultChange" :not_empty="false" :disable="saveShow"
                              :child="fbdd_tran"
                              :index="zdxx.fbdd" :val="zdxx.fbdd" :name="'zdxx.fbdd'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="fzxx.fzjbbm">
                <span class="padd-r-5 wh66 text-right">分诊级别</span>
                <input type="text" class="zui-input wh88" v-model="fzjb[fzxx.fzjbbm]" readonly/>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                <span class="padd-r-5 wh66 text-right">是否确诊</span>
                <select-input class="wh182" @change-data="resultChange" :not_empty="false" :disable="saveShow"
                              :child="nh_tran"
                              :index="zdxx.sfgm" :val="zdxx.sfgm" :name="'zdxx.sfgm'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                <span class="padd-r-5 wh66 text-right">诊断依据</span>
                <select-input class="wh182" @change-data="resultChange" :not_empty="false" :disable="saveShow"
                              :child="zdyj_tran"
                              :index="zdxx.sfgm" :val="zdxx.sfgm" :name="'zdxx.sfgm'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5 wh66 text-right">血酮</span>
                <div class=" relative ">
                    <input @mousewheel.prevent type="number" class="zui-input wh88" :disabled="saveShow"
                           v-model="zdxx.xtong" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                    <span class="cm ">mmol</span>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5 wh66 text-right">血糖</span>
                <input class="zui-input  wh88 " id="fqfh" placeholder="饭前饭后" :disabled="saveShow" name="text"
                       v-model="zdxx.xtms" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
                <div>/</div>
                <div class=" relative ">
                    <input @mousewheel.prevent type="number" class="zui-input wh88" :disabled="saveShow"
                           v-model="zdxx.xt" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                    <span class="cm ">mmol</span>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5 wh66 text-right">血压</span>
                <div class=" relative ">
                    <input @mousewheel.prevent type="number" class="zui-input wh88  " :disabled="saveShow"
                           v-model="zdxx.xySsy" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                    <span class="cm ">mmhg</span>
                </div>
                <div>/</div>
                <div class=" relative">
                    <input @mousewheel.prevent type="number" class="zui-input wh88 " :disabled="saveShow"
                           v-model="zdxx.xySzy" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                    <span class="cm ">mmhg</span>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">体温</span>
                <div class="relative">
                    <input @mousewheel.prevent type="number" class="zui-input  wh182 " :disabled="saveShow"
                           v-model="zdxx.tw" @keydown="keyDownEnterNext($event)" :data-notEmpty="!csqxContent.N05001200271 ? true : false" name="text"/>
                    <span class="cm">°C</span>
                </div>

            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">呼吸</span>
                <div class="relative">
                    <input @mousewheel.prevent class="zui-input wh182 " type="number" :disabled="saveShow"
                           v-model="zdxx.hx" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                    <span class="cm">次/分</span>
                </div>

            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">脉搏</span>
                <div class="relative">
                    <input @mousewheel.prevent type="number" class="zui-input wh182" :disabled="saveShow"
                           v-model="zdxx.mb" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                    <span class="cm">次/分</span>
                </div>

            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">心率</span>
                <div class="relative">
                    <input @mousewheel.prevent type="number" class="zui-input wh182" :disabled="saveShow"
                           v-model="zdxx.xl" @keydown="keyDownEnterNext($event)" data-notEmpty="false" name="text"/>
                    <span class="cm">次/分</span>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                <span class="padd-r-5 wh66 text-right font-bolder ">抢救时间</span>
                <div class=" flex-container wh182">
                    <input  class="zui-input wh182"
                           placeholder="请选择时间" id="timeValqj" :disabled="saveShow" name="text"
                           v-model="zdxx.qjsj" data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
           
                </div>
            </div>
     
            <div class="flex-container flex-align-c wh100MAx padd-b-10 padd-r-10 ">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 wh66 text-right font-bolder">主诉</span>
                <div class="relative wh100MAx">
                    <!-- onkeyup="value=value.replace(/[^\a-\z\A-\Z\u4E00-\u9FA5]/g,'')" onpaste="value=value.replace(/[^\a-\z\A-\Z\u4E00-\u9FA5]/g,'')" oncontextmenu = "value=value.replace(/[^\a-\z\A-\Z\u4E00-\u9FA5]/g,'')" -->
                    <input class="zui-input " id="zs" data-select="no" placeholder="主诉" :disabled="saveShow" name="text"
                           v-model="zdxx.zs" @keydown="keyDownEnterNext($event)" :data-notEmpty="!csqxContent.N05001200271 ? true : false"/>
                </div>
            </div>
            <div class="flex-container flex-align-c wh100MAx padd-b-10 padd-r-10 ">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 wh66 text-right font-bolder">现病史</span>
                <textarea :disabled="saveShow"  rows="3" cols="10"
                          class="wh100MAx padd-t-10 padd-b-10 padd-l-10 padd-r-10" placeholder="请输入现病史" type="text"
                          v-model="zdxx.xbs"
                          @keydown="keyDownEnterNext($event)" :data-notEmpty="!csqxContent.N05001200271 ? true : false"
                ></textarea>

                <!--<input class="zui-input wh100MAx " data-select="no" placeholder="现病史" :disabled="saveShow" name="text"-->
                <!--@keydown="keyDownEnterNext($event)" v-model="zdxx.xbs" @keydown="keyDownEnterNext($event)" data-notEmpty="true" />-->
            </div>
            <div class="flex-container wh100MAx flex-align-c  padd-b-10 padd-r-10">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 wh66 text-right">流行病史</span>
                <input class="zui-input wh100MAx " data-select="no" placeholder="流行病史" :disabled="saveShow" name="text"
                       v-model="zdxx.whqtzz" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
            </div>
            <div class="flex-container wh100MAx flex-align-c  padd-b-10 padd-r-10">
                <span class="padd-r-5 wh66 text-right">既往史</span>
                <input class="zui-input wh100MAx " data-select="no" placeholder="既往史" :disabled="saveShow" name="text"
                       v-model="zdxx.jws" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
            </div>
            <div class="flex-container flex-align-c wh100MAx padd-b-10 padd-r-10 ">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 wh66 text-right font-bolder">临床症状体征</span>
                <div class="relative wh100MAx">
                    <!--
                        <input class="zui-input" placeholder="请输入症状体征" disabled name="text" v-model="zdxx.zyzztz" @keydown="keyDownEnterNext($event)" data-notEmpty="false"/>
                          -->
                    <input class="zui-input wh100MAx " data-select="no" placeholder="症状体征" :disabled="saveShow"
                           name="text" :data-notEmpty="!csqxContent.N05001200271 ? true : false" v-model="zdxx.zyzztz"
                           @keydown="tzchangeDown($event,'zyzztz','selSearch3','tzsearchCon')"
                           @input="change($event,false,'zyzztz',$event.target.value)"/>
                    <search-table :message="tzsearchCon" :selected="selSearch3" :them="tzthem"
                                  :page="page" @click-one="checkedOneOut" @click-two="selecttz">
                    </search-table>
                </div>
            </div>
            <!--<div class="zui-inlines-z">
                <span class="zui-form-labels">是否过敏</span>
                <div class="zui-input-inlines-z">
                    <select-input  @change-data="resultChange" :disable="saveShow" :not_empty="false" :child="istrue_tran" :index="zdxx.sfgm" :val="zdxx.sfgm" :name="'zdxx.sfgm'" >
                    </select-input>
                </div>
            </div>-->
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 wh66 text-right font-bolder">过敏史</span>
                <div class="position">
                    <input class="zui-input wh182" id="gms" data-select="no" placeholder="过敏史" :disabled="saveShow"
                           name="text"
                           v-model="zdxx.gms" @keydown="keyDownEnterNext($event)" :data-notEmpty="!csqxContent.N05001200271 ? true : false"/>
                </div>
            </div>

            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">家族史</span>
                <input class="zui-input wh182 " data-select="no" placeholder="家族史" :disabled="saveShow" name="text"
                       @keydown="keyDownEnterNext($event)" v-model="zdxx.jzs" @keydown="keyDownEnterNext($event)"
                       data-notEmpty="false"/>
            </div>

            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <input class="zui-input wh90 " @keydown="keyDownEnterNext($event)" :disabled="saveShow"
                       placeholder="疾病前缀" v-model="zdxx.zdsm1"/>
                <span class=" wh66 text-right font-bolder padd-l-5 red">诊断疾病</span>
                <div class="position flex-container ">
                    <input @blur="BlurData('zdxx.jbbm','诊断疾病','zdxx.jbmc')" autofocus
                           class="zui-input margin-l-10 wh122"
                           placeholder="诊断疾病" :disabled="saveShow" data-notEmpty="true" v-model="zdxx.jbmc"
                           @keydown="changeDown($event,'jbbm','selSearch','searchCon')"
                           @input="change($event,false,'jbbm',$event.target.value)"/>
                    <search-table :message="searchCon" :selected="selSearch" :them="them" :them_tran="trem_tran"
                                  :page="page" @click-one="checkedOneOut" @click-two="selectJbbm">
                    </search-table>
                </div>
                    <input class="zui-input wh100 margin-l-15 " @keydown="keyDownEnterNext($event)" :disabled="saveShow"
                           placeholder="疾病后缀" v-model="zdxx.zdsm2"/>
                    <select-input :disable="saveShow" @change-data="resultChange" class="wh90 margin-l-15"
                                  :child="sfQz_tran" :index="zdxx.zdsfqz" :val="zdxx.zdsfqz"
                                  :name="'zdxx.zdsfqz'">
                    </select-input>
                <button v-waves class="tong-btn btn-parmary height-28  margin-l-5" @click="openFjzd()">离休附加诊断</button>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">其他诊断</span>
                <!--                    @blur="BlurData('zdxx.qtzdbm','其他诊断','zdxx.qtzdmc')"-->
                <input class="zui-input wh182"
                       placeholder="其他诊断"
                       :disabled="saveShow" data-notEmpty="false" v-model="zdxx.qtzdmc"
                       @keydown="changeDown($event,'qtzd','selSearch1','jbsearchCon','qtzdbm','qtzdmc')"
                       @input="change($event,false,'qtzd',$event.target.value,'qtzdbm','qtzdmc','selSearch1')"/>
                <search-table :message="jbsearchCon" :selected="selSearch1"
                              :them="jbthem" :page="page"
                              @click-one="checkedOneOut" @click-two="selectQtzd">
                </search-table>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 " v-if="csqxContent.N03001200150=='1'">
                <span class="padd-r-5 wh66 text-right">其他诊断1</span>
                <!--                @blur="BlurData('zdxx.qtzdbm1','其他诊断1','zdxx.qtzdmc1')"-->
                <input class="zui-input wh182"
                       placeholder="其他诊断1"
                       :disabled="saveShow" data-notEmpty="false" v-model="zdxx.qtzdmc1"
                       @keydown="changeDown($event,'qtzd','selSearch8','jbsearchCon','qtzdbm1','qtzdmc1')"
                       @input="change($event,false,'qtzd',$event.target.value,'qtzdbm1','qtzdmc1','selSearch8')"/>
                <search-table :message="jbsearchCon" :selected="selSearch8"
                              :them="jbthem" :page="page"
                              @click-one="checkedOneOut" @click-two="selectQtzd">
                </search-table>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 " v-if="csqxContent.N03001200150=='1'">
                <span class="padd-r-5 wh66 text-right">其他诊断2</span>
                <!--                @blur="BlurData('zdxx.qtzdbm2','其他诊断2','zdxx.qtzdmc2')"-->
                <input class="zui-input wh182"
                       placeholder="其他诊断2"
                       :disabled="saveShow" data-notEmpty="false" v-model="zdxx.qtzdmc2"
                       @keydown="changeDown($event,'qtzd','selSearch8','jbsearchCon','qtzdbm2','qtzdmc2')"
                       @input="change($event,false,'qtzd',$event.target.value,'qtzdbm2','qtzdmc2','selSearch9')"/>
                <search-table :message="jbsearchCon" :selected="selSearch8"
                              :them="jbthem" :page="page"
                              @click-one="checkedOneOut" @click-two="selectQtzd">
                </search-table>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 " v-if="csqxContent.N03001200150=='1'">
                <span class="padd-r-5 wh66 text-right">其他诊断3</span>
                <!--                @blur="BlurData('zdxx.qtzdbm3','其他诊断3','zdxx.qtzdmc3')"-->
                <input class="zui-input wh182"
                       placeholder="其他诊断3"
                       :disabled="saveShow" data-notEmpty="false" v-model="zdxx.qtzdmc3"
                       @keydown="changeDown($event,'qtzd','selSearch9','jbsearchCon','qtzdbm3','qtzdmc3')"
                       @input="change($event,false,'qtzd',$event.target.value,'qtzdbm3','qtzdmc3','selSearch10')"/>
                <search-table :message="jbsearchCon" :selected="selSearch9"
                              :them="jbthem" :page="page"
                              @click-one="checkedOneOut" @click-two="selectQtzd">
                </search-table>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 " v-if="csqxContent.N03001200150=='1'">
                <span class="padd-r-5 wh66 text-right">其他诊断4</span>
                <!--                @blur="BlurData('zdxx.qtzdbm4','其他诊断4','zdxx.qtzdmc4')"-->
                <input class="zui-input wh182"
                       placeholder="其他诊断4"
                       :disabled="saveShow" data-notEmpty="false" v-model="zdxx.qtzdmc4"
                       @keydown="changeDown($event,'qtzd','selSearch10','jbsearchCon','qtzdbm4','qtzdmc4')"
                       @input="change($event,false,'qtzd',$event.target.value,'qtzdbm4','qtzdmc4','selSearch11')"/>
                <search-table :message="jbsearchCon" :selected="selSearch10"
                              :them="jbthem" :page="page"
                              @click-one="checkedOneOut" @click-two="selectQtzd">
                </search-table>
            </div>
            <!--<div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">医保诊断</span>
                <input @blur="BlurData('zdxx.ybjbbm','医保诊断','zdxx.ybjbmc')" class="zui-input wh182" placeholder="医保诊断"
                       :disabled="saveShow" name="text"
                       data-notEmpty="false"
                       v-model="zdxx.ybjbmc"
                       @keydown="changeDown($event,'ybjb','selSearch2','ybsearchCon')"
                       @input="change($event,false,'ybjb',$event.target.value)"/>
                <search-table :message="ybsearchCon" :selected="selSearch2"
                              :them="ybthem" :page="page"
                              @click-one="checkedOneOut" @click-two="selectYbjbbm">
                </search-table>
            </div>-->
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                <span class="padd-r-5 wh66 text-right">诊断结果</span>
                <select-input @change-data="resultChange" class="wh182" :disable="saveShow" :not_empty="false"
                              :child="zdjg_tran"
                              :index="zdxx.sfgm" :val="zdxx.sfgm" :name="'zdxx.sfgm'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                <span class="padd-r-5 wh66 text-right">转归</span>
                <select-input @change-data="resultChange" class="wh182" :disable="saveShow" :not_empty="false"
                              :child="zg_tran"
                              :index="zdxx.sfgm" :val="zdxx.sfgm" :name="'zdxx.sfgm'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">主病</span>
                <!--                    @blur="BlurData('zdxx.zy1.','主病','zdxx.zyzh')"-->
                <input class="zui-input wh182"
                       @input="change1(false,'zyzh',$event.target.value,$event,'selSearch5')"
                       :disabled="saveShow"
                       name="text" v-model="zdxx.zyzh"
                       @keydown="changeDown1($event,'zy1','zyContent','zysearchCon','zyCyzbbm','zy1','zyzh','selSearch5','主病')"
                       data-notEmpty="false"/>
                <search-table :message="zysearchCon" :selected="selSearch5" :page="page1" :them="zythem"
                              @click-one="checkedOneOut"
                              @click-two="zyselectOne">
                </search-table>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">症型</span>
                <!--                    @blur="BlurData('zdxx.zyBz1','症型','zdxx.zyzf')"-->
                <input class="zui-input wh182" :disabled="saveShow"
                       @keydown="changeDown1($event,'zyBz1','zyContent','zysearchCon','zyCyzbbmBz','zyBz1','zyzf','selSearch6')"
                       @input="change1(false,'zyzf',$event.target.value,$event,'selSearch6','症型')" name="text"
                       v-model="zdxx.zyzf" data-notEmpty="false"/>
                <search-table :message="zysearchCon" :selected="selSearch6" :page="page" :them="zythem"
                              @click-one="checkedOneOut"
                              @click-two="zyselectOne">
                </search-table>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">是否传染病</span>
                <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                              :not_empty="false" :child="istrue_tran" :index="zdxx.sfcrb"
                              :val="zdxx.sfcrb" :name="'zdxx.sfcrb'">
                </select-input>
            </div>

            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 wh66 text-right font-bolder">是否发热病人</span>
                <select-input class="wh182" @change-data="resultChange" :disable="saveShow"
                              :not_empty="!csqxContent.N05001200271 ? true : false" :child="istrue_tran" :index="zdxx.sffrbr"
                              :val="zdxx.sffrbr" :name="'zdxx.sffrbr'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span class="padd-r-5 wh66 text-right">发热情况</span>
                <input @mousewheel.prevent type="text" class="zui-input wh182"
                       :disabled="saveShow" v-model="zdxx.frqk" @keydown="keyDownEnterNext($event)"
                       data-notEmpty="false" name="text"/>
            </div>

            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span  class="padd-r-5 wh66 text-right font-bolder">就医人群类型</span>
                <select-input class="wh182" :search="true" @change-data="resultChange" :disable="saveShow"
                              :not_empty="false" :child="jyrqlx_tran" :index="zdxx.mdtrtGrpType"
                              :val="zdxx.mdtrtGrpType" :name="'zdxx.mdtrtGrpType'">
                </select-input>
            </div>

            <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                <span  class="padd-r-5 wh66 text-right font-bolder">门诊急诊转诊标志</span>
                <select-input class="wh182" :search="true" @change-data="resultChange" :disable="saveShow"
                              :not_empty="false" :child="mjzzzbz_tran" :index="zdxx.otpErReflFlag"
                              :val="zdxx.otpErReflFlag" :name="'zdxx.otpErReflFlag'">
                </select-input>
            </div>


            <div class="flex-container flex-align-c wh990 padd-b-10 padd-r-10">
                <span :class="!csqxContent.N05001200271 ?'red' :''" class="padd-r-5 font-bolder  wh66 text-right">对应处理</span>
                <div class="position wh990">
                    <input autofocus class="zui-input" data-select="no" placeholder="对应处理" :disabled="saveShow"
                           name="text" id="dycl" :data-notEmpty="!csqxContent.N05001200271 ? true : false" v-model="zdxx.dycl"
                           @keydown="clchangeDown($event,'dycl','selSearch4','clsearchCon')"
                           @input="change($event,false,'dycl',$event.target.value)"/>
                    <search-table :message="clsearchCon" :selected="selSearch4" :them="clthem"
                                  :page="page" @click-one="checkedOneOut" @click-two="selectcl">
                    </search-table>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                <span class="padd-r-5 wh66 text-right">上报日期</span>
                <input class="background-h wh182 zui-input" type="text" :disabled="saveShow"
                       v-model="istrue_tran[zdxx.sfgm]"/>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                <span class="padd-r-5 wh66 text-right">是否肿瘤</span>
                <select-input class="wh182" @change-data="resultChange" :disable="saveShow" :not_empty="false"
                              :child="nh_tran"
                              :index="zdxx.sfgm" :val="zdxx.sfgm" :name="'zdxx.sfgm'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10" v-if="gsjbpxBm!=0">
                <span class="padd-r-5 wh66 text-right">新发肿瘤报告及其它</span>
                <input class=" zui-input wh182" :disabled="saveShow" type="text"/>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-r-10 wh990">
                <span class="padd-r-5 wh66 text-right">备注说明</span>
                <input class="zui-input wh990 " placeholder="请输入备注说明" :disabled="saveShow" name="text"
                       v-model="zdxx.bzsm" @keyup.13="saveShow?edit():save()" data-notEmpty="false"/>
            </div>
        </div>
    </div>

    <span class="padd-l-10 font-bolder red whiteSpace" v-if="!csqxContent.N03001200175 == false && csqxContent.N03001200175 == '1'">武汉发热患者信息采集</span>
    <div class="jbxxcontent tab-card" v-if="!csqxContent.N03001200175 == false && csqxContent.N03001200175 == '1'">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">患者症状信息</div>
        </div>
        <div class="tab-card-body padd-t-10">
            <div class=" flex-container flex-wrap-w padd-t-15 ">
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">发热(≥37.3℃)</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whfr"
                                  :val="zdxx.whfr" :name="'zdxx.whfr'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">咳嗽</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whks"
                                  :val="zdxx.whks" :name="'zdxx.whks'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">嗓子痛（咽痛）</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whszt"
                                  :val="zdxx.whszt" :name="'zdxx.whszt'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">胸闷</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whxm"
                                  :val="zdxx.whxm" :name="'zdxx.whxm'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">乏力</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whfl"
                                  :val="zdxx.whfl" :name="'zdxx.whfl'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">呼吸困难</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whhxkl"
                                  :val="zdxx.whhxkl" :name="'zdxx.whhxkl'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">恶心呕吐</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whexet"
                                  :val="zdxx.whexet" :name="'zdxx.whexet'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">腹泻</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whfx"
                                  :val="zdxx.whfx" :name="'zdxx.whfx'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">无上述症状</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whwsszz"
                                  :val="zdxx.whwsszz" :name="'zdxx.whwsszz'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">其他症状</span>
                    <input @mousewheel.prevent type="text" class="zui-input wh453"
                           :disabled="saveShow" v-model="zdxx.whqtzz" @keydown="keyDownEnterNext($event)"
                           data-notEmpty="false" name="text"/>
                </div>
            </div>
        </div>
    </div>
    <div class="jbxxcontent tab-card" v-if="!csqxContent.N03001200175 == false && csqxContent.N03001200175 == '1'">
        <div class="tab-card-header">
            <div class="tab-card-header-title font14">患者接触情况</div>
        </div>
        <div class="tab-card-body padd-t-10">
            <div class=" flex-container flex-wrap-w ">
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">是否湖北返回人员?</span>
                    <select-input @change-data="resulwhChange" class="wh50" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whsfhbfh"
                                  :val="zdxx.whsfhbfh" :name="'zdxx.whsfhbfh'">
                    </select-input>
                </div>

                <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                    <span class="padd-r-5 wh66 text-right font-bolder red">返回时间</span>
                    <div class="position flex-container">
                        <input autofocus class="zui-input wh100    "
                               placeholder="请选择时间" id="timeValfh" :disabled="saveShow || zdxx.whsfhbfh != '1'" name="text"
                               v-model="zdxx.whhbfhsj" data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
                    </div>
                </div>

            </div>
            <div class=" flex-container flex-wrap-w ">
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">过去14天内是否接触有过新型冠状病毒感染的肺炎病例?</span>
                    <select-input @change-data="resulwhChange" class="wh50" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.wh14jzfy"
                                  :val="zdxx.wh14jzfy" :name="'zdxx.wh14jzfy'">
                    </select-input>
                </div>

                <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                    <span class="padd-r-5 wh66 text-right font-bolder red">接触时间</span>
                    <div class="position flex-container">
                        <input autofocus class="zui-input wh100    "
                               placeholder="请选择时间" id="timeValfyfy" :disabled="saveShow || zdxx.wh14jzfy != '1'" name="text"
                               v-model="zdxx.wh14jzfysj" data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
                    </div>
                </div>
            </div>
            <div class=" flex-container flex-wrap-w ">

                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">过去14天内是否有过湖北或其他有病例报告社区的旅居史？</span>
                    <select-input @change-data="resulwhChange" class="wh50" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.wh14jzhbr"
                                  :val="zdxx.wh14jzhbr" :name="'zdxx.wh14jzhbr'">
                    </select-input>
                </div>

                <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                    <span class="padd-r-5 wh66 text-right font-bolder red">接触时间</span>
                    <div class="position flex-container">
                        <input autofocus class="zui-input wh100    "
                               placeholder="请选择时间" id="timeValjzs" :disabled="saveShow || zdxx.wh14jzhbr != '1'" name="text"
                               v-model="zdxx.wh14jzhbrsj" data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
                    </div>
                </div>
            </div>
            <div class=" flex-container flex-wrap-w ">
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">过去14天内是否与本辖区内有报告病例或发热、呼吸道症状人员有接触史？</span>
                    <select-input @change-data="resulwhChange" class="wh50" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.wh14sqfrjz"
                                  :val="zdxx.wh14sqfrjz" :name="'zdxx.wh14sqfrjz'">
                    </select-input>
                </div>

                <div class="flex-container flex-align-c padd-b-10 padd-r-10">
                    <span class="padd-r-5 wh66 text-right font-bolder red">接触时间</span>
                    <div class="position flex-container">
                        <input autofocus class="zui-input wh100    "
                               placeholder="请选择时间" id="timeValxqjz" :disabled="saveShow || zdxx.wh14sqfrjz != '1'" name="text"
                               v-model="zdxx.wh14sqfrjzsj" data-notEmpty="false" @keydown="keyDownEnterNext($event)"/>
                    </div>
                </div>
            </div>
            <div class=" flex-container flex-wrap-w ">
                <div class="flex-container flex-align-c padd-b-10 padd-r-10 ">
                    <span class="padd-r-5 wh66 text-right">是否聚集性发病相关</span>
                    <select-input @change-data="resultChange" class="wh182" :disable="saveShow"
                                  :not_empty="false" :child="istrue_tran" :index="zdxx.whsfjjxfb"
                                  :val="zdxx.whsfjjxfb" :name="'zdxx.whsfjjxfb'">
                    </select-input>
                </div>

            </div>
        </div>
    </div>

    <div class="brjz-foot">
        <button v-waves class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
        <button v-waves class="tong-btn btn-parmary xmzb-db" v-if="isBshow" @click="saveShow?edit():save()"
                v-text="editTitle"></button>

        <!--<button v-if="fbbm=='40'" v-waves class="tong-btn btn-parmary-d9 xmzb-db" @click="mtbzhq">病种获取</button>-->

        <button v-waves class="tong-btn  btn-parmary-f2a  xmzb-db" @click="printBl()">打印病历</button>
        <button v-waves class="tong-btn  btn-parmary-f2a  xmzb-db" @click="editModel">另存接诊模板</button>
        <button v-waves class="tong-btn  btn-parmary-f2a  xmzb-db" @click="QuoteModel">接诊模板引用</button>
        <button v-waves class="tong-btn  btn-parmary-f2a  xmzb-db" @click="historyJzxx">历史就诊引用</button>
        <button v-show="csqxContent.N05001200269 == '1'" v-waves class="tong-btn  btn-parmary-b  " @click="openPacs">调阅报告</button>
        <button v-waves class="tong-btn  btn-parmary-f2a  xmzb-db" v-if="csqxContent.N01006400112 == '1' && csqxContent.N01006400113 != '0'" @click="ygsbUrl">院感上报</button>
        <button v-waves class="tong-btn  btn-parmary-f2a  xmzb-db" v-if="csqxContent.N01006400112 == '1' && csqxContent.N01006400135" @click="crbsbUrl">传染病上报</button>

        <span v-show="fbbm =='40' || fbbm =='41'">
            病种名称：
            <template v-for="(item, $index) in mtbzlist">
                <span>{{item.jbmc}}</span>
            </template>
        </span>
    </div>
</div>


<model :s="'保存'" :c="'退出'" class="jzMbPop" @default-click="saveData" @result-clear="popShow=false"
       :model-show="true" @result-close="popShow=false" v-if="popShow" :title="'接诊模板编辑'">
    <div class="bqcydj_model contextInfo bqcydj_model_width flex-container flex-align-c flex-wrap-w">
        <div class=" padd-r-10 padd-b-10">
            <div class="flex-container flex-jus-c flex-align-c">
                <span class="padd-r-5 wh66 text-right">模板名称</span>
                <input class="zui-input wh150" placeholder="请输入医嘱名称" type="text"
                       v-model="popContent.mc" data-notEmpty="true"
                       @keydown="keyDownEnterNext($event)" data-notEmpty="false"
                       @blur="setPYDM(popContent.mc,'popContent','pydm')"/>
            </div>
        </div>
        <div class=" padd-r-10 padd-b-10">
            <div class="flex-container flex-jus-c flex-align-c">
                <span class="padd-r-5 wh66 text-right">拼音代码</span>
                <input class="zui-input wh150" placeholder="请输入模板名称" type="text"
                       v-model="popContent.pydm"
                       @keydown="keyDownEnterNext($event)" data-notEmpty="true" disabled="disabled"/>
            </div>
        </div>
        <div class="flex-container flex-jus-c flex-align-c  padd-b-10">
            <span class="padd-r-5 wh66 text-right">模板类型</span>
            <select-input class="wh150" @change-data="resultChange" :not_empty="true" :child="jzMblx_tran"
                          :index="popContent.lx"
                          :val="popContent.lx" :name="'popContent.lx'">
            </select-input>
        </div>
        <div class=" padd-r-10 padd-b-10">
            <div class="flex-container flex-jus-c flex-align-c">
                <span class="padd-r-5 wh66 text-right">主诉</span>
                <input class="zui-input wh150" placeholder="请输入主诉" type="text"
                       v-model="popContent.zs"
                       @keydown="keyDownEnterNext($event)" data-notEmpty="true"
                />
            </div>
        </div>
        <div class=" padd-r-10 padd-b-10">
            <div class="flex-container flex-jus-c flex-align-c">
                <span class="padd-r-5 wh66 text-right">现病史</span>
                <input class="zui-input wh150" placeholder="请输入现病史" type="text"
                       v-model="popContent.xbs"
                       @keydown="keyDownEnterNext($event)" data-notEmpty="true"
                />
            </div>
        </div>
        <div class="flex-container flex-jus-c flex-align-c  padd-b-10">
            <span class="padd-r-5 wh66 text-right">既往史</span>
            <input class="zui-input wh150" placeholder="请输入既往史" type="text"
                   v-model="popContent.jws"
                   @keydown="keyDownEnterNext($event)" data-notEmpty="true"
            />
        </div>
        <div class=" padd-r-10 padd-b-10">
            <div class="flex-container flex-jus-c flex-align-c">
                <span class="padd-r-5 wh66 text-right">症状体征</span>
                <input class="zui-input wh150" placeholder="请输入症状体征" type="text"
                       v-model="popContent.zyzztz"
                       @keydown="keyDownEnterNext($event)" data-notEmpty="true"
                />
            </div>
        </div>
        <div class=" padd-r-10 padd-b-10">
            <div class="flex-container flex-jus-c flex-align-c">
                <span class="padd-r-5 wh66 text-right">过敏史</span>
                <input class="zui-input wh150" placeholder="请输入过敏史" type="text"
                       v-model="popContent.gms"
                       @keydown="keyDownEnterNext($event)" data-notEmpty="true"
                />
            </div>
        </div>
        <div class="flex-container flex-jus-c flex-align-c  padd-b-10">
            <span class="padd-r-5 wh66 text-right">家族史</span>
            <input class="zui-input wh150" placeholder="请输入家族史" type="text"
                   v-model="popContent.jzs"
                   @keydown="keyDownEnterNext($event)" data-notEmpty="true"
            />
        </div>
        <div class="flex-container flex-jus-c flex-align-c  padd-b-10">
            <span class="padd-r-5 wh66 text-right">对应处理</span>
            <input class="zui-input wh150" placeholder="请输入家族史" type="text"
                   v-model="popContent.dycl"
                   @keydown="keyDownEnterNext($event)" data-notEmpty="true"
            />
        </div>

    </div>
</model>
<model :s="'保存'" :c="'退出'" class="fjzdPop" @default-click="saveData" @result-clear="fjzdShow=false"
       :model-show="true" @result-close="fjzdShow=false" v-if="fjzdShow" :title="'附加诊断'">
    <div class="zui-table-view  bqcydj_model_width hzList-border flex-container flex-dir-c">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">序号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">诊断名称</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">诊断编码</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-m">操作</div>
                    </th>

                </tr>
                <tr @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()" v-for="(item, $index) in fjzd"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                    <td>
                        <div class="zui-table-cell cell-s">
                            <span v-text="$index+1"></span>
                        </div>
                    </td>
                    <td class="">
                        <div class="zui-table-cell flex-container flex-align-c">
                            <input class="zui-input height-input wh100MAx" data-notEmpty="false" v-model="item.jbmc"
                                   @keydown="changeDown($event,$index,'jbmc','jbmb','qtzd')"
                                   @input="change(false,$index,'jbmc','jbbm',$event.target.value,'jbbm',$event)">
                            <search-table :message="searchCon" :selected="selSearch" :page="queryObj" :them="them"
                                          @click-one="checkedOneOut"
                                          @click-two="checkedTwoOut">
                            </search-table>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            {{item.jbmb}}
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-m">
                            <span class="flex-center padd-t-5">
                                            <em  class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                                        </span>
                        </div>
                    </td>
                </tr>
                </thead>
            </table>
        </div>
    </div>
    <button v-waves slot="footer" class="tong-btn btn-parmary  xmzb-db margin-r-10 sizewidth88 font-auto" @click="addFun()">新增一行</button>
</model>
<div class="side-form" :class="{'ng-hide':num==1}" id="Quote" role="form">
    <div class="fyxm-side-top">
        <span>接诊模版引用<span class="color-wtg font-weight">[双击选中模版内容]</span></span>
        <span class="fr closex ti-close" @click="num=1"></span>
    </div>
    <div class="ksys-side ksys-side-hide">
        <div class="flex-container flex-align-c padd-b-10">
            <div class="flex-container flex-align-c">
                <span class=" wh66 text-right ft-14 padd-r-5 ">搜索</span>
                <input type="text" class="zui-input wh182 " placeholder="请输入关键字" v-model="popContent.parm"
                       @keyup.13="getData()"/>
            </div>
            <div class="flex-container flex-align-c padd-l-5">
                <span class="wh66 text-right ft-14 padd-r-5">个人</span>
                <select-input class="wh70" @change-data="getCxqxChange" :not_empty="false"
                              :child="jzMblx_tran" :index="popContent.lx" :val="popContent.lx" :name="'popContent.lx'">
                </select-input>
            </div>
        </div>
        <div class="flex-container flex-one height100">
            <div class="zui-table-view ">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-s ">名称</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s ">拼音代码</div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">操作</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index),clickSetData(item)">
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.mc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pydm"></div>
                            </td>
                            <td class="cell-m" :class="item.czybm ==czybm ? '':'h-visibility'">
                                <div class="zui-table-cell cell-m flex-container flex-align-c">
                                    <i data-title="删除" class="icon-sc icon-font" @click="remove($index)"></i>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="zui-table-fixed table-fixed-r background-f">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>操作</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table zui-collapse">
                            <tbody>
                            <tr v-for="(item, $index) in jsonList"
                                :tabindex="$index"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()">
                                <td class="cell-m" :class="item.czybm ==czybm ? '':'h-visibility'">
                                    <div class="zui-table-cell cell-m flex-container flex-align-c">
                                        <i data-title="删除" class="icon-sc icon-font" @click="remove($index)"></i>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="flex-one  over-auto">
                <div class="zui-table-view " v-if="childList.length>0" style="height: auto"
                     v-for="(value, key, index) in childText">
                    <div class="zui-table-body  flex-container skin-default" data-no-change
                         @scroll="scrollTable($event)">
                        <table class="flex-one wh100MAx"
                               :class="Object.keys(childText).length -1  == index ?'zui-table':''">
                            <tbody>
                            <tr v-for="(item, $index) in childList" :tabindex="$index">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m flex-align-c flex-container flex-jus-c">
                                        <vue-checkbox @result="reCheckChild" :new-text="''" :val="''+key+''"
                                                      :new-value="checkEd[key]"></vue-checkbox>
                                    </div>
                                </td>
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s flex-align-c flex-container flex-jus-s">
                                        {{value}}
                                    </div>
                                </td>
                                <td>
                                    <div class="flex-align-c flex-container flex-align-s" style="white-space:normal"
                                         over-auto> {{item[key]}}
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="close()">取消</button>
        <button v-waves class="zui-btn table_db_esc btn-primary xmzb-db" @click="saveData()">保存</button>
    </div>

</div>
<div class="side-form" :class="{'ng-hide':num==1}" id="jzxx" role="form">
    <div class="fyxm-side-top">
        <span>历史就诊引用</span>
        <span class="fr closex ti-close" @click="num=1"></span>
    </div>
    <div class="ksys-side ksys-side-hide">
        <div class="flex-container flex-align-c padd-b-10">
            <div class="flex-container flex-align-c">
                <span class="whiteSpace ft-14 padd-r-5 ">搜索</span>
                <input type="text" class="zui-input wh182 " placeholder="请输入关键字" v-model="popContent.parm"
                       @keyup.13="getData()"/>
            </div>

        </div>
        <div class="flex-container flex-one height100">
            <div class="zui-table-view ">
                <div class="zui-table-header">
                    <table class="zui-table table-width150">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-s ">姓名</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s ">年龄</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s ">就诊日期</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s ">诊断</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex',true,$index),clickSetData(item)">
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.brnl+nldw_tran[item.nldw]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDate(item.ghrq, 'date')"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.jbmc"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="flex-one  over-auto">
                <div class="zui-table-view " v-if="childList.length>0" style="height: auto"
                     v-for="(value, key, index) in childText">
                    <div class="zui-table-body  flex-container skin-default" data-no-change
                         @scroll="scrollTable($event)">
                        <table class="flex-one wh100MAx"
                               :class="Object.keys(childText).length -1  == index ?'zui-table':''">
                            <tbody>
                            <tr v-for="(item, $index) in childList" :tabindex="$index">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m flex-align-c flex-container flex-jus-c">
                                        <vue-checkbox @result="reCheckChild" :new-text="''" :val="''+key+''"
                                                      :new-value="checkEd[key]"></vue-checkbox>
                                    </div>
                                </td>
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s flex-align-c flex-container flex-jus-s">
                                        {{value}}
                                    </div>
                                </td>
                                <td>
                                    <div class="flex-align-c flex-container flex-align-s" style="white-space:normal"
                                         over-auto> {{item[key]}}
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="close()">取消</button>
        <button v-waves class="zui-btn table_db_esc btn-primary xmzb-db" @click="saveData()">引用</button>
    </div>

</div>
<model :s="'保存'" :c="'退出'" class="crBPop" @default-click="saveData" @result-clear="popShow=false"
       :model-show="true" @result-close="popShow=false" v-if="popShow" :title="'传染病上报提示'">
    <div class="bqcydj_model contextInfo bqcydj_model_width flex-container flex-align-c flex-wrap-w">
        <div>该病人诊断为{{popContent.jbmc}}，符合传染病公共卫生疾病相关诊断，需要填写相关报卡，请点击打开上报程序填写相关报卡</div>
    </div>
</model>
</body>
<script type="text/javascript" src="brPage/brjz.js"></script>
</html>
