var ylbm = "N040030022002";
var parm = {};
var cfrow = null; // 处方行号
var ksbm = "";

// 工具条
var toolBar = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        barContent: {
            fybz: '0',
            kfbz: '1'
        },
        ksbm: null, //科室
        TjShow: false,
        search: '',
        ylkh: null,
        yfList: [],
        csqxContent: {},
        zhuangtai: {
            "0": '待发药',
            "1": '已发药',
            /*"2": '待退款',*/
            /*"3": '完成',*/
            /*"4": '待退药',*/
            '5':'待收费',
            '6':'已作废'
        },
        //打印机
        KfyPrint: "",
        KfyPrintSize: "",
        FydPrint: "",
        FydPrintSize: "",
        sytPrintSize: "",
        jsonList: [{tysl: '1'}],
        objData: [
            {type: '1', date: 1521701395382, text: 'dsdsdsdsdsd'},
            {type: '0', date: 1521701395382, text: 'dsdsdsdsdsd'},
            {type: '0', date: 1521701395382, text: 'dsdsdsdsdsd'},
            {type: '0', date: 1521701395382, text: 'dsdsdsdsdsd'},
            {type: '0', date: 1521701395382, text: 'dsdsdsdsdsd'},
        ],
        param: {
            page: 1,
            rows: 10,
            sort: 'zdy',
            order: 'asc',
            begin: null,
            end: null,
        },
        totlePage: 0,
        sytPrint: {},
        zldPrint: {},
        fybzShow: [],
        ShShow: false,
        hoverBrListIndex: undefined,
        selSearch: 1,
        fybzClass: {
            '0': 'color-dsh',
            '1': 'color-dlr',
            '2': 'color-yzf',
            '5': 'color-wtg',
        },
        tybzClass: {
            '0': '',
            '1': 'color-wtg',
            '2': 'color-wtg',
        },
        tyztArr: {
            "0": '未退药',
            "1": '部分退药',
            "2": '全部退药'
        },
        ifClick: true, // 用于判断是否点击保存按钮
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {

        this.getYfbm();
        var myDate = new Date();
        this.param.begin = this.fDate(new Date(), 'date') + ' 00:00:00';
        this.param.end = this.fDate(myDate.setDate(myDate.getDate() + 1), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.param.begin
            , theme: '#1ab394'
            , done: function (value) {
                toolBar.param.begin = value;
                toolBar.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.param.end
            , theme: '#1ab394'
            , done: function (value) {
                toolBar.param.end = value;
                toolBar.getData();
            }
        });
    },
    filters: {
        fybz: function (fybz, kfbz) {
            if (fybz == '0' && kfbz == '0') {
                return '待收费'
            } else if (fybz == '0' && kfbz == '1') {
                return '已发药'
            } else if (fybz == '0' && kfbz == '1') {

            }
        }
    },
    methods: {
        getDyCsqx: function () {
            //syt打印机
            window.top.J_tabLeft.csqxparm.csbm = "N010024006";
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0 && json.d != null && json.d != undefined && json.d.length > 0) {
                    toolBar.sytPrint = json.d[0].csz;
                    toolBar.sytPrintSize = json.d[0].cszmc;
                }
            });
            window.top.J_tabLeft.csqxparm.csbm = "N010024016";
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0 && json.d != null && json.d != undefined && json.d.length > 0) {
                    toolBar.KfyPrint = json.d[0].csz;
                    toolBar.KfyPrintSize = json.d[0].cszmc;
                }
            });

            //发药单打印机
            window.top.J_tabLeft.csqxparm.csbm = "N010024015";
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0 && json.d != null && json.d != undefined && json.d.length > 0) {
                    toolBar.FydPrint = json.d[0].csz;
                    toolBar.FydPrintSize = json.d[0].cszmc;
                }
            });

        },
        //获取参数权限
        getPdjhCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N030012001',
                "ksbm": ksbm
            };
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'CsqxAction',
                    types: 'csqx',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json) {
                if (json.body.a == 0 && json.body.d && json.body.d.length > 0) {
                    for (var i = 0; i < json.body.d.length; i++) {
                        var csjson = json.body.d[i];
                        switch (csjson.csqxbm) {
                            case "N03001200131": //排队叫号请求地址
                                if (csjson.csz) {
                                    toolBar.csqxContent.N03001200131 = csjson.csz;
                                }
                                break;
                        }
                    }

                } else {
                    malert('参数权限获取失败' + json.c, 'top', 'defeadted')
                }
            })
        },
        loadYkt: function (yes) {
            console.log(toolBar.ylkh);
            if (toolBar.ylkh.length == 8) {
                toolBar.getDataGr(toolBar.ylkh, yes);
                return;
            }
            if(wap.N04003002200211 == '1' && toolBar.ylkh.length == 12){//宣汉就诊卡
                toolBar.getDataGr(toolBar.ylkh, yes);
                return;
            }
            if(wap.N04003002200212 == '1' && toolBar.ylkh.length >= 64){//宣汉就诊卡
                toolBar.getDataGr(toolBar.ylkh, yes);
                return;
            }
            $.get("http://" + document.location.hostname + ":18888/" + toolBar.ylkh, function (kh) {
                if (kh == undefined || kh == null || kh == "0" || kh == "-1") {
                    malert("卡解密失败,请重试！", 'top', 'defeadted');
                } else {
                    //clearInterval(interval);
                    toolBar.getDataGr(kh, yes);
                }
            }).error(function (xhr, errorText, errorType) {
                malert("卡解密失败,请重试！", 'top', 'defeadted');
            });
        },
        getDataGr(kh, yes) {
            this.ylkh = kh;
            //common.openloading('.setScroll');
            if (!this.param.begin) {
                this.param.begin = getTodayDateBegin();
            }

            if (!this.param.end) {
                this.param.end = getTodayDateEnd();
            }
			var cxrq = '';
			console.log(this.barContent.fybz);
            parm = {
                yfbm: this.barContent.yfbm,
                beginrq: this.param.begin,
                endrq: this.param.end,
                ylkh: this.ylkh,
                zfbz: '0',
				cxrq:cxrq,
            };

            if (this.ylkh) {
                this.param.page = 1;
            }
            ;
            var cfh = "";
            if (cfrow) {
                cfh = this.jsonList[cfrow].cfh;
                console.log("cfh:" + this.jsonList[cfrow].cfh);
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=queryypcf&dg=" + JSON.stringify(this.param) + "&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    toolBar.totlePage = Math.ceil(json.d.total / toolBar.param.rows);
                    toolBar.jsonList = json.d.list;
                    wap.totlePage = Math.ceil(json.d.total / wap.param.rows);
                    wap.jsonList = json.d.list;
                    if (toolBar.barContent.fybz == "6"){
                        for (var i = 0;i < toolBar.jsonList.length;i++){
                            toolBar.jsonList[i].fybz  = toolBar.barContent.fybz;
                        }
                    }
                    if (yes == 1) {
                        wap.contents = "已发药";
                        wap.printshow = true;
                        wap.getData(toolBar.jsonList[cfrow].cfh);
                        toolBar.Ckdetail(cfrow, toolBar.jsonList[cfrow].fybz);
                        wap.open();

                    }
                } else {
                    malert("处方查询失败：" + json.c, 'top', 'defeadted')
                }
            });

        },
        getYfbm: function () {
            parm = {
                ylbm: ylbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    toolBar.yfList = json.d.list;
                    if (toolBar.yfList.length > 0) {
                        Vue.set(toolBar.barContent, 'yfbm', toolBar.yfList[0].yfbm);
                        ksbm = toolBar.yfList[0].ksbm;
                        //获取药房成功后查询处方数
                        toolBar.getData();
                        toolBar.getPdjhCsqx();
                        toolBar.getDyCsqx();
                    }
                } else {
                    malert("药房编码获取失败：" + json.c, 'top', 'defeadted');
                }
            });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            //先获取到操作的哪一个
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            if (val[3] != null) {
                Vue.set(this[val[2][0]], val[3], val[4]);
            }
            if (val[2][val[2].length - 1] == 'fybz' && val[0] == '0') {
                Vue.set(this[val[2][0]], val[2][val[2].length - 1], '0');
                Vue.set(this[val[2][0]], 'kfbz', '1');
            }
            toolBar.goToPage(1);
        },
        //刷新处方
        getData: function (yes) {
            common.openloading('.setScroll');
			var cxrq = '';
			if(this.barContent.fybz !='5' && this.barContent.fybz !='6' && this.barContent.fybz !='1'){
				cxrq = 'kfrq';
            }else if(this.barContent.fybz =='1'){
                cxrq = 'fyrq';
            }
            
            parm = {
                cxrq:cxrq,
                yfbm: this.barContent.yfbm,
                fybz: this.barContent.fybz,
                kfbz: this.barContent.kfbz,
                beginrq: this.param.begin,
                endrq: this.param.end,
                parm: this.search,
                zfbz: '0',
            };
            if(this.barContent.fybz =='1'){
                parm.kfbz=null;
            }
            if (this.barContent.fybz == '5'){
                parm.fybz = '0';
                parm.kfbz = '0';
            }
            if (this.barContent.fybz == '6'){
                parm.fybz = '1';
                parm.kfbz = '1';
                parm.zfbz = "1";
            }
            //如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=queryypcf&dg=" + JSON.stringify(this.param) + "&parm=" + JSON.stringify(parm), function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
				$('#cfzje').remove();
                if (json.a == 0) {
                    toolBar.totlePage = Math.ceil(json.d.total / toolBar.param.rows);
                    toolBar.jsonList = json.d.list;
                    if (toolBar.barContent.fybz == "6"){
                        for (var i = 0;i < toolBar.jsonList.length;i++){
                            toolBar.jsonList[i].fybz  = toolBar.barContent.fybz;
                        }
                    }
                    if(yes == "refresh"){
                        if(toolBar.jsonList && toolBar.jsonList.length>0){
                            toolBar.Ckdetail(0,toolBar.jsonList[0].fybz);
                        }else{
                            wap.closes();
                        }
                    }
					if(json.b && json.b>0){
						$(".page-count").before("<span id='cfzje'>共计:"+json.b+"元,</span>");
					}
                } else {
                    malert("处方查询失败：" + json.c, 'top', 'defeadted')
                }
            });
            //cfrow = null;
            common.closeLoading()
        },

        hoverPop: function (index, type) {
            this.fybzShow[index] = type ? !this.fybzShow[index] : undefined;
            this.$forceUpdate()
        },
        reslut: function (list) {
            console.log(list);
        },
        //键盘事件
        ChangUp: function (event, content) {
            console.log("keyup:keyCode");
            //窗口检索键盘操作方法  content:操作的json对象
            if (event.keyCode == 40) { //回车

            } else if (event.keyCode == 40) { //下移
                this.isChecked = [];
                if ((this[content].length - 1) == this.index) {
                    this.isChecked[0] = true;
                    this[content].index = 0;
                } else {
                    this[content].index++;
                    this.isChecked[this.index] = true;
                }
                return false;
            } else if (event.keyCode == 38) { //上移
                this.isChecked = [];
                if (this.index == 0) {
                    this[content].index = this[content].length - 1;
                    this.isChecked[this.index] = true;
                } else {
                    this[content].index--;
                    this.isChecked[this.index] = true;
                }
                return false;
            }
            return true;
        },

        //调配
        tiaopei:function(index,item){
            var obj = {
                bah:item.bah,
                cfh:item.cfh,
            };
            common.openloading('.setScroll');
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=tiaopei', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == 0) {
                        malert(data.body.c);
                        toolBar.goToPage(1);
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                    common.closeLoading();
                }, function (error) {
                    console.log(error);
                    common.closeLoading();
                });
        },
        //配药
        fayao: function (num, obj) {
            if(wap.N04003002200215=='1'){
                if(this.jsonList[num].cfshbz!='1'){
                    malert("请先完成处方审核再进行发药！","top","defeadted");
                    return;
                }
            }
            if(wap.N04003002200214 == '1'){
                if(!this.jsonList[num].tpczy){
                    malert("请先完成调配再发药！","top","defeadted");
                    return;
                }
            }
            cfrow = num;
            wap.yfyShow = true;
            wap.title = '发药';
            wap.tyShow = false;
            wap.fyShow = true;
            wap.contents = "发药";
            wap.printshow = false;
            wap.open();
            console.log(num);
            wap.popContent = this.jsonList[num];
            //检索配方
            var cfh = this.jsonList[num].cfh;
            wap.cfh = cfh;
            wap.getData(cfh);
        },
        //单击事件
        checkO: function (index) {
            cfrow = index;
        },
        //作废
        zuofei: function (num) {
            cfrow = num;
            wap.title = '作废';
            wap.contents = "作废";
            wap.tyShow = false;
            wap.printshow = false;
            wap.open();
            wap.popContent = this.jsonList[num];
            //检索配方
            var cfh = this.jsonList[num].cfh;
            wap.cfh = cfh;
            wap.getData(cfh);
        },
        //退药
        tuiyao: function (num) {
            if (wap.N04003002200213 == '0') {
                //不允许退药
                malert("参数不允许退药，请直接作废处方", 'top', 'defeadted')
                return;
            }
            wap.title = '退药';
            wap.contents = "退药";
            wap.printshow = false;
            wap.tyShow = true;
            wap.popContent = this.jsonList[num];
            var cfh = this.jsonList[num].cfh;
            wap.cfh = cfh;
            wap.getData(cfh);
            wap.open();
        },
        //双击明细
        Ckdetail: function (num, bz) {
            if(wap.N04003002200215=='1'){
                if(this.jsonList[num].cfshbz!='1'){
                    malert("请先完成处方审核再进行发药！","top","defeadted");
                    return;
                }
            }

            wap.tyShow = false;
            cfrow = num;
            toolBar.activeIndex=num;
            if (bz == '0') {
                if(wap.N04003002200214 == '1'){
                    if(!this.jsonList[num].tpczy){
                        malert("请先完成调配再发药！","top","defeadted");
                        return;
                    }
                }
                wap.contents = "发药";
                wap.printshow = false;
            } else if (bz == '1') {
                wap.contents = "已发药";
                wap.printshow = true;
            } else if (bz == '2') {
                wap.contents = "待退款";
                wap.printshow = false;
            } else if (bz == '3') {
                wap.contents = "完成";
                wap.printshow = false;
            } else if (bz == '4') {
                wap.contents = "待退药";
                wap.printshow = false;
            } else if (bz == '5') {
                wap.contents = "作废";
                wap.printshow = false;
            }
            wap.popContent = this.jsonList[num];
            //检索配方
            var cfh = this.jsonList[num].cfh;
            wap.cfh = cfh;
            wap.getData(cfh);
            wap.open();
        }
    }
});

//侧边弹框部分
var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        title: '',
        N04003002200208: '',
        N04003002200201: '', //药房处方发药是否打印处方 0-否 1-是
        contents: '',
        zfShow: false,//作废
        printshow: false,//打印
        popContent: {},
        yfyShow: false,
        fyShow: false,
        jsonList: [],
        param: {
            page: 1,
            rows: 100,
            sort: 'mxxh',
            order: 'asc'
        },
        tyShow: false,
        totlePage: 0,
        isChecked: [],
        isCheckAll: false,
        cfh: null,
        printList: [],
        bxjk: null,
        ifClick: true, // 用于判断是否点击保存按钮
    },
    updated: function () {
        changHeight()
    },
    mounted: function () {
        this.getCsqx();
    },
    methods: {
        //关闭
        closes: function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');
            toolBar.getData();
            toolBar.ylkh = null;
            $("#ylkh").focus();
            this.hoverIndex = undefined;
            this.activeIndex = undefined;
        },
        openCpt:function(item){
            var url = window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=fpdy%2Fejkf%2Fyfgl_cfj.cpt&yljgbm=' + wap.getQueryVariable('yljgbm') + '&ghxh=' + item.bah + '&cfh=' + wap.cfh;
            wap.topNewPage('处方预览',url )
        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
        },
        getData: function (cfh) {
            if (cfh == undefined) {
                return;
            }
            var types = 'queryyppf'
            if (wap.contents == '发药') {
                types = window.top.J_tabLeft.obj.sfhbxtyp == '0' ? 'queryyppf' : 'queryyppfhb'
            }
            //如果有分页请用jq的api,一般情况下避免冲突请用vue-resource,具体参照index.js的事例
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=" + types + "&dg=" + JSON.stringify(wap.param) + '&parm={"cfh":"' + cfh + '"}', function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    wap.totlePage = Math.ceil(json.d.total / wap.param.rows);
                    // json.d.list.forEach(function (item) {
                    //     item.tysl = item.cfyl;
                    // });
                    wap.jsonList = json.d.list;
                    if (wap.N04003002200209 == '1') {
                        for (var int = 0; int < wap.jsonList.length; int++) {
                            wap.jsonList[int].yypcmc = wap.jsonList[int].ldmc;
                        }
                    }
                    console.log(wap.jsonList)
                } else {
                    malert("查询失败：" + json.c, 'top', 'defeadted')
                }
            });
            if (wap.popContent.brlx == '0') {//门诊病人
                //根据处方号，查询已收费处方的费别/保险类别
                var parm_str = {
                    cfh: cfh,
                    brlx: wap.popContent.brlx
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=queryBxjkByCfh&parm=" + JSON.stringify(parm_str), function (json) {
                    if (json.a == 0) {
                        var obj = json.d;
                        if (obj) {
                            wap.bxjk = obj.bxjk;
                        } else {
                            wap.bxjk = null;
                        }
                    } else {
                        wap.bxjk = null;
                    }
                });
            } else {//住院病人
                if (wap.popContent.bxjk) {
                    wap.bxjk = wap.popContent.bxjk;
                } else {
                    wap.bxjk = null;
                }
            }
        },

        // 发药审核
        fysh: function () {
            if (!this.ifClick) {// 已经点击过就不能再点
                malert("请勿重复点击！", "top", "defeadted");
                return;
            }
            this.ifClick = false;

            if (this.contents == '配药') {
            }
            else if (this.contents == '作废') {
                if (cfrow == null || cfrow < 0) {
                    malert("请选择需作废处方!", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }
                // var json = this.jsonList[cfrow]; //处方
                cfrow = null;
                if (this.cfh == "") {
                    malert("处方号异常，不能进行作废!", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }
                if (toolBar.barContent.yfbm == "") {
                    malert("处方药房编码异常，不能进行作废!", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }
                var obj = JSON.stringify({
                    "cfh": this.cfh
                });
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCftyzf&types=cfzf', obj)
                    .then(function (data) {
                        if (data.body.a == 0) {
                            wap.getData();
                            wap.isChecked = [];
                            malert("处方作废保存成功", 'top', 'success');
                            if (toolBar.ylkh != null) {
                                toolBar.loadYkt(1);
                            } else {
								toolBar.getData();
                                wap.closes();
                            }
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                        wap.ifClick = true;
                    }, function (error) {
                        console.log(error);
                    });
            }
            else if (this.contents == '发药') {
                if (!this.cfh) {
                    malert("处方号异常，不能进行发药!", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }
                if (!toolBar.barContent.yfbm) {
                    malert("处方药房编码异常，不能进行发药!", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }
                var obj = JSON.stringify({
                    "cfh": this.cfh
                });
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCffy&types=cffy&ksbm=' + ksbm, obj)
                    .then(function (data) {
                        if (data.body.a == 0) {
                            toolBar.isChecked = [];
                            if (wap.N04003002200201 == '1') {
                                wap.printCf(); //1  打印处方
                            } else if (wap.N04003002200201 == '2') {

                                var kfyType = 0;
                                var zldType = 0;
                                for (var i = 0; i < wap.jsonList.length; i++) {
                                    if (wap.jsonList[i].zxdlx == '1' || wap.jsonList[i].zxdlx == '0') {
                                        kfyType = 1;
                                    }
                                    if (wap.jsonList[i].zxdlx == '2' || wap.jsonList[i].zxdlx == '3') {
                                        zldType = 1;
                                    }
                                }
                                if (kfyType == 1) {
									wap.printKfy();
                                    // if (mconfirm('是否打印口服!')) {
                                    //     wap.printKfy();
                                    // } else {
                                    //     if (toolBar.ylkh != null) {
                                    //         toolBar.loadYkt(1);
                                    //     } else {
                                    //         wap.closes();
                                    //     }
                                    // }
                                } else {
                                    if (toolBar.ylkh != null) {
                                        toolBar.loadYkt(1);
                                    } else {
										toolBar.getData();
                                        wap.closes();
                                    }
                                }
                                if (zldType == 1) {
                                    setTimeout(function () {
										wap.printSykOrZld('zld');
                                        // if (mconfirm('是否打印治疗单!')) {
                                        //     wap.printSykOrZld('zld');
                                        // } else {
                                        //     if (toolBar.ylkh != null) {
                                        //         toolBar.loadYkt(1);
                                        //     } else {
                                        //         wap.closes();
                                        //     }
                                        // }
                                    }, 1000)
                                } else {
                                    if (toolBar.ylkh != null) {
                                        toolBar.loadYkt(1);
                                    } else {
										toolBar.getData();
                                        wap.closes();
                                    }
                                }
                            }else{
                                wap.printshow = false;
                                toolBar.getData("refresh");
                            }
                            malert("发药成功", 'top', 'success');
                            wap.printshow = true;
                            // @yqq 向叫号系统推送最新数据
                            if (toolBar.csqxContent.N03001200131) {
                                this.getAjax(toolBar.csqxContent.N03001200131 + '/pushData/YFBM' + toolBar.barContent.yfbm, function () {
                                    console.log("数据发送成功");
                                })
                            }
                            var orderNumbers={
                                orderNumbers:[wap.cfh]
                            }
                            toolBar.$http.post(window.top.J_tabLeft.obj.spdUrl+'/spd-api/spd/dispensing-confirmation',JSON.stringify(orderNumbers)).then(function (data) {
                                if (data.body.a == 0) {

                                }
                            })
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                        wap.ifClick = true;
                    }, function (error) {
                        console.log(error);
                        wap.ifClick = true;
                    });
            } else if (this.contents == '退药') {
                if (wap.N04003002200213 == '0') {
                    //不允许退药
                    malert("参数不允许退药，请直接作废处方", 'top', 'defeadted')
                    this.ifClick = true;
                    return;
                }

                //退药判断是否是医保病人，如果是，则不能退药，走作废处方流程
                if (this.bxjk != 'null' && this.bxjk) {
                    malert("此处方是保险缴费，勿退药，请直接作废处方！", "top", "defeadted");
                    this.ifClick = true;
                    return;
                }
                //退药配方LIST
                if (this.jsonList.length <= 0) {
                    malert("请选择配方明细!", 'top', 'defeadted');
                    this.ifClick = true;
                    return;
                }
                var objlist = [];
                for (var i = 0; i < this.jsonList.length; i++) {
                    if (this.jsonList[i].tysl != null && this.jsonList[i].tysl > 0) {
                        objlist.push(this.jsonList[i]);
                    }
                }
                if (objlist == null || objlist.length < 1) {
                    malert("请填写退药数量！", 'top', 'defeadted');
                    this.ifClick = true;
                    return
                }
                //循环处理退药明细
                var obj = objlist;
                var jsons = [],drugCodes=[];
                for (var i = 0; i < obj.length; i++) {
                    //先判断数据有效性
                    var tysl = obj[i].tysl;
                    var cfyl = obj[i].cfyl;
                    var ytsl = obj[i].ytsl;
                    console.log("tysl:" + tysl + "|cfyl:" + cfyl + "|ytsl:" + ytsl);
                    if (tysl > (cfyl - ytsl)) {
                        malert("处方用量[" + cfyl + "]已退数量[" + ytsl + "]最大可退数量[" + (cfyl - ytsl) + "]", 'top', 'defeadted');
                    }
                    var json = {};
                    json.cfh = obj[i].cfh;
                    json.ypbm = obj[i].ypbm;
                    json.mxxh = obj[i].mxxh;
                    json.tysl = obj[i].tysl;
                    json.xtph = obj[i].xtph;
                    drugCodes.push(obj[i].ypbm);
                    jsons.push(json);
                }
                var list = JSON.stringify({
                    "list": jsons
                });
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCftyzf&types=cfty', list)
                    .then(function (data) {
                        if (data.body.a == 0) {
                            toolBar.isChecked = [];
                            malert("处方退药保存成功,处方号:" + data.body.d.cfh, 'top', 'success');
                            if (toolBar.ylkh != null) {
                                toolBar.loadYkt(1);
                            } else {
                                wap.closes();
                            }
                            var orderNumbers={
                                patientsType:'1',
                                orderNumbers:[data.body.d.cfh],
                                drugCodes:drugCodes
                            }
                            toolBar.$http.post(window.top.J_tabLeft.obj.spdUrl+'/spd-api/spd/return-medicine',JSON.stringify(orderNumbers)).then(function (data) {
                                if (data.body.a == 0) {

                                }
                            })
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                        wap.ifClick = true;
                    }, function (error) {
                        console.log(error);
                        wap.ifClick = true;
                    });

            }
        },
        // @yqq 打印输液卡或者治疗单
        printSykOrZld: function (type) {
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            if (type == 'syk') {
                // zhcx.getSy();    // 暂不用， 改为调帆软打印
                reportlets = "[{reportlet: 'fpdy" + frpath + "ejkf" + frpath + "yfgl_syt.cpt',yljgbm:'" + jgbm + "',cfh:'" + wap.cfh + "'}]";
                if (FrPrint(reportlets,toolBar.sytPrint, toolBar.sytPrintSize)) {
                    return;
                }
            }
            if (type == 'zld') {
                reportlets = "[{reportlet: 'fpdy" + frpath + "ejkf" + frpath + "yfgl_zld.cpt',yljgbm:'" + jgbm + "',cfh:'" + wap.cfh + "'}]";
                if (toolBar.ylkh != null) {
                    toolBar.loadYkt(1);
                } else {
                    wap.closes();
                }
                if (FrPrint(reportlets, toolBar.FydPrint, toolBar.FydPrintSize)) {
                    return;
                }
            }
        },
        printKfy:function (){
            reportlets = "[{reportlet: 'fpdy%2Fejkf%2Fyfgl_kfy.cpt',cfh:'" + wap.cfh + "'}]";
            if (toolBar.ylkh != null) {
                toolBar.loadYkt(1);
            } else {
                wap.closes();
            }
            if (FrPrint(reportlets, toolBar.KfyPrint, toolBar.KfyPrintSize)) {
                return;
            }
        },
        printQd: function () {
            reportlets = "[{reportlet: 'fpdy%2Fejkf%2Fyfgl_ypxsqd.cpt',cfh:'" + wap.cfh + "'}]";
            if (FrPrint(reportlets, toolBar.KfyPrint, toolBar.KfyPrintSize)) {
                return;
            }
        },
        //打印处方票据
        printCf: function (item) {
            var cfh = wap.cfh;
            var cflb = wap.popContent.cflb;
            if (!cfh) {
                malert("当前没有处方可以打印", 'top', 'defeadted');
                return false;
            }
            var parm = {
                cfh: cfh
            };
            console.log(wap.popContent);
            //帆软打印
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            var reportlets = null;
            if (cflb == "2") {//中药
                reportlets = "[{reportlet: 'fpdy" + frpath + "ejkf" + frpath + "yfgl_zycfj%28mz%29zb.cpt',yljgbm:'" + jgbm + "',cfh:'" + cfh + "'}]";
            } else {
                reportlets = "[{reportlet: 'fpdy" + frpath + "ejkf" + frpath + "yfgl_cfj.cpt',yljgbm:'" + jgbm + "',cfh:'" + cfh + "'}]";
            }
            if (FrPrint(reportlets)) {
                return;
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=printCf&parm=" + JSON.stringify(parm), function (json) {
                if (json.d != null) {
                    wap.printList = json.d.list;
                    // 性别转换
                    wap.printList[0]['brxb'] = wap.brxb_tran[wap.printList[0]['brxb']];
                    // 年龄转换
                    wap.printList[0]['nldw'] = wap.nldw_tran[wap.printList[0]['nldw']];
                    //处方类型名称
                    wap.printList[0]['cflxmc'] = json.d.list[0].cflxmc;
                    // 增加其他诊断
                    if (wap.printList[0]['qtzdmc'] != null) {
                        wap.printList[0]['lczd'] = wap.printList[0]['lczd'] + ',' + wap.printList[0]['qtzdmc']
                    }
                    // table.printData[0]['cflb']=='3'
                    if (wap.printList[0]['cflb'] == '1') {// 西医
                        var num = Math.ceil(wap.printList[0]['yppfList'].length / 5);
                        for (var i = 0; i < num; i++) {
                            var cfList = [];
                            for (var j = 0; j < 5; j++) {
                                if (wap.printList[0]['yppfList'][i * 5 + j] != null) {
                                    cfList.push(wap.printList[0]['yppfList'][i * 5 + j]);
                                }
                            }
                            wap.print(cfList);
                        }
                    } else {// 中药
                        num = Math.ceil(wap.printList[0]['yppfList'].length / 20);
                        for (var a = 0; a < num; a++) {
                            cfList = [];
                            for (var b = 0; b < 20; b++) {
                                if (wap.printList[0]['yppfList'][a * 20 + b] != null) {
                                    cfList.push(wap.printList[0]['yppfList'][a * 20 + b]);
                                }
                            }
                            wap.doPrintZycf(cfList);
                        }
                    }
                }
            });
        },

        //处方笺
        print: function (cfList) {
            // 查询打印模板
            var json = {repname: '处方笺'};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                if (json.d.length == 0) {
                    json.d[0] = printTemplets.getTempletByName('处方笺');
                }
                // 清除打印区域
                wap.clearArea(json.d[0]);
                // 绘制模板的canvas
                wap.drawList = JSON.parse(json.d[0]['canvas']);
                wap.creatCanvas();
                wap.reDraw();
                // 为打印前生成数据
                wap.printContent(wap.printList[0]);
                wap.printTrend(cfList);
                // 开始打印
                window.print();
            });
        },
        // 中药调用打印
        doPrintZycf: function (zycfList) {
            // 查询打印模板
            var json = {repname: '中药处方笺'};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                wap.clearArea(json.d[0]);
                // 绘制模板的canvas
                wap.drawList = JSON.parse(json.d[0]['canvas']);
                wap.creatCanvas();
                wap.reDraw();
                // 为打印前生成数据
                wap.printContent(wap.printList[0]);
                wap.printTrend(zycfList);
                // 开始打印
                window.print();
            });
        },
        //查询用例科室参数权限
        getCsqx: function () {
            parm = {
                "ylbm": ylbm,
                "ksbm": '0030'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d.length > 0) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N04003002200208": //是否打印输液卡治疗卡
                                if (csjson.csz) {
                                    wap.N04003002200208 = csjson.csz;
                                }
                                break;
                            case "N04003002200201": //药房处方发药是否打印处方
                                if (csjson.csz) {
                                    wap.N04003002200201 = csjson.csz;
                                }
                                break;
                            case "N04003002200209": // 是否使用频次简码
                                if (csjson.csz) {
                                    wap.N04003002200209 = csjson.csz;
                                }
                                break;
                            case "N04003002200210": // 医保退药强制走作废处方流程 0 否 1 是
                                if (csjson.csz) {
                                    wap.N04003002200210 = csjson.csz;
                                }
                                break;
                            case "N04003002200211": // 药房使用就诊卡（宣汉）0-否 1-是
                                if (csjson.csz) {
                                    wap.N04003002200211 = csjson.csz;
                                }
                                break;
                            case "N04003002200212": // 药房使用健康卡（宣汉）0-否 1-是
                                if (csjson.csz) {
                                    wap.N04003002200212 = csjson.csz;
                                }
                                break;
                            case "N04003002200213": //是否允许退药 0-不允许 1-允许
                                if (csjson.csz) {
                                    wap.N04003002200213 = csjson.csz;
                                }
                                break;
                            case "N04003002200214": //是否使用发药前调配 0-否 1-是
                                if (csjson.csz) {
                                    toolBar.csqxContent.N04003002200214 = csjson.csz;
                                    wap.N04003002200214 = csjson.csz;
                                }
                                break;
                            case "N04003002200215": //发药是否判断需要先审核 0-否 1-是
                                if (csjson.csz) {
                                    toolBar.csqxContent.N04003002200215 = csjson.csz;
                                    wap.N04003002200215 = csjson.csz;
                                }
                                break;
                        }
                    }
                } else {
                    malert("科室权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });
        },

    }
});
$(document).keydown(function (event) {
    // 下键
    if (event.keyCode == 40) {
        if(cfrow!=null){
            ++cfrow
            if(cfrow == toolBar.jsonList.length){
                cfrow=0;
            }
            toolBar.activeIndex=cfrow;
            toolBar.fayao(cfrow)
        }
    } else if (event.keyCode == 38) {
        if(cfrow!=null){
            --cfrow
            if(cfrow == toolBar.jsonList.length){
                cfrow=0;
            }
            toolBar.activeIndex=cfrow;
            toolBar.fayao(cfrow)
        }
    }
    else if (event.keyCode == 13 && !event.target.dataset.enter) {
        wap.fysh()
    }
});
