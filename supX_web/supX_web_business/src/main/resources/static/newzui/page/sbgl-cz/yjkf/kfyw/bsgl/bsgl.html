<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>报损管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="bsgl.css" rel="stylesheet"/>
</head>

<body class="skin-default">
<div class="background-box">
    <div class="wrapper" id="wrapper" >
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="topNewPageFun()">开单</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label ">库房</label>
                    	<div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="kfList" :index="'sbkfmc'" :index_val="'sbkfbm'" :val="param.sbkf"
                                      :name="'param.sbkf'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    	</div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">时间段</label>
                        <div class="zui-input-inline margin-f-l10 flex-container flex-align-c">
                            <i class="icon-position icon-rl"></i>
                            <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh200 " placeholder="请选择申请结束日期" id="timeVal1" />
                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline margin-f-l25">
                            <input class="zui-input wh180" @keyup.13="goToPage(1)" placeholder="请输入关键字" type="text" v-model="param.pram"/>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view  " v-cloak  >
            <!--入库列表-->
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>报损单号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>出库方式</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>制单日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body ">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList" :tabindex="$index">
                        <td class=" cell-m">
                            <div class="zui-table-cell" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.ckdh">报损单号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="ckfs[item.ckfs]">出库方式</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zdrmc">制单员</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,datetime)">制单日期</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bzms">备注</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                            	 <i v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-wtg' : item.shzfbz=='3' ? 'color-yzf':'' "></i>
                            </div>
                        </td>
                        <td class=" cell-l">
                             <div class="zui-table-cell cell-l">
                                <span class="flex-center padd-t-5">
                                <em class="width30" v-if="item.shzfbz == 0">
                                    <i class="icon-sh" data-title="审核"  @click="sh($index)"></i>
                                </em>
                                <em  class="width30" v-if="item.shzfbz ==0" >
                                    <i class="icon-js" data-title="作废" @click="Refuse($index)"></i></em>
                                </em>
                                <em class="width30" v-if="item.shzfbz != 0">
                                    <i class="icon-yl" data-title="明细"  @click="sh($index)"></i>
                                </em>
                               </span>
                            </div>
                        </td>
                 		<!--暂无数据提示,绑数据放开-->
                        <p v-show="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>

<script src="bsgl.js"></script>
</body>

</html>
