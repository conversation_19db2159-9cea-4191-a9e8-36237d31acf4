    var qjindex = '';
    //工具栏
    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            jsonList: [],
            zhyzbm: null,
            zhyzmc: null,
            yfbm: null,
            cflxbm: null,
            num:0,
            totlePage:'',
            param: {
                zhyzbm:this.zhyzbm,
                parm:'',
                page: '',
                rows: 100,
            }
        },
        mounted:function(){
            this.moun();
            window.addEventListener('storage',function (e) {
                if(e.key=="ypzhcf" && e.oldValue !== e.newValue){
                    wrapper.moun();
                }
            })
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增组合医嘱药品明细';
                wap.open();
                wap.popContent={};
                wap.popContent.zhyzbm=this.zhyzbm;
                wap.popContent.zhyzmc=this.zhyzmc;
                wap.jsSaveOrEdit=0;
            },

            moun: function () {
                var param = JSON.parse(sessionStorage.getItem('ypzhcf'));
                this.zhyzbm=param.zhyzbm;
                this.zhyzmc=param.zhyzmc;
                this.yfbm = param.yfbm;
                this.cflxbm = param.cflxbm;
                this.getData();
            },
            //查询
            getData:function () {
                this.jsonList = [];
                this.param.zhyzbm = this.zhyzbm;

                $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhypyz&types=query&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.d != null) {
                        wrapper.jsonList = json.d.list;
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    }
                });
            },
            //
            //删除
            remove: function (index) {
                var zhypyz = {}, zhypyzList = [];
                if(index != undefined || index != null){
                    zhypyz.zhyzbm = this.jsonList[index].zhyzbm;
                    zhypyz.zhypyzxh = this.jsonList[index].zhypyzxh;
                    zhypyzList.push(zhypyz);
                }else{
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            zhypyz.zhyzbm = this.jsonList[i].zhyzbm;
                            zhypyz.zhypyzxh = this.jsonList[i].zhypyzxh;
                            zhypyzList.push(JSON.parse(JSON.stringify(zhypyz)));
                        }
                    }
                }
                if (zhypyzList.length == 0) {
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }

                if (common.openConfirm("确认删除该条信息吗？", function () {
                    var json = '{"list":' + JSON.stringify(zhypyzList) + '}';
                    wrapper.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhypyz&types=delete', json).then(function (data) {
                        if (data.body.a == 0) {
                            wrapper.getData();
                            wrapper.isChecked=[];
                            malert("删除成功",'right')
                        } else {
                            malert("删除失败",'right','defeadted')
                        }
                    }, function (error) {
                        console.log(error);
                    });
                })) {
                    return false;
                }
            },
            //赋值修改
            edit: function (num) {
                if (num == null) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if (num == null) {
                        malert("请选中你要修改的数据",'top','defeadted');
                        return false;
                    }
                }
                wap.jsSaveOrEdit=1;
                wap.open();
                wap.title='编辑药品';
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                wap.ypContent.text = wap.popContent.ypmc;
            },
        }
    });

    //弹出框添加组合药品明细
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
        	jsSaveOrEdit: null, //判断是新增还是
            isShowpopL:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            pcList: [],//频次下拉框
            ksbmList: [],//科室集合
            rybmList: [],//人员集合
            cflxList: [],//处方类型集合
            yfList: [],  //药房集合
            yyffList:[],
            ypContent: {},
            searchCon: [],
            selSearch: -1,
            page: {
                page: 1,
                rows: 10,
                total: null
            },
            centent:'',
            isFold: false,
            title:'',
            ifClick:true,
            num:0,
            csContent: {},
            jsonList: [],
            popContent: {},
            them: {
                '统筹类别': 'ybtclbmc',
                '农保类别': 'nbtclbmc',
                '有效期至': 'yxqz',
                '药品名称': 'ypmc',
                '药品规格': 'ypgg',
                '库存数量': 'kcsl',
                '实际库存': 'sjkc',
                '拼音代码 ': 'pydm',

            }
        },
        mounted:function(){
            this.pcSelect();
            this.yyffSelect();
        },
        methods: {
            //关闭
            closes: function () {
            	wap.jsSaveOrEdit=null;
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },
            //频次下拉框
            pcSelect: function () {
                this.param.rows = 20000;
                this.param.sort = '';
                this.param.order = '';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yzpc&dg=" + JSON.stringify(this.param), function (json) {
                    wap.pcList = json.d.list;
                });
            },

            //用药方法下拉框
            yyffSelect: function () {
                this.param.rows = 20000;
                this.param.sort = '';
                this.param.order = '';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yyff&dg=" + JSON.stringify(this.param), function (json) {
                    wap.yyffList = json.d.list;
                });
            },
            searching: function (add, type,val) {             // 搜索调用API的方法，add为true就表示请求下一页、为null就为请求第一页
                this.ypContent[type]=val;
                if (!add) this.page.page = 1;       // 设置当前页号为第一页
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.page.parm = this.ypContent[type];
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                var json = {
                    yfbm: wrapper.yfbm,
                    cflx: wrapper.cflxbm
                }
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkcNoYf&dg=" +
                    JSON.stringify(str_param) + "&json=" + JSON.stringify(json), function (json) {
                    if (json.a == 0) {
                        var date = null;
                        if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                            for (var i = 0; i < json.d.list.length; i++) {
                                json.d.list[i].yxqz = wap.fDate(json.d.list[i].yxqz, "date");
                                wap.searchCon.push(json.d.list[i]);
                            }
                        } else {
                            for (var i = 0; i < json.d.list.length; i++) {
                                json.d.list[i].yxqz = wap.fDate(json.d.list[i].yxqz, "date");
                            }
                            wap.searchCon = json.d.list;
                        }

                        wap.page.total = json.d.total;
                        wap.selSearch = 0;
                        if (json.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c,'top','defeadted');
                    }
                })
            },
            selectOne: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.searching(true, 'text',this.ypContent['text']);           // 传参表示请求下一页,不传就表示请求第一页
                } else {   // 否则就是选中事件,为json赋值
                    this.ypContent = item;
                    Vue.set(this.ypContent, 'text', this.ypContent['ypmc']);
                    wap.popContent.ypbm = this.ypContent.ypbm;
                    wap.popContent.jldw = this.ypContent.jldw;
                    wap.popContent.jldwmc = this.ypContent.jldwmc;
                    Vue.set(wap.popContent, 'yyff', this.ypContent.yyff);
                    $(".selectGroup").hide();

                }
            },
            changeDown: function (event, type) {
                if (this['searchCon'][this.selSearch] == undefined) return;
                this.keyCodeFunction(event, 'ypContent', 'searchCon');
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (type == "text") {
                        Vue.set(this.ypContent, 'text', this.ypContent['ypmc']);
                        wap.popContent.ypbm = this.ypContent.ypbm;
                        wap.popContent.jldw = this.ypContent.jldw;
                        wap.popContent.jldwmc = this.ypContent.jldwmc;
                        Vue.set(wap.popContent, 'yyff', this.ypContent.yyff);
                        this.nextFocus(event);
                    }
                }
            },
            //添加
            save:function () {
				if (!wap.ifClick) return;
                wap.ifClick = false;
                if (wap.popContent['ypbm'] == undefined || wap.popContent['ypbm'] == null || wap.popContent['ypbm'] == '') {
                    malert("药品不能为空",'top','defeadted');
                    wap.ifClick = true;
                    return false;
                }
                if (wap.popContent['dcjl'] <= 0) {
                    malert("单次计量不能小于0",'top','defeadted');
                    wap.ifClick = true;
                    return false;
                }
                if (wap.popContent['yyzl'] <= 0) {
                    malert("用药总量不能小于0",'top','defeadted');
                    wap.ifClick = true;
                    return false;
                }
                if (wap.popContent['sysd']!=null && wap.popContent['sysd']!="" && wap.popContent['sysd'] <= 0) {
                    malert("输液速度不能小于0",'top','defeadted');
                    wap.ifClick = true;
                    return false;
                }

                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhypyz&types=save',
                    JSON.stringify(wap.popContent))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("数据更新成功");
                            wap.ifClick = true;
                            wap.popContent = {};
                            wap.ypContent = {};
                            wrapper.getData();
                            $("#ypmc").focus();
                            wap.popContent['zhyzbm'] = wrapper.zhyzbm;
                			wap.popContent['zhyzmc'] = wrapper.zhyzmc;
                			if(wap.jsSaveOrEdit==1){
                				wap.closes();
                			}
                			wap.jsSaveOrEdit=null;
                        } else {
                            malert(data.body.c + "数据失败",'top','defeadted');
                            wap.ifClick = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
            },
        }
    });
    //监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
    $(document).mouseup(function (e) {
        var bol = $(e.target).parents().is(".selectGroup");
        if (!bol) {
            $(".selectGroup").hide();
        }
    });






