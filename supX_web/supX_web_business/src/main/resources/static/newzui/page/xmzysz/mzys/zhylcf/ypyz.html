<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药品组合医嘱</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="zhgl.css" rel="stylesheet">
</head>
<body class="skin-default padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper background-f" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button v-waves class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addData">新增</button>
            <button v-waves class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            <button v-waves class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="remove() ">删除</button>
        </div>
    </div>
    <div class="zui-table-view hzList  ">
            <div class="zui-table-header">
                <table class="zui-table" >
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                              :type="'all'" :val="isCheckAll">
                        </input-checkbox></div></th>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left"><span>组合医嘱</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left"><span>明细诊疗项目</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>分组号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>单价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>频次</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>医生说明</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :key="item.ksbm"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" @dblclick="edit($index)">
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td ><div class="zui-table-cell cell-xl text-left" v-text="item.zhyzmc"></div></td>
                        <td><div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.mxzlxmmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.fzh"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.sl"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.dj"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.pcmc"></div></td>
                        <td ><div class="zui-table-cell cell-s" v-text="item.ysms"></div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s">
                            <span  class="flex-center padd-t-5">
                            <em class="width30"><i class="icon-bj" @click="edit($index)" data-title="编辑"></i></em>
                            <em class="width30"><i class="icon-sc icon-font" data-title="删除" @click="remove(item)"></i></em>
                           </span>
                        </div></td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>

</div>
<div class="side-form  pop-548" :class="{'ng-hide':index==1}"   id="brzcList" role="form" v-cloak>
    <div class="tab-message">
        <a>{{title}}</a>
        <a href="javascript:" class="fr closex ti-close" @click="closes"></a>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li><i>组合医嘱</i>
                <input type="text" class="zui-input" autocomplete="off" v-model="popContent.zhyzmc" disabled="disabled"/></li>
            <li><i>诊疗项目</i>
                <input data-notEmpty="false" autocomplete="off" class="zui-input" v-model="zlContent.text" @keydown="changeDown($event,'text')"
                       @input="searching(false,'text',$event.target.value)">
                <search-table :message="searchCon" :selected="selSearch"
                              :them="them" page="page"
                              @click-one="checkedOneOut" @click-two="selectOne">
                </search-table></li>
            <li><i>分组号</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="fzh_tran" :index="popContent.fzh" :val="popContent.fzh"
                              :name="'popContent.fzh'" :search="true">
                </select-input></li>
            <li><i>数量</i>
                <input type="number" autocomplete="off" @mousewheel.prevent class="zui-input" v-model="popContent.sl" @keydown="nextFocus($event)" data-notEmpty="false"></li>
            <li><i>单价</i>
                <input class="zui-input" autocomplete="off" type="number" @mousewheel.prevent v-model="popContent.dj" @keydown="nextFocus($event)" data-notEmpty="false"></li>
            <li><i>频次</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="pcList" :index="'pcmc'" :index_val="'pcbm'" :val="popContent.pcbm"
                              :name="'popContent.pcbm'" :search="true">
                </select-input></li>
            <li><i>医生说明</i>
                <input class="zui-input" autocomplete="off" v-model="popContent.ysms" @keydown.13="saveData"></li>
        </ul>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveData">确定</button>
    </div>
</div>

<div id="pop">
</div>
<script type="text/javascript" src="ypyz.js"></script>
</body>
</html>
