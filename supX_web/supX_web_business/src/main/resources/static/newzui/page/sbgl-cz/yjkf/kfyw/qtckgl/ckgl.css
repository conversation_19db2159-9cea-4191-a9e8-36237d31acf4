
.icon-bj {
  margin: 0 !important;
}


.tong-padded {
  padding: 20px 10px 10px;
}

.tong-search .zui-form .padd-l-40 {
  padding-left: 40px !important;
}
.rkgl-kd {
  width: 100%;
  padding: 5px 0 0;
  box-sizing: border-box;
  color: #333333;
  font-size: 14px;
}
.rkgl-kd span {
  padding-left: 27px;
}
.tab-edit-list .inner li {
  width: 100% !important;
  margin-bottom: 0 !important;
}
.ksys-side .zui-select-inline {
  margin-right: 0px !important;
  width: 100% !important;
}
.tab-edit-list {
  padding: 5px;
}
.rkgl-position {
  position: fixed;
  bottom: 10px;
  left: 10px;
  right: 10px;
  width: auto;
  z-index: 11;
  height: 70px;
  background: #fff;
  align-items: center;
  color: #81878e;
  border-top: 1px solid #eee;
}

/**/
.ly-left {
  width: 325px;
  float: left;
}
.ly-left .ly-left-top {
  width: 100%;
  background: #edf2f1;
  border: 1px solid #e9eee6;
  height: 38px;
}
.ly-left .ly-left-top i {
  width: auto;
  display: block;
  line-height: 38px;
  float: left;
  text-align: center;
}
.ly-left .ly-left-top i:nth-child(1) {
  width: 30px;
}
.ly-left .ly-left-top i:nth-child(2) {
  width: 22%;
}
.ly-left .ly-left-top i:nth-child(3) {
  width: 43%;
}
.ly-left .ly-left-top i:nth-child(4) {
  width: 23%;
}
.ly-left .ly-left-content {
  width: 100%;
}
.ly-left .ly-left-content li {
  width: 100%;
  border: 1px solid #e9eee6;
  border-top: none;
  line-height: 40px;
  overflow: hidden;
  cursor: pointer;
}
.ly-left .ly-left-content li i {
  width: auto;
  display: block;
  line-height: 38px;
  float: left;
  text-align: center;
}
.ly-left .ly-left-content li i:nth-child(1) {
  width: 30px;
}
.ly-left .ly-left-content li i:nth-child(2) {
  width: 22%;
}
.ly-left .ly-left-content li i:nth-child(3) {
  width: 43%;
}
.ly-left .ly-left-content li i:nth-child(4) {
  width: 23%;
}
.ly-left .ly-left-content li:nth-child(2n) {
  background: #fdfdfd;
}
.ly-left .ly-left-content li:hover {
  background: rgba(26, 188, 156, 0.06);
}
.ly-right {
  width: 454px;
  float: right;
}
.ly-right .ly-right-top {
  width: 100%;
  background: #edf2f1;
  border: 1px solid #e9eee6;
  height: 38px;
}
.ly-right .ly-right-top i {
  width: auto;
  display: block;
  line-height: 38px;
  float: left;
  text-align: center;
  overflow: hidden;
}
.ly-right .ly-right-top i:nth-child(1) {
  width: 50px;
}
.ly-right .ly-right-top i:nth-child(2) {
  width: 37%;
}
.ly-right .ly-right-top i:nth-child(3) {
  width: 17%;
}
.ly-right .ly-right-top i:nth-child(4) {
  width: 17%;
}
.ly-right .ly-right-top i:nth-child(5) {
  width: 17%;
}
.ly-right .ly-right-content {
  width: 100%;
}
.ly-right .ly-right-content li {
  width: 100%;
  border: 1px solid #e9eee6;
  border-top: none;
  line-height: 40px;
  overflow: hidden;
  cursor: pointer;
}
.ly-right .ly-right-content li i {
  width: auto;
  display: block;
  line-height: 38px;
  float: left;
  text-align: center;
  overflow: hidden;
}
.ly-right .ly-right-content li i:nth-child(1) {
  width: 50px;
}
.ly-right .ly-right-content li i:nth-child(2) {
  width: 37%;
}
.ly-right .ly-right-content li i:nth-child(3) {
  width: 17%;
}
.ly-right .ly-right-content li i:nth-child(4) {
  width: 17%;
}
.ly-right .ly-right-content li i:nth-child(5) {
  width: 17%;
}
.ly-right .ly-right-content li:nth-child(2n) {
  background: #fdfdfd;
}
.ly-right .ly-right-content li:hover {
  background: rgba(26, 188, 156, 0.06);
}
.tem {
  position: relative;
  float: left;
  /*width: 300px;*/
  /*height: 450px;*/
  width: 800px;
  height: 500px;
  border: 1px solid green;
  margin-left: 20px;
  margin-top: 20px;
}

.item {
  position: absolute;
  display: inline-block;
  top: 10px;
  margin-bottom: 20px;
  font-size: 14px;
  cursor: default;
  z-index: 100;
}

.item span {
  float: left;
}

.item input {
  float: left;
  width: 80px;
  border: 1px solid #aaaaaa;
  padding: 4px;
}

.item div {
  float: left;
  width: 100px;
  height: 20px;
  border: 0;
  border-bottom: 1px solid #000000;
}
.temTable{
  position: absolute;
  display: inline-block;
}

.printTable{
  border: 0;
  border-collapse: collapse;
}

.printTable td{
  border: 1px solid #444444;
  width: 30px;
  height: 30px;
}
.zui-table-body table .background-red,.zui-table-body table .background-red:hover{
  background: #ff0000c7 !important;
}
