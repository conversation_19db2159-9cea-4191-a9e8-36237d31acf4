var toolBar = new Vue({
        el: '#wrapper',
        mixins: [dic_transform, tableBase, baseFunc, checkData, mformat, printer],
        data: {
            IsPrint_tran: {
                '-1': '全部',
                '0': '未打印',
                '1': '已打印',
            },
            openJson:'',
            PrintType: '0',
            num: 0,
            cqyzlx: 0,
            lsyzlx: 0,
            ptypbz: 0,
            jslyp: 0,
            dmypbz: 0,
            gwyp: 0,
            gzhcbz: 0,
            cq: '1',
			yyff:'0',
            isPrent: undefined,
            isActives: undefined,
            SldIndex: undefined,
            //对于顶部菜单栏隐藏显示的控制
            sldmxShow: true,
            bqbyshow: true,
            ksfydShow: false,
            tysqmxShow: false,
            kshzlydShow: false,
            notSldmxShow: false,
            fylydShow: false,
            isActive: undefined,
            hoverIndex3: undefined,
            activeIndex3: undefined,
            barContent: {
				lxmc: '-1',
			},
            ksList: [],
            isCheckedByd: [],
            isCheckBydAll: false,
            yfList: [],
            bqList: [],
            fyList: [],
            //申领单明细
            open: [],
            person: [],
            sldh: [],
            ksbm: null,
            treeList: [],
            zyhList: [],
            sqdmxList: [],

            //科室发药单
            fylx: '0', //发药单类型
            type: '1', //用法选择
            treeBydList: [],
            fylist: {
                "bt": "医嘱明细发药单"
            },
            isMx: true,
            printData: {},
            fydmxList: [],
            fydmxZList: [],
            talAll: 0,//参考合计金额
            //科室汇总领药单
            lyList: {
                "bt": "医嘱明细发药单"
            },
            lydmxList: [], //领药单明细
            treeLydList: [], //领药单
            bydhList: [],  //领药单号集合
            wlyList: [], //未领药
            printLydData: {}, //打印数据
            cancleLingYao: true,
            //退药申请
            treeTydList: [],
            tydmxList: [],
            bydh: null,//摆药单
            fydh: null,//发药单号
            parameter: '',//检索条件
            bydTotalFee: 0,//摆药总计
            is_csqx: {
                N04003002200403: '0',
            },
            //打印机
            KfyPrint: "",
            KfyPrintSize: "",
            FydPrint: "",
            FydPrintSize: "",
            fydIndex: null,
            lsksbm: null,
            sfdjl: '-1',
            djl_tran: {
                '-1': '全部',
                '0': '否',
                '1': '是',
            },
            tydAll: false,
            tydIisChecked: [],
        },
        mounted: function () {
            this.getYfbm();
            this.getKS(); //获取科室信息
            this.getBq(); //获取病区
            this.getCsqx();
            this.initData();
            //初始化检索日期！为今天0点到今天24点
            var myDate = new Date();
            this.barContent.dateBegin = this.fDate(new Date(), 'date') + ' 00:00:00';
            this.barContent.dateEnd = this.fDate(new Date(), 'date') + ' 23:59:59';
            laydate.render({
                elem: '#timeVal',
                value: this.barContent.dateBegin,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    toolBar.barContent.dateBegin = value;
                    toolBar.refresh();
                }
            });
            laydate.render({
                elem: '#timeVal1',
                value: this.barContent.dateEnd,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    toolBar.barContent.dateEnd = value;
                    toolBar.refresh();
                }
            });


            window.addEventListener('storage', function (e) {
                if (e.key == 'bqby' && e.oldValue !== e.newValue) {
                    toolBar.initData();
                }
            });
        },
        updated: function () {
            changeWin();
        },
        methods: {
			hzdcx:function(){
				console.log($('#fydmx'))
				$('#fydmx').attr('src','/FR/ReportServer?reportlet=zyb%2Fzy%2Fzy_hzfyqd.cpt&ksbm='+this.barContent.ksbm+'&yfbm='+this.barContent.yfbm+'&starttime='+toolBar.barContent.dateBegin+"&endtime="+toolBar.barContent.dateEnd)
			},
			kfdcx:function(){
				
				$('#fydmx').attr('src','/FR/ReportServer?reportlet=zyb%2Fzy%2Fzy_hzfyqd_kfy.cpt&ksbm='+this.barContent.ksbm+'&yfbm='+this.barContent.yfbm+'&starttime='+toolBar.barContent.dateBegin+"&endtime="+toolBar.barContent.dateEnd)
			},
			sfnm:function(){
				this.getslmxData(this.sldh);
			},
            initData:function (){
                if(JSON.parse(sessionStorage.getItem("bqby"))){
                    this.barContent.ksbm = JSON.parse(sessionStorage.getItem("bqby")).ksbm;
                    this.num= JSON.parse(sessionStorage.getItem("bqby")).num;
                    sessionStorage.removeItem('bqby')
                }
            },
            resultChangeData: function (val) {
                this.PrintType = val[0];
                if (this.isActives) {
                    this.getBydPerson(this.bydh, this.isActives, this.isPrent)
                } else {
                    this.getLydPerson(this.bydh, this.lyList.fysj, this.isPrent)
                }
            },
            reCheckOne: function (val) {
                Vue.set(this, val[0], val[1]);
                this.getslmxData(this.sldh);
            },
            reCheckBoxByd: function (val) {
                this.reCheckBoxCommon(val, 'isCheckBydAll', 'isCheckedByd')
            },
            reCheckBoxCommon: function (val, checkedAll, checked, index) {
                var that = this;
                if (val[0] == 'some') {
                    Vue.set(this[checked], val[1], val[2]);
                    if (that.notempty(this[checked]).length == this[val[3]].length) {
                        this[checked].every(function (el) {
                            if (el === true) {
                                if (index != undefined) {
                                    return that[checkedAll][index] = true
                                } else {
                                    return that[checkedAll] = true
                                }

                            } else {
                                return that[index != undefined ? checkedAll[index] : checkedAll] = false
                            }
                        })
                    }
                } else if (val[0] == 'all') {
                    if (index != undefined) {
                        this[checkedAll][index] = val[2];
                    } else {
                        this[checkedAll] = val[2];
                    }
                    if (val[1] == null) val[1] = "jsonList";
                    if (index != undefined) {
                        var all = this[checkedAll][index]
                    } else {
                        var all = this[checkedAll]
                    }
                    if (all) {
                        var list = typeof val[1] == 'string' ? this[val[1]] : val[1]
                        for (var i = 0; i < list.length; i++) {
                            Vue.set(this[checked], i, true);
                            // this.isChecked[i] = true;
                        }
                    } else {
                        this[checked] = [];
                    }
                }
            },
            getCsqx: function () {
                //获取参数权限
                parm = {
                    "ylbm": 'N040030022004',
                    "ksbm": ksbm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0 && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N04003002200401":
                                    if (csjson.csz) {
                                        toolBar.is_csqx.N04003002200401 = csjson.csz;
                                    }
                                    break;
                                case "N04003002200402":
                                    if (csjson.csz) {
                                        toolBar.is_csqx.N04003002200402 = csjson.csz;
                                    }
                                    break;
                                case "N04003002200403":
                                    if (csjson.csz) {
                                        toolBar.is_csqx.N04003002200403 = csjson.csz;
                                    }
                                    break;
                                case "N04003002200405": //摆药跳转地址
                                    if (csjson.csz) {
                                        toolBar.is_csqx.N04003002200405 = csjson.csz;
                                    }
                                    break;

                            }
                        }
                    } else {
                        malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                    }
                });
                //口服药打印机
                window.top.J_tabLeft.csqxparm.csbm = "N010024016";
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                    if (json.a == 0) {
                        console.log(json.d);
                        if (json.d != null && json.d != undefined && json.d.length > 0) {
                            toolBar.KfyPrint = json.d[0].csz;
                            toolBar.KfyPrintSize = json.d[0].cszmc;
                        }
                    }
                });
                //发药单打印机
                window.top.J_tabLeft.csqxparm.csbm = "N010024015";
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                    if (json.a == 0) {
                        console.log(json.d);
                        if (json.d != null && json.d != undefined && json.d.length > 0) {
                            toolBar.FydPrint = json.d[0].csz;
                            toolBar.FydPrintSize = json.d[0].cszmc;
                        }
                    }
                });

            },

            resultChangeKs: function (val) {
                this.fydIndex = null;
                this.lsksbm = val[0];
                if (val[2].length > 1) {
                    if (Array.isArray(this[val[2][0]])) {
                        Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                        this.refresh();
                        // toolBar.queryWly();
                    } else {
                        Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                        if (val[3] != null) {
                            Vue.set(this[val[2][0]], val[3], val[4]);
                        }
                        this.$forceUpdate()
                        this.refresh();
                        // toolBar.queryWly();
                    }
                } else {
                    this[val[2][0]] = val[0];
                    this.refresh();
                    // toolBar.queryWly();
                }
                if (val[1] != null) {
                    this.nextFocus(val[1]);
                }
            },

            resultChangeLyd: function (val) {
                this[val[2][0]] = val[0];
                if (this.isCheckAll) {
                    this.getLydmxData()
                } else {
                    toolBar.getLydPerson(toolBar.lyList.fydh, toolBar.lyList.fysj, toolBar.isActive);
                    toolBar.lydmxList = Object.assign({}, toolBar.fydmxList);
                    if (val[1] != null) {
                        this.nextFocus(val[1]);
                    }
                }
            },

            resultChangeyf: function (val) {
                Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                Vue.set(this[val[2][0]], 'yfmc', val[4]);
                this.$forceUpdate();
                toolBar.refresh();
                // toolBar.queryWly();
            },
            //用法切换刷新

            resultChangeyyff: function (val) {
                //toolBar.refresh();
                if (val[2].length > 1) {
                    if (Array.isArray(this[val[2][0]])) {
                        Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                        //toolBar.printDJ();
                        toolBar.fydmxList = Object.assign({}, toolBar.fydmxList);
                        //toolBar.queryWly();
                    } else {
                        Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                        if (val[3] != null) {
                            Vue.set(this[val[2][0]], val[3], val[4]);
                            //toolBar.queryWly();
                        }
                        //toolBar.printDJ();
                        toolBar.fydmxList = Object.assign({}, toolBar.fydmxList);
                        //toolBar.queryWly();
                    }
                } else {
                    this[val[2][0]] = val[0];
                    //toolBar.printDJ();
                    //toolBar.queryWly();
                    //用法过滤，根据单号用法过滤检索
                    if (toolBar.num == 1) {
                        toolBar.printDJ();
                    } else {
                        toolBar.getLydPerson(toolBar.lyList.fydh, toolBar.lyList.fysj, toolBar.isActive);
                    }
                    toolBar.fydmxList = Object.assign({}, toolBar.fydmxList);
                }

                /*
                if (val[1] != null) {
                    this.nextFocus(val[1]);
                }
                */
            },

            resultChangeDy: function (val) {
                toolBar.PrintType = val[0];

                toolBar.getKshzlydData();
            },

            resultChangeSfdjl: function (val) {
                toolBar.sfdjl = val[0];

                toolBar.getKshzlydData();
            },

            //用法切换刷新
            resultChangeBq: function (val) {
                if (val[2].length > 1) {
                    if (Array.isArray(this[val[2][0]])) {
                        Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                        toolBar.getSldmxData();
                    } else {
                        Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                        if (val[3] != null) {
                            Vue.set(this[val[2][0]], val[3], val[4]);
                        }
                        toolBar.getSldmxData();
                        this.$forceUpdate()
                    }
                } else {
                    toolBar.getSldmxData();
                    this[val[2][0]] = val[0];
                }
                if (val[1] != null) {
                    this.nextFocus(val[1]);
                }
            },

            //刷新
            refresh: function () {
                this.parameter = "";
                //0申领单明细,1科室发药单,2科室汇总领药单,3退药申请明细
                if (toolBar.num == 0) {
                    toolBar.getSldmxData();
                } else if (toolBar.num == 1) {
                    toolBar.getKsfydData();
                    //toolBar.queryWly();
                } else if (toolBar.num == 2) {
                    toolBar.getKshzlydData();
                    toolBar.queryWly();
                } else {
                    toolBar.getTysqData();
                    toolBar.queryWly();
                }
            },

            bqtyjl: function () {
                var url = '';
                if (toolBar.is_csqx.N04003002200402) {
                    url = toolBar.is_csqx.N04003002200402;
                } else {
                    url = window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=ejkf%2Fyfgl_hsztysqlist.cpt';
                }
                this.topNewPage('病区退药记录', url);
            },

            notCheck: function () {
                var tyid = [];
                for (var i = 0; i < toolBar.tydmxList.length; i++) {
                    if (toolBar.isChecked[i]) {
                        tyid.push(toolBar.tydmxList[i].tysqid);
                    }
                }
                toolBar.isChecked = [];//取消选择
                if (tyid.length <= 0) {
                    malert("请选择需要退药审核药品明细!", 'top', 'defeadted');
                    return;
                }
                var json = {
                    searchtysqid: tyid
                };
                common.openloading();
                this.$http.post('/actionDispatcher.do?reqUrl=HszHlywBqty&types=deleteTysq', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("病区退药作废成功!", 'top', 'success');
                            toolBar.refresh();
                        } else {
                            malert(data.body.c, "top", "defeadted");
                        }
                        common.closeLoading();
                    }, function (error) {
                        console.log(error);
                        common.closeLoading();
                    });
            },
            //菜单栏切换
            tabBg: function (index) {
                this.open = [];
                this.tydAll = false;
                this.isCheckAll = false;
                this.hoverIndex3 = undefined;
                this.activeIndex3 = undefined;
                this.tydIisChecked = [];
                this.isChecked = [];
                this.isActive = undefined;
                this.SldIndex = undefined;
                this.bydh = undefined;//摆药单号清空
                this.fydh = undefined;//汇总发药单号
                //0申领单明细,1科室发药单,2科室汇总领药单,3退药申请明细

                //切换时跟具病区编码初始化切换的页面的科室 @zh

                if (toolBar.barContent.bqbm == '00' || toolBar.barContent.bqbm) {
                    loop:
                        for (var i = 0; i < toolBar.ksList.length; i++) {
                            if (toolBar.barContent.bqbm == toolBar.ksList[i].bqbm) {
                                toolBar.barContent.ksbm = toolBar.ksList[i].ksbm;
                                break loop;
                            } else {
                                toolBar.barContent.ksbm = toolBar.lsksbm;
                            }
                        }
                } else {
                    toolBar.barContent.ksbm = toolBar.lsksbm;
                }
                /*****************************************************/
                if (index == 0) {
                    this.sldmxShow = true;
                    this.bqbyshow = true;
                    this.ksfydShow = false;
                    this.tysqmxShow = false;
                    this.kshzlydShow = false;
                    this.notSldmxShow = false;
                    this.fylydShow = false;
                } else if (index == 1) {
                    this.sldmxShow = false;
                    this.bqbyshow = false;
                    this.ksfydShow = true;
                    this.tysqmxShow = false;
                    this.kshzlydShow = false;
                    this.notSldmxShow = true;
                    this.fylydShow = true;
                } else if (index == 2) {
                    this.sldmxShow = false;
                    this.bqbyshow = false;
                    this.ksfydShow = false;
                    this.tysqmxShow = false;
                    this.kshzlydShow = true;
                    this.notSldmxShow = true;
                    this.fylydShow = true;
					toolBar.hzdcx();
                } else {
                    this.sldmxShow = false;
                    this.bqbyshow = false;
                    this.ksfydShow = false;
                    this.tysqmxShow = true;
                    this.kshzlydShow = false;
                    this.notSldmxShow = true;
                    this.fylydShow = false;
                }
                this.num = index;
                this.talAll = 0;
                toolBar.refresh();
            },
            //获取药房
            getYfbm: function () {
                var pram = {
                    "ylbm": 'N040030022004'
                };
                common.openloading();
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(pram), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            toolBar.yfList = json.d.list;
                            toolBar.barContent.yfbm = json.d.list[0].yfbm;
                            toolBar.barContent.yfmc = json.d.list[0].yfmc;
                            // toolBar.barContent.yfmc = json.d.list[0].yfmc;
                        }
                    } else {
                        malert("药房编码获取失败：" + json.c, 'top', 'defeadted');
                    }
                    common.closeLoading();
                });
            },
            //查询科室列表
            getKS: function () {
                var dg = {
                    "page": 1,
                    "rows": 50,
                    "sort": "",
                    "order": "asc"
                };
                var bean = {
                    "zyks": "1"
                };
                common.openloading();
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" +
                    JSON.stringify(dg) + "&json=" + JSON.stringify(bean),
                    function (json) {
                        if (json.a == 0 || json.d.list.length != 0) {
                            toolBar.ksList = json.d.list;
														toolBar.barContent.ksbm = json.d.list[0].ksbm;
							toolBar.barContent.ksbm = json.d.list[0].ksmc;
							toolBar.lsksbm = json.d.list[0].ksbm
//                    var qbks={
//                    		ksbm:'',
//                    		ksmc:'全部',
//                    }
//                    toolBar.ksList.push(qbks);
                        }
                        common.closeLoading();
                    });
            },

            //查询病区列表
            getBq: function () {
                var dg = {
                    "page": 1,
                    "rows": 50,
                    "sort": "bqbm",
                    "order": "asc"
                };
                common.openloading();
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=bq&dg=" +
                    JSON.stringify(dg),
                    function (json) {
                        toolBar.bqList = json.d.list;
                        var qbbq = {
                            'bqbm': '00',
                            'bqmc': '全部',
                        }
                        toolBar.bqList.unshift(qbbq);
                        toolBar.barContent.bqbm = '00';
                        common.closeLoading();
                    });
            },

            //**************************************申领单明细start
            isOpen: function (index) {
                this.SldIndex = index;
                Vue.set(this.open, index, !this.open[index])
            },
            //根据申领单获取对应住院病人
            reCheckBoxBy: function (val) {
                if (val[1] !== 'all') this.activeIndex = val[0];
                if (val[0] == 'some') {
                    Vue.set(this.isChecked, val[1], val[2]);
                    if (!val[2]) this.isCheckAll = false;
                } else if (val[0] == 'one') {
                    this.isCheckAll = false;
                    this.isChecked = [];
                    Vue.set(this.isChecked, val[1], val[2]);
                } else if (val[0] == 'all') {
                    this.isCheckAll = val[2];
                    if (val[1] == null) val[1] = "jsonList";
                    if (this.isCheckAll) {
                        for (var i = 0; i < this[val[1]].length; i++) {
                            Vue.set(this.isChecked, i, true);
                            // this.isChecked[i] = true;
                        }
                    } else {
                        this.isChecked = [];
                    }
                }
                this.zyhList = [];
                for (var i = 0; i < toolBar.person.length; i++) {
                    if (this.isChecked[i]) {
                        toolBar.zyhList.push({
                            "zyh": toolBar.person[i].zyh
                        });
                    }
                }
                toolBar.getslmxData(toolBar.sldh);
            },

            setBrList: function (item) {
                for (var y = 0; y < item.hzxx.length; y++) {
                    this.person.push(item.hzxx[y]);
                }
            },
            recursionByd: function (arr) {
                for (var j = 0; j < arr.length; j++) {
                    for (var h = 0; h < arr[j]['sldh'].length; h++) {
                        if (arr[j]['sldh'][h].checked == true) {
                            this.sldh.push(arr[j]['sldh'][h].sldh)
                            for (var n = 0; n < arr[j]['sldh'][h].hzxx.length; n++) {
                                this.person.push(arr[j]['sldh'][h].hzxx[n]);
                            }
                        }

                    }
                    if (arr[j] && typeof arr[j]['sldh'] == 'object') {
                        this.recursionByd(arr[j]['sldh'])
                    }
                }
            },
            getPerson: function (item, index, sldh, $index, val) {
                toolBar.lsksbm = this.treeList[$index].ksbm;
                this.zyhList = [];
                this.sldh = [];
                this.person = [];
                this.isCheckAll = true;
                if (this.is_csqx.N04003002200403 == '0') {
                    this.sldh.push(item.sldh)
                    this.ksbm = item.ksbm
                    this.person = item.hzxx
                    // this.setBrList(item)
                } else {
                    this.recursionByd(this.treeList)
                }
                this.checkAll('person');
                for (var j = 0; j < this.person.length; j++) {
                    this.zyhList.push({
                        "zyh": this.person[j].zyh
                    });
                }
                this.getslmxData(this.sldh);
            },
//根据申领单号和住院号查询申领明细
            getslmxData: function (sldh) {
                toolBar.talAll = 0;
                toolBar.sqdmxList = [];
                if (!this.zyhList || this.zyhList.length <= 0 || !this.zyhList[0].zyh) {
                    malert("未查到相关住院病人！请检查数据是否正常！", "top", "defeadted");
                    return;
                }
                var json = {
                    "list": this.zyhList
                };
                common.openloading();
                var str = '&cqyzlx=' + this.cqyzlx + '&lsyzlx=' + this.lsyzlx + '&ptypbz=' + this.ptypbz + '&jslyp=' + this.jslyp + '&dmypbz=' + this.dmypbz + '&gwyp=' + this.gwyp + '&gzhcbz=' + this.gzhcbz + '&yyff=' + this.yyff + ''
                this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywBqby&types=sldmxcx&yfbm=' + this.barContent.yfbm + '&sldh=' + sldh.join() + str, JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            toolBar.sqdmxList = data.body.d.list;
                            toolBar.isCheckedByd = [];
                            for (var i = 0; i < data.body.d.list.length; i++) {
                                toolBar.talAll = toolBar.fDec(parseFloat(toolBar.talAll) + parseFloat(toolBar.fDec(data.body.d.list[i].yplj * data.body.d.list[i].sl, 2)), 2);
                                Vue.set(toolBar.isCheckedByd, i, true)
                                Vue.set(toolBar, 'isCheckBydAll', true)
                            }
                            console.log(toolBar.talAll);
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                        common.closeLoading();
                    }, function (error) {
                        common.closeLoading();
                        malert(error, 'top', 'defeadted');
                    });
            }
            ,
            searching: function () {
                if (this.num == 0) {
                    this.getSldmxData();
                } else if (this.num == 1) {
                    this.getKsfydData();
                }
            }
            ,
//刷新摆药单
            getSldmxData: function () {
                var pram_str = {};
                pram_str.yfbm = this.barContent.yfbm;
                pram_str.beginrq = this.barContent.dateBegin;
                pram_str.endrq = this.barContent.dateEnd;
                pram_str.bqbm = this.barContent.bqbm == '00' ? '' : this.barContent.bqbm;
                pram_str.parm = this.parameter;
                common.openloading();
                $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywBqby&types=sldcx&parm=" + JSON.stringify(pram_str), function (json) {
                    if (json.a == 0) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            toolBar.open.push(false);
                        }
                        toolBar.treeList = json.d.list;
                        toolBar.person = [];
                        toolBar.sqdmxList = [];
                    } else {
                        // malert(json.c,'top','defeadted');
                    }
                    common.closeLoading();
                });
            }
            ,
//摆药
            bqby: function () {
                var obj = {};
                obj.yfbm = toolBar.barContent.yfbm;
                obj.yfmc = toolBar.barContent.yfmc;
                var zyhs = [];
                var ypzxlshs = '';
                if (this.is_csqx.N04003002200403 && this.is_csqx.N04003002200403 == '1') { //新流程
                    for (var j = 0; j < this.isCheckedByd.length; j++) {
                        if (this.isCheckedByd[j] == true) {
                            if (this.sqdmxList[j] && this.sqdmxList[j].ypzxlsh) {
                                ypzxlshs += (this.sqdmxList[j].ypzxlsh + ',')
                            }
                        }
                    }
                    obj.ypzxlshs = ypzxlshs;

                    if (obj.ypzxlshs.length == 0) {
                        malert("请选择药品!", 'top', 'defeadted');
                        return;
                    }
                } else { // 老流程
                    obj.sldh = toolBar.sldh[0];
                    obj.ksbm = toolBar.ksbm;
                    for (var i = 0; i < toolBar.zyhList.length; i++) {
                        zyhs.push(toolBar.zyhList[i].zyh);
                    }
                    obj.zyhs = JSON.stringify(zyhs);

                    if (!obj.sldh) {
                        malert("申领单号为空，不能进行发药!", 'top', 'defeadted');
                        return;
                    }
                    if (!obj.ksbm) {
                        malert("科室编码为空，不能进行发药!", 'top', 'defeadted');
                        return;
                    }
                }
                if (!obj.yfbm) {
                    malert("药房编码为空，不能进行发药!", 'top', 'defeadted');
                    return;
                }
//			if(obj.zsks){
//
//			}
				obj.yyff = this.yyff;
                common.openBar();
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywBqby&types=bqby', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == 0) {
                        // toolBar.refresh();
                        malert("发药保存成功", 'top', 'success');
                        toolBar.fylist.bydh = data.body.d;
                        toolBar.barContent.ksbm = toolBar.ksbm;
                        toolBar.toolBar = '0';
                        if (toolBar.is_csqx.N04003002200405 == '1') {
                            toolBar.tabBg(2);
                        } else {
                            toolBar.tabBg(1);
                        }
                        if (data.body.c != '成功') {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    } else {
                        malert("发药错误！" + data.body.c, 'top', 'defeadted');
                    }
                    common.closeLoading();
                }, function (error) {
                    malert("发药错误！" + error, 'top', 'defeadted');
                    console.log(error);
                    common.closeLoading();
                });
            }
            ,

//****************************************申领单明细end

//****************************************科室发药单start
//打印
            print: function () {
                //帆软打印
                var frpath = "";
                if (window.top.J_tabLeft.obj.frprintver == "3") {
                    frpath = "%2F";
                } else {
                    frpath = "/";
                }
                var fpreport = "", reportlets = '', str = '', zxdlx = "", PrintName = "", PrintSize = "", sfdjl = "";
                PrintName = toolBar.FydPrint;
                PrintSize = toolBar.FydPrintSize;

                //处理执行单类型
                if (toolBar.fylx != undefined && toolBar.fylx != "0") {
                    zxdlx = ",zxdlx:'" + toolBar.fylx + "'";
                }
                if (this.num == 1) {
                    if (toolBar.type == '1') {//明细
                        if (toolBar.barContent.yfmc == "中药房") {
                            fpreport = "yfgl_ksfyd_zymx.cpt";
                        } else {//西药房
                            fpreport = "yfgl_ksfyd_mx.cpt";
                        }
                    } else if (toolBar.type == '0') {//汇总
                        fpreport = "yfgl_ksfyd_hz.cpt";
                    } else if (toolBar.type == '2') {
                        fpreport = "yfgl_zybyd.cpt";
                    } else {
                        fpreport = "yfgl_kshzfyd_kfy.cpt";
                        zxdlx = ",zxdlx:'1'";
                        if (toolBar.sfdjl != "-1") {
                            zxdlx += ",sfdjl:" + toolBar.sfdjl;
                        }
                        PrintName = toolBar.KfyPrint;
                        PrintSize = toolBar.KfyPrintSize;
                    }
                } else {
                    //toolBar.fylx
                    if (this.isActives == undefined) {//发药单

                        if (toolBar.type == '0') {//汇总
                            fpreport = "yfgl_kshzfyd_hz.cpt";
                        } else if (toolBar.type == "1") {//明细
                            fpreport = "yfgl_kshzfyd_mx.cpt";
                        } else if (toolBar.type == "3") {
                            fpreport = "yfgl_kshzfyd_kfy.cpt";
                            zxdlx = ",zxdlx:'1'";
                            if (toolBar.sfdjl != "-1") {
                                zxdlx += ",sfdjl:" + toolBar.sfdjl;
                            }
                            PrintName = toolBar.KfyPrint;
                            PrintSize = toolBar.KfyPrintSize;
                        }
                    } else {
                        if (toolBar.type == '2') {//中药
                            fpreport = "yfgl_zybyd.cpt";
                        } else {//汇总
                            fpreport = "yfgl_kshzfyd_hz.cpt";
                        }
                    }
                }
                var lxarr = [], obj = {};
                if (this.num == 2 || toolBar.type == '2') {
                    if (this.num == 2) {
                        if (this.isPrent == undefined) {
                            malert('请选择发药单', 'top', 'defeadted');
                            return;
                        }
                        if (this.isActives != undefined) {
                            lxarr.push(this.treeLydList[this.isPrent]['fymx'][this.isActives])

                        } else {
                            lxarr = this.treeLydList[this.isPrent]['fymx'].reduce((cur, next) => {
                                obj[next.zyh] ? "" : obj[next.zyh] = true && cur.push(next);
                                return cur;
                            }, []) //设置cur默认类型为数组，并且初始值为空的数组
                        }
                    } else {
                        if (this.isActive == undefined) {
                            malert('请选择发药单', 'top', 'defeadted');
                            return;
                        }
                        lxarr = this.treeBydList[this.isActive]['fymx'].reduce((cur, next) => {
                            obj[next.yzxh] ? "" : obj[next.yzxh] = true && cur.push(next);
                            return cur;
                        }, []) //设置cur默认类型为数组，并且初始值为空的数组
                    }
                    //console.log("this.isActives:"+this.isActives+" fylx:"+fylx);
                    if (this.num == 2 && this.isActives == undefined) {
                        if (!this.bydh) {
                            malert("请选择需要打印的摆药单", 'top', 'defeadted');
                            return;
                        }
                        str = "{reportlet: 'fpdy" + frpath + "yfgl%2F" + fpreport + "',fycfh:'" + this.bydh + "',yljgbm:'" + jgbm + "'" + zxdlx + "}";
                    } else {
                        for (var i = 0; i < lxarr.length; i++) {
                            var zyh = lxarr[i].zyh;
                            var dybyd = lxarr[i].bydh;
                            var yzxh = lxarr[i].yzxh;
                            if (this.num == 2 && toolBar.type == '2') {//汇总单中药
                                dybyd = lxarr[i].bydh;
                            }
                            str += "{reportlet: 'fpdy" + frpath + "yfgl%2F" + fpreport + "',yljgbm:'" + jgbm + "',yzxh:'" + yzxh + "',bydh:'" + dybyd + "',zyh:'" + zyh + "'},";
                        }
                    }

                } else {
                    str = "{reportlet: 'fpdy" + frpath + "yfgl%2F" + fpreport + "',yljgbm:'" + jgbm + "',bydh:'" + this.bydh + "'" + zxdlx + "}";
                }
                this.updatedDybz();
                if (!FrPrint(reportlets = '[' + str + ']', PrintName, PrintSize)) {
                    window.print();
                    toolBar.refresh();
                }

            },
            updatedDybz: function () {
                var pram = {
                    "ylbm": 'N040030022004',
                    "ksbm": this.barContent.ksbm, //toolBar在bqby.js内定义
                    "fylx": this.fylx,
                    "fycfh": this.bydh,
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=updDybz&parm=" + JSON.stringify(pram), function (json) {
                    if (json.a == 0) {
                        //malert( json.c, 'top', 'defeadted');
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                });
            },
            unique: function (arr) {
                var res = [];
                var obj = {};
                for (var i = 0; i < arr.length; i++) {
                    if (!obj[arr[i]]) {
                        obj[arr[i]] = 1;
                        res.push(arr[i]);
                    }
                }
                return res;
            }
            ,
            printDJ: function () {
                toolBar.talAll = 0;
                var param = {
                    'bydh': toolBar.fylist.bydh,
                    'ksbm': toolBar.barContent.ksbm,
                    'fylx': this.fylx,
                    'yfbm': this.barContent.yfbm,
                };
                if (this.sfdjl != '-1') {
                    pram.sfdjl = this.sfdjl;
                }
                common.openloading();
                //获取打印数据s
                $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywBqby&types=print&parm=" + JSON.stringify(param),
                    function (json) {
                        if (json.a == 0) {
                            toolBar.printData = json.d;
                            if (json.d != null) {
                                toolBar.fydmxList = json.d.djmx;
                                for (var i = 0; i < json.d.djmx.length; i++) {
                                    toolBar.talAll = toolBar.fDec(parseFloat(toolBar.talAll) + parseFloat(toolBar.fDec(toolBar.fydmxList[i].yplj * toolBar.fydmxList[i].fysl, 2)), 2);

                                }


                            } else {
                                toolBar.fydmxList = [];
                            }
                        } else {
                            malert(json.c, 'top', 'defeadted');
                        }
                        common.closeLoading();
                    });
            }
            ,
//点击查看发药单明细信息
            getFyPerson: function (item, index) {
                this.bydh = item.bydh;
                this.isActive = index;
                toolBar.fylist = item;
                toolBar.fylist.yfmc = toolBar.barContent.yfmc;
                toolBar.fylist.ksmc = toolBar.listGetName(toolBar.ksList, toolBar.barContent.ksbm, 'ksbm', 'ksmc');
//			toolBar.fydmxList = item.fymx;
                //打印
                this.printDJ();
                this.getType();
            },
            getType: function () {
                if (toolBar.type == '1') {
                    toolBar.fylist.bt = "医嘱明细发药单";
                    toolBar.lyList.bt = "医嘱明细发药单";
                } else if (toolBar.type == '0') {
                    toolBar.fylist.bt = "医嘱汇总发药单";
                    toolBar.lyList.bt = "医嘱汇总发药单";
                }
            },
//发药单类型改变事件
            changeType: function (val) {
                toolBar.type = val[0];
                this.getType();
                //这里初始化请求发药单需要的参数
                if (toolBar.type == 1) {
                    // toolBar.isMx = true;
                    // toolBar.fylist.bt = "医嘱明细发药单";
                } else if (toolBar.type == 0) {
                    console.log("---->", toolBar.fydmxList)
                    toolBar.fydmxZList = []
                    var obj = {}
                    for (var i = 0; i < this.fydmxList.length; i++) {
                        if (!obj[this.fydmxList[i].ryypbm]) {
                            toolBar.fydmxZList.push(this.fydmxList[i])
                            obj[this.fydmxList[i].ryypbm] = true;
                        }
                    }
                    fyList = JSON.parse(JSON.stringify(toolBar.fydmxZList));
                    console.log(toolBar.fyList)
                    console.log(toolBar.fydmxZList)
                    console.log(toolBar.fydmxList)
                    for (var a = 0; a < fyList.length; a++) {
                        fyList[a].fysl = 0
                    }
                    for (var a = 0; a < fyList.length; a++) {
                        for (var i = 0; i < toolBar.fydmxList.length; i++) {
                            if (fyList[a].ryypbm == toolBar.fydmxList[i].ryypbm) {
                                fyList[a].fysl = fyList[a].fysl + toolBar.fydmxList[i].fysl

                            }
                        }
                    }
                    toolBar.fydmxZList = fyList
                    toolBar.isMx = false;
                    // toolBar.fylist.bt = "医嘱汇总发药单";
                }

            }
            ,
//获取科室发药单信息
            getKsfydData: function () {
                if (!this.fylx) {
                    this.fylx = "0";
                }
                var pram_str = {
                    "ylbm": 'N040030022004',
                    "ksbm": toolBar.barContent.ksbm, //toolBar在bqby.js内定义
                    "beginrq": this.barContent.dateBegin,
                    "endrq": this.barContent.dateEnd,
                    "fylx": this.fylx,
                    "yfbm": this.barContent.yfbm,
                    "parm": this.parameter,
                };
                var types = "";
                if (this.type == "0") {
                    types = "bydhzwfy";
                } else {
                    types = "bydmxwfy";
                }
                common.openloading();
                toolBar.treeBydList = [];
                //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=" + types + "&parm=" + JSON.stringify(pram_str), function (json) {
                    if (json.a == 0 && json.d.list.length != 0) {
                        toolBar.treeBydList = json.d.list;
                        toolBar.getFyPerson(toolBar.treeBydList[0], 0);
                        setTimeout(function () {
                            /********************************统计摆药单总金额**********************************/
                            common.openloading();
                            $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=bydZjeTj&parm=" + JSON.stringify(pram_str), function (json) {
                                if (json.a == 0) {
                                    if (json.d) {
                                        toolBar.bydTotalFee = json.d.bydTotalFee;
                                    }
                                } else {
                                    malert(json.c, 'top', 'defeadted');
                                }
                                common.closeLoading();
                            });
                            /********************************统计摆药单总金额**********************************/
                        }, 200);
                    } /*else {
                    malert('未查询到摆药信息！', 'top', 'defeadted');
                }*/
                    common.closeLoading();
                });
                //清空配方明细
                toolBar.fydmxList = [];
                toolBar.fylist = {};
                this.getType();
            }
            ,
//*****************************************科室发药单end

//****************************************科室汇总领药单start
            printFyd: function () {
                if (!this.lyList.fydh) {
                    malert('请选择发药单', 'top', 'defeadted');
                    return;
                }
                //发药单打印
                //帆软打印
                if (this.num == 2) {
                    this.print()
                } else {
                    var reportlets = "[{reportlet: 'fpdy%2Fejkf%2Fyfgl_kshzfyd_hz.cpt',yljgbm:'" + jgbm + "',fycfh:'" + toolBar.lyList.fydh + "',zxdlx:'" + this.fylx + "'}]";
                    console.log(reportlets);
                    if (!FrPrint(reportlets, null)) {
                        window.print();
                    }
                }
            }
            ,

//摆药单查询
            getKshzlydData: function () {
                if (this.fylx == undefined || this.fylx == null || this.fylx == "") {
                    this.fylx = "0";
                }
                var pram = {
                    "ylbm": 'N040030022004',
                    "ksbm": toolBar.barContent.ksbm, //toolBar在bqby.js内定义
                    "beginrq": this.barContent.dateBegin,
                    "endrq": this.barContent.dateEnd,
                    "fylx": this.fylx,
                    'yfbm': toolBar.barContent.yfbm
                };
                if (toolBar.PrintType != '-1') {
                    pram.dybz = toolBar.PrintType;
                }
                var types = "";
                if (this.type == "0") {
                    types = "fydhz";
                } else {
                    types = "fydmx";
                }
                common.openloading();
                //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=" + types + "&parm=" + JSON.stringify(pram), function (json) {
                    if (json.a == 0) {
                        toolBar.treeLydList = toolBar.filterSort(json.d.list, 'j', 'fysj');
                        toolbar.wlyList = [];
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                    common.closeLoading();
                });
            }
            ,
//未发摆药单查询



            queryWly: function () {
                if (this.fylx == undefined || this.fylx == null || this.fylx == "") {
                    this.fylx = "0";
                }
                var pram = {
                    "ylbm": 'N040030022004',
                    "ksbm": toolBar.barContent.ksbm, //toolBar在bqby.js内定义
                    "beginrq": toolBar.barContent.dateBegin,
                    "endrq": toolBar.barContent.dateEnd,
                    "fylx": this.fylx,
                    "yfbm": toolBar.barContent.yfbm
                };
                var types = "";
                if (this.type == "0") {
                    types = "bydhzwfy";
                } else {
                    types = "bydmxwfy";
                }
                common.openloading();
                //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=" + types + "&parm=" + JSON.stringify(pram), function (json) {
                    if (json.a == 0) {
                        toolBar.wlyList = json.d.list;
                        toolBar.isCheckAll = true;
                        toolBar.checkAll("wlyList");
                        toolBar.getLydmxData();
                    } else {
                        malert(json.c, 'top', 'defeadted');
                    }
                    common.closeLoading();
                });
            }
            ,

//领药
            lingYao: function () {
                toolBar.bydhList = [];
                var bydhList = []
                for (var i = 0; i < toolBar.wlyList.length; i++) {
                    if (toolBar.isChecked[i]) {
                        toolBar.bydhList.push({
                            "bydh": toolBar.wlyList[i].bydh
                        });
                        bydhList.push(toolBar.wlyList[i].bydh)
                    }
                }
                if (toolBar.bydhList.length <= 0) {
                    malert("请选摆药单号", 'top', 'defeadted');
                    return;
                }
                toolBar.isChecked = []; //领药操作后取消领药选择标志
                var json = {
                    "list": toolBar.bydhList
                };
                common.openloading();
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywBqby&types=kshzfy&ksbm=' + toolBar.barContent.ksbm, JSON.stringify(json))
                    .then(function (data) {
                        common.closeLoading();
                        if (data.body.a == 0) {
                            toolBar.wlyList = [];
                            toolBar.lydmxList = [];
                            toolBar.cancleLingYao = false;
                            malert("科室汇总药成功!", 'top', 'success');
                            toolBar.tabBg(2);
                            toolBar.saveSpd(bydhList);
                            // window.print();
                        } else {
                            malert("摆药单查询失败：" + data.body.c, 'top', 'defeadted');
                        }
                    });
            },
            saveSpd: function (bydh) {
                var bydhs = {
                    bydhs: bydh
                }
                this.$http.post(window.top.J_tabLeft.obj.spdUrl + '/spd-api/spd/hospital-dispensing-drug', JSON.stringify(bydhs)).then(function (data) {
                    if (data.body.a == 0) {

                    }
                })
            },

//取消领药
            QxlingYao: function () {
                var json = {
                    "fycfh": toolBar.lyList.fydh,
                    "ksbm": toolBar.barContent.ksbm,
                    "yfbm": toolBar.barContent.yfbm
                };
                common.openloading();
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywBqby&types=kshzfyqx&ksbm=' + toolBar.barContent.ksbm, JSON.stringify(json))
                    .then(function (data) {
                        common.closeLoading();
                        if (data.body.a == 0) {
                            toolBar.wlyList = [];
                            toolBar.lydmxList = [];
                            toolBar.cancleLingYao = true;
                            toolBar.tabBg(2);
                            malert("取消科室汇总药成功!", 'top', 'success');
                            // window.print();
                        } else {
                            malert("取消摆药单查询失败：" + data.body.c, 'top', 'defeadted');
                        }
                    });
            }
            ,


//根据摆药单号查询明细
            getLydmxData: function () {
                this.isActives = undefined;
                this.isPrent = undefined;
                toolBar.talAll = 0.00;
                toolBar.lyList.yfmc = toolBar.barContent.yfmc;
                toolBar.lyList.ksmc = toolBar.listGetName(toolBar.ksList, toolBar.barContent.ksbm, 'ksbm', 'ksmc');
                toolBar.lyList.fydh = '';
                toolBar.lyList.fysj = '';
                this.bydhList = [];
                var byd = "";
                for (var i = 0; i < this.wlyList.length; i++) {
                    if (this.isChecked[i]) {
                        this.bydhList.push({
                            "bydh": this.wlyList[i].bydh
                        });
                        byd = byd + this.wlyList[i].bydh;
                    }
                }
                if (this.bydhList.length <= 0) {
                    toolBar.lydmxList = [];
                    //malert("请选摆药单号", 'top', 'defeadted');
                    return;
                }
                this.bydhList.bydh = byd;
                //打印数据
                this.printLydData.dj = this.bydhList;
                var fylx = null;
                if (toolBar.fylx == '2') {
                    fylx = '3';
                } else if (toolBar.fylx == '3') {
                    fylx = '2';
                } else {
                    fylx = toolBar.fylx;
                }
                var pram = {
                    "ksbm": toolBar.barContent.ksbm, //toolBar在bqby.js内定义
                    "fylx": fylx,
                };

                var json = {
                    "list": {
                        "pram": pram,
                        "bydh": this.bydhList
                    }
                };
                var types = "";
                if (toolBar.type == "0") {
                    types = "bydhzfymx";
                } else {
                    types = "bydfymx";
                }
                common.openloading();
                //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=' + types, JSON.stringify(json)).then(function (data) {
                    common.closeLoading();
                    if (data.body.a == 0) {
                        toolBar.lydmxList = data.body.d.list;
                        for (var i = 0; i < data.body.d.list.length; i++) {
                            toolBar.talAll = toolBar.fDec(parseFloat(toolBar.talAll) + parseFloat(toolBar.fDec(data.body.d.list[i].yplj * data.body.d.list[i].fysl, 2)), 2);
                            console.log(toolBar.talAll);
                        }
                        //打印数据
                        toolBar.printLydData.djmx = toolBar.lydmxList;
                    } else {
                        toolBar.lydmxList = [];
                        malert("摆药单查询失败：" + data.body.c, 'top', 'defeadted');
                    }
                    toolBar.getType();
                });
            }
            ,

            fymx: function (arr) {
                var obj = {};
                newArr = arr.reduce(function (item, next) {
                    obj[next.bydh] ? '' : obj[next.bydh] = true && item.push(next);
                    return item;
                }, []);
                return newArr
            }
            ,
//发药单打印
            getBydPerson: function (bydh, index, prentIndex) {
				$('#fydmx').attr('src','/FR/ReportServer?reportlet=zyb%2Fzy%2Fzy_hzfyqd.cpt&ksbm='+this.barContent.ksbm+'&yfbm='+this.barContent.yfbm+'&bydh='+bydh+'&starttime='+toolBar.barContent.dateBegin+"&endtime="+toolBar.barContent.dateEnd)
                // this.isActives = index;
                // this.bydh = bydh
                // this.isPrent = prentIndex
                // this.lydmxList = [];
                // this.talAll = 0;
                // var param = {
                //     'bydh': bydh,
                //     'ksbm': this.barContent.ksbm,
                //     'fylx': this.fylx,
                //     'lybz': '1',
                //     'yfbm': this.barContent.yfbm,
                //     'dybz': this.PrintType
                // };
                // common.openloading();
                // //获取打印数据
                // $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywBqby&types=print&parm=" + JSON.stringify(param),
                //     function (json) {
                //         if (json.a == 0 && json.d != null) {
                //             toolBar.lydmxList = json.d.djmx;
                //             for (var i = toolBar.lydmxList.length - 1; i >= 0; i--) {
                //                 if (toolBar.lydmxList[i].fysl == undefined || toolBar.lydmxList[i].fysl == null || toolBar.lydmxList[i].fysl == '') {
                //                     toolBar.lydmxList.splice(i, 1)
                //                 }
                //                 toolBar.talAll = toolBar.fDec(parseFloat(toolBar.talAll) + parseFloat(toolBar.fDec(toolBar.lydmxList[i].yplj * toolBar.lydmxList[i].fysl, 2)), 2);

                //             }
                //             common.closeLoading();
                //         } else {
                //             common.closeLoading();
                //             malert(json.c, 'top', 'defeadted');
                //         }
                //         common.closeLoading();
                //     });
            }
            ,
            getLydIsOpen: function (fydh, fysj, index, event) {
                if (event.type == "dblclick") {
                    this.isOpen(index)
                } else if (event.type == "click") {
                    this.fydIndex = index;
                    this.getLydPerson(fydh, fysj, index)
                    this.isChecked = [];
                    this.isCheckAll = false;
                }
            }
            ,
//发药单明细查询
            getLydPerson: function (fydh, fysj, index) {
				if(fydh.indexOf('FYD') != -1){
					$('#fydmx').attr('src','/FR/ReportServer?reportlet=zyb%2Fzy%2Fzy_hzfyqd.cpt&ksbm='+this.barContent.ksbm+'&yfbm='+this.barContent.yfbm+'&fycfh='+fydh+'&starttime='+toolBar.barContent.dateBegin+"&endtime="+toolBar.barContent.dateEnd)
				}else{
					$('#fydmx').attr('src','/FR/ReportServer?reportlet=zyb%2Fzy%2Fzy_hzfyqd.cpt&ksbm='+this.barContent.ksbm+'&yfbm='+this.barContent.yfbm+'&bydh='+fydh+'&starttime='+toolBar.barContent.dateBegin+"&endtime="+toolBar.barContent.dateEnd)
				}
				
				
				
				
                // this.talAll = 0;
                // this.bydh = fydh;
                // this.isActives = undefined;
                // this.isPrent = index ? index : this.fydIndex;
                // this.lyList.yfmc = this.barContent.yfmc;
                // this.lyList.ksmc = this.listGetName(this.ksList, this.barContent.ksbm, 'ksbm', 'ksmc');
                // this.lyList.fydh = fydh;
                // this.lyList.fysj = fysj;
                // if (this.type == "0") {
                //     types = "fydhzfymx"; //发药单汇总
                // } else {
                //     types = "fydfymx"; //发药单明细
                // }
                // var pram = {
                //     "ylbm": 'N040030022004',
                //     "ksbm": this.barContent.ksbm, //toolBar在bqby.js内定义
                //     "fylx": this.fylx,
                //     "fycfh": fydh,
                //     'dybz': this.PrintType
                // };
                // if (this.sfdjl != '-1') {
                //     pram.sfdjl = this.sfdjl;
                // }
                // common.openloading();
                // $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywFymx&types=" + types + "&parm=" + JSON.stringify(pram), function (json) {
                //     if (json.a == 0) {
                //         toolBar.lydmxList = json.d.list;
                //         for (var i = toolBar.lydmxList.length - 1; i >= 0; i--) {
                //             toolBar.talAll = toolBar.fDec(parseFloat(toolBar.talAll) + parseFloat(toolBar.fDec(toolBar.lydmxList[i].yplj * toolBar.lydmxList[i].fysl, 2)), 2);
                //             if (toolBar.lydmxList[i].fysl == undefined || toolBar.lydmxList[i].fysl == null || toolBar.lydmxList[i].fysl == '') {
                //                 toolBar.lydmxList.splice(i, 1)
                //             }
                //         }

                //     } else {
                //         malert("发药单查询失败：" + json.c, 'top', 'defeadted');
                //     }
                //     common.closeLoading();
                // });
                // this.getType();
                // this.$forceUpdate()
            }
            ,
//****************************************科室汇总领药单end

//****************************************退药申请start
//点击退药单查看退药单明细
            getTydPersonVal: function (val) {
				                var that = this;
                if (val[0] == 'some') {
                    Vue.set(this.tydIisChecked, val[1], val[2]);
                    if (that.notempty(this.tydIisChecked).length == this[val[3]].length) {
                        this.tydIisChecked.every(function (el) {
                            if (el === true) {
                                return that.tydAll = true
                            } else {
                                return that.tydAll = false
                            }
                        })
                    }
                } else if (val[0] == 'all') {
                    this.tydAll = val[2];
                    if (val[1] == null) val[1] = "jsonList";
                    if (this.tydAll) {
                        for (var i = 0; i < this[val[1]].length; i++) {
                            Vue.set(this.tydIisChecked, i, true);
                        }
                    } else {
                        this.tydIisChecked = [];
                    }
                }
				
				if(this.tydIisChecked.length>0){
					var tptymx = []
					var checkeds = this.tydIisChecked
					for (let i = 0; i < checkeds.length; i++) {
						if(checkeds[i]== true){
							Object.assign(tptymx,this.treeTydList[i].tymx)
						}
					}
					this.getTydPerson(tptymx);
				}
				
				
				
                
            },
            getTydPerson: function (item, index) {
                
				// $('#tydmx').attr('src','http://172.20.103.18:8075/WebReport/ReportServer?reportlet=zyb%2Fzy%2Fzy_tyd.cpt&tydh='+this.treeTydList[index].tysqdh)
				
                toolBar.tydmxList = item;
                
                toolBar.talAll = 0;
                for (var i = 0; i < toolBar.tydmxList.length; i++) {
                    this.isChecked[i] = true;
                }
                for (var i = 0; i < toolBar.tydmxList.length.length; i++) {
                    toolBar.talAll = toolBar.talAll + toolBar.tydmxList.length[i].yplj;
                    console.log(toolBar.talAll);
                }
                if (!toolBar.talAll) {
                    toolBar.talAll = 0;
                }
            }
            ,
//
            getTysqData: function () {
                var pram = {
                    "ylbm": 'N040030022004',
                    "ryksbm": toolBar.barContent.ksbm, //toolBar在bqby.js内定义
                    "beginrq": this.barContent.dateBegin,
                    "endrq": this.barContent.dateEnd,
                    'yfbm': toolBar.barContent.yfbm,
                    'ryyfbm': toolBar.barContent.yfbm,
                };
                //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
                common.openloading();
                $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywBqty&types=tydcx&parm=" + JSON.stringify(pram), function (data) {
                    if (data.a == 0) {
                        toolBar.treeTydList = data.d.list;
                        toolBar.tydmxList = [];
                    } else {
                        toolBar.treeTydList = [];

                        malert("退药单查询失败：" + data.c, 'top', 'defeadted');
                    }
                    common.closeLoading();
                });

            }
            ,
//审核
//             shData: function () {
//             	if (toolBar.tydmxList.length <= 0) {
//                     malert("请选择需要退药审核药品明细!", 'top', 'defeadted');
//                     return;
//                 }
//                 var tyArr = [];
//                 for (var i = 0; i < toolBar.tydmxList.length; i++) {
//                     if (toolBar.isChecked[i]) {
//                     	tyArr.push(toolBar.tydmxList[i]);
//                     }
//                 }
//
//                 var arr = tyArr.reduce((r, x) => ((r[x.tysqdh] || (r[x.tysqdh] = [])).push(x), r), {});
//                 console.log(arr)
//
//                 // 循环退药
//                 common.openloading();
//                 for(var key in arr){
//                 	var list = arr[key]
//                 	var tyid = [];
//                     for (var i = 0; i < list.length; i++) {
//                            tyid.push({"tysqid": list[i].tysqid});
//                     }
//                     var json = {"list": tyid};
//                     console.log(json)
//                     this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywBqty&types=bqtysh&yfbm=' + toolBar.barContent.yfbm, JSON.stringify(json))
//                         .then(function (data) {
//                             if (data.body.a == 0) {
//                                 malert("病区退药审核成功!", 'top', 'success');
//                                 toolBar.getTysqData();
//                             } else {
//                                 malert(data.body.c, 'top', 'defeadted');
//                             }
//                         }, function (error) {
//                             common.closeLoading();
//                             console.log(error);
//                             malert(error, 'top', 'defeadted');
//                         });
//                 }
//                 common.closeLoading();
//                 toolBar.isChecked = [];//取消选择
//             },
            shData: function () {
                var tyid = [], tydh = [];
                for (var i = 0; i < toolBar.tydmxList.length; i++) {
                    if (toolBar.isChecked[i]) {
                        tyid.push({"tysqid": toolBar.tydmxList[i].tysqid});
                        tydh.push(toolBar.tydmxList[i].tysqid)
                    }
                }
                if (tyid.length <= 0) {
                    malert("请选择需要退药审核药品明细!", 'top', 'defeadted');
                    return;
                }
                var json = {"list": tyid};


                toolBar.isChecked = [];//取消选择
                common.openloading();
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywBqty&types=bqtysh&yfbm=' + toolBar.barContent.yfbm, JSON.stringify(json))
                    .then(function (data) {
                        common.closeLoading();
                        if (data.body.a == 0) {
                            malert("病区退药审核成功!", 'top', 'success');
                            toolBar.getTysqData();
                            toolBar.shSpd(tydh);
							common.openConfirm("是否打印退药单？", function () {
								var reportlets ="[{reportlet:'zyb%2Fzy%2Fzy_tyd.cpt',yljgbm:'"+jgbm+"',tydh:'"+tydh+"'}]";
								if(!window.top.J_tabLeft.obj.FRorWindow){
								    if (FrPrint(reportlets,null)){
								        return;
								    }
								}
							})
							
							
                        } else {
                            malert(data.body.c, 'top', 'defeadted');
                        }
                    }, function (error) {
                        common.closeLoading();
                        console.log(error);
                        malert(error, 'top', 'defeadted');
                    });
            },
            shSpd: function (tydh) {
                var orderNumbers = {
                    orderNumbers: tydh
                }
                this.$http.post(window.top.J_tabLeft.obj.spdUrl + '/spd-api/spd/return-medicine', JSON.stringify(orderNumbers)).then(function () {
                    if (data.body.a == 0) {

                    }
                })
            },
        }
    })
;


setTimeout(function () {
    toolBar.refresh();
}, 1000);


//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;

Vue.component('vue-tree-by', {
    props: {
        item: {
            type: Array,
            default: function () {
                return []
            },
        },
        open: {
            type: Array,
            default: function () {
                return []
            },
        },
        $index: {
            type: Number,
            default: function () {
                return undefined
            },
        },
        is_csqx: {
            type: Object,
            default: function () {
                return {}
            },
        },
    },
    template: '<ul><li><div class="flex-container flex-align-c"><span style="width: 10px" class="fa" :class="open[$index]?\'fa-caret-down\':\'fa-caret-right\'"></span>\n' +
        '<div @click="isOpen($index)" class="tree_text1" v-text="item.ksmc"></div>\n' +
        '<div v-if="is_csqx.N04003002200403 == \'1\'" class="cell-m flex-container flex-align-c flex-jus-c zui-table-cell text-center">\n' +
        '<input   class="green" type="checkbox"  v-model="isCheckSldAll">\n' +
        '<label @click="reCheckBoxSld([\'all\',item.sldh,!isCheckSldAll[$index],$index])" @dblclick.stop></label>\n' +
        '</div></div><ul v-show="open[$index]"><li v-for="(itemDh,index) in item.sldh" class="itemCarl flex-container flex-align-c">\n' +
        '<div v-if="is_csqx.N04003002200403 == \'1\'" class="cell-m flex-container flex-align-c flex-jus-c zui-table-cell text-center">\n' +
        '<input   class="green" type="checkbox"  v-model="isCheckedSld[index]">\n' +
        '<label @click="reCheckBoxSld([\'some\',item.sldh,!isCheckedSld[index],index])" @dblclick.stop></label>\n' +
        '</div><div :class="{active_tree:index==isActive}" @click="is_csqx.N04003002200403 == \'0\'?getPerson(itemDh,index,itemDh.sldh):\'\'" class="tree_text1" v-text="itemDh.sldh"></div>\n' +
        '</li></ul></li></ul>',
    data: function () {
        return {
            isCheckSldAll: false,
            isCheckedSld: [],
            sldh: [],
            isActive: undefined,
        }
    },
    created: function () {

    },
    methods: {
        getPerson: function (itemDh, index, sldh) {
            this.isCheckSldAll = false;
            this.isCheckedSld = [];
            this.sldh.push(sldh)
            this.isActive = index;
            this.$emit('get-person', itemDh, index, this.sldh, this.$index)
        },
        reCheckBoxSld: function (val) {
            // this.setCheckSld()
            this.reCheckBoxCommon(val, 'isCheckSldAll', 'isCheckedSld', val[3]);
            if (val[0] == 'all') {
                for (var i = 0; i < val[1].length; i++) {
                    val[1][i].checked = this.isCheckSldAll;
                }
            } else {
                val[1][val[3]].checked = !val[1][val[3]].checked;
                if (!val[1][val[3]].checked) {
                    this.isCheckSldAll = false;
                } else {
                    var checkAllCheck = true;
                    for (var i = 0; i < val[1].length; i++) {
                        if (!val[1][i].checked) {
                            checkAllCheck = false;
                            break;
                        }
                    }
                    if (checkAllCheck) {
                        this.isCheckSldAll = true;
                    }
                }
            }

            this.$parent.getPerson('', val[3], '', this.$index, val)
        },
        setCheckSld: function () {
            for (var i = 0; i < this.$parent.$refs.vueTreeBy.length; i++) {
                for (var j = 0; j < this.$parent.$refs.vueTreeBy[i].item.sldh.length; j++) {
                    this.$parent.$refs.vueTreeBy[i].item.sldh[j].checked = false;
                }
                this.$parent.$refs.vueTreeBy[i].isCheckSldAll = false;
                this.$parent.$refs.vueTreeBy[i].isCheckedSld = [];
            }
        },
        isOpen: function (val) {
            this.$emit('is-open', val)
        },
        reCheckBoxCommon: function (val, checkedAll, checked, index) {
            var that = this;
            if (val[0] == 'some') {
                Vue.set(this[checked], val[3], val[2]);
                if (that.$parent.notempty(this[checked]).length == val[1].length) {
                    this[checked].every(function (el) {
                        if (el === true) {
                            return that[checkedAll][index] = true
                        } else {
                            return that[checkedAll][index] = false
                        }
                    })
                }
            } else if (val[0] == 'all') {
                this[checkedAll] = !this[checkedAll];
                if (val[1] == null) val[1] = "jsonList";
                var all = this[checkedAll]
                if (all) {
                    var list = typeof val[1] == 'string' ? this[val[1]] : val[1]
                    for (var i = 0; i < list.length; i++) {
                        Vue.set(this[checked], i, true);
                        // this.isChecked[i] = true;
                    }
                } else {
                    this[checked] = [];
                }
            }
        },
    },
})
