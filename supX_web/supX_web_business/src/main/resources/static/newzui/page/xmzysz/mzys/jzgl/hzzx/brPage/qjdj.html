<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>抢救登记</title>
    <link href="brPage/lgdj.css" rel="stylesheet">
</head>
<body>
<div id="qjdj" class=" padd-l-10 padd-r-10">
    <div class="flex-container flex-wrap-w flex-align-c">
        <div class=" flex-container flex-align-c padd-b-15 padd-r-10">
            <span class="padd-r-5">登记时间</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" id="djsj" v-model="fDate(popContent.djrq,'datetime')" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-15 padd-r-10">
            <span class="padd-r-5">抢救科室</span>
            <select-input class="wh180" @change-data="resultChange" :not_empty="false" :child="qjksList"
                          :index="'ksmc'" :index_val="'ksbm'" :index_mc="'lgksmc'" :val="popContent.lgks"
                          :name="'popContent.lgks'"
                          :search="true" :phd="''">
            </select-input>
        </div>
        <div class=" flex-container flex-align-c padd-b-15 padd-r-10">
            <span class="padd-r-5">计划时间</span>
            <div class="zui-input-inline wh180">
                <input class="zui-input" v-model="popContent.jhsj" type="text"/>
                <span class="cm">天</span>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-15 padd-r-10">
            <span class="padd-r-5">入室病情</span>
            <select-input @change-data="resultChange" :data-notEmpty="false" class="wh180"
                          :child="rsbq_tran" :index="popContent.rsbq" :val="popContent.rsbq"
                          :name="'popContent.rsbq'">
            </select-input>
        </div>
    </div>
    <div class="flex-container flex-wrap-w flex-align-c">
        <div class=" flex-container flex-align-c padd-b-15 with100">
            <span class="whiteSpace padd-r-5">初&emsp;&emsp;步</br>诊断西医</span>
            <div class="zui-input-inline with100">
                <input class="zui-input height-input" data-notEmpty="false" v-model="popContent.xyzyzdmc"
                       @keydown="changeDown($event,'xy','jbContent','jbsearchCon','mzzdbm','xyzyzd','xyzyzdmc')" @input="change(false,'xy',$event.target.value)"
                       id="mzzdbm" >
                <jbsearch-table :message="jbsearchCon" :selected="selSearch" :page="page" :them="jbthem" @click-one="checkedOneOut"
                                @click-two="jbselectOne">
                </jbsearch-table>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-15 with100">
            <span class="whiteSpace padd-r-5">初&ensp;步&ensp;诊</br>断中医病</span>
            <div class="zui-input-inline with100">
                <input class="zui-input height-input" data-notEmpty="false" v-model="popContent.zyzyzdmc"
                       @keydown="changeDown($event,'zy','zyContent','zysearchCon','mzzdbmZy','zyzyzd','zyzyzdmc')" @input="change(false,'zy',$event.target.value)"
                       id="mzzdbmZy">
                <zysearch-table :message="zysearchCon" :selected="selSearch" :page="page" :them="zythem" @click-one="checkedOneOut"
                                @click-two="jbselectOne">
                </zysearch-table>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-15 with100">
            <span class="whiteSpace padd-r-5">初&ensp;步&ensp;诊</br>断中医症</span>
            <div class="zui-input-inline with100">
                <input class="zui-input height-input" data-notEmpty="false" v-model="popContent.zyzzmc"
                       @keydown="changeDown($event,'zy','zyContent','zysearchCon','mzzdbmZy','zyzzbm','zyzzmc')" @input="change(false,'zy',$event.target.value)">
                <zysearch-table :message="zysearchCon" :selected="selSearch" :page="page" :them="zythem" @click-one="checkedOneOut"
                                @click-two="zyselectOne">
                </zysearch-table>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-15 with100">
            <span class="whiteSpace padd-r-5">抢救目的</span>
            <div class="zui-input-inline with100">
                <input class="zui-input" v-model="popContent.lgmd" placeholder="请输入抢救目的" type="text"/>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-b-15 with100">
            <span class="whiteSpace padd-r-5">特殊说明</span>
            <div class="zui-input-inline with100">
                <textarea class="zui-input" v-model="popContent.tssm" style="height:68px;" placeholder="请输入特殊说明"
                          type="text"></textarea>
            </div>
        </div>
    </div>
    <div class="zui-table-tool flex-container flex-align-c flex-jus-e">
        <button v-waves class="tong-btn btn-parmary-d9" @click="qxdj()">取消抢救</button>
        <button v-waves class="tong-btn btn-parmary   " @click="saveData()">抢救登记</button>
    </div>
</div>
<script type="text/javascript" src="brPage/qjdj.js"></script>
</body>
</html>
