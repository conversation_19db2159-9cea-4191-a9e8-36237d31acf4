var fyxx=new Vue({
    el:'#fyxx',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
    	brfyjsonList:[],
    },
    mounted:function () {
    },
    created: function () {
        this.getData();
    },
    watch:{
    },
    computed:{
        sum:function () {
            if (this.brfyjsonList.length >= 1) {
                var sum = this.brfyjsonList.reduce(function (total, num) {
                    return total + num.fydj * num.fysl
                }, 0)
            }
            return sum == undefined ? 0 : sum.toFixed(2);
        }
    },
    methods:{
    	getData:function(){
    		// 请求后台查询已收费门诊费用（包含退费）
    		this.brfyjsonList = []; //清空未收费信息
            var parm = {
                page: 1,
                rows: 10000,
                sfjs: '1',
                ryghxh: userNameBg.Brxx_List.ghxh,
                //fphm: fphm,
                sort: 'sfsj',
                order: 'desc',
            };
            console.log(parm);
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=query&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                	fyxx.brfyjsonList = json.d.list;
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
    	}


    }
});
