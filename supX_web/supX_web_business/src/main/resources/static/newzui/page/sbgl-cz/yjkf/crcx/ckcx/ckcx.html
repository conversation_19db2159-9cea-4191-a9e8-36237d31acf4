<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出库查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label   margin-f-l10">时间段</label>
                    <div class="zui-input-inline   margin-f-l20">
                        <input class="zui-input todate wh240 text-indent20" placeholder="请选择申请日期"  id="timeVal" @keydown.13="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class=" cell-m"><div class="zui-table-cell cell-m"><span>
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th class=" cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>审核时间</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body "  @scroll="scrollTable($event)" >
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex}]" @dblclick="edit($index)" >
                    <td class=" cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class=" cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">1</div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <i class="title title-width"  :data-title="item.ckdh" v-text="item.ckdh"></i>
                        </div>

                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.shrq,'date')">西药收入</div></td>
                    <td>
                        <div class="zui-table-cell cell-xl">
                            <i class="title"  :data-title="item.ypmc" v-text="item.ypmc"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl">
                            <i class="title"  :data-title="item.ypgg" v-text="item.ypgg"></i>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cksl">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.scph">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cdmc">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbl">药品收入</div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<script src="ckcx.js"></script>

</body>

</html>
