<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>卫生库位</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<style>
    .zui-form .zui-form-label{
        left: 5px;
    }
    .icon-dcc:before{
        left: -5px;
        top: -13px;
    }
    .ksys-side {
        width: 100%;
        padding: 26px 17px;
        float: left;
    }
    .ksys-side .span0  {
        display: block;
        width: 100%;
        position: relative;
    }
    .ksys-side .span0  i {
        display: block;
        width: 100%;
    }
    .tong-search{
     padding: 13px 0 5px 20px;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel ">
        <div class="tong-top">
            <button class="tong-btn btn-parmary  icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dcc icon-dc" >导出</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">卫生</label>
                    <div class="zui-input-inline wh122 margin-l-7">
                        <select-input @change-data="resultRydjChange"
                                      :child="kfList" :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm"
                                      :name="'param.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">卫生名称</label>
                    <div class="zui-input-inline margin-l-5">
                        <input class="zui-input  wh240 " @keydown.enter="goToPage(1)" placeholder="请输入关键字" v-model="param.parm"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl">卫生编码</div></th>
                    <th><div class="zui-table-cell cell-xl">卫生名称</div></th>
                    <th><div class="zui-table-cell cell-s">卫生规格</div></th>
					<th><div class="zui-table-cell cell-s">库房单位</div></th>
					<th><div class="zui-table-cell cell-s">分装比例</div></th>
					<th><div class="zui-table-cell cell-s">卫生单位</div></th>
					<th><div class="zui-table-cell cell-s">库位编码</div></th>
					<th><div class="zui-table-cell cell-s">库位名称</div></th>
					<th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item, $index) in YfkwList"  :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" @dblclick="edit($index)">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
					<td><div class="zui-table-cell cell-xl" v-text="item.ypbm"></div></td>
					<td><div class="zui-table-cell cell-xl" v-text="item.ypmc"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.ypgg"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.fzbl"></div></td>
					<td><div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kwbm"></div></td>
                    <td><div class="zui-table-cell cell-s " v-text="item.kwmc"></div></td>
					<td class="cell-s">
					    <div class="zui-table-cell cell-s">
					        <span class="flex-center padd-t-5">
					            <em class="width30"><i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i></em>
					        </span>
					    </div>
					</td>
                    <p v-if="YfkwList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
		<page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
<div class="side-form ng-hide pop-width"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <span class="span0 padd-b-10">
            <i>库位编码</i>

            <select-input @change-data="resultChange"
                          :child="kwList" :index="'kwmc'" :index_mc="'kwmc'" :index_val="'kwbm'" :val="popContent.kwbm"
                          :name="'popContent.kwbm'" :search="true" :index_mc="'kwmc'" >
                        </select-input>

        </span>
        <span class="span0">
            <i>卫生编码</i>
			 <input id="ypmc" class="zui-input" @input="change(false,$event.target.value)" v-model="popContent.ypmc" @keydown="changeDown($event,'ypmc','searchCon')">
			 <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
			     @click-one="checkedOneOut" @click-two="selectOne">
			 </search-table>

        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<script src="ypkw.js"></script>
</body>

</html>
