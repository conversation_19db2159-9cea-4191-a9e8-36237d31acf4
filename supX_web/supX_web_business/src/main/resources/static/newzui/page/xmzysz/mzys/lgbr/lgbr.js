var panel=new Vue({
    el:'.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        index:0,
        newLocalValue:'', //监听是否需要刷新
        csqxContent: {cs03001200132:"0"}, //参数对象
        ksList: [], //科室下拉
        param:{ //查询条件
        	page: 1,
            rows: 10,
            sort: "",
            order: 'desc',
        	parm: "",
        	lgstart: "",
        	lgend: "",
        	// brgl: '0'
        },
        total:0,
        // mzbrgl_tran:{
        //     '0':'全科病人',
        //     '1':'接诊病人'
        // },
        parm: {},
        jhks:{
        	ghksip:''
        },
        jhksbm:'',
    },

    created:function () {
       // this.$nextTick(function () {
           this.getKsbm();
       // })
    },
    watch:{
        'newLocalValue':function (o,n) {
            // panel.Wf_GetBrList();
        }
    },
    mounted:function(){
    	//初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.param.lgstart = this.fDate(myDate.setDate(myDate.getDate()), 'date')+' 00:00:00';
        this.param.lgend = this.fDate(new Date(), 'date')+' 23:59:59';
    	laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.param.lgstart,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
            	if (value != '') {
                    panel.param.lgstart = value;
                } else {
                    panel.param.lgstart = '';
                }
                //获取一次列表
                panel.Wf_GetBrList(String);
            }
        });
    	laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.param.lgend,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
            	if (value != '') {
                    panel.param.lgend = value;
                } else {
                    panel.param.lgend = '';
                }
                //获取一次列表
                panel.Wf_GetBrList(String);
            }
        });
    },
    methods:{
        tab:function (index) {
            this.index=index;
            zuiItem.index=index;
            kp.index=index;
            changeWin();
            panel.Wf_GetBrList(String);
        },
        getData:function () {
            panel.Wf_GetBrList(String)
        },
        getList:function(){
            var url=window.location.href;
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N030012001',
                "ksbm": this.param.lgks
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {

                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03001200101": //是否强行接诊  0=不允许,1=允许
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100101 = csjson.csz;
                                    }
                                    break;
                                case "N03001200102": //自动获取就诊病人刷新时间0=手动刷新,大于零时为刷新间隔秒数
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100102 = csjson.csz;
                                    }
                                    break;
                                case "N03001200103": //门诊医生站默认药房 （选择默认药房）
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100103 = csjson.csz;
                                    }
                                    break;
                                case "N03001200104": //是否必须录入门诊诊断0=否，1＝是
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100104 = csjson.csz;
                                    }
                                    break;
                                case "N03001200105": //门诊医生接诊范围0=本人，1=全院，2=全科
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100105 = csjson.csz;
                                        if(csjson.csz == "1"){
                                            panel.mzbrgl_tran["2"] = "全院病人";
                                        }
                                    }
                                    break;
                                case "N03001200106": //电子处方默认处方类型（输入药品处方类型编码）
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100106 = csjson.csz;
                                    }
                                    break;
                                case "N03001200107": //是否强行限定药品处方位数0=否,1=是
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100107 = csjson.csz;
                                    }
                                    break;
                                case "N03001200108": //病人信息修改权限0=否,1=是
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100108 = csjson.csz;
                                    }
                                    break;
                                case "N03001200109": //甘肃省疾病普排序接口0=否 1=是(接诊按接口规范录入病人信息和门诊诊断)  2=是(接诊时不强制输入，以后再补)
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100109 = csjson.csz;
                                    }
                                    break;
                                case "N03001200110": //保存电子处方时是否打印0=无，1=有
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100110 = csjson.csz;
                                    }
                                    break;
                                case "N03001200111": //是否限制一个挂号序号只能开一张处方0-不限定,1限定
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100111 = csjson.csz;
                                    }
                                    break;
                                case "N03001200112": //申请单的打印方式0-不打印，1-提示打印，2-直接打印
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100112 = csjson.csz;
                                    }
                                    break;
                                case "N03001200113": //保存时是否签名0=否，1=是
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100113 = csjson.csz;
                                    }
                                    break;
                                case "N03001200114": //开电子申请单方式1-医嘱,2-项目格式
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100114 = csjson.csz;
                                    }
                                    break;
                                case "N03001200115": //中医处方是否必须填写主症、治法	0-否，1-是
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100115 = csjson.csz;
                                    }
                                    break;
                                case "N03001200116": //处方金额是否允许为零0-不允许，1-允许
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100116 = csjson.csz;
                                    }
                                    break;
                                case "N03001200117": //急诊挂号对应急诊处方（急诊挂号对应急诊处方）
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100117 = csjson.csz;
                                    }
                                    break;
                                case "N03001200118": //中药处方数量允许输入小数0=不允许 1=允许
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100118 = csjson.csz;
                                    }
                                    break;
                                case "N03001200119": //是否允许开非药品处方0=否 1=是
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100119 = csjson.csz;
                                    }
                                    break;
                                case "N03001200120": //中药煎药袋参数
                                    if (csjson.csz) {
                                        panel.csqxContent.cs00600100120 = csjson.csz;
                                    }
                                    break;
                                case "N01006400108": //合理用药接口地址参数
                                    if (csjson.csz) {
                                        panel.csqxContent.cs01006400108 = csjson.csz;
                                    }
                                 case "N03003200118": //合理用药接口地址参数
                                    if (csjson.csz) {
                                        panel.csqxContent.cs003003200118 = csjson.csz;
                                    }
                                    break;
                                case "N03003200126": //是否对抗肿瘤药品使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200126 = csjson.csz;
                                    }
                                    break;
                                case "N03003200127": //是否对药品种类中成药使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200127 = csjson.csz;
                                    }
                                    break;
                                case "N03001200125": //甘肃双向转诊门诊转出参数

                                    if (csjson.csz&&csjson.csz=='1') {
                                        kp.isShow=true;
                                    }
                                    break;
                                case "N03001200131": //排队叫号请求地址
                                    if (csjson.csz) {
                                        panel.csqxContent.N03001200131 = csjson.csz;
                                    }
                                    break;
                                case "N03001200132": //门诊用药使用服药剂量
                                    if (csjson.csz) {
                                        panel.csqxContent.cs03001200132 = csjson.csz;
                                    }
                                    break;
                                case "N03001200133": //排队叫号方式
                                    if (csjson.csz) {
                                        panel.csqxContent.N03001200133 = csjson.csz;
                                        if(sessionStorage.getItem('jhks')){
                                            panel.jhks =  JSON.parse(sessionStorage.getItem('jhks'));
                                            if(panel.jhks && panel.jhks.ghksip){
                                                panel.jhksbm = panel.jhks.ksbm;
                                            }
                                        }
                                    }
                                    break;
                                case "N03001200138": //门诊医生跳转补挂号
                                	if (csjson.csz) {
                                		panel.csqxContent.N03001200138 = csjson.csz;
                                	}
                                	break;
                            }
                        }
                    }
                } else {
                    malert('参数权限获取失败'+json.c,'top','defeadted')
                }
            });
        },

        //获取当前操作员的拥有科室权限
        getKsbm: function () {
            common.openloading('.wrapper');
            var str_param = {
                ylbm: "N030012001"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(str_param), function (json) {
                if (json.a == 0 && json.d!=null) {
                    if (json.d.length > 0) {
                        //限制为急诊科
                        panel.ksList = json.d.filter(item=>item.ksbm==='0922');
                        panel.param.lgks=panel.ksList[0].ksbm;
                        setTimeout(function () {
                            panel.getCsqx();//科室记载完成之后再加载参数权限
                            setTimeout(function () {
                                panel.Wf_GetBrList(undefined); //加载完成后自动获取就诊病人列表
                            }, 100);
                        }, 100);

                    }
                } else {
                    malert('获取科室失败','top','defeadted')
                }
            });
        },

        //科室改变时
        ksChange: function (val) {
        	//先获取住院医生的值
            panel.getCsqx();//科室改变之后再加载参数权限
            this.Wf_GetBrList(val);
            kp.brk_list();
        },

      //科室改变时查询出叫号科室的ip
        queryKsip: function (val) {
        	 $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryKsbm&types=queryOne&ksbm="
        			 + val[0], function (json) {
                 if (json.a == 0 && json.d!=null) {
                    if(json.d.ghksip){
                    	var r = confirm("是否设置"+ json.d.ksmc + "为叫号科室？")
                    	  if (r==true){
                    		  malert('设置成功');
                    		  panel.jhksbm = val[0];
                    		  sessionStorage.setItem('jhks',JSON.stringify(json.d));
                    	    } else{
                    	    	malert('设置失败，请联系管理员！','top','defeadted')
                    	    }
                    }else{
                    	panel.jhksbm = val[0];
              		  	sessionStorage.setItem('jhks',JSON.stringify('{}'));
                    	malert('未设置科室ip，请联系管理员！','top','defeadted')
                    }
                 } else {
                     malert('获取科室ip失败，请联系管理员！','top','defeadted')
                 }
             });
        },
        toGh: function(){
            sessionStorage.setItem('bgh','0')
        	this.topNewPage('挂号退号','page/ghgl/ghyw/brgh/brgh.html');
        },
        //获取病人信息
        Wf_GetBrList: function (val) {

            zuiItem.Brxx_List = [];
            if(val)kp.Brxx_List=[]
            kp.brk_listD =0;
        	if(typeof val =='object'){
                this.param.page=1
                if(val[2][1]=='lgks'){
                    Vue.set(this.param, 'lgks', val[0]);
                }
        	}
            // if(this.param.brgl == "2"){
            //     Vue.set(this.csqxContent,"cs00600100105","1");
            // }else if(this.param.brgl == "0"){
            //     Vue.set(this.csqxContent,"cs00600100105","2");
            // }else{
            //     Vue.set(this.csqxContent,"cs00600100105","0");
            // }
            this.$forceUpdate();
            //门诊医生接诊范围0=本人，1=全院，2=全科
            // if (this.csqxContent.cs00600100105 == '0') {
            //     this.param.jzys = userId;
            //     // this.param.ghks=this.param.ksbm;
            //     this.param.lgks=this.param.ksbm;
            // }else if(this.csqxContent.cs00600100105 == '1'){
            //     this.param.jzys =null;
            //     // this.param.ghks=null;
            //     this.param.lgks=this.param.ksbm;
            // }else if(this.csqxContent.cs00600100105 == '2'){
            // 	this.param.jzys =null;
            //     // this.param.ghks=this.param.ksbm;
            //     this.param.lgks=this.param.ksbm;
            // 	if(this.param.brgl=='1'){
            // 		this.param.jzys = userId;
            // 	}
            // }

            // this.param.lgks=this.param.ksbm;
            this.param.rows= this.index ? zuiItem.param.rows :kp.param.rows ;
            this.param.sort='ghrq';
            this.param.order='desc';
            this.param.lgflag = "1";
            $.ajax({
                type:'get',
                url:'/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryJzlb&parm=' + JSON.stringify(this.param),
                async:true,
                dataType:'json',
                success:function(json){
                    if (json.a == 0 && json.d!=null) {
                        kp.scollType=true
                        kp.isDoneCb=false;
                        panel.total=json.d.total;
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                //把时间戳改改时间
                                json.d.list[i].ghrq = formatTime(json.d.list[i].ghrq, "datetime");
                                json.d.list[i].qhrq = formatTime(json.d.list[i].qhrq, "datetime");
                                //判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                                if(json.d.list[i].brnl<7&&json.d.list[i].brxb=='1'){
                                    json.d.list[i].nljd='1';
                                }else if(json.d.list[i].brnl<7&&json.d.list[i].brxb=='2'){
                                    json.d.list[i].nljd='2';
                                }else if(json.d.list[i].brnl<18&&json.d.list[i].brnl>6&&json.d.list[i].brxb=='1'){
                                    json.d.list[i].nljd='3';
                                }else if(json.d.list[i].brnl<18&&json.d.list[i].brnl>6&&json.d.list[i].brxb=='2'){
                                    json.d.list[i].nljd='4';
                                }else if(json.d.list[i].brnl<41&&json.d.list[i].brnl>17&&json.d.list[i].brxb=='1'){
                                    json.d.list[i].nljd='5';
                                }else if(json.d.list[i].brnl<41&&json.d.list[i].brnl>17&&json.d.list[i].brxb=='2'){
                                    json.d.list[i].nljd='6';
                                }else if(json.d.list[i].brnl<66&&json.d.list[i].brnl>40&&json.d.list[i].brxb=='1'){
                                    json.d.list[i].nljd='7';
                                }else if(json.d.list[i].brnl<66&&json.d.list[i].brnl>40&&json.d.list[i].brxb=='2'){
                                    json.d.list[i].nljd='8';
                                }else if(json.d.list[i].brnl>65&&json.d.list[i].brxb=='1'){
                                    json.d.list[i].nljd='9';
                                }else if(json.d.list[i].brnl>65&&json.d.list[i].brxb=='2'){
                                    json.d.list[i].nljd='10';
                                }else{
                                    json.d.list[i].nljd='11';
                                }
                                //状态
                                if(json.d.list[i].jzbz=='0'&&json.d.list[i].wcbz=='0'){
                                    json.d.list[i].zt='0';
                                }else if(json.d.list[i].jzbz=='1'&&json.d.list[i].wcbz=='0'){
                                    json.d.list[i].zt='1';
                                }else if(json.d.list[i].wcbz=='1'){
                                    json.d.list[i].zt='2';
                                }
                                //获取时间
                                var times=getxcsfm(json.d.list[i].ghrq);
                                json.d.list[i].hh=times.hh;
                                json.d.list[i].mm=times.mm;
                                json.d.list[i].ss=times.ss;
                            }
                        }
                        if(panel.param.parm==64){
                            zuiItem.userGet(json.d.list[0],['brPage/brjz',0,json.d.list[0]])
                        }
                        zuiItem.Brxx_List = json.d.list;
                        zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
                        kp.brk_list(json.d.list.length);
                        kp.Brxx_List=kp.Brxx_List.concat(json.d.list);
                        common.closeLoading()
                    } else {
                        common.closeLoading()
                        kp.isDoneCb=false;
                        malert('获取挂号列表失败'+json.c,'top','defeadted')
                    }
                },
                error:function(response){
                    malert('获取挂号列表失败','top','defeadted')
                }
            });
        },
    },
});
var orignalSetItem=sessionStorage.setItem;
sessionStorage.setItem=function (key,newValue) {
    var setItemEvent=new Event('setItemEvent');
    setItemEvent.newValue=newValue;
    window.dispatchEvent(setItemEvent);
    orignalSetItem.apply(this,arguments);
};
window.addEventListener('setItemEvent', function (e) {
    Vue.set(panel,'newLocalValue',e.newValue);
    console.log(panel.newLocalValue)
});
window.addEventListener('storage',function (e) {
    Vue.set(panel,'newLocalValue',e.newValue)
});


window.onload=function () {
    panel.getList();
};

//列表信息
var zuiItem=new Vue({
    el:'.zui-item',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        index:0,
        Brxx_List:[],
    },
    updated:function(){
        changeWin()
    },
    methods:{
        goBre:function (list,val) {
          kp.userGet(list,val);
        },
    },
});

//卡片信息
var kp=new Vue({
    el:'.kp',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        index:0,
        loadData:'',
        isShow: false,
        isDoneCb:false,
        scollType:true,
        brk_listD:0,
        Brxx_List:[],
        csContent:{},
        param:{
        	page:1,
        	rows:30,
        	sort:'',
        	order:'asc'
        }
    },
    methods:{
        openUrl:function (list) {
            window.open(window.top.J_tabLeft.obj.xwpacsdz+"/ClinicList.aspx?colid0=3078&colvalue0="+list.ghxh+"");
        },
        loadingData:function (event) {
            if(event.wheelDelta>0)return
            if(this.scollType){
                this.scrollTop=this.$refs.kp.scrollTop
                this.scollType=false
                if(this.Brxx_List.length<panel.total){
                    if(this.uilPageBottom()){
                        panel.isflag=false;
                        panel.param.page=panel.param.page+1;panel.Wf_GetBrList(undefined,1);
                    }
                }else {
                    this.loadData='暂无更多数据...'
                }
            }
        },
        sxzz:function(list){

            var param = {
                brid: list.brid,
                xh: list.ghxh,
                bz: '1',
                yljgbm: list.yljgbm
            }

            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryZcHzxx&&parm=" + JSON.stringify(param), function (json) {

                if (json.a == 0) {
                    kp.askJson = JSON.parse(json.d);
                    if(!kp.askJson.YWSJ.JBXX.ZJHM){
                        malert('身份证不能为空','top','defeadted')
                        return;
                    }
                    kp.callByRestful();
                }


            });
        },
        callByRestful() {
            var jsonPara={
                'YWJSON': kp.askJson
            }
            $.ajax({
                type:'post',
                url: 'http://127.0.0.1:8081/open',
                data:JSON.stringify(jsonPara),
                contentType:'application/x-www-form-urlencoded',
                beforeSend: function(xhr) {
                },
                dataType:"json",
                success:function(data){
                    malert(data.YWXT.MSG)
                }
            });
        },
        jhcz:function(list){
        	var obj ={
        			ghxh:list.ghxh,
        			brxm:list.brxm,
        			ghks:list.ghks,
        			ghksmc:list.ghksmc,
        			jzys:list.jzys,
        			jzysxm:list.jzysxm,
        			ghksip: panel.jhks.ghksip,

        	};
        	$.ajax({
                type:'post',
                url:panel.csqxContent.N03001200131 + '/callNumber',
                data: JSON.stringify(obj),
                async:true,
                dataType:'json',
                contentType: "application/json;charset=utf-8",
                success:function(json){
                	if(json.code == 0){
                		malert(json.msg)
                	}else{
                		malert(json.msg,'top','defeadted')
                	}
                },
                error:function(response){
                    malert('叫号失败','top','defeadted')
                }
            });
        },
        userGet:function (list,val) {
            this.topNewPage(list.brxm+'-留观','page/xmzysz/mzys/jzgl/hzzx/hzzx.html');
            val[2].fypcfShow=panel.csqxContent.cs00600100119; //非药品处方是否显示
            val[3] = panel.csqxContent;
            sessionStorage.setItem('brPage',JSON.stringify(val))
        },
        userGet1:function (list,val) {
            // this.topNewPage(list.brxm+'-留观','page/xmzysz/mzys/jzgl/hzzx.html');
            this.topNewPage(list.brxm+'-留观','page/xmzysz/mzys/jzgl/hzzx/lgyz.html');

            val.push(list.ksbm);
            val[2].ksbm=list.ksbm
            val.push('留观');
            sessionStorage.setItem('brPage1',JSON.stringify(val))
        },
        //电子处方
        userGetDz:function (list,val) {
            var time=panel.csqxContent.N03001200159 == '1' ? list.ghxh:'1';
            this.topNewPage(list.brxm+'-留观','page/xmzysz/mzys/jzgl/hzzx/hzzx.html?ghxh='+time);
            val[2].fypcfShow=panel.csqxContent.cs00600100119; //非药品处方是否显示
            val[2].ksbm = panel.param.ksbm;
            val[2].ksmc = this.listGetName(panel.ksList, panel.param.ksbm, 'ksbm', 'ksmc');
            val[3] = panel.csqxContent;
            sessionStorage.setItem('brPage'+time,JSON.stringify(val))
        },
        //电子病历
        userdzbl:function(list){
           console.log(list);
           if(!list.brid || !list.ghxh){
           		malert("请选择患者!",'top','defeadted');
           		return;
           };
            //写注册信息
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + list.brid+"&json=" + JSON.stringify(kp.param), function (json) {
                    if (json.a == "0") {
                        //写就诊信息
                        $.getJSON(
    	                "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=M" + list.ghxh+"&json=" + JSON.stringify(kp.param), function (json) {
    	                    if (json.a == "0") {
    	                        $.ajaxSettings.async = false;
					            var sxdz = "";
					            var user = "";
					            var password = "";
					            //取病历参数
					            $.getJSON("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(kp.param), function (json) {
					                if (json.a == "0") {
					                	kp.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
					                    sxdz = kp.csContent.blSxdz;
					                    user = userId;
					                    //取操作员信息
					                    $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
					                        if (json.a == "0") {
					                        	password=json.d.password;
					                        }
					                    });
					                    if (!sxdz) {
					                        malert("书写地址为空，打开病历失败！",'top','defeadted');
					                        return
					                    }
					                    if (!user) {
					                        malert("用户名为空，打开病历失败！！",'top','defeadted');
					                        return
					                    }
					                    if (!password) {
					                        malert("用户密码为空，打开病历失败！",'top','defeadted');
					                        return
					                    }
					                    var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=M" + list.ghxh + ",userid=" + user + ",password=" + password + ",lyzyhmz=1,blhhl=0"; //0医生 1 护理
					                    window.open(url);
					                }
					              });

    	                    } else {
	    	                        malert("患者信息上传失败失败："+json.c,'top','defeadted')
	    	                    }
	    	                });


                    } else {
                        malert("患者信息上传失败失败："+json.c,'top','defeadted')
                    }
            });
        },
        brk_list:function (list) {
            if(this.index==0){
                this.brk_listD=parseInt(this.$refs.kp.offsetWidth/307)-(this.Brxx_List.length%parseInt(this.$refs.kp.offsetWidth/307))
            }
            // if(list>5){
            // }
        },
    },
});
$(window).resize(function () {
    kp.brk_list();
});
kp.brk_list();
//当前时间与过去时间相差的时分秒
function getxcsfm(enddate){
    var times1=new Date();
    var times2=new Date(Date.parse(enddate));
    var date=times1-times2;
    var hoursRound = Math.floor(date /1000 /60 /60);
    var minutesRound=Math.floor(date /1000 /60 -(60*hoursRound));
    var secondRound=Math.floor(date /1000  -(60*60*hoursRound)-(60 * minutesRound));
    if(hoursRound<=0){
    	hoursRound=0;
    }
    if(minutesRound<=0){
    	minutesRound=0;
    }
    if(secondRound<=0){
    	secondRound=0;
    }
    var resulttime={
        hh: hoursRound,
        mm: minutesRound,
        ss: secondRound
    };
    return resulttime;
}

