var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ksslhc:{},
        popContent: {},
        queryData: {},
        kfList: [],
        ryList: [],
        ksList: [],
        zyksList: [],
        mxList: [],
        tjbtn: false,
        shbtn: false,
    },
    updated: function () {
        changeWin()
    },
    mounted:function(){
        this.initial();
        this.getCgry();
        this.getKfmc();
        // this.GetZyksData();
        this.getGhdw();
    },
    methods: {
        sh: function () {
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywSld&types=passDj', JSON.stringify(this.popContent))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.cancel()
                        malert("审核成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
        // 提交所有
        submitAll: function () {
            malert('提交所有', 'top', 'success')
            var sld = {}
            Vue.set(sld, 'kfbm', this.popContent.kfbm);//库房编码
            Vue.set(sld, 'jbry', this.popContent.rybm);//经办人员
            Vue.set(sld, 'ksbm', this.popContent.ksbm);//发票号码
            Vue.set(sld, 'bzms', this.popContent.bzms);//备注描述
            var Sbglbms = this.listGetName(this.kfList, this.popContent.kfbm, 'Sbglbm', 'ksbm');
            //新增操作
            var obj = {
                list: {
                    dj: sld,
                    djmx: this.mxList,
                    Sbgl: this.popContent.kfbm
                }
            }
            if(JSON.stringify(wrapper.ksslhc)=='{}'){
                wrapper.ksslhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
            }else {
                if (JSON.stringify(wrapper.ksslhc)==JSON.stringify(obj)){
                    malert("科室申领单已经保存请不要重复提交!", 'top', 'defeadted');
                    return false;
                }else {
                    wrapper.ksslhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
                }
            }
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywSld&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.cancel()
                        malert("保存成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        // 取消2018/07/09取消回退上一级页面
        cancel: function () {
            // malert('取消','top','defeadted')
            this.topClosePage('page/sbgl/kfyw/ksslgl/ksslkd.html', 'page/sbgl/kfyw/ksslgl/ksslgl.html');
            window.top.$("#科室申领管理")[0].contentWindow.getData();
        },
        // 编辑
        edit: function (index) {
            console.log(1);
            pop.title = '编辑设备'
            pop.popContent = this.mxList[index];
            pop.open();

        },
        // 删除2018/07/09二次弹窗删除提示
        remove: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.mxList.splice(index, 1);
            })) {
                return false;
            }
            // kd.mxList.splice(index, 1);
        },
        // 新增
        AddMdel: function () {
            pop.title = '添加设备'
            pop.open();
            pop.popContent = {};
        },
        //初始化
        initial: function () {
            this.queryData = JSON.parse(sessionStorage.getItem('obj'));
            this.popContent.ksbm = this.queryData.ksbm;
            this.zyksList = this.queryData.zyksList;
            // this.popContent.rybm = this.queryData.sljh.jbry;
            if (this.queryData.sh || this.queryData.dy) {
                //审核
                this.popContent = Object.assign(this.popContent,this.queryData.sljh);
                this.getMx();
            }

        },
        getMx: function () {
            var obj = {
                sldh: this.queryData.sljh.sldh,
                ksbm: this.popContent.ksbm,
                kcXtph: this.popContent.kcXtph
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywSld&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 &&  data.d.length !=0) {
                        wrapper.mxList = data.d
                        console.log(3321);
                        // for (var  i = wrapper.mxList.length - 1; i >=0; i--) {
                        //     if(wrapper.mxList[i].kcsl <=0){
                        //         wrapper.mxList.splice(i,1)
                        //     }
                        // }
                    } else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载库房名称
        getKfmc: function () {
            // 请求库房的api
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=sbkf', function (data) {
                if (data.a == 0) {
                    wrapper.kfList = data.d.list;
                    pop.sbglList = data.d.list;
                    wrapper.popContent.kfbm = data.d.list[0].sbkfbm;
                    /*Vue.set(wrapper.popContent,'Sbglbm',data.d.list[0].Sbglbm);
                    kcList.getData();*/
                } else {
                    malert("获取库房列表失败");
                }
            });
        },
        //加载科室
        GetZyksData: function () {
            var bean = {"zyks": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    // wrapper.zyksList = json.d.list;
                    wrapper.ksList = json.d.list;
                    // wrapper.popContent.ksbm = json.d.list[0].ksbm;
                } else {
                    malert(json.c, "住院科室列表查询失败");
                    return;
                }
            });
        },
        //加载经办人员
        getCgry: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    if (data.a == 0) {
                        wrapper.ryList = data.d.list
                        wrapper.popContent.rybm =wrapper.queryData.sljh && wrapper.queryData.sljh.jbry || data.d.list[0].rybm;
                    } else {
                        malert("获取经办人员失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载供货单位
        getGhdw: function () {
            //初始化页面记载供货单位
            var parm = {
                page: 1,
                rows: 20000,
                sort: 'dwbm',
                tybz: '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        pop.ghdwList = json.d.list;
                    } else {
                        malert("供货单位获取失败", 'top', 'defeadted');
                    }
                });
        },
    }

});


var pop = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        title: '',
        num: 0,
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '设备名称': 'sbmc',
            '设备规格': 'sbgg',
            '设备单位': 'kfdw',
            '进价': 'jj',
            '库存数量': 'kcsl',
            '产地': 'cd',
        },
        searchCon: [],
        selSearch: -1,
        popContent: {},
        ghdwList: [],
        sbglList: [],
        ksbmList: [],
        slksList: [],
        total: 0,
    },
    methods: {
        // 关闭
        // 关闭
        closes: function () {
            this.num = 0;

        },
        open: function () {
            this.num = 1;
            // 设置库房
        },
        //保存
        save: function () {
            if (pop.title == '添加设备') {
                //先判断申领数不能够大于库存数
                if (pop.popContent.kcsl < pop.popContent.slsl) {//如果库存数量小于申领数量则不能够保存
                    malert("该批次库存数小于申领数量请合理分配谢谢！", 'top', 'defeadted');
                    return;
                }
                if(pop.popContent.slsl){

                }

                //添加
                if (wrapper.mxList.length == 0) {
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }
                for (var i = 0; i < kd.mxList.length; i++) {
                    if (wrapper.mxList[i].wzbm == pop.popContent.wzbm && wrapper.mxList[i].scph == pop.popContent.scph) {
                        malert("已有该批号的设备！", 'top', 'defeadted');
                        return;
                    }
                }

                wrapper.mxList.push(pop.popContent);
                pop.popContent = {};
                return;

            } else if (pop.title == '编辑设备') {
                if (wrapper.mxList.length == 0) {
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }
                //编辑保存成功关闭弹窗
                pop.popContent = {};
                pop.closes();
                return;
            }
        },

        //药品名称下拉table检索数据
        changeDown: function (event, type) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                $("#rksl").focus();
            }
        },
        //当输入值后才触发
        change: function (event, type, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var parm = {
                sbkf: wrapper.popContent.kfbm,
                page: pop.dg.page,
                rows: pop.dg.rows,
                parm: val
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=kfkc' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    pop.searchCon = data.d.list;
                    //wap.total = data.d.total;
                    pop.selSearch = 0;
                    $(".selectGroup").show();
                });

            this.popContent[type] = val;
            if (wrapper.popContent["kfbm"] == undefined || wrapper.popContent["kfbm"] == null || wrapper.popContent["kfbm"] == "") {
                malert("库房不能为空", 'top', 'defeadted');
                return;
            }
            if (wrapper.popContent["rybm"] == undefined || wrapper.popContent["rybm"] == null || wrapper.popContent["rybm"] == "") {
                malert("经办人不能为空", 'top', 'defeadted');
                return;
            }
        },
        //双击选中下拉table
        selectOne: function (item) {

            //查询下页
            if (item == null) {
                //分页操作

                pop.dg.page++;
                var parm = {
                    page: pop.dg.page,
                    rows: pop.dg.rows,
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=wzzd&json=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                pop.searchCon.push(data.d.list[i]);
                            }
                            pop.total = data.d.total;
                            pop.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }

            this.popContent = item;
            $(".selectGroup").hide();
        },


    }
});

laydate.render({
    elem: '.sctimes'
    , eventElem: '.icon-rl'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {

        // wrapper.param.time = value
    }
});
laydate.render({
    elem: '.yxtimes'
    , eventElem: '.icon-rl'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        pop.popContent.yxqz = value;
        wrapper.param.time = value
    }
});
