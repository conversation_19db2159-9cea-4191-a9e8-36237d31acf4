//权限科室编码
var qxksbm = '';
//盘点生成页面vm
var toolMenu_0 = new Vue({
	el: '.toolMenu_0',
	isSubmited: false, //是否禁止提交
	data: {
		//分页信息
		dg: {
			page: 1,
			rows: 2000,
			sort: "",
			order: "asc",
		},
	},

	methods: {
		//生成盘点表
		add: function() {
			//清空库存明细列表
			enter.jsonList = [];
			if(enter.hasWhpdb != '') {
				malert(enter.hasWhpdb);
				return;
			}
			//库房非空判断
			if(enter.pdbContent.kfbm == 0) {
				malert("请先选择库房！");
				return;
			}
			//物资种类非空
			if(enter.pdbContent.pdfs == 1 && (enter.pdbContent.ypzl == null || enter.pdbContent.ypzl == undefined || enter.pdbContent.ypzl == '')) {
				malert("请先选择物资种类！");
				return;
			}
			//单种物资非空
			if(enter.pdbContent.pdfs == 2 && (enter.pdbContent.ypmc == null || enter.pdbContent.ypmc == undefined || enter.pdbContent.ypmc == '')) {
				malert("请先选择单种物资！");
				return;
			}
			malert("正在生成盘点表，请稍后！");
			//准备参数
			var bean = {
				"wzkf": enter.pdbContent.kfbm,
				//盘点标志 盘点标志为1显示0库存
				'pdbz': 1
			};
			//按种类
			if(enter.pdbContent.pdfs == 1) {
				bean.lbbm = enter.pdbContent.ypzl;
			} else if(enter.pdbContent.pdfs == 2) {
				bean.wzbm = enter.pdbContent.ypmc;
			}
			bean.rows = 10000;
			//获取库存明细列表
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywCkd&types=queryWzList&parm=' + JSON.stringify(bean),
				function(json) {

					if(json != null || json.d.list.length > 0) {
						for(var i = 0; i < json.d.list.length; i++) {
							//计算零价金额
							json.d.list[i].ljje = Math.round(json.d.list[i].dj * json.d.list[i].kcsl * 100) / 100;
							//设置实存数量
							json.d.list[i].scsl = 0;
						}
						enter.jsonList = json.d.list;
					} else {
						malert("物资信息获取失败！");
						return;
					}
				});
		},
		//保存盘点表
		save: function() {
			//是否禁止提交
			if(this.isSubmited) {
				malert("数据提交中，请稍候！");
				return;
			}
			//库房非空判断
			if(enter.pdbContent.kfbm == 0) {
				malert("请先选择库房！");
				return;
			}
			//明细非空判断
			if(enter.jsonList.length == 0) {
				malert("没有可以提交的数据！");
				return;
			}
			//是否禁止提交
			this.isSubmited = true;

			//准备盘点表参数
			var pdb = {
				'wzkf': enter.pdbContent.kfbm,
				'bzms': '盘点管理',
				'qrzfbz': '0'
			};
			var json = {
				list: {
					'dj': pdb,
					'djmx': enter.jsonList
				}
			};

			this.$http.post('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=saveBatch', JSON.stringify(json))
				.then(function(data) {
					malert(data.body.c);
					//是否禁止提交
					this.isSubmited = false;
					//清空页面
					toolMenu_0.clearAll();
				}, function(error) {
					//是否禁止提交
					this.isSubmited = false;
					console.log(error);
				});
		},
		//清空盘点表生成页面
		clearAll: function() {
			enter.jsonList = [];
			enter.selectPdfs = '0';
			enter.ypzlShow = false;
			enter.ypmcShow = false;
		}
	}
});

var enter = new Vue({
	el: '.enter',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	data: {
		pdWay: 0,
		//是否存在未审核盘点表
		hasWhpdb: '',
		jsonList: [],
		//库房列表
		KFList: [],
		//参数属性
		csParm: {},
		//盘点单
		pdbContent: {},
		//显示物资种类下拉框
		ypzlShow: false,
		ypmcShow: false,
		//库存明细列表复选框
		isChecked: [],
		//盘点方式
		selectPdfs: '0',
		//盘点方式下拉框
		options: [{
				text: '全部物资',
				value: '0'
			},
			{
				text: '种类物资',
				value: '1'
			},
			{
				text: '单种物资',
				value: '2'
			},
		],
		//盘点类型子菜单列表
		infoList: [],
	},

	//启动加载
	mounted: function() {
		//加载时获取库房信息
		this.getKFData();
		//设置库房默认值
		this.pdbContent.kfbm = 0;
	},
	methods: {
		//获取科室权限
		getCsqx: function() {
			var parm = {
				"ksbm": qxksbm,
				"ylbm": "012001007"
			};
			//获取科室权限
			$.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function(json) {
				if(json.a == 0) {
					if(json.d.length > 0) {
						//药库盘点表生成权限，1-有，0-无
						for(var i = 0; i < json.d.length; i++) {
							if(json.d[i].csqxbm == '00200100601') {
								if(json.d[i].csz == '1') {
									//代码未添加
								} else {
									//代码未添加
								}
							}
							//药库盘点表作废权限，1-有，0-无
							if(json.d[i].csqxbm == '00200100602') {
								if(json.d[i].csz == '0') {
									//代码未添加
								} else {
									//代码未添加
								}
							}
							//药库盘点数据录入权限，1-有 0-无
							if(json.d[i].csqxbm == '00200100603') {
								if(json.d[i].csz == '0') {
									//代码未添加
								} else {
									//代码未添加
								}
							}
							//药库盘点录入增加物资权限，1-有 0-无
							if(json.d[i].csqxbm == '00200100604') {
								if(json.d[i].csz == '0') {
									//代码未添加
								} else {
									//代码未添加
								}
							}
							//药库盘点录入单审核权限，1-有 0-无
							if(json.d[i].csqxbm == '00200100605') {
								if(json.d[i].csz == '0') {
									//代码未添加
								} else {
									//代码未添加
								}
							}
							//药库盘点录入单作废权限，1-有 0-无
							if(json.d[i].csqxbm == '00200100606') {
								if(json.d[i].csz == '0') {
									//代码未添加
								} else {
									//代码未添加
								}
							}
							//药库盘点表确认权限，1-有 0-无
							if(json.d[i].csqxbm == '00200100607') {
								if(json.d[i].csz == '0') {
									//代码未添加
								} else {
									//代码未添加
								}
							}
						}
					}
				}
			});

		},

		//库房改变后重新获取权限科室编码，保存盘点表后刷新库房列表也可触发此方法
		opChange: function(event, type) {
			if(type == 'ksbm') {
				//设置库房编码
				document.getElementById('_kfbm').value = enter.pdbContent.kfbm;
				var obj = event.currentTarget;
				var selected = $(obj).find("option:selected");
				qxksbm = selected.attr(type) == undefined ? qxksbm : selected.attr(type); //获取科室编码
				this.getCsqx(); //选中库房之后再次请求获取参数权限

				//判断是否存在未审核的盘点表
				var parm = {
					'wzkf': enter.pdbContent.kfbm,
					'qrzfbz': 0
				};
				//查询盘点表
				$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryDj&parm=' + JSON.stringify(parm), function(json) {
					if(json != null && json.a == 0) {
						var str = '';
						if(json.d.length > 0) {
							for(var i = 0; i < json.d.length; i++) {
								str += ('【' + json.d[i].pdpzh + '】 ');
								enter.hasWhpdb = '盘点表' + str + '未审核';
							}
						} else {
							enter.hasWhpdb = '';
						}
					} else {
						malert('查询未审核盘点表失败！')
					}
				});
				//判断是否存在未审核的盘点表end

			} else if(type == 'zlbm') {
				//获取物资种类编码
				enter.pdbContent.zlbm = event.currentTarget.value;
			} else if(type == 'ypbm') {
				//获取物资编码
				enter.pdbContent.ypbm = event.currentTarget.value;
			}
		},

		//判断是否有操作权限
		hasCx: function(cx) {
			if(!cx) {
				malert("用户没有操作权限！");
				return true;
			}
		},

		//获取库房信息
		getKFData: function() {
			$.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxwzkf&parm={"ylbm":"N050080012007"}',
				function(data) {
					if(data.a == 0) {
						//库房下拉框集合赋值
						enter.KFList = data.d;
						//默认获取该用例第一个科室的权限科室编码
						qxksbm = data.d[0].ksbm;
						//启动获取权限参数
						enter.getCsqx();
					} else {
						malert("药库获取失败");
					}
				});
		},

		//盘点类型改变时获取响应的子菜单信息,参数：分页信息/查询类型/查询参数/错误信息
		getInfo: function(dg, type, bean, errInfo) {
			$.getJSON('/actionDispatcher.do?reqUrl=' + type + '&dg=' + JSON.stringify(dg) +
				"&json=" + JSON.stringify(bean),
				function(json) {
					if(json != null || json.d.list.length > 0) {
						enter.infoList = json.d.list;
					} else {
						malert(errInfo);
						return;
					}
				});
		},

		//盘点类型改变
		selChange: function(val) {
			//库房非空判断
			if(enter.pdbContent.kfbm == null || enter.pdbContent.kfbm == undefined || enter.pdbContent.kfbm == '') {
				malert("请先选择库房！");
				//清空盘点方式
				val.currentTarget.value = 0;
				return;
			}

			//根据盘点方式显示子菜单
			if(val.currentTarget.value == 1) {
				//盘点方式赋值
				enter.pdbContent.pdfs = 1;
				enter.ypzlShow = true;
				enter.ypmcShow = false;
			} else if(val.currentTarget.value == 2) {
				//盘点方式赋值
				enter.pdbContent.pdfs = 2;
				enter.ypzlShow = false;
				enter.ypmcShow = true;
			} else {
				//盘点方式赋值
				enter.pdbContent.pdfs = 0;
				enter.ypzlShow = false;
				enter.ypmcShow = false;
			}
			//准备参数
			var bean = {
				"kfbm": enter.pdbContent.kfbm
			};
			//根据盘点方式不同，生成相应的字菜单
			if(enter.selectPdfs == 1) {
				//获取物资种类。参数：分页信息/查询类型/查询参数/错误信息
				enter.getInfo(toolMenu_0.dg, 'WzkfXtwhWzlb&types=query', bean, "物资种类获取失败！");

			} else if(enter.selectPdfs == 2) {
				//查询单个物资库存。参数：分页信息/查询类型/查询参数/错误信息
				enter.getInfo(toolMenu_0.dg, 'WzkfXtwhwzzd&types=query', bean, "物资信息获取失败！");
			}
		},
	},
})