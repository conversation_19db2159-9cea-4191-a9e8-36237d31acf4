
.sameStart {
    position: absolute;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 50%;
    right: 50%;
    bottom: 0;
}

.sameEnd {
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 1px solid #000000;
    width: 10px !important;
    height: 50%;
    right: 50%;
    top: 0;
}

.same {
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 100%;
    right: 50%;
    top: 0;
}
.yzd-name {
    float: left;
    height: 42px;
}
.yzdTitle {
    font-size: 22px;
    text-align: center;
}
.yzd-table table {
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.yzd-table td, .yzd-table th {
    border: 1px solid #999;
    font-weight: 500;
    height: 43px;
    width: 30px;
    position: relative;
    text-align: center;
}

.yzd-table td span {
    display: block;
    float: left;
    width: calc(50% - 2px);
    text-align: left;
    margin-left: 2px;
}
.yzd-table-blank span:first-child{
    border-bottom: 1px solid #999
}
.yzd-table-blank span {
    width: 100% !important;
    height: 50%;
    text-align: center !important;
}
@page{
    size: A4;
    /*margin: 0.5cm 0;*/
}

.yzd-brInfo > div, .yzd-ysInfo > div {
    font-size: 14px;
    margin-right: 20px;
}
.yzBoxSide  {
    font-size: 12px;
    padding-bottom: 40px;
}
.yzBoxSide{
    /* position: absolute; */
    background: #fff;
    z-index: 9999;
    top: 0;
    left: 0;
    right: 0;
    margin-left: 10px;
}
.yzBoxSide p,
.yzBoxSide ul,
.yzBoxSide li,
.yzBoxside i {
    padding: 0;
    margin: 0;
    font-style: normal;
    list-style: none;
}
.yzBoxSide .zhcx {
    transform:rotate(-90deg);
    /* width: 560px; */
    margin: 0 auto;
    /* margin-top: -30px; */
}
.yzBoxSide .zhcxx {
    width: 740px;
    zoom: 0.95;
    max-width: 900px;
    margin: 0 auto 0 0;
    padding-top: 1px;
}
.yzBoxSide .zhcxx h3{
    text-align: center;
    border-bottom: 1px solid #333333;
    position: relative;
}
.yzBoxSide .nav {
    width: 100%;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    padding: 20px 0;
}
.yzBoxSide .zhcx .header {
    display: flex;
    width: 100%;
    height: 40px;
    align-items: center;
    align-content: center;
    padding: 0 15px;
    line-height: 40px;
    box-sizing: border-box;
    align-self: center;
    border-bottom: 1px solid #333333;
}
.yzBoxSide .zhcx .header:nth-child(2) {
    border-top: none;
}
.yzBoxSide .zhcx .header .left-xb {
    width: 70%;
}
.yzBoxSide .zhcx .header .left-dz {
    width: 60%;
}
.yzBoxSide .zhcx .header .center-jz {
    width: 40%;
    border-left: 1px solid #333333;
    padding-left: 21px;
}
.yzBoxSide .zhcx .header .center-xb {
    width: 15%;
    padding-left: 10px;
    border-left: 1px solid #333333;
}
.yzBoxSide .p-box {
    padding: 0;
}
.yzBoxSide .zhcx .header .right-nl {
    width: 15%;
    padding-left: 10px;
    border-left: 1px solid #333333;
}
.yzBoxSide .bqzy .ysqm {
    text-align: right;
    margin-right: 120px;
    padding-bottom: 10px;
}
.yzBoxSide .bqzy {
    padding-top: 10px;
    text-indent: 15px;
}
.yzBoxSide .bqzy .text {
    margin-top: 23px;
    outline: none;
    height: 40px;
}
.yzBoxSide .zdxx .left-text {
    width: 60px;
    text-align: center;
    line-height: 30px;
    height: 100%;
    border-right: 1px solid #333333;
}
.yzBoxSide .zdxx .zdxx_child {
    width: 45%;
    outline: none;
    height: 100%;
    line-height: 30px;
    border-right: 1px solid #333333;
}
.yzBoxSide .zdxx .zdxx_child:nth-child(3) {
    border-right: none;
}
.yzBoxSide .zdxx {
    display: flex;
    align-self: center;
    height: 30px;
    align-items: center;
    border-top: 1px solid #333;
    border-bottom: 1px solid #333;
}
.yzBoxSide .psqz {
    padding: 0;
}
.yzBoxSide .psqz .center-text .qz {
    display: inline-block;
    margin-right: 70px;
    font-family: "Yu Gothic UI Semilight";
}
.yzBoxSide .psqz .center-text {
    text-align: center;
    margin-top: 10px;
    border-bottom: 1px solid #333;
    padding-bottom: 10px;
}
.yzBoxSide .header-content {
    display: flex;
    height: 34px;
    padding: 0;
    border-bottom: 1px solid #333;
}
.yzBoxSide .header-content .left-yp {
    width:58px;
    position: relative;
}
.yzBoxSide .header-content .left-yp:after {
    position: absolute;
    content: '';
    right: -8px;
    top: 23px;
    width: 75px;
    transform: rotate(-39deg);
    -webkit-transform:rotate(-39deg) ;
    -moz-transform: rotate(-39deg);
    -o-transform: rotate(-39deg);

    height: 1px;
    background-color: #333333;
}

.yzBoxSide .ri {
    position: absolute;
    left: 14px;
    top: 4px;
}
.yzBoxSide .yue {
    position: absolute;
    bottom: 3px;
    right: 13px;
}
.yzBoxSide .left-two-yp {
    width:50px;
    border: 1px solid #333;
    border-top: none;
    border-bottom: none;
    display:flex;
    justify-content: center;
    align-items: center;
}
.yzBoxSide .left-two-text {
    width: 50%;
    border-right: 1px solid #333;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    padding: 5px 0;
}
.yzBoxSide .left-two-text p {
    width: 100%;
    text-align: left;
    padding: 0 15px;
}
.yzBoxSide .right-two-text {
    width: 77px;
    border-right: 1px solid #333;
    text-align: center;
    padding: 4px 0;
}
.yzBoxSide .right-display {
    display: flex;
    justify-content: center;
    align-items: center;
}
.yzBoxSide .header-text {
    width: 100%;
    display: flex;
    padding: 0;
    border-bottom: 1px solid #333;
    min-height: 45px;
}
.yzBoxSide .header-height {
    min-height: 110px;
}
.yzBoxSide .text-rq {
    width:58px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.yzBoxSide .gcjl {
    width: 100%;
    padding: 10px 0;
    overflow: hidden;
    border-bottom: 1px solid #333;
}
.yzBoxSide .gctext {
    width: 80%;
    min-height: 30px;
    margin-left: 70px;
}
.yzBoxSide .yshsqm {
    float: right;
    margin-right: 120px;
}
.yzBoxSide .hzqm {
    padding: 5px 0;
    line-height: 40px;
    overflow: hidden;
}
.yzBoxSide .hzjsqm {
    width: 40%;
    float: left;
}
.yzBoxSide .hzage {
    width: 16%;
    float: left;
}

.yzBoxSide .qmryhzgx {
    width: 233px;
    float: right;
}
.yzBoxSide header h3 .qmryhzgx {
    position: absolute;
    right: 0;
    width: 140px;
    top: 0;
    font-size: 12px;
    font-weight: initial;
    line-height: 19px;
}
.yzBoxSide .timeAge{
    min-width: 50px;
    text-align: right;
    display: inline-block;
}
/*}*/
