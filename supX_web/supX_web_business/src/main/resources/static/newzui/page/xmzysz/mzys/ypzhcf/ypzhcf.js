    var qjindex = '';
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
        },
        methods:{
            AddMdel:function(){
            	wap.title='新增组合医嘱',
            	wap.open();
            	wap.popContent={};
            },
            sx:function () {
              zhypTableInfo.getData();
            },
            //删除
            del:function () {
                zhypTableInfo.remove();
            },
            //检索查询回车键
            searchHc: function() {
                if(event.keyCode == 13) {
                    zhypTableInfo.getData();
                }
            },
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            ksbmList: [],//科室集合
            rybmList: [],//人员集合
            cflxList: [],//处方类型集合
            yysmList:[],
            yfList: [],  //药房集合
            centent:'',
            isFold: false,
            title:'',
            ifClick: true, //判断是否点击过按钮
            num:0,
            csContent: {},
            jsonList: [],
            popContent: {},
        },
        methods: {
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },
            //下拉框科室加载
            yyksSelect: function(){
                this.param.rows=20000;
                this.param.sort='';
                this.param.order='';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg="+JSON.stringify(this.param),function (json) {
                    wap.ksbmList = json.d.list;
                });
            },
            //下拉框人员加载
            yyrSelect: function(){
                this.param.rows=20000;
                this.param.sort='';
                this.param.order='';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg="+JSON.stringify(this.param),function (json) {
                    wap.rybmList = json.d.list;
                });
            },
            //下拉框处方类型加载
            cflxSelect: function(){
                this.param.rows=20000;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=cflx&dg="+JSON.stringify(this.param),function (json) {
                    wap.cflxList = json.d.list;
                });
            },

            yysmSelect:function(){
                this.param.rows=100;
                $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmzcyyysm&types=query&dg=" + JSON.stringify(this.param), function (json) {
                	wap.yysmList = json.d.list;
                });
            },

            //下拉框药房加载
            yfSelect: function(){
                this.param.rows=20000;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf&dg="+JSON.stringify(this.param),function (json) {
                    wap.yfList = json.d.list;
                });
            },

            //保存
            saveData: function () {
            	if(!wap.ifClick) return;
            		wap.ifClick=false;
                 if(wap.popContent.cflxbm==null||wap.popContent.cflxbm==""){
                    malert("处方类型不能为空！");
                    wap.ifClick=true;
                    return;
                }
                if(wap.popContent.yfbm==null||wap.popContent.yfbm==""){
                    malert("药房不能为空！");
                    wap.ifClick=true;
                    return;
                }
                wap.popContent.ypbz = '1';
                    var json = JSON.stringify(wap.popContent);
                    this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=save',
                        json).then(function (data) {
                        if (data.body.a == 0) {
                            zhypTableInfo.getData();
                            wap.ifClick=true;
                            wap.closes();
                            malert("保存成功", "top", "success");
                        } else {
                            malert("上传数据失败", "top", "defeadted");
                            wap.ifClick=true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
            },
        }
    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var zhypTableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            popContent: {},
            jsonList: [],//
            iShow:false,
            isShowpopL:false,
            totlePage:0,
            total:'',
            page:'',
            kmbm:'',
            LcList:[],
            kmmc:'',
            rows:10,
            param: {
                page:1,
                rows:100,
                sort: 'zhyzbm',
                order: 'asc',
                lx:'1',
                parm:'',
            },
            ksbm: '', //科室编码
            is_csqx:{
                cs00400100201:'0'
            },
        },
        mounted:function () {
          changeWin()
        },
        methods: {
        	//获取参数权限
        	getKsbm: function () {
				$.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify({
					'ylbm': 'N030014002'
				})).then(function (json) {
					if (json.a == 0) {
						if (json.d.length > 0) {
							zhypTableInfo.ksbm = json.d[0].ksbm;
							//获取参数权限
							parm = {
								"ylbm": 'N030014002',
								"ksbm": zhypTableInfo.ksbm
							};
							$.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
								if (json.a == 0) {
									if (json.d.length > 0) {
										for (var i = 0; i < json.d.length; i++) {
											var csjson = json.d[i];
											switch (csjson.csqxbm) {
												case "N03001400201": //查看权限  0--个人，1--科室，2--全院
													if (csjson.csz == null || csjson.csz == undefined || csjson.csz == "") {
														zhypTableInfo.is_csqx.cs00400100201 = '0';
													} else {
														zhypTableInfo.is_csqx.cs00400100201 = csjson.csz;
													}
													break;
											}
										}
									}
									zhypTableInfo.getData();
								} else {
									malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
								}
							});

						}
					}
				});
			},

            getData: function () {
                    this.param.parm = wrapper.param.parm;
                this.param.lx=1;
                if(zhypTableInfo.is_csqx.cs00400100201=='0'){
                	this.param.yyz=userId;
                }
                this.param.ypbz = '1';
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=query&parm="+JSON.stringify(this.param),function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if(json.a==0){
                        zhypTableInfo.totlePage = Math.ceil(json.d.total/zhypTableInfo.param.rows);
                        zhypTableInfo.jsonList = json.d.list;
                    }

                });
            },
 			//双击跳转
            goNew:function(zhyzbm,zhyzmc,yfbm,cflxbm){
                var param ={
                    zhyzbm: zhyzbm,
                    zhyzmc: zhyzmc,
                    yfbm: yfbm,
                    cflxbm: cflxbm
                }
                sessionStorage.setItem("ypzhcf",JSON.stringify(param));
				this.topNewPage('组合药品医嘱','page/xmzysz/mzys/ypzhcf/xzyp.html')
            },


            //删除
            remove: function(index) {
                var zhyzList = [];
                var zhyz={};
                if (index!=null) {
                    zhyz.zhyzbm=this.jsonList[index].zhyzbm
                    zhyz.ypbz = this.jsonList[index].ypbz;
                    zhyzList.push(zhyz);
                } else {
                    for(var i=0;i<this.isChecked.length;i++){
                        if(this.isChecked[i] == true){
                            zhyz.zhyzbm=this.jsonList[i].zhyzbm
                            zhyzList.push(JSON.parse(JSON.stringify(zhyz)));
                            zhyz.ypbz = this.jsonList[i].ypbz;
                            zhyzList.push(zhyz);
                        }
                    }
                }
                var json='{"list":'+JSON.stringify(zhyzList)+'}'
                if (common.openConfirm("确认删除该条信息吗？", function () {
                        zhypTableInfo.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=delete', json).then(function (data) {
                            if (data.body.a == 0) {
                                malert("删除成功", "top", "success")
                                zhypTableInfo.isChecked=[];
                                zhypTableInfo.getData();
                            } else {
                                malert("删除失败", "top", "defeadted")
                            }
                        }, function (error) {
                            console.log(error);
                        });
                    })) {
                    return false;
                }
            },
              //双击赋值修改
            edit: function (num) {
                if (num == null) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            num = i;
                            break;
                        }
                    }
                    if (num == null) {
                        malert("请选中你要修改的数据");
                        return false;
                    }
                }
                wap.open();
                wap.title='编辑药品';
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },
        },
    });
    zhypTableInfo.getKsbm();
    wap.yyksSelect();
    wap.yyrSelect();
    wap.cflxSelect();
    wap.yysmSelect();
    wap.yfSelect();
    //监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
    $(document).mouseup(function (e) {
        var bol = $(e.target).parents().is(".selectGroup");
        if (!bol) {
            $(".selectGroup").hide();
        }
    });





