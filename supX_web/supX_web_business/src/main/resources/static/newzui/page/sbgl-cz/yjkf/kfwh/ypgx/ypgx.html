<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药品功效</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
</head>
<style>
    .zui-form .zui-form-label {
        left: 5px !important;
    }

    .icon-width:before {
        width: 24px;
        height: 24px;
        position: absolute;
        left: 0px;
        top: -13px;
    }

    #jyxm_icon .switch {
        top: 60%;
        left: 35%;
        height: 38px;
    }
</style>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="sx">刷新</button>
                <button class="tong-btn btn-parmary-b"><i class=" icon-width icon-dc padd-l-25"></i>导出</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline margin-f-l20">
                            <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="search"/>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="zui-table-view padd-l-10 padd-r-10" id="utable1" z-height="full">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox"
                                                                                     :list="'jsonList'"
                                                                                     :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>功效编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s text-left"><span>功效名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>拼音简码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>抗生素标志</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>麻醉药品</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body body-heights" id="zui-table" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" @dblclick="edit($index)"
                        @click="checkSelect([$index,'one','jsonList'],$event)"
                        :class="[{'table-hovers':isChecked[$index]}]">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>

                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.gxbm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-left ">
                                <div class="relative title title-width"><i v-text="item.gxmc"
                                                                           :data-title="item.gxmc"></i></div>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s relative">
                                <div class="title title-width"><i v-text="item.pyjm"
                                                                  style="width: 84px;display: block;overflow: hidden;"></i>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="istrue_tran[item.kssbz]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="istrue_tran[item.mzyp]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">

                                <div class="switch">
                                    <input type="checkbox" true-value="0" false-value="1" v-model="item.tybz" disabled/>
                                    <label></label>
                                </div>
                            </div>

                        </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>

            </div>
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>

        </div>

    </div>
</div>
<div class="side-form ng-hide pop-width" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
            <span class="span0">
                <i>功效编码</i>
                <input type="text" class="zui-input border-r4" placeholder="自动生成" v-model="popContent.gxbm"
                       @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
        <span class="span0">
                <i>功效名称</i>
                <input type="text" class="zui-input border-r4" v-model="popContent.gxmc" @keydown="nextFocus($event)"
                       @blur="setPYDM(popContent.gxmc, 'popContent', 'pyjm')"/>
            </span>
        <span class="span0">
                <i>拼音简码</i>
                <input class="zui-input border-r4" type="text" v-model="popContent.pyjm" @keydown="nextFocus($event)"
                       disabled="disabled"/>
            </span>
        <span class="span0">
                <i>抗生素标志</i>
                <select-input @change-data="resultChange"
                              :child="istrue_tran" :index="popContent.kssbz" :val="popContent.kssbz"
                              :name="'popContent.kssbz'">
                                </select-input>
            </span>
        <span class="span0">
                <i>麻醉药物</i>
                <select-input @change-data="resultChange"
                              :child="istrue_tran" :index="popContent.mzyp" :val="popContent.mzyp"
                              :name="'popContent.mzyp'">
                                </select-input>
            </span>
        <span class="margin-top-10 span0">
                <i style="float:left;">状态</i>
                <div class="switch">
                    <input type="checkbox" id="tybz" true-value="0" false-value="1" v-model="popContent.tybz"/>
                    <label for="tybz"></label>
                </div>
            </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<style>
    .side-form-bg {
        background: none;
    }
</style>
<script src="ypgx.js"></script>
</body>

</html>
