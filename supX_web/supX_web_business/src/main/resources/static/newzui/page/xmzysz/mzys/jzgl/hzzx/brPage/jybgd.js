

var bgdList = new Vue({
    el: '#bgdList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        djList: []
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        //请求后台查询
        getData: function () {
            printShow.doPrintRep({}, [{}]);  //先请求一个空模板过来
            //后台查询数据
            var parm = {
                bah: userNameBg.Brxx_List.ghxh,
                lx: '0'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=mzysLisJydj&types=query&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        bgdList.djList = data.d.list;
                    }
                } else {
                    malert("检验查询失败" + data.c,"top","defeadted");
                }
            });
        },
        //单击查询
        checkOne: function (index) {
            var jyxh = this.djList[index].jyxh;
            var parm = {
                jyxh: jyxh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=mzysLisJydj&types=queryMx&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    data.d['xb'] = bgdList.brxb_tran[data.d['xb']];
                    data.d['nldw'] = bgdList.nldw_tran[data.d['nldw']];
                    data.d['lx'] = bgdList.jydjlx_tran[data.d['lx']];
                    data.d['sqrq'] = bgdList.fDate(data.d['sqrq'], "date");
                    data.d['cyrq'] = bgdList.fDate(data.d['cyrq'], "date");
                    data.d['ybhsrq'] = bgdList.fDate(data.d['ybhsrq'], "date");
                    printShow.doPrintRep(data.d, data.d.jydjmxList);
                } else {
                    printShow.doPrintRep({}, [{}]);
                }
            })
        }
    }
});

var printShow = new Vue({
    el: '#print-show',
    mixins: [printer],
    data: {},
    methods: {
        doPrintRep: function (content, list) {
            // 查询打印模板
            var json = { repname: '检验报告单' };
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                if (json.d.length == 0) {
                    json.d[0] = printTemplets.getTempletByName('检验报告单');
                }
                // 清除打印区域
                printShow.clearBgd(json.d[0]);
                // 绘制模板的canvas
                printShow.drawList = JSON.parse(json.d[0]['canvas']);
                printShow.creatCanvas();
                printShow.reDraw();
                // 为打印前生成数据
                printShow.printContent(content);
                printShow.printTrend(list);
            });
        },
        clearBgd: function (json) {
            $(".bgdArea").html('');
            $(".bgdArea").append(json['content']);
            $(".bgdArea").append('<div style="page-break-before: avoid"></div>');
            this.isClearArea = true;
        }
    }
});