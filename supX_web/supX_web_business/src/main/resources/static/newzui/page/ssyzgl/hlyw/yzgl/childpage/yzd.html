
<link rel="stylesheet" href="childpage/yzdprint.css" media="print"/>
<link href="childpage/yzd.css" rel="stylesheet" type="text/css" media="screen">
<!--style="height: calc(100% - 111px)"-->
<div id="yzd"  class="flex-container flex-dir-c hide over-auto">
    <div  v-cloak class="toolMenu_yzd printHide flex-container">
        <div @click="long(0)" class="cursor" :class="{'yzd_select': which==0}">长&nbsp;&nbsp;期</div>
        <div @click="short(1)" class="cursor padd-l-10 padd-r-10" :class="{'yzd_select': which==1}">临&nbsp;&nbsp;时</div>
        <button class="tong-btn btn-parmary padd-l-10"  @click="doPrint(false)">打印</button>
        <button class="tong-btn btn-parmary-f2a" @click="doPrint(true)">续打</button>
        <select-input class="wh120" @change-data="commonResultChange"
                      :child="qsxzList" :index="'yexm'" :index_val="'yebh'" :val="popContent.yebh"
                      :name="'popContent.yebh'" :index_mc="'yexm'" search="true" id="yebh">
        </select-input>
        <!-- <div  class="flex-container flex-align-c padd-l-10">
            <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'本科'" :val="'bk'"  :new-value="bk"></vue-checkbox>
        </div> -->
        <div class="zui-inline flex-container flex-align-c padd0">
            <label class="font-14" style="margin-left: 20px;padding: 0px">标志</label>
            <select-input class="wh80" @change-data="commonResultChange2" :child="ypfy_tran" :index="ypbz"
                          :val="ypbz" :name="'ypbz'" :search="true">
            </select-input>
        </div>
        <div class="zui-inline flex-container flex-align-c">
            <label class="font-14" style="margin-left: 20px;padding: 0px">状态</label>
            <select-input class="wh80 padd0" @change-data="commonResultChange2" :child="ystzbz_tran" :index="ystzbz"
                          :val="ystzbz" :name="'ystzbz'" :search="true">
            </select-input>
        </div>
		<div class="zui-inline flex-container flex-align-c" style="margin-left: 10px;">
			<input class="zui-input wh182" type="text" data-select="no" v-model="zx_begin" id="dbegin"
				   @keydown="searchListHc">
			<span class="padd-r-5 padd-l-5">至</span>
			<input class="zui-input wh182" type="text" data-select="no" v-model="zx_end" id="dEnd"
				   @keydown="searchListHc">
			   </div>
    </div>

    <div v-cloak class="cqyzd printHide" v-show="isShow">
        <div class="yzdTitle" >{{yljgmc}}长期医嘱单</div>
        <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
			<div>
			    <span>姓名:</span>
			    <span>{{BrxxJson.brxm}}</span>
			</div>
			<div>
			    <span>性别:</span>
			    <span>{{brxb_tran[BrxxJson.brxb]}}</span>
			</div>
			<div>
			    <span>年龄:</span>
			    <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}{{BrxxJson.nl2?(BrxxJson.nl2 + nldw_tran[BrxxJson.nldw2]):''}}</span>
			</div>
            <div class="wh180 skin-default">
                <span>科室:</span>
                <span>{{BrxxJson.ryksmc}}</span>
            </div>
            <div>
                <span>床号:</span>
                <span>{{BrxxJson.rycwbh}}</span>
            </div>
            <div>
                <span>住院号:</span>
                <span>{{BrxxJson.zyh}}</span>
            </div>
        </div>

        <div class="yzd-table">
            <table cellspacing="0" cellpadding="0" class="yz-tables">
                <tbody>
                <tr :class="{'goPrintHide': isGoPrint}">
                    <th colspan="3">下达医嘱</th>
					<th colspan="2">校对</th>
					<th colspan="2" style="width: 320px">长期医嘱</th>
                    <th colspan="3">停止医嘱</th>
					<th colspan="2">确认</th>
                </tr>
                <tr :class="{'goPrintHide': isGoPrint}">
                    <th class="wh50">日期</th>
                    <th class="wh50">时间</th>
					<th class="wh50">医生</th>
					<th class="wh50">时间</th>
					<th class="wh50">护士</th>
					<th colspan="2" style="width: 320px">内容</th>
                    <th class="wh50">日期</th>
                    <th class="wh50">时间</th>
                    <th class="wh50">医生</th>
					<th class="wh50">时间</th>
					<th class="wh50">护士</th>
                </tr>
				
                <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                    @click="goPrint($index)">
                    <td>
                       {{sameDate('ksrq', $index,'ry')}}
                    </td>
                    <td>
                        {{sameDate('ksrq', $index, 'sj')}}
                    </td>
                    <td>
                        {{item.ysqmxm}}
                    </td>
					<td>
					    {{sameDate('zxsj', $index, 'sj')}}
					</td>
					<td>
					    {{item.shhsxm}}
					</td>
                    <td style="width: 222px;">
                    	<span class="yzd-name" :class="[item.tzbj ]" ><span v-if="item.sfjz=='1'" style="color: blue;">急</span>{{item.xmmc}}</span>
                    </td>
                    <td>
						<span class="yzd-yysm">
							{{item.yyffmc}}{{item.yysm}}
							<template v-if="item.ypbz =='1' && item.sl =='0'">不发药</template>
							<template v-else-if="item.ypbz =='0' && item.tsyz =='1'">嘱托</template>
						</span>
                    </td>
                    <td>
                    	{{sameDate('ystzsj', $index, 'ry')}}
                     </td>
					<td>
						{{sameDate('ystzsj', $index, 'sj')}}
					</td>
					<td>
						{{item.tzysxm}}
					</td>
                    <td>
                        {{sameDate('hstzsj', $index, 'sj')}}
                    	</td>
                    <td>
                    	{{item.tzhsxm}}
                     </td>
                </tr>
                </tbody>
                
            </table>
        </div>
    </div>

    <div v-cloak  class="cqPrint">
        <!--<transition name="pop-fade">-->
        <!--:style="index|compuGd()"-->
        <div  v-show="isShow">
            <div  class="popCenter"  v-for="(itemList, index) in list" >
                <div class="yzdTitle">
					<div class="dybt">{{yljgmc}}</div>
					<div class="dybt">长期医嘱记录单</div>
				</div>
                <div class="yzd-brInfo" >
                    <div class="hzxm">
                    						    <span>姓名:{{BrxxJson.brxm}}</span>
                    						    
                    						</div>
                    						<div class="hzxb" >
                    						    <span>性别:{{brxb_tran[BrxxJson.brxb]}}</span>
                    						    
                    						</div>
                    						<div class="hznl" >
                    						    <span>年龄:{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}{{BrxxJson.nl2?(BrxxJson.nl2 + '' + nldw_tran[BrxxJson.nldw2]):''}}</span>
                    						    
                    						</div>
											<div class="hzbs" >
											    <span>科室:{{BrxxJson.ryksmc}}</span>
											</div>
                    						<div class="hzch" >
                    						    <span>床号:{{BrxxJson.rycwbh}}</span>
                    						</div>
                    						<div class="hzzyh" >
                    						    <span>住院号:{{BrxxJson.zyh}}</span>
                    						</div>
                    						
                </div>

                <div class="yzd-table">
                    <table cellspacing="0" cellpadding="0" class="yz-tables">
                        <tbody>
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th colspan="3" class="xdyz">下达医嘱</th>
                        	<th colspan="2" class="yzxd">校对</th>
                        	<th colspan="2" class="cqyznr">长期医嘱</th>
                            <th colspan="3" class="tzyz">停止医嘱</th>
                        	<th colspan="2" class="yzxd">确认</th>
                        </tr>
                        <tr :class="{'goPrintHide': isGoPrint}">
                            <th class="wh50 yzrq">日期</th>
                            <th class="wh50 yzsj">时间</th>
                        	<th class="wh50 yzys">医生</th>
                        	<th class="wh50 yzsj">时间</th>
                        	<th class="wh50 yzhs">护士</th>
                        	<th colspan="2" class="cqyznr">内容</th>
                            <th class="wh50 yzrq">日期</th>
                            <th class="wh50 yzsj">时间</th>
                            <th class="wh50 lsyzhs">医生</th>
                        	<th class="wh50 yzsj">时间</th>
                        	<th class="wh50 yzhs">护士</th>
                        </tr>
                        <tr v-for="(item, $index) in itemList" :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]" >
                            <td>
                               {{setDate(item.ksrq, 'ry', '','')}}
                            </td>
                            <td>
                                {{setDate(item.ksrq, 'sj', '','')}}
                            </td>
                            <td>
                                {{item.ysqmxm}}
                            </td>
                            <td>
                                {{setDate(item.zxsj, 'sj', '','')}}
                            </td>
                            <td>
                                {{item.shhsxm}}
                            </td>
                            <td>
                            	<span class="yzd-name" :class="[item.tzbj ]"  ><span v-if="item.sfjz=='1'" style="color: blue;">急</span>{{item.xmmc}}</span>
                            </td>
                            <td class="">
                                <div class="cqyznrgg">{{item.yyffmc}}{{item.yysm}}
								<template v-if="item.ypbz =='1' && item.sl =='0'">不发药</template>
								<template v-else-if="item.ypbz =='0' && item.tsyz =='1'">嘱托</template>
								</div>
                            </td>
                            <td>
                            	{{setDate(item.ystzsj, 'ry', '','')}}
                             </td>
                            <td>
                            	{{setDate(item.ystzsj, 'sj', '','')}}
                             </td>
                            <td>
                            	{{item.tzysxm}}
                            </td>
                            <td>
                                {{setDate(item.hstzsj, 'sj', '','')}}
                            	</td>
                            <td>
                            	{{item.tzhsxm}}
                             </td>
                        </tr>
                        </tbody>
                        
                    </table>
                </div>
					<div class="ysDiv" primaryKey="ysDiv">
                        <div class="yzd-ysInfo">
                            <div class="yzd-ysqm">
                                <span>医生签名:</span>
                                <span></span>
                            </div>
                            <div>
                                <span>护士签名:</span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                <div style="page-break-after: always;" :class="{'goPrintHide':cssFun(index)}" class="text-center" v-text="'第  ' + (index + 1) + '  页'"></div>
            </div>
        </div>
        <!--</transition>-->
    </div>

    <div v-cloak class="lsyzd printHide" v-show="isShow">
        <div class="yzdTitle" >{{yljgmc}}临时医嘱单</div>
        <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
			<div>
			    <span>姓名:</span>
			    <span>{{BrxxJson.brxm}}</span>
			</div>
			<div>
			    <span>性别:</span>
			    <span>{{brxb_tran[BrxxJson.brxb]}}</span>
			</div>
			<div>
			    <span>年龄:</span>
			    <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}{{BrxxJson.nl2?(BrxxJson.nl2 + nldw_tran[BrxxJson.nldw2]):''}}</span>
			</div>
            <div class="wh180 skin-default">
                <span>科室:</span>
                <span>{{BrxxJson.ryksmc}}</span>
            </div>
            <div>
                <span>床号:</span>
                <span>{{BrxxJson.rycwbh}}</span>
            </div>
            <div>
                <span>住院号:</span>
                <span>{{BrxxJson.zyh}}</span>
            </div>
        </div>

        <div class="yzd-table">
            <table cellspacing="0" cellpadding="0" class="lsyz-tables">
                <tbody>
                <tr :class="{'goPrintHide': isGoPrint}">
					<tr :class="{'goPrintHide': isGoPrint}">
					    <th colspan="3">下达医嘱</th>
						<th colspan="2" style="width: 320px">临时医嘱</th>
					    <th colspan="3">执行医嘱</th>
					</tr>
					<tr :class="{'goPrintHide': isGoPrint}">
					    <th class="wh50 yzrq">日期</th>
					    <th class="wh50 yzsj">时间</th>
						<th class="wh50 yzys">医生</th>
						<th colspan="2" style="width: 320px">内容</th>
					    <th class="wh50">日期</th>
					    <th class="wh50">时间</th>
					    <th class="wh50">护士</th>
					</tr>
                </tr>
                <tr v-for="(item, $index) in jsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                    @click="goPrint($index)" >
                    <td>
                        <span>{{sameDate('ksrq', $index,'ry')}}</span>
                        
                    </td>
                    <td>
                        {{sameDate('ksrq', $index, 'sj')}}
                    </td>
					<td>
					    {{item.ysqmxm}}
					</td>
					<td>
						<span class="yzd-name" :class="[item.tzbj ]" ><span v-if="item.sfjz=='1'" style="color: blue;">急</span>{{item.xmmc}}</span>
					</td>
                    <td  class="" v-if="item.psff">
                    	<div class="lsyznrgg">{{item.psff}}{{item.ldmc}}
                    	<template v-if="item.ypbz =='1' && item.sl =='0'">不发药</template>
                    	<template v-else-if="item.ypbz =='0' && item.tsyz =='1'">嘱托</template>
                    	</div>
                    </td>
                    <td class="" v-else>
                    	<div class="lsyznrgg">{{item.yyffmc}}{{item.ldmc}}
                    	<template v-if="item.ypbz =='1' && item.sl =='0'">不发药</template>
                    	<template v-else-if="item.ypbz =='0' && item.tsyz =='1'">嘱托</template>
                    	</div>
                    	
                    </td>
                    <td>
						{{sameDate('zxsj', $index, 'ry')}}
                        </td>
                    <td>
                        {{sameDate('zxsj', $index, 'sj')}}
						</td>
                    <td >
						{{item.shhsxm}}
                     </td>
                </tr>
                </tbody>
                
            </table>
        </div>

    </div>

    <div v-cloak  class="lsPrint">
<!--        <transition name="pop-fade">-->
            <div v-show="isShow" class="background-f">
                <div class="popCenter "  v-for="(itemList, index) in list" >
                    <div class="yzdTitle" >
						<div class="dybt">{{yljgmc}}</div>
						<div class="dybt">临时医嘱记录单</div>
					</div>
                    <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}">
						
                        <div class="hzxm">
                        						    <span>姓名:{{BrxxJson.brxm}}</span>
                        						    
                        						</div>
                        						<div class="hzxb" >
                        						    <span>性别:{{brxb_tran[BrxxJson.brxb]}}</span>
                        						    
                        						</div>
                        						<div class="hznl" >
                        						    <span>年龄:{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}{{BrxxJson.nl2?(BrxxJson.nl2 + '' + nldw_tran[BrxxJson.nldw2]):''}}</span>
                        						    
                        						</div>
												<div class="hzbs" >
												    <span>科室:{{BrxxJson.ryksmc}}</span>
												</div>
                        						<div class="hzch" >
                        						    <span>床号:{{BrxxJson.rycwbh}}</span>
                        						</div>
                        						<div class="hzzyh" >
                        						    <span>住院号:{{BrxxJson.zyh}}</span>
                        						</div>
                        						
                    </div>
                    <div class="yzd-table">
                        <table cellspacing="0" cellpadding="0" class="lsyz-tables">
                            <tbody>
                            	<tr :class="{'goPrintHide': isGoPrint}">
                            	    <th colspan="3">下达医嘱</th>
                            		<th colspan="2" class="lsyznr">临时医嘱</th>
                            	    <th colspan="3">执行医嘱</th>
                            	</tr>
                            	<tr :class="{'goPrintHide': isGoPrint}">
                            	    <th class="wh50 lsyzrq">日期</th>
                            	    <th class="wh50 yzsj">时间</th>
                            		<th class="wh50 lsyzys">医生</th>
                            		<th colspan="2" class="lsyznr">内容</th>
                            	    <th class="wh50 lsyzrq">日期</th>
                            	    <th class="wh50 yzsj">时间</th>
                            	    <th class="wh50 lsyzhs">护士</th>
                            	</tr>
                            <tr v-for="(item, $index) in itemList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                                @click="goPrint($index)" >
                                <td>
                                    <span>{{setDate(item.ksrq, 'ry','','')}}</span>
                                    
                                </td>
                                <td>
                                    {{setDate(item.ksrq, 'sj','','')}}
                                </td>
                            	<td>
                            	    {{item.ysqmxm}}
                            	</td>
                            	<td>
                            		<span class="lsyzd-name" :class="[item.tzbj ]" ><span v-if="item.sfjz=='1'" style="color: blue;">急</span>{{item.xmmc}}</span>
                            	</td>
                                <td  class="" v-if="item.psff">
									<div class="lsyznrgg">{{item.psff}}{{item.ldmc}}
									<template v-if="item.ypbz =='1' && item.sl =='0'">不发药</template>
									<template v-else-if="item.ypbz =='0' && item.tsyz =='1'">嘱托</template>
									</div>
                                </td>
								<td class="" v-else>
									<div class="lsyznrgg">{{item.yyffmc}}{{item.ldmc}}
									<template v-if="item.ypbz =='1' && item.sl =='0'">不发药</template>
									<template v-else-if="item.ypbz =='0' && item.tsyz =='1'">嘱托</template>
									</div>
									
								</td>
								
								
								
                                <td>
                            		{{setDate(item.zxsj, 'ry', '','')}}
                                    </td>
                                <td>
                                    {{setDate(item.zxsj, 'sj', '','')}}
                            		</td>
                                <td >
                            		{{item.shhsxm}}
                                 </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
					<div class="ysDiv" primaryKey="ysDiv">
					    <div class="yzd-ysInfo flex-container flex-jus-c">
					        <div class="yzd-ysqm">
					            <span>医生签名:</span>
					            <span></span>
					        </div>
					        <div>
					            <span>护士签名:</span>
					            <span></span>
					        </div>
					    </div>
					</div>
                    <div style="page-break-after: always;" :class="{'goPrintHide':cssFun(index)}" class="text-center" v-text="'第  ' + (index + 1) + '  页'"></div>
                </div>
            </div>
<!--        </transition>-->
    </div>
</div>
<script type="application/javascript" src="childpage/yzd.js"></script>
