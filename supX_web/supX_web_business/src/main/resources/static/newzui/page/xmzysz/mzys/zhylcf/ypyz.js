//医疗医嘱明细列表
var hzList = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        num: 0,
        zhylyz:{},
        popContent:{},
        csqxs:{},
        jsonList: [],
    },
    created: function () {
    },
    mounted:function () {
        this.getKsbm()
    },
    updated:function(){
        changeWin();
    },
    methods: {
        //获取参数权限
        getKsbm: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify({'ylbm': 'N030014002'})).then(function (json) {
                if (json.a == 0 && json.d.length > 0) {
                        parm = {
                            "ylbm": 'N030014002',
                            "ksbm": json.d[0].ksbm
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0 && json.d.length > 0) {
                                    for (var i = 0; i < json.d.length; i++) {
                                        var csjson = json.d[i];
                                        switch (csjson.csqxbm) {
                                            case "N03001400202": //0门诊，1住院
                                                hzList.csqxs.N03001400202 = csjson.csz;
                                                break;
                                        }
                                    }
                                hzList.getDatess();
                            } else {
                                malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                            }
                        });
                }
            });
        },
        addData: function () {
            brzcList.title = '新增组合医疗医嘱';
            brzcList.index = 0
            brzcList.popContent = {
                zhyzbm:this.popContent.zhyzbm,
                zhyzmc:this.popContent.zhyzmc,
            }
        },
        getDatess:function () {
            if(sessionStorage.zhylyz){
                this.obj=JSON.parse(sessionStorage.zhylyz);
                this.popContent.zhyzmc=this.obj.zhyzmc
                this.popContent.zhyzbm=this.obj.zhyzbm
                sessionStorage.clear('zhylyz')
                this.getData();
            }
        },
        //初始化页面加载列表
        getData: function () {
            this.jsonList=[];
            common.loadDebrisHTML('.zui-table-body')
            var json={
                zhyzbm:this.obj.zhyzbm,
                mzorzy:(hzList.csqxs.N03001400202=='0'?'0':'1')
            };
            $.getJSON("/actionDispatcher.do?reqUrl=MzysZlglZhylyz&types=query&parm="+JSON.stringify(json),function (json) {
               common.closeLoading()
                if(json.a =='0' && json.d!=null){
                    hzList.jsonList = json.d.list;
                }
            });
        },

        edit: function (num) {
            if (num == null) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if (num == null) {
                    malert('请选中你要修改的数据', 'top', 'defeadted');
                    return false;
                }
            }
            brzcList.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            brzcList.num = this.num;
            brzcList.index = 0;
            brzcList.title = '编辑组合药品医嘱';
            brzcList.zlContent.text=brzcList.popContent.mxzlxmmc;
        },
        remove: function (item) {
            var zhylyzList = [];
            if(this.isChecked.length>0){
                for(var i=0;i<this.isChecked.length;i++){
                    if(this.isChecked[i] == true){
                        zhylyzList.push(JSON.parse(JSON.stringify(this.jsonList[i])));
                    }
                }
            }else{
                zhylyzList.push(JSON.parse(JSON.stringify(item)))
            }
            var json = '{"list":'+JSON.stringify(zhylyzList)+'}';
            console.log(json);
            if (common.openConfirm("确认删除该条信息吗？", function () {
                hzList.$http.post('/actionDispatcher.do?reqUrl=MzysZlglZhylyz&types=delete', json).then(function (data) {
                    if (data.body.a == 0) {
                        hzList.isChecked=[];
                        malert("删除成功", "top", "success")
                        hzList.getData();
                    } else {
                        malert("删除失败", "top", "defeadted")
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }
        },
    }
})
//弹框展示
var brzcList=new Vue({
    el:'#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data:{
        num:1,
        index:1,
        popContent:{
            ypbz : '0',
        },
        ypContent:{},
        zlContent:{},
        title:'',
        jsonList: [],//组合药品医嘱列表
        pcList:[],//频次下拉框
        mxzlxmList: [], //明细诊疗项目下拉框
        yyffList: [], //用药方法下拉框

        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them: {
            '诊疗项目编码': 'zlxmmc',
            '诊疗项目名称': 'zlxmbm',
            '拼音代码': 'pydm',
        }
    },
    components: {
        'search-table': searchTable
    },
    created:function () {
        this.pcSelect()
    },
    methods:{
        searching: function (add,type,val) {
            // 搜索调用API的方法，add为true就表示请求下一页、为null就为请求第一页
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            this.zlContent[type] = val;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (this.zlContent[type] == undefined || this.zlContent[type] == null){
                this.page.parm = "";
            }else{
                this.page.parm = this.zlContent[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,mzorzy:(hzList.csqxs.N03001400202=='0'?'0':'1')};
            //zlmxxm;mxfyxm
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zlmxxm&json=" +
                JSON.stringify(str_param), function (json) {
                if (json.a == 0) {
                    var date = null;
                    if (add) {                  // 往searchCon里面赋值的方式都是push（不管是第一次加载还是加载更多）
                        for (var i = 0; i < json.d.list.length; i++) {
                            brzcList.searchCon.push(json.d.list[i]);
                        }
                    } else {
                        brzcList.searchCon = json.d.list;
                    }

                    brzcList.page.total = json.d.total;
                    brzcList.selSearch = 0;
                    if (json.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                } else {
                    malert("查询失败  " + json.c,'top', 'defeadted');
                }
            })
        },
        changeDown: function (event,type) {
            if(this['searchCon'][this.selSearch]==undefined) return;
            this.keyCodeFunction(event, 'zlContent', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 ||event.code== 'NumpadEnter') {
                if(type=="text"){
                    Vue.set(this.zlContent, 'text', this.zlContent['zlxmmc']);
                    brzcList.popContent.mxzlxmbm=this.zlContent.zlxmbm;
                    $(".selectGroup").hide();
                    this.nextFocus(event);
                }
            }
        },
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true,'text');           // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.zlContent=item;
                Vue.set(this.zlContent, 'text', this.zlContent['zlxmmc']);
                brzcList.popContent.mxzlxmbm=this.zlContent.zlxmbm;
                $(".selectGroup").hide();
            }
        },
        closes:function () {
            this.index=1
        },
        saveData:function () {
            if(this.popContent['zhyzbm']==undefined||this.popContent['zhyzbm']==null||this.popContent['zhyzbm']==''){
                malert("选择左边组合医嘱之后才能进行添加操作",'top', 'defeadted');
                return false;
            }
            if(this.popContent['sl']<=0){
                malert("数量不能小于0",'top', 'defeadted');
                return false;
            }
            if(this.popContent['dj']<0){
                malert("单价不能小于0",'top', 'defeadted');
                return false;
            }
            if (this.popContent.zhypxh==null){//新增否则是修改
                var mzxxh=0;
                for (var i=0;i<hzList.jsonList.length;i++){
                    if(hzList.jsonList[i].zhypxh>mzxxh){
                        mzxxh=hzList.jsonList[i].zhypxh;
                    }
                }
                this.popContent.zhypxh=mzxxh+1;
            }
            this.$http.post('/actionDispatcher.do?reqUrl=MzysZlglZhylyz&types=save',
                JSON.stringify(this.popContent))
                .then(function (data) {
                    if(data.body.a == 0){
                        malert("数据更新成功",'top', 'success');
                        hzList.getData()
                        if(brzcList.title!='新增组合医疗医嘱'){
                            brzcList.index=1

                        }
                        brzcList.zlContent={}
                        brzcList.popContent = {
                            zhyzbm:brzcList.popContent.zhyzbm,
                            zhyzmc:brzcList.popContent.zhyzmc,
                        }
                    } else {
                        malert(data.body.c+"数据失败",'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });

        },
        //频次下拉框
        pcSelect: function(){
            this.param.rows=20000;
            this.param.sort='';
            this.param.order='';
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yzpc&dg="+JSON.stringify(this.param),function (json) {
                brzcList.pcList = json.d.list;
            });
        },
    },
});

