<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>领药审核</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<style>
    .icon-sh:before{
        color:#1abc9c;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="wrapper">
        <div class="panel ">
            <div class="tong-top">
                <button class="tong-btn  btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">刷新</button>
                <button class="tong-btn  btn-parmary-b icon-sh paddr-r5" @click="shenHe()">审核</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                  <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                    <div class="zui-input-inline wh122">
                       <!--<select-input @change-data="resultChangeYf"-->
                                       <!--:child="YFList"-->
                                      <!--:index="'yfmc'" :index_val="'yfbm'"-->
                                      <!--:val="yfbm" :search="true" :name="'yfbm'"-->
                                      <!--id="yfbm" :index_mc="'yfmc'">-->
                   		<!--</select-input>-->

                        <select-input @change-data="resultChangeYf"
                                      :child="YFList" :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm"
                                      :name="'popContent.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>

                    </div>
                	</div>
                        <div class="flex-container">
                                <div class="flex-container flex_items  margin-l-20">
                                <label class="whiteSpace padd-r-5 ft-14">开单时间</label>
                                    <div class="position flex-container flex-align-c  margin-f-l5">
                                        <i class="icon-position icon-rl"></i>
                                        <input class="zui-input todate wh160 text-indent20"
                                               placeholder="请选择开单时间"
                                               id="timeVal" /><span class="padd-l-5 padd-r-5">~</span>
                                        <input class="zui-input todate wh160 "
                                               placeholder="请选择结束时间"
                                               id="timeVal1" />
                                    </div>

                                </div>
                        </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view flex-container padd-l-10 padd-r-10">
               <div class="padd-r-10" style="width:20%">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th v-if="!csN04003002002201201"><div class="zui-table-cell cell-xl"><span>SPD单号</span></div></th>
                                <th><div class="zui-table-cell cell-xl"><span>领用单号</span></div></th>
                                <th><div class="zui-table-cell cell-s"><span>领用方式</span></div></th>
                                <th><div class="zui-table-cell cell-s"><span>库房</span></div></th>
                                <th><div class="zui-table-cell cell-s"><span>领用人</span></div></th>
                                <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                                <th><div class="zui-table-cell cell-s"><span>领用科室</span></div></th>
                                <th><div class="zui-table-cell cell-s"><span>制单日期</span></div></th>
                                <th><div class="zui-table-cell cell-s"><span>申领单号</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body "  @scroll="scrollTable($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in ckdList" @click="checkSelect([$index,'some','jsonList'],$event)"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()" @dblclick="queryMx($index)">
                                <td v-if="!csN04003002002201201"><div class="zui-table-cell cell-xl" v-text="item.spdno"></div></td>
                                <td><div class="zui-table-cell cell-xl" v-text="item.ckdh"></div></td>
                                <td><div class="zui-table-cell cell-s" >领用出库</div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.kfmc"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.lyrxm"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.zdyxm"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.lyksmc"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')"></div></td>
                                <td><div class="zui-table-cell cell-s" v-text="item.sldh"></div></td>
                            </tr>
                            </tbody>
                        </table>

                    </div>
                </div>
            <div style="width:80%">
                <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>药品编码</span></div></th>
                        <th><div class="zui-table-cell cell-xxl text-left"><span>药品名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>批价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>进价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>药品批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>分装比列</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>招标类型</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body "  @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in ckdmxList" @click="checkSelect([$index,'some','ckdmxList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" @dblclick="edit($index)">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                        <td><div class="zui-table-cell cell-xxl text-over-2 text-left" v-text="item.ypmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypgg"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.cksl"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div></td>
                        <td><div class="zui-table-cell cell-s" ></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypjj"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yplj"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.xtph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.scrq,'date')"></div></td>
                        <td><div class="zui-table-cell cell-s" ></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.cdmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.fzbl"></div></td>
                        <td><div class="zui-table-cell cell-s" ></div></td>
                        <td><div class="zui-table-cell cell-s" ></div></td>
                        <td><div class="zui-table-cell cell-s" ></div></td>
                    </tr>
                    </tbody>
                </table>

            </div>
        </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>

    </div>
</div>
<script src="lysh.js"></script>
</body>
</html>
