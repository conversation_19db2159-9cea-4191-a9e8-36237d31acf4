@import "../../../../css/baseColor";
.rkgl-position{
  position: fixed;
  bottom:10px;
  display: flex;
  justify-content: flex-start;
  left:10px;
  right:10px;
  width: auto;
  z-index: 11;
  height:60px;
  background: @colorff;
  align-items: center;
  color: @color81;
  padding-left: 20px;
  border-top: 1px solid @coloree;
  .rkgl-fl{
    width: auto;
    float: left;
    padding-left: 15px;
    i{
      float: left;
      padding-left: 20px;
    }
  }
  span{
    display: block;
  }
  .rkgl-fr{
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
.icon-fy:before{
  left:39px !important;
}
.icon-dy:before{
  left:17px !important;
}
.icon-ty:before{
  left:77px !important;
}
.slgl-by{
  width: 100%;
  height: 40px;
  display: flex;
  line-height: 40px;
  background:@colorRgbf2a;
  justify-content: space-between;
  i{
    width: 20%;
    display: block;
    text-align: center;
    color: @color75;
    &:nth-child(5){
      padding-right: 15px;
    }
    em{
      color: @color35;
      padding-left: 5px;
      float: left;
    }
  }
}
.fyxm-tab div{
  border: none !important;
  padding: 0 40px 0 0;
}
.fyxm-tab div:first-child{
  border: none !important;
}
.actives{
  border-bottom: 2px solid @color1a;
  color: @color1a;
}
.fyty-fr{
  float: right;
  width: 120px;
  padding-right: 20px !important;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  i{
    float: left;
  }
  .fyty-select{
    width:50px;
    border: none !important;
    text-align: right;
    -webkit-appearance: none;
    position: relative;
    height: 26px;
    line-height: 26px;

  }

  .fyty-dsj{
    width: 10px;
    position: absolute;
    top:29px;
    right: 0;
    height: 10px;
    background: url("../../../../css/@{image}<EMAIL>") center right no-repeat;
    transform: rotate(270deg);
  }
}

.cfhj-top{
  width: 100%;
  height: 36px;
  background:@colored;
  line-height: 36px;
  li{
    width: 100%;
    display: flex;
    justify-content: center;
    i{
      width: calc(~"(100% - 50px)/8");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
    }
  }
}
.cfhj-content{
  width: 100%;
  overflow: auto;
  max-height:500px;
  li{
    width: 100%;
    display: flex;
    border:1px solid @coloree;
    border-top: none;
    justify-content: center;
    i{
      width: calc(~"(100% - 50px)/8");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
    }
    &:hover{
      background:rgba(26,188,156,0.08);
    }
  }
}
.title-width{
  width: 70px !important;
}
.contenter{
  width:100%;
  background: #fff;
  min-height: 768px;
  float: left;
  padding: 0 15px;
  .cont-left{
    width:calc(~"(100% - 15px) /2");
    float: left;
    .left-top{
      width:100%;
      background:@colored;
      overflow: hidden;
      height: 36px;
      line-height: 36px;
      i{
        width: calc(~"(100% - 50px)/3");
        display: block;
        float:left;
        text-align: center;
        &:nth-child(1){
          width: 50px !important;
        }
      }
    }
    .left-list{
      width: 100%;
      overflow: auto;
      max-height:650px;
      li{
        width: 100%;
        line-height: 40px;
        border: 1px solid #eee;
        border-top:none;
        overflow: hidden;
        cursor: pointer;
        i{
          width: calc(~"(100% - 50px)/3");
          display: block;
          float:left;
          text-align: center;
          &:nth-child(1){
            width: 50px !important;
          }
          em{
            margin: 0 auto;
          }
        }
        &:hover{
          background: @colorRgb08;

        }
      }

    }

  }
  .cont-right{
    float: right;
    .right-top{
      width:100%;
      background:@colored;
      overflow: hidden;
      height: 36px;
      line-height: 36px;
      i{
        width: calc(~"(100% - 50px)/5");
        display:block;
        text-align: center;
        float: left;
        &:nth-child(1){
          width: 50px !important;
        }
      }
    }
    .right-list{
      width: 100%;
      overflow: auto;
      max-height:650px;
      li{
        width: 100%;
        line-height: 40px;
        border: 1px solid #eee;
        border-top: 1px solid #fff;
        overflow: hidden;
        cursor: pointer;
        i{
          width: calc(~"(100% - 50px)/5");
          display: block;
          float:left;
          text-align: center;
          &:nth-child(1){
            width: 50px !important;
          }
          em{
            margin: 0 auto;
          }
        }
        &:hover{
          background: @colorRgb08;

        }
      }
    }

  }
}
.zui-form .zui-form-label{
  left: 5px !important;
}
.h2title{
  display: none;
}
.pop-850 .ksys-side{
  padding: 15px;
}