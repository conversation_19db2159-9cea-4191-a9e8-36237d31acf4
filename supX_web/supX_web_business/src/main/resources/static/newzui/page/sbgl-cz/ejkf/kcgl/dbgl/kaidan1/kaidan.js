var wrapper = new Vue({
        el: '#wrapper',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],
            ylbm: 'N040030020022003',
            title: '',
            totle: '',
            zdy: userId,
            dbdList: [],
            thdDetail: [], // 退货单明细集合
            yfbm: null,
            zhuangtai: {
                "0": "待审核",
                "1": "已审核",
                "2": "作废",
            },
            queren: {
                "0": "待确认",
                "1": "确认"
            },
            cgryList: [], // 退库人员
            KSList: [],
            KFList: [],
            ghdwList: [],
            // 材料信息对象
            isUpdate: 0,
            modifyIndex: null,
            isShow: false,
            popContent: {},
            bsdContent: {
                ckfs: "03", // 报损方式
                "zdrq": getTodayDateTime(),
                lyr: userId
                // 操作人
            },
            // 调拨单
            dbdContent: {
                'zdrq': getTodayDate(),
                'kfbm': null,
            },
            zdrq: getTodayDateTime(), // 获取制单日期
            zdyxm: '',
            qxksbm: '',
            rkd: {}, // 入库单对象
            json: {
                jjzj: 0,
                ljzj: 0
            },//价格总计
            yfList: [],
            ryList: [],
            flag: false,
            ckd: null,//页面传过来的数据-出库单号
            dbd: null,
            isNew: 0
        },
        computed:{
            getKS:function () {
                    for (var i = 0; i < this.yfList.length; i++) {
                        if (this.popContent.yfbm == wrapper.yfList[i].yfbm) {
                            this.ksbm = this.yfList[i].ksbm;
                            break;
                        }
                    }
            },
            money:function () {
                var reducers = {
                    totalInEuros: function(state, item) {
                        return state.jjzj += item.ypjj * parseFloat(item.cksl);
                    },
                    totalInYen: function(state, item) {
                        return state.ljzj += item.yplj * parseFloat(item.cksl);
                    }
                };
                var manageReducers = function(reducers){
                    return function(state, item){
                        return Object.keys(reducers).reduce(function(nextState, key){
                            reducers[key](state, item);
                            return state;
                        },{})
                    }
                }
                var bigTotalPriceReducer = manageReducers(reducers);
                this.jsonList.reduce(bigTotalPriceReducer, this.json={
                    jjzj:0,
                    ljzj:0,
                });
            }
        },
        watch: {
            'popContent.yfbm': function () {
                if (wrapper.popContent.yfbm == wrapper.popContent.dbyf) {
                    malert("调入、调出二级库房不能相同！",'top','defeadted');
                    Vue.set(wrapper.popContent, 'yfbm', '');
                }
            },
            'popContent.dbyf': function () {
                if (wrapper.popContent.yfbm == wrapper.popContent.dbyf) {
                    malert("调入、调出二级库房不能相同！",'top','defeadted');
                    Vue.set(wrapper.popContent, 'dbyf', '');
                }
            },
        },
        updated: function () {
            changeWin();
        },
        created: function () {
            this.initial();
        },
        methods: {
            initial: function () {
                this.ckd = JSON.parse(sessionStorage.getItem('dbglitem'));
                    if (this.ckd.ckdh) {
                        this.isNew = 0;
                        //查询调拨单和明细
                        $.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=query&bean=" + JSON.stringify(this.ckd), function (json) {
                                if (json.a == 0 && json.d) {
                                    wrapper.dbd = json.d.list[0];//调拨单详情
                                    wrapper.getYFData(wrapper.dbd);
                                }
                            });
                    } else {
                        this.isNew = 1;//新开单
                        this.getYFData();
                    }
                        this.zdyxm = this.ckd.zdrxm;
                        this.zdrq = this.fDate(this.ckd.zdrq, 'date');
                        this.popContent.yfbm = this.ckd.yfbm;
                        this.popContent.dbyf = this.ckd.dbyf;
                        this.popContent.bzms = this.ckd.bzms;
            },

            // 获取二级库房
            getYFData: function (item) {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf", function (json) {
                        if (json.a == 0) {
                            wrapper.yfList = json.d.list;
                        }
                    });
                if (item) {
                    Vue.set(this.popContent, 'yfbm', item.yfbm);//调出
                    Vue.set(this.popContent, 'dbyf', item.dbyf);//调入
                    Vue.set(this.bsdContent, 'zdrq', item.zdrq);//制单日期
                    Vue.set(this.bsdContent, 'lyr', item.jbrmc);//制单人
                    this.getData(item.ckdh);
                }
            },
            // 作废
            invalidData: function () {
                if (confirm("确认作废-" + this.dbd.ckdh + "-调拨单吗?")) {
                    var obj = {
                        ckdh: wrapper.dbd.ckdh
                    };
                    //作废调拨单
                    this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=zf', JSON.stringify(obj)).then(function (data) {
                            if (data.body.a == 0) {
								window.top.$("#调拨管理")[0].contentWindow.getData();
                                kd.topClosePage('page/hcgl/ejkf/kcgl/dbgl/kaidan.html', 'page/hcgl/ejkf/kcgl/dbgl/dbgl.html');
								
                            } else {
                                malert(data.body.c,'top','defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });
                }

            },
            printDJ: function () {
                var reportlets = "[{reportlet: 'fpdy%2Fejkf%2Fyfgl_dbckd.cpt',yljgbm:'" + jgbm + "',yfbm:'" + this.popContent.yfbm + "',ckdh:'" + this.ckd.ckdh + "'}]";
                if (!FrPrint(reportlets, null)) {
                    window.print();
                }
            },
            //获取明细
            getData: function (ckdh) {
                common.openloading('.zui-table-view')
                var bean = {
                    ckdh: ckdh
                }
                $.getJSON("/actionDispatcher.do?reqUrl=YfbKcglDbgl&types=queryMx&bean=" + JSON.stringify(bean), function (json) {
                        if (json.a == 0 && json.d) {
                            wrapper.jsonList = json.d;
                        }
                    });
                common.closeLoading()
            },
            // 审核
            passData: function () {
                if (!this.dbd.ckdh) {
                    malert('请选择调拨单！','top','defeadted');
                    return;
                }
                //设置审核标志
                wrapper.dbd.shzfbz = 1;
                wrapper.dbd.qrbz = 0;

                //方式请求审核调拨单
                this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=updateDbd', JSON.stringify(wrapper.dbd)).then(function (data) {
                        if (data.body.a == 0) {
                            malert("审核成功",'top','success');
                            window.top.$("#调拨管理")[0].contentWindow.getData();
                            this.topClosePage('page/hcgl/ejkf/kcgl/dbgl/kaidan1/kaidan.html', 'page/hcgl/ejkf/kcgl/dbgl/dbgl.html');
							
                        } else {
                            malert(data.body.c,'top','defeadted');
                        }
                    }, function (error) {
                        console.log(error);
                    });
            },
            // 取消
            cancel: function () {
                this.topClosePage('page/hcgl/ejkf/kcgl/dbgl/kaidan1/kaidan.html', 'page/hcgl/ejkf/kcgl/dbgl/dbgl.html');
            },
        }
    });
window.addEventListener('storage', function (e) {
    if (e.key == 'dbglitem' && e.oldValue !== e.newValue) {
        wrapper.initial();
    }
});
