var todayTime = getTodayDate();
var ischeck = null;
var ToolMenu = new Vue({
    el: '.toolMenu',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        printNameCsz:'',
        clickType:true,
    },
    mounted:function(){
        this.getPrintName()
    },
    methods: {
        openSetTwd:function(){
            SetUserTwd.wjzShow=true
            SetUserTwd.$nextTick(function () {
                $("#loadPage").load("childpage/batch.html").fadeIn(300);
            })
        },
        //刷新
        getData: function () {
            right.getData();
        },
        //保存
        save: function () {
        	if(!ToolMenu.clickType) return;
            ToolMenu.clickType=false;
            var list = [];
            if (yzclLeft.isChecked.length < 1 || yzclLeft.isChecked == null) {
                malert("请选择一个病人之后再操作！", "top", "defeadted")
                ToolMenu.clickType=true;
                return;
            }
            for (var i = 0; i < yzclLeft.brlist.length; i++) {
                if (yzclLeft.isChecked[i]) {
                    list.push(yzclLeft.brlist[i]);
                }
            }
            if (list.length > 1) {
                ToolMenu.clickType=true;
                malert("此操作只针对单人！", "top", "defeadted");
                return;
            }
console.log(right.twdQtjlContent.ttpgx)

//        	if(left.sfgbsj==1){
//        		return false;
//        	}
//			if(left.sfccsj==1){
//			    return false;
//			}
            right.twdList = [];
            var zyh = right.brxxContent['zyh'];
            var ryksbm = right.brxxContent['ryks'];
            var yebh = right.brxxContent['yebh'];
            //var clrq = right.clrq;
            var clrq = right.cxrq; // @yqq 取显示的input框中的时间
            if (zyh && ryksbm  && clrq ) {
                //处理体温单集合
                right.oneTwd['tbsmsj'] = new Date(clrq + ' ' + right.tbsmsj + ':00').getTime();
                right.twoTwd['tbsmsj'] = new Date(clrq + ' ' + right.tbsmsj1 + ':00').getTime();
                right.threeTwd['tbsmsj'] = new Date(clrq + ' ' + right.tbsmsj2 + ':00').getTime();
                right.fourTwd['tbsmsj'] = new Date(clrq + ' ' + right.tbsmsj3 + ':00').getTime();
                right.fiveTwd['tbsmsj'] = new Date(clrq + ' ' + right.tbsmsj4 + ':00').getTime();
                right.sixTwd['tbsmsj'] = new Date(clrq + ' ' + right.tbsmsj5 + ':00').getTime();
                right.oneTwd['clrq'] = clrq;
                right.twoTwd['clrq'] = clrq;
                right.threeTwd['clrq'] = clrq;
                right.fourTwd['clrq'] = clrq;
                right.fiveTwd['clrq'] = clrq;
                right.sixTwd['clrq'] = clrq;
                right.oneTwd['zyh'] = zyh;
                right.twoTwd['zyh'] = zyh;
                right.threeTwd['zyh'] = zyh;
                right.fourTwd['zyh'] = zyh;
                right.fiveTwd['zyh'] = zyh;
                right.sixTwd['zyh'] = zyh;
                right.oneTwd['ryksbm'] = ryksbm;
                right.twoTwd['ryksbm'] = ryksbm;
                right.threeTwd['ryksbm'] = ryksbm;
                right.fourTwd['ryksbm'] = ryksbm;
                right.fiveTwd['ryksbm'] = ryksbm;
                right.sixTwd['ryksbm'] = ryksbm;
                right.oneTwd['ryksmc'] = right.brxxContent['ryksmc'];
                right.twoTwd['ryksmc'] = right.brxxContent['ryksmc'];
                right.threeTwd['ryksmc'] = right.brxxContent['ryksmc'];
                right.fourTwd['ryksmc'] = right.brxxContent['ryksmc'];
                right.fiveTwd['ryksmc'] = right.brxxContent['ryksmc'];
                right.sixTwd['ryksmc'] = right.brxxContent['ryksmc'];
                right.oneTwd['yebh'] = right.brxxContent['yebh']=='000'?undefined:right.brxxContent['yebh'];
                right.twoTwd['yebh'] = right.brxxContent['yebh']=='000'?undefined:right.brxxContent['yebh'];
                right.threeTwd['yebh'] = right.brxxContent['yebh']=='000'?undefined:right.brxxContent['yebh'];
                right.fourTwd['yebh'] = right.brxxContent['yebh']=='000'?undefined:right.brxxContent['yebh'];
                right.fiveTwd['yebh'] = right.brxxContent['yebh']=='000'?undefined:right.brxxContent['yebh'];
                right.sixTwd['yebh'] = right.brxxContent['yebh']=='000'?undefined:right.brxxContent['yebh'];
                right.twdList.push(right.oneTwd);
                right.twdList.push(right.twoTwd);
                right.twdList.push(right.threeTwd);
                right.twdList.push(right.fourTwd);
                right.twdList.push(right.fiveTwd);
                right.twdList.push(right.sixTwd);
                //处理体温单其他记录
                right.twdQtjlContent['clrq'] = clrq;
                right.twdQtjlContent['zyh'] = zyh;
                right.twdQtjlContent['ryksbm'] = ryksbm;
                right.twdQtjlContent['ryksmc'] = right.brxxContent['ryksmc'];
                right.twdQtjlContent['yebh'] = right.brxxContent['yebh']=='000'?undefined:right.brxxContent['yebh'];
                // @yqq判断是否包含 时段
                var type = false;
                right.twdList.forEach(obj => {
                    if(!obj.clsd)
                        type = true;
                });
                if(type){
                    ToolMenu.clickType = true;
                    malert('时段为空，请刷新页面后重试！', "top", "defeadted");
                    return;
                }

                //拼接对象
                //var json = '{"list": {"twbqtjlModel":' + JSON.stringify(right.twdQtjlContent) + ',"twbList":' + JSON.stringify(right.twdList) + '}}';
                var json = '{"twbqtjlModel":' + JSON.stringify(right.twdQtjlContent) + ',"twbList":' + JSON.stringify(right.twdList) + '}';
                //请求后台
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=save', json).then(function (data) {
                    if (data.body.a == 0) {
                        malert("上传数据成功！", "top", "success");
                        ToolMenu.clickType = true;
//                        left.queryPatient(ischeck);
                        ToolMenu.getData();
                    } else {
                        malert(data.body.c, "top", "defeadted");
                        ToolMenu.clickType = true;
                    }
                }, function (error) {
                	ToolMenu.clickType = true;
                    console.log(error);
                });
            } else {
                malert("请选择相关病人！", "top", "success");
            }
        },
        print: function () {
            // if(this.printNameCsz){
            //     right.addImgAndRemove('twd_cvs')
            //     right.addImgAndRemove('twd_title')
                // right.LOdopPrint('补打预交', "<link href='childpage/twd.css' type='text/css' rel='stylesheet'>", 'tw-table', this.printNameCsz)
                // right.LOdopPrint('补打预交', "<link href='childpage/twd.css' type='text/css' rel='stylesheet'>", 'tw-table', 'Microsoft XPS Document Writer')
            // }else{
                $('.yzclRight').removeClass('yzcl-792')
                 window.print();
                 $('.yzclRight').addClass('yzcl-792')
            // }
        },
        setCsrq:function(dom,add,code){
            var aa=new Date($('#'+dom).val());
            if(add){
                aa.setDate(aa.getDate()+7);
            }else{
                aa.setDate(aa.getDate()-7);
            }
            var pared=new Date(aa).getTime();
            right[code] = right.fDate(pared, 'date');
        },
        prev: function () {
            if(right.which =='0'){
               this.setCsrq('time1',0,'cxrq')
            }else{
                this.setCsrq('time2',0,'cxrq1')
            }
        },
        next:function () {
            if(right.which =='0'){
                this.setCsrq('time1',1,'cxrq')
            }else{
                this.setCsrq('time2',1,'cxrq1')
            }
        },
        getPrintName:function () {
            window.top.J_tabLeft.csqxparm.csbm = "N010024007";
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm="+ JSON.stringify(window.top.J_tabLeft.csqxparm) , function (json) {
                if (json.a == 0 && json.d!=null && json.d.length>0) {
                    ToolMenu.printNameCsz=json.d[0].csz
                }
            })
        }
    }
});
var dateend = getTodayDateEnd();
var datestart = getTodayDateBegin();
//*****************左边展示区*************************

//*************************右边体温单列表展示***************************************
var right = new Vue({
    el: '#twdList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popContent:{},
        qsxzList:[],
        which: 0,
        twd:'1',
        brxxContent: JSON.parse(JSON.stringify(yzclLeft.HszbrItem)),
        // brxxContent: yzclLeft.HszbrItem,
        oldbrxxContent: {},
        twdList: [],                        //病人体温单信息
        //time: null,                         //默认时间
        oneTwd: {},                         //第一个时间段
        twoTwd: {},                         //第二个时间段
        threeTwd: {},                       //第三个时间段
        fourTwd: {},                        //第四个时间段
        fiveTwd: {},                        //第五个时间段
        sixTwd: {},                         //第六个时间段
        twdQtjlContent: {},                 //体温单其他记录
        lrTime: null,
        time: '',
        clrq: null,
        pageType: 0,
        tbsmsj: '',
        tbsmsj1: '',
        tbsmsj2: '',
        tbsmsj3: '',
        tbsmsj4: '',
        tbsmsj5: '',
        tbsmObj:{
            'oneTwd':'',
            'twoTwd':1,
            'threeTwd':2,
            'fourTwd':3,
            'fiveTwd':4,
            'sixTwd':5,
        },
        HszHlywTwd:{
            '1':'oneTwd',
            '2':'twoTwd',
            '3':'threeTwd',
            '4':'fourTwd',
            '5':'fiveTwd',
            '6':'sixTwd',
        },
        option: {},
        csqxContent: {}, //参数权限对象
        qtxmList:[],//体温单其他项目显示
        cxrq:'', // 查询日期
        cxrq1:'', // 查询日期
        dqym:'', //当前打印页码
		ttpg_tran: {
            "无": "无",
            "轻度": "轻度",
            "中度": "中度",
			"重度": "重度"
        },
		gms_tran: {
		    "0": "无",
		    "1": "有",
		},
    },
    mounted: function () {
        Mask.newMask(this.MaskOptions('time2'));
        this.option=laydate.render({
            elem: '#time1',
            format: 'yyyy-MM-dd',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                right.cxrq = value;
            }
        });
        this.setLaydate()
        laydate.render({
            elem: '#time2',
            format: 'yyyy-MM-dd',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                right.cxrq1 = value;
            }
        });
        this.oldbrxxContent.brxm = yzclLeft.HszbrItem.brxm;
        this.oldbrxxContent.brxb = yzclLeft.HszbrItem.brxb;
        this.oldbrxxContent.nl = yzclLeft.HszbrItem.nl;
        this.oldbrxxContent.nldw = yzclLeft.HszbrItem.nldw;
    },
    created: function () {
    	this.getCsqx();
        //this.clrq = this.$options.filters['formDate'](new Date());
    	this.cxrq = this.$options.filters['formDate'](new Date());
    	this.cxrq1 = this.$options.filters['formDate'](this.brxxContent.jrrq);
        this.getData();
        this.iscf();
    },
    watch: {
        'cxrq1': function (val, oldVal) {
            if (val !== oldVal) {
            	// @yqq 计算体温单时段开始日期，从入院使劲按算起，每7天为一页。 start
            	var ryrq = right.fDate(right.brxxContent.jrrq, 'date');
            	var dqrq = right.fDate(val, 'date');

                var start = new Date(ryrq.replace("-", "/").replace("-", "/"));
                var end = new Date(dqrq.replace("-", "/").replace("-", "/"));
                if (end < start) {
                    malert("只能选择入院之后的日期！", "top", "defeadted");
                    return;
                }
            	var xcts = (new Date(dqrq) - new Date(ryrq)) / (1000 * 60 * 60 * 24);
            	// 根据相差天数计算出此页开始日期
            	var x = Math.floor(xcts/7);
            	// 判断打印页码
            	right.dqym = x <= 0 ? '1' : x+1;
            	startDay = new Date(ryrq).getTime() + x * (1000 * 60 * 60 * 24) * 7;
            	// right.cxrq1 = right.fDate(startDay, 'date');
            	// @yqq end...
                //printNumber.$forceUpdate()

                if (right.pageType == 1) {
                    other.getOther();
                }
            }
        },
        'cxrq':function (val, oldVal) {
            if (val !== oldVal) {
                var starttime = right.fDate(right.brxxContent.ryrq, 'date');
                var endtime = val;
                var start = new Date(starttime.replace("-", "/").replace("-", "/"));
                var end = new Date(endtime.replace("-", "/").replace("-", "/"));
                if (end < start) {
                    malert("只能选择入院之后的日期！", "top", "defeadted");
                    return;
                }
                right.getData();
            }
        },
        'brxxContent':function (val, oldVal) {
            this.setLaydate()
        }
    },
    methods: {
        setDate:function(value, types){
            if (!value) {
                return "";
            }
            var date = new Date(value),
                i = date.getMinutes(),
                s = date.getSeconds();
            var i=getNow(i),s=getNow(s);
            if(types == 'minutes'){
                return  i
            }else if(types == 'seconds'){
                return  s
            }
        },
        setLaydate:function(){
            var that=this;
            this.option.config.min = {
                year: that.fDate(new Date(that.brxxContent.ryrq), 'year'),
                month: that.fDate(new Date(that.brxxContent.ryrq), 'month')-1,
                date: that.fDate(new Date(that.brxxContent.ryrq), 'day'),
                hours: new Date(that.brxxContent.ryrq).getHours(),
                minutes: that.setDate(new Date(that.brxxContent.ryrq), 'minutes'),
                seconds:that.setDate(new Date(that.brxxContent.ryrq), 'seconds')
            }
            this.option.config.max = {
                year: that.fDate(new Date(), 'year'),
                month: that.fDate(new Date(), 'month')-1,
                date: that.fDate(new Date(), 'day'),
                hours: new Date().getHours(),
                minutes: that.setDate(new Date(), 'minutes'),
                seconds:that.setDate(new Date(), 'seconds')
            }
        },
        commonResultChange:function(val){
            console.log(12)
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.brxxContent['yebh']=this.qsxzList[val[5]].yebh;
            this.$forceUpdate();
            var ageContent = {};
            if(this.brxxContent['yebh'] == '000'){
                right.qtxmList = right.csqxContent.N03004200241;//成人显示
                this.brxxContent.brxm = this.oldbrxxContent.brxm;
                this.brxxContent.brxb = this.oldbrxxContent.brxb;
                this.brxxContent.nl = this.oldbrxxContent.nl;
                this.brxxContent.nldw = this.oldbrxxContent.nldw;
            }else{
                right.qtxmList = right.csqxContent.N03004200242;//婴儿显示
                ageContent.brxm = this.qsxzList[val[5]].yexm;
                ageContent.brxb = this.qsxzList[val[5]].xb;
                ageContent.nl = this.qsxzList[val[5]].yenl;
                ageContent.nldw = this.qsxzList[val[5]].yenldw;
                if(!ageContent.nl){
                    ageContent.nl = this.toAge(this.qsxzList[val[5]].csrq).age;
                }
                if(!ageContent.nldw){
                    ageContent.nldw = this.toAge(this.qsxzList[val[5]].csrq).unitNum;
                }
                var obj=Object.assign(this.brxxContent,ageContent);
                this.brxxContent=JSON.parse(JSON.stringify(obj))
            }
            if(this.which==0){
                right.loadCon(0);
            }else{
                other.qtxm1 = right.qtxm1;
                panel.brxxContent.brxm = this.brxxContent.brxm;
                panel.brxxContent.nl = this.brxxContent.nl;
                panel.brxxContent.nldw = this.brxxContent.nldw;
                panel.brxxContent.brxb = this.brxxContent.brxb;
                right.loadCon(1);
            }
        },
        iscf: function () {
            var that=this;
            this.qsxzList=[];
            this.popContent={}
            var parm = {
                zyh: this.brxxContent.zyh
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' + JSON.stringify(parm), function (json) {
                    if (json.a == '0') {
                        that.qsxzList = [];
                        var list = [];
                        if (json.d.list != null && json.d.list.length > 0) {
                            list = json.d.list;
                        }
                        var qb = {
                            yexm: right.brxxContent.brxm,//如果有影响请还原上面代码，注释本行代码
                            yebh: "000",
                        };
                        list.unshift(qb);
                        that.qsxzList = list;//亲属选择
                        that.popContent.yebh = that.qsxzList[0].yebh;
                        that.brxxContent.yebh = that.qsxzList[0].yebh;
                    }
                });
        },
        resultChangeData:function(val){
            if(val[0] == '入院'){
                var index=this.tbsmObj[val[2][0]]
                this['tbsmsj'+index]=this.fDate(this.brxxContent.ryrq,'time')
            }
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.nextFocus(val[1],6)
        },
        resultChangeMx:function(val){
            Vue.set(this[val[1][0]], val[1][1], val[0]);
            console.log(this[val[1][0]])
            // this.twdQtjlContent[val[1][val[1].length - 1]]=val[0]
            // console.log(this.twdQtjlContent.mx)
        },
        getData: function () {
            if(this.pageType == 0){
                this.twdQtjlContent = {};
                this.oneTwd={}
                this.twoTwd={}
                this.threeTwd={}
                this.fourTwd={}
                this.fiveTwd={}
                this.sixTwd={}
                var zyh = this.brxxContent['zyh'];
                var ryksbm = this.brxxContent['ryks'];
                var yebh = this.brxxContent['yebh']=='000'?undefined:this.brxxContent['yebh'];
                var clrq = this.clrq;
                if (zyh != null && ryksbm != undefined && ryksbm != null) {
                    var parm = {
                        zyh: zyh,
                        ryksbm: ryksbm,
                        yebh: yebh,
                        //clrq: clrq
                        clrq: this.cxrq
                    };
                    //请求后台查询病人体温单信息
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryByOneBr&parm=' + JSON.stringify(parm),
                        function (json) {
                            if (json.a == 0 && json.d) {
                                var type = true;

                                var yebhType = true;
                                for (var i = 0; i <json.d.list.length ; i++) {
                                	yebhType = json.d.list[i].yebh ? false : true;
                                    // 录入入院时间后，不再默认入院时间
                                    if(json.d.list[i].tbsm == '入院' &&  json.d.list[i].tbsmsj){
                                        type = false;
                                    }
                                    right[right.HszHlywTwd[json.d.list[i]['clsd']]]=json.d.list[i]
                                }
                                // if (json.d.list[0] != undefined) {
                                //     right.oneTwd = json.d.list[0];
                                // }
                                // if (json.d.list[1] != undefined) {
                                //     right.twoTwd = json.d.list[1];
                                // }
                                // if (json.d.list[2] != undefined) {
                                //     right.threeTwd = json.d.list[2];
                                // }
                                // if (json.d.list[3] != undefined) {
                                //     right.fourTwd = json.d.list[3];
                                // }
                                // if (json.d.list[4] != undefined) {
                                //     right.fiveTwd = json.d.list[4];
                                // }
                                // if (json.d.list[5] != undefined) {
                                //     right.sixTwd = json.d.list[5];
                                // }
                                right.oneTwd.twbw= right.oneTwd.twbw==null || right.oneTwd.twbw==undefined ?'1':right.oneTwd.twbw
                                right.twoTwd.twbw= right.twoTwd.twbw==null || right.twoTwd.twbw==undefined ?'1':right.twoTwd.twbw
                                right.threeTwd.twbw= right.threeTwd.twbw==null || right.threeTwd.twbw==undefined ?'1':right.threeTwd.twbw
                                right.fourTwd.twbw= right.fourTwd.twbw==null || right.fourTwd.twbw==undefined ?'1':right.fourTwd.twbw
                                right.fiveTwd.twbw= right.fiveTwd.twbw==null || right.fiveTwd.twbw==undefined ?'1':right.fiveTwd.twbw
                                right.sixTwd.twbw= right.sixTwd.twbw==null || right.sixTwd.twbw==undefined ?'1':right.sixTwd.twbw
                                right.oneTwd['clsd'] = '1';
                                right.twoTwd['clsd'] = '2';
                                right.threeTwd['clsd'] = '3';
                                right.fourTwd['clsd'] = '4';
                                right.fiveTwd['clsd'] = '5';
                                right.sixTwd['clsd'] = '6';
                                Vue.set(right, 'tbsmsj', right.fDate(right.oneTwd.tbsmsj, "time"));
                                Vue.set(right, 'tbsmsj1', right.fDate(right.twoTwd.tbsmsj, "time"));
                                Vue.set(right, 'tbsmsj2', right.fDate(right.threeTwd.tbsmsj, "time"));
                                Vue.set(right, 'tbsmsj3', right.fDate(right.fourTwd.tbsmsj, "time"));
                                Vue.set(right, 'tbsmsj4', right.fDate(right.fiveTwd.tbsmsj, "time"));
                                Vue.set(right, 'tbsmsj5', right.fDate(right.sixTwd.tbsmsj, "time"));

                                // 显示默认入院时间。
                                // Vue.set(right, 'tbsmsj2', null);
                                if(right.csqxContent.N03004200237 == '1' && type && yebhType){
                                    var rq = right.fDate(right.brxxContent.jrrq, 'datetime');
                                    var sd = rq.substr(11,2);
                                    var ryrq = rq.substr(0,10);
                                    if(right.cxrq == ryrq){
                                        // 判断入院是什么时段
                                        for (var i = 0; i <6; i++) {
                                            var a =  parseInt(sd) - (2 + 4*i);
                                            if((a < 2 && a >= -2) || a <= -21){
                                                if(''+(i+1) == '1' && !right['tbsmsj']){
                                                    right.oneTwd['tbsm'] = '入院';
                                                    Vue.set(right, 'tbsmsj', rq.substr(11));
                                                    break;
                                                }
                                                if(''+(i+1) == '2' &&  !right['tbsmsj1']){
                                                    right.twoTwd['tbsm'] = '入院';
                                                    Vue.set(right, 'tbsmsj1', rq.substr(11));
                                                    break;
                                                }
                                                if(''+(i+1) == '3'  && !right['tbsmsj2']){
                                                    right.threeTwd['tbsm'] = '入院';
                                                    Vue.set(right, 'tbsmsj2', rq.substr(11));
                                                    break;
                                                }
                                                if(''+(i+1) == '4' &&  !right['tbsmsj3']){
                                                    right.fourTwd['tbsm'] = '入院';
                                                    Vue.set(right, 'tbsmsj3', rq.substr(11));
                                                    break;
                                                }
                                                if(''+(i+1) == '5' &&  !right['tbsmsj4']){
                                                    right.fiveTwd['tbsm'] = '入院';
                                                    Vue.set(right, 'tbsmsj4', rq.substr(11));
                                                    break;
                                                }
                                                if(''+(i+1) == '6' &&  !right['tbsmsj5']){
                                                    right.sixTwd['tbsm'] = '入院';
                                                    Vue.set(right, 'tbsmsj5', rq.substr(11));
                                                    break;
                                                }
                                            }
                                        }

                                    }
                                }

                            } else {
                                malert(json.c, 'top', 'defeadted');
                                return false;
                            }
                        });
                    //请求后台查询体温单其他记录信息
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=select&parm=' + JSON.stringify(parm),
                        function (json) {
                            if (json.a == 0) {
                                //给体温单其他记录对象赋值
                            	if( json.d){
                            		right.twdQtjlContent = json.d;
                            	}

                            } else {
                                malert(json.c, 'top', 'defeadted');
                                return false;
                            }
                        });
                }
            }else {
                other.getOther()
            }

        },
        loadCon: function (index) {
            right.pageType = index;
            right.which = index;
            if (index == 0) {
                $(".dataEnter").show();
                $("#twd").html('');
                right.getData();
            } else {
                $(".dataEnter").hide();
                $("#twd").load('childpage/twd.html');
            }
        },
        getCsqx:function(){
            var parm = {"ylbm": 'N030042002', "ksbm": this.brxxContent.ryks};
            this.$http.get('/actionDispatcher.do', {params:{reqUrl:'CsqxAction',types:'csqx',parm:JSON.stringify(parm)}}).then( function (json) {
                if (json.body.a == 0 && json.body.d && json.body.d.length > 0) {
                        for (var i = 0; i < json.body.d.length; i++) {
                            var csjson = json.body.d[i];
                            switch (csjson.csqxbm) {
                                case "N03004200230"://婴儿体温单黄疸显示
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200230 = csjson.csz;
                                    }
                                    break;
                                case "N03004200231":// 是否使用氧饱和度
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200231 = csjson.csz;
                                    }
                                    break;
                                case "N03004200237":// 体温单首次录入时自动获取入院时间
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200237 = csjson.csz;
                                    }
                                    break;
                                case "N03004200239":// 展示信息
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200239 = csjson.csz.split(',');
                                    }
                                    break;
                                case "N03004200240":// 体温单大小便次 显示导尿灌肠标志
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200240 = csjson.csz;
                                    }
                                    break;
                                case "N03004200241":// 成人体温单其他项目1 - 12显示名字，一一对应，逗号隔开
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200241 = csjson.csz.split(',');
                                        right.qtxmList = csjson.csz.split(',');
                                    }
                                    break;
                                case "N03004200242":// 婴儿体温单其他项目1 - 12显示名字，一一对应，逗号隔开
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200242 = csjson.csz.split(',');
                                    }
                                    break;
                                case "N03004200263":// 体温单，出入量汇总显示 0-否 1-是
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200263 = csjson.csz;
                                    }
                                    break;
                                case "N03004200264":// 婴儿体温单隐藏部分数据（台江）
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200264 = csjson.csz;
                                    }
                                    break;
                                case "N03004200279": //体温单是否显示"®" 0否 1是
                                    if (csjson.csz) {
                                        right.csqxContent.N03004200279 = csjson.csz;
                                    }
                                    break;    
                            }
                        }
                        console.log(right.csqxContent)
                } else {
                    malert("参数权限获取失败!"+json.c,'top','defeadted');
                }
            });

        },
    }
});
var SetUserTwd=new Vue({
    el:'.SetUserTwd',
    data:{
        wjzShow:false,
    },
	methods:{
		saveData:function(){
			batch.saveData();
		},
		closeModel:function(){
			console.log("-----------------------")
			SetUserTwd.wjzShow=false;
			right.getData();
		}
	}
})
//function prev() {
//    var aa=new Date($('#time1').val());
//    aa.setDate(aa.getDate()-7);
//    var pared=new Date(aa).getTime();
//    $('#time1').val(formatTime(pared,'date'))
//}
//function next() {
//    var aa=new Date($('#time1').val());
//    aa.setDate(aa.getDate()+7);
//    var nextd=new Date(aa).getTime();
//    $('#time1').val(formatTime(nextd,'date'))
//}
//时间改变事件
window.getTime = function (event) {
    var starttime = right.fDate(right.brxxContent.ryrq, 'date');
    var endtime = $(event).val();
    var start = new Date(starttime.replace("-", "/").replace("-", "/"));
    var end = new Date(endtime.replace("-", "/").replace("-", "/"));
    if (end < start) {
        malert("只能填写入院之后的数据！", "top", "success");
    }
};

yzclLeft.$watch('HszbrItem', function (newVal, oldVal) {
    console.log('scsj')
    if(newVal.zyh != right.brxxContent.zyh && this.index==8){
        right.brxxContent = JSON.parse(JSON.stringify(newVal));
        right.cxrq1 = right.$options.filters['formDate'](right.brxxContent.ryrq);
        right.oldbrxxContent = JSON.parse(JSON.stringify(newVal));
        right.getData();
        right.iscf();
    }
})
