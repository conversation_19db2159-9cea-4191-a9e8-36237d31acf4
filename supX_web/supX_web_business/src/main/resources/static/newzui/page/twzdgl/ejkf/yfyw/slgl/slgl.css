.tong-btn {
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
}
.font12 {
  font-size: 12px !important;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.slgl-by {
  width: 100%;
  height: 40px;
  display: flex;
  line-height: 40px;
  background: rgba(242, 166, 84, 0.08);
  justify-content: space-between;
}
.slgl-by i {
  width: 20%;
  display: block;
  text-align: center;
}
.slgl-by i:nth-child(5) {
  padding-right: 15px;
}
.slgl-by i em {
  color: #354052;
  padding-left: 5px;
  float: left;
}
.cffy-list {
  width: 100%;
  padding: 20px;
}
.cffy-list li {
  width: 100%;
  float: left;
  font-size: 14px;
  display: flex;
  line-height: 30px;
  justify-content: flex-start;
}
.cffy-list li i {
  float: left;
  display: flex;
  justify-content: flex-start;
  width: calc((100% - 30px)/4);
  text-align: left;
  color: #7f8fa4;
  margin-right: 10px;
}
.cffy-list li em {
  color: #354052;
}
.cffy-list li.zkmore {
  display: flex;
  justify-content: center;
  align-items: center;
}
.color-75 {
  color: #757c83;
  font-size: 12px;
}
.cfhj-top {
  width: 100%;
  height: 36px;
  background: #edf2f1;
  line-height: 36px;
}
.cfhj-top li {
  width: 100%;
  display: flex;
  justify-content: center;
}
.cfhj-top li i {
  width: calc((100% - 50px)/6);
  display: block;
  text-align: center;
}
.cfhj-top li i:nth-child(1) {
  width: 50px !important;
}
.cfhj-content {
  width: 100%;
  overflow: auto;
  max-height: 500px;
}
.cfhj-content li {
  width: 100%;
  display: flex;
  border: 1px solid #eee;
  border-top: none;
  justify-content: center;
}
.cfhj-content li i {
  width: calc((100% - 50px)/6);
  display: block;
  text-align: center;
}
.cfhj-content li i:nth-child(1) {
  width: 50px !important;
}
.cfhj-content li:hover {
  background: rgba(26, 188, 156, 0.08);
}
.all-check {
  position: absolute;
  right: 0;
  top: -10px;
  width: 20px;
  height: 20px;
}
.slgl-fl {
  position: absolute;
  left: 0;
  top: 20px;
  line-height: 32px;
  padding-left: 65px;
  color: #ff4735;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.icon-width:before {
  left: 34px !important;
}
input[type=checkbox].green + label:before {
  top: 41% !important;
}
.ksys-btn {
  bottom: 0px;
}
