<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>入库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="pdgl.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box  flex-one flex-dir-c flex-container">
    <div class="wrapper flex-one wh1000 flex-dir-c flex-container" id="wrapper" v-cloak>
        <div class="panel ">
            <div class="tong-top">
                <button class="tong-btn btn-parmary iconfont font-14 icon-iocn44 padd-r-10 paddr-r5" v-show="pdscShow" @click="add">生成</button>
                <button class="tong-btn btn-parmary iconfont font-14 padd-r-10 icon-iocn51 paddr-r5" v-show="deleteShow" @click="invalid">作废</button>
                <button class="tong-btn btn-parmary iconfont font-14 padd-r-10 icon-iocn45 paddr-r5" v-show="passShow" @click="confirm">审核</button>
                <button class="tong-btn btn-parmary iconfont font-14 padd-r-10 icon-iocn42 paddr-r5"  v-show="pdlrShow" @click="addpdlr">新增</button>
                <button class="tong-btn btn-parmary iconfont font-14 padd-r-10 icon-iocn42 paddr-r5"  v-show="pdlrShow" @click="savepdlr">添加</button>
                <button class="tong-btn btn-parmary-b  iconfont font-14 padd-r-10 icon-iocn56 paddr-r5 " @click="refresh" >刷新</button>
                <button class="tong-btn btn-parmary-b iconfont font-14 padd-r-10 icon-iocn4 paddr-r5" v-show="saveShow" @click="save" >保存</button>
                <button class="tong-btn btn-parmary-b  iconfont font-14 padd-r-10 icon-Artboard-1 paddr-r5" v-show="pdscShow" @click="autoGen">报表</button>
                <button class="tong-btn btn-parmary-b  iconfont font-14 padd-r-10 icon-Artboard-1 paddr-r5" v-show="pdlrShow" @click="autoAdd">自动生成</button>
                <button class="tong-btn btn-parmary-f2a" v-if="num==1" @click="printYppdd()">打印</button>
            </div>
            <div class="tong-search flex-container">
                <div class="top-form flex-container flex-align-c padd-r-20">
                    <label class="top-label font-14 padd-r-5">二级库房</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input @change-data="yfChange"
                                          :child="YFSelect" :index="'yfmc'" :index_val="'yfbm'" :val="yfSelected"
                                          :name="'yfSelected'" :search="true">
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="top-form flex-container flex-align-c padd-r-20" v-if="pdscShow">
                    <label class="top-label font-14 padd-r-5">盘点方式</label>
                    <div class="top-zinle">
                        <select-input @change-data="wayChange"
                                      :child="pdfs_tran" :index="popContent.pdWay" :val="popContent.pdWay"
                                      :name="'popContent.pdWay'">
                        </select-input>
                    </div>
                </div>

                <div class="top-form flex-container flex-align-c padd-r-20" v-if="ypzlShow">
                    <label class="top-label font-14 padd-r-5">种类名称</label>
                    <div class="top-zinle">
                        <select-input @change-data="resultChange"
                                      :child="YPZLSelect" :index="'ypzlmc'" :index_val="'ypzlbm'" :val="popContent.ypzl"
                                      :name="'popContent.ypzl'" :search="true">
                        </select-input>
                    </div>
                </div>

                <div class="top-form flex-container  flex-align-c padd-r-20" v-if="pzhShow">
                    <label class="top-label font-14 padd-r-5">凭证号</label>
                    <div class="top-zinle">
                        <select-input @change-data="resultPzhChange"
                                      :child="pzhList" :index="'pdpzh'" :index_val="'pdpzh'" :val="pzNum"
                                      :name="'pzNum'">
                        </select-input>
                    </div>
                </div>

                <div class="top-form flex-container  flex-align-c padd-r-20" v-if="ypmcShow">
                    <label class="top-label font-14 padd-r-5">材料名称</label>
                    <div class="top-zinle">
                        <input class="zui-input" :value="ypmcInput" @keydown="changeDown($event,'text')"
                               @input="change(false,$event.target.value)">
                        <search-table :message="searchCon" :selected="selSearch"
                                      :them="them" :them_tran="them_tran" :page="page"
                                      @click-one="checkedOneOut" @click-two="selectOne">
                        </search-table>
                    </div>
                </div>
                <div class="top-form flex-container  flex-align-c padd-r-20" v-if="ypjsShow">
                    <label class="top-label font-14 padd-r-5">材料检索</label>
                    <div class="top-zinle">
                        <input class="zui-input" placeholder="材料编码/名称" @input="ypjsData" v-model="ypjsValue">
                    </div>
                </div>
            </div>
        </div>
        <div class=" flex-container flex-align-c flex-jus-s padd-l-10">
            <tabs :num="num" :tab-child="[{text:'盘点生成'},{text:'未核盘点表'},{text:'盘点录入'},{text:'录入审核'},{text:'盘点完成'}]" @tab-active="tabBg"></tabs>
            <aside class="flex-container padd-r-20" v-show="ypjgshow">
                <p class="padd-r-20">材料进价总计:<span class="color-f2a654" v-text="json.jjzj.toFixed(2)"></span><span class="color-f2a654">元</span></p>
                <p>材料零价总计:<span class="color-f2a654" v-text="json.ljzj.toFixed(2)"></span><span class="color-f2a654">元</span></p>
            </aside>
        </div>
        <div key="a" class="zui-table-view flex-one flex-dir-c flex-container wh1000 padd-r-10 padd-l-10"  v-if="num==0" >
            <!--未核盘点列表-->
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>材料编码</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>材料批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>实存数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>二级库房单位	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>进价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>进价金额	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>生产日期</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>分装比例	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>供货单位</span></div></th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body padd-b-10 over-auto flex-one" data-no-change   @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in pdscJsonList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdscJsonList'],$event)" :tabindex="$index">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ypbm">000984</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">复方骨肽</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="item.ypgg">0.25g*12袋/盒</div>
                        </td>
                        <td><div class="zui-table-cell cell-l" v-text="item.xtph">PC04201803160001</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.kcsl">0</div></td>
                        <td><div class="zui-table-cell cell-s">0</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc">支</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypjj">29.5</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDec(item.ypjjje,2)">924.00</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yplj">1.155</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDec(item.ypljje,2)">924.00</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.scph">493160818</div></td>
                        <td><div class="zui-table-cell cell-l" v-text="fDate(item.scrq,'date')">2016-12-24</div></td>
                        <td><div class="zui-table-cell cell-l" v-text="fDate(item.yxqz,'date')">2018-11-30</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">支</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.fzbl">1</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.kw==undefined? '未定义':item.scsl">未定义</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.cdmc">默认产地</div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.ghdwmc">遵义市兴民医药有限公司</div></td>
                        <!--<p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                    </tr>
                    </tbody>
                </table>
            </div>
<!--            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>-->
        </div>

        <!--未核盘点列表-->
        <div v-if="num==1" key="f" class="flex-container zui-table-view flex-one ">
            <div class="flex-dir-c flex-container w30  padd-l-10"  >
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-l"><span>盘点单号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>盘点日期</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>制单人</span></div></th>
                        </tr>
                        </thead>
                    </table>

                </div>
                <div class="zui-table-body padd-b-10 flex-one" data-no-change  @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item,$index) in whpdList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','whpdList'],$event),whshowDetail($index,item)" :tabindex="$index">
                            <td>
                                <div class="zui-table-cell cell-l" v-text="item.pdpzh">PD04201807000001</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')">2018-07-05</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.zdr">00001</div>
                            </td>
                            <!--<p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class=" flex-dir-c flex-container w70 padd-l-10 padd-r-10"  >
                <h1 class="text-center h1title margin-b-10 margin-top-10">二级库房未核盘点表</h1>
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-s"><span>材料编码	</span></div></th>
                            <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>规格</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>材料批号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>实存数量	</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                        </tr>
                        </thead>
                    </table>

                </div>
                <div class="zui-table-body flex-one"   @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item,$index) in whpdmxList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','whpdmxList'],$event)" :tabindex="$index">
                            <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                            <td><div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">一次性胃管</div></td>
                            <td><div class="zui-table-cell cell-l" v-text="item.ypgg">0.25g*12袋/盒</div></td>
                            <td><div class="zui-table-cell cell-l" v-text="item.xtph">PC04201805280001</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.kcsl">50</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.scsl">0</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc">0</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.yplj">01.155</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.kcsl,2)">165.00</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.scph">0000000</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="fDate(item.scrq,'date')">2018-05-14</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">001118</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">2018-05-14</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.fzbl">1</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.kw">1</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.cdmc">1</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc">1</div></td>
                            <!--<p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </tr>
                        </tbody>
                    </table>
                </div>
<!--                <page style="position:relative" @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>-->
            </div>
        </div>

        <!--盘点录入-->
        <div key="b" class="flex-container zui-table-view flex-one flex-dir-c wh1000 padd-r-10 padd-l-10"  v-if="num==2" >
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>材料名称</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>材料批号</span></div></th>
                        <th><div class="zui-table-cell cell-s text-left"><span>库存数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>实存数量</span></div></th>
                        <th><div class="zui-table-cell cell-s text-left"><span>单位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-s text-left"><span>零价金额</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产日期	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库房单位	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>分装比例	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body flex-one"   @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in pdlrList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdlrList'],$event)" :tabindex="$index">
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.ypbm">01118</div></td>
                        <td class="cell-m"><div class="zui-table-cell text-over-2 cell-xl" v-text="item.ypmc">一次性输液器双管7#</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-l" v-text="item.ypgg">0.25g*12袋/盒</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-l" v-text="item.xtph">PC04201805280001</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s text-left" v-text="item.kcsl">50</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s"><input type="number" class="zui-input pdgl-height-30" v-model="item.scsl" @keydown="nextFocus($event)"/></div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s text-left" v-text="item.ghdwmc">50</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.yplj">31.92</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s text-left" v-text="fDec(item.yplj*item.kcsl,2)">165.00</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.scph">0000000</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="fDate(item.scrq,'date')">2018-05-14</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">2018-05-14</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.kfdwmc">0</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.fzbl">1</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.kw">0</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.cdmc">0</div></td>
                        <td class="cell-m"><div class="zui-table-cell cell-s" v-text="item.ghdwmc">0</div></td>
                        <td class="cell-s"><div class="zui-table-cell cell-s">
                            <div class="zui-table-cell cell-s icon-bj_parent">
                                <em class="width30"><i class="iconfont icon-iocn44 icon-font20" data-title="确认" @click="queren($index)"></i></em>
                                <em class="width30"><i class="icon-js" data-title="作废" @click="zuofei($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </div>
                        </div>

                        </td>
                        <!--<p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in pdlrList"  :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','pdlrList'],$event)">
                            <td  class="cell-s">
                                <div class="zui-table-cell cell-s icon-bj_parent">
                                    <!--<em class="width30"><i class="icon-bj" data-title="编辑" @click="scmx($index,pdlrList)"></i></em>-->
                                    <em class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
<!--            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>-->
            <!--end-->
        </div>

        <!--录入审核-->
        <div v-if="num==3" key="c" class="flex-container zui-table-view flex-one ">
            <div class="flex-dir-c flex-container w30  padd-l-10"  >
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-l"><span>盘点单号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>盘点日期</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>制单人</span></div></th>
                        </tr>
                        </thead>
                    </table>

                </div>
                <div class="zui-table-body padd-b-10 flex-one" data-no-change  @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item,$index) in pdlrShList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','pdlrShList'],$event),lrshShowDetail($index,item)" :tabindex="$index">
                            <td>
                                <div class="zui-table-cell cell-l" v-text="item.pdlrpzh">PD04201807000001</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')">2018-07-05</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.zdrmc">00001</div>
                            </td>
                            <!--<p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class=" flex-dir-c flex-container w70 padd-l-10 padd-r-10"  >
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                            <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>规格</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>材料批号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>实存数量	</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                        </tr>
                        </thead>
                    </table>

                </div>
                <div class="zui-table-body flex-one"   @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item,$index) in pdlrShmxList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','pdlrShmxList'],$event)" :tabindex="$index">
                            <td><div class="zui-table-cell cell-s" v-text="item.ypbm">一次性胃管</div></td>
                            <td><div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">0.25g*12袋/盒</div></td>
                            <td><div class="zui-table-cell cell-l" v-text="item.ypgg">PC04201805280001</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.xtph">50</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.kcsl">0</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.scsl">0</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc">01.155</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.yplj">165.00</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.kcsl,2)">0000000</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.scph">2018-05-14</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="fDate(item.scrq,'date')" >2018-05-14</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')" >2018-05-14</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">1</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.fzbl">1</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.kw">1</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.cdmc">1</div></td>
                            <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc">001118</div></td>
                            <!--<p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
<!--            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>-->
        </div>

        <!--盘点完成-->
        <div key="d" class="zui-table-view flex-one flex-dir-c flex-container wh1000 padd-r-10 padd-l-10"  v-if="num==4" >
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>材料名称</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>材料批号</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>库存数量</span></div></th>
                        <th><div class="zui-table-cell cell-s text-left"><span>实存数量	</span></div></th>
                        <th><div class="zui-table-cell cell-s text-left"><span>单位</span></div></th>
                        <th><div class="zui-table-cell cell-s text-left"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至	</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库房单位	</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>分装比例</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>库位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>供货单位</span></div></th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body padd-b-10 over-auto flex-one"   @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in pdwcPddList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdwcPddList'],$event)" :tabindex="$index">
                        <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                        <td><div class="zui-table-cell cell-xl text-over-2" v-text="item.ypmc">000984</div></td>
                        <td><div class="zui-table-cell cell-xl text-left" v-text="item.ypgg">复方骨肽</div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.xtph">0.25g*12袋/盒</div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.kcsl">PC04201803160001</div></td>
                        <td><div class="zui-table-cell cell-s text-left" v-text="item.scsl">支</div></td>
                        <td><div class="zui-table-cell cell-s text-left" v-text="item.yfdwmc">29.5</div></td>
                        <td><div class="zui-table-cell cell-s text-left" v-text="item.yplj">924.00</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.fzbl,2)">924.00</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.scph">493160818</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.scrq,'date')">2016-12-24</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">2018-11-30</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">支</div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.fzbl">1</div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.kw==undefined?'未定义':item.scsl">未定义</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.cdmc">默认产地</div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.ghdwmc">遵义市兴民医药有限公司</div></td>
                        <!--<p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                    </tr>
                    </tbody>
                </table>
            </div>
<!--            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>-->
        </div>
    </div>
    <!-- :class="{'ng-hide':index==0}" -->
    <div class="side-form  pop-width" id="brzcList" v-cloak :class="{'ng-hide':type}" role="form">
        <div class="fyxm-side-top flex-between">
            <span>新增库位</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list1 flex-start">
                <li>
                    <i>材料名称</i>
                    <input class="zui-input" :value="ypmcInput" @keydown="changeDown($event,'ypmc')"
                           @input="change(false,$event.target.value)">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </li>
                <li>
                    <i>材料规格</i>
                    <input type="text" class="label-input background-h zui-input " disabled="disabled" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>分装比例</i>
                    <input class="label-input background-h zui-input " disabled="disabled" v-model="popContent.fzbl" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>材料批号</i>
                    <input class="label-input background-h zui-input " disabled="disabled" v-model="popContent.xtph" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>二级库房单位</i>
                    <input class="label-input background-h zui-input " disabled="disabled" v-model="popContent.yfdw" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>生产日期</i>
                    <span class="iconfont icon-icon61"></span>
                    <input id="timeVal" class="label-input zui-input  background-h" disabled="disabled" v-model="popContent.scrq" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>材料零价</i>
                    <input class="label-input zui-input  background-h" disabled="disabled" v-model="popContent.yplj" @keydown="nextFocus($event)"/>
                </li>
                <div class="flex-container flex-jus-sp">
                    <div class="wh49">
                        <i>帐存数量</i>
                        <input class="label-input zui-input  background-h" disabled="disabled" v-model="popContent.kcsl" @keydown="nextFocus($event)"/>
                    </div>
                    <div class="wh49">
                        <i>实存数量</i>
                        <input class="label-input zui-input " id="scsl" v-model="popContent.scsl"  @keydown.13="addOne"/>
                    </div>
                </div>
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="addOne">保存</button>
        </div>
    </div>

    <div class="side-form   pop-width" id="pddsjxz" v-cloak :class="{'ng-hide':type}" role="form">
        <div class="fyxm-side-top flex-between">
            <span>新增</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list1 flex-start">
                <li>
                    <i>材料名称</i>
                    <input class="zui-input" :value="ypmcInput" @keydown="changeDown($event,'ypmc'),nextFocus($event)"
                           @input="change(false,$event.target.value)">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </li>
                <li>
                    <i>材料编码</i>
                    <input type="text" class="label-input background-h zui-input _addData" disabled="disabled" v-model="popContent.ypbm" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>材料规格</i>
                    <input type="text" class="label-input background-h zui-input _addData" disabled="disabled" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>分装比例</i>
                    <input class="label-input background-h zui-input _addData" disabled="disabled" v-model="popContent.fzbl" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>生产批号</i>
                    <input class="label-input zui-input _addData" v-model="popContent.scph" @keydown="nextFocus($event)" data-notEmpty="true"/>
                </li>
                <li>
                    <i>产品标准号</i>
                    <input class="label-input zui-input _addData" v-model="popContent.cpbzh" @keydown="nextFocus($event)" data-notEmpty="true"/>
                </li>
                <li>
                    <i>批准文号</i>
                    <input class="label-input zui-input _addData" v-model="popContent.pzwh" @keydown="nextFocus($event)" data-notEmpty="true"/>
                </li>
                <li>
                    <i>生产日期</i>
                    <span class="iconfont icon-icon61"></span>
                    <input v-model="popContent.scrq" class="label-input zui-input _addData times1"  @blur="dateForVal($event, 'popContent.scrq')"
                           @keydown="changeDate($event,'_scrq')" readonly>
                </li>
                <li>
                    <i>有效期至</i>
                    <span class="iconfont icon-icon61"></span>
                    <input v-model="popContent.yxqz" class="label-input zui-input _addData times2"
                           @keydown="changeDate($event,'_yxqz')" readonly>
                </li>
                <li>
                    <i>供货单位</i>
                    <select-input @change-data="resultChange" :not_empty="true" :child="ghdwList" :index="'dwmc'"
                                  :index_val="'dwbm'" :val="popContent.ghdw" :search="true" :name="'popContent.ghdw'">
                    </select-input>
                </li>
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="addNewYp">保存</button>
        </div>
    </div>
</div>
<script src="pdgl.js"></script>

</body>

</html>
