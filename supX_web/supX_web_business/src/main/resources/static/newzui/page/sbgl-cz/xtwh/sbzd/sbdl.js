var wzdl = new Vue({
	el: '#wzdl',
	mixins: [dic_transform, tableBase],
	data: {
		jsonList: [],
		dg: {
			page: 1,
			rows: 500,
			sort: "",
			order: "asc",
		},
	},
	methods: {
		getData: function() {
			//准备参数
			var json = {
				'tybz': 0,
			}
			$.getJSON("/actionDispatcher.do?reqUrl=WzkfXtwhWzdl&types=query&json=" +
				JSON.stringify(json) + "&dg=" + JSON.stringify(this.dg),
				function(data) {
					if(data.a == 0) {
						wzdl.totlePage = Math.ceil(data.d.total / wzdl.param.rows);
						wzdl.jsonList = data.d.list;
					} else {

					}

				});
		},
		addData: function() {
			//设置操作类型
			wzdlPop.op = 'save';
			wzdlPop.popContent = {};
			wzdlPop.isShow = true;
		},
		edit: function(num) {
			wzdlPop.op = 'update';
			if(num == null) {
				for(var i = 0; i < this.isChecked.length; i++) {
					if(this.isChecked[i] == true) {
						num = i;
						break;
					}
				}
				if(num == null) {
					malert("请选中你要修改的数据");
					return false;
				}
			}
			wzdlPop.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
			wzdlPop.isShow = true;
		},
		remove: function() {
			var wzdlList = [];
			for(var i = 0; i < this.isChecked.length; i++) {
				if(this.isChecked[i] == true) {
					wzdlList.push(this.jsonList[i]);
				}
			}
			if(wzdlList.length == 0) {
				malert("请选中您要删除的数据");
				return false;
			}
			if(!confirm("请确认是否删除")) {
				return false;
			}

			var json = {
				'list': wzdlList
			}
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhWzdl&types=delete', JSON.stringify(json)).then(function(data) {
				if(data.body.a == 0) {
					malert("数据删除成功")
				} else {
					malert("数据删除失败");
				}
				wzdl.getData();
			}, function(error) {
				console.log(error);
			});
		},
	}
});
wzdl.getData();

//弹出层
var wzdlPop = new Vue({
	el: '#wzdlPop',
	mixins: [dic_transform, baseFunc],
	data: {
		isShow: false,
		popContent: {},
		isKeyDown: null,
		title: '物资大类',
		op: 'save'
	},
	methods: {
		saveData: function() {
			//非空判断
			if(this.popContent['tybz'] == null || this.popContent['tybz'] == undefined) {
				malert("停用标志输入不正确");
				return;
			};
			
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhWzdl&types=' + this.op, JSON.stringify(this.popContent)).then(function(data) {
				if(data.body.a == 0) {
					wzdl.getData();
					wzdlPop.isShow = false;
					wzdlPop.isAdd = false;
					malert("数据保存成功")
				} else {
					malert("数据提交失败");
				}
			}, function(error) {
				console.log(error);
			});
		}
	}
});

//验证是否为空
$('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
	if($(this).val() == '' || $(this).val() == null) {
		$(this).addClass("emptyError");
	} else {
		$(this).removeClass("emptyError");
	}
});

//为table循环添加拖拉的div
var drawWidthNum = $(".wzdl tr").eq(0).find("th").length;
for(var i = 0; i < drawWidthNum; i++) {
	if(i >= 2) {
		$(".wzdl th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
	}
}