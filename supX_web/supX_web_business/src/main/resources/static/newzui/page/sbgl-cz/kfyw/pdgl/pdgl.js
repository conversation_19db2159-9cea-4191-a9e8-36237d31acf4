var wrapper = new Vue({
    el:'#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
        totlePage:0,
        jsonList:[],
        popContent:{},
        zhuangtai:{
            '0':'待审核',
            '1':'已审核',
            '2':'已作废'
        }
    },
    mounted:function(){
        var myDate=new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        laydate.render({
            elem: '#timeVal'
            , trigger: 'click',
            type:'datetime',
            value:this.param.beginrq
            , theme: '#1ab394'
            , done: function (value, data) {
                wrapper.param.beginrq=value
                wrapper.goToPage(1)
            }
        });
        laydate.render({
            elem: '#timeVal1'
            , trigger: 'click',
            type:'datetime',
            value:this.param.endrq
            , theme: '#1ab394'
            , done: function (value, data) {
                wrapper.param.endrq=value
                wrapper.goToPage(1)
            }
        });
        this.getData()
    },
    methods: {
        getData:function(){
            this.param.wzkf='01';
            $.getJSON('/actionDispatcher.do?reqUrl=New1WzkfKfywPdb&types=queryDj&parm=' + JSON.stringify(this.param),
                function(data) {
                    if(data.a == "0") {
                        wrapper.jsonList = data.d.list;
                        wrapper.totlePage=Math.ceil(data.d.total / wrapper.param.rows)
                    } else {
                        malert("药库获取失败", 'top', 'defeadted');
                    }
                });
        },
        Verify:function (index,type) {
            if(index != undefined){
                var scpd={
                    scpd:this.jsonList[index],
                    sh:(this.jsonList[index].qrzfbz=='0'),
                    dy:(this.jsonList[index].qrzfbz !='0'),
                    pdShow:type,
                }
                sessionStorage.setItem('scpd',JSON.stringify(scpd))
                this.topNewPage('盘点开单','page/wzkf/kfyw/pdgl/scpd.html');
            }else{
                var scpd={
                    sh:false,
                    pdShow:type,
                }
                sessionStorage.setItem('scpd',JSON.stringify(scpd))
                this.topNewPage('盘点开单','page/wzkf/kfyw/pdgl/scpd.html');
            }
        },
        //拒绝
        Refuse: function (index) {
            var obj = this.jsonList[index];
            if (common.openConfirm("<div>确定作废物资盘点单号-" + obj.pdpzh + "-吗？<br/> <div class=\"flex-container flex-align-c\"><span class=\"ft-14 whiteSpace padd-r-5\">作废原因</span>\n" +
                "<textarea rows=\"3\" cols=\"100\" id='zfyy' class=\" padd-t-5 padd-b-5 padd-l-5 padd-r-5 wh100MAx\"></textarea></div></div>", function () {
                obj.zfyy=$('#zfyy').val()
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywPdb&types=invalid', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        // wrapper.getKfData();
                        malert("作废成功！", 'top', 'success');
                        wrapper.getData();
                        // malert("审核成功！")
                    } else {
                        malert("作废失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }
        },

    }
});
function getData() {
    wrapper.getData()
}
