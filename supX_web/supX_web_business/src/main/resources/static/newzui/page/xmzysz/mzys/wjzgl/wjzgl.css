.icon-transform {
    transform: rotate(-31deg);
}
.crisis-danger {
    background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);
    border: 1px solid #f46161;
    border-radius: 38px;
    width: 16px;
    height: 16px;
    font-size: 12px;
    color: #fff;
    display: block;
    float: left;
    margin: 6px 4px 0 0;
}
small {
    transform: scale(0.75);
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 11px;
}
.icon-position {
    top: 35px;
}
.text-indent-25 {
    text-indent: 25px !important;
}
.wjz-width {
    width: 534px;
    height: 300px;
    position: absolute;
    top: calc((100vh - 300px) / 2);
    left: calc((100vw - 534px) / 2);
    z-index: 9999;
    background: #fff;
    box-shadow: 0 0 18px 0 rgba(0,0,0,0.50);
}
.wjz-top {
    width: 100%;
    height: 46px;
    background: #1abc9c;
    color: #fff;
    font-size: 16px;
    padding: 0 15px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.wjz-content {
    padding: 19px 15px 15px 30px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.wjz-jsr {
    width: auto;
    padding-right: 5px;
    min-width: 47px;
}
.wjz-radio {
    padding: 0 15px 15px 80px;
}
.c_radio {
    float: left;
    color: #354052;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-right: 20px;
}
.c_radio .lb_text {
    padding-left: 5px;
}
.c_radio > input {
    display: none;
}
.c_radio label {
    vertical-align: middle;
    line-height: 16px;
    display: inline-block;
    height: 16px;
    font-size: 14px;
}
.c_radio > input:checked + label::before,
.c_radio > input:checked + label::after {
    display: block;
}
.c_radio > input + label {
    position: relative;
    cursor: pointer;
    vertical-align: middle;
}
.c_radio > input + label::before {
    position: relative;
    top: 0;
    left: 0;
    display: inline-block;
    width: 16px;
    height: 16px;
    content: '';
    background: url("/newzui/css/images/dx.png") center no-repeat;
    background-size: 16px 16px;
}
.c_radio > input + label::after {
    content: "";
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 16px;
    height: 16px;
    background: url("/newzui/css/images/dx_h.png") center no-repeat;
    background-size: 16px 16px;
}
.wjz-bz {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 15px 15px 0px 33px;
    box-sizing: border-box;
}
.wjz-textarea {
    width: 100%;
    border: 1px solid #d7dbe1;
    padding: 10px 12px;
    box-sizing: border-box;
    height: 68px;
    border-radius: 5px;
    color: #354052;
}
.wjz-btn {
    width: 100%;
    padding: 30px 5px 0 0;
    display: flex;
    justify-content: flex-end;
}
.wjz-hz-width {
    width: 360px;
    height: 220px;
    position: absolute;
    top: calc((100vh - 220px) / 2);
    left: calc((100vw - 360px) / 2);
    z-index: 9999;
    background: #fff;
    box-shadow: 0 0 18px 0px rgba(0,0,0,0.5);
}
.wjz-hz-content {
    padding: 19px 15px 15px 15px;
    position: relative;
}
.wjz-hz-content .wjz-jsr {
    min-width: 61px;
}
.wjz-hz-content .icon-positions {
    top: 28px;
    left: 80px;
}
.wjz-btn1 {
    padding-top: 54px;
}
.zui-collapse{
    border-collapse: collapse;
}
.zui-collapse tr.table-active{
    background:rgba(255,69,50,0.08);
}
.icon-iocn12:before {
    content: "\e90b";
    color: #5f5f5f;
}
.icon-font20:before {
    font-size: 20px;
}
.icon-iocn32:before {
    content: "\e91f";
    color: #5f5f5f;
}
