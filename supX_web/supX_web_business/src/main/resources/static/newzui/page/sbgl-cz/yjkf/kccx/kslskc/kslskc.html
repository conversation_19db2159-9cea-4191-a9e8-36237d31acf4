<html>

	<head>
		<title>历史库存查询</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<script src="/pub/top.js"></script>
		<link href="kslskc.css" rel="stylesheet" type="text/css" />
	</head>

	<body>

		<div id="crcx">
			<div class="toolMenu" style="display: block;">
<!--				<input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" onchange="getTime(this, 'star')" />至
-->				<input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd' })" onchange="getTime(this, 'end')" />
				<select class="YFSelect" v-model="yfkf" @change="yfkfChange">
					<option :value="0">-全部科室-</option>
					<option v-for="item in yfkfList" :value="item.ksbm" v-text="item.ksmc"></option>
				</select>
				<button @click="getData"><span class="fa fa-refresh"></span>查询</button>
				<button @click="getData"><span class="fa fa-check-square-o"></span>报表</button>
				<input @change="getData" v-model="queryType" class="tableNo" type="checkbox" /><span class="fa fa-refresh">按设备汇总</span>
			</div>
			<div class="tableDiv">
				<table class="patientTable" cellspacing="0" cellpadding="0">
					<thead style="position: absolute;">
						<tr>
							<th class="tableNo"></th>
							<th><input type="checkbox" v-model="isCheckAll" @click="checkAll"></th>
							<th>日期</th>
							<th>设备编码</th>
							<th>设备名称</th>
							<th>设备规格</th>
							<th>批次停用</th>
							<th>库存数量</th>
							<th>设备进价</th>
							<th>设备零价</th>
							<th>零价金额</th>
							<th>设备批号</th>
							<th>有效期至</th>
							<!--<th>设备产地</th>
							<th>库房单位</th>
							<th>药房单位</th>-->
							<th>分装比例</th>
						</tr>
					</thead>
					<tr>
						<th v-for="item in 14"></th>
					</tr>
					<tr v-for="(item, $index) in jsonList" @click="checkOne($index,item)" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
						<th class="tableNo" v-text="$index+1"></th>
						<th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)" />
						</th>
						<td v-text="fDate(item.rq,'date')"></td>
						<td v-text="item.wzbm"></td>
						<td v-text="item.wzmc"></td>
						<td v-text="item.wzgg"></td>
						<td v-text="stopSign[item.pcty]"></td>
						<td v-text="item.kcsl"></td>
						<td v-text="fDec(item.jj,2)"></td>
						<td v-text="fDec(item.dj,2)"></td>
						<td v-text="fDec(item.dj*item.kcsl,2)"></td>
						<td v-text="item.scph"></td>
						<td v-text="fDate(item.yxqz,'date')"></td>
						<!--<td v-text="item.cd"></td>
						<td v-text="item.kfdwmc"></td>
						<td v-text="item.lydwmc"></td>-->
						<td v-text="item.fzbl"></td>
					</tr>
				</table>
			</div>

			<div class="pageDiv">
				<div class="page">
					<div @click="goPage(page, 'prev', 'getData')" class="fa fa-angle-left num"></div>
					<div class="num" :class="{currentPage: param.page == 1}" @click="goPage(1, null, null)">1</div>
					<div v-show="prevMore">...</div>
					<div class="num" v-for="(item, $index) in totlePage" v-text="item" :class="{currentPage: param.page == $index + 1}" @click="goPage($index + 1, null, null)" v-show="showPageList.indexOf(item) != -1 && item != 1 && item != totlePage"></div>
					<div v-show="nextMore">...</div>
					<div class="num" :class="{currentPage: param.page == totlePage}" @click="goPage(totlePage, null, null)" v-text="totlePage"></div>
					<div @click="goPage(page, 'next', 'getData')" class="fa fa-angle-right num next"></div>
					<div>
						第<input type="number" v-model="page" />页
						<div class="divBtu" @click="goPage(page, null, null)">跳转</div>
					</div>
					<div>
						共<span v-text="totlePage"></span>页
						<select v-model="param.rows" @change="getData()">
							<option value="10">10</option>
							<option value="20">20</option>
							<option value="30">30</option>
						</select>条/页
					</div>
				</div>
			</div>
		</div>

		<!--弹出框-->
		<div id="mxtzPop">
			<transition name="pop-fade">
				<div class="pop" v-show="isShow" style="display: none">
					<div class="popCenter">
						<div id="mxtzPopCon" class="popInfo" style="height: 200px">
							<div class="popTitle dragCSS" v-text="title" onmousedown="drag(event,'mxtzPopCon')" onmouseup="stopDrag()"></div>
							<table class="popTable" cellspacing="0" cellpadding="0">
								<tr>
									<th>有效期:</th>
									<td><input onclick="WdatePicker({ minDate:'%y-%M-%d' })" @blur="dateForVal($event, 'popContent.xxq')" data-notEmpty="false" v-model="popContent.xxq" @keydown="nextFocus($event)" />
									</td>
									<th>批次停用:</th>
									<td>
										<select-input @change-data="resultChange" :not_empty="true" :child="stopSign" :index="popContent.xpcty" :val="popContent.xpcty" :name="'popContent.xpcty'">
										</select-input>
									</td>
								</tr>
							</table>
							<div class="popDoBtu popBtu">
								<button @click="saveData" class=""><span class="fa fa-save"></span>保存</button>
								<button @click="isShow = false" class="cancel"><span class="fa fa-close"></span>取消</button>
							</div>
						</div>
					</div>
				</div>
			</transition>
		</div>
	</body>
	<script type="text/javascript" src="kslskc.js"></script>

</html>