
    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data:{
            slType: '',
            param: {
                'page': 1,
                'rows': 100,
                'sort': 'ckd.ckdh,ypmc',
                'order': 'desc',
                'shzfbz': 1,
                'kfbm': '',
                'ckfs':'03',//03-出库
                'beginrq': null,
                'endrq': null,
                'parm':''
            },
            jsonList: [],
            yfkfList: [],
        },
        mounted:function(){
            this.getKf();
        },
        updated:function () {
            changeWin()
        },
        methods:{
            getKf: function() {
                //库房列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.yfkfList = data.d.list;
                            Vue.set(wrapper.param,'kfbm',data.d.list[0].kfbm);
                            //获取列表
                            wrapper.getData();
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });

            },

            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.param, 'kfbm', val[0]);
                Vue.set(this.param, 'kfmc', val[4]);
                this.getData();
            },
            //获取数据
            getData: function() {
                this.jsonList = [];
                $.getJSON("/actionDispatcher.do?reqUrl=YkglKfcxCrcx&types=mxtz&parm=" + JSON.stringify(this.param), function(json) {
                    if(json.a == "0") {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.jsonList = json.d.list;
                        console.log(json);
                    }
                });

            }
        },
    });

    laydate.render({
        elem: '.todate'
        , trigger: 'click'
        , theme: '#1ab394',
        range: true
        , done: function (value, data) {
            // wrapper.param.time = value
            wrapper.param.beginrq = value.slice(0,10);
            wrapper.param.endrq =value.slice(13,23);
            wrapper.getData();
        }
    });
