
var pram = {};
var ylbm = "N040030020012004";
var ksbm='';
var objbydh='';
(function () {
    //科目
    var tableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            jsonList: [],
            yfkfList: [],
            type: 1,
            fylx: 0, //服药方法类型
            useWayShow: true,
            yfkf: 0, //药房库房信息
            param: {
                page: 1,
                rows: 10,
                //'sort': 'rkd.rkdh',
                //'order': 'desc',
                //'shzfbz': 1,
                //kfbm: '',
                //'rkfs':'01',//01-出库
                beginrq: null,
                endrq: null,
                parm:'',
                ylbm : ylbm,
                ryksbm : ''
            },
            zhuangtai:{
                "0":'待发药',
                "1":'已发药',
                "2":'部分发药',
                "3":'已退药'
            }


        },

        methods: {
            //获取数据
            getData: function() {
                common.openloading('.zui-table-view');
                if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                    this.param.parm = $("#jsvalue").val();
                } else {
                    this.param.parm = '';
                }
               Vue.set(tableInfo.param,'ryksbm',toolBar.barContent.ryks);
                //console.log("pram:"+JSON.stringify(pram));
                //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
                $.getJSON("/actionDispatcher.do?reqUrl=New1YfbYfywBqty&types=tydcx&parm=" + JSON.stringify(tableInfo.param), function(json) {
                    if(json.a == 0) {
                        //console.log("json:"+JSON.stringify(json.d.list));
                        //tableInfo.total = json.d.total;
                        tableInfo.jsonList = json.d.list;
                        console.log(tableInfo.jsonList);
                    } else {
                        malert(json.c,'top','defeadted')
                    }
                });
                common.closeLoading()
            },
            fayao:function (index) {
                fydContext.title='药品明细';
                fydContext.open();
                var obj = tableInfo.jsonList[index].tymx;
                var list = [];
                var temp = {};
                var res = [];
                if(obj != null) {
                	for(var i = 0 ; i < obj.length ; i ++){
                		if(list.length==0){
                			list.push(obj[i]);
                			temp[obj[i].zyh] = list; 
                		}else{
                			for(var key in temp){
                				if(key == obj[i].zyh){
                					temp[key].push(obj[i]);
                					break;
                				}
                			}
                			if(!temp[obj[i].zyh]){
                				list = [];
                				list.push(obj[i]);
                				temp[obj[i].zyh] = list;
                			}
                		}
                	}
                	for(var key in temp){
                		res.push(temp[key]);
                		fydContext.times.push(temp[key][0]);
                	}
                }
                console.log(res);
                fydContext.mxList = res;
                /*var param = {
                    'bydh':bydh,
                    'ksbm':toolBar.barContent.ryks,
                };
                //获取打印数据
                $.getJSON("/actionDispatcher.do?reqUrl=YfbYfywBqby&types=print&parm=" + JSON.stringify(param),
                    function(json) {
                        if(json.a == 0) {
                            fydContext.mxList = json.d.djmx;
                            fydContext.times = json.d.dj;

                        } else {
                            alert("摆药单查询失败：" + json.c)
                        }
                    });*/
            },
            //单击
            checkOneMx: function(index,item) {
                this.isCheckMx = [];
                this.isCheckMx[index] = true;

                objbydh=item.bydh;
                console.log(objbydh);

            }
        },


    });
    
    var toolBar=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
        data:{
            jsonList: [],
            yfkfList: [],
            
            fylx: 0, //服药方法类型
            num:0,
            barContent: {
                "dateBegin": getTodayDateBegin(),
                "dateEnd": getTodayDateEnd()
            },
            yfkf: 0, //药房库房信息
            param: {
                'page': 1,
                'rows': 10,
                'sort': 'ckd.ckdh,ypmc',
                'order': 'desc',
                'shzfbz': 1,
                'kfbm': '',
                'ckfs':'01',//01-出库
                'beginrq': null,
                'endrq': null,
                'parm':''
            },
            
            yfList:[],
            zyksList:[],
        },
        mounted: function () {
            var myDate=new Date();
            tableInfo.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date');
            tableInfo.param.endrq = this.fDate(new Date(), 'date');
        	laydate.render({
                elem: '#timeVal', 
                eventElem: '.zui-date', 
                value: tableInfo.param.beginrq,
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                	tableInfo.param.beginrq = value;
                }
            });
        	laydate.render({
                elem: '#timeVal1',
                eventElem: '.zui-date',
                value: tableInfo.param.endrq,
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                	tableInfo.param.endrq = value;
                    tableInfo.getData();
                }
            });
        },
        methods:{
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    tableInfo.getData();
                }

            },
            tabBg: function(index) {
                this.num=index;
                //这里初始化请求发药单需要的参数
                switch (index){
                    case 0:
                        // alert('0');
                        break;
                    case 1:
                        // alert('1');
                         break;

                }

            },
            FaYaoEdit:function (obj) {
                obj=objbydh;
                if(num == null) {
                    for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            num = i;

                            tableInfo.fayao(obj);
                            break;
                        }
                    }
                    if(num == null) {
                        malert("请选中当前数据",'top','defeadted');
                        return false;
                    }
                }
            },
            //刷新
            sx:function () {
                tableInfo.getData();
            },
            //获取药房
            getYfbm: function() {
                pram = {
                    "ylbm": ylbm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(pram), function(json) {
                    if(json.a == 0) {
                        toolBar.yfList = json.d.list;
                        // pop.jsonList = json.d.list;

                        if(json.d.list.length > 0) {
                            Vue.set(toolBar.barContent,'yfbm',json.d.list[0].yfbm);
                            Vue.set(toolBar.barContent,'yfmc',json.d.list[0].yfmc);
                            toolBar.GetZyksData();
                        }
                    } else {
                        malert("药房编码获取失败：" + json.c,'top','defeadted');
                    }
                });
            },
            //组件选择下拉框之后的回调
            /*resultRydjChange: function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "yfbm":
                        Vue.set(this.barContent, 'yfbm', val[0]);
                        Vue.set(this.barContent, 'kwmc', val[4]);
                        tableCfInfo.getData();
                        // wap.getCsqx();
                        break;
                    default:
                        break;
                }
            },*/
            //页面加载时自动获取住院科室Dddw数据
            GetZyksData: function () {
                var bean = {"zyks": "1"};
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                    if (json.a == 0) {
                        toolBar.zyksList = json.d.list;
                        Vue.set(toolBar.barContent,'ryks',json.d.list[0].ksbm);
                        console.log(toolBar.zyksList);
                    } else {
                        malert(json.c+ "住院科室列表查询失败",'top','defeadted');

                    }
                });
            },
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "ryks"    :
                        //先获取住院医生的值
                        Vue.set(this.barContent, 'ryks', val[0]);
                        Vue.set(this.barContent, 'ryksmc', val[4]);
                        tableInfo.getData();
                        break;
                    case "yfbm":
                        Vue.set(this.barContent, 'yfbm', val[0]);
                        Vue.set(this.barContent, 'kwmc', val[4]);
                        tableInfo.getData();
                        // wap.getCsqx();
                        break;
                    default:
                        break;
                }
            },
            //申领科室选择
            Wf_KsChange: function() {
                var obj = event.currentTarget;
                var selected = $(obj).find("option:selected");
                toolbar.lyksmc = selected.text();
            }

        }
    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

 
    //发药单明细信息
    var fydContext = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            isMx: true,
            title:'',
            jsonList: [],
            mxList:[],
            times:[],
            barContent:{},
            fylxShow:true,
            num:0,
            fylist: {
                "bt": "医嘱明细发药单"
            },
            isMxChecked:[],
            AllList:{
                '0':'全部',
                '1':'口服',
                '2':'输液',
                '3':'肌注',
                '4':'其他/口服',
                '5':'输液/肌注',
                '6':'输液/肌注/其他',
            },
            tyList : [] 
        },
        methods: {
            closes:function () {
                $(".side-form").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');
                fydContext.isMxChecked = [];
                fydContext.isCheckAll = false;
            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg');
                $(".side-form").removeClass('ng-hide');
            },
            tabBg:function (index) {
                this.num=index;
                switch (index){
                    case 0:

                        break;
                    case 1:
                        break;
                    default:
                        break;
                }
            },
            //组件选择下拉框之后的回调
            resultRydjChanges: function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "fylx":
                        if(val[0]==0){
                          this.fylxShow=true;
                        }else{
                            this.fylxShow=false;
                        }
                        Vue.set(this.barContent, 'fylx', val[0]);
                        Vue.set(this.barContent, 'yfxzmc', val[4]);
                        // wap.getCsqx();
                        break;
                    default:
                        break;
                }
            },
            
            mxCheckBox: function (index,type) {
            	var obj = this.mxList[index];
            	if(type == -1){
            		//全选
            		console.log(this.isCheckAll);
            		this.isCheckAll = this.isCheckAll == true ? false : true;
            		if(this.isCheckAll == true){
            			for(var i = 0 ; i < obj.length ; i ++){
                			Vue.set(this.isMxChecked, i , true);
                			var temp = {};
                			Vue.set(temp,'tysqid',this.mxList[index][i].tysqid);
                			fydContext.tyList.push(temp);
                		}
            		}else{
            			var num= 0 ;
            			for(var i = 0 ; i < fydContext.tyList.length ; i++){
            				Vue.set(this.isMxChecked, (i+num) , false);
            				var id = fydContext.tyList[i].tysqid;
            				for(var j = 0 ; j < obj.length ; j ++){
            					if(id == obj[j].tysqid){
            						fydContext.tyList.splice(i,1);
            						i = i -1;
            						num ++;
            					}
            				}
            			}
            			
            		}
            	}else{
            		if(this.isMxChecked[type] == true){
            			Vue.set(this.isMxChecked, type , false);
            			this.isCheckAll = false;
            			for(var i = 0 ; i < fydContext.tyList.length ; i++){
            				if(fydContext.tyList[i].tysqid == this.mxList[index][type].tysqid){
            					fydContext.tyList.splice(i,1);
            					return;
            				}
            			}
            		}else{
            			Vue.set(this.isMxChecked, type , true);
            			fydContext.tyList.push(this.mxList[index][type]);
            		}
            	}
            },
            
            back:function(){
            	if(fydContext.tyList.length  == 0 ){
            		malert("请选择退药的项目！",'top','defeadted');
            		return;
            	}
            	var json = {"list": fydContext.tyList};
            	this.$http.post('/actionDispatcher.do?reqUrl=YfbYfywBqty&types=bqtysh&yfbm='+toolBar.barContent.yfbm,JSON.stringify(json))
                .then(function (data) {
                	console.log(data.body);
                    if (data.body.a == 0) {
                        malert("病区退药审核成功!");
                        tableInfo.getData();
                        fydContext.closes();
                        fydContext.isMxChecked = [];
                        fydContext.isCheckAll = false;
                    } else {
                        malert(data.body.c);
                    }
            	});
            }
        }
    });
    //tableInfo.getData();
    toolBar.getYfbm();
})();
window.getTime = function(event, type) {
    if(type == 'star') {
        toolBar.param.beginrq = $(event).val().slice(0,10);
    } else if(type == 'end') {
        toolBar.param.endrq = $(event).val().slice(12,23);
    }
};




