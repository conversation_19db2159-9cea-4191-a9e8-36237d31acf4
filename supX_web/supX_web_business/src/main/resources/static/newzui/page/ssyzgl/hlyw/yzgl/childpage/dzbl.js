var dzbl=new Vue({
    el:'#dzbl',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data:{
    	csContent:{},
        Brxx_List:{},
    },
    created(){
        this.Brxx_List=JSON.parse(sessionStorage.getItem("HszbrItem"));

    },
    methods:{
    	xbl:function(){
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + this.Brxx_List.brid + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！','top','success')
                } else {
                    malert('信息上传失败失败：'+data.body.c,'top','defeadted')
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=" + this.Brxx_List.zyh + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！','top','success')
                } else {
                    malert('信息上传失败失败：'+data.body.c,'top','defeadted')
                }
            });
            var sxdz = "";
            // var user = "";
            var password = "";
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                	dzbl.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                    sxdz = dzbl.csContent.blSxdz;
                    // user = userId;
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                        if (json.a == "0") {
                        	password=json.d.password;
                        }
                    });
                    if (!sxdz) {
                        malert("书写地址为空，打开病历失败！",'top','defeadted');
                        return
                    }
                    if (!userId) {
                        malert("用户名为空，打开病历失败！！",'top','defeadted');
                        return
                    }
                    if (!password) {
                        malert("用户密码为空，打开病历失败！",'top','defeadted');
                        return
                    }
                    if (!dzbl.Brxx_List.zyh) {
                        malert("请先选择病人后再书写病历！",'top','defeadted');
                        return
                    }
                    var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=" + dzbl.Brxx_List.zyh + ",userid=" + userId + ",password=" + password + ",lyzyhmz=0,blhhl=1";
                    window.open(url);
    	},
    },
});
yzclLeft.$watch('HszbrItem',function (val) {
    console.log('dzbl')
    if(JSON.stringify(val)!='{}'){
        dzbl.Brxx_List=val
    }
})