<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室退药</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ksty.css" rel="stylesheet"/>
</head>
<style>
    .table-hovers-filexd-l{
        border-right: none !important;
    }
    .table-hovers-filexd-r{
        border-left: none!important;
    }
    .table-hovers-filexd-r-child{
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">

<div class="wrapper" id="jyxm_icon">
    <div class="panel box-fixed">
        <div class="tong-top">
            <!-- <button class="tong-btn btn-parmary  icon-xz1 paddr-r5" @click="FaYaoEdit(num)">退药</button> -->
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="sx">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                    <div class="zui-input-inline wh122 margin-l-7">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfList" :index="'yfmc'" :index_val="'yfbm'" :val="barContent.yfbm"
                                      :name="'barContent.yfbm'" :search="true" :index_mc="'yfmc'" :ksbm="barContent.ksbm" :cflx="barContent.cflx">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">科室</label>
                    <div class="zui-input-inline wh122 margin-f-l20">
                        <select-input @change-data="resultRydjChange"
                                      :child="zyksList" :index="'ksmc'" :index_val="'ksbm'" :val="barContent.ryks"
                                      :name="'barContent.ryks'" :search="true" :index_mc="'ryksmc'" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline flex-container flex-align-c  margin-f-l5">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh120 text-indent20" placeholder="请选择申请开始日期" id="timeVal"  @keydown="searchHc"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh120 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view "  z-height="full" style="margin-top: 108px; padding: 0 10px;border: none">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                 :type="'all'" :val="isCheckAll">
                    </input-checkbox></span></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>退药申请单号</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  :class="[{'table-hovers':isChecked[$index]}]" @click="checkOneMx($index,item),checkSelect([$index,'one','jsonList'],$event)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.tysqdh">西药收入</div></td>
                   <!--  <td><div class="zui-table-cell" v-text="fDate(item.fysj,'date')">药品收入</div></td>
                    <td><div class="zui-table-cell" v-text="item.ksmc">药品收入</div></td>
                    <td><div class="zui-table-cell">关联申请号单</div></td> -->

                    <!--<td><div class="zui-table-cell" v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-wtg':item.shzfbz=='1' ? 'color-ysh':item.shzfbz=='2' ? 'color-dlr':item.shzfbz=='3' ? 'color-wc': '' ">-->

                        <!--</div>-->
                    <!--</td>-->
                    <!-- <td>
                        <div class="zui-table-cell">状态</div>
                    </td> -->
                    <td class="cell-l"><div class="zui-table-cell cell-l">
                        <!--<i class="icon-width icon-dy-h icon-dy" v-show="item.shzfbz!=3"></i>-->
                        <!--<i class="icon-width icon-fy-h icon-fy" v-if="item.shzfbz==1" @click="fayao(item)"></i>-->
                        <!--<i class="icon-width icon-ty-h icon-ty" v-if="'item.shzfbz==2"></i>-->
                        <span  class="flex-center">
                                <em class="width30" >
                                  <i class="icon-width icon-ty-h" @click="fayao($index)" data-title="退药"></i>
                                </em>
                        </span>
                    </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>

</div>
    <div class="side-form ng-hide pop-850" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div v-for="(item,$index) in mxList">
	    <div class="slgl-by">
	        <!-- <i style="width: 30%;text-indent: 20px;"><em>退药单号:</em><em v-text="item[0].tysqdh"></em></i> -->
	        <i style="width: 25%"><em>患者姓名:</em><em v-text="item[0].brxm"></em></i>
	        <i><em>床号:</em><em v-text="item[0].rycwbh"></em></i>
	        <i><em>入院科室:</em><em v-text="item[0].ryksmc"></em></i>
	    </div>
	    <div class="ksys-side" style="padding: 15px 10px 15px 10px;">
              <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==0}">
                  <ul class="cfhj-top all-height">
                      <li>
                      <i>
                      	<input-checkbox @result="mxCheckBox($index,-1)" :list="'mxList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                      </i>
                      <i>序号</i>
                      <i>床号</i>
                      <i>姓名</i>
                      <i class="text-left">药品名称</i>
                      <i>剂型</i>
                      <i class="text-left">规格</i>
                      <i>发药数量</i>
                      <i>申请数量</i>
                      </li>
                  </ul>
                  <ul class="cfhj-content all-height">
                      <li v-for="(it,$in) in item" @click="checkSelect([$in,'one','item'],$event)" >
                      	  <i>
                      	  	<input-checkbox @result="mxCheckBox($index,$in)" :list="'mxList'"
                                            :type="'some'" :which="$in"
                                            :val="isMxChecked[$in]">
                            </input-checkbox>
                          </i>
                          <i v-text="$index+1">1</i>
                          <i class="relative">
                              <em class="title title-width" v-text="it.rycwbh" ></em>
                          </i>
                          <i v-text="it.brxm"></i>
                          <i class="relative">
                              <em class="title title-width" v-text="it.ryypmc" ></em>
                          </i>
                          <i v-text="it.jxmc"></i>
                          <i class="relative text-left">
                              <em class="title title-width" v-text="it.ypgg" ></em>
                          </i>
                          <i v-text="it.fysl"></i>
                          <i v-text="it.sqsl">频次</i>
                      </li>
                  </ul>
              </div>
	    </div>
	</div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <!-- <button class="zui-btn btn-parmary-f2a xmzb-db">打印</button> -->
        <button class="zui-btn btn-primary xmzb-db" @click="back">退药</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="ksty.js"></script>
</body>

</html>
