var wrapper = new Vue({
    el: '.panel',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        jsonList: [],
        isShow: true,
        kfList: [],
        thisKf: '02',
        popContent: {},
        kfEmpty: false,
        //分页信息
        param: {},
        //用例编码
        ylbm: '003002006',
        //权限科室编码
        qxksbm: '',
        YFList: [],
        yfSelected: '',
        thisYF: {
            'yfmc': 0,
        },
        search: '',//模糊查询条件
    },
    methods: {
        //检索查询回车键
        searchHc: function () {
            yjkmtableInfo.param.page = 1
            yjkmtableInfo.getData();

        },
        editSave: function () {
            yjkmtableInfo.save();
        },
        //获取药房权限
        getYfData: function () {
            //加载药房列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                function (data) {
                    if (data.a == 0) {
                        wrapper.YFList = data.d.list;
                        console.log(wrapper.YFList);
                        if (data.d.list.length > 0) {
                            wrapper.qxksbm = data.d.list[0].ksbm;
                            Vue.set(wrapper.popContent, 'yfbm', data.d.list[0].yfbm);//默认药房编码
                            wrapper.getCsqx(); //加载完库房再次加载参数权限
                        }
                    } else {
                        malert("药房获取失败", 'top', 'defeadted');
                    }

                });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "yfbm":
                    Vue.set(this.popContent, 'yfbm', val[0]);
                    Vue.set(this.popContent, 'yfmc', val[4]);
                    // wap.getCsqx();
                    yjkmtableInfo.getData();
                    break;
                default:
                    break;
            }
        },
        edit: function () {
            this.isShow = false;
            $('.box-disabled').attr('disabled', false)
        },
        //权限管理
        getCsqx: function () {

            //获取参数权限
            var parm = {
                "ylbm": this.ylbm,
                "ksbm": wrapper.qxksbm
            };
            //获取参数权限信息
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            //该用例没有权限参数
                        }
                    }
                    yjkmtableInfo.getData();
                } else {
                    malert('参数权限获取失败' + json.c, 'top', 'defeadted')
                }
            });
            // yjkmtableInfo.getData();
        },
        //药房改变
        yfChange: function (item) {
            this.yfSelected = item;
            if (item == 0) {
                return;
            } else {
                wrapper.qxksbm = item.ksbm
            }
            //查询获取药房权限
            wrapper.getCsqx();

        },


    }
});

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
var saves = null;

//科目
var yjkmtableInfo = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        jsonList: [],
        isShow: true,
        //用例编码
        ylbm: '003002006',
        //权限科室编码
        qxksbm: '',

        YFList: [],
        yfSelected: '',
        thisYF: {
            'yfmc': 0,
        },
        kfEmpty: false,
        changeList: [],
        param: {
            page: 1,
            rows: 10,
            parm: ''
        }


    },
    methods: {
        //进入页面加载列表信息
        getData: function () {
            common.openloading('.zui-table-view')
            Vue.set(yjkmtableInfo.param, 'yfbm', wrapper.popContent.yfbm);
            Vue.set(yjkmtableInfo.param, 'parm', wrapper.search);
            //分页信息
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglkcxl&types=query&" +
                "json=" + JSON.stringify(yjkmtableInfo.param),
                function (json) {
                    if (json.a == "0") {
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                        console.log(yjkmtableInfo.jsonList);
                    }
                });
            common.closeLoading()
        },

        save: function () {
            /* var isEmpty = false;
             if (wrapper.thisKf == null) {
                 malert("请先选择库房！");
                 this.kfEmpty = true;
                 isEmpty = true;
             }*/
            var json = {
                "list": this.changeList
            };
            this.$http.post('/actionDispatcher.do?reqUrl=YfbKcglkcxl&types=update',
                JSON.stringify(json))
                .then(function (data) {
                    if (data.body.a == 0) {
                        malert("数据更新成功", 'top', 'success');
                        wrapper.isShow = true;
                    } else {
                        malert("数据更新失败", 'top', 'defeadted');
                    }
                })
        },

        modify: function (index) {
            var obj = yjkmtableInfo.jsonList[index];
            if (obj.zdkc && obj.zgkc && obj.zdkc > obj.zgkc) {
                malert("请确认上量与下量大小", 'top', 'defeadted');
                return;
            }
            if (yjkmtableInfo.changeList.length == 0) {
                //添加
                yjkmtableInfo.changeList.push(obj);
            } else {
                for (var i = 0; i < yjkmtableInfo.changeList.length; i++) {
                    if (yjkmtableInfo.changeList[i].ypbm == obj.ypbm && yjkmtableInfo.changeList[i].yfbm == obj.yfbm) {
                        yjkmtableInfo.changeList[i] = obj;
                        return;
                    }
                }
                yjkmtableInfo.changeList.push(obj);
            }
        }


    },


});
//yjkmtableInfo.getData();
wrapper.getYfData();
// wap.loadyjkm();
//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

})




