var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        pdkdhc:{},
        kfList: [],
        lbList: [],
        dlList: [],
        jsonList: [],
        editIndex:undefined,
        popContent: {
            'pdfs':'1'
        },
        queryData: {},
        pdfs_tran:{
            '1':'物资大类',
            '2':'物资类别',
            '3':'全部物资',
        },
    },
    mounted: function () {
        this.init()
        this.getKfData()
        this.getData()
        this.getwzlb()
        this.getwzdl()
    },
    updated: function () {
        changeWin()
    },
    methods: {
        init:function(){
            this.queryData=sessionStorage.getItem('scpd') && JSON.parse(sessionStorage.getItem('scpd'))
            this.popContent=Object.assign(this.queryData.scpd,this.popContent)
            this.param.pdpzh=this.queryData.scpd.pdpzh
        },
        getwzlb : function(){
            //加载数据
            var parm = {
                page : 1,
                rows : 2000,
            }
            //获取物资类别
            $.getJSON("/actionDispatcher.do?reqUrl=New1WzkfXtwhWzlb&types=query&json="+JSON.stringify(parm), function(json) {
                if(json.a=="0"){
                    wrapper.lbList = json.d.list;
                }else{
                    malert("获取物资大类失败！",'top','defeadted');
                }
            });
        },
        getwzdl : function(){
            //加载数据
            var parm = {
                page : 1,
                rows : 2000,
            }
            //获取物资大类
            $.getJSON("/actionDispatcher.do?reqUrl=New1WzkfXtwhWzdl&types=query&json="+JSON.stringify(parm), function(json) {
                if(json.a=="0"){
                    wrapper.dlList = json.d.list;
                }else{
                    malert("获取物资大类失败！",'top','defeadted');
                }
            });
        },
        delectFun:function(index){
            this.jsonList.splice(index,1)
        },
        getKfData: function () {
            // 请求库房的api
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=wzkf', function (data) {
                if (data.a == 0) {
                    wrapper.kfList = data.d.list;
                    brzcList.wzkfList = data.d.list;
                    wrapper.popContent.kfbm = data.d.list[0].wzkfbm;
                } else {
                    malert("获取库房列表失败");
                }
            });
        },
        // 新增
        AddMdel: function () {
            brzcList.title = '添加物资'
            brzcList.open();
            brzcList.popContent = {};

        },
        //获取明细
        getData: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=New1WzkfKfywPdb&types=queryMx&parm=' + JSON.stringify(this.param),
                function(data) {
                    if(data.a == "0") {
                        wrapper.jsonList=data.d
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        zdpd:function(){
            if(this.popContent.pdfs == '1') {
                var pdlb = this.popContent.dlbm
            }else if(this.popContent.pdfs == '2'){
                var pdlb = this.popContent.lbbm
            }
            var dj={
                pdfs:this.popContent.pdfs,
                lbbm:pdlb,
                wzkf:this.popContent.kfbm,
                yljgbm:jgbm
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywPdb&types=zdpd',JSON.stringify(dj))
                .then(function (json) {
                    if(json.body.a == "0" && json.body.d) {
                        wrapper.jsonList=json.body.d.list
                        malert("提交成功", 'top', 'success');
                    } else {
                        malert(json.body.c, 'top', 'defeadted');
                    }
                });





        },

        //保存盘点
        submitAll :function(){
           var dj={
               wzkf:this.popContent.kfbm,
               bzms:this.popContent.bzms,
                yljgbm:this.yljgbm,
                zdr:this.userId,
                // zdrq : new Date(),
            };
            var djmx ={
                yljgbm:this.yljgbm,
                zdr:this.userId,
                // zdrq : new Date(),

            };
            var submit ={
                list:{
                    dj:dj,
                    djmx: this.jsonList,
                }
            }

            if(JSON.stringify(wrapper.pdkdhc)=='{}'){
                wrapper.pdkdhc=JSON.parse(JSON.stringify(submit));//用于缓存提交的对象，二次提交时防止重复提交
            }else {
                if (JSON.stringify(wrapper.pdkdhc) == JSON.stringify(submit)) {
                    malert("盘点单已经保存请不要重复提交!", 'top', 'defeadted');
                    return false;
                } else {
                    wrapper.pdkdhc = JSON.parse(JSON.stringify(submit));//用于缓存提交的对象，二次提交时防止重复提交
                }
            }
            submit.list.djmx.zdr=new Date();
            submit.list.dj.zdrq=new Date();
            this.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywPdb&types=saveBatch',JSON.stringify(submit))
                .then(function (json) {
                    if(json.body.a == "0") {
                        malert("提交成功", 'top', 'success');
                    } else {
                        //清空缓存
                        wrapper.pdkdhc={};
                        malert(json.body.c, 'top', 'defeadted');
                    }
                });
        },
        // 审核
        passData: function () {
            this.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywPdb&types=passDj',JSON.stringify(this.queryData)).then(function (json) {
                    if(json.body.a == "0") {
                        malert("审核成功", 'top', 'success');
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
        // 取消
        cancel: function () {
            wrapper.topClosePage('page/wzkf/kfyw/pdgl/scpd.html','page/wzkf/kfyw/pdgl/pdgl.html')
            window.top.$("#盘点管理")[0].contentWindow.getData();
        },
        // 编辑
        edit: function (index) {
            this.editIndex=index;
            brzcList.title = '编辑物资';
            brzcList.popContent=this.jsonList[index]
            brzcList.open();
        },
        sh:function(){
            var obj={
                a:this.popContent,//对象
                b:this.jsonList,//数组
            }
            //保存

            this.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywPdb&types=passDj', JSON.stringify(this.popContent))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("审核成功！", 'top', 'success');
                       wrapper.cancel()
                        // malert("审核成功！")
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
    }

});


var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        title: '',
        num: 0,
        popContent: {},
        dg: {
            page: 1,
            rows: 30,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '物资名称': 'wzmc',
            '物资规格': 'wzgg',
            '分装比例': 'fzbl',
            '进价': 'jj',
            '单价': 'dj',
            '库存数量':'kcsl',
            '产地': 'cd',
        },
        searchCon:[],
        selSearch:-1,
    },
    mounted: function () {
        this.GetRy();
    },
    methods: {
        //药品名称下拉table检索数据
        changeDown: function (event, type) {
            this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                $("#rksl").focus();
            }
        },
        //当输入值后才触发
        change: function (add, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (!add) this.page.page = 1; //  设置当前页号为第一页
            var parm = {
                wzkf: wrapper.popContent.kfbm,
                page:this.dg.page,
                rows:this.dg.rows,
                parm : val
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1WzkfKfywKccx&types=kfkc&parm=' + JSON.stringify(parm),
                function (data) {
                    if (add) {//不是第一页则需要追加
                        brzcList.searchCon=brzcList.searchCon.concat(data.list);
                    } else {//第一页则直接赋值
                        brzcList.searchCon = data.d.list;
                    }
                    brzcList.page.total = data.d.total; //  设置当前页号为第一页
                    brzcList.selSearch = 0;
                    $(".selectGroup").show();
                });
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                this.dg.page++;
                this.change(true,this.popContent.wzmc)
            }
            this.popContent = item;
            $(".selectGroup").hide();
        },
        // 关闭
        closes: function () {
            this.num = 0;

        },
        open: function () {
            this.num = 1;
            // 设置库房
        },
        //保存
        save: function () {
            if(this.title == '编辑物资'){
                wrapper.jsonList[wrapper.editIndex]=this.popContent
            }else{
                wrapper.jsonList.push(this.popContent)
            }
                //编辑保存成功关闭弹窗
                this.closes();
        },
        GetRy: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    if (data.a == '0') {
                        brzcList.ryList = data.d.list;
                    }
                });
        }
    }
});
