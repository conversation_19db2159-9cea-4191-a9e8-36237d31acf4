<link rel="stylesheet" href="brPage/jybgd.css">
<div class="flex-container">
    <div id="bgdList" class="xm-list">
        <div class="zui-table-view">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                        <tr>
                            <th class="cell-l">
                                <div class="zui-table-cell cell-l">检验项目</div>
                            </th>
                            <th class="cell-l">
                                <div class="zui-table-cell cell-l">病人姓名</div>
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body no-user-select" @scroll="scrollTable($event)">
                <p v-if="!djList.length" class="noData text-center zan-border">暂无数据...</p>
                <table v-if="djList.length" class="zui-table table-width50">
                    <tbody>
                        <tr ref="list" v-for="(item, $index) in djList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" :tabindex="$index" @click="checkOne($index)">
                            <td class="cell-l">
                                <div class="zui-table-cell cell-l" v-text="$index+1" v-text="item.jyxmmc"></div>
                            </td>
                            <td class="cell-l">
                                <div class="zui-table-cell cell-l" v-text="$index+1" v-text="item.brxm"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="print-show" class="bgdArea printShow"></div>
</div>
<script src="bgd.js"></script>
