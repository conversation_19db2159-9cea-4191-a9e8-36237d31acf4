var dqsyh = null;
var ryrq = getTodayDateTime();
var jbbmZy = {'中医疾病编码': 'jbmb', '中医疾病名称': 'jbmc', '拼音代码': 'pydm'};
var jbbm = {'疾病编码': 'jbmb', '疾病名称': 'jbmc', '拼音代码': 'pydm'};
var zd_enter = new Vue({
    el: '#lgdj_info',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData, printer],
    components: {
        'search-table': searchTable,
        'jbsearch-table': searchTable,
    },
    hzsearch: '',
    url: '',
    hisbm: '',
    isShow: false,
    zzbrxx: [],
    data: {
        bxbrdisable: true,   //禁用保险病人
        brid: null,
        _ghxh: null,
        param: {},
        popContentPkun: {},
        isShow: false,
        pkhShow: false,
        yjqk: true,
        bqcy: false,
        ghxh: null,
        popContent: {
            'brjbxxModel': {},
            'ryqk': '3',
            'bxlbbm': '01',
            sfcr: '1'
        },
        popContentTmp: {},
        nhdj: false,
        isShowPage: false,
        title: '',
        caqxContent: {},
        readonly: false, //判断控件是否可用
        ifClick: true, //判断有没有点击这个按钮
        csqxjycw: false, //针对参数权限设置床位是否可以选
        csqxjyzyys: false,//针对参数权限设置住院医生是否可以选
        ylkxx: false, //进入查看信息时医疗卡信息都不可以修改
        mzList: [],//民族
        gjList: [],//国籍
        hyzkList: [],//婚姻状况
        zyList: [],//职业
        bxlbList: [],//保险类别
        brfbList: [],//病人费别
        zyksList: [],//住院科室
        zyysList: [],//住院医生
        glzyysList: [],//过滤过后的住院医生
        cwhList: [],//床位号
        glcwhList: [],//过滤过后的床位号
        mzksList: [],//门诊科室
        mzysList: [],//门诊医生
        glmzysList: [],//过滤后的门诊医生
        zflxList: [], //支付类型
        ylklxList: [],//医疗卡类型
        lxrgxList: [], //联系人关系
        zjlxList: [], //证件类型
        selSearch: -1,
        selSearchZy: -1,
        selectBrxm: false, //病人下拉框是否显示
        nhbrxxList: [],  //农合家庭成员信息
        provinceList: [], //省市县集合
        cityList: [],
        countyList: [],
        provinceList1: [],
        cityList1: [],
        countyList1: [],
        rydjList: [],
        dgparm: {rows: 20000},
        pkhbzGz: '0',
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        //下拉table病人基本信息检索选中对象
        brxxContent: {},
        searchCon: [],
        them_tran: {
            'brxb': dic_transform.data.brxb_tran,
            'ismzry': dic_transform.data.istrue_tran
        },
        gsjkptBm: '',
        them: {
            '门诊入院': 'ismzry',
            '姓名': 'brxm',
            '性别': 'brxb',
            '出生日期': 'csrq',
            '身份证号码': 'sfzjhm',
            '手机号码': 'sjhm',
            '门诊医生': 'mzysxm',
            '门诊科室': 'mzksmc'
        },
        //疾病编码下拉table
        jbbmContent: {},
        jbbmContentZy: {},
        jbsearchCon: [],
        jbsearchConZy: [],
        jbthem: jbbm,
        jbthemZy: jbbmZy,
        printList: [],
        zyh: {},
        cardList: {},
        isUpdate: 0,
        ismzjr: 0,
        ylkh: null,
        popContent1: {},
        queryStr: {
            page: 1,
            rows: 20,
            order: "asc",
            total: null
        },
        them_tran1: {},
        pkhObj: {},
        them1: {
            '人员姓名': 'ryxm',
            '人员编码': 'rybm',
            '科室名称': 'ksmc',
        },
        searchCon1: [],
        selSearch1: -1,
        zflxIfInput: true, //  是否允许选择支付类型
        searchCon2: [],
        them_tran2: {},
        searchjson: {},
        jzdmcType: 0,
        them2: {
            '医生姓名': 'ryxm',
            '医生编码': 'rybm',
            '医生科室': 'ksmc',
        },
		readonlyBah: false,
		med_typeList:{
			'21':'普通住院',
			'22':'外伤住院',
			'23':'转外诊治住院',
			'24':'急诊转住院',
			'92':'其他住院',
			'2905':'慢性病住院',
            '2110':'新冠住院',
            '210104':'急诊住院'
		},
        // them_tran: {
        //     'brxb': dic_transform.data.brxb_tran
        // },
        jyrqlx_tran: {
            "1": "新冠肺炎确诊患者",
            "2": "其他国家突发公共事件",
            "3": "新冠肺炎疑似患者",
            "4": "无症状感染者",
        },
        jytssx_tran: {
            "9101": "大学生外伤门诊首诊",
            "9102": "大学生外伤门诊复诊",
            "9205": "床日费用精神病住院按高额",
            "9206": "床日费用精神病住院按床日",
            "9301": "住院属性精神病合并躯干",
            "9302": "住院属性艾滋病",
            "9303": "住院属性肾功能衰竭透析治疗及移植手术",
            "9304": "住院属性肝、肾、骨髓（含造血干细胞）移植术",
            "9305": "住院属性重型再生障碍性贫血",
            "9306": "住院属性系统性红斑狼疮",
            "9307": "住院属性恶性肿瘤手术及放化疗治疗",
            "9308": "住院属性慢性病白血病",
            "9309": "住院属性骨髓异常综合症及骨髓增生性疾病",
            "9310": "住院属性精神病合并躯干",
        },
    },
    updated: function () {
        $('#tplink').remove();
    },
    //页面渲染完成之后加载数据
    mounted: function () {
        if (sessionStorage.getItem('brPage1'+ this.getQueryString('ghxh'))) {
            Vue.set(this,'ghxh',JSON.parse(sessionStorage.getItem('brPage1'+ this.getQueryString('ghxh')))[2].ghxh);
            Vue.set(this,'brid',JSON.parse(sessionStorage.getItem('brPage1'+ this.getQueryString('ghxh')))[2].brid);
            this.gsjkptBm = window.top.J_tabLeft.gsjkptBm;
            var DomeDateAttr = ['#pydm', '#zjlx input', '#nldw input', '#gj input', '#hzlx input', '#lxrgx input', '#lxr', '#lxrdh'];
            for (var i = 0; i < DomeDateAttr.length; i++) {
                $(DomeDateAttr[i]).attr("data-skip", '');
            }
            this.getCsqx(); //参数权限
            this.getbxlb(); //获取保险类别
            //门特病标志
            Vue.set(this.popContent.brjbxxModel, 'mtbbz', '0');
            this.getData(this.brid);
            $("#ykth").focus();
            this.popContent.sfcr = '1';
            // 入院日期
            laydate.render({
                elem: '#timeRyrq',
                trigger: 'click',
                theme: '#1ab394',
                type: 'datetime',
                done: function (value, data) {
                    //应该在这个储存这个值
                    zd_enter.popContent.brjbxxModel.ryrq = value;
                }
            });
        }
    },
    methods: {
		guanbi:function(){
			yibao_panel.title = '患者医保信息';
			yibao_panel.isShowYibaoPage = false;
			yibao_panel.isYibaoDengJi = false;
			yibao_panel.close = true;
		},
        mtbrQuery() {
            toMtbrList.isFold = true;
            toMtbrList.getData();
        },
        setDm: function (brxm) {
            this.setPYDM(brxm, 'popContent', 'pydm');
            this.popContent.wbjm = this.setWBJM(brxm);
        },
        pkfGz: function () {
            popTable.isShow = true;
            zd_enter.pkhbzGz = '0';
            zd_enter.$nextTick(function () {
                $('#loadPageGznh').load('nhYbPage/gzywxhy/pkhrz.html');
            })
        },
        close: function () {
            $("#ykth").focus();
            this.bqcy = false
        },
        save: function () {
            this.bqcy = false
        },
        commonResultChange: function (val) {
            var type = val[2][1];
            switch (type) {
                case "ylklx":
                    Vue.set(this.popContent, "ylklx", val[0]);
                    Vue.set(this.popContent.brjbxxModel, "ylklx", val[0]);
                    break;
            }
        },
        //刷新
        refresh: function () {
            this.popContent = {
                'brjbxxModel': {},
                'ryqk': '3',
                sfcr: '1'
            };
            this.popContent.brjbxxModel.ryrq = getTodayDateTime();
            //入院情况-一般，//支付类型：取参数，已增加参数
            this.popContent.ryqk = '3';
            //入院途径：门诊
            this.popContent.rytj = '2';
			
			this.popContent.med_type = '21';
			
            //身份证件类型
            this.popContent.brjbxxModel.sfzjlx = "01";
            //年龄单位
            this.popContent.nldw = "1";
            //是否保险病人
            this.popContent.bxbr = "0";
            //门特病标志
            Vue.set(this.popContent.brjbxxModel, 'mtbbz', '0');
            this.popContent.sfcr = '1';
            //this.brid = JSON.parse(decodeURI(this.getQueryVariable('item')));
            // if (loadPage.stateMap.chlb.pageParam) {
            //     this.brid = loadPage.stateMap.chlb.pageParam.brid;
            //     console.log(this.brid);
            //     if (loadPage.stateMap.chlb.pageParam.ismzjr) {
            //         this.ismzjr = 1;
            //         $("#search").hide();//不允许检索
            //     }
            // }
            this.isUpdate = 0;
            this.getCsqx(); //参数权限
            this.getbxlb(); //获取保险类别
            this.getData(null);
            this.ylkh = null;
            this.pkhbzGz = '0';
            this.jbbmContent.jbmc = null;
            $("#ykth").focus();
        },
        //当输入值后才触发
        change1: function (add, val) {
            // //库房非空判断
            zd_enter.popContent.mzysxm = val;
            if (!add) this.queryStr.page = 1;       // 设置当前页号为第一页
//            this.queryStr.parm=val
//            this.queryStr.ysbz=1
            var _searchEvent = $(event.target.nextElementSibling)[0];
            this.queryStr.parm = zd_enter.popContent.mzysxm;
            var json = {
                ysbz: '1',
                tybz: '0',
            };
            var dg = {
                page: this.queryStr.page,
                rows: this.queryStr.rows,
                parm: this.queryStr.parm,
            };
            //初始化页面加载产地编码
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(json) + "" + "&dg=" + JSON.stringify(dg), function (data) {
                if (data.a == 0) {
                    if (add) {
                        zd_enter.searchCon1 = zd_enter.searchCon1.concat(data.d.list)
                    } else {
                        zd_enter.searchCon1 = data.d.list;
                    }
                    //药品产地
                    zd_enter.queryStr.total = data.d.total;
                    zd_enter.selSearch1 = 0;
                    if (data.d.list.length != 0) {
                        // $(".selectGroup").hide();
                        $(_searchEvent).show()
                    } else {
                        $(_searchEvent).show();
                    }
                } else {
                    // malert("药品产地获取失败!",'top','defeadted');
                }

            });
        },
        //双击选中下拉table
        selectOne1: function (item) {
            //查询下页
            if (item == null) {
                this.queryStr.page++;
                this.change1(true, this.popContent['ryxm'])
                return;
            }
			            this.popContent['mzys'] = item['rybm'];
            this.popContent['mzysxm'] = item['ryxm'];
			this.popContent['mzysks'] = item['ksbm'];
			
            this.$forceUpdate()
            $(".selectGroup").hide();
        },
        //药品名称下拉table检索数据
        changeDown1: function (event, value) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.popContent1 = this.searchCon1[this.selSearch1]
            this.inputUpDown(event, this.searchCon1, "selSearch1");
            //选中之后的回调操作
            if (event.keyCode == 13) {
				                this.popContent['mzys'] = this.popContent1['rybm'];
                this.popContent['mzysxm'] = this.popContent1['ryxm'];
				this.popContent['mzysks'] = this.popContent1['ksbm'];
                this.$forceUpdate()
                $(".selectGroup").hide();
                this.selSearch1 = -1
                this.nextFocus(event)
            }
        },
        scrollButtom: function ($event) {
            var scrollbar = document.getElementById('scrollbar')
            if (scrollbar.scrollHeight > window.innerHeight) {
                scrollbar.scrollTop = scrollbar.scrollHeight;
            }

        },
        setCsrq: function (event) {
            if (!this.popContent.brjbxxModel.sfzjhm) {
                console.log(this.popContent.brjbxxModel.csrq);
                if (this.popContent.nl > 0) {
                    this.popContent.brjbxxModel.csrq = this.AgetoBrnl(this.popContent.nl, this.popContent.nldw);
                    zd_enter.popContent = Object.assign({}, zd_enter.popContent);
                    if (event) this.nextFocus(event, 3)
                } else {
                    if (event) this.nextFocus(event)
                }
            }
        },
        // loadYibaoPage: function () {
        //     yibao_panel.isShowYibaoPage = true;
        //     yibao_panel.isYibaoDengJi = false;
        //
        //     $('#loadYibaoPage').load('/newzui/page/zygl/rcygl/rydj/014cdyhyb/014cdyhyb.html');
        // },
        // loadIdCard: function () {
        //     if (zd_enter.caqxContent.N05002200159) {
        //         var str = "IDCard&HD"
        //         switch (window.top.J_tabLeft.IDCardType) {
        //             case "01":  //华大
        //                 str = "HD";
        //                 break;
        //             case "02":  //华旭FDX3
        //                 str = "HX3";
        //                 break;
        //             case "03":  //神思
        //                 str = "SS";
        //                 break;
        //             case "04":  //新中新
        //                 str = "XZX";
        //                 break;
        //             default:
        //                 malert("读卡器类型设置无效[" + window.top.J_tabLeft.IDCardType + "];", 'top', 'defeadted');
        //                 break;
        //         }
        //         search.ifReadCard = true;
        //         if (str == "HX3") { // 华旭
        //             $.post("http://127.0.0.1:18889/readIdCard", {
        //                 'klx': str
        //             }, function (d) {
        //                 if (d.code == 0) {
        //                     zd_enter.popContent.brjbxxModel.brxm = d.data.pName;
        //                     zd_enter.popContent.brjbxxModel.brxb = d.data.pSex;
        //                     zd_enter.popContent.brjbxxModel.jzdmc = d.data.pAddress;
        //                     zd_enter.jzdmcType = 1;
        //                     zd_enter.popContent.brjbxxModel.sfzjhm = d.data.pCertNo;
        //                     zd_enter.sfjhm();
        //                     malert("读取身份证成功成功");
        //                 } else {
        //                     search.ifReadCard = false;
        //                     malert("读取身份证失败！！", 'top', 'defeadted');
        //                 }
        //
        //             });
        //         }
        //
        //         if (str == "XZX") { // 新中新
        //             $.ajax({
        //                 dataType: "JSONP",
        //                 type: "get",
        //                 url: "http://127.0.0.1:8989/api/ReadMsg",
        //                 success: function (data) {
        //                     if (data.errmsg == '') {
        //                         zd_enter.popContent.brjbxxModel.brxm = data.name;
        //                         zd_enter.popContent.brjbxxModel.brxb = data.sex == '男' ? '1' : '2';
        //                         zd_enter.popContent.brjbxxModel.jzdmc = data.address;
        //                         zd_enter.popContent.brjbxxModel.sfzjhm = data.cardno;
        //                         zd_enter.jzdmcType = 1;
        //                         zd_enter.setPYDM(zd_enter.popContent.brjbxxModel.brxm, 'popContent', 'pydm');
        //                         zd_enter.popContent.wbjm = zd_enter.setWBJM(zd_enter.popContent.brjbxxModel.brxm);
        //                         zd_enter.sfjhm();
        //                         malert("读取身份证成功成功");
        //                     } else {
        //                         search.ifReadCard = false;
        //                         malert("读取身份证失败！！", 'top', 'defeadted');
        //                     }
        //                 },
        //                 error: function (e) {
        //                     //失败执行
        //                     //alert(e.status + ',' + e.statusText);
        //                     malert("读取身份证失败！" + e.status + ',' + e.statusText, 'top', 'defeadted');
        //                 }
        //             });
        //         }
        //     } else {
        //         var str = "IDCard&HD"
        //         switch (window.top.J_tabLeft.IDCardType) {
        //             case "01":  //华大
        //                 str = "IDCard&HD";
        //                 break;
        //             case "02":  //华旭FDX3
        //                 str = "IDCard&HX3";
        //                 break;
        //             case "03":  //神思
        //                 str = "IDCard&SS";
        //                 break;
        //             default:
        //                 malert("读卡器类型设置无效[" + window.top.J_tabLeft.IDCardType + "];", 'top', 'defeadted');
        //                 break;
        //         }
        //         window.top.J_tabLeft.socketCard.send(str);
        //         var cs = 0;
        //         var interval = setInterval(function () {
        //             cs += 1;
        //             if (window.top.J_tabLeft.rsCard) {
        //                 if (window.top.J_tabLeft.ifokCard) {
        //                     clearInterval(interval);
        //                     var cardobj = JSON.parse(window.top.J_tabLeft.rslmsgCard);
        //                     zd_enter.popContent.brjbxxModel.brxm = cardobj.pName;
        //                     if (cardobj.pSex == "1") {
        //                         zd_enter.popContent.brjbxxModel.brxb = "1";
        //                     } else if (cardobj.pSex == "2") {
        //                         zd_enter.popContent.brjbxxModel.brxb = "2";
        //                     } else {
        //                         zd_enter.popContent.brjbxxModel.brxb = "9";
        //                     }
        //                     zd_enter.popContent.brjbxxModel.jzdmc = cardobj.pAddress;
        //                     zd_enter.jzdmcType = 1;
        //                     zd_enter.popContent.brjbxxModel.sfzjhm = cardobj.pCertNo;
        //                     var csrq = zd_enter.sfzhtodate(cardobj.pCertNo);
        //                     if (csrq) {
        //                         zd_enter.popContent.brjbxxModel.csrq = csrq;
        //                     }
        //                     zd_enter.popContent.nl = zd_enter.toAge(zd_enter.popContent.brjbxxModel.csrq).age;
        //                     zd_enter.popContent.nldw = zd_enter.toAge(zd_enter.popContent.brjbxxModel.csrq).unitNum;
        //
        //
        //                     zd_enter.getAddress();
        //
        //                     zd_enter.$forceUpdate();
        //                     malert("读取身份证成功成功", 'top', 'success');
        //                     window.top.J_tabLeft.rsCard = false;
        //                     window.top.J_tabLeft.ifokCard = false;
        //                 } else {
        //                     malert("读取身份证失败！", 'top', 'defeadted');
        //                     clearInterval(interval);
        //                     window.top.J_tabLeft.rsCard = false;
        //                     window.top.J_tabLeft.ifokCard = false;
        //                 }
        //             }
        //             if (cs >= 10) {
        //                 // malert(rslmsg);
        //                 window.top.J_tabLeft.rsCard = false;
        //                 window.top.J_tabLeft.ifokCard = false;
        //                 clearInterval(interval);
        //                 malert("读取身份证超时！请重试", 'top', 'defeadted');
        //             }
        //         }, 1500);
        //     }
        //
        //
        // },
        //检索获取病人信息
        getData: function (brid) {
            console.log(brid);
            this.addData(); //调用清除
            if (!brid) {
                this.brxxContent = {};
                this.mrz();//如果住院号不存在则设置默认值
                return;
            }
            //设置传参 --- 门诊接入
            var parm = {
                brid: brid,
				bqcybz: '0'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=querrydjJs&parm=" + JSON.stringify(parm), function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    console.log(json.d);
                    if (json.d != null && json.d != "") {
                        if (json.d[0].zyh != null) {
                            var _text = "该病人已在院，不允许重复入院！<br/>";
                            _text += "住院号：" + json.d[0].zyh + "<br/>";
                            _text += "姓名：" + json.d[0].brjbxxModel.brxm + "<br/>";
                            _text += "入院日期：" + zd_enter.fDate(json.d[0].ryrq, "datetime") + "<br/>";
                            _text += "科室：" + json.d[0].ryksmc;
                            malert(_text, "top", "defeadted");
                            zd_enter.ylkh = null;
                            zd_enter.brxxContent.text = null;
                            return
                        }
                        zd_enter.popContent = JSON.parse(JSON.stringify(json.d[0]));
                        zd_enter.popContentTmp = JSON.parse(JSON.stringify(json.d[0]));
                        if (zd_enter.ylkh) {
                            zd_enter.popContent.brjbxxModel.ylkh = zd_enter.ylkh;
                            zd_enter.popContent.ylklx = '07';
                            zd_enter.popContent.brjbxxModel.ylklx = '07';
                        } else {
                            zd_enter.popContent.brjbxxModel.ylkh = zd_enter.popContent.ylkh;
                            zd_enter.popContent.brjbxxModel.ylklx = zd_enter.popContent.ylklx;
                            zd_enter.popContent.ylklx = '07';
                            zd_enter.popContent.brjbxxModel.ylklx = '07';
                        }
                        if (zd_enter.popContent['jbbm'] != null) {
                            zd_enter.popContent['ryzdbm'] = zd_enter.popContent['jbbm'];
                            zd_enter.popContent['ryzdmc'] = zd_enter.popContent['jbmc'];
                        }
                        //Vue.set(zd_enter.brxxContent,'text',zd_enter.popContent.brjbxxModel.brxm);
                        //******************
                        var brid = zd_enter.popContent.brjbxxModel.brid;
                        var parm = {
                            brid: brid
                        };
                        //if(zd_enter.ismzjr == 1){
                        // @yqq 替换判断条件进行门诊入院信息查询
                        if (zd_enter.ismzry == 1) {
                            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDownYw&types=hzxx&json=" + JSON.stringify(parm), function (json) {
                                console.log(json);
                                //入院诊断
                                Vue.set(zd_enter.jbbmContent, "jbmc", json.d.list[0].cbzdmc);
                                Vue.set(zd_enter.popContent, 'ryzdbm', json.d.list[0].cbzdmc);
                                //入院科室
                                Vue.set(zd_enter.popContent, "ryks", json.d.list[0].mzks);
                                //门诊医生
                                Vue.set(zd_enter.popContent, "mzys", json.d.list[0].mzys);
                                Vue.set(zd_enter.popContent, "mzysxm", json.d.list[0].mzysxm);
                                //入院途径
                                zd_enter.popContent.rytj = '2';
                                //入院情况-一般，//支付类型：取参数，已增加参数
                                zd_enter.popContent.ryqk = '3';
                            });
                            //入院途径：门诊
                            zd_enter.popContent.rytj = '2';
                            //默认支付类型
                            zd_enter.popContent.zflxbm = zd_enter.caqxContent.cs00700100114;
                        }
                        //入院当前时间
                        //zd_enter.popContent.ryrq = ryrq;
                        //******************
                        zd_enter.selectFz();//赋值
                        zd_enter.csqxIfInputSr(); //根据参数权限判断是否允许输入
                        //赋值门诊入院的信息
                        console.log(zd_enter.brxxContent);
                        console.log();
                        zd_enter.popContent.ryqk = '3';
                        // zd_enter.popContent.brjbxxModel.hyzk = '20';
                    } else {
                        malert('未查到相关记录', 'top', 'defeadted');
                    }
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
                //有住院号或者有病人id都不允许输入医疗卡信息
                if (zd_enter.popContent.zyh != null || zd_enter.popContent.brjbxxModel.brid != null) {
                    //禁用医疗卡信息部分
                    zd_enter.ylkxx = true;
                }
                if (zd_enter.popContent.zyh != null && zd_enter.popContent.zyh != '') {
                    zd_enter.yjqk = false;//隐藏预交情况
                }
            });
            //this.GetPjhData();
        },

        //获取保险类别
        getbxlb: function () {
            var param = {bxjk: "B07"};
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            zd_enter.bxlbbm = json.d.list[0].bxlbbm;
                            zd_enter.bxurl = json.d.list[0].url;
//                            zd_enter.getS02();
                        }
                    } else {
                        malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
                    }
                });
        },
        //认证鉴权
        getS02: function () {
            var head = {
                operCode: "S02",
                rsa: ""
            };
            var body = {
                userName: "",
                passWord: ""
            };
            var param = {
                head: head,
                body: body
            };
            var str_param = JSON.stringify(param);

            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zd_enter.bxurl + "&bxlbbm=" + zd_enter.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                    if (json.a == 0) {
                        zd_enter.billCode = json.d;
                        zd_enter.rztg = true;
                    } else {
                        malert("认证鉴权失败，请从新操作", 'top', 'defeadted');
                        zd_enter.rztg = false;
                    }
                });
        },
        //医保卡号失焦事件
        ybkhBlur: function () {
            //先判断是否属于农合
            if (zd_enter.popContent.fbbm != '01') {
                switch (zd_enter.popContent.bxlbbm) {
                    case '01':
                        if (zd_enter.popContent.ybkh != null && zd_enter.popContent.ybkh != "") {
                            zd_enter.selectBrxm = true;//姓名下拉框显示
                            //执行读取参合证号信息的操作
                            var head = {
                                operCode: "S03",
                                billCode: zd_enter.billCode,
                                rsa: ""
                            };
                            var body = {
                                year: zd_enter.fDate(new Date(), 'year'),
                                medicalNo: zd_enter.popContent.ybkh
                            };
                            var param = {
                                head: head,
                                body: body
                            };
                            var str_param = JSON.stringify(param);
                            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zd_enter.bxurl + "&bxlbbm=" + zd_enter.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                                if (json.a == 0) {
                                    var res = eval('(' + json.d + ')');
                                    zd_enter.nhbrxxList = res.list;
                                    console.log(zd_enter.nhbrxxList)
                                } else {
                                    malert(json.c, 'top', 'defeadted');
                                }
                            });
                        } else {
                            zd_enter.selectBrxm = false;//姓名下拉框显示
                        }
                        break;
                    default:
                        zd_enter.selectBrxm = false;//姓名下拉框显示
                        break;
                }
            } else {
                zd_enter.selectBrxm = false;//姓名下拉框显示
            }
            // setTimeout(function(){
            //     $('.name input').focus();
            // }, 200);
        },
        //***********************测试票据信息
        cspjDate: function () {
            this.GetPjhData();
        },

        //病人基本信息下拉检索
        changeDown: function (event, type, content, searchCon) {
            if (event.keyCode == 13 && !this.caqxContent.cs00700100103) {
                this.nextFocus(event)
            }
            if (this[searchCon][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, content, searchCon);
            console.log(zd_enter.brxxContent);
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'text') {
                    Vue.set(zd_enter.brxxContent, 'text', zd_enter.brxxContent['brid']);
                    zd_enter.getData(zd_enter.brxxContent['brid']);
                }
                if (type == 'jbbm') {
                    zd_enter.popContent['ryzdbm'] = zd_enter.jbbmContent['jbmb'];
                    zd_enter.popContent['ryzdmc'] = zd_enter.jbbmContent['jbmc'];
                    this.nextFocus(event);
                }
                this.selSearch = -1
            }
        },

        //当输入值后才触发
        change: function (add, type, val) {
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            //病人基本信息检索
            if (type == 'text') {
                this.brxxContent[type] = val;
                if (this.brxxContent[type] == undefined || this.brxxContent[type] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.brxxContent[type];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=hzxx'
                    + '&dg=' + JSON.stringify(str_param),
                    function (data) {
                        if (data.d.list.length > 0) {
                            //如果查询结果只有1条则直接请求后台查询门诊费用信息
                            if (data.d.list.length == 1) {
                                zd_enter.getData(data.d.list[0].brid);
                            }
                            if (add) {//不是第一页则需要追加
                                for (var i = 0; i < data.d.list.length; i++) {
                                    var csrq = zd_enter.fDate(data.d.list[i].csrq, "date");
                                    Vue.set(data.d.list[i], 'csrq', csrq);
                                    zd_enter.searchCon.push(data.d.list[i]);
                                }
                            } else {//第一页则直接赋值
                                for (var i = 0; i < data.d.list.length; i++) {
                                    var csrq = zd_enter.fDate(data.d.list[i].csrq, "date");
                                    Vue.set(data.d.list[i], 'csrq', csrq);
                                }
                                zd_enter.searchCon = data.d.list;
                            }
                        }
                        zd_enter.page.total = data.d.total;
                        zd_enter.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }
            if (type == 'jbbm') {
                this.jbbmContent['jbmc'] = val;
                if (this.jbbmContent['jbmc'] == undefined || this.jbbmContent['jbmc'] == null) {
                    this.page.parm = "";
                } else {
                    this.page.parm = this.jbbmContent['jbmc'];
                }
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm'
                    + '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) {//不是第一页则需要追加
                            for (var i = 0; i < data.d.list.length; i++) {
                                zd_enter.jbsearchCon.push(data.d.list[i]);
                            }
                        } else {
                            zd_enter.jbsearchCon = data.d.list;
                        }
                        zd_enter.page.total = data.d.total;
                        zd_enter.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }

        },

        //中医疾病编码检索
        changeDown_zy: function (event, type, content, searchCon) {
            // if (event.keyCode == 13 && !zd_enter.caqxContent.cs00700100103) {
            //     this.nextFocus(event)
            // }
            if (this[searchCon][this.selSearchZy] == undefined) return;
            this.inputUpDown(event, searchCon, 'selSearchZy');
            this[content] = this[searchCon][this.selSearchZy]
            console.log(zd_enter.brxxContent);
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'jbbm') {
                    zd_enter.popContent['ryzyzdbm'] = zd_enter.jbbmContentZy['jbbm'];
                    console.log("---", zd_enter.jbbmContentZy['jbbm'])
                    zd_enter.popContent['ryzyzdmc'] = zd_enter.jbbmContentZy['jbmc'];
                    this.nextFocus(event);
                    this.selSearchZy = -1
                    this.$forceUpdate()
                    $(".selectGroup").hide();
                }
            }
        },

        //当输入值后才触发
        change_zy: function (add, type, val) {
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            if (type == 'jbbm') {
                this.jbbmContentZy['jbmc'] = val;
                this.page.parm = val;
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows,};
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbzy'
                    + '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) {//不是第一页则需要追加
                            for (var i = 0; i < data.d.list.length; i++) {
                                zd_enter.jbsearchConZy.push(data.d.list[i]);
                            }
                        } else {
                            zd_enter.jbsearchConZy = data.d.list;
                        }
                        zd_enter.page.total = data.d.total;
                        zd_enter.selSearchZy = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    });
            }

        },

        //鼠标双击（入院诊断信息）
        selectJbbmZy: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change_zy(true, 'jbbm', this.jbbmContentZy['jbmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {     // 否则就是选中事件,为json赋值
                zd_enter.jbbmContentZy = item;
                zd_enter.popContent['ryzyzdbm'] = zd_enter.jbbmContentZy['jbbm'];
                zd_enter.popContent['ryzyzdmc'] = zd_enter.jbbmContentZy['jbmc'];
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },

        //鼠标双击（病人挂号信息）
        selectOne: function (item) {
			            zd_enter.ismzry = item.ismzry;
            this.$forceUpdate()
            console.log(zd_enter.ismzry)
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'text', this.brxxContent['text']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {                            // 否则就是选中事件,为json赋值
                var parm = {
                    brid: item.brid,
					bqcybz: '0'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=querrydj&parm=" + JSON.stringify(parm), function (json) {
                    console.log(json);
                    if (zd_enter.isUpdate == 0) {
                        //添加
                        if (json.d.list.length > 0) {
                            malert("病人尚未出院，无法办理入院！", 'top', 'defeadted');

                        } else {
                            //无该病人入院登记信息
                            this.brxxContent = item;
                            //Vue.set(zd_enter.brxxContent, 'text', this.brxxContent['brxm']);
                            zd_enter.getData(this.brxxContent['brid']);
                            $(".selectGroup").hide();
                        }
                    }

                });

            }

        },

        //鼠标双击（入院诊断信息）
        selectJbbm: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.change(true, 'jbbm', this.jbbmContent['jbmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {     // 否则就是选中事件,为json赋值
                zd_enter.jbbmContent = item;
                zd_enter.popContent['ryzdbm'] = zd_enter.jbbmContent['jbmb'];
                zd_enter.popContent['ryzdmc'] = zd_enter.jbbmContent['jbmc'];
                this.$forceUpdate()
                $(".selectGroup").hide();
            }
        },

        //编辑页新增页面
        addData: function () {
            this.popContent = {'brjbxxModel': {}};
            this.popContentTmp = {'brjbxxModel': {}};
            $("#fphm").val(dqsyh);
            $("#jsylkh").val("");
            this.readonly = false;//设置编辑区允许输入
            this.yjqk = true;//隐藏预交情况

            $("#brxm").attr("disabled", false);
            $("#brxb").find("input").attr("disabled", false);
            $("#brnl").attr("disabled", false);
            $("#nldw").attr("disabled", false);
            $("#dbr").attr("disabled", false);
            $("#dbje").attr("disabled", false);
            $("#ryrq").attr("disabled", false);
            this.mrz();//如果住院号不存在则设置默认值

            // 允许修改入院科室 支付类型 预交金额
            $("#ryks-box input").removeAttr("disabled");
            $("#zyyj").removeAttr("disabled");
            this.zflxIfInput = true;

        },
        //清除
        clear: function () {
            this.popContent = {'brjbxxModel': {}};
            zd_enter.brxxContent = {};
            zd_enter.jbbmContent = {};
            zd_enter.jbbmContentZy = {};
            $("#fphm").val(dqsyh);
            zd_enter.readonly = false;//设置编辑区允许输入
            zd_enter.yjqk = true;//隐藏预交情况
            $("#brxm").attr("disabled", false);
            $("#brxb").find("input").attr("disabled", false);
            $("#brnl").attr("disabled", false);
            $("#nldw").attr("disabled", false);
            $("#dbr").attr("disabled", false);
            $("#dbje").attr("disabled", false);
            $("#ryrq").attr("disabled", false);
            this.mrz();//如果住院号不存在则设置默认值
        },
        searchHz: function () {
            if (this.hzsearch == '') {
                malert("请输入患者身份证号！", 'top', 'defeadted')
                return;
            }
            var param = {
                sfzjhm: this.hzsearch,
                hisbm: this.hisbm,
                url: this.url
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryBrxx&parm=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    zd_enter.zzbrxx = json.d;
                    Vue.set(zd_enter.popContent.brjbxxModel, 'brxm', zd_enter.zzbrxx.brxm);
                    zd_enter.setPYDM(zd_enter.popContent.brjbxxModel.brxm, 'popContent', 'pydm');
                    Vue.set(zd_enter.popContent, 'wbjm', zd_enter.setWBJM(zd_enter.popContent.brjbxxModel.brxm));
                    Vue.set(zd_enter.popContent.brjbxxModel, 'brxb', zd_enter.zzbrxx.brxb);
                    Vue.set(zd_enter.popContent.brjbxxModel, 'sfzjhm', zd_enter.zzbrxx.sfzjhm);
                    Vue.set(zd_enter.popContent.brjbxxModel, 'sjhm', zd_enter.zzbrxx.sjhm);
                    Vue.set(zd_enter.popContent, 'nl', zd_enter.zzbrxx.nl);
                } else {
                    malert('查询转诊患者数据失败，请手动输入！', 'top', 'defeadted');
                    return;
                }
            });
        },
        //获取参数权限
        getCsqx: function () {
            var ksparm = {"ylbm": 'N050012002'};
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                if (json.a == 0 && json.d.length > 0) {
                    //获取参数权限
                    var parm = {"ylbm": 'N050012002', "ksbm": json.d[0].ksbm};
                    $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0 && json.d.length > 0) {
                            for (var i = 0; i < json.d.length; i++) {
                                var csjson = json.d[i];
                                switch (csjson.csqxbm) {
                                    case "N05001200217": //居住地默认省份
                                        zd_enter.caqxContent.cs00400100217 = csjson.csz;
                                        break;
                                    case "N05001200218": //居住地默认城市
                                        zd_enter.caqxContent.cs00400100218 = csjson.csz;
                                        break;
                                    case "N05001200219": //居住地默认县
                                        zd_enter.caqxContent.cs00400100219 = csjson.csz;
                                        break;
                                    case "N05001200234": //甘肃贫困人口识别
                                        zd_enter.caqxContent.cs00400100234 = csjson.csz;
                                        break;
                                    case "N05002201138": //甘肃双向转诊转入地址
                                        if (csjson.csz && csjson.csz != '0') {
                                            zd_enter.isShow = true;
                                            zd_enter.url = csjson.csz
                                        }
                                        break;
                                    case "N05002201139": //甘肃双向转诊转入参数
                                        if (csjson.csz && csjson.csz != '0') {
                                            zd_enter.hisbm = csjson.csz
                                        }
                                        break;
                                    case "N05002201143": //甘肃双向转诊转入参数
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.N05002201143 = csjson.csz
                                        }
                                        break;
                                }
                            }
                        }
                    });
                }
            });
            //先获取到科室编码
            var ksparm = {"ylbm": 'N050022001'};
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(ksparm), function (json) {
                if (json.a == 0 && json.d && json.d.length > 0) {
                    //获取参数权限
                    var parm = {"ylbm": 'N050022001', "ksbm": json.d[0].ksbm};
                    $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0 && json.d && json.d.length > 0) {
                            for (var i = 0; i < json.d.length; i++) {
                                var csjson = json.d[i];
                                switch (csjson.csqxbm) {
                                    case "N05002200102" :    //入院是否允许录入医生和床位  0-不用录入；1-必须录入；2-可以只录入医生
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100102 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200103" :   //入院是否输入诊断
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100103 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200104" :   //是否打印入院通知单
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100104 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200105" :  //是否允许修改
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100105 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200106" :  //是否允许取消
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100106 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200107" :  //是否允许修改姓名、性别、年龄
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100107 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200108" :   //是否允许修改担保人
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100108 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200109" :  //是否允许修改担保金额
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100109 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200110" :  //是否允许修改入院时间
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100110 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200111" :  //是否允许修改出院时间
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100111 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200112" :  //是否允许修改出院病人信息
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100112 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200113" :   //是否在修改保险时同步修改对应费别
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100113 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200114" :   //录入支付类型编码
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100114 = csjson.csz;
                                        }
                                        break;
                                    case "N05002200115" :   //是否使用住院卡
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs00700100115 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200158" :   // @yqq 是否输入中医入院诊断
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.N05002200158 = parseInt(csjson.csz);
                                            console.log("是否输入中医入院诊断 :" + zd_enter.caqxContent.N05002200158);
                                        }
                                        break;
                                    case "N05002200159" :   // @yqq 身份证读卡器调用方式（入院登记）
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.N05002200159 = parseInt(csjson.csz);
                                        }
                                        break;
                                    case "N05002200160"://入院登记是否必填电话号码
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.cs05002200160 = csjson.csz;
                                        }
                                        break;
                                    case "N05002200161"://贵州药监平台地址
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.N05002200161 = csjson.csz;
                                        }
                                        break;
                                    case "N05002200162"://健康卡程序地址（宣汉）
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.N05002200162 = csjson.csz;
                                        }
                                        break;
                                    case "N05002200163"://住院默认卡类型
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.N05002200163 = csjson.csz;
                                        }
                                        break;
                                    case "N05002200165"://是否调用医保农合入院接口 0-否，1-是
                                        if (csjson.csz) {
                                            zd_enter.caqxContent.N05002200165 = csjson.csz;
                                        }
                                        break;
                                }
                            }
                            // console.log(zd_enter.caqxContent) ;
                            //参数权限加载完成再初始化下拉框数据（为了方便做参数权限控制）
                            zd_enter.GetMzData();   //民族
                            zd_enter.GetGjData();   //国籍
                            zd_enter.GetHyzkData();  //婚姻状况
                            zd_enter.GetZybmData();  //职业
                            zd_enter.GetBxlbData();  //保险类别
                            zd_enter.GetBrfbData();  //病人费别
                            zd_enter.GetGhksData();  //门诊科室
                            zd_enter.GetZyksData();  //住院科室
                            zd_enter.GetZyysData();  //住院医生
                            zd_enter.GetCwhData();   //住院床位号
                            zd_enter.GetZflxData(); //支付类型
                            zd_enter.GetPjhData();   //票据信息
                            zd_enter.ylklxselect();  //医疗卡信息
                            zd_enter.GetLxrgxData(); //联系人关系
                            zd_enter.GetzjlxData(); //证件类型
                            zd_enter.shengselect();//省

                            //设置是否能够选择门诊诊断
                            if (!zd_enter.caqxContent.cs00700100103) {
                                $("#ryzd").attr('disabled', true);
                            } else {
                                $("#ryzd").attr('disabled', false);
                            }
                            //是否允许修改（隐藏修改按钮）
                            if (!zd_enter.caqxContent.cs00700100105) {
                                $("#listEdit").hide();	//修改按钮隐藏
                            } else {
                                $("#listEdit").show();	//修改按钮显示
                            }
                            //是否允许取消入院
                            if (!zd_enter.caqxContent.cs00700100106) {
                                $("#listRemove").hide(); //取消入院按钮隐藏
                            } else {
                                $("#listRemove").show(); //取消入院按钮显示
                            }
                            //入院登记时必须录入医生和床位
                            if (!zd_enter.caqxContent.cs00700100102) {
                                zd_enter.csqxjyzyys = true;
                            }
                            if (!zd_enter.caqxContent.cs00700100102) {
                                zd_enter.csqxjycw = true;
                            }
                            //是否允许使用医疗卡
                            if (zd_enter.caqxContent.cs00700100115) {
                                zd_enter.ylkxx = false;//医疗卡信息输入恢复
                            } else {
                                zd_enter.ylkxx = true;//医疗卡信息输入恢复
                            }

                        } else {
                            malert('参数权限获取失败', 'top', 'defeadted');
                        }
                    });
                } else {
                    malert('权限科室获取失败', 'top', 'defeadted');
                }
            });
        },
        //进入页面默认加载值
        mrz: function () {
            //入院日期：默认为当天。
            /*var date = new Date();
            var month = date.getMonth() + 1;
            var ryrq = date.getFullYear() + "-" + (month < 10 ? '0' : '') + month + "-" + (date.getDate() < 10 ? '0' : '') + date.getDate()
            $("#ryrq").val(ryrq);*/

            this.popContent.brjbxxModel.ryrq = ryrq;
            //入院情况-一般，//支付类型：取参数，已增加参数
            this.popContent.ryqk = '3';
            //入院途径：门诊
            this.popContent.rytj = '2';
			
			this.popContent.med_type = '21';
            //身份证件类型
            this.popContent.brjbxxModel.sfzjlx = "01";
            //国籍
            this.popContent.brjbxxModel.brgj = '156';
            //民族
            // this.popContent.brjbxxModel.brmz = '01';
            //年龄单位
            this.popContent.nldw = "1";
            //保险病人
            this.popContent.bxbr = "0";
            //默认支付类型
            this.popContent.zflxbm = this.caqxContent.cs00700100114;
            //医疗卡类型默认
            if (this.caqxContent.N05002200163) {
                this.popContent.ylklx = this.caqxContent.N05002200163;
            } else {
                this.popContent.ylklx = '07';
            }
            //是否允许使用医疗卡
            if (this.caqxContent.cs00700100115) {
                this.ylkxx = false;//医疗卡信息输入恢复
            } else {
                this.ylkxx = true;//医疗卡信息输入恢复
            }
        },

        /*********************获取下拉框数据***********************/
        //页面加载时自动获取民族Dddw数据
        GetMzData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=mzbm", function (json) {
                if (json.a == 0) {
                    zd_enter.mzList = json.d.list;
                } else {
                    malert('民族列表查询失败', 'top', 'defeadted');
                    return false;
                }
            });
        },

        //页面加载时自动获取国籍Dddw数据
        GetGjData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=gjbm", function (json) {
                if (json.a == 0) {
                    zd_enter.gjList = json.d.list;
                    console.log(zd_enter.gjList);
                    zd_enter.popContent.brjbxxModel.brgj = '156';
                } else {
                    malert('国籍列表查询失败', 'top', 'defeadted');
                    return false;
                }
            });
        },

        //页面加载时自动获取婚姻状况Dddw数据
        GetHyzkData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=hyzk", function (json) {
                if (json.a == 0) {
                    zd_enter.hyzkList = json.d.list;
                    // zd_enter.popContent.brjbxxModel.hyzk = '20';
                } else {
                    malert('婚姻状况列表查询失败', 'top', 'defeadted');
                    return false;
                }
            });
        },

        //页面加载时自动获职业编码Dddw数据
        GetZybmData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zybm", function (json) {
                if (json.a == 0) {
                    zd_enter.zyList = json.d.list;
                } else {
                    malert('职业列表查询失败', 'top', 'defeadted');
                    return false;
                }
            });
        },

        //页面加载时自动获取保险类别Dddw数据
        GetBxlbData: function () {$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=bxlb", function (json) {
                if (json.a == 0) {
                    zd_enter.bxlbList = json.d.list;
                } else {
                    malert('保险类别列表查询失败', 'top', 'defeadted');
                }
            });
        },

        //页面加载时自动获取病人费别Dddw数据
        GetBrfbData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=brfb", function (json) {
                if (json.a == 0) {
                    zd_enter.brfbList = json.d.list;
                } else {
                    malert('病人费别列表查询失败', 'top', 'defeadted');
                }
            });
        },

        //页面加载时自动获取门诊科室Dddw数据
        GetGhksData: function () {
            var bean = {"ghks": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    zd_enter.mzksList = json.d.list;
                } else {
                    malert('门诊科室列表查询失败', 'top', 'defeadted');

                }
            });
        },

        //页面加载时自动获取住院科室Dddw数据
        GetZyksData: function () {
            var bean = {"zyks": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    zd_enter.zyksList = json.d.list;
                } else {
                    malert('住院科室列表查询失败', 'top', 'defeadted');
                }
            });
        },
        //页面加载时自动获取住院医生Dddw数据
        GetZyysData: function () {
            //首先判断权限是否允许输入
            var bean = {"ysbz": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    zd_enter.glzyysList = json.d.list;
                } else {
                    malert('住院医生列表查询失败', 'top', 'defeadted');

                }
            });
        },

        //页面加载时自动获取床位号Dddw数据
        GetCwhData: function () {
            var parm = {"ywbz": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=query&parm=" + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        zd_enter.glcwhList = json.d.list;
                    } else {
                        malert('床位号列表查询失败', 'top', 'defeadted');

                    }
                });
        },

        //页面加载时自动获取支付类型Dddw数据
        GetZflxData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zflx", function (json) {
                if (json.a == 0) {
                    zd_enter.zflxList = json.d.list;
                    //默认支付类型
                    zd_enter.popContent.zflxbm = zd_enter.caqxContent.cs00700100114;
                } else {
                    malert('支付类型列表查询失败', 'top', 'defeadted');

                }
            });
        },
        //证件类型
        GetzjlxData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&json={'zylb':'02'}", function (json) {
                if (json.a == 0) {
                    zd_enter.zjlxList = json.d.list;
                } else {
                    malert("身份证件类型下拉列表查询失败：" + json.c, 'top', 'defeadted');
                    return false;
                }
            });
        },

        //下拉框加载医疗卡类型
        ylklxselect: function () {
            this.param.rows = 20000;
            var json = {
                zylb: "01"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyzbm&dg=" + JSON.stringify(this.param) + "&json=" + JSON.stringify(json), function (json) {
                zd_enter.ylklxList = json.d.list;
                if (zd_enter.caqxContent.N05002200163) {
                    zd_enter.popContent.ylklx = zd_enter.caqxContent.N05002200163;
                    zd_enter.popContent.brjbxxModel.ylklx = zd_enter.caqxContent.N05002200163;
                } else {
                    zd_enter.popContent.ylklx = '07';
                    zd_enter.popContent.brjbxxModel.ylklx = '07';
                }
            });
        },

        //页面加载时自动获取联系人关系尚的Dddw数据
        GetLxrgxData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=lxrgx", function (json) {
                if (json.a == 0) {
                    zd_enter.lxrgxList = json.d.list;
                } else {
                    malert('联系人关系列表查询失败', 'top', 'defeadted');

                }
            });
        },
        //获取省
        shengselect: function () {
            this.param.rows = 20000;
            var json = {
                xzqhlx: "1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&dg=" + JSON.stringify(this.param) + "&json=" + JSON.stringify(json), function (json) {
                zd_enter.provinceList = json.d.list;
                zd_enter.provinceList1 = json.d.list;

                var shengbm = zd_enter.caqxContent.cs00400100217;
                if (!zd_enter.popContent.brjbxxModel.jzdsheng && shengbm != null) {
                    for (var i = 0; i < json.d.list.length; i++) {
                        if (shengbm == json.d.list[i].xzqhbm) {
                            zd_enter.popContent.brjbxxModel.jzdsheng = shengbm;
                            zd_enter.popContent.brjbxxModel.jzdshengmc = json.d.list[i].xzqhmc;

                        }
                    }
                    zd_enter.shiselect('2', shengbm);
                }

            });
        },
        //获取市、县-居住地
        shiselect: function (lx, sjbm) {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"' + lx + '","sjbm":"' + sjbm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                if (json.a == 0) {
                    if (lx == 2) {
                        //市
                        zd_enter.cityList = [];
                        zd_enter.cityList = json.d.list;
                        var shibm = zd_enter.caqxContent.cs00400100218;
                        if (!zd_enter.popContent.brjbxxModel.jzdshi && shibm != null) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                if (shibm == json.d.list[i].xzqhbm) {
                                    zd_enter.popContent.brjbxxModel.jzdshi = shibm;
                                    zd_enter.popContent.brjbxxModel.jzdshimc = json.d.list[i].xzqhmc;

                                }
                            }
                            zd_enter.shiselect('3', shibm);
                        }
                    }
                    if (lx == 3) {
                        //县
                        zd_enter.countyList = [];
                        zd_enter.countyList = json.d.list;
                        var xianbm = zd_enter.caqxContent.cs00400100219;
                        if (!zd_enter.popContent.brjbxxModel.jzdxian && xianbm != null) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                if (xianbm == json.d.list[i].xzqhbm) {
                                    zd_enter.popContent.brjbxxModel.jzdxian = xianbm;
                                    zd_enter.popContent.brjbxxModel.jzdxianmc = json.d.list[i].xzqhmc;

                                }
                            }
                        }
                    }
                } else {
                    malert("市、县编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                    return false;
                }
            });
        },
        //出生地
        csdshiselect: function (lx, sjbm) {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"' + lx + '","sjbm":"' + sjbm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                if (json.a == 0) {
                    if (lx == 2) {
                        //市
                        zd_enter.cityList1 = [];
                        zd_enter.cityList1 = json.d.list;
                    }
                    if (lx == 3) {
                        //县
                        zd_enter.countyList1 = [];
                        zd_enter.countyList1 = json.d.list;
                    }
                } else {
                    malert("市、县编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                    return false;
                }
            });
        },
        //页面加载时自动获取票据号数据
        GetPjhData: function () {
            var str_parm = {
                pjlx: "05"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=PjhInfo&types=getpj&parm=" + JSON.stringify(str_parm), function (json) {
                if (json.a == 0) {
                    if (json.d != null && json.d != "") {
                        dqsyh = json.d[0].dqsyh;
                        $("#fphm").val(dqsyh);
                        $("#fpsyzs").val(json.d[0].fpzs);
                    } else {
                        malert('请先进行领票操作', 'top', 'defeadted');
                    }
                } else {
                    malert('发票信息查询失败', 'top', 'defeadted');

                }
            });
        },
        //根据传入参数获取省市县名称
        getSSX: function (val, list, num) {
            this.param.rows = 20000;
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"' + num + '","sjbm":"' + val + '"}&dg=' + JSON.stringify(this.param), function (json) {
                if (json.a == 0) {
                    zd_enter[list] = [];
                    zd_enter[list] = json.d.list;
                } else {
                    malert("省市县编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                    return false;
                }
            });
        },

        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "brxb"://病人性别
                    Vue.set(this.popContent.brjbxxModel, 'brxb', val[0]);
                    break;
                case "sfzjlx"://身份证件类型
                    Vue.set(this.popContent.brjbxxModel, 'sfzjlx', val[0]);
                    break;
                case "brgj"://国籍
                    Vue.set(this.popContent.brjbxxModel, 'brgj', val[0]);
                    break;
                case "brmz"://民族
                    Vue.set(this.popContent.brjbxxModel, 'brmz', val[0]);
                    break;
                case "hyzk"://婚姻状况
                    Vue.set(this.popContent.brjbxxModel, 'hyzk', val[0]);
                    break;
                case "zybm"://职业
                    Vue.set(this.popContent.brjbxxModel, 'zybm', val[0]);
                    break;
                case "hzlx"://婚姻状况
                    Vue.set(this.popContent.brjbxxModel, 'hzlx', val[0]);
                    break;
                case "lxrgx"://联系人关系
                    Vue.set(this.popContent.brjbxxModel, 'lxrgx', val[0]);
                    break;
                case "mzys"://门诊医生
                    //获取门诊医生的值
                    Vue.set(this.popContent, 'mzys', val[0]);
                    Vue.set(this.popContent, 'mzysxm', val[4]);
                    //过滤以后获取到当前选中门诊医生对象
                    this.glmzysList = jsonFilter(this.mzysList, "rybm", this.popContent.mzys);
                    //给门诊科室赋初始值
                    this.popContent['mzks'] = this.glmzysList[0]['ksbm'];
                    break;
                case "ryks"    :
                    //先获取住院医生的值
                    Vue.set(this.popContent, 'ryks', val[0]);
                    Vue.set(this.popContent, 'ryksmc', val[4]);
                    //先清空
                    this.zyysList = [];
                    this.cwhList = [];
                    //过滤住院医生
                    this.zyysList = jsonFilter(this.glzyysList, "ksbm", this.popContent.ryks);
                    //过滤床位号
                    this.cwhList = jsonFilter(this.glcwhList, "ksbm", this.popContent.ryks);
                    break;
                case "brfb"://病人费别
                    Vue.set(this.popContent, 'brfb', val[0]);
                    Vue.set(this.popContent, 'brfbmc', val[4]);
                    for (var i = 0; i < this.brfbList.length; i++) {
                        if (val[0] == this.brfbList[i]['fbbm']) {
                            Vue.set(this.popContent, 'bxlbbm', this.brfbList[i]['bxlbbm']);
                            this.nextFocus(val[1], 2);
                            isTwo = true;
                        }
                    }
                    if (val[0] == "01") {
                        Vue.set(this.popContent, 'bxbr', '0');
                    } else {
                        Vue.set(this.popContent, 'bxbr', '1');
                    }
                    break;
                case "memberId"://病人姓名
                    var nhbrxx = {};
                    for (var i = 0; i < zd_enter.nhbrxxList.length; i++) {
                        if (zd_enter.nhbrxxList[i].memberId == val[0]) {
                            nhbrxx = zd_enter.nhbrxxList[i];
                        }
                    }
                    if (nhbrxx != null) {
                        //不为空进行赋值操作
                        Vue.set(this.popContent, 'memberId', val[0]);
                        this.popContent.brjbxxModel.brxm = val[4];
                        this.setPYDM(this.popContent.brjbxxModel.brxm, 'popContent', 'pydm');
                        this.popContent.wbjm = this.setWBJM(this.popContent.brjbxxModel.brxm);
                        this.popContent.brjbxxModel.brxb = nhbrxx.memberSex;
                        this.popContent.birthday = nhbrxx.birthday;
                        this.popContent.brjbxxModel.sfzjhm = nhbrxx.idcard;
                        this.popContent.brjbxxModel.jzdmc = nhbrxx.areaName;
                        //身份证号存在则生成出年龄并且查询后台
                        /*if(nhbrxx.idcard!=null){
                            zd_enter.sfzjh();
                        }*/
                    }
                    break;
                case "jzdsheng"://居住地省
                    Vue.set(this.popContent.brjbxxModel, "jzdsheng", val[0]);
                    Vue.set(this.popContent.brjbxxModel, "jzdshengmc", val[4]);
                    if (this.jzdmcType == 0) {
                        this.popContent.brjbxxModel.jzdmc = (this.popContent.brjbxxModel.jzdshengmc || '') + (this.popContent.brjbxxModel.jzdshimc || '') + (this.popContent.brjbxxModel.jzdxianmc || '');
                    }
                    this.countyList = [];
                    this.shiselect(2, val[0]);
                    break;
                case "jzdshi"://居住地市
                    Vue.set(this.popContent.brjbxxModel, "jzdshi", val[0]);
                    Vue.set(this.popContent.brjbxxModel, "jzdshimc", val[4]);
                    if (this.jzdmcType == 0) {
                        this.popContent.brjbxxModel.jzdmc = (this.popContent.brjbxxModel.jzdshengmc || '') + (this.popContent.brjbxxModel.jzdshimc || '') + (this.popContent.brjbxxModel.jzdxianmc || '');
                    }
                    this.shiselect(3, val[0]);
                    break;
                case "jzdxian"://居住地县
                    //if(this.json.jzdxian==null){
                    Vue.set(this.popContent.brjbxxModel, "jzdxian", val[0]);
                    Vue.set(this.popContent.brjbxxModel, "jzdxianmc", val[4]);
                    //}
                    if (this.jzdmcType == 0) {
                        this.popContent.brjbxxModel.jzdmc = (this.popContent.brjbxxModel.jzdshengmc || '') + (this.popContent.brjbxxModel.jzdshimc || '') + (this.popContent.brjbxxModel.jzdxianmc || '');
                    }
                    break;
                case "csdsheng"://出生地省
                    Vue.set(this.popContent.brjbxxModel, "csdsheng", val[0]);
                    Vue.set(this.popContent.brjbxxModel, "csdshengmc", val[4]);
                    this.popContent.brjbxxModel.csd = (this.popContent.brjbxxModel.csdshengmc || "") + (this.popContent.brjbxxModel.csdshimc || "") + (this.popContent.brjbxxModel.csdxianmc || "");
                    this.countyList1 = [];
                    this.csdshiselect(2, val[0]);
                    break;
                case "csdshi"://居住地市
                    Vue.set(this.popContent.brjbxxModel, "csdshi", val[0]);
                    Vue.set(this.popContent.brjbxxModel, "csdshimc", val[4]);
                    this.popContent.brjbxxModel.csd = (this.popContent.brjbxxModel.csdshengmc || "") + (this.popContent.brjbxxModel.csdshimc || "") + (this.popContent.brjbxxModel.csdxianmc || "");
                    this.csdshiselect(3, val[0]);
                    break;
                case "csdxian"://居住地县
                    //if(this.json.jzdxian==null){
                    Vue.set(this.popContent.brjbxxModel, "csdxian", val[0]);
                    Vue.set(this.popContent.brjbxxModel, "csdxianmc", val[4]);
                    //}
                    this.popContent.brjbxxModel.csd = (this.popContent.brjbxxModel.csdshengmc || "") + (this.popContent.brjbxxModel.csdshimc || "") + (this.popContent.brjbxxModel.csdxianmc || "");
                    break;
                case "nldw"://年龄单位 @yqq
                    if (this.popContent.nldw2 == val[0]) {
                        Vue.set(this.popContent, 'nldw', '');
                        Vue.set(this.popContent.brjbxxModel, 'nldw', '');
                        malert("年龄单位与月龄单位不能相同，请检查！", 'top', 'defeadted');
                        return;
                    }
                    Vue.set(this.popContent, 'nldw', val[0]);
                    Vue.set(this.popContent.brjbxxModel, 'nldw', val[0]);
                    zd_enter.setCsrq(null);
                    this.$forceUpdate();
                    break;
                case "nldw2"://年龄单位2
                    if (this.popContent.nldw == val[0]) {
                        Vue.set(this.popContent, 'nldw2', '');
                        Vue.set(this.popContent.brjbxxModel, 'nldw2', '');
                        malert("年龄单位与月龄单位不能相同，请检查！", 'top', 'defeadted');
                        return;
                    }
                    Vue.set(this.popContent, 'nldw2', val[0]);
                    Vue.set(this.popContent.brjbxxModel, 'nldw2', val[0]);
                    this.$forceUpdate();
                    break;
                case "mtbbz"://门特病标志
                    Vue.set(this.popContent.brjbxxModel, 'mtbbz', val[0]);
                    break;
                default:
                    break;
            }
            //方便更新试图（添加set。get方法）
            this.popContent = Object.assign({}, this.popContent);
            //回车跳转
            if (val[1] != null && !isTwo) {
                this.nextFocus(val[1]);
            }
        },
        saveData: function () {
            if (this.popContent.brjbxxModel.brid && this.popContent.brjbxxModel.brid == this.popContentTmp.brjbxxModel.brid) {
                var tip = '';
                if (this.popContent.brjbxxModel.brxm != this.popContentTmp.brjbxxModel.brxm) {
                    tip += "原姓名【" + this.popContentTmp.brjbxxModel.brxm + "】> 【" + this.popContent.brjbxxModel.brxm + "】</br>";
                }
                if (this.popContent.brjbxxModel.sfzjhm != this.popContentTmp.brjbxxModel.sfzjhm) {
                    tip += "原证件号【" + this.popContentTmp.brjbxxModel.sfzjhm + "】> 【" + this.popContent.brjbxxModel.sfzjhm + "】";
                }
                if (tip) {
                    common.openConfirm("是否修改病人基本信息</br>" + tip, function () {
                        zd_enter.saveData1();
                    }, function () {
                        zd_enter.popContent.brjbxxModel.brxm = zd_enter.popContentTmp.brjbxxModel.brxm;
                        zd_enter.popContent.brjbxxModel.sfzjhm = zd_enter.popContentTmp.brjbxxModel.sfzjhm;
                        zd_enter.saveData1();
                    }, {title: '基本信息修改提示', cb: '不修改保存', sb: '修改保存'});
                } else {
                    this.saveData1();
                }
            } else {
                this.saveData1();
            }
        },
        //保存
        saveData1: function () {
            console.log(this.popContent);
            if (!this.ifClick) return; //如果为false表示已经点击了不能再点
            this.ifClick = false;
            // 判断身份证号码
            if (this.popContent.brjbxxModel.sfzjlx == '01') {
                if (this.popContent.brjbxxModel.sfzjhm) {
                    if (this.popContent.brjbxxModel.sfzjhm.length != 18) {
                        malert("身份证件号码长度非法！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                }
            }

            if (!dqsyh) {
                this.ifClick = true;
                malert('请先领票再进行操作', 'top', 'defeadted');
                return;
            }
            // 提交前验证数据(非空)
            if (!this.empty_sub('contextInfo')) {
                this.ifClick = true;
                return false;
            }

            //获取病人基本信息
            /*this.popContent.ryrq = $("#ryrq")[0].value;//获取入院日期*/
            this.popContent.ryrq = this.fDate(this.popContent.brjbxxModel.ryrq, "datetime");
            this.popContent.fphm = $("#fphm").text(); //获取发票号码
            this.popContent.brjbxxModel.pydm = this.popContent.pydm;
            this.popContent.brjbxxModel.wbjm = this.popContent.wbjm;
            if (!this.popContent.brjbxxModel.brxm) {
                Vue.set(this.popContent.brjbxxModel, 'brxm', this.brxxContent.text);
            }
            //处理默认值取name问题
            this.popContent.brjbxxModel.brgjmc = this.listGetName(this.gjList, this.popContent.brjbxxModel.brgj, 'gjbm', 'gjmc');
            this.popContent.brjbxxModel.brmzmc = this.listGetName(this.mzList, this.popContent.brjbxxModel.brmz, 'mzbm', 'mzmc');
            this.popContent.brjbxxModel.hyzkmc = this.listGetName(this.hyzkList, this.popContent.brjbxxModel.hyzk, 'hyzkbm', 'hyzkmc');
            this.popContent.brjbxxModel.zybmmc = this.listGetName(this.zyList, this.popContent.brjbxxModel.zybm, 'zybm', 'zymc');
            this.popContent.zflxmc = this.listGetName(this.zflxList, this.popContent.zflxbm, 'zflxbm', 'zflxmc');
            this.popContent.bxlbmc = this.listGetName(this.bxlbList, this.popContent.bxlbbm, 'bxlbbm', 'bxlbmc');
            this.popContent.ryksmc = this.listGetName(this.zyksList, this.popContent.ryks, 'ksbm', 'ksmc');
            this.popContent.zyysxm = this.listGetName(this.zyysList, this.popContent.zyys, 'rybm', 'ryxm');
            this.popContent.rycwbh = this.listGetName(this.cwhList, this.popContent.rycwid, 'cwid', 'cwbh');
            // zd_enter.popContent.mzysxm = zd_enter.listGetName(zd_enter.mzysList, zd_enter.popContent.mzys, 'rybm', 'ryxm');
            this.popContent.mzksmc = zd_enter.listGetName(this.mzksList, this.popContent.mzks, 'ksbm', 'ksmc');
            var brrytj = this.popContent.rytj;//入院途径
            var sjhm = this.popContent.brjbxxModel.sjhm;//手机号码
            var lxrdh = this.popContent.brjbxxModel.lxrdh;//联系人电话号码
            //验证保险卡长度
            var regPos = "/ ^\d+$/";// 非负整数
            var cardlen = this.listGetName(this.bxlbList, this.popContent.bxlbbm, 'bxlbbm', 'cardlen');
            if (!cardlen == false && !this.popContent.ybkh == false && parseInt(cardlen) != this.popContent.ybkh.length) {
                malert('保险卡长度非法，应该为[' + cardlen + ']位!', 'top', 'defeadted');
                this.ifClick = true;
                return false;
            }
			
			if(!sjhm){
				malert('手机号码不能为空!', 'top', 'defeadted');
				this.ifClick = true;
				return false;
			}
			
            //验证电话号码
            if (brrytj != "1") {//非急诊入院都需要判断是否填写了电话号码
                if (zd_enter.caqxContent.cs05002200160 == '1') {//1表示电话号码必填,0表示非必填
                    if (lxrdh == null && sjhm == null) {
                        malert('手机号码和联系人电话号码不能都为空!', 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    if (sjhm) {
                        if (sjhm.length > 11) {
                            malert("手机号长度超长！", 'top', 'defeadted');
                            this.ifClick = true;
                            return false;
                        }
                    }
                    if (lxrdh) {
                        if (lxrdh.length > 11) {
                            malert("联系人电话长度超长！", 'top', 'defeadted');
                            this.ifClick = true;
                            return false;
                        }
                    }
                }
            }
            //数据验证
            if (!this.ry_DataValid()) {
                this.ifClick = true;
                return;
            }
            malert("正在入院登记，请稍后....", 'top', 'success');
            if (zd_enter.ismzjr == '1') {
                this.popContent.ghxh = zd_enter._ghxh;
            }
            this.popContent.brjbxxModel.brjbxxModel = null;
            this.popContent.mtbbz = this.popContent.brjbxxModel.mtbbz;
            this.popContent.djbh = this.popContent.brjbxxModel.djbh;
            this.popContent.brjbxxModel.brxmgsuse= this.popContent.brjbxxModel.brxm;
            this.popContent.brjbxxModel.brmzuse= this.popContent.brjbxxModel.brmz;
            this.popContent.brjbxxModel.csrquse= this.popContent.brjbxxModel.csrq;
            this.popContent.brjbxxModel.rylbuse= this.popContent.brjbxxModel.rylb;
            //电子健康卡注册
            if (zd_enter.caqxContent.N05002200162 && (zd_enter.popContent.brjbxxModel.ylklx == '011' || zd_enter.popContent.ylklx == '011')) {
                if (zd_enter.popContent.brjbxxModel.sfzjhm) {//有身份证才进入
                    //查询是否本地有健康卡信息
                    if (zd_enter.popContent.brjbxxModel.brid) {
                        this.updatedAjax(zd_enter.caqxContent.N05002200162 + "/jsgapi/healthCard/queryCardByBrid?brid=" + zd_enter.popContent.brjbxxModel.brid + "&yljgbm=" + jgbm,
                            function (data) {
                                if (data.returnCode == "0" && data.outResult) {//本地没有就查询注册
                                    var outResult = JSON.parse(data.outResult);
                                    zd_enter.popContent.brjbxxModel.ylkh = outResult.YLKH;
                                    zd_enter.popContent.ylkh = outResult.YLKH;
                                    zd_enter.popContent.ylklx = "011";
                                    zd_enter.popContent.brjbxxModel.ylklx = "011";
                                } else {
                                    zd_enter.healthCardRegister();
                                }
                            }, function (error) {
                                console.log("查询是否本地有健康卡信息错误！" + error)
                            });
                    } else {//新入院病人，查询注册
                        zd_enter.healthCardRegister();
                    }
                }
            }

            this.popContent.gbxtbz = '0';
            this.popContent.isvirtry = '1';
            this.popContent.sfqj = '0'; //是否抢救
            this.popContent.ghxh = this.ghxh;
            var json = JSON.stringify(this.popContent);
			yibao_panel.popContent = JSON.parse(json);
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=save', json).then(function (data) {
                if (data.body.a == 0 && data.body.d != null) {
                    zd_enter.printList = data.body.d
                    // malert(data.body.c, 'top', 'success');
                    //新加代码：入院登记/修改入院患者信息成功后需要调用护理接口上传数据
                    // if (window.top.J_tabLeft.obj.hljkurl != null && window.top.J_tabLeft.obj.hljkurl != undefined) {
                    //     //判断是入院登记还是修改患者
                    //     var method = '';
                    //     if (this.popContent.zyh != null && this.popContent.zyh != undefined) {//说明是修改患者
                    //         method = 'ADT_A08';
                    //     } else {
                    //         method = 'ADT_A01';
                    //     }
                    //
                    //     var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                    //     var inJson = {
                    //         yljgbm: jgbm,
                    //         zyh: data.body.d.zyh
                    //     }
                    //     var params = {
                    //         yljgbm: jgbm,
                    //         zyh: data.body.d.zyh,
                    //         types: 'hzxx',
                    //         method: method,
                    //         inJson: JSON.stringify(inJson)
                    //     }
                    //     this.postAjax(hljkurl, JSON.stringify(params), function (result) {
                    //         if (result.code == 0) {
                    //             console.log("护理接口调取成功!");
                    //         } else {
                    //             console.log("护理接口调取失败:" + result.msgInfo);
                    //             console.log("护理接口调取失败的住院号为:" + params.zyh)
                    //         }
                    //     });
                    // }
                    // if (zd_enter.caqxContent.N05002200161) {//药监平台
                    //     var aa = zd_enter.popContent;
                    //     var jzryxx;
                    //     if (data.body.d['brjbxxModel']) {
                    //         jzryxx = {
                    //             "birth_date": this.fDate(data.body.d['brjbxxModel']['csrq'], 'date'),//出生日期,
                    //             "carte_vital_id": "",
                    //             "gender_code": data.body.d['brjbxxModel']['brxb'],//病人性别
                    //             "health_card_no": "",
                    //             "health_file_no": "",
                    //             "idcard": data.body.d['brjbxxModel']['sfzjhm'],//身份证件号码
                    //             "idcard_code": data.body.d['brjbxxModel']['sfzjlx'],//身份证件类型
                    //             "inpatient_no": data.body.d['zyh'],
                    //             "is_filing_card_poor": "2",//贫困户标志
                    //             "name": data.body.d['brjbxxModel']['brxm'],//病人姓名
                    //             "outpatient_no": "",//门诊号
                    //             "patient_local_id": data.body.d['brjbxxModel']['brid'],//本地唯一标志
                    //             "phone_number": data.body.d['brjbxxModel']['sjhm'],//手机
                    //         };
                    //     } else {
                    //         jzryxx = {
                    //             "birth_date": this.fDate(zd_enter.popContent['brjbxxModel']['csrq'], 'date'),//出生日期,
                    //             "carte_vital_id": "",
                    //             "gender_code": zd_enter.popContent['brjbxxModel']['brxb'],//病人性别
                    //             "health_card_no": "",
                    //             "health_file_no": "",
                    //             "idcard": zd_enter.popContent['brjbxxModel']['sfzjhm'],//身份证件号码
                    //             "idcard_code": zd_enter.popContent['brjbxxModel']['sfzjlx'],//身份证件类型
                    //             "inpatient_no": data.body.d['zyh'],
                    //             "is_filing_card_poor": "2",//贫困户标志
                    //             "name": data.body.d['brxm'],//病人姓名
                    //             "outpatient_no": "",//门诊号
                    //             "patient_local_id": data.body.d['brid'],//本地唯一标志
                    //             "phone_number": zd_enter.popContent['brjbxxModel']['sjhm'],//手机
                    //         };
                    //     }
                    //     // 调取贵州省医药监管平台就诊人员提醒接口
                    //     this.postAjax(zd_enter.caqxContent.N05002200161 + 'patientRemend', JSON.stringify(jzryxx), function (json) {
                    //         if (json.success == 'T') {
                    //             malert(json.error_msg, 'top', 'success');
                    //         } else {
                    //             malert(json.error_msg, 'top', 'defeadted');
                    //         }
                    //     }, function (e) {
                    //         malert("监管平台已经断开连接，请联系管理员！", 'top', 'defeadted');
                    //     });
                    // }

                    malert(data.body.c, 'top', 'success');
                    console.log(zd_enter.popContent);
                    zd_enter.hzsearch = '';
                    zd_enter.zyh = data.body.d
                    if (zd_enter.popContent.zyh == null) {
                        // var sendmsg = {
                        //     msgtype: '1',
                        //     ksbm: zd_enter.popContent.ryks,
                        //     yljgbm: jgbm,
                        //     yqbm: yqbm,
                        //     msg: '有待接科科病人：' + zd_enter.popContent.brjbxxModel.brxm,
                        //     toaddress: 'page/hsz/hlyw/bygl/bygl.html',
                        //     sbid: zd_enter.popContent.brjbxxModel.sfzjhm + "_" + zd_enter.fDate(new Date(), 'YY'),
                        //     ylbm: 'N030042001',
                        //     pagename: "病员管理"
                        // };
                        // console.log(sendmsg);
                        // window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                    }
                    // var zyh = data.body.d["zyh"];
                    // if (zd_enter.sfcr == '0') {
                    //     var strPrint = [{reportlet: 'fpdy%2Fzygl%2Fzygl_xsrwddy.cpt', zyh: zd_enter.zyh}];
                    //     zd_enter.frPrint(strPrint, 'N010024009')
                    //
                    // }
                    // // else{
                    // //     var bm= "N010024008",strPrint=[{reportlet: 'fpdy%2Fzygl%2Fzygl_wddy.cpt',zyh:zd_enter.zyh}]
                    // // }
                    // //如果有预交金额就应打印预交发票
                    // if (zd_enter.popContent.yjje != null && zd_enter.popContent.yjje > 0) {
                    //     //结院预交票打印机参数
                    //     var fphm = data.body.d["fphm"];
                    //     // var zyh = data.body.d["zyh"];
                    //     var frpath = "";
                    //     if (window.top.J_tabLeft.obj.frprintver == "3") {
                    //         frpath = "%2F";
                    //     } else {
                    //         frpath = "/";
                    //     }
                    //     var reportlets = "[{reportlet: 'fpdy" + frpath + "zygl" + frpath + "zygl_zyyjjl.cpt',yljgbm:'" + jgbm + "',zyh:'" + zyh + "',fphm:'" + fphm + "'}]";
                    //     zd_enter.frPrint(reportlets, 'N010024005')
                    //     zd_enter.ifClick = true;
                    //     // $("#startInput").find("input").focus();
                    //     zd_enter.ylkh = null;
                    //     $("#ykth").focus();
                    //     zd_enter.GetPjhData();
                    // }

                    var bxjk = zd_enter.listGetName(zd_enter.bxlbList, zd_enter.popContent.bxlbbm, 'bxlbbm', 'bxjk');
                    var bxjkUrl = zd_enter.listGetName(zd_enter.bxlbList, zd_enter.popContent.bxlbbm, 'bxlbbm', 'url');
                    
                    if (zd_enter.caqxContent.N05002200165) {
                        if (bxjk == '001') {
                            zd_enter.nhdj = true;
                            zd_enter.$nextTick(function () {
                                loadOpenPage('nhYbPage/nhPage/nh');
                            })
                        } else if (bxjk == '002') {
                            zd_enter.nhdj = true;
                            zd_enter.$nextTick(function () {
                                loadOpenPage('nhYbPage/ybPage/yb');
                            })
                        } else if (bxjk == '008') {
                            zd_enter.nhdj = true;
                            zd_enter.$nextTick(function () {
                                loadOpenPage('nhYbPage/008yndryb/ybdj');
                            })
                        } else if (bxjk == 'B07') {
                            zd_enter.$nextTick(function () {
                                yibao_panel.title = '医保患者请完成住院登记';
                                yibao_panel.isShowYibaoPage = true;
                                yibao_panel.isYibaoDengJi = true;
                                yibao_panel.close = true;
                                $('#loadYibaoPage').load('/newzui/page/zygl/rcygl/rydj/014cdyhybgb/014cdyhyb.html');
                            })
                        }else if (bxjk == '015') {
                            zd_enter.$nextTick(function () {
                                                                yibao_panel.title = '医保患者请完成住院登记';
                                yibao_panel.isShowYibaoPage = true;
                                yibao_panel.isYibaoDengJi = true;
                                yibao_panel.close = true;
                                $('#loadYibaoPage').load('/newzui/page/zygl/rcygl/rydj/014cdyhybgb/014cdyhybgs.html');
                            })
                        }
                        else {
                            zd_enter.refresh();//调用刷新按钮
                        }
                    } else {
                        zd_enter.refresh();//调用刷新按钮
                    }


                    //滞空患者姓名将不能重复提交相同的患者住院信息  @yqq
                    zd_enter.popContent.brjbxxModel.brxm = '';
                    zd_enter.$forceUpdate()
                    zd_enter.ifClick = true;
                } else {
                    // if(data.body.a == 0){
                    // 	zd_enter.nhdj = true;
                    // 	zd_enter.$nextTick(function () {
                    // 		yibao_panel.isShowYibaoPage = true;
                    // 		yibao_panel.isYibaoDengJi = true;
                    // 		$('#loadYibaoPage').load('../../../CommonPage/insurancePort/014cdyhyb/014cdyhyb.html');
                    // 	})
                    // }
                    malert(data.body.c, 'top', 'defeadted');
                    zd_enter.ifClick = true;
                }
                zd_enter.ylkh = null;
                zd_enter.pkhbz = '0';
                $("#ykth").focus();
            }, function (error) {
                console.log(error);
                zd_enter.ifClick = true;
            });
        },
        //注册电子健康卡
        healthCardRegister: function () {
            //查询平台
            var req = {
                idType: zd_enter.popContent.brjbxxModel.sfzjlx,//证件类型 ->String(2)
                idNo: zd_enter.popContent.brjbxxModel.sfzjhm,//证件号 【可根据电子健康卡ID或证件信息组合查询】-->String(32)
            };
            var inParm = {
                yljgbm: jgbm,
                czybm: userId,
                inJson: JSON.stringify(req),
                medstepcode: '010101',//诊疗环节代码 【见字典】->String(12)必填 默认挂号
                ksbm: zd_enter.popContent.ryks,
            };
            zd_enter.postAjax(zd_enter.caqxContent.N05002200162 + "/jsgapi/healthCard/query",
                JSON.stringify(inParm), function (data) {
                    if (data.returnCode == "0" && data.outResult) {
                        var outResult = JSON.parse(JSON.parse(data.outResult).bizContent);
                        zd_enter.popContent.brjbxxModel.ylkh = outResult.ehealthCardId;//	电子健康卡ID
                    } else {
                        //注册
                        var req = {
                            registerNo: "RYDJ" + new Date().getTime(), //->String(32)必填
                            registerTime: new Date().getTime() + "",    //->Long(32)必填（毫秒）
                            applyType: '2', //申请方式：1 手机申请 2 机构服务窗口 3	机构自助终端
                            idType: zd_enter.popContent.brjbxxModel.sfzjlx,//证件类型->String(2)必填
                            idNo: zd_enter.popContent.brjbxxModel.sfzjhm,//证件号->String(32)必填
                            name: zd_enter.popContent.brjbxxModel.brxm,//用户姓名->String(50)  必填
                            gender: zd_enter.popContent.brjbxxModel.brxb,//用户性别->String(1)必填
                            birthday: new Date(zd_enter.popContent.brjbxxModel.csrq).getTime() + "",//出生日期->Long(20) （年月日，毫秒)必填
                            cellphone: zd_enter.popContent.brjbxxModel.sjhm,//手机号码->String(32)
                            telephone: zd_enter.popContent.brjbxxModel.sjhm,//联系电话->String(32)
                            address: zd_enter.popContent.brjbxxModel.jzdmc,//居住地址->String(200)
                        };
                        var inParm = {
                            czybm: userId,
                            yljgbm: jgbm,
                            inJson: JSON.stringify(req),
                            medstepcode: '010101',//诊疗环节代码 【见字典】->String(12)必填 默认挂号
                            ksbm: zd_enter.popContent.ryks,
                        };
                        if (!zd_enter.caqxContent.N05002200162) {
                            console.log("接口地址无效，请维护参数【N05002200162】为健康卡程序地址！");
                        } else {
                            zd_enter.postAjax(zd_enter.caqxContent.N05002200162 + "/jsgapi/healthCard/register",
                                JSON.stringify(inParm), function (data) {
                                    if (data.returnCode == "0") {
                                        var outResult = JSON.parse(JSON.parse(data.outResult).bizContent);
                                        zd_enter.popContent.brjbxxModel.ylkh = outResult.ehealthCardId;
                                        zd_enter.popContent.ylkh = outResult.ehealthCardId;
                                        zd_enter.popContent.ylklx = "011";
                                        zd_enter.popContent.brjbxxModel.ylklx = "011";
                                        console.log(data.msgInfo);
                                    } else {
                                        console.log(data.msgInfo);
                                        zd_enter.popContent.brjbxxModel.ylklx = "07";
                                        zd_enter.popContent.ylklx = "07";
                                    }
                                }, function (error) {
                                    console.log("【健康卡注册错误】：");
                                    zd_enter.popContent.brjbxxModel.ylklx = "07";
                                    zd_enter.popContent.ylklx = "07";
                                });
                        }
                    }
                }, function (error) {
                    console.log("【健康卡查询错误】" + error);
                });
        },
        quxiao: function () {
            this.topNewPage('接诊管理', 'xmzysz/mzys/jzgl/jzgl.html');
        },
        nhdjSave: function () {
            var bxjk = zd_enter.listGetName(zd_enter.bxlbList, zd_enter.popContent.bxlbbm, 'bxlbbm', 'bxjk');

            if (bxjk == '001') { //贵州移动农合
                hyjl.edit()
            } else if (bxjk == '002') { //贵州五险合一
                toolMenu.ybry()
            } else if (bxjk == '008') { //云南东软医保
                ybdj.ybrydj()
            }

        },
        nhdjClose: function () {
            this.nhdj = false;
            this.refresh();//调用刷新按钮
        },
        frPrint: function (printData, bm) {
            if (window.top.J_tabLeft.obj.FRorWindow) {
                zd_enter.print();
                return false
            }
            window.top.J_tabLeft.csqxparm.csbm = bm;
            var ZyyjPrint = null;
            //打印方法调整致取参数之后
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0) {
                    console.log(json.d);
                    if (json.d != null && json.d != undefined && json.d.length > 0) {
                        ZyyjPrint = json.d[0].csz;
                    }
                    console.log(printData);
                    if (!FrPrint(printData, ZyyjPrint)) {
                        zd_enter.print();

                    }
                } else {
                    if (json.body != undefined) {
                        zd_enter.print();
                    }
                }
            });
        },
        queryDetail: function (param) {
            let data = false;
            let bxUrl = zd_enter.listGetName(zd_enter.bxlbList, zd_enter.popContent.bxlbbm, 'bxlbbm', 'url');
            let query_url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + bxUrl + "&bxlbbm=" + zd_enter.popContent.bxlbbm + "&types=inhospital&method=queryInHospitalDetail&parm=";
            $.fn.getJSONAjax(query_url, param, function (res) {
                if (res.a != '0') {
                    malert(res.c, "top", "defeadted");
                }
                data = JSON.parse(res.d);
            });
            return data;
        },
        deleteyz: function () {
            //请求后台取消入院（已在后台判断了是否产生了费用）
                        let json = '{"list":' + JSON.stringify(this.rydjList) + '}';
            $.ajaxSettings.async = false;
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=delete', json).then(function (data) {
                if (data.body.a == 0) {
                    let bxjk = zd_enter.listGetName(zd_enter.bxlbList, zd_enter.popContent.bxlbbm, 'bxlbbm', 'bxjk');
                    let bxjkUrl = zd_enter.listGetName(zd_enter.bxlbList, zd_enter.popContent.bxlbbm, 'bxlbbm', 'url');
                    if (bxjk != 'B07') {
                        malert("取消入院成功。", 'top', 'success');
                        zd_enter.ifClick = true;
						zd_enter.refresh();
                        return true;
                    }

                    let param = {
                        'ykc010': zd_enter.popContent.zyh
                    }
                    let data = this.queryDetail(param);
                    if (!data) return false;
                    if (data.ztName =='未登记') {//医保已是取消状态，直接返回成功
                        malert("取消入院成功。", 'top', 'success');
						zd_enter.refresh();
						zd_enter.ifClick = true;
                        return true;
                    }

                    let param2404={
                        data : {
                            mdtrt_id:data.akc190,
                            psn_no : data.aac001,

                        }
                    }
                    let data2 = window.insuranceGbUtils.call1("2404",param2404,data.cbdqh,data.ydbz)
                    if(data2){
                        let upDjxx={
                            zyh:data.zyh,
                            jzid:data.akc190,
                            zfbz:'1',
                            zfry:userName,
                            zfrq:zd_enter.fDate(new Date,"AllDate"),
                        }

                        let bxUrl = zd_enter.listGetName(zd_enter.bxlbList, zd_enter.popContent.bxlbbm, 'bxlbbm', 'url');

                        let url1 = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + bxUrl + "&bxlbbm=" + zd_enter.popContent.bxlbbm + "&types=gbrydj&method=upDjxx&parm=";
                        $.fn.getJSONAjax(url1, upDjxx, function (res) {
                            if (res.a == 0) {
                                malert("取消入院成功！", "top", "success");
                            } else {
                                malert("取消入院失败！", "top", "defeadted");
                            }
                        })
                    }

                } else {
                    malert(data.body.c, 'top', 'defeadted');
                    zd_enter.ifClick = true;
                }
            }, function (error) {
                malert('取消入院无响应,请重新登录或反馈IT', 'top', 'defeadted');
                zd_enter.ifClick = true;
            });
        },

        canelRy: function () {
            if (!this.ifClick) return; //如果为false表示已经点击了不能再点
            this.rydjList = [];
            var rydj = {};
            rydj.zyh = this.popContent.zyh;
            rydj.brxm = this.popContent.brjbxxModel.brxm;
            this.rydjList.push(rydj);
            common.openConfirm("请确认是否取消入院？", this.deleteyz);
            zd_enter.ifClick = true;
        },
        print: function () {
            // 查询打印模板
            console.log(this.printList);
            var json = {repname: '补打预交单'};
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                zd_enter.clearArea(json.d[0]);
                // 为打印前生成数据
                zd_enter.printContent(zd_enter.printList);
                // 开始打印
                window.print();
            });
            // printYjd.print(this.printList);
        },
        //身份证号码加载
        getAddress: function () {
            //根据身份证号码查询出居住地省市县
            if (this.popContent.brjbxxModel.sfzjlx == '01') {
                if (this.popContent.brjbxxModel.sfzjhm) {
                    var shengbm = this.popContent.brjbxxModel.sfzjhm.substring(0, 2);
                    var shibm = this.popContent.brjbxxModel.sfzjhm.substring(0, 4);
                    var xianbm = this.popContent.brjbxxModel.sfzjhm.substring(0, 6);
                    this.popContent.jzdsheng = null;
                    this.popContent.jzdshengmc = null;
                    //popWin.popContent.hjdsheng=null;
                    //popWin.popContent.hjdshengmc=null;
                    var shengmc = this.listGetName(this.provinceList, shengbm, 'xzqhbm', 'xzqhmc');
                    Vue.set(this.popContent.brjbxxModel, 'jzdsheng', shengbm);
                    Vue.set(this.popContent.brjbxxModel, 'jzdshengmc', shengmc);

                    //户籍地信息默认为身份证信息
                    Vue.set(this.popContent.brjbxxModel, 'hjdsheng', shengbm);
                    Vue.set(this.popContent.brjbxxModel, 'hjdshi', shibm);
                    Vue.set(this.popContent.brjbxxModel, 'hjdxian', xianbm);

                    if (this.popContent.brjbxxModel.jzdsheng) {
                        shengbm = this.popContent.brjbxxModel.jzdsheng;
                    }
                    if (this.popContent.brjbxxModel.jzdshi) {
                        shibm = this.popContent.brjbxxModel.jzdshi;
                    }
                    if (this.popContent.brjbxxModel.jzdxian) {
                        xianbm = this.popContent.brjbxxModel.jzdxian;
                    }

                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"2","sjbm":"' + shengbm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                        if (json.a == 0) {
                            zd_enter.cityList = [];
                            zd_enter.cityList = json.d.list;
                            var shimc = zd_enter.listGetName(zd_enter.cityList, shibm, 'xzqhbm', 'xzqhmc');
                            Vue.set(zd_enter.popContent.brjbxxModel, 'jzdshi', shibm);
                            Vue.set(zd_enter.popContent.brjbxxModel, 'jzdshimc', shimc);
                        } else {
                            malert("市编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                            return false;
                        }
                    });

                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"3","sjbm":"' + shibm + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                        if (json.a == 0) {
                            zd_enter.countyList = [];
                            zd_enter.countyList = json.d.list;
                            var xianmc = zd_enter.listGetName(zd_enter.countyList, xianbm, 'xzqhbm', 'xzqhmc');
                            Vue.set(zd_enter.popContent.brjbxxModel, 'jzdxian', xianbm);
                            Vue.set(zd_enter.popContent.brjbxxModel, 'jzdxianmc', xianmc);
                        } else {
                            malert("县编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                            return false;
                        }
                    });
                }
            }
        },
        brnlFun: function () {
            if (this.popContent.nl >= 0) {
                if (!this.popContent.brjbxxModel.csrq) {
                    this.popContent.brjbxxModel.csrq = this.AgetoBrnl(this.popContent.nl, this.popContent.nldw);
                    //方便更新试图（添加set。get方法）
                    this.popContent = Object.assign({}, this.popContent);
                }
            }
        },
        selectFz: function () {
            //绑定年龄
            var csrq = this.fDate(this.popContent.brjbxxModel.csrq, "date");
            Vue.set(this.popContent.brjbxxModel, 'csrq', csrq);
            var brnl = "";
            if (this.CheckDate(csrq)) {
                brnl = this.datetoage(csrq);
            }
            if (!this.popContent.nl) {
                Vue.set(this.popContent, 'nl', brnl);
                Vue.set(this.popContent, 'nldw', "1");
            }
            if (this.popContent.brjbxxModel.ryrq) {

                var rq = this.popContent.brjbxxModel.ryrq;
                var ryrq = this.fDate(rq, "date");
                var time = this.fDate(rq, "times");
                ryrq = ryrq + " " + time;
                Vue.set(this.popContent.brjbxxModel, 'ryrq', ryrq);
            } else {
                this.popContent.brjbxxModel.ryrq = getTodayDateTime();
            }
            if (!this.popContent.fphm) {
                this.popContent.fphm = dqsyh;
            }
            //拼音代码
            this.popContent.pydm = this.popContent.brjbxxModel.pydm;
            //入院诊断
            this.jbbmContent.jbmc = this.popContent.ryzdmc;
            //设置医保卡信息
            if (this.popContent.ybkh && this.popContent.fbbm != '01' && this.popContent.bxlbbm == '01') {
                this.selectBrxm = true;//姓名下拉框显示
                this.ybkhBlur();
            } else {
                this.selectBrxm = false;//姓名下拉框隐藏输入框显示
            }
            //获取市县
            this.getSSX(this.popContent.brjbxxModel.jzdsheng, 'cityList', 2);
            this.getSSX(this.popContent.brjbxxModel.jzdshi, 'countyList', 3);
            // this.getSSX(this.popContent.brjbxxModel.csdsheng, 'cityList1', 2);
            // this.getSSX(this.popContent.brjbxxModel.csdshi, 'countyList1', 3);
            //手机号码
            this.popContent.sjhm = this.popContent.brjbxxModel.sjhm;
        },
		isBah:function(){
			let tp = true;
			if(!this.popContent.brjbxxModel.bah){
				tp = false;
				return tp;
			}
			var parm = {
				bah:this.popContent.brjbxxModel.bah,
				brid:this.popContent.brjbxxModel.brid,
			}
			
			$.fn.getJSONAjax("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryIsBan&parm=", parm, function (res) {
				console.log(3)
			    if(res.d && res.d >=1){
			    	malert("该病案号已使用", 'top', 'defeadted');
			    	tp =  false;
			    }else{
			    	tp = true;
			    }
			});
			return tp;
		},
        sfjhm: function () {
			            this.getAddress();
			let that = this;
            if (this.popContent.brjbxxModel.sfzjhm) {
                var parm = {
                    sfzjlx: this.popContent.brjbxxModel.sfzjlx,
                    sfzjhm: this.popContent.brjbxxModel.sfzjhm,
					bqcybz: '0',
                    gbxtbz: '0',
                };
                this.updatedAjax("/actionDispatcher.do?reqUrl=ZyglCryglRydj&types=querrydjJs&parm=" + JSON.stringify(parm), function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0 && json.d != null && json.d.length != 0 && json.d.length != undefined) {
                        var result = json.d[0];
                        //判断其是否有住院信息
                        var parm = {
                            brid: json.d[0].brjbxxModel.brid,
                            bqcybz: '0',
                            gbxtbz: '0',
                        };
                        zd_enter.updatedAjax("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=querrydj&parm=" + JSON.stringify(parm), function (json) {
                            console.log(json);
							
							
							
                            if (zd_enter.isUpdate == 0) {
                                //添加
                                if (json.d.list.length > 0) {
                                    malert("病人尚未出院，无法办理入院！", 'top', 'defeadted');

                                } else {
                                    if (zd_enter.popContent.brjbxxModel.ylkh) {
                                        result.brjbxxModel.ylkh = zd_enter.popContent.brjbxxModel.ylkh
                                    }
                                    if (zd_enter.popContent.brjbxxModel.ylklx) {
                                        result.brjbxxModel.ylklx = zd_enter.popContent.brjbxxModel.ylklx
                                    }
									// if(!result.brjbxxModel.bah){
									// 	zd_enter.readonlyBah = false;
									// 	zd_enter.updatedAjax("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryMaxBah", function (res) {
									// 		console.log(res)
									// 		result.brjbxxModel.bah = res.d
									// 	 });
									// }else{
									// 	zd_enter.readonlyBah = true;
									// }
                                    zd_enter.popContent = JSON.parse(JSON.stringify(result));
                                    zd_enter.popContentTmp = JSON.parse(JSON.stringify(result));
                                    //console.log(zd_enter.popContent);
                                    zd_enter.selectFz(); //赋值
                                    zd_enter.csqxIfInputSr(); //根据参数权限判断是否允许输入
                                    //有住院号或者有病人id都不允许输入医疗卡信息
                                    if (zd_enter.popContent.zyh || zd_enter.popContent.brjbxxModel.brid) {
                                        //禁用医疗卡信息部分
                                        zd_enter.ylkxx = true;
                                    }
                                    //设置病人姓名
                                    zd_enter.selectBrxm = false;//姓名下拉框隐藏输入框显示
                                }
                            }else{
								// if(!result.brjbxxModel.bah){
								// 	zd_enter.readonlyBah = false;
								// 	zd_enter.updatedAjax("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryMaxBah", function (res) {
								// 		console.log(res)
								// 		result.brjbxxModel.bah = res.d
								// 		zd_enter.popContent = JSON.parse(JSON.stringify(result));
								// 		zd_enter.popContentTmp = JSON.parse(JSON.stringify(result));
								// 	 });
								// }
								
								
								
							}

                        });
                    } else {
                        that.popContent.brjbxxModel.brid=null;
                        that.popContent.brjbxxModel.ylkh=null;
                        that.popContent.brjbxxModel.bah=null;
                        that.popContent.brjbxxModel.sjhm=null;
                        that.popContent.brjbxxModel.zybm=null;

                        that.popContent.brjbxxModel.lxrxm=null;
                        that.popContent.brjbxxModel.lxrgx=null;
                        that.popContent.brjbxxModel.lxrdh=null;
                        that.popContent.brjbxxModel.jzdmc=null;
                        that.popContent.brjbxxModel.brxm=null;
                        that.popContent.brjbxxModel.ylklx=null;

                    }
                });
                var sfzh = this.popContent.brjbxxModel.sfzjhm;
                var csrq = this.sfzhtodate(sfzh);
                var brnl = this.toAge(csrq);
                Vue.set(this.popContent.brjbxxModel, 'csrq', csrq);
                Vue.set(this.popContent, 'nl', brnl.age);
                Vue.set(this.popContent, 'nldw', brnl.unitNum);
                $("#sjhm").focus();
				zd_enter.$forceUpdate();
            }
        },
        csqxIfInputSr: function () {
            //是否允许修改
            if (!this.caqxContent.cs00700100105) {
                if (this.popContent.zyh) {
                    this.readonly = true; //设置编辑区不允许输入
                }
            } else {
                this.readonly = false; //设置编辑区允许输入
            }
            //是否允许修改姓名，年龄，性别
            if (!this.caqxContent.cs00700100107) {
                $("#brxm").attr("disabled", true);
                $("#brxb").find("input").attr("disabled", true);
                $("#brnl").attr("disabled", true);
                $("#nldw").attr("disabled", true);
            } else {
                $("#brxm").attr("disabled", false);
                $("#brxb").find("input").attr("disabled", false);
                $("#brnl").attr("disabled", false);
                $("#nldw").attr("disabled", false);
            }
            //是否允许修改担保人
            if (!this.caqxContent.cs00700100108) {
                $("#dbr").attr("disabled", true);
            } else {
                $("#dbr").attr("disabled", false);
            }
            //是否允许修改担保金额
            if (!this.caqxContent.cs00700100109) {
                $("#dbje").attr("disabled", true);
            } else {
                $("#dbje").attr("disabled", false);
            }
            //是否允许修改入院时间
            if (!this.caqxContent.cs00700100110) {
                $("#ryrq").attr("disabled", true);
            } else {
                $("#ryrq").attr("disabled", false);
            }
        },
        ry_DataValid: function () {
            var i = 0;
            var errString = "";
            if (!this.popContent.brjbxxModel.brxm) {
                i++;
                errString += "</br>【病人姓名】不能为空！";
            }
            if (!this.popContent.brjbxxModel.brxb) {
                i++;
                errString += "</br>【病人性别】不能为空！";
            }
//    if (zd_enter.popContent.brjbxxModel.sfzjlx == '01') {
//        if (zd_enter.popContent.brjbxxModel.sfzjhm != '' && zd_enter.popContent.brjbxxModel.sfzjhm != null) {
//            if (zd_enter.popContent.brjbxxModel.sfzjhm.length != 18) {
//                i++;
//                errString += "</br>【身份证号码】长度非法!！";
//            }
//        }
//    }
            if (!this.popContent.brjbxxModel.csrq) {
                i++;
                errString += "</br>【出生日期】不能为空！";
            }
//    var sjhm = zd_enter.popContent.brjbxxModel.sjhm;
            var myreg = /^1[34578]\d{9}$/;
//    if (sjhm == null || sjhm == undefined || sjhm == "") {
//        i++;
//        errString += "</br>【手机号码】不能为空！";
//    }
//    else if (sjhm.length != 11) {
//        i++;
//        errString += "</br>【手机号码】无效！";
//    } else if (!myreg.test(sjhm)) {
//        i++;
//        errString += "</br>【手机号码】无效！";
//    }

            if (!this.popContent.brfb) {
                i++;
                errString += "</br>【病人费别】不能为空！";
            }

            if (!this.popContent.ryks) {
                i++;
                errString += "</br>【入院科室】不能为空！";
            }
            if (this.caqxContent.cs00700100102 == 1) {
                // if (!this.popContent.zyys) {
                //     i++;
                //     errString += "</br>【住院医生】不能为空！";
                // }
                if (!this.popContent.rycwid) {
                    i++;
                    errString += "</br>【入院床位】不能为空！";
                }
            } else if (this.caqxContent.cs00700100102 == 2) {
                // if (!this.popContent.zyys) {
                //     i++;
                //     errString += "</br>【住院医生】不能为空！";
                // }
            } else if (zd_enter.caqxContent.N05002200158 == 1) {
                if (!this.popContent.ryzyzdmc) {
                    i++;
                    errString += "</br>【中医入院诊断】不能为空！";
                }
            }

            if (this.caqxContent.cs00700100103) {
                if (!this.popContent.ryzdbm) {
                    i++;
                    errString += "</br>【入院诊断】不能为空！";
                }
            }
            if (!this.popContent.brjbxxModel.ryrq) {
                i++;
                errString += "</br>【入院日期】不能为空！";
            }
            if (!this.popContent.mzys) {
                i++;
                errString += "</br>【门诊医生】不能为空！";
            }
            if (!this.popContent.ryqk) {
                i++;
                errString += "</br>【入院情况】不能为空！";
            }
            if (!this.popContent.rytj) {
                i++;
                errString += "</br>【入院途径 】不能为空！";
            }
            if (this.popContent.nl < 14) {
                if (!this.popContent.brjbxxModel.lxrxm) {
                    i++;
                    errString += "</br>【联系人信息】14以下联系人信息不能为空！";
                }
            }
            if (!this.popContent.nldw && !this.popContent.nl == false) {
                i++;
                errString += "</br>【未填写年龄单位，不应该有年龄 】！";
            }
            if (!this.popContent.nldw2 && !this.popContent.nl2 == false) {
                i++;
                errString += "</br>【未填写月龄单位，不应该有月龄 】！";
            }
            if (!this.popContent.nl && !this.popContent.nl2) {
                i++;
                errString += "</br>【月龄和年龄】不能同时为空！";
            }

            if (i > 0) {
                malert(errString, 'top', 'defeadted');
                return false;
            } else {
                return true;
            }
        },
        selectOne2: function (item) {
            if (item == null) {
                this.page.page++;
                this.changeDown2(true, 'text', this.searchjson['text']);
            } else {
                this.searchjson = item;
                console.log(this.searchjson);
                Vue.set(this.popContent, 'dbr', this.searchjson['ryxm']);
                Vue.set(this.popContent, 'dbrbm', this.searchjson['rybm']);
                $(".selectGroup").hide();
            }
        },
        change2: function (add, val, event) {
            if (!add) this.page.page = 1;       //  设置当前页号为第一页
            var _searchEvent = $(event.nextElementSibling || event.target.nextElementSibling).eq(0);
            this.page.parm = val
            var json = {
                ysbz: '1',
                tybz: '0',
            };
            var dg = {
                page: this.page.page,
                rows: this.page.rows,
                parm: this.page.parm,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(json) + "" + "&dg=" + JSON.stringify(dg), function (data) {
                if (add) {
                    for (var i = 0; i < data.d.list.length; i++) {
                        zd_enter.searchCon2.push(data.d.list[i]);
                    }
                } else {
                    zd_enter.searchCon2 = data.d.list;
                }
                zd_enter.page.total = data.d.total;
                zd_enter.selSearch = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                }
            });
        },
        changeDown2: function (event) {
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter' && this.popContent.dbr) {
                this.nextFocus(event);
            }
            if (this['searchCon2'][this.selSearch] == undefined) return;
            this.inputUpDown(event, 'searchjson', 'selSearch');
            this.searchjson = this['searchCon2'][this.selSearch]
            // 选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                Vue.set(this.popContent, 'dbr', this.searchjson['ryxm']);
                Vue.set(this.popContent, 'dbrbm', this.searchjson['rybm']);
                this.selSearch = -1
                this.nextFocus(event);
                $(".selectGroup").hide();

            }
        },
    }
});
var popTable = new Vue({
    el: '#popCenter',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        isShow: false,
        jsonList: []
    },
    methods: {
        success: function () {

        }
    }
});
/*********************************非空判断**************************************/
$('input[data-notEmpty=true],select[data-notEmpty=true],input[data-notempty=true]').on('blur', function () {
    if ($(this).val() == '' || $(this).val() == null) {
        $(this).addClass("emptyError");
    } else {
        $(this).removeClass("emptyError");
    }
});
//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});

/*********************快捷键********************************/
$(document).keydown(function (e) {
    //F2门诊收费保存
    if (e.keyCode == 113) {
        zd_enter.saveData();//保存
    }
});

//入院登记信息 出身日期时间选择器
laydate.render({
    elem: '#csrq',
    rigger: 'click',
    theme: '#1ab394',
    type: 'date',
    done: function (value, data) {
        var data1 = zd_enter.fDate(new Date(), "date");
        if (value > data1) {
            zd_enter.popContent.brjbxxModel.csrq = data1;
            zd_enter.$forceUpdate();
            malert("出生日期填写有误,出生日期[" + value + "]不能晚于当前日期,请检查!", 'top', 'defeadted');
            return;
        }
        zd_enter.popContent.brjbxxModel.csrq = value;
        // 生成年龄
        var csrq = value;
        var brnl = "";
        if (zd_enter.CheckDate(csrq)) {
            brnl = zd_enter.datetoage(csrq);
        }
        Vue.set(zd_enter.popContent, 'nl', brnl);
        if (zd_enter.popContent.nl != null && zd_enter.popContent.nl != "") {
            $("#sjhm").focus();
        }
    }
});

function loadOpenPage(name) {
    $('#loadPage').load(name + '.html');
    popTable.isshow = true;
}

var printYjd = new Vue({
    el: "#print-yjd",
    mixins: [mformat],
    data: {
        ifShow: false,
        printData: []
    },
    mounted: function () {

    },
    methods: {
        open: function () {
            this.ifShow = true;
        },
        close: function () {
            this.ifShow = false;
        },
        print: function (printData) {
            var _printDataType = Object.prototype.toString.call(printData);
            if (_printDataType == "[object Array]") {
                this.printData = printData;
            } else if (_printDataType == "[object Object]") {
                this.printData = [printData];
            }
            this.open();
            this.$nextTick(function () {
                window.print();
                this.printData = [];
                this.close();
            });
        }
    }

});

var toMtbrList = new Vue({
    el: '#mtbrList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isFold: false,
        jsonList: [],
        ksrq: '',
        jsrq: '',
        jsValue: '',
        param: {
            page: 1,
            rows: 30,
            sort: 'ghxh',
            order: 'desc',
            parm: ''
        },
        totlePage: 0,
        mtzt_tran: {
            '9': '未制定方案',
            '0': '待审核',
            '1': '已审核',
            '2': '已变更',
            '3': '已回退',
            '4': '已结账',
            '5': '未结账',
        },
        bxlbbm: '',
        bxurl: '',
    },
    mounted: function () {
        this.ksrq = this.fDate(new Date(), 'date') + ' 00:00:00';
        this.jsrq = this.fDate(new Date(), 'date') + ' 23:59:59'
    },
    created: function () {
        this.param.zt = '9';
        this.getbxlb();
    },
    updated: function () {
    },
    methods: {
        showTime: function (el, code) {
            laydate.render({
                elem: '#' + el
                , show: true //直接显示
                , trigger: 'click'
                , theme: '#1ab394'
                , done: function (value, data) {
                    toMtbrList[code] = value;
                    toMtbrList.getData();
                }
            });
        },
        closes: function () {
            this.isFold = false
        },
        getbxlb: function () {
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    toMtbrList.bxlbbm = json.d.list[0].bxlbbm;
                    toMtbrList.bxurl = json.d.list[0].url;
                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted');
                }
            });
        },
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "zt":
                    Vue.set(this.param, 'zt', val[0]);
                    break;
                case "ksbm":
                    Vue.set(this.param, 'ksbm', val[0]);
                    break;
            }
            this.$forceUpdate()
            this.getData();
        },
        getData: function () {
            if (this.ksrq != '' && this.jsrq != '') {
                startDate = this.fDate(this.ksrq, 'date') + ' 00:00:00';
                endtDate = this.fDate(this.jsrq, 'date') + ' 23:59:59';
            }

            var param = {
                zt: this.param.zt == '9' ? '' : this.param.zt,
                ksbm: 'all',
                beginTime: startDate,
                endTime: endtDate,
                parm: this.param.parm,
                page: this.param.page,
                rows: this.param.rows,
            };

            this.updatedAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=mzjy&method=mtlist&parm=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    var res = JSON.parse(json.d);
                    toMtbrList.totlePage = Math.ceil(res.total / toMtbrList.param.rows);
                    toMtbrList.totlePage = toMtbrList.totlePage == 0 ? 1 : toMtbrList.totlePage;
                    toMtbrList.jsonList = res.records;
                } else {
                    malert("获取门特病患者列表失败  " + json.c, 'top', 'defeadted');
                }
            }, function () {
            },);
        },
        edit: function (index, item) {
            // rightVue.searching1(false, 'ghxh', item.ghxh);
            Vue.set(zd_enter.popContent.brjbxxModel, 'mtbbz', '1');
            Vue.set(zd_enter.popContent.brjbxxModel, 'djbh', item.aac001);
            this.isFold = false
        }
    }
});

let yibao_panel = new Vue({
    el: '#yibao_panel',
    data: {
        title: '患者医保信息',
        isShowYibaoPage: false,
        isYibaoDengJi: false,
        close: true,
        //婚姻状况映射 k-his,v-医保
        hyzkMap: {
            '10': '1',//未婚
            '20': '2',//已婚
            '21': '9',//初婚
            '22': '9',//再婚
            '23': '9',//复婚
            '30': '3',//丧偶
            '40': '4',//离婚
            '90': '9'//未说明的婚姻状况
        },
		popContent:{}
    },
    mounted: function () {

    },
    created: function () {
        //初始化医保控件
    },
    updated: function () {

    },
    methods: {
        
       
        notification: function (param, method) {
            let result = false;
            let req_param = {ykc010: param.ykc010, akc190: param.akc190};
            let url = "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + zd_enter.bxurl + "&bxlbbm=" + zd_enter.bxlbbm + "&types=inhospital&method=" + method + "&parm=";
            $.fn.getJSONAjax(url, req_param, function (res) {
                result = (res.a == '0');
            });
            return result;
        },
        failOpenConfirm: function (text) {
            common.openConfirm(text, function () {
                window.clc(this);
            });
            return false;
        }
    },
    watch: {
        "isShowYibaoPage": function () {

        }
    }
})
