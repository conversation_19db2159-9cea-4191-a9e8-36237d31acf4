
function getData() {
    wrapper.getData()
}

var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        popContent: {},
        search: '',
        kfList: [],
        zyksList: [],
        totlePage: 0,
        zhuangtai: {
            '0': '待审核',
            '1': '已审核',
            '2': '未通过',
            '3': '已作废'
        },
        param: {
            page: 1,
            rows: 10,
            parm: '',
            beginrq: null,
            endrq: null
        },
        jsonList: [],
    },
    mounted: function () {
        this.GetZyksData();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.beginrq  = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal'
            , eventElem: '.zui-date'
            , trigger: 'click',
            type: 'datetime',
            value: this.param.beginrq
            , theme: '#1ab394'
            , done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1'
            , eventElem: '.zui-date'
            , trigger: 'click',
            type: 'datetime',
            value: this.param.beginrq
            , theme: '#1ab394'
            , done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
    },
    methods: {
        //开单
        topNewPageFun: function () {
            var obj = {};
            Vue.set(obj, 'ksbm', this.param.ksbm);
            Vue.set(obj, 'zyksList', this.zyksList);
            Vue.set(obj, 'sh', false);
            this.Verify(obj);
        },
        //库房
        resultRydjChange: function (val) {
            Vue.set(this.param, 'kfbm', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
        },
        //页面加载时自动获取住院科室Dddw数据
        GetZyksData: function () {
            var bean = {"zyks": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    wrapper.zyksList = json.d.list;
                    wrapper.param.ksbm = json.d.list[0].ksbm;
                    wrapper.getData();
                } else {
                    malert(json.c, "住院科室列表查询失败");
                    return;
                }
            });
        },
        //审核
        sh: function (index) {
            var obj = {};
            Vue.set(obj, 'ksbm', this.param.ksbm);
            Vue.set(obj, 'sljh', this.jsonList[index]);
            Vue.set(obj, 'sh', this.jsonList[index].shzfbz ==0);
            Vue.set(obj,'dy',this.jsonList[index].shzfbz !=0);
            console.log(obj);
            this.Verify(obj);
        },
        //跳转
        Verify: function (obj) {
            sessionStorage.setItem('obj', JSON.stringify(obj));
            this.topNewPage('科室申领开单', 'page/sbgl/kfyw/ksslgl/ksslkd.html');
        },
        //作废2018/07/09二次作废弹窗提示
        Refuse: function (index) {
            var obj = this.jsonList[index];
            if (common.openConfirm("<div>确定作废科室申领计划单号-" + obj.sldh + "-采购计划单吗？<div class=\"flex-container flex-align-c\">\n" +
                "<span class=\"ft-14 whiteSpace padd-r-5\">作废原因</span><textarea rows=\"3\" cols=\"100\"id=\"zfyy\" class=\"padd-t-5 padd-b-5 padd-l-5 padd-r-5 wh100MAx\"></textarea>\n" +
                "</div></div>", function () {
                obj.zfyy=$('#zfyy').val()
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywSld&types=invalidDj', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        wrapper.getData();
                        malert("作废成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert("作废失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }
        },
        getData: function (val) {
            //清空入库明细信息
            this.jsonLit = [];
            //发送请求获取结果
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywSld&types=queryDj' +
                '&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == "0") {
                        wrapper.jsonList = data.d.list;
                        wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
                        malert(data.c);
                    } else {
                        malert(data.c);
                    }

                });
        }
    },

});





