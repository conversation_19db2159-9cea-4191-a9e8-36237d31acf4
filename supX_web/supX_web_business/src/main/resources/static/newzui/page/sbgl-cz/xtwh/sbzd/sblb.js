var wzlb = new Vue({
	el: '#wzlb',
	mixins: [dic_transform, tableBase],
	data: {
		jsonList: [],
		dg: {
			page: 1,
			rows: 500,
			sort: "",
			order: "asc",
		},
	},
	methods: {
		getData: function() {
			//准备参数
			var json = {
				'tybz': 0,
			}
			$.getJSON("/actionDispatcher.do?reqUrl=WzkfXtwhWzlb&types=query&json=" +
				JSON.stringify(json) + "&dg=" + JSON.stringify(this.dg),
				function(data) {
					if(data.a == 0) {
						wzlb.totlePage = Math.ceil(data.d.total / wzlb.param.rows);
						wzlb.jsonList = data.d.list;
					} else {
						alert(data.c)
					}

				});
		},
		addData: function() {
			//设置操作类型
			wzlbPop.op = 'save';
			wzlbPop.popContent = {};
			wzlbPop.isShow = true;
		},
		edit: function(num) {
			wzlbPop.op = 'update';
			if(num == null) {
				for(var i = 0; i < this.isChecked.length; i++) {
					if(this.isChecked[i] == true) {
						num = i;
						break;
					}
				}
				if(num == null) {
					malert("请选中你要修改的数据");
					return false;
				}
			}
			wzlbPop.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
			wzlbPop.isShow = true;
		},
		remove: function() {
			var wzlbList = [];
			for(var i = 0; i < this.isChecked.length; i++) {
				if(this.isChecked[i] == true) {
					wzlbList.push(this.jsonList[i]);
				}
			}
			if(wzlbList.length == 0) {
				malert("请选中您要删除的数据");
				return false;
			}
			if(!confirm("请确认是否删除")) {
				return false;
			}

			var json = {
				'list': wzlbList
			}
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhWzlb&types=delete', JSON.stringify(json))
				.then(function(data) {
					if(data.body.a == 0) {
						malert("数据删除成功")
					} else {
						malert("数据删除失败");
					}
					wzlb.getData();
				}, function(error) {
					console.log(error);
				});

		}
	}
});
wzlb.getData();

//弹出层
var wzlbPop = new Vue({
	el: '#wzlbPop',
	mixins: [dic_transform, baseFunc],
	data: {
		dg: {
			page: 1,
			rows: 500,
			sort: "",
			order: "asc",
		},
		isSubmited: false, //是否保存
		//大类列表
		dlList: [],
		isShow: false,
		popContent: {},
		isKeyDown: null,
		title: '物资类别',
		op: 'save'
	},
	//启动加载
	mounted: function() {
		//select-input调价事件，回车提交
		document.getElementsByTagName('input')[8].addEventListener('keyup', function() {
			if(wzlbPop.popContent.tybz != null || wzlbPop.popContent.tybz != undefined) {
				if(confirm('是否保存?')) {
					wzlbPop.saveData();
				}
			}
		});
		//准备参数
		var json = {
			'tybz': 0,
		};

		$.getJSON("/actionDispatcher.do?reqUrl=WzkfXtwhWzdl&types=query&json=" +
			JSON.stringify(json) + "&dg=" + JSON.stringify(this.dg),
			function(data) {
				if(data.a == 0) {
					wzlbPop.dlList = data.d.list;
				} else {

				}

			});
	},
	methods: {
		//大类改变
		dlChange: function() {
			for(var i = 0; i < this.dlList.length; i++) {
				if(this.dlList[i].dlmc == this.popContent.dlmc) {
					Vue.set(this.popContent, 'dlbm', this.dlList[i].dlbm)
				}
			}
		},
		//保存数据
		saveData: function() {
			if(this.isSubmited) {
				malert("数据提交中");
				return;
			}
			//非空判断
			if(this.popContent['tybz'] == null || this.popContent['tybz'] == undefined) {
				malert("停用标志输入不正确");
				return;
			};

			//是否禁止提交
			this.isSubmited = true;

			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhWzlb&types=' + this.op, JSON.stringify(this.popContent))
				.then(function(data) {
					if(data.body.a == 0) {
						wzlb.getData();
						wzlbPop.isShow = false;
						wzlbPop.isAdd = false;
						malert("数据保存成功");
						//是否禁止提交
						this.isSubmited = false;
					} else {
						malert("数据提交失败");
						//是否禁止提交
						this.isSubmited = false;
					}
				}, function(error) {
					console.log(error);
				});
		}
	}
});

//验证是否为空
$('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
	if($(this).val() == '' || $(this).val() == null) {
		$(this).addClass("emptyError");
	} else {
		$(this).removeClass("emptyError");
	}
});

//为table循环添加拖拉的div
var drawWidthNum = $(".wzlb tr").eq(0).find("th").length;
for(var i = 0; i < drawWidthNum; i++) {
	if(i >= 2) {
		$(".wzlb th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
	}
}