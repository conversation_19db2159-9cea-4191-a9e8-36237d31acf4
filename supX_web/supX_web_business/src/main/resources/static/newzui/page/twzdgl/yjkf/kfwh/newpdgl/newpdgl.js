//主体部分
var wrapper=new Vue({
	el:'#jyxm_icon',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data:{
		//*****************公共
		num:0,
		KFList: [{
			kfbm:'06',
			kfmc:'体外诊断试剂库',
			ksbm:'0952'
		}],//库房列表KFList
		editPdlr:false,
		YPZLSelect: [],//药品种类列表
		pdscJsonList: [],//药品列表
		// yfSelected: '',//选中的药房
		pzNum: '', //选中的凭证号
		qxksbm: '0952', //权限科室编码
		csqxContent: {},//获取的参数权限对象
		popContent: { //判断方式选择
			pdWay: 1,
			ypzl: 0,
		},
		pdscShow: true, //盘点生成需要显示
		passShow: false,
		ypzlShow: false, //药品种类显示
		ypmcShow: false, //药品名称
		ypjsValue: '', //药品检索内容
		isSubmited: false,//是否禁止保存

		//******************************盘点生成
		fyContent: {},
		searchCon: [],
		selSearch: -1,
		jjyyk:'',
		ljyyk:'',
		wpjjyyk:'',
		wpljyyk:'',
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		param: {
			page: 1,
			rows: 50,
			sort: '',
			order: 'asc'
		},
		isChecked:false,
		totlePage: 0,
		them_tran: {},
		pdbContent: {},
		them: {
			'药品编号': 'ypbm',
			'药品名称': 'ypmc',
			'规格': 'ypgg',
			'分装比例': 'fzbl',
			'进价': 'ypjj',
			'零价': 'yplj',
			'库房单位': 'kfdwmc',
			'药房单位': 'yfdwmc',
			'药房种类': 'ypzlmc',
			'药品剂型': 'jxmc'
		},
		json:{
			jjzj:0,
			ljzj:0,
		},
	},
	updated:function(){
		changeWin()
	},
	mounted: function() {
		this.pdbContent.kfbm = 0;
	},

	methods:{
		//切换菜单栏
		tabBg:function (n) {
			wrapper.param.page = '1';
			this.num=n;
			wrapper.getPdSC();
			//根据菜单栏的切换隐藏显示
			//0盘点生成,1未核盘点表,2盘点录入,3录入审核,4盘点完成,

			if(this.num != 1) {
				wapAddmx.closes();
				wap.closes();
				wapAddmx.popContent = {};
				wap.popContent = {};
				wapAddmx.ypmcInput = "";
				wap.inputYpmc = '';
			}

			if(this.num==0){
				this.pdscShow=true;
				this.passShow=false;
				this.ypzlShow=false;
				this.ypmcShow=false;
			}else if(this.num==1){
				this.pdscShow=false;
				this.passShow=false;
				this.ypzlShow=false;
				this.ypmcShow=false;
			}else if(this.num==2){
				this.pdscShow=false;
				;
				this.passShow=false;
				this.ypzlShow=false;
				this.ypmcShow=false;
			}else if(this.num==3){
				this.pdscShow=false;
				this.passShow=true;
				this.ypzlShow=false;
				this.ypmcShow=false;
			}
		},
		//获取药品种类集合
		getYpzlData: function(){
			//加载药品种类列表
			$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl',
				function(data) {
					wrapper.YPZLSelect = data.d.list;
				});
		},
		reCheckBox: function (val) {
            wrapper.isChecked = val[2];
        },

		//获取库房信息
		getKFData: function () {
			$.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040030012006"}',
				function (data) {
					if (data.a == 0) {

						//库房下拉框集合赋值
						// wrapper.KFList = data.d;
						//默认获取该用例第一个科室的权限科室编码
						// wrapper.qxksbm = data.d[0].ksbm;
						//启动获取权限参数
						// wrapper.getCsqx();
					} else {
						malert("药库获取失败",'top','defeadted');
					}
				});
		},


		//库房改变后重新获取权限科室编码，保存盘点表后刷新库房列表也可触发此方法
		opChange: function (event, type) {
			if (type == 'ksbm') {
				//设置库房编码
				// document.getElementById('_kfbm').value = wrapper.pdbContent.kfbm;
				var obj = event.currentTarget;
				var selected = $(obj).find("option:selected");
				qxksbm = selected.attr(type) == undefined ? qxksbm : selected.attr(type); //获取科室编码

				//判断是否存在未审核的盘点表
				wrapper.getPdSC();

			} else if (type == 'zlbm') {
				//获取药品种类编码
				enter.pdbContent.zlbm = event.currentTarget.value;
			} else if (type == 'ypbm') {
				//获取药品编码
				enter.pdbContent.ypbm = event.currentTarget.value;
			}
		},


		//药品下拉检索
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'fyContent', 'searchCon');
			//选中之后的回调操作
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				if (type == 'text') {
					Vue.set(this.popContent, 'ypmc', this.fyContent.ypmc);
					Vue.set(this.popContent, 'ypbm', this.fyContent.ypbm);
					this.ypmcInput = this.fyContent.ypmc;
				}
			}
		},

		//当输入值后才触发
		change: function (add, val) {
			this.ypmcInput = val;
			if (!add) this.page.page = 1;       // 设置当前页号为第一页
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			this.page.parm = this.ypmcInput;
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
			$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(str_param),
				function (data) {
					if (data.d.list.length > 0) {
						if (add) {
							for (var i = 0; i < data.d.list.length; i++) {
								wrapper.searchCon.push(data.d.list[i]);
							}
						} else {
							wrapper.searchCon = data.d.list;
						}
					}
					wrapper.page.total = data.d.total;
					wrapper.selSearch = 0;
					if (data.d.list.length > 0 && !add) {
						$(".selectGroup").hide();
						_searchEvent.show();
					}
				});
		},

		//鼠标双击
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;  // 设置当前页号
				this.change(true, this.ypmcInput);           // 传参表示请求下一页,不传就表示请求第一页
			} else {
				Vue.set(this.popContent, 'ypbm', item.ypbm);
				Vue.set(this.popContent, 'ypmc', item.ypmc);
				this.ypmcInput = item.ypmc;
				$(".selectGroup").hide();
			}
		},

		//生成盘点表
		add: function() {
			//非空判断
			if(parseInt(wrapper.pdbContent.kfbm)<=0) {
				malert('请选择库房！', "top",'defeadted');
				return;
			}
			//设置参数
			var json = {
				'kfbm': wrapper.pdbContent.kfbm,
				'ifgx': wrapper.isChecked == true ? '1': ''
			};
			common.openloading()
			//发送请求，查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=updatePd&parm=' + JSON.stringify(json),
				function(data) {
					if(data.a == 0) {
						common.closeLoading()
						malert(data.c, "top",'success');
					} else {
						common.closeLoading()
						malert(data.c, "top",'defeadted');
					}
					wrapper.getPdSC();
				});
		},


		//盘点生成
		getPdSC: function() {
			//非空判断
			if(parseInt(wrapper.pdbContent.kfbm)<=0) {
				malert('请选择库房！', "top",'defeadted');
				return;
			}
			wrapper.pdscJsonList = [];
			wrapper.param['kfbm'] = wrapper.pdbContent.kfbm;

			common.openloading()

			if(this.num == 1 ||this.num==2 || this.num==3){
				if(this.num==1){
					wrapper.param['pdlc'] = '1';
				}else if(this.num==2){
					wrapper.param['pdlc'] = '2';
				}else {
					wrapper.param['pdlc'] = '3';
				}
				//发送请求，查询盘点表
				$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=selectPdMxlb&parm=' + JSON.stringify(wrapper.param),
				function(data) {
					if(data.a == 0) {
						common.closeLoading()

						if(wrapper.num==2 || wrapper.num==3){
							wrapper.jjyyk = wrapper.fDec(data.d.jjyyk,4);
							wrapper.ljyyk = wrapper.fDec(data.d.ljyyk,4);
							wrapper.wpjjyyk = wrapper.fDec(data.d.wpjjyyk,4);
							wrapper.wpljyyk = wrapper.fDec(data.d.wpljyyk,4);
						}
						wrapper.pdscJsonList = data.d.list;
						wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
					} else {
						common.closeLoading()
						malert(data.c, "top",'defeadted');
					}
				});

			}else {

				wrapper.param['pdlc'] = '';
				//发送请求，查询盘点表
				$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=selectPdlb&parm=' + JSON.stringify(wrapper.param),
				function(data) {
					if(data.a == 0) {
						common.closeLoading()
						wrapper.pdscJsonList = data.d.list;
						wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
					} else {
						common.closeLoading()
						malert(data.c, "top",'defeadted');
					}
				});

			}
		},

		//双击修改有效期和批次停用
        edit: function ($index,index) {
			wrapper.editPdlr = true;
			var json = JSON.parse(JSON.stringify(wrapper.pdscJsonList[$index]));
			if(json.ifpdlr == 1) { //新增

				wapAddmx.open();
				wapAddmx.popContent = json;
				wapAddmx.ypmcInput = wapAddmx.popContent.ypmc;
			}else {
				wap.open();
				wap.popContent = json;
				wap.inputYpmc = wap.popContent.ypmc;
			}

            // wap.popContent.yxqz=this.fDate(wrapper.jsonList[$index]['ypList'][index]['yxqz'], 'date')
        },
		//****************************************未核盘点
		goPage: function(val) {

			wrapper.param.page = val;
            if(val) {
				wrapper.param.page = val;
               wrapper.getPdSC();
            }
        },

		//盘点录入
		addpdlr: function(){
			wrapper.editPdlr = false;
			wap.open();
		},
		//盘点新增
		savepdlr: function(){
			wapAddmx.popContent = {};
			wapAddmx.open();
		},

		//**************************************公用提交方法
		ypjsData: function(){
			wrapper.param["parm"] = this.ypjsValue;
			wrapper.getPdSC();
		},
		//刷新
		refresh: function(){
			wrapper.getPdSC();

		},

		//格式数据值小位数，d-输入参数，len-小数位数
		fDec: function (d, len) {
			var f = parseFloat(d);
			if (isNaN(f)) {
				return "";
			}
			var f = Math.round(d * 100) / 100;
			var s = f.toString();
			var rs = s.indexOf('.');
			if (rs < 0) {
				rs = s.length;
				s += '.';
			}
			while (s.length <= rs + len) {
				s += '0';
			}
			if (s == 0.00) {
				s = "";
			}
			return s;
		},

		//审核
		confirm: function() {
			if(this.num==3){

				common.openConfirm("是否确定审核？", sccf);

				function sccf() {
					//非空判断
					if(parseInt(wrapper.pdbContent.kfbm)<=0) {
						malert('请选择药房！', "top",'defeadted');
						return;
					}
					//设置参数
					var json = {
						'kfbm': wrapper.pdbContent.kfbm,
					};
					common.openloading()
						//发送请求，查询盘点表
						$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=updateMxPdwc&parm=' + JSON.stringify(json),
							function(data) {
								if(data.a == 0) {
									common.closeLoading()
									wrapper.getPdSC();
									malert('审核成功', "top",'success');

								} else {
									common.closeLoading()
									malert(data.c, "top",'defeadted');
								}
						});
				}
			}
		},
	},
});


//右侧弹窗 盘点录入
var wap = new Vue({
    el: '#pdlrSide',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
		type:true,
        isShowpopL: false,
        iShow: false,
        isTabelShow: false,
        flag: false,
        jsShow: false,
        centent: '',

        yfbm: null,
        inputYpmc: '', //药品名称
        isFold: false,
        ksList: [],
        hszList: [],
        ywckList: [],
        title: '',
        ifClick: true,
        num: 0,
        csContent: {},
        YFList: [],
        jsonList: [],
        popContents: {},
        cgryList: [],//采购员
        csqxContent: {}, //参数权限对象
        csqx: null,
        ckdContent: {}, //出库单对象
        KSList: [], //领用科室
        zbfsList: [],//招标方式
        ghdwList: [],//供货单位
        KFList: [], //库房
        ryList: [], //领用人
        glryList: [], //过滤领用人
        ypcdList: [], //药品产地
        rkd: {}, //入库单对象
        popContent: {

        },
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '药品编号': 'ypbm',
			'药品名称': 'ypmc',
			'生产批号': 'scph',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'ypjj',
            '零价': 'yplj',
            '库房单位': 'kfdwmc',
            '药房单位': 'yfdwmc',
            '药房种类': 'ypzlmc',
        },

    },
    watch: {

        inputYpmc: function (newval, val) {
            if (newval == this.popContent.ypmc) {
                return;
            }
            var type = 'ypmc';
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.popContent[type] = val;
            if (type == "ypmc") { //药品下拉框
                wap.dg.page = 1;
                wap.dg.rows = 5;
				wap.dg.parm = newval;
				wap.dg['kfbm'] = wrapper.pdbContent.kfbm;
				$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=selectPdlb&parm=' + JSON.stringify(wap.dg),
                // $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(wap.dg),
                    function (data) {
                        if (data.a == 0) {
                            wap.searchCon = data.d.list;
                            wap.total = data.d.total;
                            wap.selSearch = 0;
                            if (data.d.list.length != 0) {
                                $(".selectGroup").hide();
                                _searchEvent.show()
                            } else {
                                $(".selectGroup").hide();
                            }
                        } else {
                            malert("药品检索失败!",'top','defeadted');
                        }

                    });

            }
        },
    },
    mounted: function () {

    },
    methods: {
		//关闭
		closes: function () {
			wrapper.editPdlr = false;
			wap.popContent = [];
			wap.inputYpmc = '';
			wap.type=true
		},
		open: function () {
			wapAddmx.type=true
			wap.type=false
		},

        //确定
        confirms: function () {
            wap.addData();
		},

        //药品名称下拉table检索数据
        changeDown: function (event, type, searchCon) {
			 this.keyCodeFunction(event, 'popContent', 'searchCon');
			 event.currentTarget.select();
            //选中之后的回调操作
            if (event.keyCode == 13) {
					this.inputYpmc = this.popContent.ypmc;
                    this.nextFocus(event);
                    //此处药品价格为库房单位价格， 需要显示药房单位价格，所以处以分装比例
                    // this.popContent.ypjj = this.popContent.ypjj/this.popContent.fzbl;
                    // this.popContent.yplj = this.popContent.yplj/this.popContent.fzbl;
            }
		},

        changeDownDate:function(event, type){
            // if (!event.target.value) {
            //     malert('请输入' + (type == '_yxqz' ? '有效期' : '生产日期'), 'top', 'defeadted');
            //     //触发时间控件
            //     event.target.click();
            //     return;
            // } else {
            //     //激活插件检测校验时间
            //     event.target.blur();
            // }
            // //判断格式
            // var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
            // var regExp = new RegExp(reg);
            // if (!regExp.test(event.target.value)) {
            //     malert("日期格式不正确，正确格式为：YYYY-MM-HH", 'top', 'defeadted');
            //     return;
            // }
			//跳转下一个输入框
			if (event.keyCode == 13) {
				if(type !='pdsjkc'){
					this.nextFocus(event);
				}else{
					this.addData();
				}
			}
		},
        //当输入值后才触发
        change: function (event, type, val) {

            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.popContent[type] = val;
            if (type == "ypmc") { //药品下拉框
                //分页参数
                wap.dg.page = 1;
                wap.dg.rows = 5;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&tybz=0' +
                    '&dg=' + JSON.stringify(wap.dg),
                    function (data) {
                        wap.searchCon = data.d.list;
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if (data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        } else {
                            $(".selectGroup").hide();
                        }
                    });
            }
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                wap.dg.page++;
                wap.dg.parm = this.popContent.ypmc;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd' +
                    '&dg=' + JSON.stringify(wap.dg),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                wap.searchCon.push(data.d.list[i]);
                            }
                            wap.total = data.d.total;
                            wap.selSearch = 0;
                        } else {
                            malert('药品检索失败', 'top', 'defeadted')
                        }

                    });
                return;
            }

            this.popContent = item;
            //此处药品价格为库房单位价格， 需要显示药房单位价格，所以处以分装比例
            // this.popContent.ypjj = this.popContent.ypjj/this.popContent.fzbl;
            // this.popContent.yplj = this.popContent.yplj/this.popContent.fzbl;
            this.inputYpmc = this.popContent.ypmc;
            $(".selectGroup").hide();

        },
        //盘点录入
        addData: function () {

			if (wrapper.pdbContent.kfbm == undefined || wrapper.pdbContent.kfbm == null || wrapper.pdbContent.kfbm == "") {
                malert("库房不能为空!", 'top', 'defeadted');
                return;
			}

			if (this.popContent['ypmc'] == undefined || this.popContent['ypmc'] == null || this.popContent['ypmc'] == "") {
                malert("药品名称不能为空!", 'top', 'defeadted');
                return;
			}

			if (this.popContent['ypbm'] == undefined || this.popContent['ypbm'] == null || this.popContent['ypbm'] == "") {
                malert("药品编码不能为空!", 'top', 'defeadted');
                return;
			}

			if (this.popContent['ypgg'] == undefined || this.popContent['ypgg'] == null || this.popContent['ypgg'] == "") {
                malert("药品规格不能为空!", 'top', 'defeadted');
                return;
			}

			if (this.popContent['yplj'] == undefined || this.popContent['yplj'] == null || this.popContent['yplj'] == "") {
                malert("药品零价不能为空!", 'top', 'defeadted');
                return;
			}

			if (this.popContent['ypjj'] == undefined || this.popContent['ypjj'] == null || this.popContent['ypjj'] == "") {
                malert("药品进价不能为空!", 'top', 'defeadted');
                return;
			}

			// if (this.popContent['scph'] == undefined || this.popContent['scph'] == null || this.popContent['scph'] == "") {
            //     malert("生产批号不能为空!", 'top', 'defeadted');
            //     return;
			// }

			// if (this.popContent['cpbzh'] == undefined || this.popContent['cpbzh'] == null || this.popContent['cpbzh'] == "") {
            //     malert("产品标准号不能为空!", 'top', 'defeadted');
            //     return;
			// }

			// if (this.popContent['pzwh'] == undefined || this.popContent['pzwh'] == null || this.popContent['pzwh'] == "") {
            //     malert("批准文号不能为空!", 'top', 'defeadted');
            //     return;
			// }

			if (this.popContent['scrq'] == undefined || this.popContent['scrq'] == null || this.popContent['scrq'] == "") {
                malert("生产日期不能为空!", 'top', 'defeadted');
                return;
			}

			if (this.popContent['yxqz'] == undefined || this.popContent['yxqz'] == null || this.popContent['yxqz'] == "") {
                malert("有效期至不能为空!", 'top', 'defeadted');
                return;
			}

			// if (this.popContent['ghdw'] == undefined || this.popContent['ghdw'] == null || this.popContent['ghdw'] == "") {
            //     malert("供货单位不能为空!", 'top', 'defeadted');
            //     return;
			// }

			// if (this.popContent['cdbm'] == undefined || this.popContent['cdbm'] == null || this.popContent['cdbm'] == "") {
            //     malert("产地不能为空!", 'top', 'defeadted');
            //     return;
			// }

			if (this.popContent['pdzcsl'] == undefined || this.popContent['pdzcsl'] == null) {
                malert("账存数量不能为空!", 'top', 'defeadted');
                return;
			}

			if (this.popContent['pdsjkc'] == undefined || this.popContent['pdsjkc'] == null || this.popContent['pdsjkc'] == "" || this.popContent['pdsjkc'] <= 0) {
                malert("实存数量不能为空!", 'top', 'defeadted');
                return;
			}

			var json = {
				ypbm:this.popContent['ypbm'],
				kfbm:wrapper.pdbContent.kfbm,
				xtph:this.popContent['xtph'],
				scph:this.popContent['scph'],
				scrq:this.popContent['scrq'],
				yxqz:this.popContent['yxqz'],
				yplj:this.popContent['yplj'],
				ypjj:this.popContent['ypjj'],
				cdbm:this.popContent['cdbm'],
				ghdw:this.popContent['ghdw'],
				kfdw:this.popContent['kfdw'],
				yfdw:this.popContent['yfdw'],
				fzbl:this.popContent['fzbl'],
				cpbzh:this.popContent['cpbzh'],
				pzwh:this.popContent['pzwh'],
				pdzcsl:this.popContent['pdzcsl'],
				pdsjkc:this.popContent['pdsjkc'],
				ifpdlr:0
			}

			if(!wrapper.editPdlr) {
				json.lrty = '1';
			}

			if(wrapper.editPdlr) {
				json.id = this.popContent['id'];
			}

			$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=updatePdmxYpKc&parm=' + JSON.stringify(json),
				function(data) {
					if(data.a == 0) {
						common.closeLoading()

						if(wrapper.editPdlr) {
							wap.closes();
							wapAddmx.closes();
						}
						wrapper.getPdSC();
						wap.popContent = [];
						wap.inputYpmc = '';
						wap.$refs.pdlrYpmc.focus();
						// malert(data.c, "top",'success');

					} else {
						common.closeLoading()
						malert(data.c, "top",'defeadted');
					}
				});
        },

    }
});

//在当前盘点单上新增明细
var wapAddmx=new Vue({
	el:'#pdxzSide',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data:{
		type:true,
		ghdwList: [], //供货单位
		ypmcInput: '',
		popContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		them_tran: {},
		them: {
			'生产批号': 'scph',
			'药品编号': 'ypbm',
			'药品名称': 'ypmc',
			'规格': 'ypgg',
			'分装比例': 'fzbl',
			'进价': 'ypjj',
			'零价': 'yplj',
			'库房单位': 'kfdwmc',
			'药房单位': 'yfdwmc',
			'药品剂型': 'jxmc'
		}

	},
	mounted:function () {
	},
	watch: {

	},
	methods:{
		//关闭
		closes: function () {
			wrapper.editPdlr = false;
			wapAddmx.popContent = [];
			wapAddmx.ypmcInput = '';
			wapAddmx.type=true

		},
		open: function () {
			wap.type = true;
			wapAddmx.type=false
		},

		//获取供货单位
		getGhdw: function(){
			//初始化页面记载供货单位
			var GhdwDg = {
				page: 1,
				rows: 500,
				sort: "dwbm",
				order: "asc",
				parm: ''
			};

			$.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(GhdwDg),
				function(json) {
					if(json.a == 0) {
						wapAddmx.ghdwList = json.d.list;
					} else {
						malert("供货单位获取失败",'top','defeadted');
					}
				});
		},

		getcdData:function(){
            //初始化页面加载产地编码
            var obj={
                rows:3000,
                sort:'cdbm',
                tybz:'0',
                page:1,
                parm:'',
            }
            this.$nextTick(function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=" + JSON.stringify(obj),
                    function (data) {

                        if (data.a == 0) {
                            wapAddmx.ypcdList = Object.freeze(data.d.list);
                            wapAddmx.$forceUpdate()
                            setTimeout(function () {
                                wapAddmx.$refs.cdbm.setLiShow(wapAddmx.$refs.cdbm.$refs.inputFu)
                            },100)
                        } else {
                            malert("药品产地获取失败!", 'top', 'defeadted');
                        }
                    });
            })
		},
		// remoteMethod:function(value){
		// 	this.getcdData(value)
		// 	},

		//盘点新增
		addNewYp: function () {

			if(wrapper.editPdlr) {
				wap.popContent = wapAddmx.popContent;
				wap.addData();
				wapAddmx.popContent = [];
				wapAddmx.ypmcInput = '';

			}else {
				if (wrapper.pdbContent.kfbm == undefined || wrapper.pdbContent.kfbm == null || wrapper.pdbContent.kfbm == "") {
					malert("库房不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['ypmc'] == undefined || this.popContent['ypmc'] == null || this.popContent['ypmc'] == "") {
					malert("药品名称不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['ypbm'] == undefined || this.popContent['ypbm'] == null || this.popContent['ypbm'] == "") {
					malert("药品编码不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['ypgg'] == undefined || this.popContent['ypgg'] == null || this.popContent['ypgg'] == "") {
					malert("药品规格不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['yplj'] == undefined || this.popContent['yplj'] == null || this.popContent['yplj'] == "") {
					malert("药品零价不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['ypjj'] == undefined || this.popContent['ypjj'] == null || this.popContent['ypjj'] == "") {
					malert("药品进价不能为空!", 'top', 'defeadted');
					return;
				}

				// if (this.popContent['scph'] == undefined || this.popContent['scph'] == null || this.popContent['scph'] == "") {
				// 	malert("生产批号不能为空!", 'top', 'defeadted');
				// 	return;
				// }

				// if (this.popContent['cpbzh'] == undefined || this.popContent['cpbzh'] == null || this.popContent['cpbzh'] == "") {
				//     malert("产品标准号不能为空!", 'top', 'defeadted');
				//     return;
				// }

				// if (this.popContent['pzwh'] == undefined || this.popContent['pzwh'] == null || this.popContent['pzwh'] == "") {
				//     malert("批准文号不能为空!", 'top', 'defeadted');
				//     return;
				// }

				if (this.popContent['scrq'] == undefined || this.popContent['scrq'] == null || this.popContent['scrq'] == "") {
					malert("生产日期不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['yxqz'] == undefined || this.popContent['yxqz'] == null || this.popContent['yxqz'] == "") {
					malert("有效期至不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['ghdw'] == undefined || this.popContent['ghdw'] == null || this.popContent['ghdw'] == "") {
					malert("供货单位不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['cdbm'] == undefined || this.popContent['cdbm'] == null || this.popContent['cdbm'] == "") {
					malert("产地不能为空!", 'top', 'defeadted');
					return;
				}

				if (this.popContent['pdsjkc'] == undefined || this.popContent['pdsjkc'] == null || this.popContent['pdsjkc'] == "") {
					malert("实存数量不能为空!", 'top', 'defeadted');
					return;
				}

				var json = {
					ypbm:this.popContent['ypbm'],
					kfbm:wrapper.pdbContent.kfbm,
					xtph:this.popContent['xtph'],
					scph:this.popContent['scph'],
					scrq:this.popContent['scrq'],
					yxqz:this.popContent['yxqz'],
					yplj:this.popContent['yplj'],
					ypjj:this.popContent['ypjj'],
					cdbm:this.popContent['cdbm'],
					ghdw:this.popContent['ghdw'],
					kfdw:this.popContent['kfdw'],
					yfdw:this.popContent['yfdw'],
					fzbl:this.popContent['fzbl'],
					cpbzh:this.popContent['cpbzh'],
					pzwh:this.popContent['pzwh'],
					pdsjkc:this.popContent['pdsjkc'],
					ifpdlr:1
				}

				$.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=insertPdmx&parm=' + JSON.stringify(json),
					function(data) {
						if(data.a == 0) {
							common.closeLoading()
							wrapper.getPdSC();
							wapAddmx.popContent = [];
							wapAddmx.ypmcInput = '';
							wapAddmx.$refs.pdxzYpmc.focus();

						} else {
							common.closeLoading()
							malert(data.c, "top",'defeadted');
						}
					});
			}


		},

		//药品下拉检索
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'popContent', 'searchCon');
			//选中之后的回调操作
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				this.ypmcInput = this.popContent.ypmc;

				if(type == 'ypmc') {
				}

				if(type == 'ypmc') {
					$('#scsl').focus();
				}
			}
		},

		//当输入值后才触发
		change: function (add, val) {
			this.ypmcInput = val;
			if (!add) this.page.page = 1;       // 设置当前页号为第一页
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			this.page.parm = this.ypmcInput;
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};

			var bean={
				'pdpzh':wrapper.pzNum,
				'kfbm':wrapper.pdbContent.kfbm
			};
			$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(str_param) + "&parm=" + JSON.stringify(bean),
				function (data) {
					if (data.d.list.length > 0) {
						if (add) {
							for (var i = 0; i < data.d.list.length; i++) {
								data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
								data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
								wapAddmx.searchCon.push(data.d.list[i]);
							}
						} else {
							for (var i = 0; i < data.d.list.length; i++) {
								data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
								data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
							}
							wapAddmx.searchCon = data.d.list;
						}
					}
					wapAddmx.page.total = data.d.total;
					wapAddmx.selSearch = 0;
					if (data.d.list.length > 0 && !add) {
						$(".selectGroup").hide();
						_searchEvent.show();
					}
				});
		},

		//鼠标双击
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;  // 设置当前页号
				this.change(true, this.ypmcInput);           // 传参表示请求下一页,不传就表示请求第一页
			} else {
				this.popContent = item;
				this.ypmcInput = item.ypmc;
				$(".selectGroup").hide();
			}
		},
		changeDownDate:function(event, type){
            // if (!event.target.value) {
            //     malert('请输入' + (type == '_yxqz' ? '有效期' : '生产日期'), 'top', 'defeadted');
            //     //触发时间控件
            //     event.target.click();
            //     return;
            // } else {
            //     //激活插件检测校验时间
            //     event.target.blur();
            // }
            // //判断格式
            // var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
            // var regExp = new RegExp(reg);
            // if (!regExp.test(event.target.value)) {
            //     malert("日期格式不正确，正确格式为：YYYY-MM-HH", 'top', 'defeadted');
            //     return;
			// }

			//跳转下一个输入框
			if (event.keyCode == 13) {

				if(type !='pdsjkc'){
					this.nextFocus(event);
				}else{
					wapAddmx.addNewYp();
				}
				if (type == 'ypjj'){

					wapAddmx.popContent.yplj = wrapper.fDec(wapAddmx.popContent.ypjj*wapAddmx.popContent.jcbl/wapAddmx.popContent.fzbl,4);
				}
			}
        },
		changeDate: function (event, type) {

			//选中之后的回调操作
			if(window.event.keyCode == 13) {
				//获取时间控件对象
				var dateObj = document.getElementById(type);
				//获取日期
				var tempDate = dateObj.value;
				//非空判断
				if(tempDate == null || tempDate == undefined || tempDate == '') {
					malert('请输入' + (type == '_yxqz' ? '有效期' : '生产日期'),'top','defeadted');
					//触发时间控件
					dateObj.click();
					return;
				} else {
					//激活插件检测校验时间
					dateObj.blur();
				}
				//判断格式
				var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
				var regExp = new RegExp(reg);
				if(!regExp.test(tempDate)) {
					malert("日期格式不正确，正确格式为：2017-01-01",'top','defeadted');
					return;
				}
				if(type !='pdsjkc'){
									//跳转下一个输入框
					this.nextFocus(event);
				}else {
					wapAddmx.addNewYp();
				}
			}
		},
	}


});

laydate.render({
    elem: '.times1'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        wapAddmx.popContent.scrq = value
    }
});
laydate.render({
    elem: '.times2'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        wapAddmx.popContent.yxqz = value
    }
});

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
wrapper.getYpzlData();
wrapper.getKFData();
wapAddmx.getGhdw();
wapAddmx.getcdData();


//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
	var bol = $(e.target).parents().is(".selectGroup");
	if(!bol) {
		$(".selectGroup").hide();
	}

});



