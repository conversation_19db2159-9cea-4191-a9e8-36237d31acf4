<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>检查执行</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="jczx.css" rel="stylesheet"/>
</head>

<body class="skin-default padd-b-10 padd-r-10 padd-l-10 padd-r-10">
<div class="wrapper background-f" v-cloak>
    <div class="panel tong-top flex-container flex-align-c">
            <button v-waves class="tong-btn btn-parmary" @click="goToPage(1)"><i class="iconfont icon-iocn56 icon-cf"></i>刷新</button>
            <button v-waves class="tong-btn btn-parmary-b" @click="Comply" v-if="complyShow"><i
                    class="iconfont icon-iocn34 icon-c1 icon-font19"></i>执行
            </button>
            <button v-waves class="tong-btn btn-parmary-b" @click="CancelComply" v-if="!complyShow"><i
                    class="iconfont icon-iocn19 icon-c1 icon-font19"></i>取消执行
            </button>
        <div class="flex-container padd-b-10 padd-t-10 padd-l-10">
            <div class="flex-container flex-align-c padd-r-10">
                <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                <input class="zui-input wh120 zui-date text-indent-20" id="timeVal" placeholder="请输入开始时间" type="text"/>
                <div class="top-zinle">~</div>
                <input class="zui-input wh120 zui-date text-indent-20" id="timeVal1" placeholder="请输入结束时间" type="text"/>
            </div>
            <div class="flex-container flex-align-c">
                <span class="ft-14 padd-r-5 whiteSpace">检索</span>
                 <input @keydown.enter="goToPage(1)" v-model="param.parm" class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsVal"/>
            </div>
        </div>
    </div>
    <div class="zui-table-view hzList padd-r-10 padd-l-10">
        <div class="fyxm-tab">
            <div><span :class="{'active':num==0}" @click="tabBg(0)">未执行</span></div>
            <div><span :class="{'active':num==1}" @click="tabBg(1)">已执行</span></div>
        </div>
        <div class="fyxm-size " v-if="num==0">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>项目名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>单价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>金额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>开单医生</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>开单科室</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>收费时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>收费员</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>执行科室</span></div>
                        </th>
                        <!--
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s">操作</div>
                        </th>
                        -->
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index"
                        v-for="(item, $index) in jsonList" @dblclick="edit($index)">
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.mxfyxmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fysl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fydj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fyje"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.mzysxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.mzksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.sqsj,'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.czyxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zxksmc"></div>
                        </td>
                        <!--
                        <td class="cell-s">
                            <div class="zui-table-cell  cell-s">
                               <span class="flex-center">
                                      <em class="width30"><i class="iconfont icon-iocn34 icon-c75 icon-font20 padd-t-2" data-title="执行" @click="Enforce($index)"></i></em>
                               </span>
                            </div>
                        </td>
                        -->
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class=" noData text-center zan-border">暂无数据...</p>-->
            </div>
            <!--左右浮动-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell  cell-s">
                               <span class="flex-center">
                                      <em class="width30"><i class="iconfont icon-iocn34 icon-c75 icon-font20 padd-t-2" data-title="执行" @click="Enforce($index)"></i></em>
                               </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            --->
            <!--end-->
        </div>
        <!--已执行列表-->
        <div class="fyxm-size" v-if="num==1">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>项目名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>单价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>金额</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>开单医生</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>开单科室</span></div>
                        </th>
                        <!--<th>-->
                        <!--<div class="zui-table-cell cell-s"><span>收费时间</span></div>-->
                        <!--</th>-->
                        <th>
                            <div class="zui-table-cell cell-s"><span>收费员</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>执行科室</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>执行人</span></div>
                        </th>
                        <!--
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s">操作</div>
                        </th>
                        -->
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index"
                        v-for="(item, $index) in jsonList" @dblclick="edit($index)">
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.mxfyxmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fysl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fydj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fyje"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.mzysxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.mzksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.czyxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zxksmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.zxsj,'date')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zxry"></div>
                        </td>
                        <!--
                        <td class="cell-s">
                            <div class="zui-table-cell  cell-s">
                               <span class="flex-center">
                                      <em class="width30"><i class="iconfont icon-iocn19 icon-c75 icon-font20 padd-t-2" data-title="取消执行" @click="CanEnforce($index)"></i></em>
                               </span>
                            </div>
                        </td>
                        -->
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
            </div>
            <!--左右浮动-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <!--
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell  cell-s">
                               <span class="flex-center">
                                      <em class="width30"><i class="iconfont icon-iocn19 icon-c75 icon-font20 padd-t-2" data-title="取消执行" @click="CanEnforce($index)"></i></em>
                               </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
           -->
            <!--end-->
        </div>
        <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
              :next-more="nextMore"></page>
    </div>


</div>
<script src="jczx.js" type="text/javascript"></script>
</body>
</html>
