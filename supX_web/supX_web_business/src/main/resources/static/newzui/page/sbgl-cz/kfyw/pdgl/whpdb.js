var toolMenu_1 = new Vue({
	el: '.toolMenu_1',
	mixins: [tableBase],
	data: {
		//要作废的盘点表对象
		delContent: {}
	},
	methods: {
		//刷新判断凭证号列表
		showPdb: function() {
			enter_djList.getWshpdb();
		},
		//作废
		del: function() {
			if(enter_djDetail.jsonList.length == 0) {
				malert("请选择数据！");
				return;
			}
			//准备参数
			//              json = this.delContent;
			var json = {
				'wzkf': this.delContent.wzkf,//库房编码
				'pdpzh': this.delContent.pdpzh,//盘点凭证号
				'qrzfry': userId,//审核人
				'qrzfbz': '2',//审核标志  0-未审核 1-审核 2-作废
			};

			//作废盘点表
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=invalid',
					JSON.stringify(json))
				.then(function(data) {
					if(data.body.a == 0) {
						malert(data.body.c);
						//清空盘点表和盘点表明细
						enter_djList.jsonList = [];
						enter_djDetail.jsonList = [];
					} else {
						malert(data.body.c);
					}
				}, function(error) {
					console.log(error);
				});
		},
	}
});
//盘点表
var enter_djList = new Vue({
	el: '.enter_djList',
	mixins: [tableBase],
	data: {
		//盘点表列表
		jsonList: [],
	},

	methods: {
		//获取盘点表列表
		getWshpdb: function() {
			var kfbm = document.getElementById('_kfbm').value;
			if(kfbm == '') {
				malert('请选择库房');
				return;
			}
			var parm = {
				'wzkf': kfbm,
				'qrzfbz': 0
			};
			//查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryDj&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['pdrq'] = formatTime(json.d[i]['pdrq'], 'date');
					}
					if(json.d.length == 0) {
						malert("请先生成盘点表");
					}
					enter_djList.jsonList = json.d;

				} else {
					malert('数据获取失败！')
				}

			});
		},

		//获取盘点表明细
		showDetail: function(num) {
			//准备参数
			var parm = enter_djList.jsonList[num];
			//为作废准备参数
			toolMenu_1.delContent = enter_djList.jsonList[num];

			//查询盘点表明细
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryMx&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['scrq'] = formatTime(json.d[i]['scrq'], 'date');
						json.d[i]['yxqz'] = formatTime(json.d[i]['yxqz'], 'date');
						json.d[i]['ljje'] = Math.round(json.d[i]['yplj'] * json.d[i]['kcsl'] * 100) / 100;

					}
					enter_djDetail.jsonList = json.d;
				} else {
					malert('数据获取失败！')
				}

			});
		}

	},

	//加载后获取盘点表列表
	mounted: function() {
		this.getWshpdb();
	},
});
//盘点表明细
var enter_djDetail = new Vue({
	el: '.enter_djDetail',
	mixins: [tableBase],
	data: {
		jsonList: []
	},
	methods: {}
});