var wrapper=new Vue({
    el:'#wrapper',
    components: {
        'search-table': searchTable
    },
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        which:0,
        isShow:true,
        eye:true,
        pkhShow:false,
        disabled:false,
        shbzStatus:false,
        jsonList:[],
        jsonMxList:[],
        rkdList:[],
        ypjx:[],
        item:{},
        ajaxStop:true,
        popContent:{
            fs:'4',
            iskc:'0',
            jxAll:'1',
            tzrq:'',
            jxNews:[],
            ypzlNews:[],
			dsybz:'1',
        },
        modelText:[{text:'基本'},{text:'药品分类'},{text:'药品名称'}],
        parama:{
            rows:100000,
        },
        params:{
            page: 1,
            rows: 100000,
            sort: '',
            zt:'0',
            order: 'asc'
        },
        them: {
            '药品名称': 'ypmc', '药品规格': 'ypgg', '产地': 'cdmc', '供货单位': 'ghdwmc',
            '库存': 'kcsl','有效期':'yxqz',
        },
        them_tran: {},
        queryPage: {
            page: 1,
            rows: 10,
            total: null
        },
        pddh:'',
        money:{
            pdjec:0,//金额差合计
            pdjehj:0,//盘点金额合计
            pdjecb:0,//盘点成本金额合计
        },
        searchCon:[],
        selSearch:-1,
        table:[
			{text:'老编码',className:'cell-s',field:'lbm'},
			{text:'药品编码',className:'cell-s',field:'ypbm'},
			{text:'批号',className:'cell-s',field:'scph'},
            {text:'商品名',className:'cell-s',field:'ypspm'},
            {text:'规格',className:'cell-s',field:'ypgg'},
            {text:'产地',className:'cell-s',field:'cdmc',field1:'ycd'},
            {text:'单位',className:'cell-s',field:'kfdwmc'},
            {text:'有效期',className:'cell-s',field:'yxqz'},
			{text:'分装比例',className:'cell-s',field:'fzbl'},
            {text:'账面数量',className:'cell-s',field:'kkcsl',field1:'zcsl'},
            {text:'当前库存',className:'cell-s',field:'kkcsl',field1:'scsl'},
            {text:'标志',className:'cell-s',field:'bz'},
            {text:'数量差',className:'cell-s',field:'slc',},
            {text:'成本价',className:'cell-s',field:'ykjj'},
            {text:'售价',className:'cell-s',field:'yklj',},
            {text:'盘点金额',className:'cell-s',field:'pdje'},
            {text:'盘点成本金额',className:'cell-s',field:'pdcbje'},
			{text:'账面数量(药房)',className:'cell-s',field:'kcsl'},
			{text:'当前库存(药房)',className:'cell-s',field:'kcsl'},
			{text:'成本价(药房)',className:'cell-s',field:'ypjj'},
			{text:'售价(药房)',className:'cell-s',field:'yplj',},
			{text:'盘点金额(药房)',className:'cell-s',field:'ypdje'},
			{text:'盘点成本金额(药房)',className:'cell-s',field:'ypdcbje'},
			{text:'供货单位',className:'cell-s',field:'ghdwmc'},
			
        ],
    },
    updated:function (){
        changeWin();
        this.dragTd()
    },
    computed:{
        initJe:function (){
            var that=this;
			
			var tppdjec = 0.0;
			var tppdjecb = 0.0;
			if(this.jsonMxList && this.jsonMxList.length>0){
				this.jsonMxList.forEach(function (item){
					/*
					item.ypdje = that.fDec((parseFloat(item.kcsl) * parseFloat(item.yplj)),2);
					item.ypdcbje = that.fDec((parseFloat(item.kcsl) * parseFloat(item.ypjj)),2);
					tppdjec=tppdjec-1+1+parseFloat(wrapper.fDec(parseFloat(item.kcsl) * parseFloat(item.yplj),2));
				    tppdjecb=tppdjecb-1+1+parseFloat(wrapper.fDec(parseFloat(item.kcsl) * parseFloat(item.ypjj),2));
				    */
				   item.ypdje = wrapper.MathFun(wrapper.accMul(item.kcsl,item.yplj));
				   item.ypdcbje = wrapper.MathFun(wrapper.accMul(item.kcsl,item.ypjj));
				   tppdjec = wrapper.dcmAdd(tppdjec,item.ypdje);
				   tppdjecb = wrapper.dcmAdd(tppdjecb,item.ypdcbje);
				
				})
				that.money.pdjec = tppdjec
				that.money.pdjecb =tppdjecb
			}
            
        },
    },
    mounted:function (){
        //$.ajaxSettings.async = false;
        this.getJx();
        this.getKfData();
        //this.getData();
        //$.ajaxSettings.async = true;
        this.popContent.tzrq=this.fDate(new Date(),'date')
        Mask.newMask(this.MaskOptions('timeVal12','YYYY-DD-MM'));
    },
    methods:{
		setJx:function(){
			for (let i = 0; i < wrapper.ypjx.length; i++) {
			    Vue.set(wrapper.popContent.jxNews,i,wrapper.ypjx[i].jxbm)
			}
		},
		deleteThis:function( key, type, index){
			common.openConfirm("确认删除该条信息吗？", function () {
				wrapper.jsonMxList.splice(index, 1);
			})
		},
        printFun:function (){
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            var pddh=this.jsonList[this.activeIndex]['pddh'];
            var reportlets = "[{reportlet: 'yfgl" + frpath + "18_pdb_yf.cpt',pddh:'" + pddh + "',format:'excel',extype:'simple'}]";
            if (!FrPrint(reportlets, null, null, true)) {
                window.print();
            }
        },
        filterFun:function (item,child){
            return item[child.field] ? item[child.field] : item[child.field1] ? item[child.field1] : item[child.field]
        },
        resultChange:function (val){
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            this.getData()
        },
        cancel:function (){
            this.isShow=true;
            this.pddh='';
            this.jsonMxList=[];
            this.shbzStatus=false;
            this.disabled=false;
            this.getData()
        },
        shrFun:function (item,eye){
            this.isShow=false;
            this.shbzStatus=true;
            this.disabled=true;
            this.eye=eye;
            this.popContent.pddh=item.pddh;
            this.params.pddh=item.pddh;
            $('#divlink').remove()
            this.queryMx()
        },
        showDetail:function (){//审核
            if(!this.jsonMxList.length){
                malert("没有需要审核的数据", 'top', 'defeadted');
                return false;
            }
            this.popContent.shry=userId;
            this.popContent.shryxm=userName;
            this.popContent.shbz='1';
            this.popContent.shrq=this.fDate(new Date(),'datatime');
            var json = '{"list":' + JSON.stringify(this.popContent) + '}';
            this.xmlAjax(json);
        },
        invalidData:function (item){
            this.popContent.zfry=userId;
            this.popContent.zfryxm=userName;
            this.popContent.shbz='2';
            this.popContent.pddh=item.pddh;
            this.popContent.zfrq=this.fDate(new Date(),'datatime');
            var json = '{"list":' + JSON.stringify(this.popContent) + '}';
            this.xmlAjax(json);
        },
        xmlAjax:function (json){
            this.postAjax('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=pdshrkd', json,function (data) {
                if (data.a == 0) {
                    malert( data.c, 'top');
                    this.cancel()
                } else {
                    wrapper.isSubmited=false;
                    malert("保存失败:" + data.c, 'top', 'defeadted');
                }
            }, function (error) {
                wrapper.isSubmited = false;
            });
        },
        queryMx:function (){//查询明细
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=queryPddMx&parm=" + JSON.stringify(this.params), function (json) {
                if (json.a == "0") {
					for (let i = 0; i < json.d.list.length; i++) {
						let tpykjj = json.d.list[i].ykjj
						json.d.list[i].ykjj = wrapper.fDec(tpykjj.substr(0,tpykjj.indexOf("(")),2)+"("+json.d.list[i].kfdwmc+")";
					}
                    wrapper.jsonMxList = json.d.list;
                }
            });
        },
        resultRydjChange: function (val) {
            Vue.set(this.params, 'yfbm', val[0]);
            Vue.set(this.params, 'yfmc', val[4]);
            this.getData();
        },
        getData:function (){//查询单号
            if (this.params.zt != '9') {
                this.params.shbz = this.params.zt;
            } else {
                this.params.shbz = null;
            }
            //this.params.yfbm = wrapper.params.yfbm;
            //this.params.yfmc = wrapper.params.yfmc;
            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=queryYkypd&parm=" + JSON.stringify(this.params), function (json) {
                if (json.a == "0") {
                    wrapper.jsonList = json.d.list;
                }
            });
        },
        setPdh:function (pddh){
            this.jsonMxList=this.spliceFun(this.jsonMxList,'ypbm')
            this.jsonMxList.forEach(function (item,index){
                item.pddh=pddh;
				
                item.zcsl=item.kcsl;
				item.spsl=item.kcsl;
                item.scsl=item.kcsl || item.scsl;
                item.yljgbm=jgbm;
                item.mxxh=index;
                item.yxqz=wrapper.fDate(item.yxqz,'datetime');
                item.pdje=wrapper.fDec(parseFloat(item.kcsl) * parseFloat(item.yplj),4);
            })
            if (this.jsonMxList.length <= 50) {
                this.saveMx(this.jsonMxList);
                this.cancel()
            } else {
                for (var j = 0; j < this.jsonMxList.length; j += 50) {
                    var xjsonlist = this.jsonMxList.slice(j, j+ 50);
                    this.saveMx(xjsonlist)
                }
                if(this.ajaxStop){
                    this.cancel()
                }else {
                    this.ajaxStop=true;
                    malert("盘点出错,请重新保存", 'top', 'defeadted');
                }
                common.closeLoading()
            }
        },
        resultFun:function (){
            this.pkhShow=false;
            this.cancel()
        },
        saveMx:function (xjsonlist){//保存明细
            if(!this.ajaxStop){
                console.error('盘点出错，暂停执行请求');
                return false;
            }
            common.openloading('','正在盘点，请稍后')
            var json = '{"list":' + JSON.stringify(xjsonlist) + '}';
            this.postAjax('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=insertPddmx', json,function (data) {
                if (data.a == 0) {
                    this.ajaxStop=true;
                    this.isSubmited=false;
                } else {
                    this.ajaxStop=false;
                    this.isSubmited=false;
                    malert("保存失败:" + data.c, 'top', 'defeadted');
                }
            }, function (error) {
                wrapper.isSubmited = false;
            });
        },
        submitAll:function (){
            if(this.pddh){
                wrapper.setPdh(this.pddh);
                return  false;
            }
            this.popContent.yljgbm=jgbm
            this.isSubmited = true;
            var json = '{"list":' + JSON.stringify(this.popContent) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=insertPdd', json).then(function (data) {
                if (data.body.a == 0) {
                    wrapper.isSubmited=false;
                    wrapper.pddh=data.body.d
                    wrapper.setPdh(data.body.d)
                } else {
                    wrapper.isSubmited=false;
                    malert("保存失败,请重新保存", 'top', 'defeadted');
                }
            }, function (error) {
                wrapper.isSubmited = false;
            });
        },
        getPdData:function (){
            common.openloading('','正在生成的盘点')
            this.popContent.jxbm=this.popContent.jxNews.join(',');
            this.popContent.zlbm=this.popContent.ypzlNews.join(',');
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1YfbKcglRkgl&types=queryYfKc&parm=" + JSON.stringify(this.popContent), function (json) {
                if (json.a == "0") {
                    common.closeLoading();
                    wrapper.pkhShow=false;
                    if(json.d && json.d.list.length>0){
                        wrapper.jsonMxList = wrapper.initList(json.d.list);
                    }else {
                        malert('未生成盘点数据','top','defeadted');
                        
                    }
                }else {
                    malert('失败','top','defeadted');
                    common.closeLoading();
                }
            },function (){
                common.closeLoading();
            });
        },
        initList:function (list){
			
            var that=this;
            list.forEach(function (item){
                item.yxqz=wrapper.fDate(item.yxqz,'datetime');
                item.bz='平';
                item.slc=0;
				item.ycd = item.cdmc
				var kkcsl = item.kcsl+"";
				item.kkcsl = that.fDec(item.kcsl/item.fzbl,4);
				
                item.pdje=that.fDec((parseFloat(item.kkcsl) * parseFloat(item.yklj)),2);
                item.pdcbje=that.fDec((parseFloat(item.kkcsl) * parseFloat(item.ykjj)),2);
				
				item.ykjj = item.ykjj+"("+item.kfdwmc+")"
				item.ypdje = that.fDec((parseFloat(item.kcsl) * parseFloat(item.yplj)),2);
				item.ypdcbje = that.fDec((parseFloat(item.kcsl) * parseFloat(item.ypjj)),2);
				
            })
            return list
        },
        getKfData: function () {
            //下拉框药房加载
                this.param.rows=20000;
                // this.updatedAjax("/actionDispatcher.do?reqUrl=GetDropDown&types=yf&dg="+JSON.stringify(this.param),function (json) {
                //     this.yfkfList = json.d.list;
                //     this.params.yfbm=json.d.list[0]['yfbm']
                //     this.params.yfmc=json.d.list[0]['yfmc']
                //     this.$forceUpdate()
                // });
            //获取列表

            var parm = {
                "ylbm": 'N040100021006',
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0 && data.d) {
                        wrapper.yfkfList = data.d.list;
                        if (data.d.list.length > 0) {
                            wrapper.params.yfbm = data.d.list[0]['yfbm'];
                            wrapper.params.yfmc = data.d.list[0]['yfmc'];
                            wrapper.$forceUpdate();
                            wrapper.getData();
                        }
                    } else {
                        malert("药房获取失败", 'top', 'defeadted');
                    }
                });

        },
        tabBg:function (index){
            this.which=index;
        },
        getReCheckAll:function (val){
            Vue.set(this.popContent,val[0][1],val[1])
            if(val[1] == '1'){
                for (let i = 0; i < this.ypjx.length; i++) {
                    Vue.set(this.popContent.jxNews,i,this.ypjx[i].jxbm)
                }
            }else {
                this.popContent.jxNews=[];
            }
        },
        getReCheckYpAll:function (val){
            Vue.set(this.popContent,val[0][1],val[1])
            if(val[1] == '1'){
                for (let i = 0; i < this.ypzl.length; i++) {
                    Vue.set(this.popContent.ypzlNews,i,this.ypzl[i].ypzlbm)
                }
            }else {
                this.popContent.ypzlNews=[];
            }
        },
        getReCheckOneNew:function (item,index,code,bm){
            if(this.popContent[code][index]){
                Vue.set(this.popContent[code],index,'')
            }else {
                Vue.set(this.popContent[code],index,item[bm])
            }
        },
		getReCheckOne:function(){
			if(this.popContent.dsybz){
			    Vue.set(this.popContent,'dsybz','')
			}else {
			    Vue.set(this.popContent,'dsybz','1')
			}
			
		},
        getJx:function () {
            var pageParam = {
                'page': 1,
                'rows': 500
            }
            // 查询药品剂型
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpjx&types=query&dg=" + JSON.stringify(pageParam), function (json) {
                if (json.a == '0' && json.d) {
                    wrapper.ypjx = json.d.list;
					wrapper.setJx();
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
            });

            // 查询药品种类
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl&dg=' + JSON.stringify(pageParam), function (json) {
                if(json.a== '0' && json.d) {
                    wrapper.ypzl = json.d.list
                }else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
        },
        kdFun:function (){
            this.popContent.yfmc=this.params.yfmc
            this.popContent.yfbm=this.params.yfbm;
            this.popContent.tzry=userId;
            this.popContent.tzryxm=userName;
            this.popContent.tzrq=this.fDate(new Date(),'date');
            this.pkhShow=true;
            this.isShow=false;
        },
        addFun:function (){
            if(this.jsonMxList[this.jsonMxList.length-1]['ypbm']){
                this.jsonMxList.push({})
            }
        },
        showTime: function (element, code) {
            var  code =code.split('.');
            laydate.render({
                elem: '#' + element,
                show: true,//直接显示
                type: 'date'
                , theme: '#1ab394',
                done: function (value, data) {
                    Vue.set(wrapper[code[0]], code[1], value);
                }
            });
            this.$forceUpdate();
        },
        Wf_change: function (add, index, val) {
            this.item = {};
            dqindex = index;
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            // var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var _searchEvent = $("#ypmc" + index + " + div");
            this.jsonMxList[index].ypbm = null;
            this.jsonMxList[index].ypmc = null;
            this.jsonMxList[index]['ypmc'] = val;
            this.queryPage.parm = trimStr(encodeURIComponent(val));
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(this.queryPage), function (data) {
                for (var n = 0; n < data.d.list.length; n++) {
                    if (add) {//不是第一页则需要追加
                        wrapper.searchCon = wrapper.searchCon.concat(data.d.list)
                    } else {
                        wrapper.searchCon = data.d.list;
                    }
                    wrapper.page.total = data.d.total;
                    wrapper.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                }
            });
        },
        changeDown: function (index, event, searchCon) {
            //赋值
            ypIndex = index;
            this.inputUpDown(event, this[searchCon], 'selSearch');
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                this.item = this[searchCon][this.selSearch]
                this.jsonMxList[index] = this.item;
                this.$forceUpdate();
                this.selSearch = -1;
                $(".selectGroup").hide();
                this.addFun()
                this.$nextTick(function (){
                    this.nextFocus(event);
                })
            }
        },
        //双击选中下拉table
        selectOne1: function (item) {
            //查询下页
            if (item == null) {
                this.queryStr.page++;
                this.Wf_change(true, dqindex,this.item['ypmc'])
                return;
            }
            this.jsonMxList[dqindex]  = item;
            this.selSearch = -1;
            this.addFun()
            $(".selectGroup").hide();
        },
    },
})
