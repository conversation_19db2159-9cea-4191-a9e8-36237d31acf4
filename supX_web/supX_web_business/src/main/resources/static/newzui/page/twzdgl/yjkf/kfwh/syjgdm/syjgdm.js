var socket;
var wsImpl = window.WebSocket || window.MozWebSocket;
var tableInfo = new Vue({
    el: '.wrapper',
    //混合js字典庫
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    components: {
        'search-table': searchTable
    },
    data: {
        popContent:{},
        value:'dsds',
        jsonList: [],
        ypbmList: [],
        zlxmList: [],
        rybmList: [],
        bxbmList: [],
        isChecked: [],
        isCheckAll: null,
        sbjgbh:null,
        ksVal: null,
        hoverIndex1: undefined,
        activeIndex1: undefined,
        falg:true,
        falg1:true,
        username: 'username',
        num: 0,
        bxlbbm:null,
        bxurl:null,
        dwybUrl:null,
        param: {
            page: 1,
            rows: 10,
            sort: '',
            parm: '',
            time: 10000,
            order: 'asc',
            sfdm:'qb',
            type:'all',
        },
        page:{
            page: 1,
            rows: 10,
            sort: '',
            parm: '',
            order: 'asc'
        },
        type_tran:{
            'all':'全部',
            '0':'启用',
            '1':'停用',
        },
        scbz_tran:{
            '1':'未上传',
            '0':'已上传',
        },
        sclx_tran:{
            '1':'新增',
            '0':'更新',
        },
        flag:'1',
        type:'all',
        isConnected:false,
        requestParameters:{},
        qjIndex: null,
        isEdit: null,
        jsonRightList:'ypbmList',
        searchCon: {},
        selSearch: -1,
        dg: {page: 1, rows: 20, sort: "", order: "asc", parm: ""},
        them_tran: {},
        them: {'类型':'lx','项目编码': 'ybbm', '项目名称': 'ybmc','剂型':'jx', '项目规格': 'gg','国药准字':'pzwh','生产单位':'scdw', '自付比例': 'lbmc','审核状态':'shzt'},
    },
    updated:function () {
        changeWin()
    },
    created:function(){
        this.getbxlb();
    },
    mounted:function () {

    },
    methods: {


        commonResultChange:function(val){
            var type = val[2][1];
            switch (type) {
                case "type":
                    Vue.set(this.param, 'type', val[0]);
                    this.getData();
                    break;
                case "sfdm":
                    Vue.set(this.param, 'sfdm', val[0]);
                    this.getData();
                    break;
                case "scbz":
                    Vue.set(this.param, 'scbz', val[0]);
                    Vue.set(this, 'flag', val[0]);
                    this.getData();
                    break;
            }

            this.type = val[0];
            this.$forceUpdate();
            this.getData();
        },
        getData:function(){
            if(this.num == 0){
                this.param.tybz = this.param.type;
                this.jsonRightList= "ypbmList";
                this.getYnypxm();
            }else if(this.num == 1){
                this.param.tybz = this.param.type;
                this.jsonRightList = "zlxmList";
                this.getYnfyxm();
            }
        },

        uploadYpxx:function(){//上传药品

            if(this.isChecked.length <= 0){
                malert("请选择要上传的数据！","bottom","defeadted");
                return;
            }
            var uploadList = [];
            for (var i = 0; i < this.isChecked.length; i++) {
                if(this.isChecked[i]){
                    uploadList.push(tableInfo.ypbmList[i]);
                }
            }
            tableInfo.requestParameters = {};
            tableInfo.requestParameters.add_yyxm_info_all = uploadList;
            socket.send(JSON.stringify(tableInfo.requestParameters));
        },
        uploadZlxx:function(){//上传诊疗
            if(this.isChecked.length <= 0){
                malert("请选择要上传的数据！","bottom","defeadted");
                return;
            }
            var uploadList = [];
            for (var i = 0; i < this.isChecked.length; i++) {
                if(this.isChecked[i]){
                    uploadList.push(tableInfo.zlxmList[i]);
                }
            }
            tableInfo.requestParameters = {};
            tableInfo.requestParameters.add_yyxm_info_all = uploadList;
            socket.send(JSON.stringify(tableInfo.requestParameters));
        },

        uploadYsbm:function(){//上传医师

            if(this.isChecked.length <= 0){
                malert("请选择要上传的数据！","bottom","defeadted");
                return;
            }
            if(!tableInfo.sbjgbh){
                malert("请选择社保机构！","bottom","defeadted");
                return;
            }

            var uploadList = [];
            for (var i = 0; i < this.isChecked.length; i++) {
                if(this.isChecked[i]){
                    tableInfo.rybmList[i].flag = tableInfo.flag;
                    tableInfo.rybmList[i].sbjgbh = tableInfo.sbjgbh;
                    uploadList.push(tableInfo.rybmList[i]);
                }
            }
            tableInfo.requestParameters = {};
            tableInfo.requestParameters.add_ys = uploadList;
            socket.send(JSON.stringify(tableInfo.requestParameters));
        },
        tabBg: function (index) {
            this.num = index;
            this.param.parm = '';
            this.activeIndex=undefined;
            this.hoverIndex=undefined;
            this.hoverIndex1=undefined;
            this.activeIndex1=undefined;
            wapse.closes();
            this.isChecked = [];
            this.isCheckAll = null;
            this.param.page = 1;
            this.param.rows = 10;
            this.getData();
        },
        //院内药品项目
        getYnypxm:function(){
            var inParm = {
                yljgbm:jgbm,
                types:'baseData',
                method:'getYnypxm',
                czybm:userId,
                bxlbbm:tableInfo.bxlbbm,
                inJson:JSON.stringify(this.param)
            };
            tableInfo.postAjax("http://192.168.1.201:10002/jsgapi/syjgdm/post",
                JSON.stringify(inParm), function (data) {
                    tableInfo.ypbmList = [];
                    if (data.returnCode == '0') {
                        var res = JSON.parse(data.outResult);
                        if(res && res.list && res.list.length > 0){
                            tableInfo.totlePage = Math.ceil(res.total / tableInfo.param.rows);
                            tableInfo.ypbmList = res.list;
                        }
                    } else {
                        malert(data.msgInfo,"bottom","defeadted");
                        return;
                    }
                }, function (error) {
                    malert(error, "bottom", "defeadted");
                });
        },
        //院内费用项目
        getYnfyxm:function(){
            var inParm = {
                yljgbm:jgbm,
                types:'baseData',
                method:'getYnfyxm',
                czybm:userId,
                bxlbbm:tableInfo.bxlbbm,
                inJson:JSON.stringify(this.param)
            };
            tableInfo.postAjax("http://192.168.1.201:10002/jsgapi/syjgdm/post",
                JSON.stringify(inParm), function (data) {
                    tableInfo.zlxmList = [];
                    if (data.returnCode == '0') {
                        var res = JSON.parse(data.outResult)
                        if(res && res.list && res.list.length > 0){
                            tableInfo.totlePage = Math.ceil(res.total / tableInfo.param.rows);
                            tableInfo.zlxmList = res.list;
                        }
                        malert(data.msgInfo,"bottom","success");
                    } else {
                        malert(data.msgInfo,"bottom","defeadted");
                    }
                }, function (error) {
                    malert(error, "bottom", "defeadted");
                });
        },
        searchHc: function () {
            this.getData()
        },
        //更新院内项目
        updateYnxm:function(){
            if(tableInfo.num == 1){
                tableInfo.updateYnzlxm();
            }else{
                tableInfo.updateYnypxm()
            }
        },
        updateYnypxm:function(){
            var inParm = {
                yljgbm:jgbm,
                types:'baseData',
                method:'updateYnypxm',
                czybm:userId,
                bxlbbm:tableInfo.bxlbbm,
                inJson:JSON.stringify(this.param)
            };
            tableInfo.postAjax("http://192.168.1.201:10002/jsgapi/syjgdm/post",
                JSON.stringify(inParm), function (data) {
                    if (data.returnCode == '0') {
                        malert(data.msgInfo,"bottom","success");
                        tableInfo.getYnypxm();
                    } else {
                        malert(data.msgInfo,"bottom","defeadted");
                    }
                }, function (error) {
                    malert(error, "bottom", "defeadted");
                });
        },
        updateYnzlxm:function(){
            var inParm = {
                yljgbm:jgbm,
                types:'baseData',
                method:'updateYnzlxm',
                czybm:userId,
                bxlbbm:tableInfo.bxlbbm,
                inJson:JSON.stringify(this.param)
            };
            tableInfo.postAjax("http://192.168.1.201:10002/jsgapi/syjgdm/post",
                JSON.stringify(inParm), function (data) {
                    if (data.returnCode == '0') {
                        malert(data.msgInfo,"bottom","success");
                        tableInfo.getYnfyxm();
                    } else {
                        malert(data.msgInfo,"bottom","defeadted");
                        return;
                    }
                }, function (error) {
                    malert(error, "bottom", "defeadted");
                });
        },
        // 请求保险类别
        getbxlb: function () {
            var param = {bxjk: "011"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        tableInfo.bxlbbm = json.d.list[0].bxlbbm;
                        tableInfo.getYnypxm();
                    }
                } else {
                    malert("保险类别查询失败!" + json.c,"bottom","defeated")
                }
            });
        },

        edit: function (index) {
            this.isEdit = index;
            this.$nextTick(function () {
                $("#mc_" + index).focus();
            })
        },
        // 输入内容进行检索
        searching: function (index, add, type, val) {
            this.qjIndex = index;
            this[this.jsonRightList][index]['ybmc'] = val;
            this[this.jsonRightList][index]['ybbm'] = '';
            this[this.jsonRightList][index]['mc'] = '';
            this[this.jsonRightList][index]['fw'] = '';
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.popContent = {};
            tableInfo.dg['parm'] = val;
            tableInfo.dg['lx'] = '0';
            if(tableInfo.num == '1'){
                tableInfo.dg['lx'] = '1';
            }

            var inParm = {
                yljgbm:jgbm,
                types:'baseData',
                method:'querySyxm',
                czybm:userId,
                bxlbbm:tableInfo.bxlbbm,
                inJson:JSON.stringify(tableInfo.dg)
            };
            tableInfo.postAjax("http://192.168.1.201:10002/jsgapi/syjgdm/post",
                JSON.stringify(inParm), function (data) {
                    if (data.returnCode == '0' && data.outResult) {
                        var res = JSON.parse(data.outResult);
                        if (add) {
                            for (var i = 0; i < res.list.length; i++) {
                                tableInfo.searchCon.push(res.list[i]);
                            }
                        } else {
                            tableInfo.searchCon = res.list;
                        }
                        tableInfo.page.total = res.total;
                        if (res.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert(data.msgInfo,"bottom","defeadted");
                    }
                }, function (error) {
                    malert(error, "bottom", "defeadted");
                });
        },
        changeDown: function (index, event, type) {
            this.inputUpDown(event,'searchCon','selSearch')
            this.popContent=this['searchCon'][this.selSearch]
            if (event.code == 'Enter' || event.keyCode == 13 || event.keyCode=='NumpadEnter') {
                if(this.popContent){
                    this.popContent.ynxmbm = this[this.jsonRightList][this.qjIndex].yyxmbm;
                    this.popContent.ynxmmc = this[this.jsonRightList][this.qjIndex].yyxmmc;
                    var saveObj = Object.assign(this[this.jsonRightList][this.qjIndex],this.popContent);
                    this.save(saveObj);
                    this.nextFocus(event);
                    this.selSearch=-1;
                    $(".selectGroup").hide();
                }
            }
        },
        // 点击进行赋值的操作
        selectOne: function (item, index) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(tableInfo.qjIndex, true, 'ybmc', tableInfo[this.jsonRightList][tableInfo.qjIndex].ybmc); // 传参表示请求下一页,不传就表示请求第一页
            } else {   // 否则就是选中事件,为json赋值
                this.popContent = item;
                item.ynxmbm = this[this.jsonRightList][this.qjIndex]['yyxmbm'];
                item.ynxmmc = this[this.jsonRightList][this.qjIndex]['yyxmmc'];
                var saveObj = Object.assign(this[this.jsonRightList][this.qjIndex],item)
                this.save(saveObj);
                $(".selectGroup").hide();
            }
        },
        // 保存项目详情
        save: function (item) {
            var method = "saveYpxm";
            if(tableInfo.num == 1){
                method = "saveZlxm";
            }else{
                method = "saveYpxm";
            }
            var inParm = {
                yljgbm:jgbm,
                types:'baseData',
                method:method,
                czybm:userId,
                bxlbbm:tableInfo.bxlbbm,
                inJson:JSON.stringify(item)
            };
            tableInfo.postAjax("http://192.168.1.201:10002/jsgapi/syjgdm/post",
                JSON.stringify(inParm), function (data) {
                    if (data.returnCode == '0') {
                        malert(data.msgInfo,'bottom','success');
                        tableInfo[tableInfo.jsonRightList] = [];
                        tableInfo.getData();
                    } else {
                        malert(data.msgInfo,"bottom","defeadted");
                    }
                }, function (error) {
                    malert(error, "bottom", "defeadted");
                });
        },
    }
});

var wapse = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase],
    data: {
        sj: '',
        titles: '',
        index: 1,
        jsm: '',
        ybh: '',
        addCs: '',
        centent: '',
        cents: '',
        isFold: false,
        ifClick: true,
        title: '',
        popContent: {},
    },
    created: function () {
        this.qy = this.stopSign[0];
    },
    methods: {
        loadNum:function(){
            wapse.index = 0;
        },
        //取消
        closes: function () {
            wapse.index = 1

        },
        saveData: function () {

        },
    },
});
//初始化页面需要加载的数据
$(window).resize(function () {
    changeWin();
});


$('body').click(function () {
    tableInfo.isEdit = null;
    $(".selectGroup").hide();
});
