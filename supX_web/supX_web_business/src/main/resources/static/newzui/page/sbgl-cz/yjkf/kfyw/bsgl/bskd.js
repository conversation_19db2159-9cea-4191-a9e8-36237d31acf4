var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        WzrkPrint:'',
        bskdhc:{},//缓存报损开单
        popContent: {},
        queryData: {},
        kfList: [],
        ryList: [],
        mxList: [],
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        this.initial();
        this.getCgry();
        this.getGhdw();
    },
    methods: {
        //打印
        dy(){
            var ckdh =this.popContent.ckdh;
            // var yljgbm =jgbm;
            var kfbm = this.pdbSelected.wzkf;
            var reportlets = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                reportlets = "[{reportlet: 'wzdy%2Frcyw%2Fbskdmx.cpt',yljgbm:'"+jgbm+"',ckdh:'"+ckdh+"',kfbm'"+kfbm+"'}]";
            } else {
                reportlets = "[{reportlet: 'wzdy/rcyw/bskdmx.cpt',yljgbm:'"+jgbm+"',ckdh:'"+ckdh+"',kfbm'"+kfbm+"'}]";
            }
            //帆软打印
            if (!FrPrint(reportlets, wrapper.WzrkPrint)) {
            }

        },


        //审核
        sh: function () {
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywBsd&types=confirm', JSON.stringify(this.popContent))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("审核成功！", 'top', 'success');
                        wrapper.cancel()
                        // malert("审核成功！")
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
        // 提交所有
        submitAll: function () {
            var bsd = {}
            Vue.set(bsd, 'sbkf', this.popContent.kfbm);//库房编码
            Vue.set(bsd, 'lyr', this.popContent.rybm);//领用人
            Vue.set(bsd, 'bzms', this.popContent.bzms);//备注描述
            Vue.set(bsd, 'ckfs', '03');
            var wzkfbms = this.listGetName(this.kfList, this.popContent.kfbm, 'sbkfbm', 'ksbm');
            console.log(wzkfbms);
            //新增操作
            var obj = {
                list: {
                    ckd: bsd,
                    ckdmx: this.mxList,
                    wzkf: wzkfbms
                }
            }
            if(JSON.stringify(wrapper.bskdhc)=='{}'){
                wrapper.bskdhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
            }else {
                if (JSON.stringify(wrapper.bskdhc) == JSON.stringify(obj)) {
                    malert("报损单已经保存请不要重复提交!", 'top', 'defeadted');
                    return false;
                } else {
                    wrapper.bskdhc = JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
                }
            }

            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywBsd&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.cancel()
                        malert("保存成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        // 取消2018/07/09取消回退上一级页面
        cancel: function () {
            // malert('取消','top','defeadted')
            this.topClosePage('page/sbgl/yjkf/kfyw/bsgl/bskd.html', 'page/sbgl/yjkf/kfyw/bsgl/bsgl.html');
            window.top.$("#报损管理")[0].contentWindow.getData();
        },
        // 编辑
        edit: function (index) {
            pop.title = '编辑设备'
            pop.popContent = this.mxList[index];
            pop.open();
        },
        // 删除2018/07/09二次弹窗删除提示
        remove: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.mxList.splice(index, 1);
            })) {
                return false;
            }
            // kd.mxList.splice(index, 1);
        },
        // 新增
        AddMdel: function () {
            pop.title = '添加设备'
            pop.open();
            pop.popContent = {};
        },
        //初始化
        initial: function () {
            this.queryData = JSON.parse(sessionStorage.getItem('obj'));
            this.kfList = this.queryData.kfList;
            if (this.queryData.ckd) {
                //审核
                console.log( this.queryData.kfList[0].sbkfbm);
                console.log( this.queryData.sbkf );
                this.popContent = Object.assign(this.popContent,this.queryData.ckd);
                this.popContent.kfbm = this.queryData.sbkf || this.queryData.kfList[0].sbkfbm;
                Vue.set(this.popContent, 'rybm', this.popContent.lyr);
                this.getMx();
            }
        },
        getMx: function () {
            var obj = {
                ckdh: this.queryData.ckd.ckdh,
                sbkf: this.popContent.kfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywBsd&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 &&  data.d.length !=0) {
                        wrapper.mxList = data.d
                        // for (var  i = wrapper.mxList.length - 1; i >=0; i--) {
                        //     if(wrapper.mxList[i].kcsl <=0){
                        //         wrapper.mxList.splice(i,1)
                        //     }
                        // }
                    } else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },

        //加载采购人员
        getCgry: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    if (data.a == 0) {
                        wrapper.ryList = data.d.list
                        wrapper.popContent.rybm = data.d.list[0].rybm;
                    } else {
                        malert("获取采购人员失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载供货单位
        getGhdw: function () {
            //初始化页面记载供货单位
            var parm = {
                page: 1,
                rows: 20000,
                sort: 'dwbm',
                tybz: '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhGys&types=query&json=" + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        pop.ghdwList = json.d.list;
                    } else {
                        malert("供货单位获取失败", 'top', 'defeadted');
                    }
                });
        },
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "kfbm":
                    Vue.set(this.popContent, 'kfbm', val[0]);
                    Vue.set(this.popContent, 'kfmc', val[4]);
                    break;
                case "rybm":
                    Vue.set(this.popContent, 'rybm', val[0]);
                    Vue.set(this.popContent, 'ryxm', val[4]);
                    break;
                default:
                    break;
            }
        }
    }

});

var pop = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        title: '',
        num: 0,
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '设备名称': 'sbmc',
            '设备规格': 'sbgg',
            '进价': 'jj',
            '库存数量': 'kcsl',
            '产地': 'cd',
        },
        searchCon: [],
        selSearch: -1,
        popContent: {},
        ghdwList: [],
        wzkfList: wrapper.kfList,
        total: 0,
    },
    methods: {
        // 关闭
        closes: function () {
            this.num = 0;

        },
        open: function () {
            this.num = 1;
            // 设置库房
        },
        //保存
        save: function () {
            if (pop.title = '添加设备') {
                if (pop.popContent.kcsl<pop.popContent.cksl){
                    malert("报损数量不能大于库存数量，请重新操作！", 'top', 'defeadted');
                    return;
                }
                //添加
                if (wrapper.mxList.length == 0) {
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }
                for (var i = 0; i < wrapper.mxList.length; i++) {
                    if (wrapper.mxList[i].sbbm == pop.popContent.sbbm && wrapper.mxList[i].scph == pop.popContent.scph) {
                        malert("已有该批号的设备！", 'top', 'defeadted');
                        return;
                    }
                }

                wrapper.mxList.push(pop.popContent);
                pop.popContent = {};
                return;
            } else if (pop.title = '编辑设备') {
                if (wrapper.mxList.length == 0) {
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }
                //编辑保存成功关闭弹窗
                pop.popContent = {};
                pop.closes();
                return;
            }
        },
        //药品名称下拉table检索数据
        changeDown: function (event, type) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                $("#rksl").focus();
            }
        },
        //当输入值后才触发
        change: function (event, type, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var parm = {
                sbkf: wrapper.popContent.kfbm,
                page: pop.dg.page,
                rows: pop.dg.rows,
                parm: val
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=kfkc' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    pop.searchCon = data.d.list;
                    //wap.total = data.d.total;
                    pop.selSearch = 0;
                    $(".selectGroup").show();
                });

            this.popContent[type] = val;
            if (wrapper.popContent["kfbm"] == undefined || wrapper.popContent["kfbm"] == null || wrapper.popContent["kfbm"] == "") {
                malert("库房不能为空", 'top', 'defeadted');
                return;
            }
            if (wrapper.popContent["rybm"] == undefined || wrapper.popContent["rybm"] == null || wrapper.popContent["rybm"] == "") {
                malert("采购人员不能为空", 'top', 'defeadted');
                return;
            }
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                pop.dg.page++;
                var parm = {
                    page: pop.dg.page,
                    rows: pop.dg.rows,
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=sbzd&json=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                pop.searchCon.push(data.d.list[i]);
                            }
                            pop.total = data.d.total;
                            pop.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }

            this.popContent = item;
            $(".selectGroup").hide();
        },


    }
});


laydate.render({
    elem: '.sctimes'
    , eventElem: '.icon-rl'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {

        // wrapper.param.time = value
    }
});
laydate.render({
    elem: '.yxtimes'
    , eventElem: '.icon-rl'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {

        // wrapper.param.time = value
    }
});
