<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="pdsc">
    <div class="toolMenu toolMenu_0 padd-l-10 flex-container">
        <button @click="add" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-plus"></span>生成</button>
        <button @click="save" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-save"></span>保存</button>
    </div>

    <!--数据录入区-->
    <div class="enter_tem1 enter">
        <!--库房下拉框-->
        <div class=" flex-container flex-align-c">
            <span class="padd-r-5">当前库房</span>
            <select class="zui-input wh150" v-model="pdbContent.kfbm" @change="opChange($event,'ksbm')">
                <option value="0">-请选择-</option>
                <option :value="item.kfbm" v-for="item in KFList" v-text="item.kfmc" :ksbm="item.ksbm"></option>
            </select>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">数据显示区</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="grid-box">
                    <div class="enter_tem1_item flex-container flex-align-c">
                        <span style="width: 60px">盘点方式</span>
                        <select class="wh120"  v-model="selectPdfs" @change="selChange">
                            <option v-for="option in options" :value="option.value">{{option.text}}</option>
                        </select>
                    </div>
                    <!--材料种类下拉框-->
                    <div class="enter_tem1_item ypzl" v-if="ypzlShow">
                        <span>材料种类：</span>
                        <select v-model="pdbContent.ypzl" @change="opChange($event,'zlbm')">
                            <option :value="item.ypzlbm" v-for="item in infoList" v-text="item.ypzlmc"></option>
                        </select>
                    </div>
                    <!--材料名称下拉框-->
                    <div class="enter_tem1_item ypmc" v-if="ypmcShow">
                        <span>材料名称：</span>
                        <select v-model="pdbContent.ypmc" @change="opChange($event,'ypbm')">
                            <option :value="item.ypbm" v-for="item in infoList" v-text="item.ypmc"></option>
                        </select>
                    </div>
                    <div class="zui-table-view hzList  padd-t-10">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50" >
                                <thead>
                                <tr>
                                    <th >
                                        <div class="zui-table-cell cell-s"><span>材料编码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>材料名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell text-left cell-s"><span>规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>材料批号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>库存数量</span></div>
                                    </th>
                                    <th><div class="zui-table-cell cell-s">实存数量</div></th>
                                    <th><div class="zui-table-cell cell-s">单位</div></th>
                                    <th><div class="zui-table-cell cell-s">零价</div></th>
                                    <th><div class="zui-table-cell cell-s">零价金额</div></th>
                                    <th><div class="zui-table-cell cell-s">生产批号</div></th>
                                    <th><div class="zui-table-cell cell-s">生产日期</div></th>
                                    <th><div class="zui-table-cell cell-s">有效期至</div></th>
                                    <th><div class="zui-table-cell cell-s">库房单位</div></th>
                                    <th><div class="zui-table-cell cell-s">分装比例</div></th>
                                    <th><div class="zui-table-cell cell-s">库位</div></th>
                                    <th><div class="zui-table-cell cell-s">产地</div></th>
                                    <th><div class="zui-table-cell cell-s">供货单位</div></th>
                                </tr>
                                <!--@click="checkOne($index)"-->
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                            <table class="zui-table table-width50" v-if="jsonList.length!=0">
                                <tbody>
                                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()"
                                    @click="checkOne($index)"
                                    :tabindex="$index"
                                    v-for="(item, $index) in jsonList"
                                    @dblclick="edit($index)">
                                    <td ><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                                    <td ><div class="zui-table-cell cell-s title text-left" v-text="item.ypmc"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.ypgg"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.xtph"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.kcsl"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.scsl"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.yfdwmc"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.yplj"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.ljje"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.scph"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.scrq"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.yxqz"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.kfdwmc"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.fzbl"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text=""></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.cdmc"></div></td>
                                    <td ><div class="zui-table-cell cell-s title" v-text="item.ghdw"></div></td>
                                </tr>
                                </tbody>
                            </table>
                            <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="pdsc.js"></script>