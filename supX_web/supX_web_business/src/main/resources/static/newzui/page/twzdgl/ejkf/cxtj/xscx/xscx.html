
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>销售查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel" >
        <div class="tong-top flex-container flex-jus-sb padd-r-20 flex-align-c">
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5 padd-r-10" @click="goToPage(1)">刷新</button>
            <span class="ysb-red">合计 {{money}}元</span>
        </div>
        <div class="flex-container padd-b-10 padd-t-10 padd-l-10 padd-r-10">
            <div class="flex-container" >
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="padd-r-5 ft-14">库房</span>
                        <select-input class="wh100" @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm"
                                      :name="'param.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="padd-r-5 ft-14">性质</span>
                    <select-input class="wh100" @change-data="resultRydjChange"
                                  :child="ywxzList" :index="'ywmc'" :index_val="'ywbm'" :val="param.ywbm"
                                  :name="'param.ywbm'" :search="true" :index_mc="'ywmc'" >
                    </select-input>
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="padd-r-5 ft-14">分类</span>
                    <select-input class="wh100" @change-data="resultRydjChange"
                                  :child="ypzlList" :index="'ypzlmc'" :index_val="'ypzlbm'" :val="param.ypzlbm"
                                  :name="'param.ypzlbm'" :search="true" :index_mc="'ypzlmc'" >
                    </select-input>
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="padd-r-5 ft-14">查询方式</span>
                        <select-input class="wh100" @change-data="resultRydjChange"
                                      :child="fsList" :index="'fsmc'" :index_val="'fsbm'" :val="param.fsbm"
                                      :name="'param.fsbm'" :search="true" :index_mc="'fsmc'" >
                        </select-input>
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="padd-r-5 ft-14 ">时间段</span>
                        <input class="zui-input todate wh200 " placeholder="请选择申请日期" id="timeVal" />
                </div>
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="padd-r-5 ft-14">检索</span>
                        <input class="zui-input todate wh100 " v-model="param.parm" placeholder="请输入关键字" id="jsvalue" @keydown.13="goToPage(1)"/>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable padd-r-10  padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>发药日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品规格</span></div></th>
                    <th  v-if="!sum"><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品种类</span></div></th>
                    <th v-if="!sum"><div class="zui-table-cell cell-s"><span>药品零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>处方用量</span></div></th>
                    <th v-if="!sum"><div class="zui-table-cell cell-s"><span>退药数量</span></div></th>
                    <th v-if="!sum"><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品产地</span></div></th>
                    <th v-if="!sum"><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body "  @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  @dblclick="edit($index)"  @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.fyrq,'date')"></div></td>
                    <td><div class="zui-table-cell cell-s relative">{{item.ypbm}}</div></td>
                    <td ><div class="zui-table-cell cell-s relative">{{item.ypmc}}</div></td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.ypgg"></div></td>
                    <td v-if="!sum"><div class="zui-table-cell cell-s" v-text="item.scph"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlmc"></div></td>
                    <td v-if="!sum"><div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)"></div></td>
                    <td><div class="zui-table-cell cell-s" >{{item.cfyl}}</div></td>
                    <td v-if="!sum"><div class="zui-table-cell cell-s" v-text="item.tysl"></div></td>
                    <td v-if="!sum"><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.cfyl,2)"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cdmc"></div></td>
                    <td v-if="!sum"><div class="zui-table-cell cell-s" v-text="item.ghdwmc"></div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
<script src="xscx.js"></script>
</body>
</html>
