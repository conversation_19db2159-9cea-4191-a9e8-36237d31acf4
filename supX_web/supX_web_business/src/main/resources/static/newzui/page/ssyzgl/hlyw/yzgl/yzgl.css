.yzcl-302{
    width:18.3%;
}


.top-form{
    float: initial;
}
.wh212{
    width: 212px;
}
.wh85{
    max-width: 85px;
    min-width: 36px;
}
.top-form{

    margin-right: initial;
}
.yzcl-302 .zui-select-inline .zui-input{
    padding-right: 0;
    text-indent: 0;
    padding-left: 5px;
}
.padd-r-15{
    padding-right: 15px;
}
.font-14-654{
    font-size:14px;
    color:#f2a654;
}
.yzcl-table-left{
    position: absolute;
    height: 40px;
    left: 0;
    width: 100%;
    border-top: none;
    padding-left: 17px;
    bottom: 0;
}
.top-form .top-zinle input{
    text-indent: initial;
}
.yzgl{
    height: 100%;
}
.yzcl-right-tab div{
    font-size:14px;
    color:#333333;
    text-align:center;
    border:1px solid #eeeeee;
    width:94px;
    line-height: 36px;
    height:36px;
    cursor: pointer;
    border-left: none;
}
.yzcl-right-tab{
    min-height: 36px;
    background:#fafafa;
}
.yzcl-right-tab .active{
    background:#1abc9c;
    color:#ffffff;
}
.jbxx-box{
    padding: 7px 0;
}
.jbxx-size{
    padding: 0;
}
.padd-l-12{
    padding-left:12px
}
.padd-r-18{
    padding-right: 15px;
}
 .zui-table-tool{
     position: fixed;
    height: 68px;
    right: 0;
    width: 80.1%;
    /* justify-content: flex-end; */
    padding-left: 17px;
    bottom: 0;
    left: auto;
}
.wh100{
    width: 100% !important;
}
.padd-b-68{
    padding-bottom:68px
}
.bg-fiexd{
    background-image: url(/newzui/pub/image/hzxx.png);
    position: fixed;
    top: 46%;
    right: 0;
    width: 34px;
    cursor: pointer;
    height: 128px;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
}
.useritem .userlist{
    border-left:1px solid #eeeeee;
    border-right:1px solid #eeeeee;
}
.useritem .userlist:nth-child(even){
    background:#fdfdfd;
}
.useritem .userlist:nth-child(odd){
    background:#ffffff;
}
.useritem .userlist .userKey{
    border-top:1px solid #eeeeee;
    width: 155px;
    min-height: 30px;
    font-size:14px;
    color:#7f8fa4;
    text-align: center;
    padding-right: 20px;
}
.useritem .userlist .userValue{
    width:298px;
    padding: 6px 0;
    padding-left: 19px;
    font-size:14px;
    border-left:1px solid #eeeeee;
    color:#354052;
    text-align: center;
    border-top:1px solid #eeeeee;
}
.height-100{
height: 100%;
}
.useritem .userlist .font-14-654{
    font-size:14px;
    color:#f2a654;
}
.ksys-side .useritem .userlist {
    display: flex;
    padding-bottom: 0;
}
.ksys-side{
    height: 100%;
}
.userlength{
    border-bottom:1px solid #eeeeee;
}

.sx-bottom{
    background-image: url(/newzui/pub/image/sx.png);
    position: fixed;
    bottom: 8.5%;
    right: 0;
    cursor: pointer;
    width: 48px;
    height: 48px;
    border-radius: 100%;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    z-index: 1111;
}
.root-btn{
    min-width: inherit;
    padding: 0 6px;
}
.fyxm-side-top{
    display: inline-block;
}
/* 已经执行过的医嘱 */
.yzxyz td div{
    color: #1abc9c!important;
}

.yeBack {
    background-color: #1abc9c!important;
}

.confirm-height {
    width: 600px;
    max-height: 250px;
    overflow: hidden;
    height: 250px;
}
.confirm-content{
    height: calc(100% - 106px);
}
.confirm-mad{
    padding-top: 10px;
    margin-top: 10px;
    height: 100%;
    align-items: baseline;
    overflow: auto;
}
.loadingPage{
    height: 100%;
}
.bqcydj_model{
    width: 600px;
    padding: 14px 18px 14px 18px;
    background: rgba(255, 255, 255, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.fyjlModel_l{
    height: 380px;
}
.fyjlModel{
    height: 380px;
}
.fyjlWidth{
    width: auto;
}
.fyjlWidth .zui-input, .zui-textarea{
    text-indent: 0;
}
.wh_80{
    width: 80%;
}
.wh20{
    width: 20%;
}
.openLeft {
    transition: 1s;
    width:18.3%;
    padding-left: 10px;
    margin-top: 10px;
    margin-right: 10px;
    opacity: 1;
}
.closeLeft {
    transition: 1s;
    width:0;
    opacity: 0;
}
.yzcl-792{
    transition: 1s;
    width: 81.7%;
}
.yzcl-100{
    transition: 1s;
    width: 100%;
}
.zui-input{
    text-indent: 0;
}
.hh100MAx{
    height: calc(100% - 60px);
}
