/**
 * Created by mash on 2017/8/18.
 */
    var changePsw = new Vue({
        el: '.changePsw',
        data: {
            oldPsw: null,
            newPsw: null,
            againPsw: null,
            userContent:{},
            error: 0
        },
        methods: {
            doPsw: function () {
                this.checkData();
                var json = {
                    "oldPsw": this.oldPsw,
                    "newPsw": this.newPsw
                };
                $.ajaxSettings.async = false;
                
                // 修改电子病历密码
                var ry=JSON.stringify([{rybm:userId,password:this.newPsw}]);
                var param = '{"list" : [{"ry":'+ry+'}]}';
            	this.$http.post('/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=YH&method=DSEMR_YHXX_ADD&id='+null,
            			param)
    				.then(function(data){
    					if (data.body.a == "0") {
							console.log("新增成功")
						} else {
							console.log("新增失败")
						}		
					});
            	
                this.$http.post("/actionDispatcher.do?reqUrl=MainAction&types=UpdateCzykl", JSON.stringify(json))
                    .then(function (data) {
                        if(data.body.a == "0"){
                        	
                            window.location = "/newzui/login.html"
                        } else {
                            malert("密码修改失败")
                        }
                }, function (error) {
                    console.log(error);
                });
            },
            checkData: function () {
            	console.log(userId);
                if(!this.oldPsw){
                    this.error = 1;
                    malert("密码不能为空")
                    return false
                }
                if(!this.newPsw){
                    this.error = 2;
                    malert("新密码不能为空")
                    return false
                }
                if(!this.againPsw){
                    this.error = 3;
                    malert("确认密码不能为空")
                    return false
                }
                if(this.newPsw != this.againPsw){
                    this.error = 3;
                    malert("确认密码填写不正确")
                    return false
                }
            },
        }
    })
