<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>历史库存</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ypzd.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
<div class="wrapper" id="wrapper" v-cloak>
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="remove(-1)">删除</button>
            <button class="tong-btn btn-parmary-b  paddr-r5" @click="sx()">耗材属性</button>
        </div>
        <div class="flex-container flex-align-c padd-b-10 padd-t-10 padd-l-10">
                <div class="flex-container flex-align-c ">
                    <span class="ft-14 padd-r-5 ">库房</span>
                        <select-input class="wh122" @change-data="resultRydjChange"
                                      :child="kfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                </div>
            <!--<div class="flex-container flex-align-c padd-l-10">
                    <span class="ft-14 padd-r-5 whiteSpace">耗材分类</span>
                <select-input :not_empty="true" :search="true" @change-data="resultRydjChange"
                              :child="ypgx" :index="'gxmc'" :index_val="'gxbm'" :val="json.ypflbm"
                              :name="'json.ypflbm'">
                </select-input>
                </div>-->
            <div class="flex-container flex-align-c padd-l-10">
                <span class=" ft-14 padd-r-5">状态</span>
                <select-input class="wh122" @change-data="resultRydjChange" :not_empty="false"
                              :child="ypzd_tran" :index="param.tybz" :val="param.tybz"
                              :name="'param.tybz'">
                </select-input>
            </div>
                <div class="flex-container flex-align-c padd-l-10">
                    <span class="ft-14 padd-r-5 ">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" @keyup.enter="goToPage(1)" v-model.trim="param.parm"/>
                </div>
        </div>
    </div>
    <div class="zui-table-view padd-l-10 padd-r-10">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th><div class="zui-table-cell cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </div></th>
                    <th><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>字典编码</span></div></th>
                    <!--<th><div class="zui-table-cell cell-l"><span>耗材功效</span></div></th>-->
                    <th><div class="zui-table-cell cell-xl"><span>耗材名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>商品名</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>耗材类型</span></div></th>
                    <!--<th><div class="zui-table-cell cell-s"><span>化学名称代码</span></div></th>-->
<!--                    <th><div class="zui-table-cell cell-s"><span>耗材剂型</span></div></th>-->
                    <th><div class="zui-table-cell cell-l"><span>规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>科室单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药库单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>科室进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>科室零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>基本剂量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>剂量单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>可拆分标志</span></div></th>
                    <!--<th><div class="zui-table-cell cell-s"><span>用药方法</span></div></th>-->
                    <th><div class="zui-table-cell cell-l"><span>生产厂家</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>注册证编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>注册证有效期</span></div></th>
                    <!--<th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>-->
                    <!--<th><div class="zui-table-cell cell-s"><span>单位</span></div></th>-->
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" :tabindex="$index" @dblclick="edit($index)">
                    <td width="50px">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>

                    <td><div class="zui-table-cell cell-s" v-text="item.ypbm">耗材收入</div></td>
                    <!--<td><div class="zui-table-cell cell-l" v-text="item.gxmc">耗材收入</div></td>-->
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 title relative">
                            {{item.ypmc}}
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 title relative">
                            {{item.ypspm}}
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pydm">耗材收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlmc">耗材种类</div></td>
                    <td><div class="zui-table-cell cell-l" v-text="item.ypgg">耗材规格</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbl">分装比例</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc">科室单位</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">药库单位</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypjj">进价</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yplj">零价</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfypjj">科室进价</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfyplj">科室零价</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jbjl">基本剂量</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jldwmc">剂量单位</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="istrue_tran[item.kcfbz]">可拆分标志</div></td>
                    <td><div class="zui-table-cell cell-l" v-text="item.cdmc">生产厂家</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.sccdmc">产地</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.zczbm">注册证编码</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.zczyxq,'date')">注册证有效期</div></td>
<!--                    <td><div class="zui-table-cell cell-s" v-text="item.zczbm">注册证有效期</div></td>-->
                    <td>
                        <div class="zui-table-cell cell-s">
                            <his-switch class="padd-r-10  cursor" :id="item.ypbm" disable="true" v-model="item.tybz"></his-switch>
                        </div>

                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                                <em  class="width30"><i class="icon-bj" @click="edit($index)" data-title="编辑"></i></em>
                                <em class="width30"><i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData zan-border text-center">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<div class="side-form  pop-850 contextInfo" v-cloak :class="{'ng-hide':num==1}" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <div class="jbxx">
        <div class="jbxx-size">
            <div class="jbxx-position">
                <span class="jbxx-top"></span>
                <span class="jbxx-text">基本信息</span>
                <span class="jbxx-bottom"></span>
            </div>
            <ul class="tab-edit-list padd-t-20">
                <li>
                        <i>字典编码</i>
                        <input type="text" class="zui-input" @blur="selectFun()" v-model="popContent.ypbm" placeholder="" />
                </li>
                <li>
                        <i class="font-bolder red">耗材名称</i>
                        <input type="text"  data-notEmpty="true" class="zui-input background-f" data-select="no" v-model="popContent.ypmc" @keydown="nextFocus($event)" @blur="setDM(popContent.ypmc)"/>
                </li>
                <li>
                    <!--disabled-->
                    <i>商品名</i>
                    <input type="text"  class="zui-input background-f" data-select="no" v-model="popContent.ypspm" @keydown="nextFocus($event)" @blur="setDM(popContent.ypspm)"/>
                </li>
                <li>
                    <i>拼音代码</i>
                    <input type="text"  class="zui-input background-f" v-model="popContent.pydm" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>五笔简码</i>
                    <input type="text" class="zui-input background-f" v-model="popContent.wbjm" @keydown="nextFocus($event)"/>
                </li>
                <li>
                        <i>耗材规格</i>
                        <input type="text" class="zui-input background-f" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
                </li>
                <!--<li>
                        <i>耗材剂型</i>
                        <select-input :search="true" @change-data="resultChange"
                                      :child="ypjx" :index="'jxmc'" :index_val="'jxbm'" :val="popContent.jxbm"
                                      :name="'popContent.jxbm'">
                        </select-input>
                </li>-->
                <li>
                        <i>耗材种类</i>
                        <select-input :search="true" @change-data="resultChange"
                                      :child="ypzl" :index="'ypzlmc'" :index_val="'ypzlbm'" :val="popContent.zlbm"
                                      :name="'popContent.zlbm'">
                        </select-input>
                </li>
                <!--<li>
                        <i class="font-bolder red">耗材功效</i>
                        <select-input :not_empty="true" :search="true" @change-data="resultChange"
                                      :child="ypgx" :index="'gxmc'" :index_val="'gxbm'" :val="popContent.gxbm"
                                      :name="'popContent.gxbm'">
                        </select-input>
                </li>-->
                <li>
                        <i>耗材厂家</i>
                    <input  class="zui-input "  v-model="popContent.cdmc"
                            @keyup="changeDown($index,$event,'searchCon','cdbm','cdmc')"
                            @input="Wf_change(false,$index, $event.target.value,$event,'cdbm','cdmc')"/>
                    <search-table :message="searchCon" :selected="selSearch" :page="page"
                                  :them="them" :them_tran="them_tran" @click-one="checkedOneOut"
                                  @click-two="selectOne1"></search-table>
					<div><button class="tong-btn btn-parmary icon-xz1 paddr-r5" style="margin-left: 10px;" @click="isxzcj = true"></button></div>
                </li>
                <li>
                    <i>生产产地</i>
                    <input id='sccd' class="zui-input "  v-model="popContent.sccdmc"
                            @keyup="changeDown($index,$event,'searchCon','sccdbm','sccdmc')"
                            @input="Wf_change(false,$index, $event.target.value,$event,'sccdbm','sccdmc')"/>
                    <search-table :message="searchCon" :selected="selSearch" :page="page"
                                  :them="them" :them_tran="them_tran" @click-one="checkedOneOut"
                                  @click-two="selectOne1"></search-table>
					<div><button class="tong-btn btn-parmary icon-xz1 paddr-r5" style="margin-left: 10px;" @click="isxzcd = true"></button></div>
                </li>
                <li>
                        <i>供货单位</i>
                        <select-input :search="true" @change-data="resultChange"
                                      :child="ghdw" :index="'dwmc'" :index_val="'dwbm'" :val="popContent.ghdw"
                                      :name="'popContent.ghdw'">
                        </select-input>
                </li>
                <li>
                		<!-- @input="changeFzbl(false,$event.target.value)" 不使用此功能 @yqq -->
                        <i class="font-bolder red">分装比例</i>
<!--                    :disabled="fzblNot"-->
                        <input data-notEmpty="true" type="text" class="zui-input background-f" id="fzbl" type="number"  v-model="popContent.fzbl" @keydown="nextFocus($event)"/>
                </li>
                <li>
                        <i>库房单位</i>
                        <select-input :search="true" @change-data="resultChange"
                                      :child="ypdw" :index="'jldwmc'" :index_val="'jldwbm'" :val="popContent.kfdw"
                                      :name="'popContent.kfdw'">
                        </select-input>
                </li>
                <li>
                        <i class="font-bolder red">科室单位</i>
                        <select-input :not_empty="true" :search="true" @change-data="resultChange"
                                      :child="ypdw" :index="'jldwmc'" :index_val="'jldwbm'" :val="popContent.yfdw"
                                      :name="'popContent.yfdw'">
                        </select-input>
                </li>
                <li>
                    <i class="font-bolder red">耗材进价</i>
<!--                    @input="searching(false,$event.target.value)"-->
                    <input type="text" data-notEmpty="true" class="zui-input background-f" v-model="popContent.ypjj" @keydown="nextFocus($event)"  @blur="setYfypjg()"/>
            </li>
            <li>
                    <i>耗材零价</i>
                    <input type="text" class="zui-input background-f" v-model="popContent.yplj"  @keydown="nextFocus($event)"  @blur="setYfypjg()"/>
            </li>
            <li>
                <i class="font-bolder red">科室进价</i>
<!--                    @input="searching(false,$event.target.value)"-->
                <input type="text" data-notEmpty="true" class="zui-input background-f" v-model="popContent.yfypjj" @keydown="nextFocus($event)"/>
        </li>
        <li>
            <!--@blur="blurFun()"-->
                <i>科室零价</i>
                <input type="text" class="zui-input background-f" v-model="popContent.yfyplj"  />
        </li>
                
                <li>
                    <i class="font-bolder ">耗材来源</i>
                    <select-input  :search="true" @change-data="resultChange"
                                  :child="flList" :index="'ypflmc'" :index_val="'ypflbm'" :val="popContent.ypflbm"
                                  :name="'popContent.ypflbm'">
                    </select-input>
                </li>
                <li>
                    <i>国家编码</i>
                    <input type="text" class="zui-input background-f" v-model="popContent.gjbm" />
                </li>
                <li>
                    <i>价格方式</i>
                    <select-input @change-data="resultChange"  :child="jgfs_tran"
                                  :index="popContent.jgfs" :val="popContent.jgfs" :name="'popContent.jgfs'">
                    </select-input>
                </li>
                <li>
                    <i>服务对象</i>
                    <select-input @change-data="resultChange"  :child="fwdx_tran"
                                  :index="popContent.fwdx" :val="popContent.fwdx" :name="'popContent.fwdx'">
                    </select-input>
                </li>
                <li>
                    <i>耗材分类</i>
                    <select-input @change-data="resultChange"  :child="cyypfl_tran"
                                  :index="popContent.cyypfl" :val="popContent.cyypfl" :name="'popContent.cyypfl'">
                    </select-input>
                </li>
                <li>
                    <i>存储温度</i>
                    <select-input @change-data="resultChange"  :child="ccwd_tran"
                                  :index="popContent.ccwd" :val="popContent.ccwd" :name="'popContent.ccwd'">
                    </select-input>
                </li>
                <li>
                    <i>存储条件</i>
                    <input type="text" class="zui-input background-f" v-model="popContent.cctj" />
                </li>
                <li>
                    <i>产&ensp;品&ensp;标准&ensp;&ensp;&ensp;&ensp;号</i>
                    <input type="text" class="zui-input background-f" v-model="popContent.cpbzh" @keydown="nextFocus($event)"/>
                </li>
                <li>
                        <i>批准文号</i>
                        <input type="text" class="zui-input background-f" v-model="popContent.pzwh" @keydown="nextFocus($event)"/>
                </li>
                <li >
                        <i >停用标志</i>
                    <his-switch class="padd-r-10  cursor" id="tybz"   v-model="popContent.tybz"></his-switch>
                </li>
            </ul>
        </div>
        </div>
        <!--<div class="jbxx margin-top20">
            <div class="jbxx-size">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">医嘱信息</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <ul class="tab-edit-list padd-t-20">
                    <li>
                            <i>可&ensp;拆&ensp;分标&ensp;&ensp;&ensp;&ensp;志</i>
                            <select-input @change-data="resultChange" :child="istrue_tran"
                                          :index="popContent.kcfbz" :val="popContent.kcfbz" :name="'popContent.kcfbz'">
                            </select-input>
                    </li>
                    <li>
                            <i>使用方法</i>
                            <select-input :search="true" @change-data="resultChange"
                                          :child="yyff" :index="'yyffmc'" :index_val="'yyffbm'" :val="popContent.yyff"
                                          :name="'popContent.yyff'">
                            </select-input>
                    </li>
                    <li>
                            <i>绝对含量</i>
                            <input type="number" class="zui-input background-f" v-model="popContent.dddHl" @keydown="nextFocus($event)"/>
                    </li>
                    <li>
                            <i>产&ensp;品&ensp;标准&ensp;&ensp;&ensp;&ensp;号</i>
                            <input type="text" class="zui-input background-f" v-model="popContent.cpbzh" @keydown="nextFocus($event)"/>
                    </li>
                    <li>
                            <i>批准文号</i>
                            <input type="text" class="zui-input background-f" v-model="popContent.pzwh" @keydown="nextFocus($event)"/>
                    </li>
                </ul>
            </div>
        </div>-->
        <div class="jbxx margin-top20">
            <div class="jbxx-size">
                <div class="jbxx-position">
                    <span class="jbxx-top"></span>
                    <span class="jbxx-text">其他信息</span>
                    <span class="jbxx-bottom"></span>
                </div>
                <ul class="tab-edit-list padd-t-20">
                    <li>
                            <i>拼音简码</i><!--disabled="disabled"-->
                            <input type="text" class="zui-input background-f" v-model="popContent.pydm" />
                    </li>
                    <li>
                            <i>化学名称</i>
                            <input type="text" class="zui-input background-f" v-model="popContent.hxmc" @keydown="nextFocus($event)"
                                   @blur="setPYDM(popContent.hxmc, 'popContent', 'hxmcdm')"/>
                    </li>
                    <li>
                            <i>化学名称代&ensp;&ensp;&ensp;&ensp;码</i>
                            <input type="text" class="zui-input background-f" v-model="popContent.hxmcdm" />
                    </li>
                    <li>
                            <i>标准编码</i>
                            <input type="text" class="zui-input background-f" v-model="popContent.bzbm" @keydown="nextFocus($event)"/>
                    </li>
                    <li>
                            <i>条形码</i>
                            <input type="text" class="zui-input background-f" v-model="popContent.txm" @keydown="nextFocus($event)"/>
                    </li>
                    <li>
                            <i>自编码</i>
                            <input type="text" class="zui-input background-f" v-model="popContent.zbm" @keydown="nextFocus($event)"/>
                    </li>
                    <li>
                        <i>注册证编码</i>
                        <input type="text" class="zui-input background-f" v-model="popContent.zczbm" @keydown="nextFocus($event)"/>
                    </li>
                    <li>
                        <i>注册证有效期</i>
                        <input type="text" class="zui-input background-f times4" @keydown="nextFocus($event)" name="text"
                               v-model="fDate(popContent.zczyxq,'date')" id="zczyxq"/>
                    </li>
                </ul>
            </div>
        </div>
    </div>
	
	<model :s="'确定'" :c="'取消'" @default-click="confirms" @result-clear="isxzcd=false" :model-show="true"
	               @result-close="isxzcd=false" v-if="isxzcd" title="新增产地">
	            <div class="bqcydj_model">
	                
	                <div class="padd-t-5">
	                   <div class="ksys-side">
	                       <span class="span0">
	                       <i>产地编码</i>
	                       <input type="text" class="zui-input border-r4" v-model="cdpopContent.cdbm" @keyup="nextFocus($event)" disabled="disabled"/>
	                       </span>
	                       <span class="span0">
	                       <i>产地名称</i>
	                       <input type="text" class="zui-input border-r4" v-model="cdpopContent.cdmc" @keyup.enter="confirms" @input="setPYDM(cdpopContent.cdmc, 'cdpopContent', 'pyjm')"/>
	                       </span>
	                       <span class="span0">
	                       <i>拼音简码</i>
	                       <input type="text" class="zui-input border-r4" v-model="cdpopContent.pyjm" @keydown="nextFocus($event)" disabled="disabled"/>
	                       </span>
	                       <span style="display: none;" class="margin-top-10 span0">
	                       <i style="float: left;">状态</i>
	                       <div class="switch">
	                        <input  type="checkbox" id="tybz" true-value="0" false-value="1" v-model="cdpopContent.tybz"/>
	                        <label for="tybz"></label>
	                        </div>
	                       </span>
	                   </div>
	                </div>
	            </div>
	        </model>
	<model :s="'确定'" :c="'取消'" @default-click="cjconfirms" @result-clear="isxzcj=false" :model-show="true"
	               @result-close="isxzcj=false" v-if="isxzcj" title="新增耗材厂家">
	            <div class="bqcydj_model">
	                
	                <div class="padd-t-5">
	                   <div class="ksys-side">
	                       <span class="span0">
	                       <i>厂家名称</i>
	                       <input type="text" class="zui-input border-r4" v-model="cjpopContent.cdmc" @keyup.enter="cjconfirms" @input="setPYDM(cjpopContent.cdmc, 'cjpopContent', 'pyjm')"/>
	                       </span>
	                       <span class="span0">
	                       <i>拼音简码</i>
	                       <input type="text" class="zui-input border-r4" v-model="cjpopContent.pyjm" @keydown="nextFocus($event)" disabled="disabled"/>
	                       </span>
						   <span class="span0">
						   <i>类型</i>
						   <select-input @change-data="resultChange"  :child="cjlxlb"
						                 :index="cjpopContent.lx" :val="cjpopContent.lx" :name="'cjpopContent.lx'">
						   </select-input>
						   </span>
	                       <span style="display: none;" class="margin-top-10 span0">
	                       <i style="float: left;">状态</i>
	                       <div class="switch">
	                        <input  type="checkbox" id="tybz" true-value="0" false-value="1" v-model="cjpopContent.tybz"/>
	                        <label for="tybz"></label>
	                        </div>
	                       </span>
	                   </div>
	                </div>
	            </div>
	        </model>
    <div class="ksys-btn">
		
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<script src="ypzd.js"></script>
</body>

</html>
