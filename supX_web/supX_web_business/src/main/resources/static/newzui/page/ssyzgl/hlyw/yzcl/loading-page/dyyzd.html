<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <!--<script type="application/javascript" src="/newzui/pub/top.js"></script>-->
    <script type="application/javascript" src="/pub/top.js"></script>
    <!--<link href="../../../../../css/main.css" rel="stylesheet">-->
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="dyyzd.css">
</head>
<body class="skin-default">
<div class="wrapper">
    <div id="toolMenu_yzd" class="toolMenu_yzd printHide" v-cloak>
        <div @click="long(0)" :class="{'yzd_select': which==0}">长&nbsp;&nbsp;期</div>
        <div @click="short(1)" :class="{'yzd_select': which==1}">临&nbsp;&nbsp;时</div>
        <button v-waves @click="doPrint(false)">打印</button>
        <button v-waves @click="doPrint(true)">续打</button>
    </div>

    <div class="cqyzd" v-show="isShow" v-cloak>
        <div class="yzdTitle" :class="{'goPrintHide': isGoPrint}">长期医嘱单</div>
        <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}">
            <div>
                <span>科别:</span>
                <span>{{BrxxJson.ryksmc}}</span>
            </div>
            <div>
                <span>床号:</span>
                <span>{{BrxxJson.rycwbh}}</span>
            </div>
            <div>
                <span>姓名:</span>
                <span>{{BrxxJson.brxm}}</span>
            </div>
            <div>
                <span>性别:</span>
                <span>{{brxb_tran[BrxxJson.brxb]}}</span>
            </div>
            <div>
                <span>年龄:</span>
                <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
            </div>
            <div>
                <span>住院号:</span>
                <span>{{BrxxJson.zyh}}</span>
            </div>
        </div>
        <div class="yzd-table">
            <table cellspacing="0" cellpadding="0">
                <tr :class="{'goPrintHide': isGoPrint}">
                    <th colspan="2">开始</th>
                    <th rowspan="2">执行<br>时间</th>
                    <th rowspan="2" style="width: 300px">长期医嘱</th>
                    <th colspan="2">签名</th>
                    <th colspan="2">停止</th>
                    <th rowspan="2">停止<br>执行<br>时间</th>
                    <th colspan="2">签名</th>
                </tr>
                <tr :class="{'goPrintHide': isGoPrint}">
                    <th>日<br>月</th>
                    <th>时<br>间</th>
                    <th>医师</th>
                    <th>护士</th>
                    <th>日<br>月</th>
                    <th>时<br>间</th>
                    <th>医师</th>
                    <th>护士</th>
                </tr>
                <tr v-for="(item, $index) in jsonList"
                    :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                    @click="goPrint($index)">
                    <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                    <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                    <td v-text="sameDate('zxsj', $index, 'sj')"></td>
                    <td>
                        <span class="yzd-name" v-text="item.xmmc"></span>
                        <span :class="[{'sameStart': sameSE($index) == 'start'},
                                    {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                        <span class="yzd-way" v-text="item.yyffmc"></span>
                        <span class="yzd-sm" v-text="item.yysm"></span>
                    </td>
                    <td v-text="item.ysqmxm"></td>
                    <td v-text="item.zxhsxm"></td>
                    <td v-text="sameDate('ystzsj', $index, 'ry')"></td>
                    <td v-text="sameDate('ystzsj', $index, 'sj')"></td>
                    <td v-text="sameDate('hstzsj', $index, 'sj')"></td>
                    <td v-text="item.tzysxm"></td>
                    <td v-text="item.tzhsxm"></td>
                </tr>
            </table>
        </div>

        <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
            <div class="yzd-ysInfo">
                <div>
                    <span>主管医生:</span>
                    <span>{{BrxxJson.zyysxm}}</span>
                </div>
                <div>
                    <span>护士:</span>
                    <span></span>
                </div>
            </div>
        </div>
    </div>

    <div class="cqPrint" v-cloak>
        <transition name="pop-fade">
            <div class="pop" v-show="isShow" style="background-color: #fff">
                <div class="popCenter" v-for="(itemList, index) in list" style="float: left;margin-top: 60px">
                    <button v-waves @click="print">打印</button>
                    <button v-waves @click="goOnPrint">续打</button>
                    <button v-waves @click="isShow = false">取消</button>
                    <div class="yzdTitle" :class="{'goPrintHide': isGoPrint && pagePrint == index}">长期医嘱单</div>
                    <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                        <div>
                            <span>科别:</span>
                            <span>{{BrxxJson.ryksmc}}</span>
                        </div>
                        <div>
                            <span>床号:</span>
                            <span>{{BrxxJson.rycwbh}}</span>
                        </div>
                        <div>
                            <span>姓名:</span>
                            <span>{{BrxxJson.brxm}}</span>
                        </div>
                        <div>
                            <span>性别:</span>
                            <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                        </div>
                        <div>
                            <span>年龄:</span>
                            <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                        </div>
                        <div>
                            <span>住院号:</span>
                            <span>{{BrxxJson.zyh}}</span>
                        </div>
                    </div>

                    <div class="yzd-table">
                        <table cellspacing="0" cellpadding="0">
                            <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <th colspan="2">开始</th>
                                <th rowspan="2">执行<br>时间</th>
                                <th rowspan="2" style="width: 320px">长期医嘱</th>
                                <th colspan="2">签名</th>
                                <th colspan="2">停止</th>
                                <th rowspan="2">停止<br>执行<br>时间</th>
                                <th colspan="2">签名</th>
                            </tr>
                            <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <th>日<br>月</th>
                                <th>时<br>间</th>
                                <th>医师</th>
                                <th>护士</th>
                                <th>日<br>月</th>
                                <th>时<br>间</th>
                                <th>医师</th>
                                <th>护士</th>
                            </tr>
                            <tr v-for="(item, $index) in itemList"
                                :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                <td v-text="sameDate('zxsj', $index, index, 'sj')"></td>
                                <td>
                                    <span class="yzd-name" v-text="item.xmmc"></span>
                                    <span :class="[{'sameStart': sameSE($index, index) == 'start'},
                                    {'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                    <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                    <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                </td>
                                <td style="vertical-align: top" v-text="item.ysqmxm"></td>
                                <td style="vertical-align: top" v-text="item.zxhsxm"></td>
                                <td v-text="sameDate('ystzsj', $index, index, 'ry')"></td>
                                <td v-text="sameDate('ystzsj', $index, index, 'sj')"></td>
                                <td v-text="sameDate('hstzsj', $index, index, 'sj')"></td>
                                <td v-text="item.tzysxm"></td>
                                <td v-text="item.tzhsxm"></td>
                            </tr>
                        </table>
                    </div>

                    <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                        <div class="yzd-ysInfo">
                            <div>
                                <span>主管医生:</span>
                                <span>{{BrxxJson.zyysxm}}</span>
                            </div>
                            <div>
                                <span>护士:</span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                    <div v-text="'第  ' + (index + 1) + '  页'"></div>
                </div>
            </div>
        </transition>
    </div>

    <div class="lsyzd" v-show="isShow" v-cloak>
        <div class="yzdTitle" :class="{'goPrintHide': isGoPrint}">临时医嘱单</div>
        <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint}" style="width: 594px;">
            <div>
                <span>科别:</span>
                <span>{{BrxxJson.ryksmc}}</span>
            </div>
            <div>
                <span>床号:</span>
                <span>{{BrxxJson.rycwbh}}</span>
            </div>
            <div>
                <span>姓名:</span>
                <span>{{BrxxJson.brxm}}</span>
            </div>
            <div>
                <span>性别:</span>
                <span>{{brxb_tran[BrxxJson.brxb]}}</span>
            </div>
            <div>
                <span>年龄:</span>
                <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
            </div>
            <div>
                <span>住院号:</span>
                <span>{{BrxxJson.zyh}}</span>
            </div>
        </div>

        <div class="yzd-table">
            <table cellspacing="0" cellpadding="0">
                <tr :class="{'goPrintHide': isGoPrint}">
                    <th colspan="2">吩咐时间</th>
                    <th rowspan="2" style="width: 300px">临时医嘱</th>
                    <th rowspan="2">医师签名</th>
                    <th rowspan="2">执行<br>时间</th>
                    <th rowspan="2">执行者签名</th>
                </tr>
                <tr :class="{'goPrintHide': isGoPrint}">
                    <th>日<br>月</th>
                    <th>时<br>间</th>
                </tr>
                <tr v-for="(item, $index) in jsonList"
                    :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                    @click="goPrint($index)">
                    <td v-text="sameDate('ksrq', $index, 'ry')"></td>
                    <td v-text="sameDate('ksrq', $index, 'sj')"></td>
                    <td>
                        <!--<span class="yzd-name" :class="{'same': sameZH($index)}" v-text="item.xmmc"></span>-->
                        <!--<span class="yzd-way" v-text="item.yyffmc"></span>-->
                        <!--<span class="yzd-sm" v-text="item.yysm"></span>-->
                        <span class="yzd-name" v-text="item.xmmc"></span>
                        <span :class="[{'sameStart': sameSE($index) == 'start'},
                                    {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                        <span class="yzd-way" v-text="item.yyffmc"></span>
                        <span class="yzd-sm" v-text="item.yysm"></span>
                    </td>
                    <td v-text="item.ysqmxm"></td>
                    <td v-text="fDate(item.zxsj,'shortY')"></td>
                    <td v-text="item.zxhsxm"></td>
                </tr>
            </table>
        </div>

        <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
            <div class="yzd-ysInfo">
                <div>
                    <span>主管医生:</span>
                    <span></span>
                </div>
                <div>
                    <span>护士:</span>
                    <span></span>
                </div>
            </div>
        </div>
    </div>

    <div class="lsPrint" v-cloak>
        <transition name="pop-fade">
            <div class="pop" v-show="isShow" style="background-color: #fff">
                <div class="popCenter" v-for="(itemList, index) in list" style="float: left;margin-top: 60px">
                    <button v-waves @click="print">打印</button>
                    <button v-waves @click="goOnPrint">续打</button>
                    <button v-waves @click="isShow = false">取消</button>
                    <div class="yzdTitle" :class="{'goPrintHide': isGoPrint && pagePrint == index}">临时医嘱单</div>
                    <div class="yzd-brInfo" :class="{'goPrintHide': isGoPrint && pagePrint == index}" style="width: 594px;">
                        <div>
                            <span>科别:</span>
                            <span>{{BrxxJson.ryksmc}}</span>
                        </div>
                        <div>
                            <span>床号:</span>
                            <span>{{BrxxJson.rycwbh}}</span>
                        </div>
                        <div>
                            <span>姓名:</span>
                            <span>{{BrxxJson.brxm}}</span>
                        </div>
                        <div>
                            <span>性别:</span>
                            <span>{{brxb_tran[BrxxJson.brxb]}}</span>
                        </div>
                        <div>
                            <span>年龄:</span>
                            <span>{{BrxxJson.nl}}{{nldw_tran[BrxxJson.nldw]}}</span>
                        </div>
                        <div>
                            <span>住院号:</span>
                            <span>{{BrxxJson.zyh}}</span>
                        </div>
                    </div>

                    <div class="yzd-table">
                        <table cellspacing="0" cellpadding="0">
                            <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <th colspan="2">吩咐时间</th>
                                <th rowspan="2" style="width: 445px">临时医嘱</th>
                                <th rowspan="2">医师签名</th>
                                <th rowspan="2">执行<br>时间</th>
                                <th rowspan="2">执行者签名</th>
                            </tr>
                            <tr :class="{'goPrintHide': isGoPrint && pagePrint == index}">
                                <th>日<br>月</th>
                                <th>时<br>间</th>
                            </tr>
                            <tr v-for="(item, $index) in itemList"
                                :class="[{'goPrintHide': isChecked > $index && isGoPrint && pagePrint == index}]">
                                <td v-text="sameDate('ksrq', $index, index, 'ry')"></td>
                                <td v-text="sameDate('ksrq', $index, index, 'sj')"></td>
                                <td>
                                    <span class="yzd-name" v-text="item.xmmc"></span>
                                    <span :class="[{'sameStart': sameSE($index, index) == 'start'},
                                    {'sameEnd': sameSE($index, index) == 'end'},{'same': sameSE($index, index) == 'all'}]"></span>
                                    <span class="yzd-way" v-show="isShowItem($index)" v-text="item.yyffmc"></span>
                                    <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                </td>
                                <td v-text="item.ysqmxm"></td>
                                <td v-text="fDate(item.zxsj,'shortY')"></td>
                                <td v-text="item.zxhsxm"></td>
                            </tr>
                        </table>
                    </div>

                    <div class="ysDiv" :class="{'goPrintHide': isGoPrint}">
                        <div class="yzd-ysInfo">
                            <div>
                                <span>主管医生:</span>
                                <span></span>
                            </div>
                            <div>
                                <span>护士:</span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                    <div v-text="'第  ' + (index + 1) + '  页'"></div>
                </div>
            </div>
        </transition>
    </div>
</div>
</body>
<script src="dyyzd.js"></script>
</html>
