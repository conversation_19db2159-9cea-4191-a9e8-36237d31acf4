var qxksbm = '0952';
    var wrapper = new Vue({
        el: '#wrapper',
        mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
        data: {
            ifClick:true,
            isShowpopL:false,
            isTabelShow:false,
            isShowkd:true,
            isShow:false,
            csParm: {},
            zdy: userId,
            zdrq: getTodayDateTime(), //获取制单日期
            TjShow:true,
            zdyxm:'',
            jyinput: false, //禁用输入框
            bsdContent: {
                ckfs: "03", //报损方式
                lyr: userId //操作人
            },
            KFList: [{
                kfbm:'06',
                kfmc:'体外诊断试剂库',
                ksbm:'0952'
            }], //库房
            title:'',
            totle:'',
            num:0,


            isUpdate:0,
            popContent:{},

            search:'',
            //打印数据
            printData: {},
            rkdList: [], //入库单集合
            jsonList:[],
            json: {},
            ryList: [],
            tkdDetail: [],
            rkd: {}, //入库单对象
            dyShow:false,
            ShShow:false,
            mxShShow:true,
            zfShow: true,
            thdList: [], //退货单集合
            dg: {
                page: 1,
                rows: 20,
                sort: "",
                order: "asc",
                parm: ""
            },
            isCheck: null,
            thdDetail: [], //退货单明细集合
            dateBegin: null,//getTodayDateBegin(),
            dateEnd: null,//getTodayDateEnd(),
            time: {
                test: 'hello!'
            },
            t: {},
            zhuangtai: {
                "0": "未审核",
                "1": "已审核",
                "2":"已作废",
                "3":"未通过",
            },

            //****
            bsd:null,
            bsdList:[],
            param:{
            	rows:10,
            	page:1,
            	beginrq:null,
            	endrq:null,
            	parm:''
            },
            totlePage:'',
            modifyIndex:null,
        },
        updated:function () {
            changeWin();
        },
        mounted: function () {
            this.getKFData();
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        	laydate.render({
                elem: '#timeVal'
                , type: 'datetime',
                theme: '#1ab394',
                done: function (value, data) {
                    wrapper.param.beginrq = value;
                    wrapper.getData();
                }
            });
        	laydate.render({
                elem: '#timeVal1'
                , type: 'datetime',
                theme: '#1ab394',
                done: function (value, data) {
                    wrapper.param.endrq = value;
                    wrapper.getData();
                }
            })
        },
        methods: {
            loadNum: function () {
                this.num = this.num;
            },
            //判断是否有操作权限
            hasCx: function(cx) {
                if(!cx) {
                    malert("用户没有操作权限！",'top','defeadted');
                    return true;
                }
            },
            //进入页面加载单据列表信息
            getData: function () {
                this.isUpdate = 0;
                this.bsd = null;
                //清空退货明细信息
                this.thdDetail = [];
                this.jsonList = [];
                if(!this.popContent.kfbm) {
                    malert("请先择库房!",'top','defeadted');
                     return;
                }
                common.openloading('.zui-table-view');
                Vue.set(this.param,'kfbm',this.popContent.kfbm);
                Vue.set(this.param,'parm',this.search);
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywBsd&types=queryBsd&parm=' + JSON.stringify(this.param),
                    function(data) {
                        if(data.a == 0) {
                            wrapper.bsdList = data.d.list;
                            wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
                common.closeLoading()
            },

            //审核传值
            passData: function () {
                if (!wrapper.ifClick) return;
                wrapper.ifClick = false;
                //出库单非空判断
                if(this.bsdList.length == 0) {
                    return;
                }
                var json = {
                    'ckdh': this.bsdList[this.isCheck].ckdh,
                    'kfbm': wrapper.popContent.kfbm
                };

                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywBsd&types=shBsd', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == "0") {
                            wrapper.ifClick = true;
                            var mes=confirm('是否打印报损单');
                            if(mes==true){
                                this.print();
                            }else{

                            }
                            wrapper.isShow=false;
                            wrapper.isShowkd=true;
                            wrapper.isShow=false;
                            wrapper.isShowkd=true;
                            wrapper.TjShow=false;
                            wrapper.TjShow=false;
                            wrapper.zfShow=false;
                            wrapper.getData();
                            malert("审核成功！",'top','success')
                        } else {
                            wrapper.ifClick = true;
                            malert(data.body.c,'top','defeadted');
                        }
                    });
            },
            //打印
            print:function(){
            	//帆软打印
            	var djh = this.bsdList[this.isCheck].ckdh;
            	var kfbm = wrapper.popContent.kfbm;
            	this.updateDycsAndPrint(kfbm, djh);
        		// console.log(kfbm+"||"+djh);
                // var reportlets ="[{reportlet: 'fpdy%2Fyjkf%2Fykgl_bsd.cpt',yljgbm:'"+jgbm+"',kfbm:'"+kfbm+"',djh:'"+djh+"'}]";
                // if (FrPrint(reportlets,null)){
                // 	return ;
                // }
                // window.print();
            },

            updateDycsAndPrint: function(kfbm, ckdh){
                var parm = {
                    'ckdh' : ckdh
                };
                //更新打印次数
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=updateDycs' + '&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            //调用帆软打印
                            console.log(kfbm+"||"+ckdh);
                            var frpath = "";
                            if (window.top.J_tabLeft.obj.frprintver == "3"){
                                frpath = "%2F";
                            }else{
                                frpath = "/";
                            }
                            var reportlets ="[{reportlet: 'fpdy"+frpath+"yjkf"+frpath+"ykgl_bsd.cpt',yljgbm:'"+jgbm+"',kfbm:'"+kfbm+"',djh:'"+ckdh+"'}]";
                            console.log(reportlets);
                            if (FrPrint(reportlets,null)){
                                return ;
                            }
                        }else {
                            malert(data.c, 'top', 'defeadted');
                        }
                });
            },

            //作废退库单 2018/07/04二次弹窗确认提示
            invalidData: function (num) {
            	if(num!=null&&num!=undefined){
            		this.isCheck=num;
            	}
                //判断权限
                if(this.hasCx(wap.csParm.zf)) {
                    return;
                }
                //出库单非空判断
                if(this.bsdList.length == 0) {
                    return;
                }
                if (common.openConfirm("确认作废该条信息吗？", function () {
                        var json = {
                            "ckdh": wrapper.bsdList[wrapper.isCheck]['ckdh'],
                            "kfbm": wrapper.popContent.kfbm
                        };
                    wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywBsd&types=zfBsd', JSON.stringify(json)).then(function (data) {
                            if (data.body.a == "0") {
                                wrapper.getData();
                                malert("作废成功！",'top','success');
                                wrapper.cancel();
                            } else {
                                malert(data.body.c,'top','defeadted');
                            }
                        });
                    })) {
                    return false;
                }
            },
            //显示退库单细节
            showDetail: function (index) {
                this.isCheck = index;
                this.isShow=true;
                this.isShowkd=false;
                this.isShowpopL=false;
                this.isShow=true;
                this.isShowkd=false;
                this.TjShow=false;
                this.TjShow=false;
                this.zfShow=false;
               if(this.bsdList[index].shzfbz=='1' || this.bsdList[index].shzfbz=='2'){
               		if(this.bsdList[index].shzfbz=='1'){
                        this.dyShow=true;
               		}
                	this.mxShShow=false;
                   this.jyinput=true;
                	this.ShShow=false;
                }else{
                   this.dyShow=false;
                	this.mxShShow=false;
                   this.jyinput=false;
                	this.ShShow=true;
                }
                this.zdyxm=this.bsdList[index].zdyxm;
                this.zdrq=this.fDate(this.bsdList[index].zdrq,'date');
                this.popContent.bzms=this.bsdList[index].bzms;
                this.popContent.kfbm=this.bsdList[index].kfbm;
                this.getMx(this.bsdList[index]['ckdh']);
            },
            //编辑
            editIndex:function(index){
                this.isUpdate = 1;
                this.isCheck = index;
                this.isShowkd=false;
                this.isShow=true;
                this.isShowpopL=true;
                this.isShow=true;
                this.isShowkd=false;
                this.TjShow=true;
                this.ShShow=false;
                this.TjShow=false;
                this.zfShow=true;
                this.mxShShow=true;
                this.jyinput=false;
                this.zdyxm=this.bsdList[index].zdyxm;
                this.zdrq=this.fDate(this.bsdList[index].zdrq,'date');
                this.popContent.bzms=this.bsdList[index].bzms;
                this.popContent.kfbm=this.bsdList[index].kfbm;
                this.bsd = this.bsdList[index];
                this.getMx(this.bsd.ckdh);
            },
            getMx:function(ckdh){
                this.jsonList=[];
            	 var parm = {
            			 ckdh:ckdh ,
            	 };
            	 Vue.set(this.dg,'parm',ckdh);
            	 $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywBsd&types=queryBsglMxByBsdh' +
                         '&dg=' + JSON.stringify(this.dg) + '&parm=' + JSON.stringify(parm),
                         function(data) {
                     		wrapper.jsonList = data.d;
                         });
            },

            //提交所有体外诊断试剂
            submitAll: function() {
                //是否禁止提交
                if(this.isSubmited) {
                    malert("数据提交中，请稍候！",'top','success');
                    return ;
                }
                //判断提交数据正确性
                if(this.jsonList.length <= 0) {
                    malert("没有可提交的数据",'top','defeadted');
                    return ;
                }
                //判断是否选择库房
                if(wrapper.popContent.kfbm == null) {
                    malert("库房为必选项",'top','defeadted');
                    return ;
                }
                //是否禁止提交
                this.isSubmited = true;

                var bsd = null;
                if(!this.bsd){
                	bsd = wap.bsdContent
                }else{
                	bsd = this.bsd;
                	Vue.set(bsd,'lyr',wap.bsdContent.lyr);
                	Vue.set(bsd,'ckfs','03');
                }
                bsd.kfbm=wrapper.popContent.kfbm;
                bsd.bzms=wrapper.popContent.bzms;
                //准备数据，包括退货单对象和退货单明细对象
                var json = {
                    "list": {
                        "bsd": bsd,
                        "bsdmx": this.jsonList
                    }
                };

                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywBsd&types=modify',
                    JSON.stringify(json))
                    .then(function(data) {
                        if(data.body.a == 0) {
                            malert("数据更新成功",'top','success');
                            this.jsonList = [];
                            this.isShow=false;
                            this.isShowkd=true;
                            wrapper.isShowkd=true;
                            wrapper.isShow=false;
                            wrapper.isShowpopL=false;
                            wrapper.getData();
                        } else {
                            malert( data.body.c,'top','defeadted');
                        }
                        //是否禁止提交
                        wrapper.isSubmited = false;
                    }, function(error) {
                        //是否禁止提交
                        wrapper.isSubmited = false;
                    });
            },
            //删除2018/07/06 二次删除弹窗提示
            scmx: function(num) {
                if (common.openConfirm("确认删除该条信息吗？", function () {
                    wrapper.jsonList.splice(num, 1);
                    })) {
                    return false;
                }
            	//this.jsonList.splice(num, 1);
            },
            //双击修改
            edit: function(num) {
            	//时间格式化
                Vue.set(wap.popContent,'scrq',formatTime(wap.popContent.scrq, 'date'));
                Vue.set(wap.popContent,'yxqz',formatTime(wap.popContent.yxqz, 'date'));

                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                //库存查询
                wrapper.getKc(this.jsonList[num].ypbm);
                wrapper.bsd = wrapper.jsonList[num];
                wrapper.modifyIndex = num;
                wap.title = "编辑体外诊断试剂";
                wap.open();
            },

          //库存
            getKc:function(ypbm){
            	var json = {
            			ypbm : ypbm
            	};
            	$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' + '&json=' + JSON.stringify(json), function (data) {
                            if(data.a == 0){
                            	if(data.a == 0){
                            		if(data.d != null && data.d.list != null){
                            			//可用库存，实际库存
                            			Vue.set(wap.popContent,'kcsl',data.d.list[0].kcsl);
                            			Vue.set(wap.popContent,'kykc',data.d.list[0].sjkc);
                            		}
                            	}
                            }
                        });
            },

            //取消
            cancel:function () {
                wrapper.getData();
                wrapper.isShow=false;
                wrapper.isShowkd=true;
                wrapper.isShowpopL=false;
                wrapper.isShow=false;
                wrapper.isShowkd=true;
            },
            kd:function (index) {
                this.num=index;
                setTimeout(function () {
                    wap.$refs.autofocus.$refs.inputFu.focus();
                },40);
                wrapper.loadNum();
                switch (wrapper.num){
                    case 0:
                        wrapper.isUpdate = 0;
                        this.isShowkd=false;
                        this.isShow=true;
                        this.isShowpopL=true;
                        wrapper.isShow=true;
                        wrapper.isShowkd=false;
                        wrapper.TjShow=true;
                        wrapper.ShShow=false;
                        $('#bzms').attr('disabled',false);
                        wrapper.zfShow=false;
                        wrapper.mxShShow=true;
                        var reg = /^[\'\"]+|[\'\"]+$/g;
                        wrapper.zdyxm=sessionStorage.getItem("userName"+userId).replace(reg,'');
                        break;
                    case 1:
                        wrapper.isUpdate = 0;
                        wap.open();
                        wap.title='添加体外诊断试剂';
                        wap.popContent={};
                        break;
                }

            },
            searchHc:function () {
                wrapper.param.page=1
                wrapper.getData();
            },
            getKFData:function () {
                //下拉框获取库房
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011005"}',
                    function (data) {
                        if (data.a == 0) {
                            // wrapper.KFList = data.d;
                            // if (data.d.length > 0) {
                            //     qxksbm = data.d[0].ksbm;
                                Vue.set(wrapper.popContent,'kfbm',wrapper.KFList[0].kfbm);
                                Vue.set(wrapper.bsdContent,'kfbm',wrapper.KFList[0].kfbm);
                                wap.getCsqx(); //加载完库房再次加载参数权限
                            // }
                        } else {
                            malert("一级库房获取失败",'top','defeadted');
                        }
                    });

            },
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.popContent, 'kfbm', val[0]);
                Vue.set(this.popContent, 'kfmc', val[4]);
                for(var i = 0 ; i < wrapper.KFList.length ; i++){
                    if(wrapper.KFList[i].kfbm == val[0]){
                        qxksbm = wrapper.KFList[i].ksbm;
                        break;
                    }
                }
                wap.getCsqx();
            },
        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            isShowpopL:false,
            iShow:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            title:'',
            zdy: userId,
            ryList: [],
            added: false,
            lyks: null,
            tkyf: null,
            tkyfBM: null,
            zdrq: getTodayDateTime(), //获取制单日期
            bsdContent: {
                ckfs: "03", //报损方式
                lyr: userId //操作人
            },
            cgryList: [], //退库人员
            KSList: [],
            KFList: [{
                kfbm:'06',
                kfmc:'体外诊断试剂库',
                ksbm:'0952'
            }],
            lyr: null,
            bsdList: [], //入库单集合
            ghdwList: [],//供货单位list
            popContents:{},
            //体外诊断试剂信息对象
            popContent: {},
            //参数权限对象
            csParm: {},
            dg: {
                page: 1,
                rows: 10,
                sort: "",
                order: "asc",
                parm: ""
            },
            them_tran: {},
            them: {
                '生产批号': 'scph',
                '体外诊断试剂编号': 'ypbm',
                '体外诊断试剂名称': 'ypmc',
                '库存数量': 'ykkc',
                '有效期至': 'yxqz',
                '规格': 'ypgg',
                '分装比例': 'fzbl',
                '进价': 'ykjj',
                '零价': 'yklj',
                '库房单位': 'kfdwmc',
                '二级库房单位': 'yfdwmc',
                '效期': 'yxqz',
                '体外诊断试剂剂型': 'jxmc'
            }
        },
        mounted:function(){
                this.getJzData();
                this.zdlb();
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg');
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                wap.addData();
                //this.closes();
            },
            getJzData:function () {
                //初始化页面记载供货单位
                this.dg.rows = 200;
                this.dg.sort = 'dwbm';
                this.dg.tybz = '0';
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(this.dg),
                    function (json) {
                        if (json.a == 0) {
                            wap.ghdwList = json.d.list;
                        } else {
                            malert("供货单位获取失败",'top','defeadted');
                        }
                    });


            },
            //获取参数权限
            getCsqx: function () {
                //获取参数权限
                $.ajaxSetup({
                    async: false
                });

                var parm = {
                    "ksbm": qxksbm,
                    "ylbm": "N040100011005"
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        if (json.d.length > 0) {
                            //退库开单打印单据，1-开单保存后打印单据，0-开单保存后不打印单据
                            for(var i = 0; i < json.d.length; i++) {
                                if(json.d[i].csqxbm == 'N04010001100501') {
                                    if(json.d[i].csz == '1') {
                                        wap.csParm.kddy = true;
                                    } else {
                                        wap.csParm.kddy = false;
                                    }
                                }
                                //报损开单审核，0-有开单审核  1-有开单 2-有审核
                                if(json.d[i].csqxbm == 'N04010001100502') {
                                    if(json.d[i].csz == '0') {
                                        wap.csParm.kd = true;
                                        wap.csParm.sh = true;
                                    } else if(json.d[i].csz == '1') {
                                        wap.csParm.kd = true;
                                        wap.csParm.sh = false;
                                    } else {
                                        wap.csParm.sh = true;
                                        wap.csParm.kd = false;
                                    }
                                }
                                //报损单作废权限，1-有 0-无
                                if(json.d[i].csqxbm == 'N04010001100503') {
                                    if(json.d[i].csz == '0') {
                                        wap.csParm.zf = false;
                                    } else {
                                        wap.csParm.zf = true;
                                    }
                                }
                            }
                        }
                        wrapper.getData();
                    } else {
                        malert(json.c, "参数权限获取失败");
                    }
                    //设置ajax为异步
                    $.ajaxSetup({
                        async: true
                    });
                });
            },
            //

            //体外诊断试剂名称下拉table检索数据
            changeDown: function (event, type) {
                if(type == "cksl" && window.event.keyCode == 13) {
                    //添加数据到提交区
                    this.addData();
                    //清空数据录入区
                    // wap.clearData();
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
                //选中之后的回调操作
                if(window.event.keyCode == 13 && isReq) {
                    if(type == "ypmc") {
                        /*this.nextFocus(event);*/
                       $("#bssl").focus();
                    }
                }
            },
            //当输入值后才触发
            change: function (event, type) {
                if(wrapper.bsdContent.kfbm == undefined || wrapper.bsdContent.kfbm == null | wrapper.bsdContent.kfbm == "") {
                    malert("请先择库房!",'top','defeadted');
                    return;
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if(this.popContent[type] == undefined || this.popContent[type] == null) {
                    this.dg.parm = "";
                } else {
                    this.dg.parm = this.popContent[type];
                }
                this.dg.sort = "ypbm";
                var bean = {
                    "kfbm": wrapper.popContent.kfbm
                };
                //分页参数
                wap.dg.page = 1;
                wap.dg.rows = 5;
                //按二级库房/科室查询库存,二者编码同时存在时按二级库房查询
                if(wap.bsdContent.lyyf != null && wap.bsdContent.lyks != null) {
                    var temp = 'yfpckc';
                }
                //二级库房为空，按科室查询
                if(wap.bsdContent.lyyf == undefined || wap.bsdContent.lyyf == null || wap.bsdContent.lyyf == '') {
                    var temp = 'kspckc';
                }
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' +
                    '&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(bean),
                    function (data) {
            			for(var i = 0; i < data.d.list.length; i++) {
                            data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                            data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                            //判断可用库存数量
                            data.d.list[i]['kykc'] = data.d.list[i]['kcsl'] - (data.d.list[i]['wshcks'] == undefined ? 0 : data.d.list[i]['wshcks']);
                        }
                        wap.searchCon = data.d.list;
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if (data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        } else {
                            $(".selectGroup").show();
                        }
                    });
            },

            //双击选中下拉table
            selectOne: function (item) {
                //查询下页
                if (item == null) {
                    //分页操作
                    var parm = {
                        "kfbm": wrapper.popContent.kfbm
                    };
                    wap.dg.page++;

                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' +
                        '&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(parm),
                        function (data) {
                            if (data.a == 0) {
                                for (var i = 0; i < data.d.list.length; i++) {
                                    wap.searchCon.push(data.d.list[i]);
                                }
                                wap.total = data.d.total;
                                wap.selSearch = 0;
                            } else {
                                malert('分页信息获取失败','top','defeadted')
                            }

                        });
                    return;
                }

                this.popContent = item;
                $(".selectGroup").hide();
            },

            //报损信息
            addData: function () {
                //非空判断
            	if(wap.popContent.cksl == undefined || wap.popContent.cksl == null || wap.popContent.cksl == '' || wap.popContent.cksl <= 0 ){
            		malert('报损数量不正确','top','defeadted');
            		$("#bssl").focus();
            		return;
            	}

            	if(wap.popContent.ypbm == undefined || wap.popContent.ypbm == null || wap.popContent.ypbm == ''){
            		malert('请填入相关的体外诊断试剂','top','defeadted');
            		$("#ypmc").focus();
            		return;
            	}

                //判断权限
                if(wrapper.hasCx(wap.csParm.kd)) {
                	malert("无权限！",'top','defeadted');
                    return;
                }
                wap.popContent['zdy'] = userId;
                var haveError = false;

                if(haveError) {
                    malert("录入区数据不完整",'top','defeadted');
                    return;
                }
                //判断可用库存数量是否大于0（同时判断未审核库存数）
                if(this.popContent.kykc <= 0 || (this.popContent.kykc-this.popContent.cksl) < 0 ) {
                    var msg = "可用库存不足" + (this.popContent['kykc'] == 0 ? "!" : "，请检查未审核的出库！");
                    malert(msg,'top','defeadted');
                    return false;
                }
                if(wrapper.isUpdate == 0){
                	//添加
                    //判断数据是否重复
                    if(wrapper.jsonList.length > 0) {
                        for(var i = 0; i < wrapper.jsonList.length - 1; i++) {
                            if(wrapper.jsonList[i].ypbm == this.popContent.ypbm && wrapper.jsonList[i].xtph == this.popContent.xtph) {
                                wrapper.jsonList[i].cksl = this.popContent.cksl;
                                malert("体外诊断试剂【" + this.popContent.ypmc + "】已添加，请双击修改！",'top','success');
                                return;
                            }
                        }
                    }
                	//将数据加入列表
                    wrapper.jsonList.push(this.popContent);
                    this.popContent = {};
                    $("#ypmc").focus();
                }else{
                	//修改
                    wrapper.$set(wrapper.jsonList,wrapper.modifyIndex,wap.popContent);
                	this.popContent={};
                	this.closes();
                }
            },
            zdlb:function () {
                //下拉框获取科室编码
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                    function(data) {
                        if(data.a == 0) {
                            wap.KSList = data.d.list;
                        } else {
                            malert("科室获取失败!",'top','defeadted');
                        }
                    });
                //获取制单人列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                    function(data) {
                        wap.ryList = data.d.list;
                    });
            }

        }


    });

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }

});





