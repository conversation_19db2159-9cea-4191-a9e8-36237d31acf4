var toolMenu_4 = new Vue({
	el: '.toolMenu_4',
	data: {
		pzNum: 0,
		ypjs: null,
		pzhList: [],
		//权限信息之科室编码
		qxksbm: ''
	},
	//加载启动
	mounted: function() {
		//获取判断凭证单号
		this.getWshpdb();
		//获取权限科室编码
		this.getKFData();
	},
	//凭证变更
	watch: {
		pzNum: function(val) {
			//选择“-请选择-”时不进行操作
			if(val == 0) {
				return;
			}
			//加载盘点表明细
			this.showDetail(val);
		}
	},
	methods: {
		//获取盘点表明细
		showDetail: function(parm) {
			//查询盘点表明细
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryMx&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					//转换日期和数字
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['scrq'] = formatTime(json.d[i]['scrq'], 'date');
						json.d[i]['yxqz'] = formatTime(json.d[i]['yxqz'], 'date');
						json.d[i]['ljje'] = Math.round(json.d[i]['yplj'] * json.d[i]['kcsl'] * 100) / 100;
					}
					enter_pdbmx.jsonList = json.d;
				} else {
					malert('数据获取失败！')
				}
			});
		},
		//获取盘点表列表
		getWshpdb: function() {
			var kfbm = document.getElementById('_kfbm').value;
			if(kfbm == '') {
				malert('请选择库房');
				return;
			}
			var parm = {
				'wzkf': kfbm,
				'qrzfbz': '1',
			};
			//查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdblr&types=queryDj&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['pdrq'] = formatTime(json.d[i]['pdrq'], 'date');
					}
					toolMenu_4.pzhList = json.d;
				} else {
					malert('数据获取失败！')
				}

			});
		},
		//审核盘点表
		shPdb: function() {
			var kfbm = document.getElementById('_kfbm').value;
			if(kfbm == 'undefined' || kfbm == '' || kfbm == null) {
				malert('请选择库房！');
				return;
			}
			if(enter_pdbmx.jsonList.length == 0) {
				malert('没有可以审核的内容！');
				return;
			};
			//准备参数
			var json = {
				'wzkf': kfbm,
				'pdpzh': toolMenu_4.pzNum.pdpzh,
				'qrzfry': userId, //审核人
				'qrzfbz': '1', //审核标志  0-未审核 1-审核 2-作废
			};

			this.$http.post('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=passDj', JSON.stringify(json))
				.then(function(data) {
					malert(data.body.c);
					//获取判断凭证单号
					toolMenu_4.getWshpdb();
					//获取权限科室编码
					toolMenu_4.getKFData();
				}, function(error) {
					console.log(error);
				});
			//清空明细列表
			enter_pdbmx.jsonList = [];
			//重新获取凭证号
			toolMenu_4.getWshpdb();
			//获取判断凭证单号
			toolMenu_4.pzNum = 0;
		},
		//获取库房信息
		getKFData: function() {
			$.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxwzkf&parm={"ylbm":"012001007"}',
				function(data) {
					if(data.a == 0) {
						//默认获取该用例第一个科室的权限科室编码
						toolMenu_4.qxksbm = data.d[0].ksbm;
					} else {
						malert("药库获取失败");
					}
				});
		},
	},

});

var enter_pdbmx = new Vue({
	el: '.enter_pdbmx',
	mixins: [tableBase],
	data: {
		pdWay: 0,
		jsonList: []
	},
	methods: {}
})