<link rel="stylesheet" type="text/css" href="childpage/hlfxpg.css">
<div class="toolMenu padd-b-10">
	<button @click="addData" class="zui-btn btn-primary fa fa-plus">新增</button>

	<button :disabled="save" @click="edit()" class="zui-btn btn-primary-b fa fa-edit">保存</button>

	<button @click="remove" class="zui-btn btn-parmary-d2 fa fa-trash-o">删除</button>
	<div class=" padd-l-12 left line-height-12">
		科别：<span class="font-14-654 padd-r-18" v-text="jlxqContent.ryksmc"></span>
		床号：<span class="font-14-654 padd-r-18" v-text="jlxqContent.rycwbh"></span>
		姓名：<span class="font-14-654 padd-r-18" v-text="jlxqContent.brxm"></span>
		年龄：<span class="font-14-654 padd-r-18">{{
		jlxqContent.nl?(jlxqContent.nl + nldw_tran[jlxqContent.nldw]):'' + jlxqContent.nl2?(jlxqContent.nl2 + nldw_tran[jlxqContent.nldw2]):''
		}}</span>
		性别：<span class="font-14-654 padd-r-18"
			v-text="brxb_tran[jlxqContent.brxb]"></span> 住院号：<span
			class="font-14-654 padd-r-18"
			v-text="jlxqContent.brzyh|| jlxqContent.zyh"></span>
	</div>
</div>

<div class="flex-container common-css" id="jlxq">
	<div class="zui-table-view  padd-b-40 padd-r-10  wh20" >
		<div class="flex-container  flex-align-c padd-b-10">
			<span  class="font-14 color-wtg padd-r-5 whiteSpace">类型</span>
			<select-input  @change-data="changeHlfxlx"
				:child="hlfxpg_tran" :index="hlfxlx" :val="hlfxlx"
				:name="'hlfxlx'" :search="true"> </select-input>
		</div>
		<div class="flex-container  flex-align-c padd-b-10 " v-if="N03004200277!='1'">
			<span class="font-14  padd-r-5 whiteSpace">日期</span>
			 <input type="text"
				class="zui-input padd-l-10 " data-select="no" id="today" v-model="today" />
		</div>
		<div class="zui-table-header">
			<table class="zui-table table-width50">
				<thead>
					<tr>
						<th>
							<div class="zui-table-cell cell-m">序号</div>
						</th>
						<th>
							<div class="zui-table-cell cell-l">记录日期</div>
						</th>
						<th>
							<div class="zui-table-cell text-left cell-s">操作员</div>
						</th>
					</tr>
				</thead>
			</table>
		</div>
		<div class="zui-table-body flex-one over-auto" @scroll="scrollTable">
			<table class="zui-table table-width50">
				<tbody>
					<tr
						:class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
						@mouseenter="switchIndex('hoverIndex',true,$index)"
						@mouseleave="switchIndex()"
						@click="switchIndex('activeIndex',true,$index),Wf_Click($index),getData()"
						:tabindex="$index" v-for="(item, $index) in pgjlArr">
						<td>
							<div class="zui-table-cell cell-m" v-text="$index+1"></div>
						</td>
						<td>
							<div class="zui-table-cell cell-l"
								v-text="fDate(item.pgrq,'yyyy-MM-dd')"></div>
						</td>
						<td>
							<div class="zui-table-cell cell-s  text-left" v-text="item.pgryxm"></div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="over-auto wh81">
		<h2 class="fxpg-h2">{{hlfxpg_tran[hlfxlx]}}</h2>
		<!-- Barthel指数（BI）评定量表 -->
		<div class="fxpg padd-t-10" v-if="hlfxlx == '1'">
			<table border="0" cellspacing="0" cellpadding="0">
				<tr>
					<th>项目/得分</th>
					<th>进食</th>
					<th>洗澡</th>
					<th>修饰</th>
					<th>穿衣</th>
					<th>控制大便</th>
					<th>控制小便</th>
					<th>入厕</th>
					<th>床椅转移</th>
					<th>平地行走</th>
					<th>上下楼梯</th>
					<th>总分</th>
				</tr>
				<tr>
					<td>完全独立</td>
					<td>10</td><td>5</td><td>5</td><td>10</td><td>10</td><td>10</td><td>10</td><td>15</td>
					<td>15</td><td>10</td><td rowspan="4"></td>
				</tr>
				<tr>
					<td>需部分帮助</td>
					<td>5</td><td>0</td><td>0</td><td>5</td><td>5</td><td>5</td><td>5</td><td>10</td>
					<td>10</td><td>5</td>
				</tr>
				<tr>
					<td>需极大帮助</td>
					<td>0</td><td>-</td><td>-</td><td>0</td><td>0</td><td>0</td><td>0</td><td>5</td>
					<td>5</td><td>0</td>
				</tr>
				<tr>
					<td>完全依赖</td>
					<td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>-</td><td>0</td>
					<td>0</td><td>-</td>
				</tr>
				<tr>
					<td></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm1" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm2" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm3" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm4" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm5" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm6" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm7" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm8" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm9" /></td>
					<td><input type="number" @input="calculateScore()" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgxm10" /></td>
					<td>{{fxpgjg.zf}}</td>
				</tr>
				<tr><td colspan="12">自理能力分级表</td></tr>
				<tr>
					<th colspan="3">自理能力等级</th>
					<th colspan="3">重度依赖</th>
					<th colspan="2">中度依赖</th>
					<th colspan="2">轻度依赖</th>
					<th colspan="2">无需依赖</th>
				</tr>
				<tr>
					<th colspan="3">等级划分标准</th>
					<th colspan="3">总分≤40分</th>
					<th colspan="2">总分41-60分</th>
					<th colspan="2">总分61-99分</th>
					<th colspan="2">总分100分</th>
				</tr>
				<tr>
					<th colspan="3">需要照护程度</th>
					<th colspan="3">全部需要他人照护</th>
					<th colspan="2">大部分需他人照护</th>
					<th colspan="2">小部分需他人照护</th>
					<th colspan="2">无需他们照护</th>
				</tr>
				<tr><td colspan="12"><span>评估结果：</span><input style="width:80%" type="text" @keydown="nextFocus($event)"
					v-model="fxpgjg.pgjg" /></td></tr>
			</table>
			<div style="display: flex;justify-content: space-between;" class="padd-t-10">
				<div><span>评估签字：</span>
				<select-input :search="true" class="wh120"
					@change-data="resultChange" :not_empty="false" :child="hsList"
					:index="'ryxm'" :index_val="'rybm'" :val="fxpgjg.pgry"
					:name="'fxpgjg.pgry'"> </select-input></div>
				<div class="flex-container"><span style="line-height: 36px;">评估日期：</span><input type="text"
				class="zui-input wh160" id="jlsj" v-model="fxpgjg.pgrq" /> </div>
			</div>
			<div style="color:blue" class="padd-t-10">注：患者入院时及病情发生变化时需要进行评估！</div>
		</div>


		<!-- Braden压疮危险因素评估、监控表 -->
		<div class="fxpg padd-t-10" v-if="hlfxlx == '2'">
			<table border="0" cellspacing="0" cellpadding="0">
				<tr>
					<th rowspan="2">项目</th>
					<th colspan="4">感觉</th>
					<th colspan="4">潮湿</th>
					<th colspan="4">活动力</th>
					<th colspan="4">移动力</th>
					<th colspan="4">营养</th>
					<th colspan="3">摩擦力和剪切力</th>
					<th rowspan="2">总分</th>
				</tr>
				<tr>
					<td>完全受限</td><td>非常受限</td><td>轻度受限</td><td>未受损</td>
					<td>持续潮湿</td><td>潮湿</td><td>有时潮湿</td><td>很少潮湿</td>
					<td>限制卧床</td><td>可以坐椅子</td><td>偶尔行走</td><td>经常行走</td>
					<td>完全无法移动</td><td>严重受限</td><td>轻度受限</td><td>未受限</td>
					<td>非常差</td><td>可能不足够</td><td>足够</td><td>非常好</td>
					<td>有问题</td><td>有潜在问题</td><td>无明显问题</td>
				</tr>
				<tr>
					<td>分值</td>
					<td>1</td><td>2</td><td>3</td><td>4</td>
					<td>1</td><td>2</td><td>3</td><td>4</td>
					<td>1</td><td>2</td><td>3</td><td>4</td>
					<td>1</td><td>2</td><td>3</td><td>4</td>
					<td>1</td><td>2</td><td>3</td><td>4</td>
					<td>1</td><td>2</td><td>3</td><td></td>
				</tr>
				<tr>
					<td>得分</td>
					<td><input type="checkbox" @click="calculateScore()" id="green11" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm11"/>
						<label for="green11"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green12" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm12"/>
						<label for="green12"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green13" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm13"/>
						<label for="green13"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green14" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm14"/>
						<label for="green14"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green15" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm15"/>
						<label for="green15"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green16" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm16"/>
						<label for="green16"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green17" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm17"/>
						<label for="green17"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green18" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm18"/>
						<label for="green18"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green19" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm19"/>
						<label for="green19"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green20" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm20"/>
						<label for="green20"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green21" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm21"/>
						<label for="green21"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green22" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm22"/>
						<label for="green22"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green23" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm23"/>
						<label for="green23"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green24" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm24"/>
						<label for="green24"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green25" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm25"/>
						<label for="green25"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green26" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm26"/>
						<label for="green26"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green27" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm27"/>
						<label for="green27"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green28" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm28"/>
						<label for="green28"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green29" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm29"/>
						<label for="green29"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green30" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm30"/>
						<label for="green30"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green31" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm31"/>
						<label for="green31"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green32" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm32"/>
						<label for="green32"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green33" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm33"/>
						<label for="green33"></label></td>
					<td>{{fxpgjg.zf}}</td>
				</tr>
				<tr>
					<td colspan="25">
						<span>评估结果：</span><input style="width:30%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.pgjg" />
					<span class="padd-l-10">护理措施：</span><input style="width:40%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.hlcs" />
					</td>
				</tr>
			</table>
			<div style="display: flex;justify-content: space-between;" class="padd-t-10">
				<div><span>评估签字：</span>
				<select-input :search="true" class="wh120"
					@change-data="resultChange" :not_empty="false" :child="hsList"
					:index="'ryxm'" :index_val="'rybm'" :val="fxpgjg.pgry"
					:name="'fxpgjg.pgry'"> </select-input></div>
				<div class="flex-container"><span style="line-height: 36px;">评估日期：</span><input type="text"
				class="zui-input wh160" id="jlsj" v-model="fxpgjg.pgrq" /> </div>
			</div>
			<div class="padd-t-10">≤18分提示有发生压疮的危险；15-18分为低度危险；13-14分为中度危险；10-12分为高度危险；≤9分为非常危险。</div>
			<div class="padd-t-10">护理措施：①使用气垫床 ②q2b翻身 ③局部减压 ④换药 ⑤皮肤清洁 ⑥床单位清洁干燥 ⑦营养支持</div>
			<div style="color:blue" class="padd-t-10">注：≤12分每日评估一次；13-14分3天评估一次；15-18分每周一次。</div>
		</div>


		<!-- 导管滑脱风险评估、监控表 -->
		<div class="fxpg padd-t-10" v-if="hlfxlx == '3'">
			<table border="0" cellspacing="0" cellpadding="0">
				<tr>
					<th rowspan="2">项目</th>
					<th colspan="7">Ⅰ类导管</th>
					<th colspan="6">Ⅱ类导管</th>
					<th colspan="4">Ⅲ类导管</th>
					<th colspan="2">意识</th>
					<th colspan="4">其他</th>
					<th rowspan="2">总分</th>
				</tr>
				<tr>
					<td>胸管</td><td>T管</td><td>口鼻插管</td><td>天创呼吸机管</td>
							<td>气管切开导管</td><td>动静脉插管</td><td>脑室引流管</td>
					<td>术后胃管营养管</td><td>腹腔双套管</td><td>负压球</td><td>深静脉导管</td><td>三腔管</td>
							<td>造瘘管</td>
					<td>导尿管</td><td>输液管</td><td>胃管</td><td>氧气管</td>
					<td>烦躁</td><td>意识不清</td>
					<td>不配合</td><td>儿童≤7周岁</td><td>老年人≥65岁</td><td>睡眠</td>
				</tr>
				<tr>
					<td>分值</td>
					<td>3</td><td>3</td><td>3</td><td>3</td><td>3</td><td>3</td><td>3</td>
					<td>2</td><td>2</td><td>2</td><td>2</td><td>2</td><td>2</td>
					<td>1</td><td>1</td><td>1</td><td>1</td>
					<td>4</td><td>3</td>
					<td>4</td><td>3</td><td>2</td><td>2</td>
					<td></td>
				</tr>
				<tr>
					<td>得分</td>
					<td><input type="checkbox" @click="calculateScore()" id="green34" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm34"/>
						<label for="green34"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green35" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm35"/>
						<label for="green35"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green36" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm36"/>
						<label for="green36"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green37" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm37"/>
						<label for="green37"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green38" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm38"/>
						<label for="green38"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green39" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm39"/>
						<label for="green39"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green40" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm40"/>
						<label for="green40"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green41" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm41"/>
						<label for="green41"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green42" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm42"/>
						<label for="green42"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green43" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm43"/>
						<label for="green43"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green44" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm44"/>
						<label for="green44"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green45" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm45"/>
						<label for="green45"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green46" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm46"/>
						<label for="green46"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green47" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm47"/>
						<label for="green47"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green48" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm48"/>
						<label for="green48"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green49" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm49"/>
						<label for="green49"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green50" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm50"/>
						<label for="green50"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green51" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm51"/>
						<label for="green51"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green52" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm52"/>
						<label for="green52"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green53" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm53"/>
						<label for="green53"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green54" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm54"/>
						<label for="green54"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green55" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm55"/>
						<label for="green55"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green56" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm56"/>
						<label for="green56"></label></td>
					<td>{{fxpgjg.zf}}</td>
				</tr>
				<tr>
					<td colspan="25">
						<span>评估结果：</span><input style="width:30%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.pgjg" />
					<span class="padd-l-10">护理措施：</span><input style="width:40%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.hlcs" />
					</td>
				</tr>
			</table>
			<div style="display: flex;justify-content: space-between;" class="padd-t-10">
				<div><span>评估签字：</span>
				<select-input :search="true" class="wh120"
					@change-data="resultChange" :not_empty="false" :child="hsList"
					:index="'ryxm'" :index_val="'rybm'" :val="fxpgjg.pgry"
					:name="'fxpgjg.pgry'"> </select-input></div>
				<div class="flex-container"><span style="line-height: 36px;">评估日期：</span><input type="text"
				class="zui-input wh160" id="jlsj" v-model="fxpgjg.pgrq" /> </div>
			</div>
			<div class="padd-t-10">总分≥5分提示有导管滑脱的危险。</div>
			<div class="padd-t-10">护理措施：①加强巡视 ②有效固定 ③使用约束具 ④警示标识 ⑤安全教育 ⑥加强交接班</div>
			<div style="color:blue" class="padd-t-10">注：有导管者需每天评估一次</div>
		</div>


		<!-- 跌倒/坠床风险评估、监控表 -->
		<div class="fxpg padd-t-10" v-if="hlfxlx == '4'">
			<table border="0" cellspacing="0" cellpadding="0">
				<tr>
					<th rowspan="2">项目</th>
					<th colspan="4">精神状况</th>
					<th colspan="2">活动情况</th>
					<th colspan="1">年龄</th>
					<th colspan="8">疾病因素</th>
					<th colspan="9">药物因素</th>
					<th colspan="3">感觉功能</th>
					<th colspan="1">既往史</th>
					<th rowspan="2">总分</th>
				</tr>
				<tr>
					<td>意识清醒</td><td>昏睡或昏迷</td><td>嗜睡</td><td>意识模糊/躁动/谵妄/痴呆</td>
					<td>仅能床上活动</td><td>行走需要帮助/使用辅助工具/步态不稳/站立时平衡</td>
					<td>＜7岁/＞65岁</td>
					<td>低血压（包括体位性低血压）</td><td>眩晕症</td><td>帕金森综合症</td><td>癫痫发作</td><td>贫血</td>
							<td>短暂性脑缺血发作TIA</td><td>营养不良</td><td>关节疾病</td>
					<td>麻醉药物</td><td>抗组胺类药物</td><td>缓泻剂或导泻药物</td><td>利尿剂</td>
							<td>降压药</td><td>降糖药物</td><td>抗惊厥药物</td><td>抗抑郁药物</td><td>镇静催眠药物</td>
					<td>单眼或双眼矫正视力＜0.3</td><td>单盲或视野缺损</td><td>双盲</td>
					<td>入院前3月内有跌倒/坠床史</td>
				</tr>
				<tr>
					<td>分值</td>
					<td>0</td><td>1</td><td>2</td><td>4</td>
					<td>2</td><td>4</td><td>2</td>
					<td colspan="8">患有任意一种疾病或一种以上疾病+3</td>
					<td colspan="9">使用任意一种药物+1 使用任意两类药物+2</td>
					<td>1</td><td>2</td><td>3</td>
					<td>3</td>
					<td></td>
				</tr>
				<tr>
					<td>得分</td>
					<td><input type="checkbox" @click="calculateScore()" id="green57" class="green"
						true-value="0" false-value="0" v-model="fxpgjg.pgxm57"/>
						<label for="green57"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green58" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm58"/>
						<label for="green58"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green59" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm59"/>
						<label for="green59"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green60" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm60"/>
						<label for="green60"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green61" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm61"/>
						<label for="green61"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green62" class="green"
						true-value="4" false-value="0" v-model="fxpgjg.pgxm62"/>
						<label for="green62"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green63" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm63"/>
						<label for="green63"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green64" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm64"/>
						<label for="green64"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green65" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm65"/>
						<label for="green65"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green66" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm66"/>
						<label for="green66"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green67" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm67"/>
						<label for="green67"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green68" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm68"/>
						<label for="green68"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green69" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm69"/>
						<label for="green69"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green70" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm70"/>
						<label for="green70"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green71" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm71"/>
						<label for="green71"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green72" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm72"/>
						<label for="green72"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green73" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm73"/>
						<label for="green73"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green74" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm74"/>
						<label for="green74"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green75" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm75"/>
						<label for="green75"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green76" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm76"/>
						<label for="green76"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green77" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm77"/>
						<label for="green77"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green78" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm78"/>
						<label for="green78"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green79" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm79"/>
						<label for="green79"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green80" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm80"/>
						<label for="green80"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green81" class="green"
						true-value="1" false-value="0" v-model="fxpgjg.pgxm81"/>
						<label for="green81"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green82" class="green"
						true-value="2" false-value="0" v-model="fxpgjg.pgxm82"/>
						<label for="green82"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green83" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm83"/>
						<label for="green83"></label></td>
					<td><input type="checkbox" @click="calculateScore()"  id="green84" class="green"
						true-value="3" false-value="0" v-model="fxpgjg.pgxm84"/>
						<label for="green84"></label></td>
					<td>{{fxpgjg.zf}}</td>
				</tr>
				<tr>
					<td colspan="30">
						<span>评估结果：</span><input style="width:30%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.pgjg" />
					<span class="padd-l-10">护理措施：</span><input style="width:40%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.hlcs" />
					</td>
				</tr>
			</table>
			<div style="display: flex;justify-content: space-between;" class="padd-t-10">
				<div><span>评估签字：</span>
				<select-input :search="true" class="wh120"
					@change-data="resultChange" :not_empty="false" :child="hsList"
					:index="'ryxm'" :index_val="'rybm'" :val="fxpgjg.pgry"
					:name="'fxpgjg.pgry'"> </select-input></div>
				<div class="flex-container"><span style="line-height: 36px;">评估日期：</span><input type="text"
				class="zui-input wh160" id="jlsj" v-model="fxpgjg.pgrq" /> </div>
			</div>
			<div class="padd-t-10">分数高表示危机增加：轻度危机：3-6分；中度危机：7-10分；高度危机：11-15分。</div>
			<div class="padd-t-10">护理措施：①活动时有人陪伴 ②外出检查\活动时使用轮椅 ③使用床档或保护性约束
				④警示标识 ⑤加强巡视 ⑥病房设施安排合理 ⑦预防坠床/跌倒健康教育 ⑧严格交接班 ⑨对潜在问题提出注意事项 ⑩护士长检查督促护理措施的落实</div>
			<div style="color:blue" class="padd-t-10">注：入院时评估，特殊情况时评估，慢性病患者每周评估一次。</div>
		</div>

		<!-- 疼痛评估、监控表 -->
		<div class="fxpg padd-t-10" v-if="hlfxlx == '5'">
			<table border="0" cellspacing="0" cellpadding="0">
				<tr>
					<th>面部表情疼痛量表</th>
					<th>无痛</th>
					<th colspan="2">有点痛</th>
					<th colspan="2">轻微疼痛</th>
					<th colspan="2">疼痛明显</th>
					<th colspan="2">疼痛严重</th>
					<th colspan="2">剧烈痛</th>
				</tr>
				<tr>
					<td rowspan="2">疼痛分级量表</td>
					<td>1级-无痛</td><td colspan="3">2级-轻度疼痛（睡眠不受影响）</td>
					<td colspan="3">3级-中度疼痛（睡眠受影响）</td><td colspan="4">4级-重度疼痛（严重影响睡眠）</td>
				</tr>
				<tr>
					<td>0分</td><td>1分</td><td>2分</td><td>3分</td><td>4分</td><td>5分</td><td>6分</td>
					<td>7分</td><td>8分</td><td>9分</td><td>10分</td>
				</tr>
				<tr>
					<td>疼痛部位</td><td colspan="11"><input style="width:98%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.qtxm1" /></td>
				</tr>
				<tr>
					<td>疼痛性质</td><td colspan="11"><input style="width:98%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.qtxm2" /></td>
				</tr>
				<tr>
					<td>疼痛评分</td><td colspan="11"><input style="width:98%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.qtxm3" /></td>
				</tr>
				<tr>
					<td>通知医生</td><td colspan="11"><input style="width:98%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.qtxm4" /></td>
				</tr>
				<tr>
					<td>护理措施</td><td colspan="11"><input style="width:98%" type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.hlcs" /></td>
				</tr>
			</table>
			<div style="display: flex;justify-content: space-between;" class="padd-t-10">
				<div><span>评估签字：</span>
				<select-input :search="true" class="wh120"
					@change-data="resultChange" :not_empty="false" :child="hsList"
					:index="'ryxm'" :index_val="'rybm'" :val="fxpgjg.pgry"
					:name="'fxpgjg.pgry'"> </select-input></div>
				<div class="flex-container"><span style="line-height: 36px;">评估日期：</span><input type="text"
				class="zui-input wh160" id="jlsj" v-model="fxpgjg.pgrq" /> </div>
			</div>
			<div class="padd-t-10">护理措施：①安慰患者 ②解释病情 ③卧床休息 ④患肢体位摆放 ⑤分散注意力 ⑥冷敷 ⑦热敷 ⑧理疗 ⑨针刺</div>
			<div style="display: flex;justify-content: space-between;" class="padd-t-10">
				<div class="flex-container"><span>评估、告知对象（患者或家属）签名：</span>
				<input  type="text" @keydown="nextFocus($event)"
							v-model="fxpgjg.jsqm" class="zui-input "/></div>
				<div class="flex-container"><span style="line-height: 36px;">日期：</span><input type="text"
				class="zui-input wh160" id="jlsj" v-model="fxpgjg.pgrq" /> </div>
			</div>
		</div>
	</div>
</div>
<script type="applicatspann/javascript" src="childpage/hlfxpg.js"></script>
