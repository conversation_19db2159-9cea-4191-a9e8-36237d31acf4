var tableInfo = new Vue({
	el: '#wrapper',
	//混合js字典庫
	mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
	data: {
		popContent: {},
		queryType: '0', //是否按药品汇总查询
		mbModel: false,
		jsonList: [],
		sbkfList: [],
		kcFilter:{
			'0':'全部库存',
			'1':'0库存',
			'2':'非0库存',
		},
		sbkfbm:'01',
		param: {
			'page': 1,
			'rows': 10,
			'sort': '',
			'order': 'desc',
			'shzfbz': 1,
			'kfbm': '',
			'beginrq': null,
			'endrq': null,
			'parm': ''
		}
	},
	mounted: function() {
		this.initKf()
	},
	updated:function(){
		changeWin()
	},
	methods: {
		initKf: function () {
			var parm = {
				page: 1,
				rows: 20000,
				sort: 'sbkfbm',
				tybz: '0'
			}
			$.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmSbkf&types=queryAll&json=" + JSON.stringify(parm),
				function (data) {
					if (data.a == 0) {
						tableInfo.sbkfList = data.d.list;
						tableInfo.param.sbkf = data.d.list[0].sbkfbm;
						tableInfo.getData();
					} else {
						malert("获取库房失败！", 'top', 'defeadted');
					}
				});
		},
		reCheckOne: function (val) {
			Vue.set(this, val[0], val[1]);
			this.getData()
		},
		//库房库存报表导出
		exportKc: function() {
			if(!this.param.sbkf) {
				malert('请选择库房','top','defeadted');
				return;
			}
			//数据查询参数
			var param = {
				'page': 1,
				'rows': 20000,
				'yljgbm': jgbm,
				'sbkf': this.param.sbkf,
				'sbkfmc': '设备'
			};
			//准备地址
			var url = "/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=exportKc&parm=" + JSON.stringify(param);
			$.getJSON(url, function(json) {
					if(json.a == "0") {
						tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
						tableInfo.jsonList = json.d.list;
					} else {
						malert(json.c);
						tableInfo.jsonList = [];
					}
				});
			//组合地址
			this.url = (window.location.protocol + '//' + window.location.host) + url;
			//打开下载页
			window.location = this.url;
		},
		//双击修改有效期和批次停用
		edit: function(index) {
			this.mbModel = true;
			Vue.set(this.popContent, 'xxq', this.fDate(this.jsonList[index].yxqz, 'date'));
			Vue.set(this.popContent, 'xpcty', '0');
			Vue.set(this.popContent, 'sbkf', this.jsonList[index].sbkf);
			Vue.set(this.popContent, 'xtph', this.jsonList[index].xtph);
			Vue.set(this.popContent, 'sbbm', this.jsonList[index].sbbm);
		},
		//药房库房改变
		yfkfChange: function(val) {
			Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
			this.getData();
		},
		//获取数据
		getData: function() {
			//是否按药品汇总
			this.param.sort = this.queryType=='0' ? null : 'sum'
			$.getJSON("/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=kfkc&parm=" + JSON.stringify(this.param), function(json) {
					if(json.a == "0") {
						tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
						tableInfo.jsonList = json.d.list;
					} else {
						malert(json.c);
						tableInfo.jsonList = [];
					}
				});
		},
		saveData: function() {
			if(!this.popContent.xxq) {
				malert('请输入有效期','top','defeadted');

				return;
			}
			if(!this.popContent.xpcty) {
				malert('请输入停用标志','top','defeadted');
				return;
			}

			$.getJSON("/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=update&parm=" +
				JSON.stringify(this.popContent),
				function(data) {
					if(data.a == 0) {
						tableInfo.mbModel=false;
						malert('保存成功','top','success');
						tableInfo.getData();
					} else {
						 malert('上传数据失败','top','defeadted');
					}
				})
		}
	}
});

window.getTime = function(event, type) {
	if(type == 'star') {
		tableInfo.param.beginrq = $(event).val();
	} else if(type == 'end') {
		tableInfo.param.endrq = $(event).val();
	}
};
