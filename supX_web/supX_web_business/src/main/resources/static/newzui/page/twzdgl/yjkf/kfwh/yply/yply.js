    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            jsonList: [],
            param: {
                page: '',
                rows: 100,
                total: ''
            },
        },
        updated:function (){
            changHeight();
        },
        mounted:function (){
            this.getData();
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.closes();
                wap.title='新增药品种类';
                wap.isYpzl = true;
                wap.open();
                wap.popContent={};

            },
            //进入页面加载列表信息
            getData: function () {
                common.openloading('.zui-table-view')
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=queryYpfl&dg=" + JSON.stringify(this.param), function(json) {
                    if(json.a == '0') {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.jsonList = json.d.list;
                    } else {
                        malert(json.c,'top','defeadted')
                    }
                });
                common.closeLoading()
            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.closes();
                wap.title='编辑药品种类'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },



            //编辑加成区间
            editJcqj: function(num) {
                wap.closes();
                wap.isJcqj = true;
                wap.title = "编辑加成区间"
                wap.popContent = JSON.parse(JSON.stringify(this.jcqjList[num]));
                wap.open();
            },

        }
    });
    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],

        data:{
            popContent: {},
        },

        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');
                wap.isYpzl = false;
                wap.isJcqj = false;
            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                    if(!this.popContent.zfbz){
                        Vue.set(this.popContent,'zfbz','0');
                    }
                    if(this.popContent.ypflmc == null) {
                        malert("请输入药品种类名称",'top','defeadted')
                        return;
                    }
                    this.popContent.czybm=userId
                    $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=saveYpfl&json=" + JSON.stringify(wap.popContent),
                        function(data) {
                            if(data.a == 0) {
                                wrapper.getData();
                                wap.closes();
                                malert("数据保存成功",'top','success')
                            } else {
                                malert("上传数据失败",'top','defeadted');
                            }
                        })
            },
        }
    });
