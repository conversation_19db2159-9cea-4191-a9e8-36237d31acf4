var panel=new  Vue({
    el:'#panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    methods:{
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N010064001',
                "ksbm": ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                        if (json.d.length > 0) {
                            for (var i = 0; i < json.d.length; i++) {
                                var csjson = json.d[i];
                                switch (csjson.csqxbm) {
                                    case "N01006400108": //合理用药接口地址参数
                                        if (csjson.csz) {
                                            left.hlyyYkbyPzd = csjson.csz+"/ykbypzd";//默认加载第一个
                                            right.hlyyYwzd =csjson.csz+"/ywzd";//默认加载第一个
                                            //tableInfo.getData();

                                        }
                                        break;
                                }
                            }
                        }
                    }

			if (left.hlyyYkbyPzd.length==0 ){

			 left.hlyyYkbyPzd = "http://127.0.0.1:8082/hlyy"+"/ykbypzd";//默认加载第一个
                	right.hlyyYwzd="http://127.0.0.1:8082/hlyy"+"/ywzd";//默认加载第一个
			}

                left.getData();

            });
        },
        saveYKB: function () {
            if (right.hlyyYwzid == 0) {
                malert("右边药物没选择", 'top', 'defeadted');
                return
            }
            if (left.hlyyYwzdYwid == '' || left.hlyyYwzdYwid == undefined || left.hlyyYwzdYwid == null) {
                malert("左边药品没指定", 'top', 'defeadted');
                return
            }

            var param = "ypbm=" + left.hlyyYwzdYwid + "&ywid=" + right.hlyyYwzid+"&yljgbm="+jgbm

            $.getJSON(left.hlyyYkbyPzd + "/update?" + param, function (json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                //right.totlePage = json.pageCount;
                if (json.code == 1) {
                    malert("保存成功！", 'top', 'success');
                    return

                } else {
                    malert("失败！",'top','defeadted');
                    return
                }
            });


        },
    },
    mounted:function () {
        this.getCsqx();
    },







})
var left = new Vue({
    el: '#left',
    //混合js字典庫
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        hlyyYkbyPzd:'',
        hlyyYKBypbm:'',
        hlyyYwzdYwid:0,
        YkbyPzdList: [],
    },
    methods: {
        getData: function() {
            var param="pageNo="+this.param.page+"&pageSize="+this.param.rows;
            $.getJSON(left.hlyyYkbyPzd +"/page?"+param, function(json) {
                left.totlePage = json.pageCount;
                left.YkbyPzdList = json.data;
                common.closeLoading()
            });

           // this.selectData("头孢");
        },
        selectData:function(key,ypbm){
           // var param="pageNo="+this.param.page+"&pageSize="+this.param.rows+"&key="+key;
            var param="key="+key;
            $.getJSON(right.hlyyYwzd +"/findAll?"+param, function(json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                //right.totlePage = json.pageCount;
                right.YwzdList = json;
                common.closeLoading()
            });
            left.hlyyYwzdYwid=ypbm;

        },




        getDataYkb:function() {
            var param="pageNo="+this.param.page+"&pageSize="+this.param.rows+"&key="+$("#ykbvlue").val();
            $.getJSON(left.hlyyYkbyPzd +"/findpage?"+param, function(json) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                //right.totlePage = json.pageCount;
                left.YkbyPzdList = json.data;
                common.closeLoading()
            });
        },


    }
});
var right = new Vue({
    el: '#right',
    //混合js字典庫
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        hoverIndex1:undefined,
        activeIndex1:undefined,
        hlyyYwzid:'',
        YwzdList:[]
    },
    methods:{
        changerYWZD:function(id){
            right.hlyyYwzid=id;

        },

    getDataYwzd:function() {
       // var oshow=document.getElementById("ywzdvlue");
        var param="key="+$("#ywzdvlue").val();
        $.getJSON(right.hlyyYwzd +"/findAll?"+param, function(json) {
            //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
            //right.totlePage = json.pageCount;
            right.YwzdList = json;
            common.closeLoading()
        });
    },


    },
});
