//(function () {
//当前时间11
var dateend = getTodayDateEnd();
var datestart = getTodayDateBegin();
/********************************华丽分割线***************************************/
var bodyMenu = new Vue({
    el: '#bodyMenu',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        typeArr: {
            '0': 'all',
            '1': 'kf',
            '2': 'zs',
            '3': 'sy',
            '4': 'hl',
            '5': 'zl',
            '6': 'wh',
            '7': 'syt',
        },
        frPath:window.top.J_tabLeft.obj.frprintver == "3" ? '%2F' :'/',
        tabArr: [
            {text: '全部'},
            {text: '口服'},
            {text: '注射'},
            {text: '输液'},
            {text: '护理'},
            {text: '治疗'},
            {text: '雾化'},
            {text: '输液贴'},
        ],
        yzzxdList: [], //医嘱执行单集合
        yzlx: 'qb', //医嘱类型默认全部
        zyhs: [], //住院号集合
        pcs: [], //频次集合
        qsxzList: [],
        sxlx: 'all',
        picked: '0',
        num: null,
        sytPrint: {},
        popContent: {},
        isShowPslr: false,
        sytNumberOfColumns: 1,
        pcList: {
            "a": "20~80"
        },
        pc: "a",
        searchContent: {
            sysd: '3',
            sytprint: '0',
			zfbz:'0',
        },
        ksrq: '',
        jsrq: '',
        isChecked1: [],
        printChecked: [],
        sytparm: {},
        sfbd: "",
        IsPrint_tran: {
            '0': '未打印',
            '1': '已打印',
        },
        isOver: false,
		Iszf: {
		    '0': '未作废',
		    '1': '已作废',
		},
    },
    mounted: function () {
        this.getDyjMc();
        Mask.newMask(this.MaskOptions('dbegin'));
        Mask.newMask(this.MaskOptions('dEnd'));
        //默认加载当前时间
        this.ksrq = datestart;
        this.jsrq = dateend;
        laydate.render({
            elem: '#dbegin',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                bodyMenu.ksrq = value;
                bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
            }
        });
        laydate.render({
            elem: '#dEnd',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                bodyMenu.jsrq = value;
                bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
            }
        });
    },
    watch: {
        'yzzxdList': function (newValue, OldValue) {
            var _list = newValue, _length = newValue.length, _brPosition = [0], _brItemListSumHeight = 0;
            if (newValue.length > 0) {
                common.openloading('#wrapper')
                for (var i = 0; i < newValue.length; i++) {
                    if (this.yzzxdList[i].yzxx.length == 0) {
                        this.yzzxdList.splice(i, 1)
                        continue;
                    }
                    this.isAllFlag = false;
                    for (var int = 0; int < newValue[i].yzxx.length; int++) {
                        // 计算当前元素定位
                        var _yzxxListLength = _list[i].yzxx.length;
                        _brPosition[i + 1] = (93 + _yzxxListLength * 40) + (_brPosition[i] == undefined ? _brPosition[i - 1] : _brPosition[i]);
                        // 计算总高度
                        if (i === _length - 1) {
                            // + 70 + _yzxxListLength * 40
                            _brItemListSumHeight = _brPosition[i + 1];
                        }
                        if (newValue[i].yzxx[int].numb >= 1) {
                            this.isAllFlag = true;
                        }
                    }
                    this.yzzxdList[i].isAllFlag = this.isAllFlag
                }
                // 初始化显示边界
                this.$nextTick(function () {
                    var _height = $("#wrapper").height();
                    this.brListWinSize = {
                        top: 0 - _height,
                        bottom: _height * 2
                    }
                });
                yzclRight.brItemListSumHeight = _brItemListSumHeight;
                yzclRight.brPosition = this.notempty(_brPosition);
                common.closeLoading()
                this.isOverClick(true)
            }
        },
    },
    methods: {

        // 今天
        today: function () {
            this.ksrq = datestart;
            this.jsrq = dateend;
            bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
        },
        // 明天
        tomorrow: function () {
            var date = new Date();
            var next = new Date(date.getTime() + 24 * 60 * 60 * 1000);
            this.ksrq = this.fDate(next, 'date') + " 00:00:00";
            this.jsrq = this.fDate(next, 'date') + " 23:59:59";
            bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
        },
        reCheckBox1: function (val) {
            Vue.set(this.isChecked1, val[1], val[2]);
        },
        initData: function () {
			
            // var getZyhs = JSON.parse(sessionStorage.getItem("HszbrItem"));
            this.zyhs = yzclLeft.arr;
            // if (getZyhs) {
            //     for (var i = 0; i < getZyhs.length; i++) {
            //         this.zyhs.push(getZyhs[i].zyh);
            //     }
            //     ;
            //     // sessionStorage.setItem("psjglr_HszbrItem", JSON.stringify(getZyhs));
            // }
            bodyMenu.clickMenu('all', 0);
        },
        showLsYz: function () {
            pslr.getPsData();
            pslr.isShow = true;
        },
        getDyjMc: function () {
            window.top.J_tabLeft.csqxparm.csbm = "N010024006";
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0) {
                    console.log(json.d);
                    if (json.d != null && json.d != undefined && json.d.length > 0) {
                        bodyMenu.sytPrint = json.d[0];
                    }
                    // window.print();
                    //}
                }
            });
        },
        setDyDate: function (fzxhcheck,printChecked) {
            // @yqq 点击打印修改打印次数
            var parm = {
                searchzyh: bodyMenu.zyhs,
                ksbm: yzclLeft.jsContent.ksbm,
                beginrq: bodyMenu.ksrq,
                endrq: bodyMenu.jsrq,
                zxdlx: '7',
                searchpcbm: bodyMenu.pcs,
                fzxhArr: fzxhcheck,
            };
            //写打印次数
            var json = '{"list":' + JSON.stringify(parm) + '}';
            //this.postAjax('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzxdPrintCount&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm), function (json) {
            this.postAjax('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzxdPrintCount&ksbm=' + yzclLeft.jsContent.ksbm ,
            		json,function (json) {
                if (json.a == "0") {
                    bodyMenu.clickMenu('syt', 7);
                } else {
                    malert("未正确记录打印次数！", "top", "defeadted")
                }
            });
        },

        // 弹出打印格式
        printFormat: function () {
			            if (this.num == 7) {
                if(this.printChecked >20){
                    malert("请重新勾选输液贴打印，不能超过20个",'top','defeadted');
                    return false;
                }
                malert("正在打印输液贴！请稍后....");
                this.printChecked = [];
                // lxPrintSyt   printSyt
                loadOpenPage('childpage/printSyt',function () {
                    window.printBox.printChecked = [];
                    var fzxhcheck = [];//临时存fzxh
                    for (var dxx = 0; dxx < bodyMenu.isChecked1.length; dxx++) {
                        if (bodyMenu.isChecked1[dxx] == true) {
                            var yzxx = bodyMenu.yzzxdList[dxx].yzxx;
                            for (var i = 0; i < yzxx.length; i++) {
                                var fzxh = yzxx[i]["fzxh"];
                                var u = fzxhcheck.indexOf(fzxh);
                                if (yzxx[i].fzh != 0 && u >= 0) {
                                    continue;
                                }
                                var parmfr = {};
                                if (yzclLeft.caqxContent.N03004200245 = '1') {
                                    if (yzxx) {
                                        parmfr.fzxh = yzxx[i]['fzxh'];
                                        parmfr.nl = bodyMenu.yzzxdList[dxx]['nl'];
                                        parmfr.nldw = bodyMenu.yzzxdList[dxx]['nldw'];
                                        parmfr.brxm = bodyMenu.yzzxdList[dxx]['brxm'];
                                        parmfr.yzlxmc = bodyMenu.yzzxdList[dxx]['yzlxmc'];
                                        parmfr.ksbm = yzxx[i]['ysks'];
                                        parmfr.yysj = yzxx[i]['yysj'];
                                        parmfr.yyrq = bodyMenu.fDate(yzxx[i]['zx_begin'],'Monthday');
                                        parmfr.yyffmc = yzxx[i]['yyffmc'];
                                        parmfr.sysddw = yzxx[i]['sysddw'];
                                        parmfr.sysd = yzxx[i]['sysd'];
                                        parmfr.pcmc = yzxx[i]['pcmc'];
                                        parmfr.ksmc = yzxx[i]['ysksmc'];
                                        parmfr.zyh = yzxx[i]['zyh'];
                                        parmfr.bzsm = yzxx[i]['bzsm'];
                                        parmfr.yzxx = yzxx;
                                        parmfr.brxb = bodyMenu.yzzxdList[dxx]['brxb'];
                                        parmfr.rycwbh = bodyMenu.yzzxdList[dxx]['rycwbh'];
                                        parmfr.pcbm = yzxx[i]['pcbm'];
                                        parmfr.cs = i + 1;
                                        bodyMenu.printChecked.push(parmfr);
                                        window.printBox.printChecked.push(parmfr);
                                        fzxhcheck.push(yzxx[i]['fzxh']);
                                    }
                                }
                                else {
                                    if (yzxx && yzxx[i].cs != undefined && yzxx[i].cs != null) {
                                        for (var j = 0; j < yzxx[i].cs; j++) {
                                            parmfr.fzxh = yzxx[i]['fzxh'];
                                            parmfr.zyh = yzxx[i]['zyh'];
                                            parmfr.ksbm = yzxx[i]['ysks'];
                                            parmfr.ksmc = yzxx[i]['ysksmc'];
                                            parmfr.pcbm = yzxx[i]['pcbm'];
                                            parmfr.pcmc = yzxx[i]['pcmc'];
                                            parmfr.yyffmc = yzxx[i]['yyffmc'];
											parmfr.yyrq = bodyMenu.fDate(yzxx[i]['yyrq'],'Monthday');
                                            parmfr.cs = j + 1;
                                            bodyMenu.printChecked.push(parmfr);
                                            fzxhcheck.push(yzxx[i]['fzxh']);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    bodyMenu.setDyDate(fzxhcheck,bodyMenu.printChecked);
                    var reportlets = '', str = '', num = 0;
                    if(yzclLeft.caqxContent.N03004200262 =='1'){
                        // if(yzclLeft.caqxContent.N03004200245 = '1'){
                            // ',sytdysj:'" + this.printChecked[0].sytdysj + "
                            str = "[{reportlet: 'fpdy" + bodyMenu.frPath + "hsz" + bodyMenu.frPath + "hsz_sytNew.cpt',ksbm:'" + yzclLeft.jsContent.ksbm + "'}]";
                            bodyMenu.$nextTick(function () {
                                //if(!bodyMenu.printSyt(1, str)){
                                    printFormat.printHide=true;
                                   setTimeout(function () {
                                       window.print();
                                       $('#loadingPrint').html('')
                                       printFormat.printHide=false;
                                   },50)

                                //}
                            })
                            return false;
                        // }
                    }
                    //组织参数

                    if (bodyMenu.printChecked.length > 10) {
                        for (var i = 0; i < bodyMenu.printChecked.length; i++) {
                            var fzxh = bodyMenu.printChecked[i].fzxh;
                            var zyh = bodyMenu.printChecked[i].zyh;
                            var pcbm = bodyMenu.printChecked[i].pcbm;
                            var cs = bodyMenu.printChecked[i].cs;
                            if (i == 0) {
                                str += "{reportlet: 'fpdy" + bodyMenu.frpath + "hsz" + bodyMenu.frpath + "hsz_syt.cpt',cs:'" + cs + "',pcbm:'" + pcbm + "',yljgbm:'" + jgbm + "',fzxh:'" + fzxh + "',ksbm:'" + yzclLeft.jsContent.ksbm + "',zyh:'" + zyh + "',endtime:'" + bodyMenu.sytparm.endtime + "',starttime:'" + bodyMenu.sytparm.starttime + "'}";
                            } else {
                                str += ",{reportlet: 'fpdy" + bodyMenu.frpath + "hsz" + bodyMenu.frpath + "hsz_syt.cpt',cs:'" + cs + "',pcbm:'" + pcbm + "',yljgbm:'" + jgbm + "',fzxh:'" + fzxh + "',ksbm:'" + yzclLeft.jsContent.ksbm + "',zyh:'" + zyh + "',endtime:'" + bodyMenu.sytparm.endtime + "',starttime:'" + bodyMenu.sytparm.starttime + "'}";
                            }
                            if (i == 9) {
                                num++;
                                reportlets = '[' + str + ']';
                                bodyMenu.printSyt(num, reportlets)
                                bodyMenu.printChecked.splice(0, (i + 1))
                                i = -1;
                                str = '';
                            }
                        }
                    } else {
                        for (var i = 0; i < bodyMenu.printChecked.length; i++) {
                            var fzxh = bodyMenu.printChecked[i].fzxh;
                            var zyh = bodyMenu.printChecked[i].zyh;
                            var pcbm = bodyMenu.printChecked[i].pcbm;
                            var cs = bodyMenu.printChecked[i].cs;
                            if (i == 0) {
                                str += "{reportlet: 'fpdy" + frpath + "hsz" + frpath + "hsz_syt.cpt',cs:'" + cs + "',pcbm:'" + pcbm + "',yljgbm:'" + jgbm + "',fzxh:'" + fzxh + "',ksbm:'" + yzclLeft.jsContent.ksbm + "',zyh:'" + zyh + "',endtime:'" + bodyMenu.sytparm.endtime + "',starttime:'" + bodyMenu.sytparm.starttime + "'}";
                            } else {
                                str += ",{reportlet: 'fpdy" + frpath + "hsz" + frpath + "hsz_syt.cpt',cs:'" + cs + "',pcbm:'" + pcbm + "',yljgbm:'" + jgbm + "',fzxh:'" + fzxh + "',ksbm:'" + yzclLeft.jsContent.ksbm + "',zyh:'" + zyh + "',endtime:'" + bodyMenu.sytparm.endtime + "',starttime:'" + bodyMenu.sytparm.starttime + "'}";
                            }
                        }
                        reportlets = '[' + str + ']';
                        bodyMenu.printSyt(1, reportlets);
                        str = '';
                    }
                    if (str != '') {
                        reportlets = '[' + str + ']';
                        bodyMenu.printSyt(num++, reportlets)
                    }
                    bodyMenu.$nextTick(function () {
                        $('#loadingPrint').html('')
                    })
                });

            }
            else {
                if (this.num == 3 && yzclLeft.caqxContent.N03004200248 == '1') {//调用帆软
                    //组织参数
                    var sfbd = "";
                    if (this.sfbd) {
                        sfbd = "1";
                    } else {
                        sfbd = "2";
                    }
                    var reportlets = "[{reportlet: 'hsz" + this.frPath + "hsz_ypypzxd_cs.cpt',zyh:'" + bodyMenu.zyhs + "',yljgbm:'" + jgbm + "',starttime:'" + bodyMenu.ksrq + "',endtime:'" + bodyMenu.jsrq + "',sfbd:'" + sfbd + "',czybm:'" + userId + "'}]";
                    if (!FrPrint(reportlets, null, null, false)) {
                        printFormat.setData();
                        printFormat.isShow = true;
                    }
                } else {
                    printFormat.setData();
                    printFormat.isShow = true;
                }
            }
        },
        printFormatXsk:function (){
            var dyyzlx='';
            if(this.yzlx=='ls'){
                dyyzlx='0';
            }else if(this.yzlx=='cq'){
                dyyzlx='1';
            }
            console.log(111111,);
            var frzyh = "";
            for (var i = 0; i < bodyMenu.zyhs.length; i++) {
                frzyh = frzyh + bodyMenu.zyhs[i] + ",";
            }
            reportlets = "[{reportlet: 'fpdy" + this.frPath + "zyys"+this.frPath+"hsz_syxsk.cpt',zyh:'" + frzyh + "',yljgbm:'000001',ksbm:'" + yzclLeft.jsContent.ksbm + "',starttime:'" + bodyMenu.ksrq + "',endtime:'" + bodyMenu.jsrq +"',yzlx:'" +dyyzlx+"'}]";
            if (FrPrint(reportlets, null)) {
            }
        },
        //打印帆软A4单据
        printFormatA4: function () {
            
            if (bodyMenu.zyhs.length <= 0) {//住院号为空则直接返回不请求后台
                malert("请先选择需要打印的住院号！", 'top', 'defeadted');
                return;
            }
            var printType = "";
            var printTypeRefresh = "";
            var countType = '';
            if (this.num == 3) {
                printType = '3';
                countType = 'syprint';
                printTypeRefresh = 'sy';
            } else if (this.num == 7) {
                printType = '7';
                countType = 'sytprint';
                printTypeRefresh = 'syt';
            }
            if (!this.sfbd) {//非补打
                //打印判断
                var sfdy = false;//是否打印
                outLoop:
                    for (var i = 0; i < bodyMenu.yzzxdList.length; i++) {
                        if (bodyMenu.yzzxdList[i].yzxx && bodyMenu.yzzxdList[i].yzxx.length > 0) {
                            innerLoop:
                                for (var j = 0; j < bodyMenu.yzzxdList[i].yzxx.length; j++) {
                                    var yz = bodyMenu.yzzxdList[i].yzxx[j];
                                    if (!yz[countType] || yz[countType] <= 0) {
                                        sfdy = true;
                                        break innerLoop;
                                        break outLoop;
                                    }
                                }
                        }
                    }

                if (!sfdy) {
                    malert("数据全部都已经打印过了，如果需要再打印，请选择补打！", "top", "defeadted");
                    return;
                }

            }

            //拼截需要打印的住院号
            var frzyh = "";
            for (var i = 0; i < bodyMenu.zyhs.length; i++) {
                frzyh = frzyh + bodyMenu.zyhs[i] + ",";
            }
            var fzxhcheck = [];//临时存fzxh
            for (var dxx = 0; dxx < bodyMenu.isChecked1.length; dxx++) {
                if (bodyMenu.isChecked1[dxx] == true) {
                    var yzxx = bodyMenu.yzzxdList[dxx].yzxx;
                    for (var i = 0; i < yzxx.length; i++) {
                        var fzxh = yzxx[i]["fzxh"];
                        var u = fzxhcheck.indexOf(fzxh);
                        if (u >= 0) {
                            continue;
                        }
                        var parmfr = {};
                        if (yzclLeft.caqxContent.N03004200245 = '1') {
                            if (yzxx) {
                                parmfr.fzxh = yzxx[i]['fzxh'];
                                parmfr.nl = bodyMenu.yzzxdList[dxx]['nl'];
                                parmfr.nldw = bodyMenu.yzzxdList[dxx]['nldw'];
                                parmfr.brxm = bodyMenu.yzzxdList[dxx]['brxm'];
                                parmfr.ksbm = yzxx[i]['ysks'];
                                parmfr.yysj = yzxx[i]['yysj'];
                                parmfr.yyrq = yzxx[i]['yyrq'];
                                parmfr.yyffmc = yzxx[i]['yyffmc'];
                                parmfr.sysddw = yzxx[i]['sysddw'];
                                parmfr.sysd = yzxx[i]['sysd'];
                                parmfr.pcmc = yzxx[i]['pcmc'];
                                parmfr.ksmc = yzxx[i]['ysksmc'];
                                parmfr.zyh = yzxx[i]['zyh'];
                                parmfr.bzsm = yzxx[i]['bzsm'];
                                parmfr.yzxx = yzxx;
                                parmfr.rycwbh = bodyMenu.yzzxdList[dxx]['rycwbh'];
                                parmfr.pcbm = yzxx[i]['pcbm'];
                                parmfr.cs = i + 1;
                                bodyMenu.printChecked.push(parmfr);
                                fzxhcheck.push(yzxx[i]['fzxh']);
                            }
                        }
                        else {
                            if (yzxx && yzxx[i].cs != undefined && yzxx[i].cs != null) {
                                for (var j = 0; j < yzxx[i].cs; j++) {
                                    parmfr.fzxh = yzxx[i]['fzxh'];
                                    parmfr.zyh = yzxx[i]['zyh'];
                                    parmfr.ksbm = yzxx[i]['ysks'];
                                    parmfr.ksmc = yzxx[i]['ysksmc'];
                                    parmfr.pcbm = yzxx[i]['pcbm'];
                                    parmfr.cs = j + 1;
                                    bodyMenu.printChecked.push(parmfr);
                                    fzxhcheck.push(yzxx[i]['fzxh']);
                                }
                            }
                        }
                    }
                }
            }
            var reportlets = "";
            var sfbd = "";
            if (this.sfbd) {
                sfbd = "1";
            } else {
                sfbd = "2";
            }
            var yzlx ="";
            if(this.yzlx=="qb"){
                yzlx="";
            }else if(this.yzlx=="ls"){
                yzlx="0";
            }else if(this.yzlx="cq"){
                yzlx="1";
            }
            if (this.num == 3) {//执行单
                reportlets = "[{reportlet: 'hsz" + this.frPath + "hsz_ypypzxd.cpt',zyh:'" + frzyh +"',yzlx:'"+yzlx+"',yljgbm:'000001',ksbm:'" + yzclLeft.jsContent.ksbm + "',starttime:'" + bodyMenu.ksrq + "',endtime:'" + bodyMenu.jsrq + "',sfbd:'" + sfbd + "'}]";
                if (FrPrint(reportlets, null)) {
                }
            } else if (this.num == 7) {
                reportlets = "[{reportlet: 'hsz" + this.frPath + "syt_new.cpt',zyh:'" + frzyh + "',yzlx:'"+yzlx+"',yljgbm:'" + jgbm + "',ksbm:'" + yzclLeft.jsContent.ksbm + "',starttime:'" + bodyMenu.ksrq + "',endtime:'" + bodyMenu.jsrq + "',sfbd:'" + sfbd + "',czybm:'" + userId + "'}]";
                if (FrPrint(reportlets, null)) {
                }
            }
            if (printType) {
                var parm = {
                    searchzyh: bodyMenu.zyhs,
                    ksbm: yzclLeft.jsContent.ksbm,
                    beginrq: bodyMenu.ksrq,
                    endrq: bodyMenu.jsrq,
                    zxdlx: printType,
                    searchpcbm: bodyMenu.pcs
                };
                //写打印次数
                var json = '{"list":' + JSON.stringify(parm) + '}';
                this.postAjax('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzxdPrintCount&ksbm=' + yzclLeft.jsContent.ksbm , json,
                    function (json) {
                // $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzxdPrintCount&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                //     function (json) {
                        if (json.a == "0") {
                            bodyMenu.clickMenu(printTypeRefresh, bodyMenu.num);
                        } else {
                            malert("未正确记录打印次数！", "top", "defeadted")
                        }
                    });
            }
            return;
        },
        FrPrint: function (num, reportlets, printerName, paperSize) {
            setTimeout(function () {
                //var printurl = "/FR/ReportServer";
                var printurl = encodeURI(document.location.origin + "/FR/ReportServer");
                //var reportlets ="[{reportlet: 'YK%2FYK_RKD.cpt',DJHM:'RKD1201811000002',yljgbm:'000001'}]";
                var config = {
                    printUrl: printurl,
                    isPopUp: false,
                    // 是否弹出设置窗口，true为弹出，false为不弹出
                    data: {
                        reportlets: reportlets // 需要打印的模版列表
                    },
                    printType: 1, // 打印类型，0为零客户端打印，1为本地打印
                    // 以下为零客户端打印的参数，仅当 printType 为 0 时生效
                    ieQuietPrint: false,// IE静默打印设置 true为静默，false为不静默
                    pageType: 0, // 打印页码类型：0：所有页，1：当前页，2：指定页
                    copy: 1, // 打印份数
                    callback: function (sessionID) {
                    }
                };

                if (printerName) {
                    config.printerName = printerName;
                }
                if (paperSize) {
                    config.paperSizeText = paperSize;
                }
                console.log(config);
                if (window.FR) {
                    window.FR.doURLPrint(config);
                    return true;
                } else {
                    return false;
                }
            }, num * 1000)
        },
        printSyt: function (i, reportlets) {
            //输液贴帆软打印
            this.FrPrint(i, reportlets, this.sytPrint.csz, this.sytPrint.cszmc)
        },
        // printSyt:function(){
        //     if(!this.isChecked1 || this.isChecked1.length<=0){
        //         malert("请选择要打印的输液贴!");
        //         return;
        //     }
        //     var beginrq = $("#dbegin").val();
        //     var endrq = $("#dEnd").val();
        //     for(var dxx=0;dxx<this.isChecked1.length;dxx++){
        //         if (this.isChecked1[dxx] == true) {
        //             var yzxx = this.yzzxdList[dxx].yzxx;
        //             if(yzxx && yzxx.length >0){
        //                 var reportlets ="[{reportlet: 'yfgl%2Fyfgl_syt.cpt',yljgbm:'"+jgbm+"',yzxh:'"+yzxx[0].yzxh+"',fzh:'"+ yzxx[0].fzh +"',beginrq:'" + beginrq + "',endrq:'" + endrq + "'}]";
        //                 if (!FrPrint(reportlets,null)){
        //                     window.print();
        //                 }
        //             }
        //         }
        //     }
        // },
        searchListHc: function () {
            if (event.keyCode == 13) {
                bodyMenu.clickMenu(yzlx); //根据科室过滤病人
            }
        },
        YzlxChange: function (type) {
            
            this.yzlx = type;
            bodyMenu.clickMenu(bodyMenu.sxlx);
        },
        resultChangeSysd: function (val) {
            
            console.log(val);
            if (val[2].length > 1) {
                if (Array.isArray(this[val[2][0]])) {
                    Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num);
                } else {
                    Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                    console.log(this.searchContent.sytprint);
                    if (val[3] != null) {
                        Vue.set(this[val[2][0]], val[3], val[4]);
                    }
                    bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num);
                }
            } else {
                this[val[2][0]] = val[0];

            }
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }

        },
        tabBg: function (index) {
            this.num = index;
            this.clickMenu(this.typeArr[index], index);
        },
        //筛选按钮
        clickMenu: function (isClick, index) {
			console.log(yzclLeft)
            
            this.sxlx = isClick;
            this.yzzxdList = []
            this.num = index;
            rightPcxx.num = index;
            printFormat.num = index;
            //取住院号 查看查询
            if (this.zyhs.length <= 0) {//住院号为空则直接返回不请求后台
                return;
            }
            var yzlxx = "";
            if (this.yzlx == 'ls') {
                //临时医嘱执行单
                printFormat.title = "临时";
                yzlxx = "0";
            } else if (this.yzlx == 'cq') {
                //长期医嘱执行单
                printFormat.title = "长期";
                yzlxx = "1";
            } else {
                yzlxx = null;
            }
            var parm = {
                beginrq: this.ksrq,
                endrq: this.jsrq,
                yzlx: yzlxx,
                searchzyh: this.zyhs
            };
            this.pcs = [];
            for (var i = 0; i < rightPcxx.pcList.length; i++) {
                if (rightPcxx.isChecked[i]) {
                    var obj = {};
                    this.pcs.push(rightPcxx.pcList[i].pcbm);
                }
            }
            if (this.pcs.length > 0) {
                parm.searchpcbm = this.pcs;
            }
            $(".InfoMenu div").removeClass('InfoMenuSelected');
            if (isClick == 'all') {//*******************全部
                this.yzzxdList = [];
				printFormat.qtmc='';
				printFormat.clickname = '';
				printFormat.pagemaxsize = 17;
                //请求后台查询全部执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryAll&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
						                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(0).addClass('InfoMenuSelected');

            } else if (isClick == 'kf') {//********************口服
                this.yzzxdList = [];
				printFormat.qtmc='口服';
				printFormat.clickname = '口服'
				printFormat.pagemaxsize = 17;
                //请求后台查询口服执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryKf&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(1).addClass('InfoMenuSelected');

            } else if (isClick == 'zs') {//**********************************注射
                this.yzzxdList = [];
				printFormat.qtmc='其他用药';
				printFormat.clickname = '注射'
				printFormat.pagemaxsize = 6;
                //请求后台查询注射的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryZs&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(2).addClass('InfoMenuSelected');

            } else if (isClick == 'sy') {//********************输液
                this.yzzxdList = [];
				printFormat.qtmc='输液';
				printFormat.clickname = '输液'
				printFormat.pagemaxsize = 6;
                if (bodyMenu.searchContent.sysd == '0') {
                    parm.sysdsx1 = '1';
                }
                if (bodyMenu.searchContent.sysd == '1') {
                    parm.sysdsx2 = '1';
                }
                if (bodyMenu.searchContent.sysd == '2') {
                    parm.sysdsx3 = '1';
                }
				parm.zfbz = bodyMenu.searchContent.zfbz
                //请求后台查询输液的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=querySy&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(3).addClass('InfoMenuSelected');

            } else if (isClick == 'hl') {//**********************护理
                this.yzzxdList = [];
				printFormat.qtmc='其他用药';
				printFormat.clickname = '护理'
				printFormat.pagemaxsize = 6;
                //请求后台查询护理的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryHlzxd&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(4).addClass('InfoMenuSelected');

            } else if (isClick == 'zl') {//******************************治疗
                this.yzzxdList = [];
				printFormat.qtmc='其他治疗';
				printFormat.clickname = '治疗'
				printFormat.pagemaxsize = 6;
                //请求后台查询治疗的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryZlzxd&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(5).addClass('InfoMenuSelected');

            } else if (isClick == 'syt') {//输液贴
                this.yzzxdList = [];
				printFormat.qtmc='其他用药';
				printFormat.clickname = '输液贴'
				printFormat.pagemaxsize = 17;
                //取频次 查看查询
                this.pcs = [];
                for (var i = 0; i < rightPcxx.pcList.length; i++) {
                    if (rightPcxx.isChecked[i]) {
                        var obj = {};
                        this.pcs.push(rightPcxx.pcList[i].pcbm);
                    }
                }
                if (this.pcs.length <= 0) {//频次为空则直接返回不请求后台
                    malert("请先选择频次之后再打印输液贴！", 'top', 'defeadted');
                    return;
                }
                parm.searchpcbm = this.pcs;
                this.sytparm.starttime = parm.beginrq;
                this.sytparm.endtime = parm.endrq;
                this.sytparm.zyh = parm.searchzyh;
                this.sytparm.yljgbm = jgbm;
                parm.sytprint = this.searchContent.sytprint;
                this.sytparm.pcbm = JSON.stringify(this.pcs);
                //请求后台查询输液贴的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=querySyt&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
                        bodyMenu.dealSytData(json.d.list);
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(6).addClass('InfoMenuSelected');
            } else if (isClick == 'wh') {//雾化
			printFormat.clickname = '雾化'
			printFormat.qtmc='其他用药';
			printFormat.pagemaxsize = 6;
                this.sytparm.starttime = parm.beginrq;
                this.sytparm.endtime = parm.endrq;
                this.sytparm.zyh = parm.searchzyh;
                this.sytparm.yljgbm = jgbm;
                this.sytparm.pcbm = JSON.stringify(this.pcs);
                //请求后台查询输液贴的执行单
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types=queryWhxr&ksbm=' + yzclLeft.jsContent.ksbm + '&parm=' + JSON.stringify(parm),
                    function (json) {
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                    if (json.d.list[i].yzxx[int].sysd == null || json.d.list[i].yzxx[int].sysd == undefined) {
                                        json.d.list[i].yzxx[int].sysd = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].sysddw == null || json.d.list[i].yzxx[int].sysddw == undefined) {
                                        json.d.list[i].yzxx[int].sysddw = " ";
                                    }
                                    if (json.d.list[i].yzxx[int].tysl > 0) {
                                        json.d.list[i].yzxx[int].tyxx = "（该记录有退药信息！）";
                                    } else {
                                        json.d.list[i].yzxx[int].tyxx = "";
                                    }
                                    if (json.d.list[i].yzxx[int].pcmc == null || json.d.list[i].yzxx[int].pcmc == undefined) {
                                        json.d.list[i].yzxx[int].pcmc = "";
                                    }
                                }
                            }
                        }
                        // bodyMenu.dealSytData(json.d.list);
                        bodyMenu.yzzxdList = json.d.list;
                    }, function (error) {
                        console.log(error);
                    });
                $(".InfoMenu div").eq(6).addClass('InfoMenuSelected');
            }
        },

        checkSelectSon: function (brIndex, yzIndex) {
            if (bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].numb >= 1) return
            var sfcyType = '';
            if (bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].sfcy == '0' || bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].sfcy == null) {
                sfcyType = 'fzh';
            } else {
                sfcyType = 'yzxh';
            }
            var yzStatus = !bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].isChecked;
            var fzh = bodyMenu.yzzxdList[brIndex].yzxx[yzIndex][sfcyType];//临时分组号
            var ksrq = bodyMenu.yzzxdList[brIndex].yzxx[yzIndex]['ksrq'];//开始日期
            if (!bodyMenu.yzzxdList[brIndex].yzxx[yzIndex]['isChecked']) {
                this.setCheckData(brIndex, yzIndex, fzh, yzStatus, sfcyType, ksrq)
            } else {
                if (bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].sfcy == '0' || bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].sfcy == null) {
                    Vue.set(bodyMenu.yzzxdList[brIndex].yzxx[yzIndex], 'isChecked', false);
                } else {
                    this.setCheckData(brIndex, yzIndex, fzh, yzStatus, sfcyType)
                }
            }
            if (yzStatus) {
                var yzIsOverCk = true;
                for (var x = 0; x < bodyMenu.yzzxdList[brIndex].yzxx.length; x++) {
                    if (!bodyMenu.yzzxdList[brIndex].yzxx[x].isChecked) {
                        yzIsOverCk = false;
                        break;
                    }
                }
                bodyMenu.yzzxdList[brIndex].isCheckAll = yzIsOverCk;
                var isOverCk = true;
                for (var x = 0; x < bodyMenu.yzzxdList.length; x++) {
                    if (!bodyMenu.yzzxdList[x].isCheckAll) {
                        isOverCk = false;
                        break;
                    }
                }
                this.isOver = isOverCk;
            } else {
                bodyMenu.yzzxdList[brIndex].isCheckAll = false;
                this.isOver = false;
            }
            this.$forceUpdate()
        },
        isOverClick: function (isOver) {
            var isAllFlag = false;
            bodyMenu.yzzxdList.forEach(function (br) {
                if (!br.isAllFlag) {
                    br.isCheckAll = isOver;
                } else {
                    isAllFlag = true;
                }
                br.yzxx.forEach(function (yz) {
                    if (yz.numb >= 1) {
                    } else {
                        yz.isChecked = isOver;
                    }
                });
            });
            if (!isAllFlag) {
                this.isOver = isOver;
            } else {
                this.isOver = !isOver;
            }
        },
        setCheckData: function (brIndex, yzIndex, fzh, yzStatus, sfcyType, ksrq) {
            for (var i = 0; i < bodyMenu.yzzxdList[brIndex].yzxx.length; i++) {
                if (bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].sfcy == '1' && yzclLeft.caqxContent.cs00900100232 == '1') {
                    if (fzh != 0 || fzh != '') {
                        if (fzh == bodyMenu.yzzxdList[brIndex].yzxx[i][sfcyType]) {
                            Vue.set(bodyMenu.yzzxdList[brIndex].yzxx[i], 'isChecked', !bodyMenu.yzzxdList[brIndex].yzxx[i].isChecked);
                        }
                    } else if (fzh == 0 || fzh == '') {
                        bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].isChecked = yzStatus;
                        break;
                    }
                } else {
                    if (fzh != 0 || fzh != '') {
                        if (fzh == bodyMenu.yzzxdList[brIndex].yzxx[i][sfcyType] && ksrq == bodyMenu.yzzxdList[brIndex].yzxx[i].ksrq) {
                            Vue.set(bodyMenu.yzzxdList[brIndex].yzxx[i], 'isChecked', !bodyMenu.yzzxdList[brIndex].yzxx[i].isChecked);
                        }
                    } else if (fzh == 0 || fzh == '') {
                        bodyMenu.yzzxdList[brIndex].yzxx[yzIndex].isChecked = yzStatus;
                        break;
                    }
                }
            }
        },
        reCheckBoxSon: function () {
            if (arguments.length == 1) {
                var isCheckAll = bodyMenu.yzzxdList[arguments[0]].isCheckAll ? false : true,
                    yzshInfo = bodyMenu.yzzxdList[arguments[0]],
                    yzxxList = yzshInfo.yzxx;

                bodyMenu.yzzxdList[arguments[0]].isCheckAll = isCheckAll;
                for (var i = 0; i < yzxxList.length; i++) {
                    if (yzxxList[i].numb >= 1) {
                        continue;
                    }
                    bodyMenu.yzzxdList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                }
            } else if (arguments.length == 2) {
                this.activeBrListIndex = arguments[0];
                this.activeIndex = arguments[1];
            }

            var isOverCk = true;
            for (var x = 0; x < bodyMenu.yzzxdList.length; x++) {
                if (!bodyMenu.yzzxdList[x].isCheckAll) {
                    isOverCk = false;
                    break;
                }
            }
            this.isOver = isOverCk;
            this.$forceUpdate();
        },
        //对输液贴查询出来的数据做处理
        dealSytData: function (oldList) {
            var newlist = [];
            for (var i = 0; i < oldList.length; i++) {
                //执行记录数据从新根据分组号分组
                var map = {};
                var fzhobjList = [];
                for (var j = 0; j < oldList[i].yzxx.length; j++) {
                    var obj = oldList[i].yzxx[j];
                    if (obj.fzh !='0' && !map[obj.fzxh]) {
                        fzhobjList.push({
                            fzxh: obj.fzxh,
                            yzmx: [obj],
                        });
                        map[obj.fzxh] = obj;
                    }else if(obj.fzh =='0'){
						fzhobjList.push({
						    fzxh: obj.fzxh+"_"+j,
						    yzmx: [obj],
						});
					} else {
                        for (var k = 0; k < fzhobjList.length; k++) {
                            var fzcz = fzhobjList[k];
                            if (obj.fzh !='0' && fzcz.fzxh == obj.fzxh) {
                                fzcz.yzmx.push(obj);
                                break;
                            }
                        }
                    }
                }
				                oldList[i].yzxx = fzhobjList;
                if (oldList[i].yzxx.length > 1) {
                    for (var num = 0; num < oldList[i].yzxx.length; num++) {
                        var newobj = JSON.parse(JSON.stringify(oldList[i]));
                        newobj.yzxx = oldList[i].yzxx[num].yzmx;
                        newlist.push(newobj);
                    }
                } else {
                    var yzxx = oldList[i].yzxx[0].yzmx;
                    oldList[i].yzxx = yzxx;
                    newlist.push(oldList[i]);
                }
                //----------
            }
            bodyMenu.isChecked1 = [];
            for (var dex = 0; dex < newlist.length; dex++) {
                Vue.set(bodyMenu.isChecked1, dex, true)
                if (newlist[dex].yzxx[0].yzlx == "1") {
                    newlist[dex].yzlxmc = "长期";
                } else {
                    newlist[dex].yzlxmc = "临时";
                }

                newlist[dex].yyffmc = newlist[dex].yzxx[0].yyffmc;
                newlist[dex].pcmc = newlist[dex].yzxx[0].pcmc;
                newlist[dex].sysd = newlist[dex].yzxx[0].sysd + newlist[dex].yzxx[0].sysddw;
                if (newlist[dex].nldw == "1") {
                    newlist[dex].brnl = newlist[dex].nl + "岁";
                } else {
                    newlist[dex].brnl = newlist[dex].nl + "月";
                }
            }
            bodyMenu.yzzxdList = newlist;
        },
    }
});

/********************************华丽分割线***************************************/

/********************************华丽分割线***************************************/
var rightPcxx = new Vue({
    el: '#rightPcxx',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: null,
        pcList: [], //频次集合,
        isCheckAll: true,
    },
    methods: {
        //请求后台查询所有频次
        getPcData: function () {
            this.param.rows = 200000;
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yzpc'
                + '&dg=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        if (data.d.list.length > 0) {
                            rightPcxx.pcList = data.d.list;
                            for (var i = 0; i < rightPcxx.pcList.length; i++) {
                                Vue.set(rightPcxx.isChecked, i, true)
                            }
                        }
                    }
                });
        },
        reCheckBox2: function (val) {
            var that = this
            if (val[1] !== 'all') this.activeIndex = val[0];
            if (val[0] == 'some') {
                Vue.set(this.isChecked, val[1], val[2]);
                if (that.notempty(this.isChecked).length == this[val[3]].length) {
                    this.isChecked.every(function (el) {
                        if (el === true) {
                            return that.isCheckAll = true
                        } else {
                            return that.isCheckAll = false
                        }
                    })
                }
                console.log(this.isChecked)
            } else {
                this.isCheckAll = val[2];
                console.log(this.isCheckAll);
                if (val[1] == null) val[1] = "jsonList";
                if (this.isCheckAll) {
                    for (var i = 0; i < this[val[1]].length; i++) {
                        Vue.set(this.isChecked, i, true);
                        // this.isChecked[i] = true;
                    }
                } else {
                    this.isChecked = [];
                }
            }
            bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
        },
    },
    updated: function () {
        changeWin()
    }
});

var pslr = new Vue({
    el: '.pslr',
    mixins: [baseFunc, tableBase, dic_transform, mformat],
    data: {
        isShow: false,
        psYzList: [],
        hsListData: [],
        jsonListHsData: [],
        index: 0
    },
    mounted: function () {
        this.getData({hsbz: '1'}, 'hsListData', 'jsonListHsData')
        laydate.render({
            elem: '#psshsj',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
            }
        });
    },
    methods: {
        getData: function (obj, objContent, list) {
            var that = this;
            this.updatedAjax("/actionDispatcher.do?reqUrl=GetDropDown&types=rybmjkac&json=" + JSON.stringify(obj), function (json) {
                if (json.a == "0") {
                    that[list] = json.d.list;
                }
            });
            this.fliter(this[list], objContent)
        },
        fliter: function (list, objContent) {
            this[objContent] = jsonFilter(list, "ksbm", yzclLeft.jsContent.ksbm);
        },
        openTime: function (elm, index, code) {
            setTimeout(function () {
                laydate.render({
                    elem: '#' + elm,
                    trigger: 'click'
                    , show: true,
                    type: 'datetime',
                    theme: '#1ab394',
                    done: function (value, data) { //回调方法
                        pslr.psYzList[index][code] = new Date(value).getTime();
                        pslr.resultChange_save([new Date(value).getTime(), undefined, [index, code]])
                    }
                });
            }, 50)
        },
        getPsData: function () {
            var yzlxx = "";
            var endrq = $("#dEnd").val();
            var beginrq = $("#dbegin").val();
            if (bodyMenu.yzlx == 'ls') {
                yzlxx = "0";
            } else if (bodyMenu.yzlx == 'cq') {
                yzlxx = "1";
            } else {
                yzlxx = null;
            }
            var zyhs = sessionStorage.getItem("HszbrItem");
            if (zyhs.length <= 0) {
                malert("请选择要操作的病人！", "top", "defeadted");
                return;
            }
            // var searchzyh = [];
            // for (var i = 0; i < zyhs.length; i++) {
            //     searchzyh.push(zyhs[i].zyh);
            // }
            var parm = {
                beginrq: beginrq,
                endrq: endrq,
                yzlx: yzlxx,
                searchzyh: yzclLeft.arr,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywYzzxdCx&types=queryPs&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    var arr = [];
                    for (var i = 0; i < json.d.list.length; i++) {
                        if (json.d.list[i].yyffmc == '皮内注射' || json.d.list[i].yyffmc.indexOf('皮试') != -1) {
                            arr.push(json.d.list[i]);
                        }
                    }
                    json.d.list = arr;
                    pslr.psYzList = pslr.filterSort(json.d.list, 's', 'xssx');
                } else {
                    malert(json.c, 'top', 'defeadted')
                }
            }, function (error) {
                console.log(error);
            });
        },
        resultChange_save: function (val) {
            console.log(pslr.hsListData)
            Vue.set(this.psYzList[val[2][0]], [val[2][1]], val[0]);
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
            var parm_str = {
                psjg: this.psYzList[val[2][0]]['psjg'],
                pslrsj: this.psYzList[val[2][0]]['pslrsj'],
                psshsj: this.psYzList[val[2][0]]['psshsj'],
                pslrrybm: this.psYzList[val[2][0]]['pslrrybm'],
                psshrybm: this.psYzList[val[2][0]]['psshrybm'],
                ypyzxh: pslr.psYzList[val[2][0]].yzxh,
                mxxh: pslr.psYzList[val[2][0]].yzmxxh,
                ryypmc: pslr.psYzList[val[2][0]].xmmc,
            };
            if (parm_str.pslrrybm) {
                parm_str.pslrry = pslr.listGetName(pslr.hsListData, parm_str.pslrrybm, 'rybm', 'ryxm');
            }
            if (parm_str.psshrybm) {
                parm_str.psshry = pslr.listGetName(pslr.hsListData, parm_str.psshrybm, 'rybm', 'ryxm');
            }
            $.getJSON('/actionDispatcher.do?reqUrl=ZyysYsywYzcl&types=savePs&parm=' + encodeURI(JSON.stringify(parm_str)),
                function (json) {
                    if (json.a == '0') {
                        pslr.getPsData();
                        bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
                        malert("皮试结果保存成功", 'top', 'success');
                    } else {
                        malert("皮试结果保存失败", 'top', 'defeadted');
                    }
                });
        },
        isfzcfMxChecked: function () {

        },
        fzCheckOne: function (index) {
            pslr.index = index;
        }
    }
});
//执行打印
var printFormat = new Vue({
    el: '.printFormat',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        printHide: false,
        jsonList: [],
        list: [],
        xdhs: '',
        titles: {
            1: '口服',
            2: '注射',
            3: '输液',
            4: '护理',
            5: '治疗',
            6: '雾化',
            7: '输液贴',
        },
        yljgmc: sessionStorage.getItem('yljgOrUser' + userId) ? JSON.parse(sessionStorage.getItem('yljgOrUser' + userId)).yljgmc : '',
        num: null,
        BrxxJson: [],
        rowsVal: null,
        parentIndex: 0,
        pageList: [],
        blankList: [],
        title: '', //标题（长期和临时）
        isGoPrint: false,
        pageH: 790,
        goOnPrintIndex: 0,//总页数的当前行
        fromBegining: false,//是否从开始打印
        csqxContent: yzclLeft.caqxContent,
		clickname:'',
		pagemaxsize:17,
		qtmc:'',
		xdhs:'',
		lsYzlist :[],
    },
    methods: {
        setChecked: function (childIndex, parentIndex) {
            var index = printFormat.getGoOnPrintIndex(childIndex, parentIndex);
            if ($(".cqyzd tr[index=" + index + "]").hasClass("tableTrSelect")) {
                $(".cqyzd tr[index=" + index + "]").removeClass("tableTrSelect");
            } else {
                $(".cqyzd tr").removeClass("tableTrSelect");
                $(".cqyzd tr[index=" + index + "]").addClass("tableTrSelect");
            }
            printFormat.goOnPrintIndex = index;
        },
		setTbChecked: function (childIndex, parentIndex) {
		    var index = printFormat.getGoOnPrintIndex(childIndex, parentIndex);
		    if ($(".cqyzd tbody[index=" + index + "]").hasClass("tableTrSelect")) {
		        $(".cqyzd tbody[index=" + index + "]").removeClass("tableTrSelect");
		    } else {
		        $(".cqyzd tbody").removeClass("tableTrSelect");
		        $(".cqyzd tbody[index=" + index + "]").addClass("tableTrSelect");
		    }
		    printFormat.goOnPrintIndex = index;
		},
        setList: function () {
			            //处理分页
            var zxdList = [];
			
			var maxsize = printFormat.pagemaxsize
            for (var i = 0; i < this.jsonList.length; i++) {
                var pageNum = Math.ceil(this.jsonList[i]['yzxx'].length / maxsize);
                var totalNum = pageNum * maxsize;
                for (var j = 0; j < pageNum; j++) {
                    let yzxxList = [];
                    for (var k = j * maxsize; k < totalNum; k++) {
                        if (k < ((j + 1) * maxsize)) {
                            if (k < this.jsonList[i]['yzxx'].length) {
                                yzxxList.push(this.jsonList[i]['yzxx'][k]);
                            } else {
                                yzxxList.push({'emptyItem': '1'});
                            }
                        }else{
							break;
						}
                    }
					let yzdx =  [];
					yzdx.brxm = this.jsonList[i].brxm
					yzdx.brxb = this.jsonList[i].brxb
					yzdx.nl = this.jsonList[i].nl
					yzdx.nldw = this.jsonList[i].nldw
					yzdx.nl2 = this.jsonList[i].nl2
					yzdx.nldw2 = this.jsonList[i].nldw2
					
					yzdx.rycwbh =this.jsonList[i].rycwbh
					yzdx.zyh = this.jsonList[i].zyh
					yzdx.ryksmc = this.jsonList[i].ryksmc
					yzdx.yzxx = yzxxList
					zxdList.push(yzdx);
                }
				
				
				
				
            }
            this.jsonList = zxdList;
        },
        setData: function () {
            this.jsonList = [];
			printFormat.lsYzlist = [];
			if(printFormat.clickname =='输液' || printFormat.clickname =='注射' || printFormat.clickname =='治疗' || printFormat.clickname =='雾化' || printFormat.clickname =='护理' ){
				for (var i = 0; i < bodyMenu.yzzxdList.length; i++) {
				    var brxx = JSON.parse(JSON.stringify(bodyMenu.yzzxdList[i]));
				    var yzxxList = brxx.yzxx;
					printFormat.lsYzlist.push(JSON.parse(JSON.stringify(yzxxList)));
				    var yzlist = [];
				    var index = 0;
				    for (var j = 0; j < yzxxList.length; j++) {
				        if (yzxxList[j].isChecked) {
				            yzlist[index] = yzxxList[j];
				            index++;
				        }
				    }
					var zhlist = [];
					var tzlist = [];
					for (let k = 0; k < yzlist.length; k++) {
						let tpYz = JSON.parse(JSON.stringify(yzlist[k]));
						if (tpYz.fzh != 0) {
													if (k == 0) { // 第一个
														if (yzlist[k + 1] != undefined) {
															if(tpYz.ypbz=='1' && yzlist[k + 1].ypbz =='1'){
																if (tpYz.fzh == yzlist[k + 1].fzh && tpYz.yzxh == yzlist[k + 1].yzxh && tpYz.zxsj == yzlist[k + 1].zxsj && tpYz.yyffmc == yzlist[k + 1].yyffmc ) {
																	tpYz['tzbj'] = 'tz-start';
																	tzlist.push(tpYz);
																}else{
																	tzlist.push(tpYz);
																	zhlist.push(tzlist);
																	tzlist = [];
																}
															}else{
																if (tpYz.fzh == yzlist[k + 1].fzh && tpYz.ypyzxh == yzlist[k + 1].ypyzxh  ) {
																	tpYz['tzbj'] = 'tz-start';
																	tzlist.push(tpYz);
																}else{
																	tzlist.push(tpYz);
																	zhlist.push(tzlist);
																	tzlist = [];
																}
															}
															
														}else{
															tzlist.push(tpYz);
															zhlist.push(tzlist);
															tzlist = [];
														}
													} else if (k == yzlist.length - 1) { // 最后一个
														if(tpYz.ypbz=='1' && yzlist[k - 1].ypbz =='1'){
															if (tpYz.fzh == yzlist[k - 1].fzh && tpYz.yzxh == yzlist[k - 1].yzxh && tpYz.zxsj == yzlist[k - 1].zxsj) {
																tpYz['tzbj'] = 'tz-stop';
																tzlist.push(tpYz);
																zhlist.push(tzlist);
																tzlist = [];
															}else{
																tzlist.push(tpYz);
																zhlist.push(tzlist);
																tzlist = [];
															}
														}else{
															if (tpYz.fzh == yzlist[k - 1].fzh && tpYz.ypyzxh == yzlist[k - 1].ypyzxh) {
																tpYz['tzbj'] = 'tz-stop';
																tzlist.push(tpYz);
																zhlist.push(tzlist);
																tzlist = [];
															}else{
																tzlist.push(tpYz);
																zhlist.push(tzlist);
																tzlist = [];
															}
														}
													} else {
															if(tpYz.ypbz=='1' && yzlist[k + 1].ypbz =='1'){
																if ((tpYz.fzh != yzlist[k - 1].fzh || tpYz.yzxh != yzlist[k - 1].yzxh || tpYz.zxsj != yzlist[k - 1].zxsj) && (tpYz.fzh == yzlist[k + 1].fzh && tpYz.yzxh == yzlist[k + 1].yzxh && tpYz.zxsj == yzlist[k + 1].zxsj)) {
																	tpYz['tzbj'] = 'tz-start';
																	tzlist.push(tpYz);
																} else if (tpYz.fzh == yzlist[k - 1].fzh && tpYz.yzxh == yzlist[k - 1].yzxh && tpYz.zxsj == yzlist[k - 1].zxsj && tpYz.fzh == yzlist[k + 1].fzh && tpYz.yzxh == yzlist[k + 1].yzxh && tpYz.zxsj == yzlist[k + 1].zxsj) {
																	tpYz['tzbj'] = 'tz-center';
																	tzlist.push(tpYz);
																} else if ((tpYz.fzh == yzlist[k - 1].fzh && tpYz.yzxh == yzlist[k - 1].yzxh && tpYz.zxsj == yzlist[k - 1].zxsj) && (tpYz.fzh != yzlist[k + 1].fzh || tpYz.yzxh != yzlist[k + 1].yzxh || tpYz.zxsj != yzlist[k + 1].zxsj)) {
																	tpYz['tzbj'] = 'tz-stop';
																	tzlist.push(tpYz);
																	zhlist.push(tzlist);
																	tzlist = [];
																}else{
																	tzlist.push(tpYz);
																	zhlist.push(tzlist);
																	tzlist = [];
																}
															}else{
																if ((tpYz.fzh != yzlist[k - 1].fzh || tpYz.ypyzxh != yzlist[k - 1].ypyzxh) && (tpYz.fzh == yzlist[k + 1].fzh && tpYz.ypyzxh == yzlist[k + 1].ypyzxh)) {
																	tpYz['tzbj'] = 'tz-start';
																	tzlist.push(tpYz);
																} else if (tpYz.fzh == yzlist[k - 1].fzh && tpYz.ypyzxh == yzlist[k - 1].ypyzxh && tpYz.fzh == yzlist[k + 1].fzh && tpYz.ypyzxh == yzlist[k + 1].ypyzxh) {
																	tpYz['tzbj'] = 'tz-center';
																	tzlist.push(tpYz);
																} else if ((tpYz.fzh == yzlist[k - 1].fzh && tpYz.ypyzxh == yzlist[k - 1].ypyzxh) && (tpYz.fzh != yzlist[k + 1].fzh || tpYz.ypyzxh != yzlist[k + 1].ypyzxh)) {
																	tpYz['tzbj'] = 'tz-stop';
																	tzlist.push(tpYz);
																	zhlist.push(tzlist);
																	tzlist = [];
																}else{
																	tzlist.push(tpYz);
																	zhlist.push(tzlist);
																	tzlist = [];
																}
															}
														}
												}else{
													tzlist.push(tpYz);
													zhlist.push(tzlist);
													tzlist = [];
												}
					}
					
				    brxx.yzxx = zhlist;
				    this.jsonList[i] = brxx;
				}
			}else{
				for (var k = 0; k < bodyMenu.yzzxdList.length; k++) {
								    var brxx = bodyMenu.yzzxdList[k]
								    var yzlist = brxx.yzxx;
									printFormat.lsYzlist.push(JSON.parse(JSON.stringify(yzlist)));
									var yzxxList = [];
									var index = 0;
									for (var j = 0; j < yzlist.length; j++) {
									    if (yzlist[j].isChecked) {
									        yzxxList[index] = yzlist[j];
									        index++;
									    }
									}
									
									for (var i = 0; i < yzxxList.length; i++) {
										if (yzxxList[i].fzh != 0) {
											if (i == 0) { // 第一个
												if (yzxxList[i + 1] != undefined) {
													if(yzxxList[i].ypbz=='1' && yzxxList[i + 1].ypbz =='1'){
														if (yzxxList[i].fzh == yzxxList[i + 1].fzh && yzxxList[i].yzxh == yzxxList[i + 1].yzxh) {
															yzxxList[i]['tzbj'] = 'tz-start';
														}
													}else{
														if (yzxxList[i].fzh == yzxxList[i + 1].fzh && yzxxList[i].ypyzxh == yzxxList[i + 1].ypyzxh) {
															yzxxList[i]['tzbj'] = 'tz-start';
														}
													}
													
												}
											} else if (i == yzxxList.length - 1) { // 最后一个
												if(yzxxList[i].ypbz=='1' && yzxxList[i - 1].ypbz =='1'){
													if (yzxxList[i].fzh == yzxxList[i - 1].fzh && yzxxList[i].yzxh == yzxxList[i - 1].yzxh) {
														yzxxList[i]['tzbj'] = 'tz-stop';
													}
												}else{
													if (yzxxList[i].fzh == yzxxList[i - 1].fzh && yzxxList[i].ypyzxh == yzxxList[i - 1].ypyzxh) {
														yzxxList[i]['tzbj'] = 'tz-stop';
													}
												}
												
											} else {
												if(yzxxList[i].ypbz=='1' && yzxxList[i - 1].ypbz =='1'){
													if ((yzxxList[i].fzh != yzxxList[i - 1].fzh || yzxxList[i].yzxh != yzxxList[i - 1].yzxh) && (yzxxList[i].fzh == yzxxList[i + 1].fzh && yzxxList[i].yzxh == yzxxList[i + 1].yzxh)) {
														yzxxList[i]['tzbj'] = 'tz-start';
													} else if (yzxxList[i].fzh == yzxxList[i - 1].fzh && yzxxList[i].yzxh == yzxxList[i - 1].yzxh && yzxxList[i].fzh == yzxxList[i + 1].fzh && yzxxList[i].yzxh == yzxxList[i + 1].yzxh) {
														yzxxList[i]['tzbj'] = 'tz-center';
													} else if ((yzxxList[i].fzh == yzxxList[i - 1].fzh && yzxxList[i].yzxh == yzxxList[i - 1].yzxh) && (yzxxList[i].fzh != yzxxList[i + 1].fzh || yzxxList[i].yzxh != yzxxList[i + 1].yzxh)) {
														yzxxList[i]['tzbj'] = 'tz-stop';
													}
												}else{
													if ((yzxxList[i].fzh != yzxxList[i - 1].fzh || yzxxList[i].ypyzxh != yzxxList[i - 1].ypyzxh) && (yzxxList[i].fzh == yzxxList[i + 1].fzh && yzxxList[i].ypyzxh == yzxxList[i + 1].ypyzxh)) {
														yzxxList[i]['tzbj'] = 'tz-start';
													} else if (yzxxList[i].fzh == yzxxList[i - 1].fzh && yzxxList[i].ypyzxh == yzxxList[i - 1].ypyzxh && yzxxList[i].fzh == yzxxList[i + 1].fzh && yzxxList[i].ypyzxh == yzxxList[i + 1].ypyzxh) {
														yzxxList[i]['tzbj'] = 'tz-center';
													} else if ((yzxxList[i].fzh == yzxxList[i - 1].fzh && yzxxList[i].ypyzxh == yzxxList[i - 1].ypyzxh) && (yzxxList[i].fzh != yzxxList[i + 1].fzh || yzxxList[i].ypyzxh != yzxxList[i + 1].ypyzxh)) {
														yzxxList[i]['tzbj'] = 'tz-stop';
													}
												}
											}
										}
									}
									brxx.yzxx = yzxxList;
									this.jsonList[k] = brxx;
								}
								
			}
            
            this.setList();
        },
		guanbi:function(){
						this.isShow = false
			for (var k = 0; k < bodyMenu.yzzxdList.length; k++) {
				bodyMenu.yzzxdList[k].yzxx = printFormat.lsYzlist[k];
			}
			
		},
        sameDate: function (name, index, type) {
            for (var i = 0; i < bodyMenu.yzzxdList.length; i++) {
                var val = this.jsonList[i]['yzxx'][index][name];
                var prvVal = null, nextVal = null;
                if (index != this.jsonList[i]['yzxx'].length - 1 && index != 0) {
                    prvVal = this.jsonList[i]['yzxx'][index - 1][name];
                    nextVal = this.jsonList[i]['yzxx'][index + 1][name]
                }
            }

            if (val == null || val == '') return '';
            if (val == prvVal && val == nextVal) return '"';
            var reDate = new Date(val);
            if (type == 'ry') {
                return this.Appendzero((reDate.getMonth() + 1)) + '-' + this.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return this.Appendzero(reDate.getHours()) + ':' + this.Appendzero(reDate.getMinutes());
            }
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        sameSE: function (index, num) {
            var fzh = this.jsonList[num]['yzxx'][index]['fzh'];
            if (!fzh || fzh == 0) return false;
            fzh = this.jsonList[num]['yzxx'][index]['fzxh'];
            // if (index == 0 && fzh == this.jsonList[num]['yzxx'][index + 1] || this.jsonList[num]['yzxx'][index]['fzxh']) {
            if (index == 0 && this.jsonList[num]['yzxx'][index + 1] && fzh == this.jsonList[num]['yzxx'][index + 1]['fzxh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList[num]['yzxx'].length - 1) {
                var nextFzh = this.jsonList[num]['yzxx'][index + 1]['fzxh'];
                var prvFzh = this.jsonList[num]['yzxx'][index - 1]['fzxh'];
                var nextKsrq = this.jsonList[num]['yzxx'][index + 1]['zxsj'];
                var prvKsrq = this.jsonList[num]['yzxx'][index - 1]['zxsj'];
                var ksrq = this.jsonList[num]['yzxx'][index]['zxsj'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq == nextKsrq)) {
                    return 'all';
                }
                if(fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq != nextKsrq)){
                    return 'end';
                }
                if(fzh == nextFzh && fzh == prvFzh && (ksrq != prvKsrq && ksrq == nextKsrq)){
                    return 'start';
                }
            }
            return 'null'
        },
        toTime: function (dateVal,type) {
            var reDate = new Date(dateVal);
           
            if(type == 1){
				var month = reDate.getMonth() + 1;
				var day = reDate.getDate()
				if(month<10){
					month = "0"+month;
				}
				if(day<10){
					day = "0"+day;
				}
				return month + '-' + day;
			}else{
				var minutes = reDate.getMinutes();
				var hours = reDate.getHours()
				if(minutes<10){
					minutes = "0"+minutes;
				}
				if(hours<10){
					hours = "0"+hours;
				}
				return hours + ':' + minutes;
			}
            
        },
        tzqm: function (index, num) {
            if (this.sameSE(index, num) != 'start' && this.sameSE(index, num) != 'all') {
                return true;
            } else {
                return false;
            }
        },
        print: function () {
			console.log(yzclLeft.caqxContent)
            //移除所有续打加的样式
            var yzdTitle = $(".cqyzd div[primaryKey='yzdTitle']");//标题
            var yzd_brInfo = $(".cqyzd div[primaryKey='yzd-brInfo']");//病人信息栏
            var hidTitle = $(".cqyzd tr[primaryKey='goOnPrintHideTitle']");//表头
            var hidData = $(".cqyzd tr[primaryKey='goOnPrintHideData']");//数据
            var ysDiv = $(".cqyzd div[primaryKey='ysDiv']");//签名
            var pageDiv = $(".cqyzd div[primaryKey='pageDiv']");//页码
            var emptyDiv = $(".cqyzd div[primaryKey='emptyDiv']");//占位DIV

            yzdTitle.attr("class", "yzdTitle");
            yzd_brInfo.attr("class", "yzd-brInfo");
            hidTitle.attr("class", "goOnPrintHideTitle");
            hidData.attr("class", "goOnPrintHideData");
            ysDiv.attr("class", "ysDiv");
            pageDiv.attr("class", "pageDiv");
            emptyDiv.attr("class", "emptyDiv");
            if(window.printBox){
                if(bodyMenu.num == 7){
                    window.printBox.printHide=true;
                }
                window.print();
                if(bodyMenu.num == 7){
                    window.printBox.printHide=false;
                }
            }else {
                window.print();
            }

        },
        doPrint: function (isGoOn) {
            $('.no-print').html('')
            console.log(1111)
            var cqTr;
            this.isGoOn = isGoOn
            this.list = [];
            cqTr = $(".yzd-table tr");
            var _height = 0;
            var a = 0, b = -1;
            for (var i = 1; i < cqTr.length - 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height >= this.pageH) {
                    b++;
                    var as = [];
                    for (var f = a; f < i; f++) {
                        as.push(this.jsonList.yzxx[f]);
                    }
                    this.list[b] = as;
                    a = i;
                    _height = 0;
                    this.pageList.push(as.length);
                }
            }
            var pp = [];
            for (var p = a; p < this.jsonList.yzxx.length; p++) {
                pp.push(this.jsonList.yzxx[p]);
            }
            for (var l = 0; l < 21; l++) {
                _height += 43;
                if (_height >= this.pageH) {
                    break;
                }
                pp.push({'psjg': '无'});
            }
            this.list[b + 1] = pp;
            this.isShow = true;
            // this.showTable(0);
            if (isGoOn) {
                this.isGoPrint = true;
                setTimeout(function () {
                    printFormat.hideTable(0);
                    $('.yzd-table td').css('border', '1px solid transparent');
                    $('.yzd-table-blank span:first-child').css('border-bottom', '1px solid transparent');
                    $('.blank-list').css('display', 'none');
                    $('.ysDiv').css('display', 'none');
                }, 50);
                setTimeout(function () {
                    window.print();
                    $('.yzd-table td').css('border', '1px solid #999');
                    $('.yzd-table-blank span:first-child').css('border-bottom', '1px solid #999');
                    $('.blank-list').removeAttr('style');
                    $('.ysDiv').removeAttr('style');
                }, 100);
            } else {
                printFormat.isGoPrint = false;
                setTimeout(function () {
                    window.print();
                    printGd = 20
                    printFormat.isShow = false;
                    printFormat.isShow = false;
                }, 100);
            }
        },
        hideTable: function (type) {
            var num = 0;
            if (type == 0 && this.isChecked == this.isChecked) {
                for (var i = 0; i < this.pagePrint; i++) {
                    $('.printFormat .popCenter').eq(i).hide();
                    num += this.pageList[i];
                }
                this.isChecked = this.isChecked - num;
                this.$forceUpdate()
            } else if (type == 0) {
                this.isChecked = this.isChecked
                this.hideTable(0)
            }
        },
        showTable: function () {
            for (var i = 0; i < $('.printFormat .popCenter').length; i++) {
                $('.printFormat .popCenter').eq(i).show();
            }
            this.$forceUpdate()
        },
        goPrint: function (index) {
            this.isChecked = index;
            this.isChecked = index;
            var cqTr = $(".yzd-table tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > 720) {
                    b++;
                    _height = 0;
                }
            }
            this.pagePrint = b;
        },

        getGoOnPrintIndex: function (index, page) {
            printFormat.parentIndex = page;
            if (index == 0) {//如果选中的是任一一页第一个，那么从当页开始续打
                printFormat.fromBegining = true;
            } else {
                printFormat.fromBegining = false;
            }
            if (page != 0) {
                index = (page * this.pagemaxsize) + index + page;
            }
            return index;
        },
        goOnPrint: function () {
            //点击打印，隐藏数据
            var yzdTitle = $(".cqyzd div[primaryKey='yzdTitle']");//标题
            var yzd_brInfo = $(".cqyzd div[primaryKey='yzd-brInfo']");//病人信息栏
            var hidTitle = $(".cqyzd tr[primaryKey='goOnPrintHideTitle']");//表头
			
            var hidData = $(".cqyzd tr[primaryKey='goOnPrintHideData']");//数据
			if(hidData && hidData.length==0){
				hidData = $(".cqyzd tbody[primaryKey='goOnPrintHideData']");//数据
			}
            var ysDiv = $(".cqyzd div[primaryKey='ysDiv']");//签名
            var pageDiv = $(".cqyzd div[primaryKey='pageDiv']");//页码
            var emptyDiv = $(".cqyzd div[primaryKey='emptyDiv']");//占位DIV

            yzdTitle.attr("class", "yzdTitle");
            yzd_brInfo.attr("class", "yzd-brInfo");
            hidTitle.attr("class", "goOnPrintHideTitle");
            hidData.attr("class", "goOnPrintHideData");
            ysDiv.attr("class", "ysDiv");
            pageDiv.attr("class", "pageDiv");
            emptyDiv.attr("class", "emptyDiv");

printFormat.fromBegining = false;
            var loopNum = printFormat.parentIndex;
            if (!printFormat.fromBegining) {//从当前页的第一行开始,不要隐藏表头
                loopNum += 1;
            }
			            //隐藏标题，隐藏表头
            for (var i = 0; i < loopNum; i++) {
                if(this.clickname == '输液' || this.clickname =='注射' || this.clickname =='治疗' || this.clickname =='雾化' || this.clickname =='护理'){
                    let tbody = hidTitle[0].parentNode.parentNode.querySelectorAll('tbody')
                    let thead = hidTitle[0].parentNode
                    let th = thead.querySelectorAll('th')
                    
                    th.forEach(element => {
                        element.style.border = 'none'
                    });
                    tbody.forEach(element => {
                        let th = element.querySelectorAll('th')
                        let td = element.querySelectorAll('td')
                        th.forEach(element => {
                            element.style.border = 'none'
                        });
                        td.forEach(element=>{
                            element.style.border = 'none'
                        })
                    });
                }else {
                    let table = hidTitle[0].parentNode
                    let th = table.querySelectorAll('th')
                    let td = table.querySelectorAll('td')
                    th.forEach(element => {
                        element.style.border = 'none'
                    });
                    td.forEach(element=>{
                        element.style.border = 'none'
                    })
                }
                

                yzdTitle.eq(i).addClass("goPrintHide");
                yzd_brInfo.eq(i).addClass("goPrintHide");
                hidTitle.eq(2 * i).addClass("goPrintHide");
                hidTitle.eq(2 * i + 1).addClass("goPrintHide");
                if (i < loopNum) {
                    ysDiv.eq(i).addClass("goPrintHide");
                    pageDiv.eq(i).addClass("goPrintHide");
                    emptyDiv.eq(i).addClass("goPrintHide");
                }
            }
			//如果没指定选中行数打印就直接走原有逻辑
			let dyhs = this.xdhs;
			if(dyhs && dyhs>0 && printFormat.goOnPrintIndex>=0){
				
				//隐藏数据
				for (var i = 0; i < dyhs-1; i++) {
				    hidData.eq(i).removeClass("goPrintHide");
				}
				if(bodyMenu.num == 7){
				    window.printBox.printHide=true;
				}
				
				//将原有医嘱信息 存放到临时变量中
				let tpyzxx = JSON.parse(JSON.stringify(printFormat.jsonList[0].yzxx));
				
				let ddysj = JSON.parse(JSON.stringify(printFormat.jsonList[0].yzxx[printFormat.goOnPrintIndex]));
				
				for (let i = 0; i < printFormat.jsonList[0].yzxx.length; i++) {
					if(i == (dyhs-1)){
						this.$set(printFormat.jsonList[0].yzxx, i, ddysj)
					}else{
						this.$set(printFormat.jsonList[0].yzxx, i, {'emptyItem': '1'})
					}
				}
				this.$forceUpdate();
				var that = this;
				setTimeout(function () {
				    window.print();
					printFormat.jsonList[0].yzxx = tpyzxx;
					that.$forceUpdate();
					that.tablestyle(loopNum,hidTitle);
				}, 500);
				
			}else{
				//隐藏数据
				for (var i = 0; i < printFormat.goOnPrintIndex; i++) {
				    hidData.eq(i).addClass("goPrintHide");
				}
				if(bodyMenu.num == 7){
				    window.printBox.printHide=true;
				}
				window.print();
				this.tablestyle(loopNum,hidTitle);
			}
            if(bodyMenu.num == 7){
                window.printBox.printHide=false;
            }

            
            //隐藏标题，隐藏表头
            


            // if(this.xdhs!=""){
            //     for (var i = 0; i <this.xdhs; i++) {
            //         this.jsonList[this.parentIndex]['yzxx'].splice(this.isChecked-1,0,{'fzh': null, 'ksrq': null}, {'fzh': null, 'ksrq': null,xmmc:'xxxxx',xmbm:'xxxxx'})
            //     }
            //     this.isChecked=(parseInt(this.xdhs) * 2 )+this.isChecked
            // }
            //
            // setTimeout(function () {
            //
            // },100);
            //
            // setTimeout(function () {
            //     window.print();
            //     if(printFormat.xdhs !=""){
            //         printFormat.jsonList[printFormat.parentIndex]['yzxx'].splice(printFormat.isChecked - parseInt(printFormat.xdhs) * 2,parseInt(printFormat.xdhs) * 2 )
            //     }
            // }, 110);
        },
		tablestyle :function(loopNum,hidTitle){
			for (var i = 0; i < loopNum; i++) {
			    if(this.clickname == '输液' || this.clickname =='注射' || this.clickname =='治疗' || this.clickname =='雾化' || this.clickname =='护理'){
			        let tbody = hidTitle[0].parentNode.parentNode.querySelectorAll('tbody')
			        let thead = hidTitle[0].parentNode
			        let th = thead.querySelectorAll('th')
			        
			        th.forEach(element => {
			            element.style.border = '1px solid #999'
			        });
			        tbody.forEach(element => {
			            let th = element.querySelectorAll('th')
			            let td = element.querySelectorAll('td')
			            th.forEach(element => {
			                element.style.border = '1px solid #999'
			            });
			            td.forEach(element=>{
			                element.style.border = '1px solid #999'
			            })
			        });
			    }else{
			        let th = hidTitle[0].parentNode.querySelectorAll('th')
			        let td = hidTitle[0].parentNode.querySelectorAll('td')
			        th.forEach(element => {
			            element.style.border = '1px solid #999'
			        });
			        td.forEach(element=>{
			            element.style.border = '1px solid #999'
			        })
			    }
			}
		},
        isShowItem: function (num, index) {
            
            if (this.jsonList[num]['yzxx'][index]['fzh'] == this.jsonList[num]['yzxx'][index + 1]['fzh']) {
                if (this.jsonList[num]['yzxx'][index]['yyffmc'] == this.jsonList[num]['yzxx'][index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});


function loadOpenPage(name,cb) {
    $('#loadingPrint').load(name + '.html',function () {
        cb && cb()
    });
}
yzclLeft.$watch('arr', function (newVal, oldVal) {
    if (newVal != oldVal && this.index == 8) {
        console.log(111)
        bodyMenu.zyhs = []
        bodyMenu.zyhs = newVal
        bodyMenu.clickMenu(bodyMenu.sxlx, bodyMenu.num)
    }
})

/*********************************华丽分割线**************************************/
rightPcxx.getPcData();//初始化页面加载所有频次信息
bodyMenu.initData();

