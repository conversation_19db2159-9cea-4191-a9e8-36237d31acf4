<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>病区摆药</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css" />
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <link type="text/css" href="slgl.css" rel="stylesheet" />
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
    <div class="background-box flex-container flex-dir-c">
        <div class="wrapper flex-container flex-dir-c" v-cloak id="wrapper">
            <div class="panel margin-b-10  printHide">
                <div class="tong-top">
                    <button class="tong-btn flex-container flex-align-c btn-parmary iconfont padd-r-10 font-14 icon-icon60 paddr-r5"
                        v-show="sldmxShow" @click="bqby">病区摆药</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary icon-dysq  padd-r-10 paddr-r5"
                        v-show="ksfydShow" @click="print">打印</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary icon-iocn45 iconfont font-14 padd-r-10 paddr-r5"
                        v-show="tysqmxShow" @click="shData">审核</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary icon-iocn45 iconfont font-14 padd-r-10 paddr-r5"
                        v-show="tysqmxShow" @click="notCheck()">作废</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary icon-iocn45 iconfont font-14 padd-r-10 paddr-r5"
                        v-show="tysqmxShow && is_csqx.N04003002200401 == '1'" @click="bqtyjl()">病区退药记录</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary iconfont icon-iocn9 font-14  padd-r-10 paddr-r5"
                        v-show="kshzlydShow" @click="lingYao">领药</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary-b iconfont icon-iocn56 font-14 padd-r-10  paddr-r5"
                        @click="refresh">刷新</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary-b iconfont icon-iocn31 font-14 padd-r-10 paddr-r5"
                        v-show="kshzlydShow" @click="QxlingYao" :disabled="cancleLingYao">取消领药</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary-b iconfont icon-iocn38 font-14  padd-r-10 paddr-r5"
                        v-show="kshzlydShow" @click="printFyd">发药单打印</button>
                    <button class="tong-btn flex-container flex-align-c btn-parmary-b  font-14  paddr-r5" v-show="kshzlydShow"
                        @click="queryWly"><i class="iconfont icon-Artboard-12 flex-start paddr-r5"><em class="path1"></em><em
                                class="path2"></em></i>未领药查询</button>
                </div>
                <div class="tong-search">
                    <div class="top-form padd-l-10">
                        <label class="top-label font-14">药房</label>
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChangeyf" :child="yfList" :index="'yfmc'"
                                :index_val="'yfbm'" :val="barContent.yfbm" :search="true" :name="'barContent.yfbm'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form" v-show="notSldmxShow">
                        <label class="top-label font-14">科室</label>
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChangeKs" :child="ksList" :index="'ksmc'"
                                :index_val="'ksbm'" :val="barContent.ksbm" :search="true" :name="'barContent.ksbm'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form" v-show="ksfydShow">
                        <label class="top-label font-14">发药单类型</label>
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="changeType" :child="fydlx_tran" :index="type"
                                :val="type" :name="'type'">
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form" v-show="kshzlydShow">
                        <label class="top-label font-14">领药单类型</label>
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChangeLyd" :child="fydlx_tran" :index="type"
                                :val="type" :name="'type'" >
                            </select-input>
                        </div>
                    </div>
                    <div class="top-form" v-show="fylydShow">
                        <label class="top-label font-14">用法选择</label>
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChangeyyff" :child="yfxzfy_tran" :index="fylx"
                                :val="fylx" :name="'fylx'">
                            </select-input>
                        </div>
                    </div>


                    <div class="top-form" v-show="bqbyshow">
                        <label class="top-label font-14">病区</label>
                        <div class="top-zinle">
                            <select-input class="wh122" @change-data="resultChangeBq" :child="bqList" :index="'bqmc'"
                                :index_val="'bqbm'" :val="barContent.bqbm" :search="true" :name="'barContent.bqbm'">
                            </select-input>
                        </div>
                    </div>
					
                    <div class="top-form">
                        <label class="top-label font-14">申领时间</label>
                        <div class="top-zinle">
                            <div class="top-zinle flex-container flex-align-c">
                                <input class="zui-input  wh160 " placeholder="请选择申请开始日期" id="timeVal" /><span
                                    class="padd-l-5 padd-r-5">~</span>
                                <input class="zui-input todate wh160 " placeholder="请选择申请结束时间" id="timeVal1" />
                            </div>
                        </div>
                    </div>
                    <div class="top-form" v-if="num==0 || num==0">
                        <label class="top-label font-14">检索</label>
                        <div class="top-zinle">
                            <input v-if="num==0" class="zui-input wh180" @keydown.13="searching" placeholder="申领单号/住院号/姓名" v-model="parameter"/>
                            <input v-if="num==1" class="zui-input wh180" @keydown.13="searching" placeholder="摆药单号/住院号/姓名" v-model="parameter"/>
                        </div>
                    </div>

                    <div class="top-form " v-if="num==2">
                        <label class="font-14 padd-r-5">是否单剂量</label>
                        <select-input class="wh80" @change-data="resultChangeSfdjl" :not_empty="false"
                                      :child="djl_tran" :index="sfdjl" :val="sfdjl"
                                      :name="'sfdjl'">
                        </select-input>
                    </div>
                    <div class="top-form " v-if="num==2">
                        <label class="font-14 padd-r-5">打印状态</label>
                        <select-input class="wh70" @change-data="resultChangeDy" :not_empty="false"
                                      :child="IsPrint_tran" :index="PrintType" :val="PrintType"
                                      :name="'PrintType'">
                        </select-input>
                    </div>
					<div class="top-form " v-if="num==2">
					    <button class="zui-btn table_db_esc btn-default xmzb-db" @click="hzdcx">汇总</button>
					</div>
					<div class="top-form " v-if="num==2">
					    <button class="zui-btn table_db_esc btn-default xmzb-db" @click="kfdcx">口服</button>
					</div>
                </div>
            </div>
            <div class="padd-l-10 padd-r-10  printHide flex-container flex-align-c">
                <tabs :num="num" :tab-child="[{text:'申领单明细'},{text:'科室发药单'},{text:'科室汇总领药单'},{text:'退药申请明细'},]"   @tab-active="tabBg" ></tabs>
                <div v-if="num=='0'" class="flex-container flex-align-c">
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'长期'" :val="'cqyzlx'"  :new-value="cqyzlx"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'临时'" :val="'lsyzlx'"  :new-value="lsyzlx"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'普通药品'" :val="'ptypbz'"  :new-value="ptypbz"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'精神药品'" :val="'jslyp'"  :new-value="jslyp"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'毒麻药品'" :val="'dmypbz'"  :new-value="dmypbz"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'高危药品'" :val="'gwyp'"  :new-value="gwyp"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="' 高值耗材'" :val="'gzhcbz'"  :new-value="gzhcbz"></vue-checkbox>
					<input type="radio" id="radio-0" name="sfnm" value="0" @click="sfnm" v-model="yyff" class="h-radio">
					<label for="radio-0" class="padd-r-10 ">全部药品</label>
					<input type="radio" id="radio-1" name="sfnm" value="1" @click="sfnm" v-model="yyff" class="h-radio">
					<label for="radio-1" class="padd-r-10 ">口服药品</label>
					<input type="radio" id="radio-2" name="sfnm" value="2" @click="sfnm" v-model="yyff"  class="h-radio">
					<label for="radio-2">非口服药品</label>
                </div>
            </div>
            <div class="flex-container  flex-one padd-l-10 padd-r-10" v-if="num==0">
                <div class="flex-container flex-dir-c printHide" style="width: 210px;">
                    <!--<div  class="tree_list padd-t-10">
                        <tree_tem3 v-for="item in treeData" :list="item" :child="'children'" :id="'mkbm'" :checked="[]"
                                   :name="'mkmc'" @change="checkedVal"></tree_tem3>
                    </div>-->
                    <div class="tree_list padd-t-10">
                        <div class="tree_tem11" v-for="(item, $index) in treeList">
                        <vue-tree-by ref="vueTreeBy" @is-open="isOpen" :$index="$index" :item="item"  @get-person="getPerson"  :tree-list="treeList" :open="open" :is-active="isActive" :is_csqx="is_csqx" ></vue-tree-by>
                    </div>
                    </div>
                    <div class=" zui-table-view padd-t-10 flex-container flex-dir-c flex-one 10  " id="brRyList01" style="min-height:230px;">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <input-checkbox @result="reCheckBox" :list="'person'" :type="'all'" :val="isCheckAll">
                                            </input-checkbox>
                                        </th>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>床号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>住院号</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <!-- v-if="jsonList.length!=0" -->
                                <tbody>
                                    <tr :tabindex="$index" v-for="(item, $index) in person" @click="checkSelect([$index,'one','person'],$event)"
                                        :class="[{'table-hovers':isChecked[$index]}]">
                                        <!--@dblclick="edit($index)"双击回调-->
                                        <td class="cell-m">
                                            <input-checkbox @result="reCheckBoxBy" :list="'person'" :type="'some'"
                                                :which="$index" :val="isChecked[$index]">
                                            </input-checkbox>
                                        </td>
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m " v-text="item.rycwbh"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.zyh"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                        </div>
                    </div>


                </div>

                <div class=" zui-table-view flex-container padd-l-10 flex-dir-c flex-one 10  printShow">
                    <!--<h1 class="h1title margin-b-10">病区药品领药申请单明细</h1>-->
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                                <tr>
                                    <th class="cell-m" v-if="is_csqx.N04003002200403 == '1'">
                                            <input-checkbox  @result="reCheckBoxByd" :list="'sqdmxList'"
                                                             :type="'all'" :val="isCheckBydAll">
                                            </input-checkbox>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-m"><span>床号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-m"><span>分组</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell  cell-l text-left"><span>药品名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>频次</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>用量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>用药方法</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>申领时间</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>申领单号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>服药日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>服药时间</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>备注</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>参考零价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>参考金额</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>医生</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>药品编码</span></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                        <table class="zui-table table-width50">
                            <!-- v-if="jsonList.length!=0" -->
                            <tbody>
                                <tr :tabindex="$index" v-for="(item, $index) in sqdmxList" class="tableTr2">
                                    <!--@dblclick="edit($index)"双击回调-->
                                    <td class="cell-m" v-if="is_csqx.N04003002200403 == '1'">
                                        <input-checkbox @result="reCheckBoxByd" :list="'sqdmxList'"
                                                        :type="'some'" :which="$index"
                                                        :val="isCheckedByd[$index]">
                                        </input-checkbox>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-m " v-text="item.rycwbh"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-m" v-text="item.fzh"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s " v-text="$index + 1"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l text-over-2 text-left" v-text="item.ryypmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.pcmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="fDec(item.sl,2)+item.yfdwmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yyffmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                            {{item.jl}}{{item.jldwmc}}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-text="fDate(item.slsj,'AllDate')"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-text="item.sldh"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="fDate(item.yyrq,'date')"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yysj"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.bz"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj * item.sl,2)"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.ysxm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.ryypbm"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                    </div>
                    <div class="printHide" style="height:30px; font-size: 20px; color: #1abc9c;background: #fff;">参考合计金额:<i v-text="fDec(talAll,2)"></i></div>
                </div>
            </div>

            <div class="flex-container  flex-one padd-l-10 padd-r-10" v-if="num==1">
                <div class="flex-container flex-dir-c printHide ">
                    <div class="tree_-two-list  padd-r-10 flex-one padd-t-10">
                        <!--<tree_tem3 v-for="item in treeData" :list="item" :child="'children'" :id="'mkbm'" :checked="[]"
                           :name="'mkmc'" @change="checkedVal"></tree_tem3>-->
                        <div class="tree_tem1" v-for="(item, $index) in treeBydList">
                            <ul>
                                <li class="position">
                                    <div @click="getFyPerson(item,$index)"  class="tree_text1"  :class="{active_tree:$index==isActive}"
                                         >{{'摆药单'+item.bydh}} {{fDate(item.bysj,'AllDate')}}</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class=" zui-table-view flex-container padd-l-10 flex-dir-c flex-one printShow">
                    <h1 class="h1title margin-b-10" v-text="fylist.bt"></h1>
                    <div class="flex-container flex-jus-sp ksfyd-title margin-b-10">
                        <p>下嘱科室：<span v-text="fylist.ksmc"></span></p>
                        <p>摆药药房：<span v-text="fylist.yfmc"></span></p>
                        <p>摆药日期：<span v-text="fDate(fylist.bysj,'datetime')"></span></p>
                        <p>摆药单号：<span v-text="fylist.bydh"></span></p>
                    </div>
                    <!--
            	作者：offline
            	时间：2018-07-12
            	描述：科室发药单汇总
            -->
                    <div v-show="!isMx" class="printShow flex-one flex-container flex-dir-c">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>剂型</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl"><span>药品名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-xl text-left"><span>规格</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>数量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                        </th>

                                        <th>
                                            <div class="zui-table-cell cell-s"><span>金额</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <!-- v-if="jsonList.length!=0" -->
                                <tbody>
                                    <tr :tabindex="$index" v-for="(item, $index) in fydmxZList" class="tableTr2">
                                        <!--@dblclick="edit($index)"双击回调-->
                                        <td>
                                            <div class="zui-table-cell cell-s " v-text="$index+1">04</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.jxmc">杨志玗</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-over-2" v-text="item.ryypmc">长期</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypgg">阿莫西林颗粒</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.fysl+item.yfdwmc">1ml：5mg*10支</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.yplj+item.jldwmc">人份(5支)</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDec(item.fysl * item.yplj,2)">350.00</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                        </div>
                    </div>
                    <!--
            	作者：offline
            	时间：2018-07-12
            	描述：科室发药单明细
            -->
                    <div v-show="isMx" class="printShow flex-one flex-container flex-dir-c">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>住院号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-m"><span>分组号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>病员姓名</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl text-left"><span>药品名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-l "><span>服药日期</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-l "><span>服药时间</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-l "><span>备注</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-l "><span>规格</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-s "><span>频次</span></div>
                                        </th>
                                         <th>
                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>数量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-s "><span>用药方法</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>医师</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>零价</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>产地</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <!-- v-if="jsonList.length!=0" -->
                                <tbody>
                                    <tr :tabindex="$index" v-for="(item, $index) in fydmxList" class="tableTr2">
                                        <!--@dblclick="edit($index)"双击回调-->

                                        <td>
                                            <div class="zui-table-cell cell-s " v-text="$index+1"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.zyh">杨志玗</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-m" v-text="item.fzh"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.brxm">长期</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-left" v-text="item.ryypmc">001093</div>
                                        </td>

                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="fDate(item.yyrq,'date')"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.yysj"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.bz"></div>
                                        </td>

                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.ypgg">阿莫西林颗粒</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.pcmc">阿莫西林颗粒</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.fyjl+item.jldwmc">7.00</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.fysl+item.yfdwmc">1ml：5mg*10支</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.yyffmc">阿莫西林颗粒</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.ysxm">人份(5支)</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">350.00</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.ypcd">产地</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                        </div>
                    </div>
                    <div style="height:30px; font-size: 20px; color: #1abc9c;background: #fff;">总摆药单数：<i v-text="treeBydList.length"></i>&emsp;&emsp;&emsp;&emsp;总金额：<i v-text="fDec(bydTotalFee,2)"></i>&emsp;&emsp;&emsp;&emsp;本单合计金额：<i v-text="fDec(talAll,2)"></i></div>
                </div>
            </div>

            <div class="flex-container  flex-one padd-l-10 padd-r-10" v-show="num==2">
                <div class="flex-container flex-dir-c printHide" style="width:340px;">
                    <div class="tree_list padd-t-10 printHide">
                        <!--<tree_tem3 v-for="item in treeData" :list="item" :child="'children'" :id="'mkbm'" :checked="[]"
                           :name="'mkmc'" @change="checkedVal"></tree_tem3>-->
<!--                        <div class="tree_tem1" v-for="(item, $index) in treeLydList">-->
<!--                            <ul>-->
<!--                                <li>-->
<!--                                    <div @click="getLydPerson(item.fydh,item.fysj,$index)" class="tree_text1" :class="{active_tree:$index==isActive}"-->
<!--                                        style="cursor: default" v-text="'发药单'+item.fydh"></div>-->
<!--                                </li>-->
<!--                            </ul>-->
<!--                        </div>-->

                        <div class="tree_list padd-t-10 printHide">
                            <div class="tree_tem1" v-for="(item, $index) in treeLydList">
                                <ul>
                                    <li>
                                        <div class="flex-container flex-align-c">
                                            <span @click="isOpen($index)" style="width: 10px" class="fa cursor" :class="open[$index]?'fa-caret-down':'fa-caret-right'"></span>
                                            <div :data-index="$index" :data-Indexs="isPrent" :class="{active_tree:$index==isPrent}" @click="getLydIsOpen(item.fydh,item.fysj,$index,$event)"  @dblclick="getLydIsOpen(item.fydh,item.fysj,$index,$event)" class="tree_text1"
                                                 v-text="item.fydh"></div>
                                             <span class="padd-l-10">{{fDate(item.fysj,'shortY')}}</span>
                                        </div>
                                        <ul v-show="open[$index]">
                                            <li v-for="(itemDh,index) in fymx(item.fymx)" class="itemCarl">
                                                <div @click="getBydPerson(itemDh.bydh,index,$index)" :class="{active_tree:index==isActives}" class="tree_text1" v-text="itemDh.bydh"></div>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>

                    </div>
                    <div class=" zui-table-view padd-t-10 flex-container flex-dir-c flex-one ">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <input-checkbox @result="reCheckBox" @click.native="getLydmxData()" :list="'wlyList'" :type="'all'" :val="isCheckAll"></input-checkbox>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl text-left"><span>未领药-摆药单号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>摆药日期</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>摆药人</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <!-- v-if="jsonList.length!=0" -->
                                <tbody>
                                    <tr :tabindex="$index" v-for="(item, $index) in wlyList" class="tableTr2">
                                        <!--@dblclick="edit($index)"双击回调-->
                                        <td class="cell-m">
                                            <input-checkbox @result="reCheckBox" @click.native="getLydmxData()" :list="'wlyList'" :type="'some'"
                                                :which="$index" :val="isChecked[$index]">
                                            </input-checkbox>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-left " v-text="item.bydh">BYD20180423000001</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDate(item.bysj,'datetime')">04-24
                                                04:35</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.byrxm"> 叶芸芸</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                        </div>
                    </div>
                </div>

                <div class=" zui-table-view flex-container padd-l-10 flex-dir-c flex-one ">
                    <!-- <h1 class="h1title margin-b-10" v-text="lyList.bt"></h1>
                    <div class="flex-container flex-jus-sp ksfyd-title margin-b-10">
                        <div>领药科室：<span v-text="lyList.ksmc"></span></div>
                        <div>摆药药房：<span v-text="lyList.yfmc"></span></div>
                        <div>发药日期：<span v-text="fDate(lyList.fysj,'date')"></span></div>
                        <div>发药单号：<span v-text="lyList.fydh"></span></div>
                    </div>-->
                    <!--
            	作者：offline
            	时间：2018-07-12
            	描述：领药单汇总
            -->
                    <div v-show="!isMx" class="printShow flex-one flex-container flex-dir-c">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>剂型</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-xl text-left"><span>药品名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s "><span>规格</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>数量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>单位</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>零价</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>金额</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <!-- v-if="jsonList.length!=0" -->
                                <tbody>
                                    <tr :tabindex="$index" v-for="(item, $index) in lydmxList" class="tableTr2">
                                        <!--@dblclick="edit($index)"双击回调-->
                                        <td>
                                            <div class="zui-table-cell cell-s " v-text="$index+1">04</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.jxmc">长期</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell  cell-xl text-over-2 text-left" v-text="item.ryypmc">001093</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.ypgg">阿莫西林颗粒</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="fDec(item.fysl,2)">1ml：5mg*10支</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.yfdwmc">7.00</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">人份(5支)</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDec(item.fysl * item.yplj,2)">350.00</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                        </div>
                    </div>
                    <!--
            	作者：offline
            	时间：2018-07-12
            	描述：领药单明细
            -->
                    <div v-show="isMx" class="printHide flex-one flex-container flex-dir-c " >
						<iframe id="fydmx" src="/FR/ReportServer?reportlet=zyb%2Fzy%2Fzy_hzfyqd.cpt" style="height: 100%;width: 100%;">
							
						</iframe>
                       <!-- <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>序号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>住院号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-m"><span>分组号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>病员姓名</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-xl text-left"><span>药品名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s "><span>规格</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>数量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>医师</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>零价</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-l "><span>备注</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-l "><span>服药日期</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell  cell-l "><span>服药时间</span></div>
                                        </th>

                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                
                                <tbody>
                                    <tr :tabindex="$index" v-for="(item, $index) in lydmxList" class="tableTr2">
                                        
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="$index+1">04</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.zyh">杨志玗</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-m" v-text="item.fzh">杨志玗</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.brxm">长期</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell  cell-xl text-over-2 text-left" v-text="item.ryypmc">001093</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.ypgg">阿莫西林颗粒</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="fDec(item.fysl,2)+item.yfdwmc">1ml：5mg*10支</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.ysxm">人份(5支)</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">350.00</div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.bz"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="fDate(item.yyrq,'date')"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.yysj"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            
                        </div> -->
                    </div>
                    <!-- <div class="printHide" style="height:30px;font-size: 20px; color: #1abc9c;background: #fff;">参考合计金额:<i v-text="fDec(talAll,2)"></i></div>-->
                </div>
            </div>

            <div class="flex-container  flex-one padd-l-10 padd-r-10" v-if="num==3">
                <div class="flex-container flex-dir-c" style="width: 210px;">
                        <!--<tree_tem3 v-for="item in treeData" :list="item" :child="'children'" :id="'mkbm'" :checked="[]"
                           :name="'mkmc'" @change="checkedVal"></tree_tem3>-->
                        <div class=" zui-table-view flex-container padd-l-10 flex-dir-c flex-one 10 ">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <input-checkbox @result="getTydPersonVal" :list="'treeTydList'" :type="'all'" :val="tydAll">
                                            </input-checkbox>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l">退药单号</div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                                <table class="zui-table table-width50">
                                    <!-- v-if="jsonList.length!=0" -->
                                    <tbody>
                                    <tr :tabindex="$index"  :class="[{'table-hovers':$index===activeIndex3,'table-hover':$index === hoverIndex3}]"
                                        @mouseenter="switchIndex('hoverIndex3',true,$index)"
                                        @mouseleave="switchIndex()" v-for="(item, $index) in treeTydList" class="tableTr2" >
                                       <td class="cell-m">
                                           <input-checkbox @result="getTydPersonVal" :list="'treeTydList'" :type="'some'" :which="$index"
                                                           :val="tydIisChecked[$index]">
                                           </input-checkbox>
                                       </td>
                                        <th>
                                            <div class="zui-table-cell cell-l">{{item.tysqdh}}</div>
                                        </th>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                </div>
                <div class=" zui-table-view flex-container padd-l-10 flex-dir-c flex-one 10 ">
					
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                                <tr>
                                    <th class="cell-m">
                                        <input-checkbox @result="reCheckBox" :list="'tydmxList'" :type="'all'" :val="isCheckAll">
                                        </input-checkbox>
                                    </th>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-s"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>床号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>剂型</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell  cell-xl text-left"><span>药品名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>申请时间 </span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>申请数量 </span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>审核数量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>已退数量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单位</span></div>
                                    </th>
                                    
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
                        <table class="zui-table table-width50">
                            
                            <tbody>
                                <tr :tabindex="$index" v-for="(item, $index) in tydmxList" class="tableTr2">
                                    
                                    <td class="cell-m">
                                        <input-checkbox @result="reCheckBox" :list="'tydmxList'" :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                                        </input-checkbox>
                                    </td>
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-s " v-text="$index+1"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.rycwbh">08</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.brxm">顾声明</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.jxmc">001093</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ryypmc">
                                            氯化钠注射剂</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-text="item.ypgg">100ml*100</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s title" v-text="fDate(item.sqsj,'datetime')">2</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.sqsl">2</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.hdsl">0</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.ytsl">0</div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc">袋</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
					
                </div>
                <!-- <div style="width:100%;height:30px;position:absolute;bottom:20px;left:200px;box-sizing: border-box; text-align:center; font-size: 20px; color: #1abc9c;background: #fff;">参考合计金额:<i v-text="fDec(talAll,2 || '0')"></i></div> -->
            </div>

        </div>
    </div>
    <script src="bqby.js"></script>
</body>
<script>
    // $(".tree_list").on("click", ".itemCarl", function () {
    //     $(this).children().addClass('active_tree');
    //     $(this).siblings().children().removeClass('active_tree');
    //     $(this).parent().parent().parent().parent().siblings().find('.tree_text1 ').removeClass('active_tree');
    // });
</script>

</html>
