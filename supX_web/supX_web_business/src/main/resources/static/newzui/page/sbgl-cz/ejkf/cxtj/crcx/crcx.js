var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
    data: {
        param: {
            page: 1,
            rows: 10,
            order: 'asc',
            "shzfbz": '1',
            "rklx": null, //入库类型 01库房入药房 02 药房入库 03盘盈入库 04调拨入库
            "beginrq": '', //beginrq,
            "endrq": '', //endrq,
            "yfbm": null,
            'sort': 'crdjcx',
            'parm': '',
        },
        queryType: false,
        qShow: true,
        cdShow: false,
        rkmxShow: false,//入库单明细
        ckmxShow: false,//出库单明细
        slType: '',
        ypSelected: {}, //单击选中的药品
        jsonList: [],
        searchWord: null,
        json: {
            jjzj: 0.0,
            ljzj: 0.0
        },
        isShow: true,
        yfkf: 0, //药房库房信息
        isCheck: null,
        isCheckMx: null,
        yfkfList: [], //药房库房列表
        djList: [], //单据列表
        mxList: {}, //单据明细
        printData: {},//打印数据
        dateBegin: null,
        dateEnd: null,
        rkfs: {
            "01": "库房入药房",
            "02": "药房入库",
            "03": "盘盈入库",
            "04": "调拨入库"
        },
        ckfs: {
            "01": "退库",
            "02": "报损",
            "03": "盘亏出库",
            "04": "调拨入库",
            '05': '出库科室'
        },
        barContent: {},
        rkfsList: {
            "0": '入库',
            "1": '出库'
        }
    },
    computed: {
        money: function () {
            var reducers = {
                totalInEuros: function (state, item) {
                    return state.jjzj += item.ypjj * parseFloat(item.rksl);
                },
                totalInYen: function (state, item) {
                    return state.ljzj += item.yplj * parseFloat(item.rksl);
                }
            };
            var manageReducers = function (reducers) {
                return function (state, item) {
                    return Object.keys(reducers).reduce(function (nextState, key) {
                        reducers[key](state, item);
                        return state;
                    }, {})
                }
            }
            var bigTotalPriceReducer = manageReducers(reducers);
            this.jsonList.reduce(bigTotalPriceReducer, this.json = {
                jjzj: 0,
                ljzj: 0,
            });
        }
    },
    mounted: function () {
        this.getKf()
    },
    methods: {
        getKf: function () {
            //库房列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
                function (data) {
                    if (data.a == 0) {
                        wrapper.yfkfList = data.d.list;
                        wrapper.param.kfbm=wrapper.yfkfList[0].kfbm;
                        wrapper.getData();
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
            //获取列表

        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            //先获取到操作的哪一个
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData();
        },
        exportMxtz: function () {
            //设置行数
            this.param.rows = 20000;
            //设置医疗机构编码
            this.param.yljgbm = jgbm;
            //数据查询参数
            if (this.yfkf == 0) {
                malert('请选择库房', 'top', 'defeadted');
                return;
            }
            if (this.queryType) {
                this.param.sort = 'zl';
            } else {
                this.param.sort = '';
            }
            //日期判断
            if (this.fDate(new Date(), 'date') === this.param.endrq) {
                this.slType = 'dqsl'
            } else {
                this.slType = 'jysl'
            }
            //设置库房
            wrapper.param.kfbm = wrapper.yfkf == 0 ? null : wrapper.yfkf
            //准备地址
            var url = "/actionDispatcher.do?reqUrl=YfbCxtjAll&types=exportMxtz&parm=" +
                JSON.stringify(this.param);
            //组合地址
            this.url = (window.location.protocol + '//' + window.location.host) + url;
            //打开下载页
            console.log(this.url);
            window.location = this.url;
        },

        //入库获取数据
        getData: function () {
            this.djList = [];

            $.getJSON('/actionDispatcher.do?reqUrl=YfbKcglRkgl&types=rkdcx' +
                '&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == 0) {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.djList = json.d.list;
                }
            });
        },
        //入库单据明细查询
        showDetail: function (index, item) {
            this.isCheck = index;
            this.rkmxShow = true;
            this.qShow = false;
            this.cdShow = false;
            this.ckmxShow = false;
            var parm = {
                "rkdh": this.djList[index]['rkdh'],
                "yfbm": this.yfkf == 0 ? null : this.yfkf,
            };
            //获取数据
            this.mxList = [];
            $.getJSON('/actionDispatcher.do?reqUrl=YfbKcglRkgl&types=rkdmxcx' + '&parm=' + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    wrapper.mxList = data.d.list;
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },

        //出库单获取数据列表
        getData1: function () {
            this.djList = [];
            $.getJSON('/actionDispatcher.do?reqUrl=YfbKcglBsgl&types=bsdcx&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == 0) {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.djList = data.d.list;
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });


        },
//单据明细查询
        showDetail1: function (index, item) {
            this.isCheck = index;
            var parm = {
                "ckdh": this.djList[index]['ckdh']
            };
            //获取数据
            this.mxList = [];
            $.getJSON('/actionDispatcher.do?reqUrl=YfbKcglBsgl&types=bsdmxcx' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        wrapper.mxList = data.d.list;
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        cancel: function () {
            this.qShow = true;
        }
    }
});
laydate.render({
    elem: '.todate'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        // wrapper.param.time = value
        wrapper.param.beginrq = value.slice(0, 10);
        wrapper.param.endrq = value.slice(13, 23);
        wrapper.getData();
    }
});




