var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        yfkfList: [{
            kfbm:'06',
            kfmc:'体外诊断试剂库',
            ksbm:'0952'
        }],
        param: {
            'page': 1,
            'rows': 100,
            'order': 'desc',
            'shzfbz': 1,
            'kfbm': '',
            'rkfs': '01',//01-出库
            'beginrq': null,
            'endrq': null,
            'parm': ''
        }
    },
    updated:function () {
        changeWin()
    },
    mounted: function () {
        this.getKf();
    },
    methods: {
        getKf: function () {
            //库房列表
            // $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf',
            //     function (data) {
            //         if (data.a == 0) {
            //             wrapper.yfkfList = data.d.list;
            //             wrapper.param.kfbm = data.d.list[0].kfbm;
            //             wrapper.getData();
            //         } else {
            //             malert(data.c, 'top', 'defeadted');
            //         }
            //     });

            // 修改成从权限中获取库房列表 用例:N040100011010
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011010"}',
                function (data) {
                    if (data.a == 0 ) {
                        // wrapper.yfkfList = data.d;
                        wrapper.param.kfbm=wrapper.yfkfList[0]['kfbm']
                        wrapper.getData();
                        wrapper.$forceUpdate()
                    } else {
                        malert('库房列表获取:' + data.c + ',请查看权限配置', 'top', 'defeadted');
                    }
                });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this.param, 'kfbm', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
            this.getData();
        },
        getData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfcxKccx&types=lskc&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                }
            });
        }
    },
});

laydate.render({
    elem: '.todate'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        // wrapper.param.time = value
        wrapper.param.beginrq = value.slice(0, 10);
        wrapper.param.endrq = value.slice(13, 23);
        wrapper.getData();
    }
});

