var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ckkdhc:{},
        popContent: {},
        queryObj: {},
        kfList: [],
        ryList: [],
        ksList: [],
        ylbm: 'N050080012002',
        mxList: [],
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        this.initial();
        // this.getKfData();
        this.getKs();
        this.getCgry();
        this.getGhdw();
    },
    methods: {
        // 新增
        AddMdel: function () {
            pop.title = '添加设备'
            pop.open();
            pop.popContent = {
                kfdwmc: this.popContent.kfbm
            };
        },
        //加载采购人员
        getCgry: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    if (data.a == 0) {
                        wrapper.ryList = data.d.list
                        wrapper.popContent.rybm = wrapper.queryObj.ckd && wrapper.queryObj.ckd.lyr || data.d.list[0].rybm;
                    } else {
                        malert("获取采购人员失败！", 'top', 'defeadted');
                    }
                });
        },
        //科室
        getKs: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                function (data) {
                    wrapper.ksList = data.d.list;
                    pop.ksList = data.d.list;
                    wrapper.popContent.ksbm = wrapper.queryObj.ckd && wrapper.queryObj.ckd.lyks || data.d.list[0].ksbm;
                });
        },
        //加载供货单位
        getGhdw: function () {
            //初始化页面记载供货单位
            var parm = {
                page: 1,
                rows: 1000,
                tybz: '0',
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhGys&types=query&json=" + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        pop.ghgysList = json.d.list;
                    } else {
                        malert("供货单位获取失败", 'top', 'defeadted');
                    }
                });
        },
        //f帆软打印
        dy:function(){
            var  ckdh= this.popContent.ckdh;//出库单号
            var  kfbm= this.popContent.kfbm;//库房编码
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            if(ckdh && kfbm){
               var reportlets ="[{reportlet: 'sbgl" + frpath +"ckdhmx.cpt',ckdh:'"+ckdh+"'}]";
            }
            //帆软打印
            if (!FrPrint(reportlets, wrapper.WzrkPrint)) {
                return
            }

        },
        //f帆软打印
        tmdy:function(){

            var count=0;
            for(var i = 0; i < this.isChecked.length ; i++){
                if(this.isChecked[i]){
                    console.log( this.mxList[i]);
                    count++;
                }
            }
            if(count != 1){
                malert('请选择一条数据打印条码','top','defeadted');
                return;
            }
            var  ckdh= this.popContent.ckdh;//出库单号
            var  jsh= this.popContent.jsh;//出库单号
            var  kfbm= this.popContent.kfbm;//库房编码
            var  sbbm= this.popContent.sbbm;//库房编码

            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            if(ckdh && kfbm){
                var  reportlets ="[{reportlet: 'sbgl" + frpath +"sbtmdy.cpt',ckdh:'"+ckdh+"',sbbm:'"+sbbm+"'}]";
            }
            //帆软打印
            if (FrPrint(reportlets, null)) {
                console.log(123);
                return
            }
            return
        },
        //审核
        sh: function () {
            Vue.set(this.popContent, 'sbkf', this.popContent.kfbm);
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywCkd&types=confirm', JSON.stringify(this.popContent))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("审核成功！", 'top', 'success');
                        wrapper.cancel()
                        // malert("审核成功！")
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
        //初始化
        initial: function () {
            this.queryObj = sessionStorage.getItem('obj') && JSON.parse(sessionStorage.getItem('obj'))
            this.kfList = this.queryObj.kfList;
            console.log(this.kfList);
            console.log( this.queryObj.kfbm);
            this.popContent.kfbm = this.queryObj.kfbm || this.queryData.kfList[0].sbkfbm;
            if (this.queryObj.sh || this.queryObj.dy) {
                this.popContent.ksbm = this.queryObj.ksbm;
                this.popContent = Object.assign(this.popContent,this.queryObj.ckd)
                this.getMx();
            }
        },
        getMx: function () {
            var obj = {
                ckdh: this.queryObj.ckd.ckdh,
                sbkf: this.queryObj.kfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywCkd&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 &&  data.d.length !=0) {
                        console.log( data.d);
                        wrapper.mxList = data.d
                        // for (var  i = wrapper.mxList.length - 1; i >=0; i--) {
                        //     if(wrapper.mxList[i].kcsl <=0){
                        //         wrapper.mxList.splice(i,1)
                        //     }
                        // }
                    }else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },
        // 提交所有
        submitAll: function () {
            var ckd = {}
            Vue.set(ckd, 'sbkf', this.popContent.kfbm);//库房编码
            Vue.set(ckd, 'lyr', this.popContent.rybm);//采购员
            //Vue.set(ckd,'lydw',wrapper.popContent.ksbm);//领用单位
            Vue.set(ckd, 'lydw', this.popContent.ksbm);
            Vue.set(ckd, 'bzms', this.popContent.bzms);//备注描述
            Vue.set(ckd, 'ksbm', this.popContent.ksbm);//领用科室
            var lyks = this.popContent.ksbm;
            this.mxList.ksbm = this.popContent.ksbm;
            var wzkfbms = this.listGetName(this.kfList, this.popContent.kfbm, 'sbkfbm', 'ksbm');
            //新增操作
            var obj = {
                list: {
                    ckd: ckd,
                    ckdmx: this.mxList,
                    wzkf: this.popContent.kfbm,
                    lyke: lyks

                }
            }
            if(JSON.stringify(wrapper.ckkdhc) == '{}'){
                wrapper.ckkdhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
            }else {
                if (JSON.stringify(wrapper.ckkdhc)==JSON.stringify(obj)){
                    malert("出库单已经保存请不要重复提交!", 'top', 'defeadted');
                    return false;
                }else {
                    wrapper.ckkdhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
                }
            }
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywCkd&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0" ) {
                        malert("保存成功！", 'top', 'success');
                        wrapper.cancel()
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        // 取消2018/07/09取消回退上一级页面
        cancel: function () {
            // malert('取消','top','defeadted')
            // this.topNewPage('出库管理','page/wzkf/kfyw/ckgl/ckgl.html','?isgetData=1');
            this.topClosePage('page/sbgl/yjkf/kfyw/ckgl/ckkd.html', 'page/sbgl/yjkf/kfyw/ckgl/ckgl.html');
            window.top.$("#出库管理")[0].contentWindow.getData();
        },
        // 编辑
        edit: function (index) {
            pop.title = '编辑设备'
            pop.popContent = this.mxList[index];
            pop.open();

        },
        // 删除2018/07/09二次删除弹窗提示
        remove: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.mxList.splice(index, 1);
            })) {
                return false;
            }
            // kd.mxList.splice(index, 1);
        }
    }
});

var pop = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        title: '',
        num: 0,
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '设备名称': 'sbmc',
            '设备规格': 'sbgg',
            '进价': 'jj',
            '折旧类别': 'zjbmmc',
            '库存数量': 'kcsl',
        },
        searchCon: [],
        selSearch: -1,
        popContent: {},
        ghgysList: [],
        sbkfList: wrapper.queryObj.kfList,
        ksList: [],
        total: 0,
    },
    mounted: function () {
        this.getwzdwData();

    },
    methods: {
        //设备单位list
        getwzdwData: function () {
            //加载数据
            var parm = {
                page: 1,
                rows: 2000,
            }
            //获取设备单位
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhSbdw&types=query&json=" + JSON.stringify(parm), function (json) {
                if (json.a == "0") {
                    pop.dwList = json.d.list;
                    pop.popContent.lydw = json.d.list[0].dwbm;
                } else {
                    malert("获取设备大类失败！", 'top', 'defeadted');
                }
            });
        },
        // 关闭
        closes: function () {
            this.num = 0;

        },
        open: function () {
            this.num = 1;
            // 设置库房
        },
        preview: function(){
            console.log(123);
            var headstr = "<html><head><title></title></head><body style='width: 100px;height: 70px' >";
            var footstr = "</body></html>";
            var printhtml="<div >\n" +
                "        <table>\n" +
                "            <tr >\n" +
                "               <th>星期日</th>\n" +
                "            </tr>\n" +
                "        </table>\n" +
                "    </div>"
           var wind = window.open("", "newwin",
                "toolbar=no,scrollbars=yes,menubar=no");
            wind.document.body.innerHTML = headstr+printhtml+ footstr;
            wind.print();

        },
        //保存
        save: function () {
            if (pop.title == '添加设备') {

                var cksl = parseInt(pop.popContent.cksl);
                if (cksl > pop.popContent.kcsl) {
                    malert("对不起，实际库存数量小于出库，无法出库，请合理分配!", 'top', 'defeadted');
                    return;
                }
                //添加
                if (wrapper.mxList.length == 0) {
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }


                for (var i = 0; i < wrapper.mxList.length; i++) {
                    if (wrapper.mxList[i].wzbm == pop.popContent.wzbm && wrapper.mxList[i].scph == pop.popContent.scph) {
                        malert("已有该批号的设备！", 'top', 'defeadted');
                        return;
                    }

                }
                wrapper.mxList.push(pop.popContent);
                pop.popContent = {};
                return;

            } else if (pop.title == '编辑设备') {
                if (wrapper.mxList.length == 0) {
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }
                //编辑保存成功关闭弹窗
                pop.popContent = {};
                pop.closes();
                return;
            }
        },
        //药品名称下拉table检索数据
        changeDown: function (event, type) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                $("#rksl").focus();
            }
        },
        //当输入值后才触发
        change: function (event, type, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var parm = {
                sbkf: wrapper.popContent.kfbm,
                page: pop.dg.page,
                rows: pop.dg.rows,
                parm: val
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=kfkc' +
                '&parm=' + JSON.stringify(parm),
                function (data) {
                    pop.searchCon = data.d.list;
                    //wap.total = data.d.total;
                    pop.selSearch = 0;
                    $(".selectGroup").show();
                });

            this.popContent[type] = val;
            if (wrapper.popContent["kfbm"] == undefined || wrapper.popContent["kfbm"] == null || wrapper.popContent["kfbm"] == "") {
                malert("库房不能为空", 'top', 'defeadted');
                return;
            }
            if (wrapper.popContent["rybm"] == undefined || wrapper.popContent["rybm"] == null || wrapper.popContent["rybm"] == "") {
                malert("采购人员不能为空", 'top', 'defeadted');
                return;
            }
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                pop.dg.page++;
                var parm = {
                    page: pop.dg.page,
                    rows: pop.dg.rows,
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=wzzd&json=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                pop.searchCon.push(data.d.list[i]);
                            }
                            pop.total = data.d.total;
                            pop.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }

            this.popContent = item;
            $(".selectGroup").hide();
        }


    }
});


laydate.render({
    elem: '.sctimes'
    , eventElem: '.icon-rl'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {

        // wrapper.param.time = value
    }
});
//有效期至
laydate.render({
    elem: '.yxtimes'
    , eventElem: '.icon-rl'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        pop.popContent.yxqz = value;
        wrapper.param.time = value
    }
});

