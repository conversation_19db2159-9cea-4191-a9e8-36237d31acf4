
var qjindex = '';
var zlxmbm = "";
    var wrapper=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            popContent:{},
            title:'',
            totle:'',
            num:0,
            search:'',
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增材料产地';
                wap.popContent={
                    tybz:'0',
                    lx:'3'
                }
                wap.open();

            },
            sx:function () {
              yjkmtableInfo.getData();
            },
            //删除
            del:function () {
                yjkmtableInfo.remove(-1);
            },
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    yjkmtableInfo.getData();
                }

            },
        },
        watch:{
        	'search':function(){
        		Vue.set(yjkmtableInfo.param,'parm',wrapper.search);
        		yjkmtableInfo.getData();
        	}
        }
    });


    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            isShow: false,
            popContent: {},
            isKeyDown: null,
            title: '',
            searchCon: [],
            dg: {
                page: 1,
                rows: 20,
                sort: "cdbm",
                order: "asc",
                parm: ""
            },
            selSearch: -1

        },

        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },

            //确定
            confirms:function () {
                yjkmtableInfo.saveData();
            },
            changeDown: function(index, type) {
                var _input = $(".popInfo input");
                var _ul = $(".popInfo ul");
                if(window.event.keyCode == 13) {
                    if(this.selSearch != -1) {
                        this.popContent[type] = this.searchCon[this.selSearch][type];
                        this.selSearch = -1
                    } else {
                        _input.eq(index + 1).focus();
                    }
                    _ul.hide();
                } else if(window.event.keyCode == 40) {
                    if((this.searchCon.length - 1) == this.selSearch) {
                        this.selSearch = 0;
                    } else {
                        this.selSearch++;
                    }
                } else if(window.event.keyCode == 38) {
                    if(this.selSearch == 0) {
                        this.selSearch = this.searchCon.length - 1;
                    } else {
                        this.selSearch--;
                    }
                } else {
                    if(this.popContent[type] != null && this.popContent[type] != "") {
                        this.dg.parm = this.popContent[type];
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=cdbm' +
                            '&dg=' + JSON.stringify(wap.dg),
                            function(data) {
                                wap.searchCon = data.d.list;
                                wap.selSearch = -1;
                                if(data.d.list.length != 0) {
                                    var el = _input.eq(index);
                                    _ul.hide();
                                    el.next().show();
                                } else {
                                    _ul.hide();
                                }
                            });
                    }
                }
            },

        }


    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

//科目
    var yjkmtableInfo = new Vue({
        el: '.zui-table-view',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            popContent:{},
            jsonList: [],
            param: {
                sort: 'cdbm',
                lx:'3'
            },
            isChecked:[],
            totlePage : 0	
        },
        mounted:function () {
            changeWin();
        },
        methods: {
            //进入页面加载列表信息
            getData: function () {
                common.openloading('.zui-table-view')
                yjkmtableInfo.jsonList=[];
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=" + JSON.stringify(yjkmtableInfo.param), function(json) {
                    yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                    yjkmtableInfo.jsonList = json.d.list;
                });
                common.closeLoading()
            },

            //保存
            saveData: function() {

            	if(!wap.popContent.tybz){
            		Vue.set(wap.popContent,'tybz','0');
            	}
                if(wap.popContent.cdmc == null) {
                    malert("请输入产地名称",'top','defeadted')
                    return;
                }
                //lx=3 为耗材产地
                wap.popContent.lx = '3';
                this.$http.post('/actionDispatcher.do?reqUrl=YkglKfwhCdbm&types=save',
                    JSON.stringify(wap.popContent))
                    .then(function(data) {
                        if(data.body.a == 0) {
                            malert("数据更新成功",'top','success');
                            yjkmtableInfo.getData();
                            wap.closes();
                        } else {
                            malert("数据失败",'top','defeadted');
                        }
                    }, function(error) {
                        console.log(error);
                    });
            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑材料产地'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                console.log(wap.popContent);
            },
            //2018/07/04二次弹窗删除提示
            remove: function(num) {
                var cdbmList = [];
                if(num == -1){
                	for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            var cdbm = {};
                            cdbm.cdbm = this.jsonList[i].cdbm;
                            cdbmList.push(cdbm);
                        }
                    }
                }else{
                    //单个删除
                    var obj = {
                        cdbm: this.jsonList[num].cdbm
                    }
                    cdbmList.push(obj);

                }
                
                if(cdbmList.length == 0) {
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }
                if (common.openConfirm("确认删除该条信息吗？",function () {
                        //'{"list":'+ JSON.stringify(cdbmList) +'}'
                        var json = '{"json":' + JSON.stringify(cdbmList) + '}';

                        yjkmtableInfo.$http.post('/actionDispatcher.do?reqUrl=YkglKfwhCdbm&types=delete&',
                            json).then(function (data) {
                            if(data.body.a == 0) {
                                malert("删除成功",'top','success')
                                yjkmtableInfo.getData();
                                yjkmtableInfo.isChecked = [];
                            } else {
                                malert("删除失败",'top','defeadted')
                            }
                        }, function(error) {
                            console.log(error);
                        });
                    })) {
                    return false;
                }
                //添加二次删除确认弹窗注释当前 数据返回删除失败
                // var json = '{"list":' + JSON.stringify(cdbmList) + '}';
                // this.$http.post('/actionDispatcher.do?reqUrl=YkglKfwhCdbm&types=delete&',
                //     json).then(function(data) {
                //     yjkmtableInfo.getData();
                //     if(data.body.a == 0) {
                //         malert("删除成功",'top','success')
                //         yjkmtableInfo.isChecked = [];
                //     } else {
                //         malert("删除失败",'top','defeadted')
                //     }
                // }, function(error) {
                //     console.log(error);
                // });
            }


        },


    });
    yjkmtableInfo.getData();




