<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药品统筹类别</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="tclb.css" rel="stylesheet">
</head>
<style>
    .zui-form .zui-form-label{
        left: 5px !important;
    }

</style>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
<div class="wrapper" >
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="sx">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5 icon-font15" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b"><i class=" icon-width icon-dc padd-l-25"></i>导出</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="search"/>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable padd-l-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                 :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>统筹类别编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>统筹类别名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音简码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>统筹类别</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>

                    <td><div class="zui-table-cell cell-s" v-text="item.tclbbm"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.tclbmc">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.pyjm">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="tclb_tran[item.tclb]">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">

                            <div class="switch">
                                <input  type="checkbox" true-value="0" false-value="1" v-model="item.tybz"  disabled/>
                                <label></label>
                            </div>
                        </div>

                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">


                            <span  class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em  class="width30">
                                    <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                               </span>
                        </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<div class="side-form ng-hide pop-width" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
            <span class="span0">
                <i>编码</i>
                <input type="text" class="zui-input border-r4"  placeholder="自动生成" v-model="popContent.tclbbm" @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
    <span class="span0">
                <i>统筹名称</i>
                <input type="text" class="zui-input border-r4" v-model="popContent.tclbmc" @keydown="nextFocus($event)"
                       @blur="setPYDM(popContent.tclbmc, 'popContent', 'pyjm')"/>
            </span>
        <span class="span0">
                <i>拼音简码</i>
                <input  class="zui-input border-r4" type="text" v-model="popContent.pyjm"  @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
        <span class="span0">
                <i>统筹类别</i>
                <select-input @change-data="resultChange" :not_empty="false"
                              :child="tclb_tran" :index="popContent.tclb" :val="popContent.tclb"
                              :name="'popContent.tclb'">
               </select-input>
            </span>
        <span id="jyxm_icon" class="margin-top-10 span0">
                <i style="float:left;">状态</i>
                <div class="switch" style="top:-2px;left: 62px;">
                    <input type="checkbox"/>
                    <label></label>
                </div>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="tclb.js"></script>
</body>

</html>
