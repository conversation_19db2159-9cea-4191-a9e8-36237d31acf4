.selectInput {
    width: 90%;
    height: 30px;
}

.toolMenu > div {
    width: 150px;
    height: 30px;
}

.toolMenu .wh180 {
    width: 180px;
}
.zui-input{
    text-indent: 0;
}

.tableDiv {
    width: calc(100% - 4px);
    height: calc(100% - 140px);
}

.patientTable tbody tr:first-child th {
    height: 32px;
}

.patientTable th:nth-child(n+2) {
    min-width: 100px;
}

.tableDiv table {
    overflow-x: hidden;
}
.wh25{
    width: 25%;
}
.wh50ALL{
    width: 50%;
}
.brSearch {
    width: 100%;
    border: 1px solid #eee;
    padding-bottom: 10px;
    padding-top: 8px;
}
.fyqdTime{
    width: 100%;
    text-align: center;
}

.fyqdContext{
    width: 780px;
    padding-bottom: 30px;
}

.fyqdzxksContext{
    width: 900px;
    margin: 0 auto;
    padding-bottom: 30px;
}

.fyqdContext h2{
    width: 100%;
    text-align: center;
    margin: 5px 0;
}

.infoIpt{
    width: 23%;
    margin: 0;
    position: relative;
    float: left;
    height: 32px;
}
.patientTable {
    min-width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}
.infoIpt span{
    display: block;
    float: left;
    font-size: 12px;
    padding: 8px 0 0 0;
}
.patientTable td, th {
    height: 24px;
    padding: 6px;
    font-size: 14px;
    cursor: default;
    white-space: nowrap;
}
.fyqdTable td{
    border: 1px solid #000000;
	font-size: 3mm;
	color: #000;
	font-family: '微软雅黑 宋体 Arial';
}
.infoIpt p {
    float: left;
    text-align: right;
    width: 90px;
    padding: 7px 8px 7px 0;
    font-size: 14px;
    margin: 0;
    color: #333333;
}
.fyqdTable tr:first-child{
    text-align: center;
}

.total{
    margin-top: -1px;
    padding: 4px 10px;
    border: 1px solid #000000;
}

.fyqdTable td span:first-child{
    float: left;
}

.fyqdTable td span:last-child{
    float: right;
    margin-right: 176px;
}

.infoIpt p{
    width: 100px;
}
.N03004200261_table td{
    font-size: 10pt;
}
#context::-webkit-scrollbar{
    width: 15px;
}
#context:hover::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.5);
}
@page {
    padding:0;
    padding-left:1mm;
    padding-right:1mm;
    margin: 0;
    margin-left: 0.5cm;
    margin-right: 0.5cm;
}
.yz_model{
    height: 500px;
    overflow: auto;
    width: auto;
}
#fyqd .wjzDjpop{
    z-index: 110 !important;
}
.fygg{
	width:40px;
	max-width: 40px;
	overflow:hidden;
}
