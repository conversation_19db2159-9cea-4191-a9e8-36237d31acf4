var socket;
//顶部工具栏
var tool=new Vue({
    el:'#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    components: {
        'calendar': calendar,
        'search-table': searchTable,
        'search-table2': searchTable
    },
    data:{
        zt: '9',
        wjzShow:false,
        bgys:[],
        popContent:{},
        wjzxxList:[],
        index:null,
        userInfo:window.top.J_tabLeft.userInfo,
        searchjson:{},
        searchjson2:{},
        selSearch:-1,
        selSearch2:-1,
        searchCon:[],
        searchCon2:[],
        them: {
            '医生姓名': 'ryxm',
            '医生编码': 'rybm',
            '医生科室': 'ksmc',
        },
        them_tran: {
            'brxb': dic_transform.data.brxb_tran
        },
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        qxksList:[],
        beginrq:getTodayDateBegin(),
        endrq:getTodayDateEnd()
    },
    updated:function () {
        changeWin()
    },
    created:function(){
        this.getCsqx();
    },
    mounted:function(){
        laydate.render({
            elem: '#jbgrq',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                tool.popContent.jbgrq = value;
            }
        });
        laydate.render({
            elem: '#ysqrrq',
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                tool.popContent.ysqrrq = value;
            }
        });
    },
    methods:{
        jsShow:function(item){
            common.openConfirm('是否确认',function () {
                tool.save(item);
            },function () {

            })
        },
        save:function(json){
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=saveZyywWjz&parm=" + JSON.stringify(json), function(json) {
                if(json.a == '0'){
                        malert(json.c);
                        tool.getData()
                }else{
                    malert(json.c,"top","defeadted");
                }
            });
        },
        printWjz:function(){
            window.print();
        },
        change: function (type,add, val,event) {
            var jsonText;
            var searchCon;
            var selSearch;
            if(type == 'jbgys'){
                jsonText = "searchjson";
                searchCon = "searchCon";
                selSearch = "selSearch";
            }else{
                jsonText = "searchjson2";
                searchCon = "searchCon2";
                selSearch = "selSearch2";
            }
            tool[jsonText]['text'] = val;
            if (!add) this.page.page = 1;       //  设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = tool[jsonText]['text'];
            var json = {
                ysbz: '1',
                tybz: '0',
                ksbm:tool.userInfo.ksbm
            };
            var dg = {
                page: this.page.page,
                rows: this.page.rows,
                parm: this.page.parm,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(json) + "" + "&dg=" + JSON.stringify(dg), function (data) {
                if (add) {
                    for (var i = 0; i < data.d.list.length; i++) {
                        tool[searchCon].push(data.d.list[i]);
                    }
                } else {
                    tool[searchCon] = data.d.list;
                }
                tool.page.total = data.d.total;
                tool[selSearch] = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                }
            });
        },

        changeDown: function (event, type,jbgys) {
            var jsonText;
            var searchCon;
            var selSearch;
            var ys;
            if(jbgys == 'jbgys'){
                jsonText = "searchjson";
                searchCon = "searchCon";
                selSearch = "selSearch";
                ys = "jbgys";
            }else{
                jsonText = "searchjson2";
                searchCon = "searchCon2";
                selSearch = "selSearch2";
                ys = "ysqrr";
            }
            if (this[searchCon][this[selSearch]] == undefined) return;
            this.keyCodeFunction(event, jsonText,searchCon);
            if (event.code == 'Enter' || event.code == 13) {
                if (type == "text") {
                    Vue.set(tool[jsonText], 'text', this[jsonText]['ryxm']);
                    Vue.set(tool.popContent, ys, this[jsonText]['rybm']);
                    this.selSearch = 0;
                    this.nextFocus(event);
                }
            }
        },
        selectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.changeDown(true, 'text', this.searchjson['text'],'jbgys');
            } else {
                this.searchjson = item;
                Vue.set(tool.searchjson, 'text', this.searchjson['ryxm']);
                Vue.set(tool.popContent, 'jbgys', this.searchjson['rybm']);
                $(".selectGroup").hide();
            }
        },
        selectOne2: function (item) {
            if (item == null) {
                this.page.page++;
                this.changeDown(true, 'text', this.searchjson2['text'],'ysqrr');
            } else {
                this.searchjson2 = item;
                Vue.set(tool.searchjson2, 'text', this.searchjson2['ryxm']);
                Vue.set(tool.popContent, 'ysqrr', this.searchjson2['rybm']);
                $(".selectGroup").hide();
            }
        },
        refreshList:function(wjzxx){
            var len = wjzxx.length;
            var len2 = tool.wjzxxList.length;
            for(var i=0;i<len;i++){
                var add = true;
                if(wjzxx[i].ysqr != '1'){
                    if(len2 > 0){
                        for(var j=0;j<len2;j++){
                            if(wjzxx[i].jyxh == tool.wjzxxList[j].jyxh && wjzxx[i].zbbm == tool.wjzxxList[j].zbbm){
                                add = false;
                            }
                        }
                        if(add){
                            var nl = tool.toAge(wjzxx[i].csrq);
                            wjzxx[i].nl = nl.age;
                            wjzxx[i].nldw = nl.unitNum;
                            tool.wjzxxList.push(wjzxx[i]);
                        }
                    }else{
                        var nl = tool.toAge(wjzxx[i].csrq);
                        wjzxx[i].nl = nl.age;
                        wjzxx[i].nldw = nl.unitNum;
                        tool.wjzxxList.push(wjzxx[i]);
                    }
                }
            }
        },

        //请求后台查询列表信息
        getData : function(){
            //加载动画效果
            common.openloading('.zuiTableBody');
            var parameters = {
                searchksbmList : tool.qxksList,
                ysqrr : '1',
                ydbz : '1',
                lx:'0',
                beginrq:tool.beginrq,
                endrq:tool.endrq,
            };
            tool.wjzxxList = [];
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=queryWjz&parm=" + JSON.stringify(parameters), function(json) {
                    if(json.a == '0'){
                        if(json.d && json.d.list && json.d.list.length>0){
                            tool.refreshList(json.d.list);
                            malert("危急值列表刷新！");
                        }
                    }else{
                        malert(json.c,"top","defeadted");
                    }
                });
            //数据请求结束时关闭
            common.closeLoading()
        },
        //已接收操作
        phone:function () {
            pop.wjzShow=false;
        },
        launch:function () {
            brzcList.title='发起危急值'
            brzcList.btnTitle='发起'
            brzcList.launchShow=true;
            $("input[name='disable']").each(function () {
                $(this).attr('disabled',true)
                $(this).addClass('background-h');
            });
            $(".patient>.zui-left > .zui-input").each(function () {
                $(this).attr("contenteditable",false)
                $(this).addClass('background-h');
            })
            brzcList.open();
        },
        //患者姓名跳转到医嘱处理页面
        Patient:function () {
            this.topNewPage('护士站医嘱处理','page/hsz/hlyw/yzgl/yzgl.html')
        },
        editWjz:function(index){
            tool.index = index;
            tool.wjzShow = true;
            tool.popContent = tool.wjzxxList[index];
            //初始化部分值
            tool.popContent.jbgys = userId;
            tool.popContent.ysqrr = userId;
            tool.popContent.jbgrq = tool.fDate(new Date(),'datetime');
            tool.popContent.ysqrrq = tool.fDate(new Date(),'datetime');
            tool.searchjson.text = tool.userInfo.czyxm;
            tool.searchjson2.text = tool.userInfo.czyxm;
            tool.$forceUpdate();
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction').then(function (json) {
                    this.userInfo = json.body.d;
                });
        },
        saveData:function(){
            if(!tool.popContent.ysqrnr){
                malert("处理意见不能为空！","top","defeadted");
                return;
            }
            this.popContent.qrr = "";
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=saveZyywWjz&parm=" + JSON.stringify(this.popContent),
                function(json) {
                    if(json.a == '0'){
                        tool.wjzShow = false;
                        tool.getData();
                        tool.$forceUpdate();
                        malert("处理危急值成功！");
                    }else{
                        malert(json.c,"top","defeadted");
                    }
                });
        },
        getCsqx:function(){
            $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm={"ylbm":"N030032001"}',
                function (json) {
                    if (json.a == "0"){
                        tool.qxksList = json.d;
                        tool.getData();
                    }else{
                        malert(json.c,'top','defeadted');
                    }

                });
        },
    }
});
//弹窗展示
var brzcList = new Vue({
    el : '#brzcList',
    mixins : [ dic_transform, baseFunc, tableBase, mformat ,checkData,scrollOps],
    data : {
        title : '登记危急值',
        btnTitle:'登记',
        num:0,
        BgList:[],
        ryxmList:[],
        launchShow:false,
        popContent:{},
        is_state:{
            '0':' ',
            '1':' ',
        },
        is_result:{
            '0':'阴性',
            '1':'25.00mmol/L'
        },
        XsList:[

            {
                bgxs:'系统',
                ksbm:'01',
            },
            {
                bgxs:'系统+电话',
                ksbm:'02',
            },
        ],
        datas: [
            {
                name: '白蛋白',
                result: '0',
                wxbz:'1',
                state: '0'
            },
            {
                name: '白蛋白',
                result: '1',
                wxbz:'0',
                state: '1'
            },
            {
                name: '白蛋白',
                result: '1',
                wxbz:'1',
                state: '1'
            }
        ]
    },
    methods : {
        // 关闭
        close: function() {
            this.num=0;
        },
        open : function() {
            this.num=1;
        },
        //保存
        register:function () {

        },
        //报告科室
        getKsData: function(){
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm", function(json) {
                brzcList.BgList = json.d.list;
            });
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=zyjszgdm&dg=" + JSON.stringify(this.param), function(json) {
                pop.ryxmList = json.d.list;
                brzcList.ryxmList = json.d.list;
            });
        },
        //报告形式选择
        resultChanges:function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "ksbm":
                    Vue.set(this.popContent, 'ksbm', val[0]);
                    Vue.set(this.popContent, 'bgxs', val[4]);
                    //报告形式：下拉选择系统+电话或系统（如选择系统则下⽅方电话接收⼈人下拉框不不显示）
                    if(val[0]=='01'){
                        $('#phone').hide();
                    }else{
                        $('#phone').show();
                    }
                    break;
                default:
                    break;
            }
        },
    }
});
//弹窗危急值复述消息
var pop=new Vue({
    el:'.wjzFspop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data:{
        wjzShow:false,
        ryxmList:[],
        bgys:[],
        popContent:{},
    },
    methods:{
        //关闭取消操作
        Popclose:function () {
            this.popShow=false;
        },
        //确定
        popConfirm:function () {
            malert('确定','top','success')
        },
    }
});
laydate.render({
    elem: '#beginrq',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        tool.beginrq = value;
        tool.getData();
    }
});
laydate.render({
    elem: '#endrq',
    type: 'datetime',
    trigger: 'click',
    theme: '#1ab394',
    done: function (value, data) {
        tool.endrq = value;
        tool.getData();
    }
});




