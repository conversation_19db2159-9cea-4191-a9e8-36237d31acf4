.tong-search .zui-form .padd-r-20 {
    padding-left: 45px;
}
.text-decoration {
    color: #1abc9c;
    cursor: pointer;
    text-decoration: underline;
}
.userNameImg {
    border-radius: 50px;
    width: 60px;
    display: inline-block;
    height: 60px;
    margin: 5px 7px 0 8px;
    text-align: center;
}

.userNameImg img {
    width: 100%;
    height: 100%;
    margin: auto;
    cursor: pointer;
    display: inline-block;
}
.kpFlex {
    padding-top: 15px;
    border: none;
    overflow: auto;
    width: 100%;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    align-self: center;
    /*align-items: center;*/
    align-content: flex-start;
    margin: 0 auto;
    justify-content: center;
}
.userName {
    font-size: 18px;
    color: #1abc9c;
    cursor: pointer;
    float: left;
    display: block;
    font-weight:500;
}
.userName:hover{
    opacity: 0.56;
}
.userName-pin img{
    /*background-image: url("/newzui/pub/image/pin.png");*/
    /*background-image: linear-gradient(-180deg, #f99696 3%, #f56363 100%);*/
    /*border: 1px solid #f46161;*/
    border-radius: 4px;
    background-color: #f46161;
    width: 18px;
    height: 18px;
    /*background-position: center center;*/
    /*background-repeat: no-repeat;*/
    font-size: 10px;
    color: #ffffff;
}

.userName-lc img{
    /*background-image: linear-gradient(-180deg, #ffb456 3%, #ed8805 100%);*/
    border: 1px solid #ed8705;
    border-radius: 4px;
    width: 20px;
    background-color: #ed8705;
    height: 20px;
    font-size: 10px;
    color: #ffffff;
}

.sex {
    font-size: 14px;
    margin-right: 12px;
}
.color-man{
    color:#4b8ad4;
}
.color-woman{
    color: #fa6969;
}
.username-nl {
    font-size: 14px;
    color: #354052;
    margin-right: 19px;
}

.djzt {
    position: absolute;
    right:0px;
    top: -1px;
    background: url("/newzui/pub/image/<EMAIL>") center no-repeat;
    background-size: 85px 67px;
    font-size: 15px;
    color: #ffffff;
    text-align: center;
    overflow: hidden;
    width:85px;
    height:67px;
}
.jzztb{
    position: absolute;
    right:0px;
    top: -1px;
    background: url("/newzui/pub/image/jzz.png") center no-repeat;
    background-size: 85px 67px;
    font-size: 15px;
    color: #ffffff;
    text-align: center;
    overflow: hidden;
    width:85px;
    height:67px;
}

.red{
    background-color: #EF6263;
}
.blue{
    background-color: #4B9BFD;
}
.redCj{
    background-color: #F23E3E;
}
.header-text {
    background: #F6FDFA;
    width: 100%;
    height:160px;
    border-bottom: 1px solid #E9F0EE;
}
.header-text-green {
    background: #c1fdd0;
    width: 100%;
    height:160px;
    border-bottom: 1px solid #E9F0EE;
}
.header-text-yellow {
    background: #ffe1b4;
    width: 100%;
    height:160px;
    border-bottom: 1px solid #E9F0EE;
}
.header-text-red {
    background: #fdab9e;
    width: 100%;
    height:160px;
    border-bottom: 1px solid #E9F0EE;
}

.userWidth {
    /*margin: 10px 0;*/
    background: #ffffff;
    box-shadow: 0 0 9px 0 rgba(0, 0, 0, 0.15);
    width: 307px;
    margin:5px 9px 10px 9px;
    display: inline-block;
    /*overflow: hidden;*/
    height: 160px;
}

.userName {
    margin-right:20px;
}

.userName-pin {
    margin-right:10px;
}
.color-7f{
    color:#333333;
}
.main-content {
    margin-top: 4px;
    padding-left: 12px;
}

.main-content p {
    font-size: 14px;
    color: #7f8fa4;
    text-align: left;
    margin-bottom: 5px;
}

.margin-l13 {
    margin-left: 56px;
}
.font-20{
    font-size: 24px;
    position: relative;
    cursor: pointer;
    color: rgba(100, 111, 130, 0.5490196078431373);
}
.shuxian:after{
    content: '';
    height: 80%;
    width: 1px;
    background: #dfe3e9;
    position: absolute;
    z-index: 111;
    right: -10px;
    top: 2px;
}

.footer-text {
    text-align: center;
    display: flex;
    justify-content: space-around;
    align-items: center;
    height:40px;
    color:#354052;
    position: relative;
}
.footer-text span i{
        display: block;
    width: 100%;
    flex-wrap: wrap;
}
.footer-text span:hover{
    /*color: rgba(53,64,82,.56);*/
    opacity: 0.56;
}
.footer-text span{
    cursor: pointer;
    position: relative;
}
.footer-text .foot-span:before{
    content: '';
    position: absolute;
    top: 0;
    right:-10px;
    width: 1px;
    height: 20px;
    background: #eee;
}
.footer-text .foot-rf:before{
    right:-45px;
}
.footer-text span:nth-child(3):before{
    top:11px;
}
.hz-user{

}
.title-width{
    width: 60px;
}
.icon-left:before{
    left:30px !important;
}
.icon-right:before{
    left: 60px !important;
}
.wh20{
    width: 20px !important;
}
@media screen and (max-width: 1366px){

}
.icon-width:before{
    left: inherit !important;
}
.menu-right {
    padding: 0px 15px;
    height: 32px;
}
.menu-right span{
    padding: 0 5px;
    display: flex;
    height: 24px;
    justify-content: center;
    align-items: center;
}
.menu-right .fenge{
    font-size: 0;
    width: 1px;
    height: 17px;
    background:#646f82;
    padding: 0;
}
.fa-th-large:before{
    font-size: 18px;
    padding-top: 3px;
}
.fa-th-large.active::before {
    color: #1abc9c;
}
