var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        totlePag: 0,
        zhuangtai: {
            '0': '待审核',
            '1': '已审核',
            '2': '未通过',
            '3': '已作废'
        },
        rkfs: {
            '01': '入库',
            '02': '退库',
            '03': '盘点入库'
        },
        jsonList: [],
        popContent: {},
        kfList: [],
        search: '',
        ylbm: 'N050080012001',
    },
    updated:function () {
        changeWin()
    },
    mounted: function () {
        this.getKfData();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getKfData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getKfData();
            }
        });
    },
    methods: {
        LingYaoD: function () {
            brzcList.open();
        },
        sh: function (index) {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.kfbm);
            Vue.set(obj, 'kfList', this.kfList);
            Vue.set(obj, 'rkd', this.jsonList[index]);
            Vue.set(obj, 'ksbm', this.jsonList[index].ksbm);
            Vue.set(obj, 'sh', this.jsonList[index].shzfbz=="0");
            Vue.set(obj,'dy',(this.jsonList[index].shzfbz !="0"));
            wrapper.Verify(obj);
        },
        //跳转
        Verify: function (obj) {
            sessionStorage.setItem('obj', JSON.stringify(obj));
            this.topNewPage('入库开单', 'page/sbgl/kfyw/rkgl/rkkd.html');
        },


        //作废2018/07/09二次弹窗作废提示
        // Refuse: function (index) {
        //     var obj = this.jsonList[index];
        //     if (common.openConfirm("确定作废物资入库单号-" + obj.rkdh + "-入库单吗？", function () {
        //         wrapper.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywRkd&types=invald', JSON.stringify(obj)).then(function (data) {
        //             if (data.body.a == "0" || data.a == "0") {
        //                 wrapper.getKfData();
        //                 malert("作废成功！", 'top', 'success');
        //                 // malert("审核成功！")
        //             } else {
        //                 malert("作废失败", 'top', 'defeadted');
        //             }
        //         }, function (error) {
        //             console.log(error);
        //         });
        //     })) {
        //         return false;
        //     }
        // },
        Refuse: function (index) {
            var obj = this.jsonList[index];
            if (common.openConfirm("<div>确定作废物资入库单号-" + obj.rkdh + "-入库单吗？<br/> <div class=\"flex-container flex-align-c\"><span class=\"ft-14 whiteSpace padd-r-5\">作废原因</span>\n" +
                "<textarea rows=\"3\" cols=\"100\" id='zfyy' class=\" padd-t-5 padd-b-5 padd-l-5 padd-r-5 wh100MAx\"></textarea></div></div>", function () {
                obj.zfyy=$('#zfyy').val()
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywRkd&types=invald', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        wrapper.getKfData();
                        malert("作废成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert("作废失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }
        },


        //开单
        openPage: function () {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.kfbm);
            Vue.set(obj, 'kfList', this.kfList);
            this.Verify(obj);
        },
        //加载库房名称
        getKfData: function () {
            var parm = {
                page: 1,
                rows: 20000,
                sort: 'sbkfbm',
                tybz: '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=sbkf&json=" + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        wrapper.kfList = data.d.list;
                        //默认库房
                        Vue.set(wrapper.param, 'sbkf', data.d.list[0].sbkfbm);
                        wrapper.getData();
                    } else {
                        malert("获取库房失败！", 'top', 'defeadted');
                    }
                });
        },

        //获取入库单List
        getData: function () {
            this.param.rkfs = '01';
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywRkd&types=query&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == "0" && data.d && data.d.list.length!=0) {
                        wrapper.jsonList = data.d.list;
                        wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
                    } else {
                        malert("列表获取失败");
                    }
                });
        }, //库房
        //库房
        resultRydjChange: function (val) {
            Vue.set(this.param, 'sbkf', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
            this.getData();
        },
    }
});
function getData() {
    wrapper.getData()
}
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifClick: true,
        nums: 1,
        ksList: [],
        lymxList: [],
        lydList: [],
        popContent: {}
    },
    mounted: function () {
    },
    methods: {
        //关闭
        closes: function () {
            this.nums = 1
        },
        resultChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData()
        },
        open: function () {
            this.nums = 0
            this.getData()
        },
        getData: function () {
            //发送请求获取结果
            this.param.rows = 1000;
            this.param.beginrq = null;
            this.param.endrq = null;
            this.param.wzkf = wrapper.param.kfbm;
            $.getJSON('/actionDispatcher.do?reqUrl=New1WzkfKfywCgjh&types=queryDj&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == "0") {
                        brzcList.lydList = data.d.list;
                    } else {
                        malert(data.c,'top','defeadted');
                    }
                });
        },
        getMx: function (item) {
            this.popContent = item;
            var obj = {
                jhdh : item.jhdh,
                wzkf : item.kfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1WzkfKfywCgjh&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 || data.body.a == 0) {
                        brzcList.lymxList = data.d
                    } else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },
        //提交
        tjCom: function () {
            var rkd = {}
            Vue.set(rkd,'kfbm',this.popContent.wzkf);//库房编码
            Vue.set(rkd,'cgry',this.popContent.zdy);//采购员
            Vue.set(rkd,'fphm',this.popContent.fphm);//发票号码
            Vue.set(rkd,'bzms',this.popContent.bzms);//备注描述
            //新增操作
            var obj = {
                list : {
                    rkd : rkd,
                    rkdmx : this.lymxList
                }
            }

            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywRkd&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("保存成功！",'top','success');
                        brzcList.close();
                    } else {
                        malert(data.body.c,'top','defeadted');
                    }
                });
        },
    }
});
