var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        kfList: [],
        ylbm: 'N050080012002',
        totlePage: 0,
        zhuangtai: {
            '0': '待审核',
            '1': '已审核',
            '2': '未通过',
            '3': '已作废'
        },
        ckfs: {
            '01': '出库',
            '02': '退货',
            '03': '报损',
            '04': '盘亏出库'
        },
        id:{},
        jsonList: [],
    },
    mounted: function () {
        this.getKfData();
        this.getKs();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
    },
    methods: {

        getKs: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                function (data) {
                    brzcList.ksList = data.d.list;
                    brzcList.param.ksbm =  data.d.list[0].ksbm;
                });
        },
        //审核
        sh: function (index) {
            var obj = this.jsonList[index];
                    wrapper.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywZjfgl&types=confirm', JSON.stringify(obj)).then(function (data) {
                        if (data.body.a == "0" || data.a == "0") {
                            malert("作废成功！", 'top', 'success');
                            wrapper.getData();
                        } else {
                            malert("作废失败", 'top', 'defeadted');
                        }
                    }, function (error) {
                        console.log(error);
                    });
        },
        getData: function () {
            this.param.kfbm = this.param.sbkf;
            delete this.param.shzfbz
            // this.param.shzfbz=''
            // this.param.ckbz='';//出库标志
            //发送请求获取结果
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywZjfgl&types=query&parm=' + JSON.stringify(this.param), function (data) {
                if (data.a == "0") {
                    wrapper.jsonList = data.d.list;
                    wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
                    malert(data.c, 'top', 'success');
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },
        //开单
        openNewPage: function () {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.sbkf);
            Vue.set(obj, 'kfList', this.kfList);
            wrapper.Verify(obj);
        },
        //库房list
        getKfData: function () {
            // 请求库房的api
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=sbkf', function (data) {
                if (data.a == 0) {
                    console.log(data.d.list);
                    wrapper.kfList = data.d.list;
                    console.log(data.d.list[0].sbkfbm)
                    Vue.set(wrapper.param, 'sbkf', data.d.list[0].sbkfbm);
                    wrapper.getData();
                } else {
                    malert("获取库房列表失败");
                }
            });
        },
        //库房
        resultRydjChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.kf_Query(val[0]);
        },
    }
});
function getData() {
    wrapper.getData()
}




