<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>患者中心</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="hzxx.css" rel="stylesheet">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="background-box1 background-f">
    <div class="hzzx-top">
        <header class="userNameBg" v-cloak>
            <div class="printArea printShow"></div>
            <div class="flex printHide">
                <div class="text-color padd-l-10">
                    <span class="userName" v-text="Brxx_List.brxm"></span>
                    <span class="sex text" v-text="brxb_tran[Brxx_List.brxb]"></span>
                    <span class="nl text" v-text="Brxx_List.brnl+nldw_tran[Brxx_List.nldw]"></span>
                    <span class="cwh text">挂号序号：<span v-text="Brxx_List.ghxh"></span><span
                            v-if="Brxx_List.ghxh==null">无</span></span>
                    <span class="zyh text">科室：<span v-text="Brxx_List.ghksmc"></span><span
                            v-if="Brxx_List.ghksmc==null">无</span></span>
                    <span class="ys text">医师：<span v-text="Brxx_List.jzysxm"></span><span
                            v-if="Brxx_List.jzysxm==null">无</span></span>
                    <span class="bz text">病种：<span v-text="Brxx_List.jbmc"></span><span
                            v-if="Brxx_List.jbmc==null">无</span></span>
                    <span class="phone text">联系电话：<span v-text="Brxx_List.sjhm"></span><span
                            v-if="Brxx_List.sjhm==null">无</span></span>
                    <span class="sfz text">身份证：<span v-text="Brxx_List.sfzh"></span><span
                            v-if="Brxx_List.sfzh==null">无</span></span>
                    <span class="sfz text padd-r-20">详细地址：<span v-text="Brxx_List.jzdmc"></span><span
                            v-if="Brxx_List.jzdmc==null">无</span></span>
                </div>
            </div>
        </header>
    </div>
    <div class="content  background-f">

        <div class="fyxm-tab printHide">
            <div><span :class="{'active':num==0}" onclick="tabBg('brPage/qjdj',0,this)">抢救登记</span></div>
<!--            <div><span :class="{'active':num==1}" onclick="tabBg('brPage/yzgl',1,this)">留观医嘱</span></div>-->
<!--            <div v-if="showNewTab"><span :class="{'active':num==2}" onclick="tabBg('brPage/jcjy',2,this)">检查检验报告</span></div>-->
        </div>
        <div class="loadPage over-auto col-x-12">

        </div>
    </div>
</div>
<script type="text/javascript" src="qjyz.js"></script>
</body>
</html>