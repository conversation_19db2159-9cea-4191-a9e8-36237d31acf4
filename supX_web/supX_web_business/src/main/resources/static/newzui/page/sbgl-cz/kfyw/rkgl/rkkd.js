var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        WzrkPrint:'',
        rkdhc:{},//入库单缓存
        popContent: {},
        queryData: {},
        selectIndex: undefined,
        kfList: [],
        ryList: [],
        mxList: [],
        tjbtn: false,
        shbtn: false,
    },
    computed:{
        sumMoney:function () {
            return  this.mxList.reduce(function (total, num) {
                return  total+=num.rksl * num.dj
            }, 0)
        }
    },
    updated: function () {
        changeWin()
    },
    mounted:function(){
        this.initial();
        this.getCgry();
        this.getGys();
    },
    methods: {
        //帆软打印
      dy:function(){
          this.queryData = JSON.parse(sessionStorage.getItem('obj'));
          var  rkdh= this.queryData.rkd.rkdh;//入库单号
          var  kfbm= wrapper.popContent.kfbm;//库房编码
          var yljgbm=jgbm;
          if (window.top.J_tabLeft.obj.frprintver == "3") {
              frpath = "%2F";
          } else {
              frpath = "/";
          }
          if(rkdh !=null && kfbm !=null && rkdh !=undefined && kfbm !=undefined){
           var  reportlets ="[{reportlet: 'sbgl" + frpath +"rkglmx.cpt',rkdh:'"+rkdh+"'}]";
          }
          //帆软打印
          if (!FrPrint(reportlets, wrapper.WzrkPrint)) {
          }

      },
        dytm: function(){

        },

        //审核
        sh: function () {
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywRkd&types=confirm', JSON.stringify(this.popContent))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("审核成功！", 'top', 'success');
                        wrapper.cancel()
                        // malert("审核成功！")
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
        // 提交所有
        submitAll: function () {
            malert('提交所有', 'top', 'success')
            var rkd = {}
            Vue.set(rkd, 'kfbm', this.popContent.kfbm);//库房编码
            Vue.set(rkd, 'cgry', this.popContent.rybm);//采购员
            Vue.set(rkd, 'fphm', this.popContent.fphm);//发票号码
            Vue.set(rkd, 'bzms', this.popContent.bzms);//备注描述
            Vue.set(rkd, 'shzfbz', 0);//备注描述
            //新增操作
            var obj = {
                list: {
                    rkd: rkd,
                    rkdmx: this.mxList
                }
            }
            if(JSON.stringify(wrapper.rkdhc) == '{}'){
                wrapper.rkdhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
            }else {
                // console("我在判断是否重复提交了");
                if (JSON.stringify(wrapper.rkdhc)==JSON.stringify(obj)){
                    malert("入库单已经保存请不要重复提交!", 'top', 'defeadted');
                    return false;
                }else {
                    wrapper.rkdhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
                }
            }
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywRkd&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.cancel()
                        malert("保存成功！", 'top', 'success');
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        // 取消2018/07/09添加取消返回管理界面
        cancel: function () {
            this.topClosePage('page/sbgl/kfyw/rkgl/rkkd.html', 'page/sbgl/kfyw/rkgl/rkgl.html')
            window.top.$("#入库管理")[0].contentWindow.getData();
            // malert('取消','top','defeadted')
        },
        // 编辑
        edit: function (num) {
            pop.title = '编辑设备'
            pop.open();
            // pop.popContent = JSON.parse(JSON.stringify(kd.mxList[num]));
            this.selectIndex=num;
            pop.popContent = this.mxList[num];

        },
        // 删除二次弹窗删除提示
        remove: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.mxList.splice(index, 1);
            })) {
                return false;
            }
            // this.mxList.splice(index,1);
        },
        // 新增
        AddMdel: function () {
            pop.title = '添加设备'
            pop.open();
            pop.popContent = {};
            pop.popContent = {
                'kfdwmc': this.popContent.kfbm,
            };
        },
        //初始化
        initial: function () {
            this.queryData = JSON.parse(sessionStorage.getItem('obj'));
            this.kfList = this.queryData.kfList;
            this.popContent.kfbm = this.queryData.kfbm || this.queryData.kfList[0].wzkfbm;
            if (this.queryData.sh || this.queryData.dy) {
                this.popContent.ksbm = this.queryData.ksbm;
                this.popContent = Object.assign(this.popContent,this.queryData.rkd);
                Vue.set(this.popContent, 'rybm', this.popContent.cgry);
                this.getMx();
            }
        },
        getMx: function () {
            var obj = {
                rkdh: this.queryData.rkd.rkdh,
                kfbm: this.popContent.kfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywRkd&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 &&  data.d.length !=0) {
                        wrapper.mxList = data.d
                    } else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载采购人员
        getCgry: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    if (data.a == 0) {
                        wrapper.ryList = data.d.list;
                        wrapper.popContent.rybm = data.d.list[0].rybm;
                    } else {
                        malert("获取采购人员失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载供应商
        getGys: function () {
            var parm = {
                tybz: '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhGys&types=query&json=" + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        pop.gysList = data.d.list;
                        pop.popContent.gysbm = data.d.list[0].gysbm;
                    } else {
                        malert("获取供应商人员失败！", 'top', 'defeadted');
                    }
                });
        },
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "kfbm":
                    Vue.set(wrapper.popContent, 'kfbm', val[0]);
                    Vue.set(wrapper.popContent, 'kfmc', val[4]);
                    break;
                case "rybm":
                    Vue.set(wrapper.popContent, 'rybm', val[0]);
                    Vue.set(wrapper.popContent, 'ryxm', val[4]);
                    break;
                default:
                    break;
            }
        }


    }

});

var pop = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat, checkData],
    components: {
        'search-table': searchTable
    },
    data: {
        popContent: {},
        title: '',
        num: 0,
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        zjList:[{
            'zjbm':1,
            'zjmc':'一次性折旧'
        },{
            'zjbm':2,
            'zjmc':'按月折旧'
        },{
            'zjbm':3,
            'zjmc':'按半年折旧'
        },{
            'zjbm':4,
            'zjmc':'按年折旧'
        }],
        them_tran: {},
        them: {
            '设备名称': 'sbmc',
            '设备规格': 'sbgg',
            '规格': 'sbgg',
            '设备单位': 'sbdw'
        },
        searchCon: [],
        selSearch: -1,
        wzkfList: wrapper.kfList,
        ghdwList: [],
        total: 0,
        ysbgfiles:null,
        sbsqsfiles:null,
        htfiles:null
    },
    methods: {
        getFile: function(event,index) {
            if(index==1){
                this.ysbgfiles = (event.target.files[0]);
            }else if(index==2){
                this.sbsqsfiles = (event.target.files[0]);
            }else if(index==3){
                this.zczfiles = (event.target.files[0]);
            }else{
                this.htfiles = (event.target.files[0]);
            }
        },
        setTime(event, val){
            this.popContent[val] = event.target.value
        },
        // 关闭
        closes: function () {
            this.num = 0;

        },
        open: function () {
            this.num = 1;
            // 设置库房
        },
        //保存
        save: function () {
            if (!this.empty_sub('contextInfo')) {
                return false;
            }
            if (pop.title == '添加设备') {
                for (var i = 0; i < wrapper.mxList.length; i++) {
                    if (wrapper.mxList[i].sbbm == pop.popContent.sbbm && wrapper.mxList[i].scph == pop.popContent.scph) {
                        malert("已有该批号的设备！", 'top', 'defeadted');
                        return;
                    }
                }
                var formData = new FormData();
                formData.append("ysbgfiles", this.ysbgfiles);
                formData.append("sbsqsfiles", this.sbsqsfiles);
                formData.append("htfiles", this.htfiles);
                formData.append("zczfiles", this.htfiles);

                this.$http.post('/actionDispatcher.do?reqUrl=New1FileUpload&types=uploadFileToFrom',formData).then(function (json) {
                    console.log(json.body.d);

                    for(var i =0;i<json.body.d.length;i++){
                        if(json.body.d[i].ysbgfiles != undefined && json.body.d[i].ysbgfiles != "undefined" ){
                            pop.popContent.ysbgfiles=json.body.d[i].ysbgfiles;
                            pop.popContent.ysbgname=json.body.d[i].fileName;
                            console.log(i+'ysbgfiles')
                        }else if(json.body.d[i].sbsqsfiles != undefined && json.body.d[i].sbsqsfiles != "undefined"){
                            pop.popContent.sbsqsfiles=json.body.d[i].sbsqsfiles;
                            pop.popContent.sbsqsname=json.body.d[i].fileName;
                            console.log(i+'sbsqsfiles')
                        }else if(json.body.d[i].htfiles != undefined && json.body.d[i].htfiles!="undefined"){
                            console.log(i+'htfiles')
                            pop.popContent.htfiles=json.body.d[i].htfiles;
                            pop.popContent.htname=json.body.d[i].fileName;
                        }
                        else if(json.body.d[i].zczfiles != undefined && json.body.d[i].zczfiles!="undefined"){
                            pop.popContent.zczfiles=json.body.d[i].zczfiles;
                            pop.popContent.htname=json.body.d[i].fileName;
                        }
                    }
                    console.log(pop.popContent);
                    if (json.a == 0 || json.body.a == 0) {
                        malert("保存成功！");
                        console.log(json);
                    } else {
                        malert("保存失败！", 'top', 'defeadted');
                        return
                    }
                    console.log(pop.popContent);
                    console.log(pop.popContent);
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                });
            }
            else if (pop.title == '编辑设备') {
                this.$set(wrapper.mxList,wrapper.selectIndex,this.popContent)
                pop.popContent = {};
                //编辑保存成功关闭弹窗
                pop.closes();
                return;
            }
        },
        //药品名称下拉table检索数据
        changeDown: function (event, type) {
            this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13 && this.popContent.sbbm) {
                $("#rksl").focus();
            }
        },
        //当输入值后才触发
        change: function (event, type, val) {
            this.popContent[type] = val;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var parm = {
                kfbm: wrapper.popContent.kfbm,
                page: pop.dg.page,
                rows: pop.dg.rows,
                parm: val
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglXtwhSbzd&types=query' +
                '&json=' + JSON.stringify(parm),
                function (data) {
                    pop.searchCon = data.d.list;
                    //wap.total = data.d.total;
                    pop.selSearch = 0;
                    $(".selectGroup").show();
                });
            if (wrapper.popContent["kfbm"] == undefined || wrapper.popContent["kfbm"] == null || wrapper.popContent["kfbm"] == "") {
                malert("库房不能为空", 'top', 'defeadted');
                return;
            }
            if (wrapper.popContent["rybm"] == undefined || wrapper.popContent["rybm"] == null || wrapper.popContent["rybm"] == "") {
                malert("采购人员不能为空", 'top', 'defeadted');
                return;
            }

        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                pop.dg.page++;
                var parm = {
                    page: pop.dg.page,
                    rows: pop.dg.rows,
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&json=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                pop.searchCon.push(data.d.list[i]);
                            }
                            pop.total = data.d.total;
                            pop.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }
            this.popContent = item;
            $(".selectGroup").hide();
        }

    }
});




laydate.render({
    elem: '.times1'
    , theme: '#1ab394',
    done: function (value, data) {
        pop.popContent.scrq = value
    }
});
laydate.render({
    elem: '.times2'
    , theme: '#1ab394',
    done: function (value, data) {
        pop.popContent.yxqz = value
    }
});
laydate.render({
    elem: '.times3'
    , theme: '#1ab394',
    done: function (value, data) {
        pop.popContent.qjrq = value
    }
});
laydate.render({
    elem: '.times4'
    , theme: '#1ab394',
    done: function (value, data) {
        pop.popContent.bxrq = value
    }
});
