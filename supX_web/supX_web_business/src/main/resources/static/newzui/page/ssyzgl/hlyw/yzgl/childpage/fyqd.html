    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>费用清单</title>
    <link rel="stylesheet" href="childpage/fyqd.css"/>
    <link href="childpage/yzd.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
<body>

<div id="fyqd" class="flex-container flex-dir-c flex-one">
    <div v-cloak class="brSearch printHide  flex-container">
        <div class="flex-container flex-align-c padd-r-10 padd-l-10">
            <span class="padd-r-5">性别</span>
            {{brxb_tran[json.brxb]}}
        </div>
        <div class="flex-container flex-align-c padd-r-10">
            <span class="padd-r-5">年龄</span>
           {{json.nl}}
        </div>
        <div class="flex-container flex-align-c padd-r-10">
            <span class="padd-r-5">床位</span>
            {{json.rycwbh}}
        </div>
        <div class="flex-container flex-align-c padd-r-10">
            <span class="padd-r-5">入院日期</span>
            {{fDate(json.ryrq,'date')}}
        </div>
        <div class="flex-container flex-align-c padd-r-10">
            <span class="padd-r-5">费别</span>
            {{json.brfbmc}}
        </div>
        <div class="flex-container flex-align-c padd-l-10 ">
            <span class=" whiteSpace font-14 padd-r-5">费用类别</span>
            <select-input @change-data="resultChangeData"  class="wh80"
                          :child="fylbsList" :index="'lbmc'" :index_val="'lbbm'" :val="popContent.lbbm"
                          :name="'popContent.lbbm'" :search="true" >
            </select-input>
            <span  class="padd-l-10 whiteSpace font-14 padd-r-5">排序方式</span>
            <select-input :cs="true" @change-data="pxfsFun" :not_empty="false"
                          :child="qdPxfs_tran" :index="popContent.pxfs" :val="popContent.pxfs"
                          :name="'popContent.pxfs'">
            </select-input>
          <div class="padd-l-10 flex-container flex-align-c">
              <vue-checkbox class="padd-r-5" @result="reCheckOne" :new-text="'本科'" :val="'bk'"  :new-value="bk"></vue-checkbox>
          </div>
            <div class="padd-l-10 flex-container flex-align-c">
                <button @click="openTy"  class="tong-btn btn-parmary margin-l-10 xmzb-db">退药</button>
                <button @click="openTf"  class="tong-btn btn-parmary margin-l-10 xmzb-db">退费</button>
                <button @click="openJZ"  class="tong-btn btn-parmary margin-l-10 xmzb-db">记账</button>
            </div>
        </div>
    </div>

    <div id="context" v-cloak class="padd-t-5 flex-container flex-dir-c flex-one over-auto">
        <div class="toolMenu printHide" style="display: block;">
            <article class="flex-container flex-align-c">
                <input class="zui-input wh112" data-select="no" id="timeVal" v-model="fyqdContent.ksrq1" type="text"/>
                <span class="padd-r-5 padd-l-5">至</span>
                <input class="zui-input wh112" data-select="no" id="timeVal1" v-model="fyqdContent.jsrq1" type="text"/>

            <button @click="getDataList"  class="tong-btn btn-parmary margin-l-10 xmzb-db">查询</button>
            <button @click="print"  class="tong-btn btn-parmary  xmzb-db">打印</button>
            <button @click="yzShow=true,getYzData()"  class="tong-btn btn-parmary  xmzb-db">医嘱</button>
            <button @click="openCpt"  class="tong-btn btn-parmary  xmzb-db">对帐清单</button>
                    <div class="flex flex_items ">
                        <label class="whiteSpace margin-r-5 ft-14">清单类型</label>
                        <select-input :cs="true" @change-data="resultChange" :not_empty="false"
                                      :child="fyqd_tran" :index="popContent.fyqd" :val="popContent.fyqd"
                                      :name="'popContent.fyqd'">
                        </select-input>
                    </div>
                <div class="flex flex_items " v-if="haveYe">
                    <label class="whiteSpace margin-r-5 ft-14">亲属选择</label>
                    <select-input class="wh120" @change-data="commonResultChange"
                                  :child="yeList" :index="'yexm'" :index_val="'yebh'" :val="popContent.yebh"
                                  :name="'popContent.yebh'" :index_mc="'yexm'" search="true" id="yebh">
                    </select-input>
                </div>
            </article>
        </div>

        <!--汇总清单-->
        <div id="hzqd" class="flex-container flex-jus-c">
            <div class="fyqdContext" v-if="popContent.fyqd == 0">
                <h2>{{yljgmc}}住院费用汇总清单</h2>
                <div class="flex-container flex-jus-c flex-align-c">
                    <span>发生时间：</span>
                    <span v-text="fyqdContent.ksrq1"></span>
                    <span>至</span>
                    <span v-text="fyqdContent.jsrq1"></span>
                </div>
                <div class="flex-container flex-align-c flex-jus-c flex-wrap-w">
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>住院号：</p>
                        <span v-text="fyqdContent.zyh"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>病员姓名：</p>
                        <span v-text="fyqdContent.brxm"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>住院科室：</p>
                        <span v-text="fyqdContent.ryksmc"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>住院天数：</p>
                        <span>{{fyqdContent.zyts}}天</span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>床位号：</p>
                        <span v-text="fyqdContent.rycwbh"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>入院日期：</p>
                        <span v-text="fDate(fyqdContent.ryrq,'date')"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20" >
                        <p >病区出院日期：</p>
                        <span v-text="fDate(fyqdContent.bqcyrq,'date')"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20" >
                        <p>费别：</p>
                        <span v-text="fyqdContent.brfbmc"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20" >
                        <p>费用合计：</p>
                        <span v-text="fDec(fyqdContent.fyhj,2)"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20"  >
                        <p>预交合计：</p>
                        <span v-text="fDec(fyqdContent.yjhj,2)"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20" >
                        <p>余额：</p>
                        <span v-text="fDec(fyqdContent.yjhj - fyqdContent.jzrqfyhj,2)"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>入院诊断：</p>
                        <span v-text="fyqdContent.ryzdmc"></span>
                    </div>
                    <div class="flex-container  wh100MAx  flex-align-c padd-r-20" >
                        <p>家庭地址：</p>
                        <span v-text="fyqdContent.jzdmc"></span>
                    </div>
                </div>

                <div class="fyqdTable">
                    <table v-if="csqxContent.N03004200261 != '1'" class="patientTable" cellspacing="0" cellpadding="0">
                        <tr>
                            <td style="width: 80px">费用编码</td>
                            <td style="width: 150px">费用名称</td>
                            <td style="width: 40px">规格</td>
<!--                            <td style="width: 30px">单位</td>-->
                            <td style="width: 50px">单价</td>
                            <td style="width: 40px">数量</td>
                            <td style="width: 80px">金额</td>
<!--                            <td style="width: 80px">类型</td>-->
                            <!--<td style="width: 80px">医保类别</td>-->
                            <td style="width: 80px">执行科室</td>
                        </tr>
                        <tbody v-for='(itemlist,index) in jsonList'>
                        <tr>
                            <td colspan="8"><span v-text="itemlist.fylbmc"></span><span v-text="fDec(itemlist.fyze,2) + '元'"></span></td>
                        </tr>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="switchIndex('hoverIndex',true,$index)" @click="switchIndex('activeIndex',true,$index)"
                            @mouseleave="switchIndex()"
                            v-for="(item,$index) in itemlist.fymx">
                            <td v-text="item.xmbm"></td>
                            <td v-text="item.xmmc"></td>
                            <td><div class="fygg" v-text="item.fygg"></div></td>
<!--                            <td></td>-->
                            <td v-text="fDec(item.fydj,3)"></td>
                            <td v-text="item.fysl"></td>
                            <td v-text="fDec(item.fyje,2)"></td>
<!--                            <td v-text="item.fylx">费用</td>-->
                            <!--<td v-text="item.tclbmc"></td>-->
                            <td v-text="item.zxksmc"></td>
                        </tr>
                        </tbody>
                    </table>

                    <table v-if="csqxContent.N03004200261 == '1'" class="patientTable N03004200261_table" cellspacing="0" cellpadding="0">
                        <tr>
                            <td style="width: 20%">费用名称</td>
                            <td style="width: 10%">单价</td>
                            <td style="width: 5%">数量</td>
                            <td style="width: 10%">金额</td>

                            <td style="width: 20%">费用名称</td>
                            <td style="width: 10%">单价</td>
                            <td style="width: 5%">数量</td>
                            <td style="width: 10%">金额</td>
                        </tr>
                        <tbody v-for='itemlist in jsonList'>
                        <tr>
                            <td colspan="8"><span v-text="itemlist.fylbmc"></span><span style="margin-right: 20px" v-text="fDec(itemlist.fyze,2) + '元'"></span></td>
                        </tr>
                        <tr v-for="item in itemlist.fymx">
                            <td style="width: 60px;overflow: hidden" v-text="item[0]?item[0].xmmc:''"></td>
                            <td v-text="item[0]?fDec(item[0].fydj,3):''"></td>
                            <td v-text="item[0]?item[0].fysl:''"></td>
                            <td v-text="item[0]?fDec(item[0].fyje,2):''"></td>
                            <td v-text="item[1]?item[1].xmmc:''"></td>
                            <td v-text="item[1]?fDec(item[1].fydj,3):''"></td>
                            <td v-text="item[1]?item[1].fysl:''"></td>
                            <td v-text="item[1]?fDec(item[1].fyje,2):''"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="total print-inside-avoid">
                    <div>
                        <span>合计：</span>
                        <span v-text="fDec(fyqdContent.lsfyhj,2)"></span>元
                    </div>
                    <div>
                        <span>四舍五入：</span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <!--明细清单-->
        <div id="mxqd" class="flex-container flex-jus-c">
            <div class="fyqdContext detailList" v-show="popContent.fyqd == 1">
                <h2>{{yljgmc}}住院费用明细清单（费用+药品）</h2>
                <div class="fyqdTime">
                    <span>发生时间：</span>
                    <span v-text="fyqdContent.ksrq1"></span>
                    <span>至</span>
                    <span v-text="fyqdContent.jsrq1"></span>
                </div>
                <div class="flex-container flex-align-c flex-jus-c flex-wrap-w">
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>住院号：</p>
                        <span v-text="fyqdContent.zyh"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>病员姓名：</p>
                        <span v-text="fyqdContent.brxm"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>住院科室：</p>
                        <span v-text="fyqdContent.ryksmc"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>床位号：</p>
                        <span v-text="fyqdContent.rycwbh"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p>入院日期：</p>
                        <span v-text="fDate(fyqdContent.ryrq,'date')"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20">
                        <p >病区出院日期：</p>
                        <span v-text="fDate(fyqdContent.bqcyrq,'date')"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20" >
                        <p>费别：</p>
                        <span v-text="fyqdContent.brfbmc"></span>
                    </div>
                    <div class="flex-container  wh25 flex-align-c padd-r-20" >
                        <p>费用合计：</p>
                        <span v-text="fDec(fyqdContent.fyhj,2)"></span>
                    </div>
                    <div class="flex-container  wh50ALL flex-align-c padd-r-20" >
                        <p>预交合计：</p>
                        <span v-text="fDec(fyqdContent.yjhj,2)"></span>
                    </div>
                    <div class="flex-container  wh50ALL flex-align-c padd-r-20" >
                        <p>余额：</p>
                        <span v-text="fDec(fyqdContent.yjhj - fyqdContent.jzrqfyhj,2)"></span>
                    </div>
                    <div class="flex-container  wh100MAx flex-align-c padd-r-20" >
                        <p>家庭地址：</p>
                        <span v-text="fyqdContent.jzdmc"></span>
                    </div>
                </div>
                <div class="fyqdTable">
                    <table class="patientTable" cellspacing="0" cellpadding="0">
                        <tr>
                            <td style="width: 100px">记帐时间</td>
                            <td style="width: 150px">项目名称</td>
                            <td style="width: 40px">规格</td>
                            <td style="width: 30px">数量</td>
                            <td style="width: 50px">单价</td>
                            <td style="width: 50px">金额</td>
                            <td style="width: 40px">医师</td>
                            <td style="width: 80px">费用科目</td>
<!--                            <td style="width: 80px">记帐科室</td>-->
                        </tr>
                        <!--<tbody v-for='itemlist in jsonMxList'>
                        <tr>
                            <td colspan="8"><span v-text="itemlist.fylbmc"></span><span v-text="fDec(itemlist.fyze,2) + '元'"></span></td>
                        </tr> -->
                        <tr v-for="item in jsonMxList">
                            <td v-text="fDate(item.djrq,'datetime')"></td>
                            <td v-text="item.xmmc"></td>
                             <td><div class="fygg" v-text="item.fygg"></div></td>
                            <td v-text="item.fysl"></td>
                            <td v-text="fDec(item.fydj,3)"></td>
                            <td v-text="fDec(item.fyje,2)"></td>
                            <td v-text="item.zyysxm"></td>
                            <td v-text="item.fylbmc"></td>
                        </tr>
                        <!-- </tbody> -->
                    </table>
                </div>
                <div class="total">
                    <div>
                        <span>合计：</span>
                        <span v-text="fDec(fyqdContent.lsfyhj,2)"></span>元
                    </div>
                    <div>
                        <span>四舍五入：</span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>

        <!--明细清单-->
        <div id="mxqdzxks">
            <div class="fyqdzxksContext detailList" v-show="popContent.fyqd == 2">
                <h2>{{yljgmc}}住院费用明细清单（费用+药品）</h2>
                <div class="fyqdTime">
                    <span>发生时间：</span>
                    <span v-text="fyqdContent.ksrq1"></span>
                    <span>至</span>
                    <span v-text="fyqdContent.jsrq1"></span>
                </div>
                <div class="infoIpt">
                    <p>住院号：</p>
                    <span v-text="fyqdContent.zyh"></span>
                </div>
                <div class="infoIpt">
                    <p>病员姓名：</p>
                    <span v-text="fyqdContent.brxm"></span>
                </div>
                <div class="infoIpt">
                    <p>住院科室：</p>
                    <span v-text="fyqdContent.ryksmc"></span>
                </div>
                <div class="infoIpt">
                    <p>床位号：</p>
                    <span v-text="fyqdContent.rycwbh"></span>
                </div>
                <div class="infoIpt">
                    <p>入院日期：</p>
                    <span v-text="fDate(fyqdContent.ryrq,'date')"></span>
                </div>
                <div class="infoIpt" style="width: 24%">
                    <p style="width: 120px;">病区出院日期：</p>
                    <span v-text="fDate(fyqdContent.bqcyrq,'date')"></span>
                </div>
                <div class="infoIpt">
                    <p>工作单位：</p>
                    <span></span>
                </div>
                <div class="infoIpt" style="width: 30%;">
                    <p>费别：</p>
                    <span v-text="fyqdContent.brfbmc"></span>
                </div>
                <div class="infoIpt" style="width: 100%">
                    <p>家庭地址：</p>
                    <span v-text="fyqdContent.jzdmc"></span>
                </div>
                <div class="infoIpt" style="width: 30%">
                    <p>费用合计：</p>
                    <span v-text="fDec(fyqdContent.fyhj,2)"></span>
                </div>
                <div class="infoIpt" style="width: 30%">
                    <p>预交合计：</p>
                    <span v-text="fDec(fyqdContent.yjhj,2)"></span>
                </div>
                <div class="infoIpt" style="width: 30%">
                    <p>余额：</p>
                    <span v-text="fDec(fyqdContent.yjhj - fyqdContent.jzrqfyhj,2)"></span>
                </div>
                <div class="fyqdTable">
                    <table class="patientTable" cellspacing="0" cellpadding="0">
                        <tr>
                            <td style="width: 100px">记帐时间</td>
                            <td style="width: 150px">项目名称</td>
                            <td style="width: 40px">规格</td>
                            <td style="width: 30px">数量</td>
                            <td style="width: 50px">单价</td>
                            <td style="width: 50px">金额</td>
                            <td style="width: 40px">医师</td>
                            <td style="width: 80px">费用科目</td>
                            <td style="width: 80px">审核人</td>
                            <td style="width: 80px">执行科室</td>
                            <td style="width: 80px">执行人</td>
                            <!--                            <td style="width: 80px">记帐科室</td>-->
                        </tr>
                        <!--<tbody v-for='itemlist in jsonMxList'>
                        <tr>
                            <td colspan="8"><span v-text="itemlist.fylbmc"></span><span v-text="fDec(itemlist.fyze,2) + '元'"></span></td>
                        </tr> -->
                        <tr v-for="item in jsonMxList">
                            <td v-text="fDate(item.djrq,'datetime')"></td>
                            <td v-text="item.xmmc"></td>
                             <td><div class="fygg" v-text="item.fygg"></div></td>
                            <td v-text="item.fysl"></td>
                            <td v-text="fDec(item.fydj,3)"></td>
                            <td v-text="fDec(item.fyje,2)"></td>
                            <td v-text="item.zyysxm"></td>
                            <td v-text="item.fylbmc"></td>
                            <td v-text="item.czyxm"></td>
                            <td v-text="item.zxksmc"></td>
                            <td v-text="item.kskfryxm"></td>
                        </tr>
                        <!-- </tbody> -->
                    </table>
                </div>
                <div class="total">
                <div>
                <span>合计：</span>
                <span v-text="fDec(fyqdContent.lsfyhj,2)"></span>元
                </div>
                <div>
                <span>四舍五入：</span>
                <span></span>
                </div>
                </div>
            </div>
        </div>



        <model :s="'保存'" :c="'关闭'" class="wjzDjpop" @default-click="yzShow=false" @result-clear="yzShow=false"
               :model-show="true" @result-close="errBr=false" v-if="yzShow" :title="title">
            <div class="bqcydj_model yz_model">
                <div class="flex-container flex-align-c flex-wrap-w">
                    <div @click="getYzData(1)" class="cursor padd-b-5" :class="{'yzd_select': yzlx==1}">长&nbsp;&nbsp;期</div>
                    <div @click="getYzData(0)" class="cursor padd-l-10 padd-b-5 padd-r-10" :class="{'yzd_select': yzlx=='0'}">临&nbsp;&nbsp;时</div>
                </div>
                <div  class="cqyzd printHide" v-show="yzlx=='1'">
                    <div class="yzdTitle" >{{yljgmc}}长期医嘱单</div>
                    <div class="yzd-table">
                        <table cellspacing="0" cellpadding="0">
                            <tbody>
                            <tr :class="{'goPrintHide': isGoPrint}">
                                <th colspan="2">开始</th>
                                <th rowspan="2">执行<br>时间</th>
                                <th rowspan="2" style="width: 320px">长期医嘱</th>
                                <th colspan="2">签名</th>
                                <th colspan="2">停止</th>
                                <th rowspan="2">停止<br>执行<br>时间</th>
                                <th colspan="2">签名</th>
                            </tr>
                            <tr :class="{'goPrintHide': isGoPrint}">
                                <th class="wh30">日<br>期</th>
                                <th class="wh30">时<br>间</th>
                                <th>医师</th>
                                <th>护士</th>
                                <th class="wh30">日<br>期</th>
                                <th class="wh30">时<br>间</th>
                                <th>医师</th>
                                <th>护士</th>
                            </tr>
                            <tr v-for="(item, $index) in yzJsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                                @click="goPrint($index)">
                                <td>
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index, 'ry') == 'line'" class="N03004200243-arrow-line"></span>{{sameDate('ksrq', $index, 'ry') == 'line'?'':sameDate('ksrq', $index, 'ry')}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index, 'ry') == 'line' && sameDate_arrow_head('ksrq', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td>
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index, 'sj') == 'line'" class="N03004200243-arrow-line"></span>{{sameDate('ksrq', $index, 'sj') == 'line'?'':sameDate('ksrq', $index, 'sj')}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index, 'sj') == 'line' && sameDate_arrow_head('ksrq', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td>
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('zxsj', $index, 'sj') == 'line'" class="N03004200243-arrow-line"></span>{{sameDate('zxsj', $index, 'sj') == 'line'?'':sameDate('zxsj', $index, 'sj')}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('zxsj', $index, 'sj') == 'line' && sameDate_arrow_head('zxsj', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td class="flex-container border-left-top-none">
                                    <span v-if="caqxContent.N03004200243 != '1'"  class="yzd-name" v-text="item.xmmc"></span>
                                    <span v-if="caqxContent.N03004200243 != '1'" :class="[{'sameStart': sameSE($index) == 'start'}, {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                                    <span v-if="caqxContent.N03004200243 == '1'" :class="[{'N03004200243-sameStart': sameSE($index) == 'start'}, {'N03004200243-sameEnd': sameSE($index) == 'end'},{'N03004200243-same': sameSE($index) == 'all'}]"></span>
                                    <div v-if="caqxContent.N03004200243 == '1'" style="width: 100%;height:  100%; ">
                                        <div v-if="!item.end || item.end != 'plus'" class="text-left N0300420024301">
                                            {{item.xmmc}}<span class="float-right" v-if="item.plusXsyl == 'xsyl'"><span v-if="item.ypbz=='1'">{{item.sl}}</span>&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                        </div>
                                        <div v-if="item.plusYyff == 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300420024302">
                                            <span v-if="item.ypbz == '1'"> 单次剂量：</span>{{item.dcjl}}&emsp;{{item.jldwmc}}
                                        </div>
                                        <div v-if="item.plusYyff != 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300420024302">
                                            <span v-if="item.ypbz == '1'"> 用法：</span>{{item.dcjl}}{{item.jldwmc}}&emsp;{{item.yyffmc}}&emsp;{{item.sysd?(item.sysd + ' ' + item.sysddw):''}}&emsp;{{item.pcmc}}<span class="text-right">{{item.yysm}}</span>
                                        </div>
                                        <div v-if="item.end && item.end == 'plus'" class="text-left N0300420024302">
                                            用法：{{item.psff}}{{item.ldmc}}&emsp;{{item.sysd?(item.sysd + ' ' + item.sysddw):''}}&emsp;{{item.pcmc}}
                                        </div>
                                    </div>
                                    <p v-if="caqxContent.N03004200243 != '1'">
                                        <span class="yzd-way" v-show="isShowItem($index)">{{item.psff}}{{item.ldmc}}</span>
                                        <span class="yzd-sm" v-show="isShowItem($index)" v-text="item.yysm"></span>
                                        <span v-if="is_csqx.show == '1'" class="yzd-sm"><span v-if="item.ypbz=='1'">{{item.sl}}</span>&ensp;{{item.yfdwmc}}</span>
                                    </p>
                                </td>
                                <td >
                                    <img v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '0'" :src="item.src" />
                                    <span v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '1'" >{{sameDate('ysqmxm', $index, 'name')}}</span>

                                    <!-- 宁蒗的 -->
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate_qm('ksrq', $index) == 'line'" class="N03004200243-arrow-line"></span>
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate_qm('ksrq', $index) == 'line' && sameDate_arrow_head_qm('ksrq', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td >
                                    <img v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '0'" :src="item.src" />
                                    <span v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '1'" >{{sameDate('shhsxm', $index, 'name')}}</span>
                                    <!--                        <span v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '1'" >{{sameDate('zxhsxm', $index, 'name')}}</span>-->

                                    <!-- 宁蒗的 -->
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate_qm('zxsj', $index) == 'line'" class="N03004200243-arrow-line"></span>
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate_qm('zxsj', $index) == 'line' && sameDate_arrow_head_qm('zxsj', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td >
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('ystzsj', $index, 'ry') == 'line'" class="N03004200243-arrow-line"></span>{{(is_csqx.N03003200139=='1' && sameDate('ystzsj', $index, 'ry')!= 'line')? sameDate('ystzsj', $index, 'ry'):''}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('ystzsj', $index, 'ry') == 'line' && sameDate_arrow_head('ystzsj', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td >
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('ystzsj', $index, 'sj') == 'line'" class="N03004200243-arrow-line"></span>{{(is_csqx.N03003200139=='1' && sameDate('ystzsj', $index, 'sj') != 'line')? sameDate('ystzsj', $index, 'sj'):''}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('ystzsj', $index, 'sj') == 'line' && sameDate_arrow_head('ystzsj', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td >
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('hstzsj', $index, 'sj') == 'line'" class="N03004200243-arrow-line"></span>{{(is_csqx.N03003200139=='1' && sameDate('hstzsj', $index, 'sj') != 'line')? sameDate('hstzsj', $index, 'sj'):''}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('hstzsj', $index, 'sj') == 'line' && sameDate_arrow_head('hstzsj', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td >
                                    <img v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '0'" :src="item.src" />
                                    <span v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '1'" >{{sameDate('tzysxm', $index, 'name')}}</span>

                                    <!-- 宁蒗的 -->
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate_qm('ystzsj', $index) == 'line'" class="N03004200243-arrow-line"></span>
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate_qm('ystzsj', $index) == 'line' && sameDate_arrow_head_qm('ystzsj', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                                <td>
                                    <img v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '0'" :src="item.src" />
                                    <span v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '1'" >{{sameDate('tzhsxm', $index, 'name')}}</span>

                                    <!-- 宁蒗的 -->
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate_qm('hstzsj', $index) == 'line'" class="N03004200243-arrow-line"></span>
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate_qm('hstzsj', $index) == 'line' && sameDate_arrow_head_qm('hstzsj', $index) == 'head'" class="N03004200243-arrow-head-cq"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div v-cloak class="lsyzd printHide" v-show="yzlx=='0'">
                    <div class="yzdTitle" >{{yljgmc}}临时医嘱单</div>
                    <div class="yzd-table">
                        <table cellspacing="0" cellpadding="0">
                            <tbody>
                            <tr :class="{'goPrintHide': isGoPrint}">
                                <th colspan="2">吩咐时间</th>
                                <th rowspan="2" style="width: 445px">临时医嘱</th>
                                <th rowspan="2">医师签名</th>
                                <th rowspan="2">执行<br>时间</th>
                                <th rowspan="2">执行者签名</th>
                            </tr>
                            <tr :class="{'goPrintHide': isGoPrint}">
                                <th>日<br>期</th>
                                <th>时<br>间</th>
                            </tr>
                            <tr v-for="(item, $index) in yzJsonList" :class="[{'tableTrSelect':isChecked == $index}, {'goPrintHide': isChecked > $index && isGoPrint}]"
                                @click="goPrint($index)">
                                <td>
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index, 'ry') == 'line'" class="N03004200243-arrow-line"></span>{{sameDate('ksrq', $index, 'ry')=='line'?'':sameDate('ksrq', $index,'ry')}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index,'ry') == 'line' && sameDate_arrow_head('ksrq', $index,index) == 'head'" class="N03004200243-arrow-head-ls-ksrq"></div>
                                </td>
                                <td>
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index,'sj') == 'line'" class="N03004200243-arrow-line"></span>{{sameDate('ksrq', $index,'sj')=='line'?'':sameDate('ksrq', $index, 'sj')}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('ksrq', $index,'sj') == 'line' && sameDate_arrow_head('ksrq', $index,index) == 'head'" class="N03004200243-arrow-head-ls-ksrq"></div>
                                </td>
                                <td class="flex-container border-left-top-none">
                                    <span v-if="caqxContent.N03004200243 != '1'" class="yzd-name" v-text="item.xmmc"></span>
                                    <span v-if="caqxContent.N03004200243 != '1'" :class="[{'sameStart': sameSE($index) == 'start'},  {'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                                    <span v-if="caqxContent.N03004200243 == '1'" :class="[{'N03004200243-sameStart': sameSE($index) == 'start'},  {'N03004200243-sameEnd': sameSE($index) == 'end'},{'N03004200243-same': sameSE($index) == 'all'}]"></span>
                                    <div v-if="caqxContent.N03004200243 == '1'" style="width: 100%;height:  100%; ">
                                        <div v-if="(!item.end || item.end != 'plus') && item.doubleLine != '1'" class="text-left N0300420024301">
                                            {{item.xmmc}}<span v-if="is_csqx.show == '1'" class="yzd-sm"><span v-if="item.ypbz=='1'">{{item.sl}}</span>&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                        </div>
                                        <div v-if="(!item.end || item.end != 'plus') && item.doubleLine == '1'" class="text-left N0300420024303">
                                            {{item.xmmc}}<span v-if="is_csqx.show == '1'" class="yzd-sm">{{item.sl}}&ensp;{{item.yfdwmc}}&nbsp;&nbsp;</span>
                                        </div>
                                        <div v-if="item.plusYyff == 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300420024302">
                                            <span v-if="item.ypbz == '1'"> 单次剂量：</span>{{item.dcjl}}&emsp;{{item.jldwmc}}
                                        </div>
                                        <div v-if="item.plusYyff != 'dcjl' && (!item.end || item.end != 'plus')" class="text-left N0300420024302">
                                            <span v-if="item.ypbz == '1'">用法：</span><span v-if="item.sfcy != '1'">{{item.dcjl}}{{item.jldwmc}}&emsp;</span>{{item.yyffmc}}&emsp;{{item.sysd?(item.sysd + ' ' + item.sysddw):''}}&emsp;{{item.pcmc}}<span class="text-right">{{item.yysm}}</span>
                                            <span class="padd-l-20" style="width: auto" v-show="item.psjg != '无'">(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                        </div>
                                        <div v-if="item.end && item.end == 'plus'" class="text-left N0300420024302">
                                            用法：{{item.psff}}{{item.ldmc}}&emsp;{{item.sysd?(item.sysd + ' ' + item.sysddw):''}}&emsp;{{item.pcmc}}
                                        </div>
                                    </div>
                                    <p v-if="caqxContent.N03004200243 != '1'">
                                        <span class="yzd-way"  >{{item.yyffmc}}<!--{{item.ldmc}}--></span>
                                        <span class="padd-l-20" style="width: auto" v-show="item.psjg != '无'">(&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;)</span>
                                        <span class="yzd-sm"  v-text="item.yysm"></span>
                                        <span v-if="is_csqx.show == '1'" class="yzd-sm"><span v-if="item.ypbz=='1'">{{item.sl}}</span>&ensp;{{item.yfdwmc}}</span>
                                    </p>
                                </td>
                                <td>
                                    <img v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '0'" :src="item.src" />
                                    <span v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '1'" >{{sameDate('ysqmxm', $index, 'name')}}</span>
                                    <!-- 宁蒗的 -->
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate_qm('ksrq', $index, index) == 'line'" class="N03004200243-arrow-line"></span>
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate_qm('ksrq', $index, index) == 'line' && sameDate_arrow_head_qm('ksrq', $index,index) == 'head'" class="N03004200243-arrow-head-lszx"></div>
                                </td>
                                <td>
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate('zxsj', $index, 'dTime') == 'line'" class="N03004200243-arrow-line"></span>{{(is_csqx.N03003200139=='1' && sameDate('zxsj', $index, 'dTime') != 'line')? sameDate('zxsj', $index, 'dTime'):''}}
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate('zxsj', $index, 'dTime') == 'line' && sameDate_arrow_head('zxsj', $index,index) == 'head'" class="N03004200243-arrow-head-lszx"></div>
                                </td>
                                <td >
                                    <img v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '0'" :src="item.src" />
                                    <span v-if="caqxContent.N03004200243 != '1' && is_csqx.N03003200125 == '1'" >{{sameDate('shhsxm', $index, 'name')}}</span>
                                    <span v-if="caqxContent.N03004200243 == '1' && sameDate_qm('zxsj', $index, index) == 'line'" class="N03004200243-arrow-line"></span>
                                    <div v-if="caqxContent.N03004200243 == '1' && sameDate_qm('zxsj', $index, index) == 'line' && sameDate_arrow_head_qm('zxsj', $index,index) == 'head'" class="N03004200243-arrow-head-lszx"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </model>
    </div>
</div>
</body>
<script type="text/javascript" src="childpage/fyqd.js"></script>
