var batch = new Vue({
	el: '#batch',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	data: {
		num: 0,
		popContent: {},
		twbList: [], // 体温表list
		twbqtjlList: [], // 其他List
		parm: {
			clsd: '1'
		},
		twsd_tran: {
			'1': '2',
			'2': '6',
			'3': '10',
			'4': '14',
			'5': '18',
			'6': '22'
		},
		twbw: '1', // 体温部位
		xyType:'1', // 1上午 2下午
		ttpg_tran: {
		    "无": "无",
		    "轻度": "轻度",
		    "中度": "中度",
			"重度": "重度"
		},
	},
	created: function() {
		this.parm.clrq = this.$options.filters['formDate'](new Date());
		this.getTwData();
	},
	methods: {
		yjfz:function(){
			if(this.num == 1){
				let tp = JSON.parse(JSON.stringify(batch.twbqtjlList[0]));
				for (let i = 1; i < batch.twbqtjlList.length; i++) {
					batch.twbqtjlList[i].xbl = tp.xbl
					batch.twbqtjlList[i].xbs = tp.xbs
					batch.twbqtjlList[i].bldn = tp.bldn
					batch.twbqtjlList[i].dbcs = tp.dbcs
					batch.twbqtjlList[i].dbs = tp.dbs
					batch.twbqtjlList[i].dbz = tp.dbz
					batch.twbqtjlList[i].dbl = tp.dbl
					batch.twbqtjlList[i].gcqdbcs = tp.gcqdbcs
					batch.twbqtjlList[i].gc = tp.gc
					batch.twbqtjlList[i].yll = tp.yll
					batch.twbqtjlList[i].qtcl = tp.qtcl
					batch.twbqtjlList[i].yrl = tp.yrl
					batch.twbqtjlList[i].srl = tp.srl
					batch.twbqtjlList[i].swSsy = tp.swSsy
					batch.twbqtjlList[i].swSzy = tp.swSzy
					batch.twbqtjlList[i].xwSsy = tp.xwSsy
					batch.twbqtjlList[i].xwSzy = tp.xwSzy
					batch.twbqtjlList[i].tz = tp.tz
					batch.twbqtjlList[i].sg = tp.sg
					batch.twbqtjlList[i].mx = tp.mx
					batch.twbqtjlList[i].st = tp.st
					batch.twbqtjlList[i].sz = tp.sz
					batch.twbqtjlList[i].dxk = tp.dxk
					batch.twbqtjlList[i].jcdx = tp.jcdx
					batch.twbqtjlList[i].qtxm1 = tp.qtxm1
					batch.twbqtjlList[i].qtxm2 = tp.qtxm2
					batch.twbqtjlList[i].qtxm3 = tp.qtxm3
					batch.twbqtjlList[i].qtxm4 = tp.qtxm4
				}
			}else{
				let tp = JSON.parse(JSON.stringify(batch.twbList[0]));
				for (let i = 1; i < batch.twbList.length; i++) {
					batch.twbList[i].tw = tp.tw
					batch.twbList[i].twbw = tp.twbw
					batch.twbList[i].xt = tp.xt
					batch.twbList[i].xtqbq = tp.xtqbq
					batch.twbList[i].mb = tp.mb
					batch.twbList[i].rgfx = tp.rgfx
					batch.twbList[i].fx = tp.fx
					batch.twbList[i].wcyy = tp.wcyy
					batch.twbList[i].xbc = tp.xbc
					batch.twbList[i].xbl = tp.xbl
					batch.twbList[i].xbs = tp.xbs
					batch.twbList[i].bldn = tp.bldn
					batch.twbList[i].dbcs = tp.dbcs
					batch.twbList[i].dbs = tp.dbs
					batch.twbList[i].dbz = tp.dbz
					batch.twbList[i].dbl = tp.dbl
					batch.twbList[i].ssy = tp.ssy
					batch.twbList[i].szy = tp.szy
					batch.twbList[i].ttpgs = tp.ttpgs
					batch.twbList[i].ttpgx = tp.ttpgx
				}
			}
			this.$forceUpdate();
		},
		yjqk:function(){
			if(this.num == 1){
				for (let i = 0; i < batch.twbqtjlList.length; i++) {
					batch.twbqtjlList[i].xbl = ''
					batch.twbqtjlList[i].xbs = ''
					batch.twbqtjlList[i].bldn = ''
					batch.twbqtjlList[i].dbcs = ''
					batch.twbqtjlList[i].dbs = ''
					batch.twbqtjlList[i].dbz = ''
					batch.twbqtjlList[i].dbl = ''
					batch.twbqtjlList[i].gcqdbcs = ''
					batch.twbqtjlList[i].gc = ''
					batch.twbqtjlList[i].yll = ''
					batch.twbqtjlList[i].qtcl = ''
					batch.twbqtjlList[i].yrl = ''
					batch.twbqtjlList[i].srl = ''
					batch.twbqtjlList[i].swSsy = ''
					batch.twbqtjlList[i].swSzy = ''
					batch.twbqtjlList[i].xwSsy = ''
					batch.twbqtjlList[i].xwSzy = ''
					batch.twbqtjlList[i].tz = ''
					batch.twbqtjlList[i].sg = ''
					batch.twbqtjlList[i].mx = ''
					batch.twbqtjlList[i].st = ''
					batch.twbqtjlList[i].sz = ''
					batch.twbqtjlList[i].dxk = ''
					batch.twbqtjlList[i].jcdx = ''
					batch.twbqtjlList[i].qtxm1 = ''
					batch.twbqtjlList[i].qtxm2 = ''
					batch.twbqtjlList[i].qtxm3 = ''
					batch.twbqtjlList[i].qtxm4 = ''
				}
			}else{
				for (let i = 0; i < batch.twbList.length; i++) {
					batch.twbList[i].tw = ''
					batch.twbList[i].twbw = ''
					batch.twbList[i].xt = ''
					batch.twbList[i].xtqbq = ''
					batch.twbList[i].mb = ''
					batch.twbList[i].rgfx = ''
					batch.twbList[i].fx = ''
					batch.twbList[i].wcyy = ''
					batch.twbList[i].xbc = ''
					batch.twbList[i].xbl = ''
					batch.twbList[i].xbs = ''
					batch.twbList[i].bldn = ''
					batch.twbList[i].dbcs = ''
					batch.twbList[i].dbs = ''
					batch.twbList[i].dbz = ''
					batch.twbList[i].dbl = ''
					batch.twbList[i].ssy = ''
					batch.twbList[i].szy = ''
					batch.twbList[i].ttpgs = ''
					batch.twbList[i].ttpgx = ''
				}
			}
			this.$forceUpdate();
		},
		setNldw:function (value) {
			return Number.isInteger(parseInt(value)) ? this.nldw_tran[value] : value
		},
		// jq控件取值转化为vue取值   event: 当前对象 name: 要赋值的名字（String）
		dateVal: function (event, name,index) {
			this.$nextTick(function () {
				Vue.set(this.twbList[index], name, event.target.value);
			})
		},
		showTime: function() {
			var _laydate = {
				elem: '.time',
				show: true //直接显示
				,type: 'date',
				theme: '#1ab394',
				done: function(value, data) {
					batch.parm.clrq = value;
					batch.getTwData();
				},
			};
			laydate.render(_laydate) //初始化时间插件
		},
		showTime1: function(index) {
			var el='.tbsmsj'+index;
			var _laydate = {
				elem: el,
				show: true //直接显示
				,type: 'time',
				theme: '#1ab394',
				done: function(value, data) {
					batch['twbList'][index]['tbsmsj'] = new Date(batch.parm.clrq + ' ' +value).getTime();
				},
			};
			laydate.render(_laydate) //初始化时间插件
		},
		tabBg: function(index) {
			this.num = index
		},
		resultChangeData: function(val) {
			this.parm.clsd = val[0];
			batch.getTwData();
		},
		changeData:function(val,index){
			batch.twbList[index][val[2][1]] = val[0];
			if(this.num==0){
				this.nextSelect(val[1],1);
			}else{
				this.nextSelect(val[1],1);
			}
		},
		changeQtData:function(val,index){
			batch.twbqtjlList[index][val[2][1]] = val[0];
			this.nextFocus(event,37);
		},
		setTwbw: function(val) {
			this.twbw = val[0];
			for (var i = 0; i < batch.twbList.length; i++) {
				batch.twbList[i]['twbw'] = val[0];
			}
		},
		// 获取科室时段体温信息
		getTwData: function() {
			this.parm.ryksbm = yzclLeft.jsContent.ksbm;
			this.parm.ryksmc = yzclLeft.kslist[yzclLeft.jsContent.ksbm];
			// this.parm.sort1 = '1';
			// this.parm.order2 = '1';
			$.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryByKssd&parm=" + JSON.stringify(this.parm), function(json) {
					if (json.a == "0" && json.d) {
						batch.twbList = json.d.twbList;
                        batch.twbList = batch.filterSort(json.d.twbList,'s','xssx');
                        batch.twbqtjlList = batch.filterSort(json.d.twbqtjlList,'s','xssx');

                        for (var int = 0; int < batch.twbList.length; int++) {
							if(!batch.twbList[int].twbw){
								batch.twbList[int].twbw = batch.twbw;
							}
							// 简单录入模式 （将大便和小便，血压录入放在体温录入界面） start
							if(batch.twbqtjlList[int]){
								batch.twbList[int].xbc = batch.twbqtjlList[int].xbc;
								batch.twbList[int].xbl = batch.twbqtjlList[int].xbl;
								batch.twbList[int].xbs = batch.twbqtjlList[int].xbs;
								batch.twbList[int].bldn = batch.twbqtjlList[int].bldn;

								batch.twbList[int].dbcs = batch.twbqtjlList[int].dbcs;
								batch.twbList[int].dbs = batch.twbqtjlList[int].dbs;
								batch.twbList[int].dbz = batch.twbqtjlList[int].dbz;
								batch.twbList[int].dbl = batch.twbqtjlList[int].dbl;

								// 自动判断是上午血压还是下午血压
								if(batch.parm.clsd == '1' || batch.parm.clsd == '2' || batch.parm.clsd == '3'){
									batch.twbList[int].ssy = batch.twbqtjlList[int].swSsy;
									batch.twbList[int].szy = batch.twbqtjlList[int].swSzy;

									batch.xyType = '1';
								}else{
									batch.twbList[int].ssy = batch.twbqtjlList[int].xwSsy;
									batch.twbList[int].szy = batch.twbqtjlList[int].xwSzy;

									batch.xyType = '2';
								}

								batch.twbList[int].ttpgs = batch.twbqtjlList[int].ttpgs;
								batch.twbList[int].ttpgx = batch.twbqtjlList[int].ttpgx;
							}
							// 简单录入模式 end

						}
						console.log(batch.twbList)
					}
				})
		},
		saveData: function() {
			// @yqq判断是否包含 时段
			var type = false;
			batch.twbList.forEach(obj => {
				if(!obj.clsd)
					type = true;
			});
			if(type){
				malert('时段为空，请刷新页面后重试！', "top", "defeadted");
				return;
			}
			var json = [];
			if (this.num == '0') { // 体温
				for (var i = 0; i < batch.twbList.length; i++) {
					batch.twbList[i].clrq = this.$options.filters['formDate'](batch.parm.clrq);
					batch.twbList[i].ryksbm = this.parm.ryksbm;
					batch.twbList[i].ryksmc = this.parm.ryksmc;
					// batch.twbList[i].tbsmsj = new Date(batch.twbList[i].clrq + ' ' +batch.twbList[i].tbsmsj).getTime();
					batch.twbList[i].clrq = this.parm.clrq;
					batch.twbList[i].clsd = this.parm.clsd;

					// 简单模式下
					if(true){
						batch.twbqtjlList[i].ryksbm = this.parm.ryksbm;
						batch.twbqtjlList[i].ryksmc = this.parm.ryksmc;
						batch.twbqtjlList[i].clrq = this.parm.clrq;
						batch.twbqtjlList[i].clsd = this.parm.clsd;

						batch.twbqtjlList[i].xbc = batch.twbList[i].xbc;
						batch.twbqtjlList[i].xbl = batch.twbList[i].xbl;
						batch.twbqtjlList[i].xbs = batch.twbList[i].xbs;
						batch.twbqtjlList[i].bldn = batch.twbList[i].bldn;
						batch.twbqtjlList[i].dbcs = batch.twbList[i].dbcs;
						batch.twbqtjlList[i].dbs = batch.twbList[i].dbs;
						batch.twbqtjlList[i].dbz = batch.twbList[i].dbz;
						batch.twbqtjlList[i].dbl = batch.twbList[i].dbl;

						// 自动判断是上午血压还是下午血压
						if(batch.parm.clsd == '1' || batch.parm.clsd == '2' || batch.parm.clsd == '3'){
							batch.twbqtjlList[i].swSsy = batch.twbList[i].ssy;
							batch.twbqtjlList[i].swSzy = batch.twbList[i].szy;
						}else{
							batch.twbqtjlList[i].xwSsy = batch.twbList[i].ssy;
							batch.twbqtjlList[i].xwSzy = batch.twbList[i].szy;
						}
						batch.twbqtjlList[i].ttpgs = batch.twbList[i].ttpgs;
						batch.twbqtjlList[i].ttpgx = batch.twbList[i].ttpgx;
					}


					var obj = {
						twbList: [batch.twbList[i]],
						twbqtjlModel: batch.twbqtjlList[i]
					};
					json.push(obj);
				}
			}else if (this.num == '1') { // 体温其他
				for (var i = 0; i < batch.twbqtjlList.length; i++) {
					batch.twbqtjlList[i].ryksbm = this.parm.ryksbm;
					batch.twbqtjlList[i].ryksmc = this.parm.ryksmc;
					batch.twbqtjlList[i].clrq = this.parm.clrq;
					batch.twbqtjlList[i].clsd = this.parm.clsd;
					
					
					batch.twbList[i].xbl = batch.twbqtjlList[i].xbl
					batch.twbList[i].xbs = batch.twbqtjlList[i].xbs
					batch.twbList[i].bldn = batch.twbqtjlList[i].bldn
					batch.twbList[i].dbcs = batch.twbqtjlList[i].dbcs
					batch.twbList[i].dbs = batch.twbqtjlList[i].dbs
					batch.twbList[i].dbz = batch.twbqtjlList[i].dbz
					batch.twbList[i].dbl = batch.twbqtjlList[i].dbl
					batch.twbList[i].gcqdbcs = batch.twbqtjlList[i].gcqdbcs
					batch.twbList[i].gc = batch.twbqtjlList[i].gc
					batch.twbList[i].yll = batch.twbqtjlList[i].yll
					batch.twbList[i].qtcl = batch.twbqtjlList[i].qtcl
					batch.twbList[i].yrl = batch.twbqtjlList[i].yrl
					batch.twbList[i].srl = batch.twbqtjlList[i].srl
					batch.twbList[i].swSsy = batch.twbqtjlList[i].swSsy
					batch.twbList[i].swSzy = batch.twbqtjlList[i].swSzy
					batch.twbList[i].xwSsy = batch.twbqtjlList[i].xwSsy
					batch.twbList[i].xwSzy = batch.twbqtjlList[i].xwSzy
					batch.twbList[i].tz = batch.twbqtjlList[i].tz
					batch.twbList[i].sg = batch.twbqtjlList[i].sg
					batch.twbList[i].mx = batch.twbqtjlList[i].mx
					batch.twbList[i].st = batch.twbqtjlList[i].st
					batch.twbList[i].sz = batch.twbqtjlList[i].sz
					batch.twbList[i].dxk = batch.twbqtjlList[i].dxk
					batch.twbList[i].jcdx = batch.twbqtjlList[i].jcdx
					batch.twbList[i].qtxm1 = batch.twbqtjlList[i].qtxm1
					batch.twbList[i].qtxm2 = batch.twbqtjlList[i].qtxm2
					batch.twbList[i].qtxm3 = batch.twbqtjlList[i].qtxm3
					batch.twbList[i].qtxm4 = batch.twbqtjlList[i].qtxm4
					
					var obj = {
						twbList: [batch.twbList[i]],
						twbqtjlModel: batch.twbqtjlList[i]
					};
					json.push(obj);
				}
			}

			var par = '{"list":' + JSON.stringify(json) + '}';
			this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=saveBatch', par).then(function(data) {
				if (data.body.a == 0) {
					malert("保存成功！", "top", "success");
					batch.getTwData();
				} else {
					malert(data.body.c, "top", "defeadted");
				}
			}, function(error) {
				console.log(error);
			});
		}
	},
})

