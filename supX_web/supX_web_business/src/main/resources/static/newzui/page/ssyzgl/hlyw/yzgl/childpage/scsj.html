<title>三测数据</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" href="/pub/css/print.css" media="print"/>
<link href="childpage/scsj.css" rel="stylesheet" type="text/css"/>

<div class="toolMenu printHide margin-b-20 margin-t-10">
    <button v-waves @click="getData" class="tong-btn btn-parmary  xmzb-db">刷新</button>
    <button v-waves @click="save" class="tong-btn btn-parmary  xmzb-db">保存</button>
    <button v-waves @click="print" class="tong-btn btn-parmary  xmzb-db">打印</button>
    <button v-waves @click="prev()" class="tong-btn btn-parmary  xmzb-db">上一页</button>
    <button v-waves @click="next()" class="tong-btn btn-parmary  xmzb-db">下一页</button>
    <button v-waves @click="openSetTwd()" class="tong-btn btn-parmary  xmzb-db">批量录入体温单</button>
</div>
<div class="enter_tem1  flex-one flex-container flex-dir-c" id="twdList">
    <tabs class="printHide" :num="which" :tab-child="[{'text':'测量数据录入'},{'text':'体温单'}]"   @tab-active="loadCon" ></tabs>
    <div class=" printHide height-36 flex-container flex-align-c margin-b-10">
        <div class="flex-container flex-align-c"  v-show="which==0">
            <span class="margin-r-10">时间</span>
            <input class="zui-input wh180" type="text" v-model="cxrq" readonly="readonly"  id="time1">
            <input class="zui-input wh180" type="hidden" v-model="clrq" readonly="readonly"  id="time">
        </div>
        <div class="flex-container flex-align-c" v-show="which==1">
            <span class="margin-r-10">时间</span>
            <input class="zui-input wh180"  type="text" v-model="cxrq1" data-select="no"  id="time2">
        </div>
        <div class="flex-container padd-l-10  flex-align-c">
            <span class="margin-r-10 whiteSpace">体温单筛选</span>
            <select-input  class="wh120" @change-data="commonResultChange"
                          :child="qsxzList" :index="'yexm'" :index_val="'yebh'" :val="popContent.yebh"
                          :name="'popContent.yebh'" :index_mc="'yexm'" search="true" >
            </select-input>
        </div>
        <div class="flex-container padd-l-10 flex-align-c">
            <span>姓名：</span>
            <p v-text="brxxContent.brxm"></p>
        </div>
        <div class="flex-container padd-l-10 flex-align-c">
            <span>性别：</span>
            <p v-text="brxb_tran[brxxContent.brxb]"></p>
        </div>
        <div class="flex-container padd-l-10 flex-align-c">
            <span>年齡：</span>
            <p v-text="brxxContent.nl + '' + nldw_tran[brxxContent.nldw]"></p>
            <p v-text="brxxContent.nl2?(brxxContent.nl2 + '' + nldw_tran[brxxContent.nldw2]):''"></p>
        </div>
        <div class="flex-container padd-l-10 flex-align-c">
            <span>床位号：</span>
            <p v-text="brxxContent.rycwbh"></p>
        </div>
        <div class="flex-container padd-l-10 flex-align-c">
            <span>住院号：</span>
            <p v-text="brxxContent.zyh"></p>
        </div>
    </div>
    <div class="fyxm-size printHide flex-one over-auto" v-show="which==0">
        <div class="dataEnter">
            <table>
                <tr>
                    <th>时段</th>
                    <th>2<input type="hidden" v-model="oneTwd.clsd"></th>
                    <th>6<input type="hidden" v-model="twoTwd.clsd"></th>
                    <th>10<input type="hidden" v-model="threeTwd.clsd"></th>
                    <th>14<input type="hidden" v-model="fourTwd.clsd"></th>
                    <th>18<input type="hidden" v-model="fiveTwd.clsd"></th>
                    <th>22<input type="hidden" v-model="sixTwd.clsd"></th>
                </tr>
                <tr>
                    <th>体温部位</th>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="false"
                                      :child="twbw_tran" :index="oneTwd.twbw" :val="oneTwd.twbw"
                                      :name="'oneTwd.twbw'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="twbw_tran" :index="twoTwd.twbw" :val="twoTwd.twbw"
                                      :name="'twoTwd.twbw'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="twbw_tran" :index="threeTwd.twbw" :val="threeTwd.twbw"
                                      :name="'threeTwd.twbw'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="twbw_tran" :index="fourTwd.twbw" :val="fourTwd.twbw"
                                      :name="'fourTwd.twbw'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="twbw_tran" :index="fiveTwd.twbw" :val="fiveTwd.twbw"
                                      :name="'fiveTwd.twbw'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="twbw_tran" :index="sixTwd.twbw" :val="sixTwd.twbw"
                                      :name="'sixTwd.twbw'">
                        </select-input>
                    </td>
                </tr>
                <tr>
                    <th>体温</th>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="oneTwd.tw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="twoTwd.tw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="threeTwd.tw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fourTwd.tw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fiveTwd.tw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="sixTwd.tw"></td>
                </tr>
				<tr>
				    <th>脉搏</th>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="oneTwd.mb"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="twoTwd.mb"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="threeTwd.mb"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fourTwd.mb"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fiveTwd.mb"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="sixTwd.mb"></td>
				</tr>
				<tr>
				    <th>呼吸</th>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="oneTwd.fx"></td>
				    <td><input  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="twoTwd.fx"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="threeTwd.fx"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fourTwd.fx"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fiveTwd.fx"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="sixTwd.fx"></td>
				</tr>
				<tr>
				    <th>心率</th>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="oneTwd.xt"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="twoTwd.xt"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="threeTwd.xt"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fourTwd.xt"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fiveTwd.xt"></td>
				    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="sixTwd.xt"></td>
				</tr>
                <tr>
                    <th>物理降温</th>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="oneTwd.wljw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="twoTwd.wljw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="threeTwd.wljw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fourTwd.wljw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fiveTwd.wljw"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="sixTwd.wljw"></td>
                </tr>
                <tr>
                    <th>起搏器</th>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="oneTwd.xtqbq" :val="oneTwd.xtqbq"
                                      :name="'oneTwd.xtqbq'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="twoTwd.twbw" :val="twoTwd.xtqbq"
                                      :name="'twoTwd.xtqbq'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="threeTwd.xtqbq" :val="threeTwd.xtqbq"
                                      :name="'threeTwd.xtqbq'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="fourTwd.xtqbq" :val="fourTwd.xtqbq"
                                      :name="'fourTwd.xtqbq'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="fiveTwd.xtqbq" :val="fiveTwd.xtqbq"
                                      :name="'fiveTwd.xtqbq'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="sixTwd.xtqbq" :val="sixTwd.xtqbq"
                                      :name="'sixTwd.xtqbq'">
                        </select-input>
                    </td>
                </tr>
                
                <tr>
                    <th>机械通气</th>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="oneTwd.rgfx" :val="oneTwd.rgfx"
                                      :name="'oneTwd.rgfx'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="twoTwd.rgfx" :val="twoTwd.rgfx"
                                      :name="'twoTwd.rgfx'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="threeTwd.rgfx" :val="threeTwd.rgfx"
                                      :name="'threeTwd.rgfx'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="fourTwd.rgfx" :val="fourTwd.rgfx"
                                      :name="'fourTwd.rgfx'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="fiveTwd.rgfx" :val="fiveTwd.rgfx"
                                      :name="'fiveTwd.rgfx'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="istrue_tran" :index="sixTwd.rgfx" :val="sixTwd.rgfx"
                                      :name="'sixTwd.rgfx'">
                        </select-input>
                    </td>
                </tr>
                <!-- <tr v-if="csqxContent.N03004200231 == '1'">
                    <th>氧饱和度（%）</th>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="oneTwd.ybhd"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="twoTwd.ybhd"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="threeTwd.ybhd"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fourTwd.ybhd"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="fiveTwd.ybhd"></td>
                    <td><input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,6)" class="zui-input" type="number" v-model="sixTwd.ybhd"></td>
                </tr>-->
                <tr>
                    <th class="color-wtg">特别说明</th>
                    <td>
                        <select-input :search="true" @changevalue="resultChangeMx" @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="tbsm_tran" :index="oneTwd.tbsm" :val="oneTwd.tbsm"
                                      :name="'oneTwd.tbsm'">
                        </select-input>
                    </td>
                    <td>
                        <select-input :search="true" @changevalue="resultChangeMx" @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="tbsm_tran" :index="twoTwd.tbsm" :val="twoTwd.tbsm"
                                      :name="'twoTwd.tbsm'">
                        </select-input>
                    </td>
                    <td>
                        <select-input :search="true" @changevalue="resultChangeMx" @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="tbsm_tran" :index="threeTwd.tbsm" :val="threeTwd.tbsm"
                                      :name="'threeTwd.tbsm'">
                        </select-input>
                    </td>
                    <td>
                        <select-input :search="true" @changevalue="resultChangeMx" @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="tbsm_tran" :index="fourTwd.tbsm" :val="fourTwd.tbsm"
                                      :name="'fourTwd.tbsm'">
                        </select-input>
                    </td>
                    <td>
                        <select-input :search="true" @changevalue="resultChangeMx" @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="tbsm_tran" :index="fiveTwd.tbsm" :val="fiveTwd.tbsm"
                                      :name="'fiveTwd.tbsm'">
                        </select-input>
                    </td>
                    <td>
                        <select-input :search="true" @changevalue="resultChangeMx" @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="tbsm_tran" :index="sixTwd.tbsm" :val="sixTwd.tbsm"
                                      :name="'sixTwd.tbsm'">
                        </select-input>
                    </td>
                </tr>
                <tr>
                    <th>发生时间</th>
                    <td><input @keydown="nextFocus($event,6)" class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })" v-model="tbsmsj"
                               @blur="dateForVal($event, 'tbsmsj')"
                               id="oneTime"></td>
                    <td><input @keydown="nextFocus($event,6)" class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })" v-model="tbsmsj1"
                               @blur="dateForVal($event, 'tbsmsj1')"
                               id="twoTime"></td>
                    <td><input @keydown="nextFocus($event,6)" class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })" v-model="tbsmsj2"
                               @blur="dateForVal($event, 'tbsmsj2')"
                               id="threeTime"></td>
                    <td><input @keydown="nextFocus($event,6)" class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })" v-model="tbsmsj3"
                               @blur="dateForVal($event, 'tbsmsj3')"
                               id="fourTime"></td>
                    <td><input @keydown="nextFocus($event,6)" class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })" v-model="tbsmsj4"
                               @blur="dateForVal($event, 'tbsmsj4')"
                               id="fiveTime"></td>
                    <td><input @keydown="nextFocus($event,6)" class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })" v-model="tbsmsj5"
                               @blur="dateForVal($event, 'tbsmsj5')"
                               id="sixTime"></td>
                </tr>
                <tr>
                    <th class="color-wtg">未测原因</th>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="wcyy_tran" :index="oneTwd.wcyy" :val="oneTwd.wcyy"
                                      :name="'oneTwd.wcyy'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="wcyy_tran" :index="twoTwd.wcyy" :val="twoTwd.wcyy"
                                      :name="'twoTwd.wcyy'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="wcyy_tran" :index="threeTwd.wcyy" :val="threeTwd.wcyy"
                                      :name="'threeTwd.wcyy'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="wcyy_tran" :index="fourTwd.wcyy" :val="fourTwd.wcyy"
                                      :name="'fourTwd.wcyy'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="wcyy_tran" :index="fiveTwd.wcyy" :val="fiveTwd.wcyy"
                                      :name="'fiveTwd.wcyy'">
                        </select-input>
                    </td>
                    <td>
                        <select-input @change-data="resultChangeData" :data-notEmpty="true"
                                      :child="wcyy_tran" :index="sixTwd.wcyy" :val="sixTwd.wcyy"
                                      :name="'sixTwd.wcyy'">
                        </select-input>
                    </td>
                </tr>
            </table>
            <div>
                <div class="dataEnter_tem1" style="width: 48%">
                    <p>大便</p>
                    <div>
                        <div>
                            <span>(次)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input"  v-model="twdQtjlContent.dbcs">
                        </div>
                        <div>
                            <span>(色)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.dbs">
                        </div>
                        <div>
                            <span>(质)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.dbz">
                        </div>
                        <div>
                            <span>(量)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.dbl">
                        </div>
                    </div>
                    <div style="width: 53%;text-align: right">
                        <div style="height: 30px">
                            <span>(人工肛门)</span>
                            <select-input @change-data="resultChange" 
                                          :child="rygm_tran" :index="twdQtjlContent.rygm" :val="twdQtjlContent.rygm"
                                          :name="'twdQtjlContent.rygm'">
                            </select-input>
                        </div>
                        <div style="height: 30px">
                            <span>(灌肠)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.gc">
<!--                            <select-input @change-data="resultChange" :data-notEmpty="true"-->
<!--                                          :child="gc_tran" :index="twdQtjlContent.gc" :val="twdQtjlContent.gc"-->
<!--                                          :name="'twdQtjlContent.gc'">-->
<!--                            </select-input>-->
                        </div>
                        <div>
                            <span>(灌肠前大便)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.gcqdbcs">
                        </div>
                        <div>
                            <span>(脉象)</span>
                            <select-input @changevalue="resultChangeMx"  @change-data="resultChange" :child="mx_tran" :index="twdQtjlContent.mx"
                                           :val="twdQtjlContent.mx" :name="'twdQtjlContent.mx'" :search="true">
                            </select-input>
                        </div>
                    </div>
                </div>

                <div class="dataEnter_tem1" style="width: 52%">
                    <p>小便</p>
                    <div style="width: 36%;text-align: right">
                        <div>
                            <span>(次)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.xbc">
                        </div>
                        <div>
                            <span>(色)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.xbs">
                        </div>
                        <div>
                            <span>(量)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" @keydown="nextFocus($event)" type="number" v-model="twdQtjlContent.xbl">
                        </div>
                        <div>
                            <span>(导尿)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.bldn">
                        </div>
                    </div>
                    <div style="width: 54%;text-align: right">
                        <div>
                            <span>(输入量ml)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.srl">
                        </div>
                        <div>
                            <span>(饮入量ml)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.yrl">
                        </div>
                        <div>
                            <span>(引流量ml)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.yll">
                        </div>
                        <div>
                            <span>(其他出量ml)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" type="number" v-model="twdQtjlContent.qtcl">
                        </div>
                    </div>
                </div>
                <div class="dataEnter_tem1" style="width: 80%;height: 60px;margin-top: 20px;">
                    <p style="line-height: 28px">过敏</p>
                    <div style="width: 15%;text-align: right">
						<div>
							<select-input @change-data="resultChange" :data-notEmpty="true"
							              :child="gms_tran" :index="twdQtjlContent.gms" :val="twdQtjlContent.gms"
							              :name="'twdQtjlContent.gms'">
							</select-input>
						</div>
						<div>
						    <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,1)" class="zui-input" type="text" v-model="twdQtjlContent.gmssm">
						</div>
                    </div>
					<div style="width: 115px;text-align: right">
					    <div>
					        <span>食入量</span>
					        <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,1)" class="zui-input" style="width: 45px;padding: 0" type="number" v-model="twdQtjlContent.crl">
					        <span>g</span>
					    </div>
					</div>
					
                </div>

                <div class="dataEnter_tem1" style="width: 80%;height: 60px;margin-top: 20px;">
                    <p style="line-height: 28px">血压</p>
                    <div style="width: 88px;text-align: right">
                        <div>
                            <span>(上午)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,2)" class="zui-input" style="width: 45px" type="number" v-model="twdQtjlContent.swSsy">
                        </div>
                        <div>
                            <span>(下午)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,2)" class="zui-input" style="width: 45px" type="number" v-model="twdQtjlContent.xwSsy">
                        </div>
                    </div>
                    <div style="width: 115px;text-align: right">
                        <div>
                            <span>/</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,-1)" class="zui-input" style="width: 45px;padding: 0" type="number" v-model="twdQtjlContent.swSzy">
                            <span>mmHg</span>
                        </div>
                        <div>
                            <span>/</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,1)" class="zui-input" style="width: 45px;padding: 0" type="text" v-model="twdQtjlContent.xwSzy">
                            <span>mmHg</span>
                        </div>
                    </div>
                    <div style="width: 130px;text-align: right">
                        <div>
                            <span>身高</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event)" class="zui-input" style="width: 34px;padding: 0" type="text" v-model="twdQtjlContent.sg">
                            <span>(cm)</span>
                        </div>
                        <div>
                            <span>体重</span>
                            <input  @keydown="nextFocus($event)" class="zui-input" style="width: 34px;padding: 0" type="text" v-model="twdQtjlContent.tz">
                            <span>( kg)</span>
                        </div>
                    </div>
                    <div style="width: 180px;text-align: right">
                        <div>
                            <span>基础代谢</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.jcdx">
                            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                        </div>
                        <div>
                            <span>电体克</span>
                            <input @keydown="nextFocus($event)" class="zui-input">
                            <span>(次)</span>
                        </div>
                    </div>
                </div>

                <div class="dataEnter_tem1" style="width: 19%;height: 90px;margin-top: 20px;">
                    <p>舌</p>
                    <div style="width: 83px;text-align: right">
                        <div class="flex-container">
                            <span>(苔)</span>
                            <select-input @changevalue="resultChangeMx" @change-data="resultChange" :child="st_tran" :index="twdQtjlContent.st"
                                          :val="twdQtjlContent.st" :name="'twdQtjlContent.st'" :search="true">
                            </select-input>
                        </div>
                        <div class="flex-container">
                            <span>(质)</span>
                            <select-input @changevalue="resultChangeMx" @change-data="resultChange" :child="sz_tran" :index="twdQtjlContent.sz"
                                          :val="twdQtjlContent.sz" :name="'twdQtjlContent.sz'" :search="true">
                            </select-input>
                        </div>
                    </div>
                </div>
				<div class="dataEnter_tem1" style="width: 48%;height: 60px;">
                    <p style="line-height: 28px"></p>
                    <div style="width: 80%;text-align: left">
                        <div>
                            <span>血氧饱和度%(上午)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,1)" class="zui-input" style="width: 100px" type="number" v-model="twdQtjlContent.xybhds">
                        </div>
                        <div>
                            <span>血氧饱和度%(下午)</span>
                            <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent @keydown="nextFocus($event,1)" class="zui-input" style="width: 100px" type="number" v-model="twdQtjlContent.xybhdx">
                        </div>
                    </div>
                </div>
				<div class="dataEnter_tem1" style="width: 52%;height: 60px;">
				    <p></p>
				    <div style="width: 80%;text-align: left">
				        <div style="height: 30px;">
                            <span>疼痛评估(上午)</span>
							<select-input @change-data="resultChange" 
							              :child="ttpg_tran" :search="true" :index="twdQtjlContent.ttpgs" :val="twdQtjlContent.ttpgs"
							              :name="'twdQtjlContent.ttpgs'">
							</select-input>
                            </div>
                        <div style="height: 30px;">
                            <span>疼痛评估(下午)</span>
							<select-input @change-data="resultChange" 
							              :child="ttpg_tran" :search="true" :index="twdQtjlContent.ttpgx" :val="twdQtjlContent.ttpgx"
							              :name="'twdQtjlContent.ttpgx'">
							</select-input>
                            </div>
				    </div>
				</div>
				
				
                <div class="dataEnter_tem1" style="width: 98%;height: 100px;margin-top: 20px;">
                    <p style="line-height: 20px">其他项目</p>
                    <div style="width: 220px;text-align: right">
                        <div>
                            <span>{{(qtxmList && qtxmList[0])?qtxmList[0]:"(一)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm1">
                        </div>
                        <div>
                            <span>{{(qtxmList && qtxmList[1])?qtxmList[1]:"(二)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm2">
                        </div>
                    </div>
                    <div style="width: 220px;text-align: right">
                        <div>
                            <span>{{(qtxmList && qtxmList[2])?qtxmList[2]:"(三)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm3">
                        </div>
                        <div>
                            <span>{{(qtxmList && qtxmList[3])?qtxmList[3]:"(四)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm4">
                        </div>
                    </div>
                    <!--<div style="width: 110px;text-align: right">
                        <div>
                            <span>{{(qtxmList && qtxmList[8])?qtxmList[8]:"(九)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm9">
                        </div>
                        <div>
                            <span>{{(qtxmList && qtxmList[9])?qtxmList[9]:"(十)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm10">
                        </div>
                    </div>
                    <div style="width: 120px;text-align: right">
                        <div>
                            <span>{{(qtxmList && qtxmList[10])?qtxmList[10]:"(十一)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm11">
                        </div>
                        <div>
                            <span>{{(qtxmList && qtxmList[11])?qtxmList[11]:"(十二)"}}</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxm12">
                        </div>
                    </div> -->
                </div>

                <!--<div class="dataEnter_tem1" style="width: 98%;">
                    <p style="line-height: 32px">标题</p>
                    <div style="width: 110px;text-align: right">
                        <div>
                            <span>(一)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt1">
                        </div>
                        <div>
                            <span>(二)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt2">
                        </div>
                    </div>
                    <div style="width: 110px;text-align: right">
                        <div>
                            <span>(三)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt3">
                        </div>
                        <div>
                            <span>(四)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt4">
                        </div>
                    </div>
                    <div style="width: 110px;text-align: right">
                        <div>
                            <span>(五)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt5">
                        </div>
                        <div>
                            <span>(六)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt6">
                        </div>
                    </div>
                    <div style="width: 110px;text-align: right">
                        <div>
                            <span>(七)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt7">
                        </div>
                        <div>
                            <span>(八)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt8">
                        </div>
                    </div>
                    <div style="width: 110px;text-align: right">
                        <div>
                            <span>(九)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt9">
                        </div>
                        <div>
                            <span>(十)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt10">
                        </div>
                    </div>
                    <div style="width: 120px;text-align: right">
                        <div>
                            <span>(十一)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt11">
                        </div>
                        <div>
                            <span>(十二)</span>
                            <input class="zui-input" @keydown="nextFocus($event)" v-model="twdQtjlContent.qtxmBt12">
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
    <div class="fyxm-size flex-one flex-container flex-dir-c" v-show="which==1">
        <div id="twd" class="flex-one over-auto"></div>
    </div>
    <div class="dashLine printHide"></div>
</div>
<model :s="'保存'" :c="'退出'" class="SetUserTwd" @default-click="saveData" @result-clear="closeModel"
       :model-show="true" @result-close="closeModel" v-if="wjzShow" :title="'体温单批量数据录入'">
    <div class="SetUser_model " id="loadPage">

    </div>
</model>
<script type="text/javascript" src="childpage/scsj.js"></script>
