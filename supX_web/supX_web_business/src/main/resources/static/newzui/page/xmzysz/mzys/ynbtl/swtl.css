.swtl .zui-input{
    text-indent: 0;
}
.icon-upload{
    display: inline-block;
    width: 38px;
    height: 38px;
    cursor: pointer;
    position: relative;
    border-radius: 50px;
    -webkit-border-radius: 50px;
    border: 1px solid #cccfd4;
}
.icon-upload:hover{
    background:rgba(26,188,156,0.10);
}
.icon-upload:before,  .icon-upload:after {
    content: '';
    height: 1px;
    width: 24px;
    display: block;
    background:#cccfd4;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    position: absolute;
    top: 18px;
    left: 6px;
}
.icon-upload:after {
    height: 24px;
    width: 1px;
    top: 7px;
    left: 17px;
}
.HeadPortrait{
    width:36px;
    cursor: pointer;
    position: relative;
    margin-right: 7px;
    height:36px;
    border-radius:100%;
}
.color-f2a654{
    color: #f2a654;
}
.HeadPortrait .headImg{
    width: 100%;
    border-radius:100%;
    height: 100%;
}
.HeadPortrait:after{
    position: absolute;
    right: 2px;
    top: 5px;
    z-index: 11;
    content: '';
    border-radius:2px;
    width:8px;
    cursor: pointer;
    height:2px;
    background-color: #ffffff;
}
.HeadPortrait:before{
    opacity:0.8;
    content: '';
    cursor: pointer;
    right: 0;
    position: absolute;
    background:#ff5c63;
    width:12px;
    z-index: 11;
    height:12px;
    border-radius:100%;
}
.iconfont-no-hover:hover:before,.iconfont-no-hover:before{
    color: #ffffff!important;
}