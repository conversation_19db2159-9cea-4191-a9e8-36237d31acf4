<link href="childpage/twd.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="/pub/css/print.css" media="print"/>
<div id="tw-table" class="printShow" style="width:676px;margin:0 auto;">
    <div class="baseInfo">
        <div v-if="N03004200251 == '1'" style="height: 28px;"></div>
        <div class="twd-title" style="padding-bottom:0;" id="hospname" v-text="">{{brxxContent.yljgmc}}体温单</div>
        <div class="personInfo whiteSpace flex-container">
            <p>姓名:</p>
            <p v-text="brxxContent.brxm"></p>
            <p>性别:</p>
            <p v-text="brxb_tran[brxxContent.brxb]"></p>
            <p v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'">年齡:</p>
            <p v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'" v-text="brxxContent.nl+''+nldw_tran[brxxContent.nldw]"></p>
            <p v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'" v-text="brxxContent.nl2?(brxxContent.nl2 + '' + nldw_tran[brxxContent.nldw2]):''"></p>
            <p>床位号:</p>
            <p v-text="brxxContent.rycwbh"></p>
            <p>住院号:</p>
            <p v-text="brxxContent.zyh"></p>
            <p v-show="csqxContent.N03004200264 != '1'">诊断名称:</p>
            <p v-show="csqxContent.N03004200264 != '1' " v-text="brxxContent.ryzdmc"></p>
        </div>
    </div>
    <section style="width:676px;margin:0;border: 2px solid #000000">
        <table id="table_1" cellpadding="0" cellspacing="0">
            <tr>
                <td class="text-center">日期</td>
                <td v-for="item in currentDay" class="text-center" v-text="item"></td>
            </tr>
            <tr>
                <td class="text-center">住院天数</td>
                <td v-for="item in zyDay" class="text-center" v-text="item <= 0?'':item"></td>
            </tr>
<!--            <tr>-->
<!--                <td class="text-center">体重</td>-->
<!--                <td v-for="item in 7" class="text-center" v-text="item"></td>-->
<!--            </tr>-->
            <tr v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'">
                <td class="text-center">手术后天数</td>
                <td v-for="item in sshDay" class="text-center">{{item}}</td>
            </tr>
        </table>
        <canvas id="twd_title"></canvas>
        <div id="container">
            <canvas id="twd_cvs" style="margin-top: -5px"></canvas>
        </div>
        <table id="table_2" cellpadding="0" cellspacing="0">
            <tr v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'">
                <td>呼吸(次/分)</td>
                <td style="width: 14px"  :class="[('setClass'+index)]" v-for="(item,index) in hx_list" v-text="item"></td>
            </tr>
        </table>
        <table id="table_3" cellpadding="0" cellspacing="0">
            <tr v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'">
                <td class="text-center">血压(mmHg)</td>
                <!-- <td v-for="item in xy_list" class="text-center" v-text="item"></td> -->
                <td v-for="item in xy_list"
                :class="[item && item.length > 6 ? 'width-ls' : 'text-center']"
                v-text="item"></td>
            </tr>
			<tr v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'">
			    <td class="text-center">血氧饱和度(%)</td>
			    <!-- <td v-for="item in xy_list" class="text-center" v-text="item"></td> -->
			    <td v-for="item in xybhd_list"
			    :class="[item && item.length > 6 ? 'width-ls' : 'text-center']"
			    v-text="item"></td>
			</tr>
			<tr v-show="csqxContent.N03004200264 != '1' || brxxContent.yebh == '000'">
			    <td class="text-center">疼痛评估</td>
			    <!-- <td v-for="item in xy_list" class="text-center" v-text="item"></td> -->
			    <td v-for="item in ttpg_list"
			    :class="[item && item.length > 6 ? 'width-ls' : 'text-center']"
			    v-text="item"></td>
			</tr>
        </table>
        <table id="table_4" cellpadding="0" cellspacing="0">
			<tr style="color: red;">
			    <td class="text-center">过敏史</td>
			    <td v-for="item in gms_list" class="text-center" v-text="item"></td>
			</tr>
			<tr>
			    <td class="text-center">食入量</td>
			    <td v-for="item in crl_list" class="text-center" v-text="item"></td>
			</tr>
            <tr v-if="N03004200239[3]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">大便(次/日)</td>
                <td v-for="item in dbList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[7]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">身高(cm)</td>
                <td v-for="item in sgList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[6]!='0'">
                <td class="text-center">体重(kg)</td>
                <td v-for="item in tzList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[0]!='0' && csqxContent.N03004200263 != '1' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">输入量(ml)</td>
                <td v-for="item in rlList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[1]!='0' && csqxContent.N03004200263 != '1' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">饮入量(ml)</td>
                <td v-for="item in yrList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[2]!='0' && csqxContent.N03004200263 != '1' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">引流量(ml)</td>
                <td v-for="item in ylList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[2]!='0' && csqxContent.N03004200263 == '1' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">入量(ml)</td>
                <td v-for="item in hzrlList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[2]!='0' && csqxContent.N03004200263 == '1' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">出量(ml)</td>
                <td v-for="item in hzclList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[4]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">小便(次)</td>
                <td v-for="item in xbcList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[5]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">尿量</td>
                <td v-for="item in xbList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[8]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">脉象</td>
                <td v-for="item in mxList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[9]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">基础代谢</td>
                <td v-for="item in jcdxList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[10]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">灌肠</td>
                <td v-for="item in gcList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[11]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">灌肠前大便</td>
                <td v-for="item in gcqdbcsList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[12]!='0' && (csqxContent.N03004200264 != '1' || brxxContent.yebh == '000')">
                <td class="text-center">导尿</td>
                <td v-for="item in bldnList" class="text-center" v-text="item"></td>
            </tr>
			
            <tr v-if="qtxmList && qtxmList[0]">
                <td class="text-center">{{qtxmList[0]}}</td>
                <td v-for="item in qtxm1List" class="text-center qtxm1List" v-text="item"></td>
            </tr>
            <tr v-if="qtxm2List">
                <td class="text-center"></td>
                <td v-for="item in qtxm2List" class="text-center qtxm2List" v-text="item"></td>
            </tr>
            <tr v-if="qtxm3List">
                <td class="text-center"></td>
                <td v-for="item in qtxm3List" class="text-center qtxm3List" v-text="item"></td>
            </tr>
            <tr v-if="qtxm4List">
                <td class="text-center"></td>
                <td v-for="item in qtxm4List" class="text-center qtxm4List" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[13]!='0'">
                <td  rowspan="2" class="text-center ">
                    <div  class="flex-container height100 flex-align-c">
                        <p   class="text-center flex-container col-fm-5 flex-align-c flex-jus-c">舌</p>
                        <div  class="st_border">
                            <p class="text-center st_border_bottom">苔</p>
                            <p class="text-center">质</p>
                        </div>
                    </div>
                </td>
                <td rowspan="1" v-for="item in stList" class="text-center" v-text="item"></td>
            </tr>
            <tr v-if="N03004200239[14]!='0'">
                <td rowspan="1" v-for="item in szList" class="text-center" v-text="item"></td>
            </tr>
			
<!--            <tr v-for="item in 2">-->
<!--                <td class="text-center"></td>-->
<!--                <td class="text-center" v-for="item in 7" ></td>-->
<!--            </tr>-->
        </table>
    </section>
    <div id="printNumber" class="text-center padd-t-10">
        第{{dqym}}页
    </div>
</div>
<script type="application/javascript" src="childpage/twd.js"></script>
