<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <link rel="stylesheet" href="twd.css">
</head>

<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
    <div class="wrapper flex-container flex-dir-c">
        <header class="userNameBg printHide" id="userInfo" v-cloak>
            <div class="flex">
                <div class="userNameImg">
                    <span v-show="brItem.nljd==1">
                        <img src="/newzui/pub/image/maleBaby.png">
                    </span>
                    <span v-show="brItem.nljd==2">
                        <img src="/newzui/pub/image/femalebaby.png">
                    </span>
                    <span v-show="brItem.nljd==3">
                        <img src="/newzui/pub/image/Group <EMAIL>">
                    </span>
                    <span v-show="brItem.nljd==4">
                        <img src="/newzui/pub/image/Group <EMAIL>">
                    </span>
                    <span v-show="brItem.nljd==5">
                        <img src="/newzui/pub/image/juvenile.png">
                    </span>
                    <span v-show="brItem.nljd==6">
                        <img src="/newzui/pub/image/maid.png">
                    </span>
                    <span v-show="brItem.nljd==7">
                        <img src="/newzui/pub/image/youth.png">
                    </span>
                    <span v-show="brItem.nljd==8">
                        <img src="/newzui/pub/image/woman.png">
                    </span>
                    <span v-show="brItem.nljd==9">
                        <img src="/newzui/pub/image/grandpa.png">
                    </span>
                    <span v-show="brItem.nljd==10">
                        <img src="/newzui/pub/image/grandma.png">
                    </span>
                    <span v-show="brItem.nljd==11">
                        <img src="/newzui/pub/image/<EMAIL>">
                    </span>
                </div>
                <div class="text-color">
                    <p class="userHeader">
                        <span class="userName" v-text="brItem.brxm"></span>
                        <span class="sex text" v-text="brxb_tran[brItem.brxb]"></span>
                        <span class="nl text">{{brItem.nl}}{{xtwhnldw_tran[brItem.nldw]}}</span>
                    </p>
                    <div class="userCwh">
                        <span class="cwh text" v-text="'床位号：'+ brItem.rycwbh +'号'"></span>
                        <span class="zyh text" v-text="'住院号：'+ brItem.zyh"></span>
                        <span class="bq text" v-text="'科室：'+ brItem.ryksmc"></span>
                        <span class="ys text" v-text="'医师：'+ brItem.zyysxm"></span>
                        <span class="brzt text" v-text="'病人状态：'+zyzt_tran[brItem.zyzt]"></span>
                        <span class="bz text" v-text="'病种：'+ brItem.ryzdmc"></span>
                    </div>
                    <!--<div class="userCwh">-->
                    <!--<span class="fyhj text" v-text="'费用合计：'+ brItem.fyhj"></span>-->
                    <!--<span class="yjhj text" v-text="'预交合计：'+ brItem.yjhj"></span>-->
                    <!--<span class="zyts text" v-text="'住院天数：'+ brItem.zyts"></span>-->
                    <!--<span class="phone text" v-text="'联系电话：'+ brItem.sjhm"></span>-->
                    <!--<span class="sfz text"  v-text="'身份证：'+ brItem.sfzjhm"></span>-->
                    <!--</div>-->
                    <!--<div class="userFooter">-->
                    <!--<span class="hl text" v-text="'护理等级：'"></span>-->
                    <!--<span class="wz text">危重：危重</span>-->
                    <!--</div>-->
                    <div>
                        <p class="heaf text">更多详细信息>></p>
                    </div>
                </div>
            </div>
        </header>
        <div class="menu printHide flex-container flex-jus-sp flex-align-c" id="menu" v-cloak="">
            <div class="col-xxl-8 padd-l-20" v-show="num==0">
                <div class="zui-form">
                    <div class="flex-container flex-align-c ">
                        <label class="whiteSpace margin-r-5 ft-14">时间</label>
                        <div class="zui-input-inline">
                            <i class="icon-position icon-rl"></i>
                            <input class="zui-input todate text-indent20" id="lrTime" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4" v-show="num==1">
                <div class="tong-search printHide">
                    <div class="zui-form">
                        <div class="flex-container flex-align-c ">
                            <label class="whiteSpace margin-r-5 ft-14">查询时间</label>
                            <div class="zui-input-inline">
                                <i class="icon-position icon-rl"></i>
                                <input class="zui-input todate text-indent20" id="time" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-right menu flex-container flex-align-c flex-jus-e">
                <span class="fa butt-hover fa-th-large" :class="{'active':num==0}" @click="show(0)"></span>
                <span class="relative fenge"></span>
                <span class="fa butt-hover fa-bars" :class="{'active':num==1}" @click="show(1)"></span>
            </div>
        </div>
        <div id="lr" class="printHide flex-one box" v-show="isShow" v-cloak="">
            <div class="table-box">
                <div class="row grid-box">
                    <div class="col bg">时段</div>
                    <div class="col bg col-xxl-2">2</div>
                    <div class="col bg col-xxl-2">6</div>
                    <div class="col bg col-xxl-2">10</div>
                    <div class="col bg col-xxl-2">14</div>
                    <div class="col bg col-xxl-2">18</div>
                    <div class="col bg col-xxl-2">22</div>
                </div>
                <div class="row grid-box">
                    <div class="col bg">体温部位</div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="false" :child="twbw_tran" :index="oneTwd.twbw"
                            :val="oneTwd.twbw" :name="'oneTwd.twbw'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="twbw_tran" :index="twoTwd.twbw"
                            :val="twoTwd.twbw" :name="'twoTwd.twbw'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="twbw_tran" :index="threeTwd.twbw"
                            :val="threeTwd.twbw" :name="'threeTwd.twbw'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="twbw_tran" :index="fourTwd.twbw"
                            :val="fourTwd.twbw" :name="'fourTwd.twbw'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="twbw_tran" :index="fiveTwd.twbw"
                            :val="fiveTwd.twbw" :name="'fiveTwd.twbw'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="twbw_tran" :index="sixTwd.twbw"
                            :val="sixTwd.twbw" :name="'sixTwd.twbw'">
                        </select-input>
                    </div>
                </div>
                <div class="row grid-box">
                    <div class="col bg">体温</div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="oneTwd.tw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="twoTwd.tw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="threeTwd.tw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fourTwd.tw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fiveTwd.tw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="sixTwd.tw"></div>
                </div>
				<div class="row grid-box">
				    <div class="col bg">脉搏</div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="oneTwd.mb"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="twoTwd.mb"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="threeTwd.mb"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fourTwd.mb"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fiveTwd.mb"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="sixTwd.mb"></div>
				</div>
				<div class="row grid-box">
				    <div class="col bg">呼吸</div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="oneTwd.fx"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="twoTwd.fx"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="threeTwd.fx"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fourTwd.fx"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fiveTwd.fx"></div>
				    <div class="col bg col-xxl-2"><input class="zui-input" v-model="sixTwd.fx"></div>
				</div>
                <div class="row grid-box">
                    <div class="col bg">物理降温</div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="oneTwd.wljw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="twoTwd.wljw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="threeTwd.wljw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fourTwd.wljw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fiveTwd.wljw"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="sixTwd.wljw"></div>
                </div>
                <div class="row grid-box">
                    <div class="col bg">心跳</div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="oneTwd.xt"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="twoTwd.xt"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="threeTwd.xt"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fourTwd.xt"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="fourTwd.xt"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" v-model="sixTwd.xt"></div>
                </div>
                
                <div class="row grid-box">
                    <div class="col bg">起搏器</div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="oneTwd.xtqbq"
                            :val="oneTwd.xtqbq" :name="'oneTwd.xtqbq'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="twoTwd.twbw"
                            :val="twoTwd.xtqbq" :name="'twoTwd.xtqbq'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="threeTwd.xtqbq"
                            :val="threeTwd.xtqbq" :name="'threeTwd.xtqbq'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="fourTwd.xtqbq"
                            :val="fourTwd.xtqbq" :name="'fourTwd.xtqbq'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="fiveTwd.xtqbq"
                            :val="fiveTwd.xtqbq" :name="'fiveTwd.xtqbq'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="sixTwd.xtqbq"
                            :val="sixTwd.xtqbq" :name="'sixTwd.xtqbq'">
                        </select-input>
                    </div>
                </div>
                
                <div class="row grid-box">
                    <div class="col bg">人工呼吸</div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="oneTwd.rgfx"
                            :val="oneTwd.rgfx" :name="'oneTwd.rgfx'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="twoTwd.rgfx"
                            :val="twoTwd.rgfx" :name="'twoTwd.rgfx'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="threeTwd.rgfx"
                            :val="threeTwd.rgfx" :name="'threeTwd.rgfx'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="fourTwd.rgfx"
                            :val="fourTwd.rgfx" :name="'fourTwd.rgfx'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="fiveTwd.rgfx"
                            :val="fiveTwd.rgfx" :name="'fiveTwd.rgfx'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="istrue_tran" :index="sixTwd.rgfx"
                            :val="sixTwd.rgfx" :name="'sixTwd.rgfx'">
                        </select-input>
                    </div>
                </div>
                <div class="row grid-box">
                    <div class="col bg">特别说明</div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="tbsm_tran" :index="oneTwd.tbsm"
                            :val="oneTwd.tbsm" :name="'oneTwd.tbsm'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="tbsm_tran" :index="twoTwd.tbsm"
                            :val="twoTwd.tbsm" :name="'twoTwd.tbsm'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="tbsm_tran" :index="threeTwd.tbsm"
                            :val="threeTwd.tbsm" :name="'threeTwd.tbsm'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="tbsm_tran" :index="fourTwd.tbsm"
                            :val="fourTwd.tbsm" :name="'fourTwd.tbsm'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="tbsm_tran" :index="fiveTwd.tbsm"
                            :val="fiveTwd.tbsm" :name="'fiveTwd.tbsm'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="tbsm_tran" :index="sixTwd.tbsm"
                            :val="sixTwd.tbsm" :name="'sixTwd.tbsm'">
                        </select-input>
                    </div>
                </div>
                <div class="row grid-box">
                    <div class="col bg">发生时间</div>
                    <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                            v-model="oneTwd.tbsmsj" @blur="dateForVal($event, 'oneTwd.tbsmsj')" id="oneTime"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                            v-model="twoTwd.tbsmsj" @blur="dateForVal($event, 'twoTwd.tbsmsj')" id="twoTime"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                            v-model="threeTwd.tbsmsj" @blur="dateForVal($event, 'threeTwd.tbsmsj')" id="threeTime"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                            v-model="fourTwd.tbsmsj" @blur="dateForVal($event, 'fourTwd.tbsmsj')" id="fourTime"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                            v-model="fiveTwd.tbsmsj" @blur="dateForVal($event, 'fiveTwd.tbsmsj')" id="fiveTime"></div>
                    <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                            v-model="sixTwd.tbsmsj" @blur="dateForVal($event, 'sixTwd.tbsmsj')" id="sixTime"></div>
                </div>
                <div class="row grid-box">
                    <div class="col bg">未测原因</div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="wcyy_tran" :index="oneTwd.wcyy"
                            :val="oneTwd.wcyy" :name="'oneTwd.wcyy'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="wcyy_tran" :index="twoTwd.wcyy"
                            :val="twoTwd.wcyy" :name="'twoTwd.wcyy'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="wcyy_tran" :index="threeTwd.wcyy"
                            :val="threeTwd.wcyy" :name="'threeTwd.wcyy'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="wcyy_tran" :index="fourTwd.wcyy"
                            :val="fourTwd.wcyy" :name="'fourTwd.wcyy'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="wcyy_tran" :index="fiveTwd.wcyy"
                            :val="fiveTwd.wcyy" :name="'fiveTwd.wcyy'">
                        </select-input>
                    </div>
                    <div class="col col-xxl-2">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="wcyy_tran" :index="sixTwd.wcyy"
                            :val="sixTwd.wcyy" :name="'sixTwd.wcyy'">
                        </select-input>
                    </div>
                </div>
            </div>
            <div class="row grid-box">
                <div class="col bg">特别说明</div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="tbsm_tran" :index="oneTwd.tbsm" :val="oneTwd.tbsm"
                                  :name="'oneTwd.tbsm'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="tbsm_tran" :index="twoTwd.tbsm" :val="twoTwd.tbsm"
                                  :name="'twoTwd.tbsm'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="tbsm_tran" :index="threeTwd.tbsm" :val="threeTwd.tbsm"
                                  :name="'threeTwd.tbsm'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="tbsm_tran" :index="fourTwd.tbsm" :val="fourTwd.tbsm"
                                  :name="'fourTwd.tbsm'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="tbsm_tran" :index="fiveTwd.tbsm" :val="fiveTwd.tbsm"
                                  :name="'fiveTwd.tbsm'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="tbsm_tran" :index="sixTwd.tbsm" :val="sixTwd.tbsm"
                                  :name="'sixTwd.tbsm'">
                    </select-input>
                </div>
            </div>
            <div class="row grid-box">
                <div class="col bg">发生时间</div>
                <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                                                     v-model="oneTwd.tbsmsj" @blur="dateForVal($event, 'oneTwd.tbsmsj')"
                                                     id="oneTime"></div>
                <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                                                     v-model="twoTwd.tbsmsj" @blur="dateForVal($event, 'twoTwd.tbsmsj')"
                                                     id="twoTime"></div>
                <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                                                     v-model="threeTwd.tbsmsj"
                                                     @blur="dateForVal($event, 'threeTwd.tbsmsj')"
                                                     id="threeTime"></div>
                <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                                                     v-model="fourTwd.tbsmsj"
                                                     @blur="dateForVal($event, 'fourTwd.tbsmsj')"
                                                     id="fourTime"></div>
                <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                                                     v-model="fiveTwd.tbsmsj"
                                                     @blur="dateForVal($event, 'fiveTwd.tbsmsj')"
                                                     id="fiveTime"></div>
                <div class="col bg col-xxl-2"><input class="zui-input" onclick="WdatePicker({ dateFmt: 'HH:mm' })"
                                                     v-model="sixTwd.tbsmsj" @blur="dateForVal($event, 'sixTwd.tbsmsj')"
                                                     id="sixTime"></div>
            </div>
            <div class="row grid-box">
                <div class="col bg">未测原因</div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="wcyy_tran" :index="oneTwd.wcyy" :val="oneTwd.wcyy"
                                  :name="'oneTwd.wcyy'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="wcyy_tran" :index="twoTwd.wcyy" :val="twoTwd.wcyy"
                                  :name="'twoTwd.wcyy'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="wcyy_tran" :index="threeTwd.wcyy" :val="threeTwd.wcyy"
                                  :name="'threeTwd.wcyy'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="wcyy_tran" :index="fourTwd.wcyy" :val="fourTwd.wcyy"
                                  :name="'fourTwd.wcyy'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="wcyy_tran" :index="fiveTwd.wcyy" :val="fiveTwd.wcyy"
                                  :name="'fiveTwd.wcyy'">
                    </select-input>
                </div>
                <div class="col col-xxl-2">
                    <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="wcyy_tran" :index="sixTwd.wcyy" :val="sixTwd.wcyy"
                                  :name="'sixTwd.wcyy'">
                    </select-input>
                </div>
            </div>
        </div>
        <div class="grid-box card-box">
            <div class="col-xxl-6 card-item">
                <div class="bt">大&emsp;&emsp;&emsp;&emsp;便</div>
                <div class="grid-box">
                    <div class="col-xxl-6">
                        <div class="input-box">
                            <label>（次）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.dbcs"></div>
                        </div>
                        <div class="input-box">
                            <label>（色）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.dbs"></div>
                        </div>
                        <div class="input-box">
                            <label>（质）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.dbz"></div>
                        </div>
                        <div class="input-box">
                            <label>（量）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.dbl" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent type="number"></div>
                        </div>
                    </div>
                    <div class="col-xxl-6">
                        <div class="input-box">
                            <label>（人工肛门）</label>
                            <div class="zui-input-box">
                                <select-input @change-data="resultChange" :data-notEmpty="true"
                                              :child="rygm_tran" :index="twdQtjlContent.rygm" :val="twdQtjlContent.rygm"
                                              :name="'twdQtjlContent.rygm'">
                                </select-input>
                            </div>
                            <div class="input-box">
                                <label>（色）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.dbs"></div>
                            </div>
                            <div class="input-box">
                                <label>（质）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.dbz"></div>
                            </div>
                            <div class="input-box">
                                <label>（量）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.dbl" type="number"></div>
                            </div>
                        </div>
                        <div class="col-xxl-6">
                            <div class="input-box">
                                <label>（人工肛门）</label>
                                <div class="zui-input-box">
                                    <select-input @change-data="resultChange" :data-notEmpty="true" :child="rygm_tran"
                                        :index="twdQtjlContent.rygm" :val="twdQtjlContent.rygm" :name="'twdQtjlContent.rygm'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="input-box">
                                <label>（灌肠）</label>
                                <div class="zui-input-box">
                                    <select-input @change-data="resultChange" :data-notEmpty="true" :child="gc_tran"
                                        :index="twdQtjlContent.gc" :val="twdQtjlContent.gc" :name="'twdQtjlContent.gc'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="input-box">
                                <label>（灌肠前大便）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.gcqdbcs"></div>
                            </div>
                            <div class="input-box">
                                <label>（脉象）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.mx"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6 card-item">
                <div class="bt" style="left: 50px;">小&emsp;&emsp;&emsp;&emsp;便</div>
                <div class="grid-box">
                    <div class="col-xxl-6">
                        <div class="input-box">
                            <label>（次）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.xbc"></div>
                        </div>
                        <div class="input-box">
                            <label>（色）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.xbs"></div>
                        </div>
                        <div class="input-box">
                            <label>（量）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.xbl" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent type="number"></div>
                        </div>
                        <div class="input-box">
                            <label>（导尿）</label>
                            <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.bldl"
                                                              @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent         type="number"></div>
                        </div>
                    </div>
                    <div class="col-xxl-6">
                        <div class="input-box">
                            <label>（输入量ml）</label>
                            <div class="zui-input-box">
                                <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" v-model="twdQtjlContent.srl" type="number">
                            </div>
                            <div class="input-box">
                                <label>（色）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.xbs"></div>
                            </div>
                            <div class="input-box">
                                <label>（量）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.xbl" type="number"></div>
                            </div>
                            <div class="input-box">
                                <label>（导尿）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.bldl" type="number"></div>
                            </div>
                        </div>
                        <div class="input-box">
                            <label>（引入量ml）</label>
                            <div class="zui-input-box">
                                <input class="zui-input" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="twdQtjlContent.yrl" type="number">
                            </div>
                            <div class="input-box">
                                <label>（引入量ml）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.yrl" type="number">
                                </div>
                            </div>
                            <div class="input-box">
                                <label>（引流量ml）</label>
                                <div class="zui-input-box"><input class="zui-input" type="number" v-model="twdQtjlContent.yll"></div>
                            </div>
                            <div class="input-box">
                                <label>（其他出量ml）</label>
                                <div class="zui-input-box"><input class="zui-input" type="number" v-model="twdQtjlContent.qtcl"></div>
                            </div>
                        </div>
                        <div class="input-box">
                            <label>（引流量ml）</label>
                            <div class="zui-input-box"><input class="zui-input" type="number"
                                                              @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent       v-model="twdQtjlContent.yll"></div>
                        </div>
                        <div class="input-box">
                            <label>（其他出量ml）</label>
                            <div  class="zui-input-box"><input class="zui-input" type="number"
                                                               @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent       v-model="twdQtjlContent.qtcl"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="grid-box card-box">
            <div class="col-xxl-9 card-item">
                <div class="bt" style="margin-top: 0;top: 15px;">血&emsp;压</div>
                <div class="grid-box">
                    <div class="col-xxl-6">
                        <div class="input-box">
                            <label>（上午）</label>
                            <div class="zui-input-inline pop-input-box grid-box" style="margin-right: 0;">
                                <div class="col-xxl-6" style="padding-right: 12px;">
                                    <div class="zui-input-inline position pop-input-box" style="margin-right: 0;">
                                        <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" type="number" v-model="twdQtjlContent.swSsy">
                                        <i class="danwei">mmhg</i>
                                        <i class="fenge">/</i>
                                    </div>
                                </div>
                                <div class="col-xxl-6" style="padding-left: 12px;">
                                    <div class="zui-input-inline position pop-input-box" style="margin-right: 0;">
                                        <input @mousewheel.prevent @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" type="number" v-model="twdQtjlContent.swSzy">
                                        <i class="danwei">mmhg</i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="input-box">
                            <label>（下午）</label>
                            <div class="zui-input-inline pop-input-box grid-box" style="margin-right: 0;">
                                <div class="col-xxl-6" style="padding-right: 12px;">
                                    <div class="zui-input-inline position pop-input-box" style="margin-right: 0;">
                                        <input @mousewheel.prevent @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" type="number" v-model="twdQtjlContent.xwSsy">
                                        <i class="danwei">mmhg</i>
                                        <i class="fenge">/</i>
                                    </div>
                                </div>
                                <div class="col-xxl-6" style="padding-left: 12px;">
                                    <div class="zui-input-inline position pop-input-box" style="margin-right: 0;">
                                        <input @mousewheel.prevent @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" v-model="twdQtjlContent.xwSzy" type="number">
                                        <i class="danwei">mmhg</i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xxl-3">
                        <div class="input-box">
                            <label>（身高）</label>
                            <div class="zui-input-box position pop-input-box">
                                <input  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" type="number" v-model="twdQtjlContent.sg">
                                <i class="danwei">cm</i>
                            </div>
                        </div>
                        <div class="input-box">
                            <label>（体重）</label>
                            <div class="zui-input-box position pop-input-box">
                                <input  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" type="number" v-model="twdQtjlContent.tz">
                                <i class="danwei">kg</i>
                            </div>
                        </div>
                        <div class="col-xxl-3">
                            <div class="input-box">
                                <label>（基础代谢）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.jcdx">
                                </div>
                            </div>
                            <div class="input-box">
                                <label>（电体克）</label>
                                <div class="zui-input-box position pop-input-box">
                                    <input class="zui-input">
                                    <i class="danwei">次</i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-3 card-item">
                    <div class="bt" style="margin-top: 0;top: 32px;left: 50px;">舌</div>
                    <div class="grid-box">
                        <div class="col-xxl-12">
                            <div class="input-box">
                                <label>（苔）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.st"></div>
                            </div>
                            <div class="input-box">
                                <label>（质）</label>
                                <div class="zui-input-box"><input class="zui-input" v-model="twdQtjlContent.sz"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid-box card-box">
                <div class="card-item">
                    <div class="bt" style="margin-top: 0;top: 5px;">其他项目</div>
                    <div class="grid-box">
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（一）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm1">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（三）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm3">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（五）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm5">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（七）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm7">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（九）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm9">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（十一）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm11">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（二）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm2">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（四）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm4">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（六）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm6">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（八）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm8">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（十）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm10">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（十二）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxm12">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid-box card-box">
                <div class="card-item">
                    <div class="bt" style="margin-top: 0;top: 5px;">标&emsp;&emsp;题</div>
                    <div class="grid-box">
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（一）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt1">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（三）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt3">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（五）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt5">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（七）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt7">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（九）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt9">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box" style="padding-bottom: 10px">
                                <label>（十一）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt11">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（二）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt2">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（四）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt4">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（六）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt6">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（八）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt8">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（十）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt10">
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-2">
                            <div class="input-box">
                                <label>（十二）</label>
                                <div class="zui-input-box">
                                    <input class="zui-input" v-model="twdQtjlContent.qtxmBt12">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex-container flex-jus-e flex-align-c zui-table-tool printHide">
                <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closePage">取消</button>
                <!--<button id="refresh" class="zui-btn btn-primary xmzb-db" @click="getData">刷新</button>-->
                <button  v-waves id="save" class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
            </div>
        </div>
        <div id="twd-box" class="flex-one over-auto no-scrollbar box" style="display: none">
            <div id="twd"></div>
            <div class="ksys-btn action-bar fixed printHide">
                <button v-waves id="dy" class="zui-btn btn-primary xmzb-db" onclick="printFn()">打印</button>
            </div>
        </div>
    </div>
</body>
<script src="twd.js"></script>

</html>
