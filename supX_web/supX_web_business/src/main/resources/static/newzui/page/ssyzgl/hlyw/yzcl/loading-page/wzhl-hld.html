<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="text/javascript" src="/newzui/currentCSS/js/vue/vuescroll.min.js"></script>
    <link rel="stylesheet" type="text/css" href="/pub/css/print.css" media="print">
    <link href="../../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="wzhl-hld.css">
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="printArea printShow"></div>
<div id="wzhlHld" class="wrapper printHide flex-container flex-dir-c">
	<div class="menu panel tong-top printHide">
		<button v-waves class="tong-btn btn-parmary" @click="print()">打印</button>
		<button v-waves class="tong-btn btn-parmary-b" @click="closePage()">关闭</button>
	</div>
	<div class="flex-one" style="background: #fff;">
		<vue-scroll :ops="pageScrollOps">
			<div class="hld-box printShow">
				<table>
					<thead>
						<caption class="hld-title">射洪金华卫生院临床病员护理单</caption>
						<tr>
							<th colspan="37" class="brxx">科室:{{ brxx.ryksmc }} 姓名:{{ brxx.brxm }} 性别:{{ brxb_tran[ brxx.brxb ] }} 年龄:{{ brxx.nl }} 床号:{{ brxx.rycwbh }}号 住院号:{{ brxx.zyh }} 入院日期:{{ fDate( brxx.ryrq, 'yyyy-MM-dd') }} 诊断:{{ brxx.ryzdmc }}</th>
						</tr>
						<tr class="hlws_item">
							<td rowspan=3 nowrap="pre">日期时间</td>
							<td rowspan=3>意识</td>
							<td colspan=2>瞳孔</td>
							<td rowspan=2>体温</td>
							<td rowspan=2>脉搏</td>
							<td rowspan=2>呼吸</td>
							<td rowspan=2>血压</td>
							<td rowspan=2>心电监测</td>
							<td rowspan=2>血氧饱和度</td>
							<td rowspan=2>吸氧</td>
							<td colspan=3 rowspan=2>入量</td>
							<td colspan=3 rowspan=2>出量</td>
							<td colspan=3 rowspan=3>皮肤情况</td>
							<td colspan=3 rowspan=3>管路护理</td>
							<td colspan=3 rowspan=3>其它一</td>
							<td colspan=3 rowspan=3>其它二</td>
							<td colspan=3 rowspan=3>心率</td>
							<td colspan=3 nowrap="pre" rowspan=3>病情观察及措施</td>
							<td nowrap="pre" colspan=3 rowspan=3>护士签名</td>
						</tr>
						<tr class="hlws_item">
							<td>大小(MM)</td>
							<td>光反应</td>
						</tr>
						<tr class="hlws_item">
							<td>左/右</td>
							<td>左/右</td>
							<td>°C</td>
							<td>次/分</td>
							<td>次/分</td>
							<td>nnHg</td>
							<td>心率</td>
							<td>％</td>
							<td>L/min</td>
							<td>名称</td>
							<td>途经</td>
							<td>ml</td>
							<td>名称</td>
							<td>ml</td>
							<td>颜色性状</td>
						</tr>
					</thead>
					<tbody>
						<!-- 如果是夜间的话给tr加一个red类 -->
						<!--<tr
							v-for="(item, index) in hljl"
							:class="{ 'red': index%2==0, 'pageNext': index!=0&&index%10==0 }">-->
						<tr
							v-for="(item, index) in hljl"
							:class="{ 'red': item.hours>=18, 'pageNext': index!=0&&index%10==0 }">
							<td v-text="fDate(item.jlsj)">日期时间</td>
							<td v-text="item.ys">意识</td>
							<td v-text="item.tkDxZ==null ? '': item.tkDxZ+'/'+item.tkDxY==null ? '': item.tkDxY">左/右</td>
							<td v-text="item.tkGfyZ==null ? '': item.tkGfyZ+'/'+item.tkGfyY ==null ? '': item.tkGfyY">左/右</td>
							<td v-text="item.tw">°C</td>
							<td v-text="item.mb==0 ? '': item.mb">次/分</td>
							<td v-text="item.hx">次/分</td>
							<td v-text="item.xy">nnHg</td>
							<td v-text="item.xdjcXl">心率</td>
							<td v-text="item.xybhd">％</td>
							<td v-text="item.xy">L/min</td>
							<td v-text="item.rlMc">名称</td>
							<td v-text="item.rlTj">途经</td>
							<td v-text="item.rlSl">ml</td>
							<td v-text="item.clMc">名称</td>
							<td v-text="item.clSl">ml</td>
							<td v-text="item.clYsxz">颜色性状</td>
							<td colspan=3 v-text="item.pfqk">皮肤情况</td>
							<td colspan=3 v-text="item.glhl">管路护理</td>
							<td colspan=3 v-text="item.qt1">其它一</td>
							<td colspan=3 v-text="item.qt2">其它二</td>
							<td colspan=3 v-text="item.mb==0 ? '': item.mb">心率</td>
							<td colspan=3 v-text="item.bqgcjcs">病情观察及措施</td>
							<td colspan=3 v-text="item.zrhsxm">护士签名</td>
						</tr>
					</tbody>
					<tfoot>
						<tr>
							<td colspan="5" class="red text-left">红色为夜间记录</td>
							<td colspan="12"></td>
							<td colspan="20">护士长：&emsp;&emsp;&emsp;&emsp;&emsp;质控护士：</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</vue-scroll>
	</div>
</div>
<script type="text/javascript" src="wzhl-hld.js"></script>
</body>
</html>
