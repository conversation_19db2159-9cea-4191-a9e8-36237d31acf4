<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="pdsc">
    <div class="toolMenu toolMenu_0" style="display: block;">
        <button @click="add"><span class="fa fa-plus"></span>生成</button>
        <button @click="save"><span class="fa fa-save"></span>保存</button>
    </div>

    <!--数据录入区-->
    <div class="enter_tem1 enter">
        <!--库房下拉框-->
        <div class="KFSelect">
            <span>当前库房</span>
            <select v-model="pdbContent.kfbm" @change="opChange($event,'ksbm')">
                <option value="0">-请选择-</option>
                <option :value="item.kfbm" v-for="item in KFList" v-text="item.kfmc" :ksbm="item.ksbm"></option>
            </select>
        </div>

        <div class="enter_tem1_title">数据显示区</div>
        <div class="enter_tem1_item">
            <span style="width: 60px">盘点方式</span>
            <select v-model="selectPdfs" @change="selChange">
                <option v-for="option in options" :value="option.value">{{option.text}}</option>
            </select>
        </div>
        <!--物资种类下拉框-->
        <div class="enter_tem1_item ypzl" v-if="ypzlShow">
            <span>物资种类：</span>
            <select v-model="pdbContent.ypzl" @change="opChange($event,'zlbm')">
                <option :value="item.lbbm" v-for="item in infoList" v-text="item.lbmc"></option>
            </select>
        </div>
        <!--物资名称下拉框-->
        <div class="enter_tem1_item ypmc" v-if="ypmcShow">
            <span>物资名称：</span>
            <select v-model="pdbContent.ypmc" @change="opChange($event,'ypbm')">
                <option :value="item.wzbm" v-for="item in infoList" v-text="item.wzmc"></option>
            </select>
        </div>
        <div class="dashLine"></div>
        <!--盘点明细表-->
        <div class="table_tem2">
            <table>
                <tr>
                    <th>物资编码</th>
                    <th>物资名称</th>
                    <th>规格</th>
                    <th>种类</th>
                    <th>物资批号</th>
                    <th>库存数量</th>
                    <th>实存数量</th>
                    <th>单位</th>
                    <th>零价</th>
                    <th>零价金额</th>
                    <th>生产批号</th>
                    <th>生产日期</th>
                    <th>有效期至</th>
                    <th>分装比例</th>
                    <th>产地</th>
                    <th>供货单位</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <td v-text="item.wzbm"></td>
                    <td v-text="item.wzmc"></td>
                    <td v-text="item.wzgg"></td>
                    <td v-text="item.lbmc"></td>
                    <td v-text="item.xtph"></td>
                    <td v-text="item.kcsl"></td>
                    <td v-text="item.scsl"></td>
                    <td v-text="item.kfdwmc"></td>
                    <td v-text="item.dj"></td>
                    <td v-text="item.ljje"></td>
                    <td v-text="item.scph"></td>
                    <td v-text="item.scrq"></td>
                    <td v-text="fDate(item.yxqz,'date')"></td>
                    <td v-text="item.fzbl"></td>
                    <td v-text="item.cd"></td>
                    <td v-text="item.ghdwmc"></td>
                </tr>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript" src="pdsc.js"></script>