var toolMenu_2 = new Vue({
	el: '.toolMenu_2',
	mixins: [tableBase, baseFunc],
	data: {
		//查询参数
		param: {},
		//是否禁止提交
		isSubmited: false,
		ypjs: null,
		//凭证列表
		pzhList: [],
		//凭证号默认选项
		pdb: 0,
		//是否是新增盘点物资
		isAddNew: false
	},
	//加载启动
	mounted: function() {
		//获取判断凭证单号
		this.getWshpdb();
	},
	methods: {
		//物资检索
		searchYP: function() {
			this.param.parm = this.ypjs;
			this.物资();
		},
		//保存
		save: function() {
			//是否禁止提交
			if(this.isSubmited) {
				malert('数据提交中，请稍候！');
				return;
			}
			//非空判断
			if(enter_pdlrxs.jsonList.length == 0) {
				malert('没有可以保存的数据！');
				return;
			}
			//是否禁止提交
			this.isSubmited = true;
			//准备参数
			//pdbid在后台为pdbmxid，实存数量scsl在后台为录入数量lrsl
			for(var i = 0; i < enter_pdlrxs.jsonList.length; i++) {
				enter_pdlrxs.jsonList[i].pdbmxid = enter_pdlrxs.jsonList[i].pdbid;
				enter_pdlrxs.jsonList[i].lrsl = enter_pdlrxs.jsonList[i].scsl;
			}
			var json = {
				list: {
					'dj': this.pdb,
					'djmx': enter_pdlrxs.jsonList
				}
			};
			//发送请求
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfKfywPdblr&types=saveBatch', JSON.stringify(json))
				.then(function(data) {
					if(data.body.a == 0) {
						malert(data.body.c);
					} else {
						if(data.body.d == 0) {
							malert(data.body.c);
						} else {
							malert(data.body.c);
						}
					}
					//清空页面
					toolMenu_2.clearAll();
					toolMenu_2.pdb = 0;
					//是否禁止提交
					this.isSubmited = false;
				}, function(error) {
					console.log(error);
					//是否禁止提交
					this.isSubmited = false;
				});

		},
		//新增没有的物资
		addNew: function() {
			addNewDiv.isShow = true;

			//获取物资下拉框div
			var sDiv = document.getElementsByClassName("searchDiv");
			//设置新增属性为true
			//				this.isAddNew = true;
		},
		//自动生成
		autoGen: function() {
			if(this.pdb == 0) {
				malert('请选择凭证号！');
				return;
			}
			//获取凭证号对应的库存明细列表
			//准备参数
			this.param.pdpzh = this.pdb.pdpzh;
			this.param.wzkf = this.pdb.wzkf;
			//查询盘点表明细
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryMx&parm=' + JSON.stringify(this.param), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['scrq'] = formatTime(json.d[i]['scrq'], 'date');
						json.d[i]['yxqz'] = formatTime(json.d[i]['yxqz'], 'date');
						json.d[i]['ljje'] = Math.round(json.d[i]['yplj'] * json.d[i]['kcsl'] * 100) / 100;

					}
					for(var i = 0; i < json.d.length; i++) {
						json.d[i].scsl = json.d[i].kcsl;
					}
					enter_pdlrxs.jsonList = json.d;
					enter_pdlrlr.popContent = {};
				} else {
					malert(json.c)
				}

			});
		},
		//获取盘点表列表
		getWshpdb: function() {
			var kfbm = document.getElementById('_kfbm').value;
			if(kfbm == '') {
				malert('请选择库房');
				return;
			}
			var parm = {
				'wzkf': kfbm,
				'qrzfbz': 0
			};
			//查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryDj&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['pdrq'] = formatTime(json.d[i]['pdrq'], 'date');
					}
					toolMenu_2.pzhList = json.d;
				} else {
					malert('数据获取失败！')
				}
			});
		},
		//凭证号改变后清空录入区和显示去
		clearAll: function() {
			enter_pdlrlr.clear();
			enter_pdlrxs.clear();
		}
	},
});
//数据录入区
var enter_pdlrlr = new Vue({
	el: '.enter_pdlrlr',
	mixins: [tableBase, baseFunc, dic_transform, mformat],
	components: {
		'search-table': searchTable
	},
	data: {
		query: '', //查询类型，新增或添加已有
		page: [],
		pdWay: 0,
		popContent: {},
		dg: {
			page: 1,
			rows: 5,
			sort: "",
			order: "asc",
			parm: ''
		},

		them_tran: {},
		them: {
			'生产批号': 'scph',
			'物资编号': 'wzbm',
			'物资名称': 'wzmc',
			'库存数量': 'kcsl',
			'有效期至': 'yxqz',
			'规格': 'wzgg',
			'库房单位': 'kfdwmc',
			'效期': 'yxqz',
		}
	},
	methods: {
		//物资名称下拉table检索
		changeDown: function(event, type) {
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
			this.nextFocus(event);
		},
		//当输入值后才触发
		change: function(event, type) {
			if(toolMenu_2.pdb == 0) {
				malert("请先选择凭证号!");
				return;
			}
			enter_pdlrlr.query = '/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryMxByPage';
			if('addNew' == type) {
				var _searchEvent = $(event);
				enter_pdlrlr.query = '/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=';

			} else {
				var _searchEvent = $(event.target.nextElementSibling).eq(0);
			}
			//设置分页信息
			enter_pdlrlr.dg.page = 1;
			enter_pdlrlr.dg.rows = 5;
			var bean = toolMenu_2.pdb;
			bean.parm = this.popContent.wzmc;
			bean.sort = 'ypbm';
			bean.page = enter_pdlrlr.dg.page;
			bean.rows = enter_pdlrlr.dg.rows;
			//获取数据
			$.getJSON(enter_pdlrlr.query + "&parm=" + JSON.stringify(bean),
				function(data) {
					for(var i = 0; i < data.d.list.length; i++) {
						data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
						data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
					}
					enter_pdlrlr.page = data.d;
					enter_pdlrlr.searchCon = data.d.list;
					enter_pdlrlr.total = data.d.total;
					enter_pdlrlr.selSearch = 0;
					if(data.d.list.length != 0) {
						$(".searchDiv").hide();
						_searchEvent.show()
					} else {
						$(".searchDiv").hide();
					}
				});
		},
		//选择一条记录
		selectOne: function(item) {
			//查询下页
			if(item == null) {
				var bean = toolMenu_2.pdb;
				bean.parm = this.popContent.ypmc;
				bean.sort = 'ypbm';
				bean.page = enter_pdlrlr.dg.page + 1;
				bean.rows = enter_pdlrlr.dg.rows;

				$.getJSON(enter_pdlrlr.query + JSON.stringify(enter_pdlrlr.dg) + "&parm=" + JSON.stringify(bean),
					function(data) {
						if(data.a == 0) {
							for(var i = 0; i < data.d.list.length; i++) {
								enter_pdlrlr.searchCon.push(data.d.list[i]);
							}
							enter_pdlrlr.total = data.d.total;
							enter_pdlrlr.selSearch = 0;
						} else {
							malert('分页信息获取失败')
						}

					});
				return;
			}
			this.popContent = item;
			$(".searchDiv").hide();
		},
		//添加物资记录
		add: function() {
			if(this.popContent.wzmc == null) {
				malert('请输入物资！');
				return;
			}
			if(this.popContent.kcsl == null || this.popContent.scsl == null) {
				malert('请输入物资数量！');
				return;
			}
			//将数据加入录入区
			enter_pdlrxs.jsonList.push(this.popContent);
			this.popContent = {};
			$("#ypmc").focus();
		},
		//清空数据录入区
		clear: function() {
			if(this.popContent != null) {
				this.popContent = {};
			}
		},
	},
});
//数据显示区
var enter_pdlrxs = new Vue({
	el: '.enter_pdlrxs',
	mixins: [tableBase],
	data: {
		//操作行号
		rowNum: 0,
		//显示区数据列表
		jsonList: [],
		//是否可修改实际存数量
		modiScsl: false,
	},
	methods: {
		//获取已选择的记录编号
		getIndex: function(num) {
			this.rowNum = num;
		},
		//清空数据录入区
		clear: function() {
			if(this.jsonList.length != 0) {
				this.jsonList = [];
			}
		},
		del: function() {
			//删除已选择的记录
			enter_pdlrxs.jsonList.splice(enter_pdlrxs.rowNum, 1);
			malert('已删除')
		},
		//修改实存数量
		modify: function(val) {
			if(val.path[0].disabled) {
				val.path[0].disabled = false;
			} else {
				val.path[0].disabled = true;
			}
		}
	}
});

var addNewDiv = new Vue({
	el: "#_addNew",
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data: {
		ghdwList: [], //供货单位
		isShow: false,
		addContent: {},
		dg: {
			page: 1,
			rows: 5,
			sort: "",
			order: "asc",
			parm: ''
		},
		them_tran: {},
		them: {
			'生产批号': 'scph',
			'物资编号': 'wzbm',
			'物资名称': 'wzmc',
			'库存数量': 'kcsl',
			'有效期至': 'yxqz',
			'规格': 'wzgg',
			'分装比例': 'fzbl',
			'进价': 'ypjj',
			'零价': 'yplj',
			'库房单位': 'kfdwmc',
			'药房单位': 'yfdwmc',
			'效期': 'yxqz',
			'物资剂型': 'jxmc'
		}
	},

	mounted: function() {
		//初始化页面记载供货单位
		var ghdwParm = {
			page: 1,
			rows: 5000,
			sort: "dwbm",
			order: "asc",
			parm: ''
		};
		$.getJSON("/actionDispatcher.do?reqUrl=YkglKfwhGhdw&types=query&json=" + JSON.stringify(ghdwParm),
			function(json) {
				if(json.a == 0) {
					addNewDiv.ghdwList = json.d.list;
				} else {
					malert("供货单位获取失败");
				}
			});
	},
	methods: {
		//保存新增药品
		addNewYp: function() {
			//判断录入数据是否填全
			var inputArr = document.getElementsByClassName("_addData");
			for(var i = 0; i < inputArr.length; i++) {
				if(inputArr[i].value == null || inputArr[i].value == '') {
					inputArr[i].className = 'emptyError';
					malert('数据未输入!');
					return;
				}
			}
			//添加凭证号
			this.addContent.pdpzh = toolMenu_2.pdb.pdpzh;
			//准备参数
			this.addContent.scsl = 0;//设置默认实存数量
			var json = {
				'list': {
					'dj': toolMenu_2.pdb,
					'djmx': [this.addContent],
				}
			}
			//将数据加入录入区
			enter_pdlrxs.jsonList.push(this.addContent);
			this.addContent = {};
			//保存新增物资到盘点明细列表
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=addNew', JSON.stringify(json))
				.then(function(data) {
					if(data.body.a == 0) {
						malert(data.body.c);
						enter_pdlrxs.jsonList = [];
					} else {
						if(data.body.d == 0) {
							malert(data.body.c);
						} else {
							malert(data.body.c);
						}
					}

				}, function(error) {
					console.log(error);
				});
			//关闭弹出框
			this.isShow = false;
		},

		clear: function() {
			if(this.addContent != null) {
				this.addContent = {};
			}
		},

		close: function() {
			addNewDiv.isShow = false;
		},
		//物资名称下拉table检索
		changeDownAdd: function(event) {
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			var isReq = this.keyCodeFunction(event, 'addContent', 'searchCon');
		},
		//当输入值后才触发
		changeAdd: function(event) {
			if(toolMenu_2.pdb == 0) {
				malert("请先选择凭证号!");
				return;
			}
			enter_pdlrlr.query = '/actionDispatcher.do?reqUrl=WzkfXtwhwzzd&types=query&dg=';
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			var bean = toolMenu_2.pdb;
			bean.parm = this.addContent.ypmc;
			bean.sort = 'ypbm';
			enter_pdlrlr.dg.page = 1;
			enter_pdlrlr.dg.rows = 5;
			$.getJSON(enter_pdlrlr.query + JSON.stringify(enter_pdlrlr.dg) + "&json=" + JSON.stringify(bean),
				function(data) {
					for(var i = 0; i < data.d.list.length; i++) {
						data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
						data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
					}
					addNewDiv.searchCon = data.d.list;
					addNewDiv.total = data.d.total;
					addNewDiv.selSearch = 0;
					if(data.d.list.length != 0) {
						$(".searchDiv").hide();
						_searchEvent.show()
					} else {
						$(".searchDiv").hide();
					}
				});
		},
		//选择一条记录
		selectOneAdd: function(item) {
			if(item == null) {
				var bean = toolMenu_2.pdb;
				bean.parm = this.addContent.ypmc;
				bean.sort = 'ypbm';
				enter_pdlrlr.dg.page++;
				$.getJSON(enter_pdlrlr.query + JSON.stringify(enter_pdlrlr.dg) + "&parm=" + JSON.stringify(bean),
					function(data) {
						if(data.a == 0) {
							for(var i = 0; i < data.d.list.length; i++) {
								addNewDiv.searchCon.push(data.d.list[i]);
							}
							addNewDiv.total = data.d.total;
							addNewDiv.selSearch = 0;
						} else {
							malert('分页信息获取失败')
						}
					});
				return;
			}
			this.addContent = item;
			$(".searchDiv").hide();
		},

	}
});

//监听记账项目检索外的点击事件 ，自动隐藏.searchDiv
$(document).mouseup(function(e) {
	var bol = $(e.target).parents().is(".searchDiv");
	if(!bol) {
		$(".searchDiv").hide();
	}
})