    //顶部工具栏
    var tool=new Vue({
        el:'.wrapper',
        mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
        data:{
            complyShow:true,
            userName:null,
            jsonList : [],
            num:0,
            totlePage : 0
        },
        mounted: function () {
            laydate.render({
                elem: '#timeVal',
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    tool.param.beginrq = value;
                    tool.getData();
                }
            });
            laydate.render({
                elem: '#timeVal1',
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    tool.param.endrq = value;
                    tool.getData();
                }
            });
            this.getKsbm();
            this.getUserName();
        },
        methods:{
        	getUserName:function(){
        		this.userName=JSON.parse(sessionStorage.getItem("userName"+userId));
        		console.log(this.userName);
        	},
            //切换
            tabBg: function (index) {
                this.num = index;
                switch (this.num){
                    case 0:
                        this.complyShow=true;
                        this.getData();
                        break;
                    case 1:
                        this.complyShow=false;
                        this.getData();
                        break;
                    default:
                        break;
                }
            },
			//获取当前操作员的拥有科室权限 
			        getKsbm: function () {
			            common.openloading('.wrapper');
			            var str_param = {
			                ylbm: "N030012004"
			            };
			            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(str_param), function (json) {
							common.closeLoading()
			                if (json.a == 0 && json.d!=null) {
			                    if (json.d.length > 0) {
			                        tool.param.ksbm=json.d[0].ksbm;
			                        setTimeout(function () {
			                            tool.getData();
			                        }, 100);
			
			                    }else{
									tool.param.ksbm='';
									setTimeout(function () {
									    tool.getData();
									}, 100);
								}
			                } else {
			                    malert('获取科室失败','top','defeadted')
			                }
			            });
			        },
            //刷新操作
            getData:function () {
            	  var parm={
                  		beginrq:this.param.beginrq,
                  		endrq:this.param.endrq,
                  		zxbz:this.num,
                  		brxm:this.param.parm,
                  		page:this.param.page,
                  		rows:this.param.rows,
						zxks : tool.param.ksbm,
                    }
                    $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryJy&parm=" + JSON.stringify(parm), function (json) {
                        if (json.a == 0) {
                      	  tool.totlePage = Math.ceil(json.d.total / tool.param.rows);
                            tool.jsonList = json.d.list;
                        } else {
                            malert(json.c+",查询失败：",'top','defeadted');
                        }
                    });
            },
            //执行操作
            Comply:function () {
            	var zxlist=[];
                for (var i = 0; i < this.isChecked.length; i++) {
                         if (this.isChecked[i] == true) {
                             this.jsonList[i].zxbz="1";
                             this.jsonList[i].zxsj=new Date();
                             this.jsonList[i].zxry=tool.userName;
                        	 zxlist.push(JSON.parse(JSON.stringify(this.jsonList[i])));
                         }
                     }
                if (zxlist.length == 0) {
                    malert('请选中您要退费的数据','top','defeadted');
                         return;
                     }
                var json = '{"list":' + JSON.stringify(zxlist) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=jyzxUpdate', json)
                        .then(function (data) {
                            if (data.body.a == 0) {
                            	tool.isChecked=[];
                                tool.isCheckAll=false;
                            	malert("执行成功！",'top','success');
                            	tool.getData();
                            } else {
                                malert(data.body.c+",执行失败",'top','defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });
            },
            //取消执行操作
            CancelComply:function () {
            	var qxzxlist=[];
                for (var i = 0; i < this.isChecked.length; i++) {
                         if (this.isChecked[i] == true) {
                             this.jsonList[i].zxbz="0";
                             this.jsonList[i].zxsj=zxsj;
                             this.jsonList[i].zxry=tool.userName;
                        	 qxzxlist.push(JSON.parse(JSON.stringify(this.jsonList[i])));
                         }
                     }
                if (qxzxlist.length == 0) {
                    malert('请选中您要退费的数据','top','defeadted');
                         return;
                     }
                var json = '{"list":' + JSON.stringify(qxzxlist) + '}';
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=jyzxUpdate', json)
                        .then(function (data) {
                            if (data.body.a == 0) {
                            	tool.isChecked=[];
                                tool.isCheckAll=false;
                            	malert("取消执行成功！",'top','success');
                            	tool.getData();
                            } else {
                                malert(data.body.c+",取消执行失败",'top','defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });
            }

        }
    });






