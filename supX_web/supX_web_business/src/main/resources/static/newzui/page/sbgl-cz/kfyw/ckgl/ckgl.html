<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ckgl.css" rel="stylesheet"/>
</head>

<body class="skin-default">
<div class="background-box">
    <div class="wrapper" id="wrapper" >
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="openNewPage()">设备下发</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
                <!--<button class="tong-btn btn-parmary-b icon-xz1 paddr-r5" @click="LingYaoD()">申领单</button>-->
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label ">库房</label>
                    	<div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="kfList" :index="'sbkfmc'" :index_val="'sbkfbm'" :val="param.sbkf"
                                      :name="'param.sbkf'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    	</div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">时间段</label>
                        <div class="position margin-f-l10 flex-container flex-align-c">
                            <input class="zui-input  wh200 " v-model="param.beginrq" placeholder="请选择申请日期" id="timeVal"/>
                            <span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input  wh200 " v-model="param.endrq" placeholder="请选择处方结束时间" id="timeVal1" />
                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline margin-f-l25">
                            <input class="zui-input wh180" placeholder="请输入关键字" v-model="param.pram" type="text" @keydown.enter="goToPage(1)"/>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view  " v-cloak   >
            <!--入库列表-->
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>下发单号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>下发方式</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>领用人</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>制单日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body ">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList" :tabindex="$index">
                        <td class=" cell-m">
                            <div class="zui-table-cell" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.ckdh">出库单号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="ckfs[item.ckfs]">出库方式</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.lyrmc">领用人</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zdrmc">制单员</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,datetime)">制单日期</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bzms">备注</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                            	<i v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-wtg' : item.shzfbz=='3' ? 'color-yzf':'' "></i>
                            </div>
                        </td>
                        <td class=" cell-l">
                            <div class="zui-table-cell cell-l">
                                <span class="flex-center padd-t-5">
                                 <em class="width30" v-if="item.shzfbz == 0">
                                    <i class="icon-sh" data-title="审核"  @click="sh($index)"></i>
                                </em>
                                    <!---->
                                <em  class="width30" v-if="item.shzfbz==0">
                                    <i class="icon-js" data-title="作废"  @click="Refuse($index)"></i>
                                </em>
                                <em class="width30" v-if="item.shzfbz !=0">
                                    <i class="icon-yl" data-title="明细"  @click="sh($index)"></i>
                                </em>
                               </span>

                            </div>
                        </td>

                        <!--暂无数据提示,绑数据放开-->
                        <p v-show="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>

    <div class="side-form " v-cloak :class="{'ng-hide':nums==1}" id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span>科室申领单</span>
            <span class="fr closex ti-close" @click="nums=1"></span>
        </div>
        <div class="ksys-side">
            <div class="flex-container padd-b-10 flex-align-c">
                <span class="font-14 padd-r-5">领用科室</span>
                    <select-input  class="wh112" @change-data="resultChange"
                                  :not_empty="false" :child="ksList"
                                  :index="'ksmc'" :index_val="'ksbm'"
                                  :val="param.ksbm" :search="true" :name="'param.ksbm'"
                                  :index_mc="'ksmc'">
                    </select-input>
            </div>
            <div class="zui-table-view w100 flex-container">
                  <div class="w50">
                      <div class="zui-table-header">
                          <table class="zui-table table-width50" >
                              <thead>
                              <tr>
                                  <th class="cell-m">
                                      <div class="zui-table-cell cell-m"><span>序号</span></div>
                                  </th>
                                  <th>
                                      <div class="zui-table-cell cell-s"><span>出库单号</span></div>
                                  </th>
                                  <th>
                                      <div class="zui-table-cell text-left cell-s"><span>制单时间</span></div>
                                  </th>
                                  <th>
                                      <div class="zui-table-cell cell-s"><span>制单员</span></div>
                                  </th>
                              </tr>
                              </thead>
                          </table>
                      </div>
                      <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                          <table class="zui-table table-width50" v-if="lydList.length!=0">
                              <tbody>
                              <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                  @mouseenter="switchIndex('hoverIndex',true,$index)"
                                  @mouseleave="switchIndex()"
                                  v-for="(item, $index) in lydList"
                                  @click="getMx(item),switchIndex('activeIndex',true,$index)">
                                  <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                                  <td ><div class="zui-table-cell cell-s" v-text="item.sldh"></div></td>
                                  <td ><div class="zui-table-cell cell-s title text-left" v-text="fDate(item.zdrq,'datetime')"></div></td>
                                  <td ><div class="zui-table-cell cell-s title" v-text="item.zdr"></div></td>
                              </tr>
                              </tbody>
                          </table>
                          <p v-if="lydList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                      </div>
                  </div>
                <div class="w50 margin-l-10">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50" >
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>设备名称</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell  cell-s"><span>设备编码</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>设备规格</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>领用数量</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                        <table class="zui-table table-width50" v-if="lymxList.length!=0">
                            <tbody>
                            <tr :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                                @mouseenter="switchIndex('hoverIndex1',true,$index)"
                                @mouseleave="hoverMouse()"
                                v-for="(item, $index) in lymxList"
                                @click="switchIndex('activeIndex1',true,$index)">
                                <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                                <td ><div class="zui-table-cell cell-s" v-text="item.sbmc"></div></td>
                                <td ><div class="zui-table-cell cell-s title " v-text="item.sbbm"></div></td>
                                <td ><div class="zui-table-cell cell-s title" v-text="item.sbgg"></div></td>
                                <td ><div class="zui-table-cell cell-s title" v-text="item.slsl"></div></td>
                            </tr>
                            </tbody>
                        </table>
                        <p v-if="lymxList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="ksys-btn" style="position:absolute">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="nums=1">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="tjCom">提交</button>
        </div>
    </div>
</div>

<script src="ckgl.js"></script>
</body>

</html>
