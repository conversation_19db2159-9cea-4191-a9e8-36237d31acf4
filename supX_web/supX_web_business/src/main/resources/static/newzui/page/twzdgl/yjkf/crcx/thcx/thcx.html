<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>退货查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
 <body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
 <div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel ">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药库</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label   margin-f-l10">时间段</label>
                    <div class="zui-input-inline   margin-f-l20">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh240 text-indent20" placeholder="请选择申请日期" id="timeVal"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label   margin-f-l10">检索</label>
                    <div class="zui-input-inline margin-f-l35">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="param.parm" @keydown.13="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view "  z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>出库单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>审核时间</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :tabindex="$index" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex}]">
                    <td class="cell-m"><div class="zui-table-cell" v-text="$index+1">001</div></td>
                    <td>
                        <div class="zui-table-cell cell-l">
                            <div class="title" :data-title="item.ckdh"><i v-text="item.ckdh"></i></div>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.shrq,'date')">西药收入</div></td>
                    <td>
                        <div class="zui-table-cell cell-xl">
                            <div class="title" :data-title="item.ypmc"><i v-text="item.ypmc"></i></div>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.ypgg">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cksl">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.scph">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cdmc">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbl">药品收入</div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
 </div>

<script src="thcx.js"></script>
</body>

</html>
