<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!--<meta http-equiv="refresh" content="20">-->
    <title>接诊管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" type="text/css" href="/newzui/css/icon.css">
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="lgbr.css" rel="stylesheet">
</head>
<body class="skin-default background-f" id="jzgl">
<div class="wrapper flex-one flex-container flex-dir-c">
    <div class="panel  " v-cloak>
        <div class="tong-top flex-jus-sb">
            <div>
                <button v-waves class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-t-10 flex-wrap-w">
            <div class="flex-container flex-align-c padd-l-10">
                <span class="ft-14 padd-r-5">当前科室</span>
                <select-input :search="true" style="width: 110px;" @change-data="ksChange" :not_empty="false"
                              :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="param.lgks"
                              :name="'param.lgks'">
                </select-input>
            </div>
            <!-- <div class="flex-container flex-align-c padd-l-10">
                <span class="ft-14 padd-r-5">病人过滤</span>
                <select-input :search="true" style="width: 90px;" @change-data="Wf_GetBrList" :not_empty="false"
                              :child="mzbrgl_tran" :index="param.brgl" :val="param.brgl"
                              :name="'param.brgl'">
                </select-input>
            </div> -->
            <div class="flex-container flex-align-c padd-l-10">
                <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                <div class="  flex-container flex-align-c">
                    <input class="zui-input todate wh160 " placeholder="请选择处方开始时间" id="timeVal"/><span
                        class="padd-l-5 padd-r-5">~</span>
                    <input class="zui-input todate wh160 " placeholder="请选择处方结束时间" id="timeVal1"/>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-l-10 ">
                <span class="ft-14 padd-r-5">检索</span>
                <input class="zui-input wh150" placeholder="请输入关键字" type="text" v-model="param.parm"
                       @keyup.enter="Wf_GetBrList(String)"/>
            </div>
        </div>
    </div>
    <div class="col-x-12 kp flex-container flex-dir-c flex-one" v-cloak align="center">
        <div class="kpFlex" ref="kp" @mousewheel="loadingData($event)">
            <div class=" position userWidth" style="margin:5px 9px 10px 9px;width: 307px;display: inline-block;"
                 v-for="list in Brxx_List" v-if="Brxx_List.length!=0">
                <div :class="list.ghks=='0922'?(list.sfqj?(list.sfqj=='1'?'header-text-red':'header-text-yellow'):'header-text-green'):'header-text'" class="flex-container" >
                        <span class="userNameImg">
                                <img v-show="list.nljd==1" src="/newzui/pub/image/maleBaby.png">
                                <img v-show="list.nljd==2" src="/newzui/pub/image/femalebaby.png">
                                <img v-show="list.nljd==3" src="/newzui/pub/image/Group <EMAIL>">
                                <img v-show="list.nljd==4" src="/newzui/pub/image/Group <EMAIL>">
                                <img v-show="list.nljd==5" src="/newzui/pub/image/juvenile.png">
                                <img v-show="list.nljd==6" src="/newzui/pub/image/maid.png">
                                <img v-show="list.nljd==7" src="/newzui/pub/image/youth.png">
                                <img v-show="list.nljd==8" src="/newzui/pub/image/woman.png">
                                <img v-show="list.nljd==9" src="/newzui/pub/image/grandpa.png">
                                <img v-show="list.nljd==10" src="/newzui/pub/image/grandma.png">
                                <img v-show="list.nljd==11" src="/newzui/pub/image/<EMAIL>">
                            </span>
                    </span>
                    <div>
                        <p class="padd-t-10 padd-b-5 text-left"
                           style="display: flex;justify-content: start;align-items: center;">
                        <span class="userName relative" @click="userGet(list,['brPage/brjz',0,list])"
                              v-if="list.brxm.length>=5"><i class="title title-width" :data-title="list.brxm"
                                                            v-text="list.brxm"></i></span>
                            <span class="userName relative" @click="userGet(list,['brPage/brjz',0,list])" v-else
                                  v-text="list.brxm"></span>
                            <span class="userName-pin" v-show="list.pkhbz==0"><img
                                    src="/newzui/pub/image/pin.png"></span>
                            <span class="userName-lc color-7f">
                            <i class="color-dlr padd-r-5" v-text="brxb_tran[list.brxb]" v-if="list.brxb==1"></i>
                            <i style="color:#fa6969" class="padd-r-5" v-text="brxb_tran[list.brxb]" v-else></i>
                            <span v-text="list.brnl"></span><span v-text="nldw_tran[list.nldw]"></span>
                        </span>
                        </p>
                        <p class="text-left color-7f">挂号序号：<span v-text="list.ghxh"></span></p>
                        <p class="text-left color-7f">病区：<span v-if="list.sfqj==='0'">留观区</span>
                            <span v-else-if="list.sfqj==='1'">抢救区</span>
                            <span v-else>诊断区</span></p>
                        <p class="text-left color-7f">疾病名称：<span v-text="list.jbmc"></span></p>
                    </div>
                    <span class="djzt" v-if="list.zt==0"></span>
                    <span class="jzztb" v-if="list.zt==1"></span>
                    <span v-if="list.zt==2"></span>
                </div>
                <div class="footer-text">
                    <!-- <span  class="foot-span" title="叫号" @click="jhcz(list)" v-if="list.zt==0">叫号</span> -->
                    <!-- <span class="foot-span" v-if="list.zt==0"  @click="userGet(list,['brPage/brjz',0,list])" title="完成" v-text="list.wcbz==0? '接诊':'完成'"></span> -->
                    <!-- <span class="foot-span" @click="userGet(list,['brPage/jcjy',5,list])"  title="检查检验">检查检验</span> -->
                    <!-- <span class="foot-span" title="报告" v-if="list.zt==0"><i>报告</i><i style="font-size: 12px;">(<em  class="color-green">0/0</em>)</i></span>
                    <span class="foot-span foot-rf" title="报告" v-if="list.zt==1 || list.zt==2"><i>报告(<em class="color-green">0/0</em>)</i></span>
                     -->
                    <span class="foot-span" @click="userGetDz(list,['brPage/dzcf',0 ,list])"
                          title="电子处方">电子处方</span>
                    <span @click="userGet1(list,['brPage/lgdj',0 ,list])" title="留观">留观</span>
                    <span @click="userdzbl(list)" title="写病历">写病历</span>
                    <!-- <span  @click="openUrl(list)" title="检查报告单">检查报告单</span> -->
                    <span v-if="isShow" @click="sxzz(list)" title="转出">转出</span>
                </div>

            </div>
            <div class="" style="margin: 5px 9px 10px 9px;width: 307px;display: inline-block;"
                 v-for="zlist in brk_listD" v-if="Brxx_List.length!=0"></div>
            <div v-if="Brxx_List.length!=0 && !isDoneCb " class=" ysb-green text-center"
                 style="width: 100%;margin:30px 0">{{loadData}}
            </div>
            <p class="noData  text-center " v-if="Brxx_List.length==0" style="width: 100%;"> 暂无数据</p>
        </div>
    </div>
</div>
<script type="text/javascript" src="lgbr.js"></script>
</body>
</html>
