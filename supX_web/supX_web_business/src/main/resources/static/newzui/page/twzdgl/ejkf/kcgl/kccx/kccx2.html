<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>库存查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="kccx.css" rel="stylesheet">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon">
        <div class="panel" v-cloak>
            <div class="tong-top">
                <button class="tong-btn btn-parmary " @click="exportKc"><i class="icon-width icon-dc-b "></i>导出</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
            <div class="tong-search">
                <div class="flex-container flex-align-c padd-b-10">
                    <div class="flex-container padd-r-10 flex-align-c ">
                        <span class="ft-14 padd-r-5">药房</span>
                        <select-input @change-data="resultRydjChange" class=" wh122 margin-l-5" :child="yfkfList" :index="'yfmc'"
                                      :index_val="'yfbm'" :val="param.yfbm" :name="'param.yfbm'" :search="true"
                                      :index_mc="'yfmc'">
                        </select-input>
                    </div>
                    <div class="flex-container padd-r-10 flex-align-c">
                        <span class="ft-14 padd-r-5">查询方式</span>
                        <select-input class=" wh122 margin-l-5" @change-data="resultRydjChange" :child="options" :index="'fsmc'"
                                      :index_val="'cxfs'" :val="param.cxfs" :name="'param.cxfs'" :search="true"
                                      :index_mc="'fsmc'">
                        </select-input>
                    </div>
                    <div class="flex-container padd-r-10 flex-align-c">
                        <span class="ft-14 padd-r-5">边界筛选</span>
                        <select-input class=" wh122 margin-l-5" @change-data="resultChangeXl" :child="cxbjlist" :index="'cxbj'"
                                      :index_val="'cxbj'" :val="param.cxbj" :name="'param.cxbj'" :search="true" :index_mc="'cxbj'">
                        </select-input>
                    </div>
                    <div class="flex-container padd-r-10 flex-align-c">
                        <span class="ft-14 padd-r-5">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text" v-model="param.parm" />
                    </div>
                    <div class="flex-container flex-align-c">
                        <span class="color-wtg font-18">库存总额： {{totalAmount}} &ensp;元</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-r-10 padd-l-10" v-cloak>
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l text-left"><span>药品名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>单位</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>大库存</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>小库存</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>剂型</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品种类</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>药品产地</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>库存数量</span></div>
                        </th>
                        <!-- <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>实际库存</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>在途库存</span></div>
                        </th> -->
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>药品进价</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>药品零价</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>零价金额</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>药品批号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>停用状态</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                        </th>

                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-l"><span>入库日期</span></div>
                        </th>
                        <th v-if="ysbShow">
                            <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                        </th>

                        <th>
                            <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body loadingTable" id="zui-table" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                        :tabindex="$index" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'red':Number(item.kcsl)<Number(item.zdkc)}]">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" :class="{'color-wtg':item.kcsl=='0'}">
                                {{$index+1}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.ypbm}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l text-left" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.ypmc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.yfdwmc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.dkc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.xkc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.ypgg}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.jxmc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.ypzlmc}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.cdmc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.kcsl}}
                            </div>

                        </td>
                        <!-- <td v-if="ysbShow">
                             <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                 {{item.sjkc}}
                             </div>
                         </td>
                         <td v-if="ysbShow">
                             <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                 {{item.kcsl-item.sjkc}}
                             </div>
                         </td> -->
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{fDec(item.ypjj,4)}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{fDec(item.yplj,4)}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{fDec(item.yplj*item.kcsl,4)}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.scph}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" >
                                {{item.pcty == 0 ? '启用' : '停用'}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{fDate(item.yxqz,'date')}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-l" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.rksj}}
                            </div>
                        </td>
                        <td v-if="ysbShow">
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.pydm}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" :class="{'color-wtg':item.kcsl=='0'}">
                                {{item.fzbl}}
                            </div>
                        </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>

            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()" :tabindex="$index" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'red':Number(item.kcsl)<Number(item.zdkc)}]">
                            <td>
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--end-->
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<!--侧边窗口-->
<div class="side-form  pop-width" v-cloak :class="{'ng-hide':type}"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
            <span class="span0">
                <i>有效期</i>
                <input type="text" class="zui-input border-r4 ytimes"
                       v-model="popContent.yxqz" @keydown="nextFocus($event)" id="_xxq" />
            </span>
        <span class="span0">
                <i>批次停用</i>
                 <select-input
                         @change-data="resultChange"
                         :not_empty="false"
                         :child="stopSign"
                         :index="popContent.pcty"
                         :val="popContent.pcty"
                         :name="'popContent.pcty'" >
                  </select-input>
            </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>

<script src="kccx.js"></script>
</body>

</html>
