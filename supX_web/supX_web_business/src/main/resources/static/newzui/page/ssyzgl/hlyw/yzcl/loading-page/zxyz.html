<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="zxyz.css">
</head>

<body class="skin-default">
    <div id="loadingPage" v-cloak style="height: 100%;">
        <div id="wrapper" class="wrapper no-scrollbar" @scroll="scrollFn">
            <div id="hzlist-box" :style="hzListBoxStyle">
                <div v-for="(brListItem, brListIndex) in showBrInfoList" :key="brListItem.zyh" class="bryz-list" :class="{'margin-b-10': yzzxInfoList.length > 1}">
                    <header class="userNameBg">
                        <div class="flex">
                            <div class="userNameImg">
                                <span>
                                    <img :src="userPhoto[ brListItem.nljd - 1 ]">
                                </span>
                            </div>
                            <div class="text-color">
                                <p class="userHeader">
                                    <span class="userName" v-text="brListItem.brxm"></span>
                                    <span class="sex text" v-text="brxb_tran[brListItem.brxb]"></span>
                                    <span class="nl text">{{brListItem.nl}}{{xtwhnldw_tran[brListItem.nldw]}}</span>
                                </p>
                                <div class="userCwh">
                                    <span class="cwh text">{{ '床位号：'+ (brListItem.rycwbh || '无') }}</span>
                                    <span class="zyh text">{{ '住院号：'+ (brListItem.brzyh || '无') }}</span>
                                    <span class="bq text">{{ '科室：'+ (brListItem.ryksmc || '无') }}</span>
                                    <span class="ys text">{{ '医师：'+ (brListItem.zyysxm || '无') }}</span>
                                    <span class="brzt text" v-text="'病人状态：'+zyzt_tran[brListItem.zyzt]"></span>
                                    <span class="bz text">{{ '病种：'+ (brListItem.ryzdmc || '无') }}</span>
                                </div>
                                <div>
                                    <p class="heaf text">更多详细信息>></p>
                                </div>
                            </div>
                        </div>
                    </header>

                    <div class="zui-table-view">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m">
                                                <input class="green" type="checkbox" v-model="brListItem.isCheckAll">
                                                <label @click="reCheckBoxZx(brListIndex)"></label>
                                            </div>
                                        </th>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                        <th class="cell-s">
                                            <div class="zui-table-cell cell-s"><span>已执行</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl text-left"><span>医嘱内容</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>剂量单位</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>用量单位</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl text-left"><span>用药方法</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xxl text-left"><span>说明</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                        </th>
                                        <!-- <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>操作</span></div>
                            </th>-->
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" :data-no-change="yzzxInfoList.length>1" @scroll="scrollTable($event)">
                            <p v-if="!brListItem.yzxx.length" class=" noData  text-center zan-border">暂无数据...</p>
                            <table v-if="brListItem.yzxx.length" class="zui-table table-width50">
                                <tbody>
                                    <tr v-for="(yzListItem,$index) in brListItem.yzxx" :tabindex="$index" @click="reCheckBoxZx(brListIndex,$index)"
                                        :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index===hoverIndex&&brListIndex===hoverBrListIndex,'yzxyz':yzListItem.numb>0||yzListItem.tsyz==='1'}]"
                                        @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                        @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')" class="tableTr2"
                                        ref="list">
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m">
                                                <input class="green" type="checkbox" v-model="yzListItem.isChecked">
                                                <label @click="checkSelectZx(brListIndex,$index)"></label>
                                            </div>
                                        </td>
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                        </td>
                                        <td class="cell-s">
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.numb"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-left title text-over-2" v-text="yzListItem.xmmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-left title" v-text="yzListItem.yyffmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xxl text-left title" v-text="yzListItem.yssm"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysqmxm"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                        </td>
                                        <!--<td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <span data-title="执行" class="icon-width butt-hover icon-zhixing"></span>
                                </div>
                            </td>-->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--左侧固定-->
                        <div class="zui-table-fixed table-fixed-l">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                        <tr>
                                            <th class="cell-m">
                                                <div class="zui-table-cell cell-m">
                                                    <input class="green" type="checkbox" v-model="brListItem.isCheckAll">
                                                    <label @click.stop="reCheckBoxZx(brListIndex)"></label>
                                                </div>
                                            </th>
                                            <th class="cell-m">
                                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                                            </th>
                                            <th class="cell-s">
                                                <div class="zui-table-cell cell-s"><span>已执行</span></div>
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTableFixed($event)" :data-no-change="yzzxInfoList.length>1">
                                <table class="zui-table">
                                    <tbody>
                                        <tr v-for="(yzListItem,$index) in brListItem.yzxx" :tabindex="$index"
                                            @click="reCheckBoxZx(brListIndex,$index)" :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index===hoverIndex&&brListIndex===hoverBrListIndex,'yzxyz':yzListItem.numb>0||yzListItem.tsyz==='1'}]"
                                            @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                            @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')" class="tableTr2">
                                            <td class="cell-m">
                                                <div class="zui-table-cell cell-m">
                                                    <input class="green" type="checkbox" v-model="yzListItem.isChecked">
                                                    <label @click.stop="checkSelectZx(brListIndex,$index)"></label>
                                                </div>
                                            </td>
                                            <td class="cell-m">
                                                <div class="zui-table-cell cell-m" v-text="$index+1">
                                                    <!--序号-->
                                                </div>
                                            </td>
                                            <td class="cell-s">
                                                <div class="zui-table-cell cell-s" v-text="yzListItem.numb"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <!--右侧固定-->
                        <!--<div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr class="cell-m">
                                <th><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)" :data-no-change="yzzxInfoList.length>1">
                        <table class="zui-table">
                            <tbody>
                            <tr  v-for="(yzListItem,$index) in brListItem.yzxx" :tabindex="$index"
                                @click="reCheckBoxZx(brListIndex,$index)"
                                :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index===hoverIndex&&brListIndex===hoverBrListIndex}]"
                                @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')"
                                class="tableTr2">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m"><span data-title="执行"  class="icon-width butt-hover icon-zhixing"></span></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>-->
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部按钮区域 begin -->
        <div class="ksys-btn  action-bar fixed">
            <span v-if="yzzxInfoList.length>1" style="flex: 1;">
                <input class="green" type="checkbox" v-model="isOver">
                <label @click.stop="isOverClick(!isOver)">全选</label>
            </span>
            <button v-waves class="zui-btn btn-primary xmzb-db qxsh" @click="closePage">关闭</button>
            <button  v-waves v-show="brList.length = 1" class="zui-btn btn-primary xmzb-db qxsh" @click="qxshenhe">取消审核</button>
            <!--<button v-show="brList.length = 1" class="zui-btn btn-primary xmzb-db syt" @click="">输液贴</button>-->
            <button v-waves class="zui-btn btn-primary xmzb-db zx" @click="zhixing">执行</button>
            <!--<button class="zui-btn btn-primary xmzb-db dy" @click="">打印</button>-->
        </div>
        <!-- 底部按钮区域 end -->
    </div>

    <!-- 执行弹窗 begin -->
    <div class="pop openConfirm printHide" id="tspop" v-cloak="" ref="tspop" style="display: none">
        <div class="pophide printHide show"></div>
        <div class="zui-form confirm-height podrag show openConfirm pop-548">
            <div class="confirm-title">医嘱执行</div>
            <div class="confirm-content">
                <div class="confirm-mad confirm-height">
                    <div class="zui-form">
                        <div class="zui-inline padding-left60">
                            <label class="zui-form-label" style="line-height: 24px;">执行时间</label>
                            <div class="zui-input-inline">
                                <i class="icon-position icon-rl"></i>
                                <input class="zui-input todate text-indent20" placeholder id="timeVal" />
                            </div>
                        </div>
                        <div class="zui-inline block-box">
                            <div class="cell-m zui-table-cell text-center">
                                <input class="green" type="checkbox" v-model="sytIsChecked">
                                <label @click="sytIsChecked=!sytIsChecked" @dblclick.stop></label>
                            </div>
                            <span>临时医嘱同时执行，否则不予执行，后面单独执行</span>
                        </div>
                        <div class="zui-inline block-box">
                            <div class="cell-m zui-table-cell text-center">
                                <input class="green" type="checkbox" v-model="lsIsChecked">
                                <label @click="lsIsChecked=!lsIsChecked" @dblclick.stop></label>
                            </div>
                            <span>同步打印输液贴</span>
                        </div>
                        <div class="tisi"><i></i>请仔细核对医嘱内容！</div>
                    </div>
                </div>
                <div class="confirm-row ">
                    <button v-waves class="confirm-btn confirm-primary-b cancel" @click="closes">取消</button>
                    <button v-waves class="confirm-btn confirm-primary  submit" @click="okzx()">确定执行</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 执行弹窗 end -->
    <script src="zxyz.js"></script>
</body>

</html>
