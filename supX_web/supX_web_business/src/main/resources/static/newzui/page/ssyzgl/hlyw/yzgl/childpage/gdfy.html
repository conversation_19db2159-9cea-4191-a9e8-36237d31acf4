<link rel="stylesheet" href="childpage/gdfy.css">
<body class="skin-default">
    <div class="wrapper"
         id="loadingPage"
         v-cloak="">
        <div class="bryz-list">
            <header class="userNameBg">
                <div class="flex">
                    <div class="userNameImg">
                        <img src="/newzui/pub/image/femalebaby.png">
                    </div>
                    <div class="text-color">
                        <p class="userHeader">
                            <span class="userName"
                                  v-text="br.brxm">
                                <!--病人姓名--></span>
                            <span class="sex text"
                                  v-text="brxb_tran[br.brxb]">
                                <!--性别--></span>
                            <span class="nl text"
                                  v-text="br.nl">
                                <!--年龄--></span>
                        </p>
                        <div class="userCwh">
                            <span class="cwh text"
                                  v-text="'床位号：'+ br.rycwbh +'号'"></span>
                            <span class="zyh text"
                                  v-text="'住院号：'+ br.zyh"></span>
                            <span class="bq text"
                                  v-text="'科室：'+br.ryksmc"></span>
                            <span class="ys text"
                                  v-text="'医师：'+ br.zyysxm"></span>
                            <span class="brzt text"
                                  v-text="'病人状态：'+zyzt_tran[br.zyzt]"></span>
                            <span class="bz text"
                                  v-text="'病种：'"></span>
                        </div>
                        <div>
                            <p class="heaf text">更多详细信息>></p>
                        </div>
                    </div>
                </div>
                <div class="blRight">
                    <span v-show="addShow"
                          class="blImg"
                          @click="lr"></span>
                    <!--<span class="xyzImg" onclick="tabBg('userPage/yzgl',1,this,1)"></span>-->
                </div>
            </header>
            <div class="fyxm-tab">
                <div><span :class="{'active':num==0}"
                          @click="tabBg(0)">执行中</span></div>
                <div><span :class="{'active':num==1}"
                          @click="tabBg(1)">变动记录</span></div>
            </div>
            <div class="fyxm-size  fyxm-hide"
                 :class="{'fyxm-show':num==0}">
                <div class="zui-table-view">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m">
                                            <input-checkbox @result="reCheckBox"
                                                            :list="'fylist'"
                                                            :type="'all'"
                                                            :val="isCheckAll">
                                            </input-checkbox>
                                        </div>
                                    </th>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>住院号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl text-left"><span>费用项目</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>数量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>金额</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>待结停用</span></div>
                                    </th>
                                    <!--
                                    <th class="cell-s">
                                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                                    </th>
                                    -->
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"
                         @scroll="scrollTable($event)">
                        <table class="zui-table table-width50">
                            <tbody v-if="fylist.length">
                                <tr v-for="(item, $index) in fylist"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()"
                                    @click="checkSelect([$index,'some','fylist'],$event)">
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m">
                                            <input-checkbox @result="reCheckBox"
                                                            :list="'fylist'"
                                                            :type="'some'"
                                                            :which="$index"
                                                            :val="isChecked[$index]">
                                            </input-checkbox>
                                        </div>
                                    </td>
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m"
                                             v-text="$index+1"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="item.zyh"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-xl title text-left"
                                             v-text="item.mxfyxmmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="item.yl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="fDec(item.dj,2)"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="fDec(item.yl * item.dj,2)"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="nh_tran[item.djty]"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <p v-if="!fylist.length"
                           class=" noData  text-center zan-border">暂无数据...</p>
                    </div>
                    <!--左侧固定-->
                    <div class="zui-table-fixed table-fixed-l">
                        <div class="zui-table-header">
                            <table class="zui-table">
                                <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m">
                                                <input-checkbox @result="reCheckBox"
                                                                :list="'fylist'"
                                                                :type="'all'"
                                                                :val="isCheckAll">
                                                </input-checkbox>
                                            </div>
                                        </th>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body"
                             @scroll="scrollTableFixed($event)">
                            <table class="zui-table">
                                <tbody>
                                    <tr v-for="(item, $index) in fylist"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)"
                                        @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'some','fylist'],$event)">
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m">
                                                <input-checkbox @result="reCheckBox"
                                                                :list="'fylist'"
                                                                :type="'some'"
                                                                :which="$index"
                                                                :val="isChecked[$index]">
                                                </input-checkbox>
                                            </div>
                                        </td>
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m"
                                                 v-text="$index+1"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!--右侧固定-->
                    <div class="zui-table-fixed table-fixed-r">
                        <div class="zui-table-header">
                            <table class="zui-table">
                                <thead>
                                    <tr>
                                        <th class="cell-s">
                                            <div class="zui-table-cell cell-s"><span>待结停用</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body"
                             @scroll="scrollTableFixed($event)">
                            <table class="zui-table">
                                <tbody>
                                    <tr v-for="(item, $index) in fylist"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)"
                                        @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'some','fylist'],$event)">
                                   	<td class="cell-s">
                                        <div class="zui-table-cell cell-s" v-text="nh_tran[item.djty]"></div>
                                    </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="fyxm-size  fyxm-hide"
                 :class="{'fyxm-show':num==1}">
                <div class="zui-table-view">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl"><span>费用项目</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>数量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>金额</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>待结停用</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>登记日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>停用日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>停用人</span></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body"
                         @scroll="scrollTable($event)">
                        <p v-if="!bdlist.length"
                           class=" noData  text-center zan-border">暂无数据...</p>
                        <table v-if="bdlist.length"
                               class="zui-table table-width50">
                            <tbody>
                                <tr v-for="(item, $index) in bdlist"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()"
                                    @click="checkSelectBd([$index,'some','bdlist'],$event)">
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m"
                                             v-text="$index+1"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-xl"
                                             v-text="item.mxfyxmmc">
                                            <</div> </td>
                                               <td>
                                                <div class="zui-table-cell cell-s"
                                                     v-text="item.yl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="fDec(item.dj,2)"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="fDec(item.yl * item.dj,2)"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="nh_tran[item.djty]"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="fDate(item.djrq,'date')"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="fDate(item.tyrq,'date')"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s"
                                             v-text="item.tyryxm"></div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--左侧固定-->
                    <div class="zui-table-fixed table-fixed-l">
                        <div class="zui-table-header">
                            <table class="zui-table">
                                <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body"
                             @scroll="scrollTableFixed($event)">
                            <table class="zui-table">
                                <tbody>
                                    <tr v-for="(item, $index) in bdlist"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)"
                                        @mouseleave="hoverMouse()"
                                        @click="checkSelectBd([$index,'some','bdlist'],$event)">
                                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="ksys-btn action-bar fixed ">
            <button class="zui-btn btn-primary xmzb-db"
                    @click="deleteXm('all')">删除</button>
            <button class="zui-btn btn-primary xmzb-db dy"
                    @click="saveGdfy()">确定</button>
        </div>
    </div>
    <div v-cloak class="side-form pop-width"
         style="padding-top: 0;"
         id="lr"
         role="form"
         :class="{'ng-hide': !isShow}">
        <div class="fyxm-side-top">
            <span>{{ title }}费用</span>
            <span class="fr closex ti-close"
                  @click="closes"></span>
        </div>
        <div class="ksys-side">
            <div>
                <i>费用项目名称</i>
                <div class="margin-top-5 pop-input-box position margin-b-20">
                    <input class="zui-input" autocomplete="off"
                           :value="mxfyContent.mxfyxmmc"
                           @keydown="changeDown($event,'mxfyxmmc')"
                           @input="searching(null,$event.target.value)"
                           id="mxfyxmmc">
                    <search-table :message="searchCon"
                                  :selected="selSearch"
                                  :page="page"
                                  :them="them"
                                  :them_tran="them_tran"
                                  @click-one="checkedOneOut"
                                  @click-two="selectOne">
                    </search-table>
                </div>
            </div>
            <div>
                <i>数量</i>
                <div class="margin-top-5 pop-input-box position margin-b-20">
                    <input class="zui-input" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                           type="number" autocomplete="off"
                           v-model.number.trim="fzContent.yl"
                           type="number"
                           @keydown.enter="bclr()">
                </div>
            </div>
            <div>
                <i>单价</i>
                <div class="margin-top-5 pop-input-box position margin-b-20">
                    <input class="zui-input" autocomplete="off"
                           v-model.number.trim="fzContent.fydj"
                           disabled>
                    <span class="danwei">元</span>
                </div>
            </div>
            <div>
                <i>金额</i>
                <div class="margin-top-5 pop-input-box position margin-b-20">
                    <input class="zui-input" autocomplete="off"
                           v-model="fzContent.fyje"
                           disabled>
                    <span class="danwei">元</span>
                </div>
            </div>
            <div>
                <i>待结停用</i>
                <div class="margin-top-5 pop-input-box position margin-b-20">
                   <select-input @change-data="resultChange" :not_empty="true" :child="nh_tran" :index="djty"
                    :val="djty" :name="'djty'">
                   </select-input>
                </div>
            </div>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db"
                    @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db"
                    @click="save"> 保存 </button>
        </div>
    </div>
    <script src="childpage/gdfy.js"></script>
</body>
