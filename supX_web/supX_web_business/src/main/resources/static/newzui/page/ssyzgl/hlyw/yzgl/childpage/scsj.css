.enter_tem1 input[type=radio] {
    width: 15px;
    height: 15px;
}

.enter_tem1 select {
    width: 126px;
    height: 24px;
    margin-left: 12px;
}


.enter_tem1 input {
    /*width: 110px;*/
    /*border: 1px solid #ccc;*/
    /*margin-bottom: 4px;*/
}
.height-36{
    height: 36px;
    min-height: 36px;
}
.enter_tem1 .wh180 {
    width: 180px !important;
    margin: 0 0 !important;
}
.zui-input{
    text-indent: 0;
}
.enter_tem1 .margin-b-0{
    margin-bottom: 0;
}
table{
    display: table;
    border-collapse: separate;
    /*border-spacing: 2px;*/
    border-color: grey;
}
.enter_tem1 > div {
    margin-bottom: 6px;
}
.enter_tem1 > div {
    margin-bottom: 6px;
}
.bRList {
    float: left;
    width: 250px;
    height: calc(100% - 74px);
    margin-right: 6px;
}

.dataCon {
    width: calc(100% - 310px);
    /*height: calc(100% - 74px);*/
}

.InfoMenu {
    position: relative;
    padding-top: 0;
}

.InfoMenu div {
    width: 110px;
}

.bRInfo p {
    margin: 0;
    color: green;
}

.bRInfo span {
    margin-left: 10px;
}

.bRInfo input {
    margin-left: 6px;
    margin-top: -3px;
}

.bRInfo span, .bRInfo, .bRInfo p, .bRInfo input {
    float: left;
}

.dataEnter {
    /*border: 1px solid #000000;*/
    display: inline-block;
    width: 714px;
}

.dataEnter .selectInput{
    width: 100%;
    height: 30px;
}

.dataEnter .selectInput ul{
    top: 31px;
    min-width: 97px;
    max-width: 97px;
}

.dataEnter th {
    width: 80px;
    font-size: 14px;
    background-color: #ccc;
    height: 26px;
    padding: 6px;
    cursor: default;
    white-space: nowrap;
}

.dataEnter input {
    width: 100%;
    font-size: 14px;
    height: 30px;
}

.dataEnter_tem1 {
    float: left;
    width: 400px;
    height: 130px;
}

.dataEnter_tem1 p {
    float: left;
    width: 18px;
    font-size: 18px;
    font-weight: 700;
    height: 100%;
    line-height: 60px;
    margin: 0 10px 0 0;
}

.dataEnter_tem1 input {
    margin-bottom: 4px;
    width: 70px;
}

.dataEnter_tem1 > div {
    float: left;
    width: 34%;
}

.dataEnter_tem1 .selectInput{
    width: 70px;
    float: right;
    margin-left: 5px;
}

.dataEnter_tem1 input{
    height: 26px;
    display: inline-block;
}
.dataEnter_tem1 .zui-select-inline {
    width: 70px;
    
    margin-left: 5px;
}
.dataEnter_tem1 input {
    margin-bottom: 4px;
    width: 70px;
}
.personInfo {
    width: 676px;
    height: 30px;
}

.personInfo span{
    display: block;
    float: left;
}

.personInfo p{
    margin: 0 10px 0 0;
    float: left;
    height: 22px;
}

#model{
    max-width: 1200px;
    width: 1200px;
    z-index: 100;
}
.SetUser_model{
    width: auto;
    height: 500px;
    overflow: hidden;
    padding: 14px 18px 14px 18px;
    background: rgba(255, 255, 255, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.border-child{
    min-width: 50px;
    width: auto;
	flex: 1;
    /*border: 1px solid rgb(70,70,70);*/
}
.border-right-none{
    border-right: none;
}
.border-bottom-none{
    border-bottom: none;
}
.zuiTableBody{
    overflow: auto;
    height: 355px;
}
.SetUser_model .zuiTableBody input{
    height: 28px;
}
.wh160{
    width: 160px;
}
.border-child-60{
	min-width:60px;
}
.jc-sp{
	justify-content: space-between;
}
.zui-table-fixed .zuiTableBody{
    height: 340px;
}

