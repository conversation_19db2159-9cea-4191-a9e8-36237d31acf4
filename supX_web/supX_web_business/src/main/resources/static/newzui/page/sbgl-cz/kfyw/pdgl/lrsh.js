var toolMenu_3 = new Vue({
	el: '.toolMenu_3',
	data: {
		pdlrpz: 0,
		ypjs: null,
		//盘点录入列表
		pdblrList: [],
	},
	//获取盘点录入凭证号
	mounted: function() {
		//启动加载
		this.getPdbList();
	},
	methods: {
		//清空页面
		clearAll: function() {
			enter_djList_sh.jsonList = [];
			enter_djDetail_sh.jsonList = [];
		},
		//审核盘点录入
		shPdlr: function() {
			if(enter_djList_sh.pdlrSelected == {}) {
				malert('请选择盘点录入单号！');
				return;
			}
			//凭证号非空判断
			if(this.pdlrpz == 0) {
				malert('请先选择凭证号！');
				return;
			};
			//准备参数
			var dj = {
				'qrzfbz': '1',
				'qrzfrq': (new Date()),
				'qrzfry': userId,
				'pdlrpzh': enter_djList_sh.pdlrSelected.pdlrpzh,
				'wzkf': enter_djList_sh.pdlrSelected.wzkf,
			}
			var json = {
				list: {
					'dj': dj,
					'djmx': enter_djDetail_sh.jsonList
				}
			};
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfKfywPdblr&types=passDj', JSON.stringify(json))
				.then(function(data) {
					malert(data.body.c);
					//凭证号下拉框清空
					toolMenu_3.pdlrpz = 0;
					//清空页面
					toolMenu_3.clearAll();
				}, function(error) {
					console.log(error);
				});
		},
		//作废盘点录入
		zfPdlr: function() {
			//凭证号非空判断
			if(this.pdlrpz == 0) {
				malert('请先选择凭证号！');
				return;
			}
			//准备作废参数
			var json = {
				'qrzfbz': '2',
				'qrzfrq': (new Date()),
				'qrzfry': userId,
				'pdlrpzh': enter_djList_sh.pdlrSelected.pdlrpzh,
				'wzkf': enter_djList_sh.pdlrSelected.wzkf,
			}

			this.$http.post('/actionDispatcher.do?reqUrl=WzkfKfywPdblr&types=invalid', JSON.stringify(json))
				.then(function(data) {
					malert(data.body.c);
					//清空页面
					toolMenu_3.clearAll();
					toolMenu_3.pdlrpz = 0;
				}, function(error) {
					console.log(error);
				});
		},
		//获取盘点录入列表
		getPdbList: function() {
			var kfbm = document.getElementById('_kfbm').value;
			if(kfbm == '') {
				malert('请选择库房');
				return;
			}
			var parm = {
				'wzkf': kfbm,
				'qrzfbz': 0
			};
			//查询未审核盘点表,获取凭证号
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdb&types=queryDj&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['zdrq'] = formatTime(json.d[i]['zdrq'], 'date');
					}
					//凭证号下拉框
					toolMenu_3.pdblrList = json.d;
				} else {
					malert('数据获取失败！')
				}
			});
		},

		//获取盘点录入表凭证号列表
		getPdlrList: function() {
			if(this.pdlrpz == 0) return;
			var parm = {
				'wzkf': this.pdlrpz.wzkf,
				'pdpzh': this.pdlrpz.pdpzh,
				'qrzfbz': '0',
			};
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdblr&types=queryDj&parm=' + JSON.stringify(parm), function(json) {
				if(json != null && json.a == 0) {
					for(var i = 0; i < json.d.length; i++) {
						json.d[i]['zdrq'] = formatTime(json.d[i]['zdrq'], 'date');
					}
					//凭证号下拉框
					enter_djList_sh.jsonList = json.d;
				} else {
					malert('数据获取失败！')
				}
			});
		},
	}
});

var enter_djList_sh = new Vue({
	el: '.enter_djList_sh',
	mixins: [tableBase],
	data: {
		//已选择的记录
		pdlrSelected: {},
		//盘点表录入明细列表
		jsonList: []
	},
	methods: {
		//获取盘点表录入明细
		getPdblrmx: function(item) {
			//保存已选择的盘点表录入
			this.pdlrSelected = item;
			//获取盘点表录入明细
			$.getJSON('/actionDispatcher.do?reqUrl=WzkfKfywPdblr&types=queryMx&parm=' + JSON.stringify(item), function(json) {
				if(json != null && json.a == 0) {
					//明细列表
					enter_djDetail_sh.jsonList = json.d;
				} else {
					malert('数据获取失败！')
				}
			});
		}
	}
});

var enter_djDetail_sh = new Vue({
	el: '.enter_djDetail_sh',
	mixins: [tableBase],
	data: {
		jsonList: []
	},
	methods: {}
});