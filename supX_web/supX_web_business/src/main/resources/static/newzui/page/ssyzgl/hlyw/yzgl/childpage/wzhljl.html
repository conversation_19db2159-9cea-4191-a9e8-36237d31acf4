<link rel="stylesheet" type="text/css" href="childpage/wzhljl.css">
<div class="toolMenu padd-b-10">
    <button @click="getData" class="zui-btn btn-primary fa fa-refresh">刷新</button>
    <button @click="print()" class="zui-btn btn-primary fa fa-plus">打印</button>
    <button @click="openCpt()" class="zui-btn btn-primary fa fa-plus">预览</button>
    <button @click="addData" class="zui-btn btn-primary fa fa-plus">新增</button>
    <button @click="getTwData"  class="zui-btn btn-primary fa  ">获取体温数据</button>
    <button @click="edit()" class="zui-btn btn-primary fa fa-edit">保存</button>
    <button @click="pledit()" class="zui-btn btn-primary fa fa-edit">修改诊断</button>
    <button @click="mrxj()" class="zui-btn btn-primary-b  ">每日小结</button>
    <button @click="remove" class="zui-btn btn-primary fa fa-trash-o">删除</button>
    <div class=" padd-l-12 left line-height-12">
        科别：<span class="font-14-654 padd-r-18" v-text="jlxqContent.ryksmc"></span>
        床号：<span class="font-14-654 padd-r-18" v-text="jlxqContent.rycwbh"></span>
        姓名：<span class="font-14-654 padd-r-18" v-text="jlxqContent.brxm"></span>
        性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[jlxqContent.nl]"></span>
        住院号：<span class="font-14-654 padd-r-18" v-text="jlxqContent.brzyh|| jlxqContent.zyh"></span>
    </div>
</div>

<div class="flex-container common-css" id="jlxq">
    <div class="zui-table-view  padd-b-40 padd-r-10  ">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th>
                        <div class="zui-table-cell cell-m">序号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">记录日期</div>
                    </th>
                    <th>
                        <div class="zui-table-cell text-left cell-s">操作员</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one over-auto"  @scroll="scrollTable">
            <table class="zui-table table-width50">
                <tbody>
                <tr @click="switchIndex('activeIndex',true,$index),Wf_Click($index)"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                    @mouseleave="hoverMouse()"
                    :class="[{'tableTr': $index%2 == 0},{'table-hovers':isChecked[$index]}]"
                    :tabindex="$index" v-for="(item, $index) in jlxx_list">
                    <td>
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="fDate(item.jlsj,'yyyy-MM-dd')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s  text-left" v-text="item.czyxm"></div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="over-auto">
        <div class="margin-b-10 padd-r-10  padd-l-10 padd-t-10 flex-container flex-align-c">
            <span class="font-14  padd-r-5">记录时间</span>
            <input type="text" class="zui-input wh182" data-select="no" @click="showDate('jlsj','jlsj')" id="jlsj" :value="fDate(jlContent.jlsj,'datetime')"/>
            <span class="padd-r-10 padd-l-10 flex-align-c flex-container">签名</span>
            <select-input :search="true" class="wh120" @change-data="resultChange" :not_empty="false" :child="hsList"
                          :index="'ryxm'" :index_val="'rybm'" :val="jlContent.rybm" :name="'jlContent.rybm'">
            </select-input>
            <span
                    class=" padd-r-10 font-14  padd-r-5 padd-l-5 margin-l-10">时&emsp;&emsp;段</span>
            <select-input class="wh120 margin-r-20" @change-data="resultChangeSd"
                          :child="sd_tran" :index="jlContent.clsd" :val="jlContent.clsd"
                          :name="'jlContent.clsd'" :search="true"> </select-input>
            <div class="flex-container flex-align-c padd-l-10">
                <div class="cell-m zui-table-cell text-center">
                    <input class="green" type="checkbox"  true-value="1" false-value="0"  v-model="jlContent.yjbz">
                    <label @click="yjbz" @dblclick.stop></label>
                </div>
                <span class="padd-l-5">夜间标志</span>
            </div>
        </div>

        <div class="margin-b-10 padd-r-10  padd-l-10 padd-t-10 flex-container flex-wrap-w">
            <div class="margin-b-10 padd-r-10 flex-container flex-align-c" >
                <span class=" font-14  padd-r-5" >意识</span>
            <select-input class="wh120" @change-data="resultChange" :child="ys_tran" :index="jlContent.ys"
                          :val="jlContent.ys" :name="'jlContent.ys'" :search="true">
            </select-input>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">体温</span>
                <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.tw"/>
                <span class="cm">℃</span>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">脉搏</span>
                <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.mb"/>
                <span class="cm">次/分</span>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">呼吸</span>
                <input type="number"  @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.hx"/>
                <span class="cm">次/分</span>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">心率</span>
                <input type="number"  @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.xl"/>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">血压</span>
                <input type="number" @keydown="nextFocus($event)" class="zui-input " style="width: 100px" v-model="jlContent.xyL"/>
                <span>/</span>
                <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.xyR"/>
                <span class="cm">mmHg</span>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">血&emsp;&emsp;氧</br>饱&ensp;和&ensp;度</span>
                <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.xybhd"/>
                <span class="cm">%</span>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">吸氧</span>
                <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.xy"/>
                <span class="cm">L/min</span>
            </div>
            <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">心电检测</span>
                <select-input class="wh120" @change-data="resultChange" :child="xdjc_tran" :index="jlContent.xdjcXl"
                              :val="jlContent.xdjcXl" :name="'jlContent.xdjcXl'" :search="true">
                </select-input>
            </div>
        </div>
        <div class="margin-b-10 ">
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">入量</div>
                </div>
                <div class="tab-card-body flex-container padd-t-10">
                    <div class="margin-b-10 flex-align-c padd-r-10 flex-container">
                        <span class=" font-14  padd-r-5">名&emsp;&emsp;称</span>
                        <input type="text" @keydown="nextFocus($event)" class="zui-input wh182" v-model="jlContent.rlMc"/>
                    </div>
                    <div class="margin-b-10 padd-r-10 flex-container position flex-align-c">
                        <span class=" font-14  padd-r-5">途&emsp;&emsp;径</span>
                        <select-input class="wh120" @change-data="resultChange" :child="tj_tran" :index="jlContent.rlTj"
                                      :val="jlContent.rlTj" :name="'jlContent.rlTj'" :search="true">
                        </select-input>
                    </div>
                    <div class="margin-b-10 flex-container position flex-align-c">
                        <span class=" font-14  padd-r-5">数&emsp;&emsp;量</span>
                        <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.rlSl">
                        <span class="cm">ml</span>
                    </div>
                </div>
            </div>
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">出量</div>
                </div>
                <div class="tab-card-body flex-container padd-t-10">
                    <div class="margin-b-10 padd-r-10 flex-align-c flex-container">
                        <span class=" font-14  padd-r-5">名&emsp;&emsp;称</span>
                        <input type="text" @keydown="nextFocus($event)" class="zui-input wh182" v-model="jlContent.clMc"/>
                    </div>
                    <div class="margin-b-10  padd-r-10 flex-container position flex-align-c">
                        <span class=" font-14  padd-r-5">数&emsp;&emsp;量</span>
                        <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.clSl">
                        <span class="cm">ml</span>
                    </div>
                    <div class="margin-b-10 flex-container position flex-align-c">
                        <span class=" font-14  padd-r-5">颜色性状</span>
                        <select-input class="wh120" @change-data="resultChange" :child="ysxz_tran" :index="jlContent.clYsxz"
                                      :val="jlContent.clYsxz" :name="'jlContent.clYsxz'" :search="true">
                        </select-input>
                    </div>
                </div>
            </div>
        </div>

        <div class=" position padd-r-10 padd-b-10 padd-l-10 padd-t-10 flex-container">
            <div class="flex-container flex-align-c padd-r-10">
                <span class="padd-r-10  font-14  padd-r-5">皮肤情况</span>
                <select-input class="wh120" @change-data="resultChange" :child="pfqk_tran" :index="jlContent.pfqk"
                              :val="jlContent.pfqk" :name="'jlContent.pfqk'" :search="true">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-t-10">
                <span class="padd-r-10  font-14  padd-r-5">管路护理</span>
                <select-input class="wh120" @change-data="resultChange" :child="glhl_tran" :index="jlContent.glhl"
                              :val="jlContent.glhl" :name="'jlContent.glhl'" :search="true">
                </select-input>
            </div>
            </div>
            <div class="padd-r-10 padd-b-10 padd-l-10 padd-t-10">
                <span style="vertical-align: top">病情观察及措施</span><br>
                <textarea style="height: 100px" class="zui-input" v-model="jlContent.bqgcjcs"></textarea>
            </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">导管/引流</div>
            </div>
            <div class="tab-card-body flex-container ">
                <div class="flex-container padd-r-10 margin-b-10 position flex-align-c">
                    <span class=" font-14  padd-r-5">名称</span>
                    <select-input class="wh120" @change-data="resultChange" :child="mc_tran" :index="jlContent.mc"
                                  :val="jlContent.dgylMc" :name="'jlContent.dgylMc'" :search="true">
                    </select-input>
                </div>
                <div class="flex-container padd-r-10 margin-b-10 position flex-align-c">
                    <span class=" font-14  padd-r-5">数量</span>
                    <input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.dgylSl">
                    <span class="cm">ml</span>
                </div>
                <div class="flex-container padd-r-10 margin-b-10 position flex-align-c">
                    <span class=" font-14  padd-r-5">性状颜色</span>
                    <select-input class="wh120" @change-data="resultChange" :child="xzys_tran" :index="jlContent.dgylYs"
                                  :val="jlContent.dgylYs" :name="'jlContent.dgylYs'" :search="true">
                    </select-input>
                </div>
                <div class="flex-container padd-r-10 margin-b-10 position flex-align-c">
                    <span class=" font-14  padd-r-5">护理</span>
                    <select-input class="wh120" @change-data="resultChange" :child="hl_tran" :index="jlContent.hl"
                                  :val="jlContent.dgylHl" :name="'jlContent.dgylHl'" :search="true">
                    </select-input>
                </div>
            </div>
        </div>
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title font14">瞳孔</div>
                </div>
                <div class="tab-card-body flex-container ">
                    <div class="flex-container flex-align-c">
                        <div class="text-center padd-r-10">大小(mm)</div>
                        <div>
                            <div class="flex-container flex-align-c margin-b-10">
                                <span class="font-14  padd-r-5">左</span>
                                <input @keydown="nextFocus($event)" type="text" class="zui-input wh120" v-model="jlContent.tkDxZ"/>
                            </div>
                            <div class="flex-container flex-align-c">
                                <span class="font-14  padd-r-5">右</span>
                                <input @keydown="nextFocus($event)" type="text" class="zui-input wh120" v-model="jlContent.tkDxY"/>
                            </div>
                        </div>
                    </div>
                    <div class="flex-container flex-align-c padd-l-10">
                        <div class="text-center padd-r-10">光反应</div>
                        <div>
                            <div class="flex-container flex-align-c margin-b-10">
                                <span class="font-14  padd-r-5">左</span>
                                <select-input class="wh120" @change-data="resultChange" :child="tk_tran" :index="jlContent.tkGfyZ"
                                              :val="jlContent.tkGfyZ" :name="'jlContent.tkGfyZ'" :search="true">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c">
                                <span class="font-14  padd-r-5">右</span>
                                <select-input class="wh120" @change-data="resultChange" :child="tk_tran" :index="jlContent.tkGfyY"
                                              :val="jlContent.tkGfyY" :name="'jlContent.tkGfyY'" :search="true">
                                </select-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <div class=" flex-container padd-r-10 padd-b-10 padd-l-10 padd-t-10 flex-wrap-w">
            <div class="flex-container padd-r-10 flex-align-c margin-b-10">
                <span class=" font-14  padd-r-5">其&ensp;他&emsp;1</span>
                <input  @keydown="nextFocus($event)" class="zui-input wh120" type="text" v-model="jlContent.qt1">
            </div>
            <div class="flex-container padd-r-10 flex-align-c margin-b-10">
                <span class=" font-14  padd-r-5">其&ensp;他&emsp;2</span>
                <input  @keydown="nextFocus($event)" type="text" class="zui-input wh120" v-model="jlContent.qt2">
            </div>
            <div class="flex-container padd-r-10 flex-align-c margin-b-10">
                <span class=" font-14  padd-r-5">其&ensp;他&emsp;3</span>
                <input @keydown="nextFocus($event)" class="zui-input wh120" type="text" v-model="jlContent.qt3">
            </div>
            <div class="flex-container padd-r-10 flex-align-c margin-b-10">
                <span  class=" font-14  padd-r-5">小结及总</br>结&ensp;小&ensp;时</span>
                <input @keydown="nextFocus($event)"  class="zui-input wh120" type="text" v-model="jlContent.xj">
            </div>
            <div class="flex-container padd-r-10 flex-align-c margin-b-10">
                <span class=" font-14  padd-r-5">护理等级</span>
                <select-input class="wh120" @change-data="resultChange" :child="hldj_tran" :index="jlContent.hldj"
                              :val="jlContent.hldj" :name="'jlContent.hldj'" :search="true">
                </select-input>
            </div>
            <div class="flex-container padd-r-10 wh100MAx  margin-b-10">
                <span class=" font-14  padd-r-5 whiteSpace">诊断名称</span>
                <textarea   class=" wh100MAx " rows="4"  v-model="jlContent.zdmc"></textarea>
            </div>
        </div>
</div>
    </div>
<script type="applicatspann/javascript" src="childpage/wzhljl.js"></script>
