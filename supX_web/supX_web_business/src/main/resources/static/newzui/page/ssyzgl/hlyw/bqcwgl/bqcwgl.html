<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <title>床位管理</title>
  <script type="application/javascript" src="/newzui/pub/top.js"></script>
  <link href="/newzui/css/main.css" rel="stylesheet">
  <link rel="stylesheet" href="/newzui/css/icon.css">
  <link rel="stylesheet" href="bqcwgl.css">
</head>

<body class="skin-default padd-l-10 padd-r-10">
  <div class="wrapper">
    <div v-cloak id="bqcw">
      <div class="panel">
        <div class="tong-top">
          <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">刷新</button>
          <!-- <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="clearZyh()">清空住院号</button>
          <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="rplZyh()">重设住院号</button> -->
        </div>
      </div>
      <div class="tong-search">
        <div class="zui-form">
          <div class="zui-inline padd-l-40">
          	<label class="zui-form-label">病区</label>
            <div class="zui-input-inline wh120">
              <select-input @change-data="resultChangeBq"  :child="bqList" :index="'bqmc'" :index_val="'bqbm'"
                                  :val="barContent.bqbm" :search="true" :name="'barContent.bqbm'">
              </select-input>  
            </div>
          </div>
           <div class="zui-inline padd-l-40">
            <label class="zui-form-label">检索</label>
            <div class="zui-input-inline wh182">
              <input class="zui-input titel" placeholder="请输入检索关键字" id="text-ss" v-model="param.parm" type="text"
                @keyUp.enter="goToPage(1)" />
              <!--@keyUp.enter传检索床位的方法！-->
            </div>
          </div>
        </div>
      </div>

      <div class="zui-table-view">
        <div class="zui-table-header">
          <table class="zui-table table-width50">
            <thead>
              <tr>
                <th class="cell-m">
                  <input-checkbox @result="reCheckBox" :list="'cwList'" :type="'all'" :val="isCheckAll">
                  </input-checkbox>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>床位号</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>住院号</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>病人姓名</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>性别</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>年龄</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>床位名称</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>房间号</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>病区科室</span></div>
                </th>
                <th>
                  <div class="zui-table-cell  cell-s"><span>编制床位</span></div>
                </th>
                <th>
                  <div class="zui-table-cell cell-s"><span>备注</span></div>
                </th>
                <th class="cell-s">
                  <div class="zui-table-cell cell-s"><span>状态</span></div>
                </th>
              </tr>
            </thead>
          </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
          <table class="zui-table table-width50">
            <tbody v-if="cwList.length">
              <tr :tabindex="$index" v-for="(item,$index) in cwList" @click="checkSelect([$index,'one','cwList'],$event)"
                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" @mouseenter="hoverMouse(true,$index)"
                @mouseleave="hoverMouse()" class="tableTr2">
                <!--@dblclick="edit($index)"双击回调-->
                <td class="cell-m">
                  <input-checkbox @result="reCheckBox" :list="'cwList'" :type="'some'" :which="$index" :val="isChecked[$index]">
                  </input-checkbox>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.cwh"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.zyh"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.brxm"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s">{{item.brxb=='未知'?'':item.brxb}}</div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.brnl"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.cwmc"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.fjh"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.bqksmc"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell  cell-s"><span v-text="istrue_tran[item.bzcw]"></span></div>
                </td>
                <td>
                  <div class="zui-table-cell cell-s"><span v-text="item.bzsm"></span></div>
                </td>
                <td class="cell-s">
                  <div class="zui-table-cell cell-s"><span v-text="stopSign[item.qyzt]"></span></div>
                </td>
              </tr>
            </tbody>
          </table>
          <p v-if="!cwList.length" class=" noData  text-center zan-border">暂无数据...</p>
        </div>
        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l">
          <div class="zui-table-header">
            <table class="zui-table">
              <thead>
                <tr>
                  <th class="cell-m">
                    <input-checkbox @result="reCheckBox" :list="'cwList'" :type="'all'" :val="isCheckAll">
                    </input-checkbox>
                  </th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="zui-table-body" @scroll="scrollTableFixed($event)">
            <table class="zui-table">
              <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in cwList" @click="checkSelect([$index,'one','cwList'],$event)"
                  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" @mouseenter="hoverMouse(true,$index)"
                  @mouseleave="hoverMouse()" class="tableTr2">
                  <td class="cell-m">
                    <input-checkbox @result="reCheckBox" :list="'cwList'" :type="'some'" :which="$index" :val="isChecked[$index]">
                    </input-checkbox>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r">
          <div class="zui-table-header">
            <table class="zui-table">
              <thead>
                <tr>
                  <th>
                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                  </th>
                </tr>
              </thead>
            </table>
          </div>
          <div class="zui-table-body" @scroll="scrollTableFixed($event)">
            <table class="zui-table">
              <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in cwList" @click="checkSelect([$index,'one','cwList'],$event)"
                  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" @mouseenter="hoverMouse(true,$index)"
                  @mouseleave="hoverMouse()" class="tableTr2">
                  <td class="cell-s">
                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
      </div>
    </div>
  </div>
  <script src="bqcwgl.js"></script>
</body>
</html>