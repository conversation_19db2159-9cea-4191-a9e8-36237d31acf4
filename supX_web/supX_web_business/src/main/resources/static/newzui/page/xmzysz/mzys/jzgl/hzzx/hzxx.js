var fyxmTab = new Vue({
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    el: '.content',
    data: {
        num: 0,
        dqks:'',
        dqksmc:'',
        fypcfShow: '1',
        csqx: {}
    },
    methods: {
        tabBg: function (page) {
            $(".loadPage").load(page + ".html").fadeIn(300);
        }
    },
});

var userNameBg = new Vue({
    el: '.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        Brxx_List: {},
        page: '',
        csqxContent: {},
        mcWebServiceUrl: '',
        mc_passim_proxy_url: '',
    },
    mounted: function () {
        this.getHlyy();
        if (sessionStorage.getItem('brPage' + this.getQueryString('ghxh'))) {
            Vue.set(this, 'Brxx_List', JSON.parse(sessionStorage.getItem('brPage' + this.getQueryString('ghxh')))[2]);
            Vue.set(fyxmTab, 'fypcfShow', this.Brxx_List.fypcfShow);
            Vue.set(fyxmTab, 'num', JSON.parse(sessionStorage.getItem('brPage' + this.getQueryString('ghxh')))[1]);
            Vue.set(fyxmTab, 'csqx', JSON.parse(sessionStorage.getItem('brPage' + this.getQueryString('ghxh')))[3]);
            Vue.set(fyxmTab, 'dqks', JSON.parse(sessionStorage.getItem('brPage' + this.getQueryString('ghxh')))[4]);
            Vue.set(fyxmTab, 'dqksmc', JSON.parse(sessionStorage.getItem('brPage' + this.getQueryString('ghxh')))[5]);
            fyxmTab.tabBg(JSON.parse(sessionStorage.getItem('brPage' + this.getQueryString('ghxh')))[0]);
            // sessionStorage.removeItem('brPage'+this.getQueryString('ghxh'))
        }
        window.addEventListener('storage', function (e) {
            if (e.key == 'brPage' + userNameBg.getQueryString('ghxh')) {
                Vue.set(fyxmTab, 'csqx', JSON.parse(sessionStorage.getItem('brPage' + userNameBg.getQueryString('ghxh')))[3]);
                Vue.set(userNameBg, 'Brxx_List', JSON.parse(e.newValue)[2]);
                Vue.set(fyxmTab, 'num', JSON.parse(e.newValue)[1]);
                Vue.set(fyxmTab, 'dqks', JSON.parse(e.newValue)[4]);
                Vue.set(fyxmTab, 'dqksmc', JSON.parse(e.newValue)[5]);
                fyxmTab.tabBg(JSON.parse(e.newValue)[0]);
                // sessionStorage.removeItem('brPage'+userNameBg.getQueryString('ghxh'))
            }

        });
    },


    methods: {
        McGetLoadBaseUrl: function () {
            var McLoadJSName = 'McLoader.js';
            if (!!this.mcWebServiceUrl) {
                return this.mcWebServiceUrl + '/passjs/'
            }

            var regex = new RegExp(McLoadJSName.replace('.', '\.') + '$', 'gi');
            var elems = document.getElementsByTagName('SCRIPT');
            for (var i = 0; i < elems.length; i++) {
                var src = elems[i].getAttribute('src');
                if (regex.test(src)) {
                    return src.replace(regex, '');
                }
            }
            return '';
        },
        getHlyy: function () {
            if (window.top.J_tabLeft.obj.hlyy == '1') { //0-无,1-美康合理用药
                // this.loadScript('/newzui/pub/PassJs/McLoader.js', function () {
                // })
                var urls = window.top.J_tabLeft.obj.hyyydz || '************:80'
                //嵌套时，请调整为美康合理用药PASS4WebService地址，不要最后的 “/”
                this.mcWebServiceUrl = "http://" + urls + "/PASS4webservice";
                var McBaseUrl = this.McGetLoadBaseUrl();

                McLoadCss(McBaseUrl + 'McCssAll.css')
                McLoadJs(McBaseUrl + 'McConfig.js');
                McLoadJs(McBaseUrl + 'McJsAll.js');
                McLoadJs(McBaseUrl + 'McPassIm.js');

                this.mc_passim_proxy_url = "http://************:80/mc_passim_proxy.html";

                function McLoadJs(src) {
                    document.write('<script src="' + src + '" type="text/javascript"></sc' + 'ript>');
                }


                function McLoadCss(href) {
                    document.write('<link href="' + href + '" rel="stylesheet" type="text/css" />');
                }

            }
        },

        //门诊入院
        addSide: function () {
            treatment.brShow = true;
            treatment.open();
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'hidden'})
        },
        //取消接诊
        cancelJz: function () {
            //入参
            var parm = {
                ghxh: userNameBg.Brxx_List.ghxh
            };
            //请求后台取消接诊信息
            this.$http.post('/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=updateQxjz', JSON.stringify(parm)).then(function (data) {
                if (data.body.a == 0) {
                    malert("取消接诊成功！", 'top', 'success');

                    this.topNewPage('接诊管理', 'page/xmzysz/mzys/jzgl/jzgl.html', '?isgetData=1');
                    sessionStorage.setItem('qcjz', Math.random())
                } else {
                    malert("取消接诊失败：" + data.body.c, 'top', 'defeadted');
                }
            });
        },
        payWx:function (dataType,sum){
                        var str=fyxmTab.csqx.N05001200270
            var parm={
                url:str.split('|')[0],
                dataType:dataType,
                hospital_id:str.split('|')[1],
                hospital_area_id:'',
                real_name:userNameBg.Brxx_List.brxm,
                id_card:userNameBg.Brxx_List.sfzjhm,
                patient_card:userNameBg.Brxx_List.sfzjhm,
                his_id:userNameBg.Brxx_List.ghxh,
                fee_type:'',
                clinic_no:'',
                total_amount:sum
            }
            if(dataType =='pushClinicFinish'){
                delete parm.fee_type;
                delete parm.total_amount;
                delete parm.clinic_no;
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=xxts&parm='+JSON.stringify(parm),(function (json) {
                if(json.a == '0'){
                    var dom=new DOMParser().parseFromString(json.d, 'text/xml');
                    if(dom.querySelector('result')){
                        var result=dom.querySelector('result').innerHTML
                    }
                    if(dom.querySelector('message')){
                        var message=dom.querySelector('message').innerHTML
                    }
                    if(dom.querySelector('return')){
                        var returnMsg=dom.querySelector('return').innerHTML;
                        returnMsg=returnMsg.replace(/[^\u4e00-\u9fa5|,]+/,'').replace(/[^\u4e00-\u9fa5|,]+/,'')
                    }
                    if(result){
                        // malert("移动支付成功");
                    }
                    if(message){
                        // malert("移动支付失败"+message,'top','defeadted');
                    }
                    if(returnMsg){
                        // malert("移动支付失败"+returnMsg,'top','defeadted');
                    }
                }
            })
            )
        },
        //完成接诊
        wcJz: function () {
            if (userNameBg.Brxx_List.jzbz == 0) {
                malert("未接诊病人不允许操作！", 'top', 'defeadted');
                return false;
            }
            //入参
            var parm = {
                ghxh: userNameBg.Brxx_List.ghxh,
                wcbz: '1',
                wcsj: new Date(),
                wcry: userId
            };
            //请求后台取消接诊信息
            this.$http.post('/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=updateJzwc', JSON.stringify(parm)).then(function (data) {
                if (data.body.a == 0) {
                    malert("完成接诊成功！", 'top', 'success');
                    this.topNewPage('接诊管理', 'page/xmzysz/mzys/jzgl/jzgl.html', '?isgetData=1');
                    sessionStorage.setItem('wcjzAndqcwc', Math.random())
                    if(fyxmTab.csqx.N05001200270  && userNameBg.Brxx_List.sfzjhm!=''){
                        userNameBg.payWx('pushClinicFinish')
                    }
                    // @yqq 向叫号系统推送最新数据
                    if (userNameBg.csqxContent.N03001200131) {
                        $.ajax({
                            type: 'get',
                            url: userNameBg.csqxContent.N03001200131 + '/pushData/' + userNameBg.Brxx_List.ghks,
                            async: true,
                            success: function (json) {
                                console.log("数据发送成功");
                            },
                            error: function (response) {
                            }
                        });
                    }
                } else {
                    malert("完成接诊失败：" + data.body.c, 'top', 'defeadted');
                }
            });

        },
        //取消完成
        qxwc: function () {
            //入参
            var parm = {
                ghxh: userNameBg.Brxx_List.ghxh,
                wcbz: '0',
                wcsj: null,
                wcry: null
            };
            //请求后台取消接诊信息
            this.$http.post('/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=updateJzwc', JSON.stringify(parm)).then(function (data) {
                if (data.body.a == 0) {
                    malert("取消完成接诊成功！", 'top', 'success');
                    this.topNewPage('接诊管理', 'page/xmzysz/mzys/jzgl/jzgl.html', '?isgetData=1')
                    sessionStorage.setItem('wcjzAndqcwc', Math.random());
                } else {
                    malert("取消完成接诊失败：" + data.body.c, 'top', 'defeadted');
                }
            });
        },

        //保存成功以后再根据挂号序号查询
        getBrxx: function (num) {
            var popContent = { //查询条件
                ghxh: userNameBg.Brxx_List.ghxh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryJzlb&parm=" + JSON.stringify(popContent), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    //把时间戳改改时间
                    json.d.list[0].ghrq = formatTime(json.d.list[0].ghrq, "datetime");
                    json.d.list[0].qhrq = formatTime(json.d.list[0].qhrq, "datetime");
                    //判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                    if (json.d.list[0].brnl < 7 && json.d.list[0].brxb == '1') {
                        json.d.list[0].nljd = '1';
                    } else if (json.d.list[0].brnl < 7 && json.d.list[0].brxb == '2') {
                        json.d.list[0].nljd = '2';
                    } else if (json.d.list[0].brnl < 18 && json.d.list[0].brnl > 6 && json.d.list[0].brxb == '1') {
                        json.d.list[0].nljd = '3';
                    } else if (json.d.list[0].brnl < 18 && json.d.list[0].brnl > 6 && json.d.list[0].brxb == '2') {
                        json.d.list[0].nljd = '4';
                    } else if (json.d.list[0].brnl < 41 && json.d.list[0].brnl > 17 && json.d.list[0].brxb == '1') {
                        json.d.list[0].nljd = '5';
                    } else if (json.d.list[0].brnl < 41 && json.d.list[0].brnl > 17 && json.d.list[0].brxb == '2') {
                        json.d.list[0].nljd = '6';
                    } else if (json.d.list[0].brnl < 66 && json.d.list[0].brnl > 40 && json.d.list[0].brxb == '1') {
                        json.d.list[0].nljd = '7';
                    } else if (json.d.list[0].brnl < 66 && json.d.list[0].brnl > 40 && json.d.list[0].brxb == '2') {
                        json.d.list[0].nljd = '8';
                    } else if (json.d.list[0].brnl > 65 && json.d.list[0].brxb == '1') {
                        json.d.list[0].nljd = '9';
                    } else if (json.d.list[0].brnl > 65 && json.d.list[0].brxb == '2') {
                        json.d.list[0].nljd = '10';
                    } else {
                        json.d.list[0].nljd = '11';
                    }
                    //状态
                    if (json.d.list[0].jzbz == '0' && json.d.list[0].wcbz == '0') {
                        json.d.list[0].zt = '0';
                    } else if (json.d.list[0].jzbz == '1' && json.d.list[0].wcbz == '0') {
                        json.d.list[0].zt = '1';
                    } else if (json.d.list[0].wcbz == '1') {
                        json.d.list[0].zt = '2';
                    }
                    userNameBg.Brxx_List = json.d.list[0];
                    if (num) {
                        tabBg('brPage/dzcf', 1, this)
                    }
                } else {
                    malert('获取病人信息失败', 'top', 'defeadted')
                }
            });
        },

    },
});

//门诊入院
var treatment = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, printer, baseFunc, tableBase, mformat, checkData, printer],
    components: {
        'search-table': searchTable
    },
    data: {
        nums: 1,
        title: '门诊入院',
        ifClick: true, //判断是否点击了
        mzksList: [], //门诊科室
        mzysList: [], //门诊医生
        glmzysList: [], //过滤后的门诊医生
        zyksList: [],  //住院科室
        printData: {},
        syShow: false,
        bxlbbm: null,
        bxlbbm1: null,
        bxurl: null,
        bxurl1: null,
        json: {},
        selSearch: -1,
        csqxContent: fyxmTab.csqx,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        //下拉table初步诊断信息检索选中对象
        jbbmContent: {},
        searchCon: [],
        them: {
            '疾病编码': 'jbmb',
            '疾病名称': 'jbmc',
        },
        rybz: userNameBg.Brxx_List.zyh, // 是否入院 0未入院 1已入院
    },
    mounted: function () {
        this.getMzksData(); //门诊科室
        this.getMzysData(); //门诊医生
        this.getZyksData(); //住院科室
        this.getbxlb(); //住院科室
        if (!this.csqxContent.N03001200146 == false && this.csqxContent.N03001200146 == "1") {
            this.getbxlb001(); //住院科室
        }
    },
    methods: {
        //获取保险类别
        getbxlb: function () {
            var param = {bxjk: "002"};
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1XtwhKsryBxlb',
                    types: 'query',
                    json: JSON.stringify(param)
                }
            }).then(function (json) {
                if (json.body.a == 0 && json.body.d.list>0) {
                    treatment.bxlbbm = json.body.d.list[0].bxlbbm;
                    treatment.bxurl = json.body.d.list[0].url;
                } else {
                    // malert("保险类别查询失败!" + json.c, 'top', 'defeadted');
                }
            });
        },
        getbxlb001: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify({bxjk: "001"}), function (json) {
                if (json.a == 0 && json.d && json.d.list.length > 0) {
                    treatment.bxlbbm1 = json.d.list[0].bxlbbm;
                    treatment.bxurl1 = json.d.list[0].url;
                } else {
                    malert("农合类别查询失败!" + json.c, "top", "defeadted");
                }
            });
        },
        isNhzd: function () {
            var result;
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + this.bxurl1 + "&bxlbbm=" + this.bxlbbm1 + "&types=jbbm&method=query&parm=" + JSON.stringify({
                parm: userNameBg.Brxx_List.jbbm,
                page: 1,
                rows: 10,
            }), function (json) {
                if (json.a == '0') {
                    var data = eval('(' + json.d + ')')
                    if (data.list && data.list.length != 0) {
                        result = true
                    } else {
                        result = false
                    }
                } else {
                    result = false
                }
            });
            return result
        },
        initYb: function () {
            var result;
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + this.bxurl + "&bxlbbm=" + this.bxlbbm + "&types=ICD10&method=query&parm=" + JSON.stringify({
                parm: userNameBg.Brxx_List.jbmc,
                page: 1,
                rows: 10
            }),
                function (json) {
                    var obj = {};
                    if(typeof(json.d)=='string'){
                        obj = JSON.parse(json.d);
                    }else {
                        obj = json.d;
                    }
                    if (json.a == 0 && json.d && obj.list.length != 0) {
                        result = true
                    } else {
                        result = false
                    }
                });
            return result
        },
        closes: function () {
            this.nums = 1;
            // treatment.json = {};
            $(".blRight").show();
            $('.hzzx-top').css({'z-index': '999'})
            $('body').css({'overflow': 'auto'})
        },
        open: function () {
            this.nums = 0;
            $(".blRight").hide();
            this.getMzry(userNameBg.Brxx_List.ghxh);
        },
        //查询是否有该病人门诊入院信息
        getMzry: function (ghxh) {
            this.jbbmContent = {};
            $.getJSON('/actionDispatcher.do?reqUrl=New1MzysZlglMzry&types=selectOne' + '&ryghxh=' + ghxh, function (data) {
                if (data.a == 0) {
                    console.log("mzry: " + JSON.stringify(data.d));
                    if (data.d != null) {
                        treatment.json = data.d;
                        treatment.jbbmContent.jbmc = treatment.json.cbzdmc;
                        treatment.json.brxm = userNameBg.Brxx_List.brxm;
                        treatment.$forceUpdate();
                    } else {
                        treatment.json = {
                            ryghxh: userNameBg.Brxx_List.ghxh,
                            rybrid: userNameBg.Brxx_List.brid,
                            brxm: userNameBg.Brxx_List.brxm,
                            cbzd: userNameBg.Brxx_List.jbbm,
                            mzys: userNameBg.Brxx_List.jzys,
                            mzks: userNameBg.Brxx_List.ghks,
                            bzsm: userNameBg.Brxx_List.bzsm
                        };
                        //给门诊入院的初步诊断附初值
                        if (userNameBg.Brxx_List.jbbm != null) {
                            treatment.json['cbzd'] = userNameBg.Brxx_List.jbbm;
                            treatment.jbbmContent.jbmc = userNameBg.Brxx_List.jbmc;
                        } else if (treatment.zdxx != undefined && treatment.zdxx != null) {
                            treatment.json['cbzd'] = treatment.zdxx.jbbm;
                            treatment.jbbmContent.jbmc = treatment.zdxx.jbmc;
                        }
                    }
                }
            });
        },

        //下拉检索
        changeDown: function (event) {
            this.nextFocus(event);
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'jbbmContent', 'searchCon');
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                treatment.json['cbzd'] = treatment.jbbmContent['jbmb'];
            }
        },

        //当输入值后才触发
        change: function (add, val) {
            this.jbbmContent['jbmc'] = val;
            if (!add) this.page.page = 1; // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = this.jbbmContent['jbmc'];
            var str_param = {
                parm: this.page.parm,
                page: this.page.page,
                rows: this.page.rows,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm' +
                '&json=' + JSON.stringify(str_param),
                function (data) {
                    if (add) { //不是第一页则需要追加
                        for (var i = 0; i < data.d.list.length; i++) {
                            treatment.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        treatment.searchCon = data.d.list;
                    }
                    treatment.page.total = data.d.total;
                    treatment.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
        },

        //鼠标双击（入院诊断信息）
        selectOne: function (item) {
            if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++; // 设置当前页号
                this.change(true, this.jbbmContent['jbmc']); // 传参表示请求下一页,不传就表示请求第一页
            } else { // 否则就是选中事件,为json赋值
                treatment.jbbmContent = item;
                treatment.json['cbzd'] = treatment.jbbmContent['jbmb'];
                $(".selectGroup").hide();
            }
        },

        //页面加载时自动获取门诊科室Dddw数据
        getMzksData: function () {
            var bean = {
                "ghks": "1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    treatment.mzksList = json.d.list;
                    //treatment.json.mzks = $("#ksList").val();
                } else {
                    malert("门诊科室列表查询失败" + json.c, 'top', 'defeadted');
                    return;
                }
            });

        },
        //页面加载时自动获取门诊医生Dddw数据
        getMzysData: function () {
            var bean = {
                "ysbz": "1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    treatment.mzysList = json.d.list;
                    treatment.glmzysList = json.d.list;
                    //treatment.json.mzys = userId; //默认为登陆人员
                } else {
                    malert("门诊医生列表查询失败" + json.c, 'top', 'defeadted');
                    return false;
                }
            });
        },

        //页面加载时自动获取门诊科室Dddw数据
        getZyksData: function () {
            var bean = {
                "zyks": "1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                if (json.a == 0) {
                    treatment.zyksList = json.d.list;
                } else {
                    malert("门诊科室列表查询失败" + json.c, 'top', 'defeadted');
                    return;
                }
            });

        },

        //门诊科室改变之后过滤门诊医生
        resultMzryChange: function (val) {
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            if (types == "mzks") {
                Vue.set(this.json, 'mzks', val[0]);
                this.mzysList = [];
                //过滤门诊医生
                this.mzysList = jsonFilter(this.glmzysList, "ksbm", this.json.mzks);
            }

            //方便更新试图（添加set。get方法）
            this.json = Object.assign({}, this.json);
            //回车跳转
            if (val[1] != null) {
                this.nextFocus(event);
            }
        },

        //保存
        saveData: function () {
            //挂号序号为空时不能进行操作
            if (!treatment.json.ryghxh) {
                this.ifClick = true;
                return false;
            }
            //挂号序号为空时不能进行操作
            if (!treatment.json.brxm) {
                this.ifClick = true;
                return false;
            }

            // 提交前验证数据(非空)
            if (!treatment.empty_sub('contextInfoMzry')) {
                this.ifClick = true;
                return false;
            }
            if (userNameBg.Brxx_List.bxjk == '001') {
                if (!this.isNhzd()) {
                    malert('农合系统没有' + userNameBg.Brxx_List.jbmc + '诊断信息，请重新填写', "right", 'defeadted');
                    return false
                }
            } else if (userNameBg.Brxx_List.bxjk == '002') {
                if (!this.initYb()) {
                    malert('医保系统没有' + userNameBg.Brxx_List.jbmc + '诊断信息，请重新填写', "right", 'defeadted');
                    return false
                }
            }

            //请求后台
            var json = JSON.stringify(this.json);
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglMzry&types=insert&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("上传数据成功", 'top', 'success');
                    treatment.closes();
                    treatment.getMzry(userNameBg.Brxx_List.ghxh);	//出门诊入院信息
                    treatment.ifClick = true;
                    treatment.jbbmContent = {};

                    // *********入院打印
                    treatment.printData = data.body.d;
                    treatment.printData.brxb = treatment.brxb_tran[treatment.printData.brxb];
                    treatment.printData.nldw = treatment.nldw_tran[treatment.printData.nldw];
                    treatment.print();
                } else {
                    malert("上传数据失败" + data.body.c, 'top', 'defeadted');
                    treatment.ifClick = true;
                }
            }, function (error) {
                console.log(error);
            });
        },
        //打印
        print: function () {
            console.log("print==>" + this.csqxContent.cs03001200126);
            if (this.csqxContent.cs03001200126 == 1) {
                //调用帆软打印
                var reportlets = "[{reportlet: 'fpdy%2Fzyys%2Fzyb_mzryz.cpt',ryghxh:'" + userNameBg.Brxx_List.ghxh + "'}]";
                console.log(reportlets);
                if (FrPrint(reportlets)) {
                    return;
                }
            } else {
                // 查询打印模板
                var json = {
                    repname: '入院申请单'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 清除打印区域
                    treatment.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    treatment.drawList = JSON.parse(json.d[0]['canvas']);
                    treatment.creatCanvas();
                    treatment.reDraw();
                    // 为打印前生成数据
                    treatment.printContent(treatment.printData);
                    // 开始打印
                    window.print();
                });
            }

        },
    },
});


var orignalSetItem = sessionStorage.setItem;
sessionStorage.setItem = function (key, newValue) {
    var setItemEvent = new Event('setItemEvent');
    setItemEvent.newValue = newValue;
    window.dispatchEvent(setItemEvent);
    orignalSetItem.apply(this, arguments);
};

function tabBg(page, index, event, num) {
    if(fyxmTab.csqx.N03001200134 != '1'){
        if (userNameBg.Brxx_List && userNameBg.Brxx_List.sfjzks != '1' || fyxmTab.csqx.N03001200134 != '1') {
            if (index == 1 || index == 2) {
                if (userNameBg.Brxx_List.wcbz == 0 && userNameBg.Brxx_List.zt == 0) {
                    malert("请先填写接诊信息！", 'top', 'defeadted');
                    return;
                }
            }
    }
    }

    if (num) {
        userNameBg.page = page
    }
    if (userNameBg.page == page && pageList == page) {
        xyz()
        if (num == 3) {
            dybl1()
        }
    } else {
        pageList = page;
        userNameBg.page = ''
        $('.isative').removeClass('active')
        fyxmTab.num = index;
        $(event).addClass('active')
        $(".loadPage").load(page + ".html", '', function () {
            if (num == 1) {
                setTimeout(function () {
                    xyz()
                }, 1000)
            }
            if (num == 3) {
                dybl1()
            }
        }).fadeIn(300);
    }

    if (index == 1) {
        $('.blRight').show();
        $('.blRight > .xyzImg').hide();
        $('.jcjy-position').hide();
    }
    if (index == 2) {
        $('.blRight').hide();
        $('.jcjy-position').show();
    }
    if (index == 0) {
        $('.blRight').show();
        $('.blRight > .xyzImg').show();
        $('.jcjy-position').hide();
    }

}
