<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <!--<script type="application/javascript" src="/newzui/pub/top.js"></script>-->
    <script type="application/javascript" src="/pub/top.js"></script>
    <!--<link href="../../../../../css/main.css" rel="stylesheet">-->
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="dylyd.css">
</head>
<body class="skin-default">
<div id="printFormat">
    <transition name="pop-fade">
        <div class="pop">
            <div class="popCenter">
                <!--<button class="fa fa-mail-reply-all printBtu" @click="isShow = false" style="right: 100px"></button>-->
                <button v-waves class="fa fa-print printBtu" @click="print"></button>
                <div class="cqyzd" v-for="(itemList, index) in jsonList">
                    <div class="lydTitle">病人领药单</div>
                    <div class="yzd-brInfo">
                        <div>
                            <span>姓名:</span>
                            <span>{{itemList.brxm}}</span>
                        </div>
                        <div>
                            <span>性别:</span>
                            <span>{{brxb_tran[itemList.brxb]}}</span>
                        </div>
                        <div>
                            <span>年龄:</span>
                            <span>{{itemList.nl}}{{nldw_tran[itemList.nldw]}}</span>
                        </div>
                        <div>
                            <span>住院号:</span>
                            <span>{{itemList.zyh}}</span>
                        </div>
                        <div>
                            <span>科别:</span>
                            <span>{{itemList.ryksmc}}</span>
                        </div>
                        <div>
                            <span>床号:</span>
                            <span>{{itemList.rycwbh}}</span>
                        </div>
                    </div>

                    <div class="ypsl-table">
                        <table cellspacing="0" cellpadding="0">
                            <tr>
                                <th style="min-width: 20px">序号</th>
                                <th style="min-width: 100px">申领时间</th>
                                <th style="min-width: 100px">药品名称</th>
                                <th style="min-width: 40px">药品规格</th>
                                <th style="min-width: 40px">数量</th>
                                <th style="min-width: 40px">单位</th>
                                <th style="min-width: 60px">用药方法</th>

                            </tr>
                            <tr v-for="(yZItem,$index) in itemList.yzxx"
                                :class="[{'tableTrSelect':isChecked['7_'+yZItem.no+'_'+$index]}]"
                                @click="checkOne('7_'+yZItem.no+'_'+$index)">
                                <td style="text-align:center;" v-text="$index+1"></td>
                                <td v-text="fDate(yZItem.slsj,'datetime')" style="text-align:center;"></td>
                                <td v-text="yZItem.ryypmc"></td>
                                <td v-text="yZItem.ypgg"></td>
                                <td v-text="yZItem.sl"></td>
                                <td v-text="yZItem.yfdwmc"></td>
                                <td v-text="yZItem.yyffmc"></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</div>
</body>
<script src="dylyd.js"></script>
</html>
