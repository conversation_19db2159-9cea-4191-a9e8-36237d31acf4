<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>入库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="rkgl.css" rel="stylesheet"/>
    <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="background-box">
    <div class="wrapper printHide" id="wrapper" v-cloak>
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kdFun(0)" v-show="isShowkd">开单</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kdFun(1)" v-if="isShowpopL">添加设备
                </button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)" v-show="!ShShow">刷新
                </button>
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)" v-show="ShShow">刷新</button>
                <!--<button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="clearAll()" v-show="TjShow">清空</button>-->
            </div>
            <div class="tong-search" :class="{'tong-padded':isShow}">
                <div class="flex-container padd-b-10 flex-align-c" v-show="isShowkd">
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">设备库</span>
                        <select-input class="wh122" @change-data="resultRydjChange" :child="KFList"
                                      :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm" :name="'param.kfbm'"
                                      :search="true" :index_mc="'kfmc'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace ">审核标志</span>
                        <select-input @change-data="resultChange"
                                      :child="ckglzt_tran"
                                      class="wh122"
                                      :index="param.zt"
                                      :val="param.zt"
                                      :name="'param.zt'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                        <div class="  flex-container flex-align-c">
                            <input class="zui-input  wh160 " placeholder="请选择申请开始日期" id="timeVal"/>
                            <span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh160 " placeholder="请选择申请结束时间" id="timeVal1"/>
                        </div>
                    </div>
                    <div class="flex-container flex-align-c">
                        <span class="ft-14 padd-r-5 whiteSpace">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text"
                               v-model="param.parm"/>
                    </div>
                </div>
                <div class="jbxx" v-show="!isShowkd">
                    <div class="jbxx-size">
                        <div class="jbxx-position">
                            <span class="jbxx-top"></span>
                            <span class="jbxx-text">基本信息</span>
                            <span class="jbxx-bottom"></span>
                        </div>
                        <div class="flex-container flex-align-c padd-l-10 padd-t-20">
                            <div class="flex-container flex-align-c">
                                <span class="ft-14 padd-r-5 whiteSpace">库房</span>
                                <select-input class="wh122" @change-data="resultRydjChange" :child="KFList"
                                              :index="'kfmc'"
                                              :index_val="'kfbm'" :val="rkd.kfbm" :name="'rkd.kfbm'"
                                              :search="true" :index_mc="'kfmc'" :disable="jyinput">
                                </select-input>
                            </div>

                            <div v-show="false" class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">入库方式</span>
                                <select-input class="wh120" @change-data="resultChange" :not_empty="false"
                                              :child="rkfs_tran"
                                              :index="rkd.rkfs" :val="rkd.rkfs" :name="'rkd.rkfs'">
                                </select-input>
                            </div>
                            <!--<div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">采购方式</span>
                                <select-input class="wh120" @change-data="resultChange" :not_empty="false"
                                              :child="cgfs_tran"
                                              :index="rkd.cgrkfs" :val="rkd.cgrkfs" :name="'rkd.cgrkfs'"
                                              :search="true" :index_mc="'cgrkfs'" :disable="jyinput">
                                </select-input>
                            </div>
                        -->
                            <div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">采购员</span>
                                <select-input class="wh120" @change-data="resultChange" :child="cgryList"
                                              :index="'ryxm'" :not_empty="true"
                                              :index_val="'rybm'" :val="rkd.cgry" :search="true" :name="'rkd.cgry'"
                                              :disable="jyinput">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">供货单位</span>

                                <select-input class="wh182" @change-data="resultChangeReset"
                                                  :not_empty="true" :child="gysList" :index="'gysmc'"
                                                  :index_val="'gysbm'" :val="rkd.gysbm"
                                                  :name="'rkd.gysbm'" :search="true">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20" v-if="!csqxContent.N04003001200111">
                                <span class="ft-14 padd-r-5 whiteSpace ">发票号</span>
                                <input type="number" @mousewheel.prevent class="zui-input wh120" v-model="rkd.fphm"
                                       @keydown="nextFocus($event)" :disabled="jyinput"/>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20" v-if="!csqxContent.N04003001200111">
                                <span class="ft-14 padd-r-5  whiteSpace">企业发票号</span>
                                <input type="number" @mousewheel.prevent class="zui-input wh120" v-model="rkd.qyfphm"
                                       @keydown="nextFocus($event)" :disabled="jyinput"/>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5  whiteSpace">备注说明</span>
                                <input type="text" class="zui-input wh400" v-model="rkd.bzms"
                                       @keydown="nextFocus($event)"
                                       @keydown.enter="kd(1)" :disabled="jyinput"/>
                            </div>
                        </div>
                        <div class="rkgl-kd">
                            <span>开单日期:<i v-text="fDate(zdrq,'datetime')"></i></span>
                            <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view">
            <!--入库列表-->
            <div class="zui-table-header" v-if="isShowkd">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>入库单号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>入库方式</span></div>
                        </th>

                        <th>
                            <div class="zui-table-cell cell-s"><span>入库时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>发票号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>总零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>总进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>差价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>采购员</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>制单员</span></div>
                        </th>
                        <!--                        <th>-->
                        <!--                            <div class="zui-table-cell cell-xl"><span>SPD单号</span></div>-->
                        <!--                        </th>-->
                        <th>
                            <div class="zui-table-cell cell-s"><span>备注</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body " v-if="isShowkd" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in rkdList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'background-red':item.totalYpjj != null ?item.totalYpjj.indexOf('-') == 0 : 0}]"
                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','rkdList'],$event)"
                        :tabindex="$index" @dblclick="openDetail($index)">
                        <td class=" cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.rkdh">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="cgfs_tran[item.cgrkfs]">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fphm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.totalYplj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.totalYpjj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"
                                 v-text="parseFloat(item.totalYplj) - parseFloat(item.totalYpjj)">序号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cgryxm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zdyxm">序号</div>
                        </td>
                        <!--                        <td>-->
                        <!--                            <div class="zui-table-cell cell-xl" v-text="item.spdno">序号</div>-->
                        <!--                        </td>-->
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bzms">状态</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <span v-text="zhuangtai[item.shzfbz]"
                                      :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : item.shzfbz=='3' ? 'color-wtg':'' "></span>
                            </div>
                        </td>
                        <td class=" cell-l">
                            <div class="zui-table-cell cell-l flex-center padd-t-5">
                                <!--                                0 未审核，1已审核 2 作废 3 未通过-->
                                <span class="width30 title icon-sh" v-if="item.shzfbz == 0" data-gettitle="审核"
                                      @click="showDetail($index)"></span>
                                <span class="width30 title icon-js" v-if="item.shzfbz == 0" data-gettitle="作废"
                                      @click="invalidData($index)"></span>
                                <span class="width30 title icon-bj" v-if="item.shzfbz == '0' || item.shzfbz == '1'"
                                      data-gettitle="编辑" @click="editIndex($index)"></span>
                            </div>
                        </td>
                        <p v-if="rkdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-r" v-if="isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in rkdList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                            @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex1',true,$index)">
                            <td class=" cell-l">
                                <div class="zui-table-cell cell-l flex-center padd-t-5">
                                    <span class="width30 title icon-sh" v-if="item.shzfbz == 0" data-gettitle="审核"
                                          @click="showDetail($index)"></span>
                                    <span class="width30 title icon-js" v-if="item.shzfbz == 0" data-gettitle="作废"
                                          @click="invalidData($index)"></span>
                                    <span class="width30 title icon-bj" v-if="item.shzfbz == '0' || item.shzfbz == '1'"
                                          data-gettitle="编辑" @click="editIndex($index)"></span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--end-->
            <!--添加材料-->
            <div class="zui-table-header" v-if="!isShowkd">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left"><span>设备名称1</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>机身号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>注册编号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>注册证号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>入库数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备进价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备总价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>强检日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>保修期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备产地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>外观质量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>验收结论</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>验收报告</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>验收报告下载</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备申请书</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备申请书下载</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>合同</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>合同下载</span></div></th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  " v-if="!isShowkd" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                        @mouseenter="switchIndex('activeIndex1',true,$index)" @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex1',true,$index)"
                        :tabindex="$index">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.sbmc">设备名称</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.sbgg">设备规格</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jsh">机身号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zcbh">注册编号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zczh">合格证号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.rksl">入库数量</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jj">设备进价</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-file="">设备总价</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.scrq,'date')">生产日期</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">有效期至</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.qjrq,'date')">强检日期</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.bxrq,'date')">保修日期</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cd">设备产地</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.kfdwmc">库房单位</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.wgzl">外观质量</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ysjl">库房单位</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ysbgname">验收报告</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"> <a :href="'http://localhost:8081'+item.ysbgfiles"  v-text="item.ysbgname" target="_blank" >设备申请书下载</a></div>

                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.sbsqsname">设备申请书</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"> <a :href="'http://localhost:8081'+item.sbsqsfiles"  v-text="item.sbsqsname" target="_blank" >设备申请书下载</a></div>

                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.htname">合同</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"> <a :href="'http://localhost:8081'+item.htfiles"  v-text="item.htname" target="_blank" >设备申请书下载</a></div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s flex-center padd-t-5">
                                <span class="icon-bj width30 title icon-bj-t" v-if="mxShShow" data-gettitle="编辑"
                                      @click="edit($index)"></span>
                                <span class="icon-sc width30  title" v-if="mxShShow && !rkd.shzfbz" data-gettitle="删除"
                                      @click="scmx($index)"></span>
                            </div>
                        </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-fixed table-fixed-l" v-if="!isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                            @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex1',true,$index)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-fixed table-fixed-r" v-if="!isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                            @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex1',true,$index)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s flex-center padd-t-5">
                                    <span class="icon-bj width30 title icon-bj-t" v-if="mxShShow" data-gettitle="编辑"
                                          @click="edit($index)"></span>
                                    <span class="icon-sc width30  title" v-if="mxShShow && (rkd.shzfbz == '0' || !rkd.shzfbz) "
                                          data-gettitle="删除" @click="scmx($index)"></span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <page @go-page="goPage" v-if="isShowkd" :totle-page="totlePage" :page="page" :param="param"
                  :prev-more="prevMore" :next-more="nextMore"></page>
            <div class="zui-table-tool flex-container padd-l-10 padd-r-10 flex-jus-sb" v-if="!isShowkd">
                    <span class="flex-container">
                        <i class="padd-r-10">材料进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></i>
                        <i>材料零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
                    </span>
                <span class="flex-container">
                        <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print" v-if="dyShow">打印</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick"
                                v-if="isCx(rkd) && dyShow">冲销</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick"
                                v-if="!dyShow && cxShow && isCx(rkd)">取消冲销</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-if="zfShow"
                                id="zfbtn">作废</button>
                        <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()"
                                v-if="TjShow && isCx(rkd)">提交</button>
                        <button class="tong-btn btn-parmary xmzb-db" @click="passData()" v-if="ShShow && isShFun(rkd)">审核</button>
                    </span>
            </div>
        </div>

    </div>
</div>
<!--侧边窗口-->
<div class="side-form  pop-548" :class="{'ng-hide':index==0}" v-cloak id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                <i>设备名称</i>
                <input autocomplete="off" id="sbmc" class="zui-input" ref="sbmc" v-model="popContent.sbmc"
                       @keydown="changeDown($event,$event.target.value,'text')"
                       @input="change(false, $event.target.value)">
                <search-table :page="queryStr" :message="searchCon" :selected="selSearch" :them="them"
                              :them_tran="them_tran"
                              :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>
            </li>

            <li>
                <i>设备规格</i>
                <input type="text" class="label-input background-h" v-model="popContent.sbgg" @keydown="nextFocus($event)" disabled="disabled"/>
            </li>
            <!--<li>-->
                <!--<i>供应商单位</i>-->
                <!--<select-input @change-data="resultChange" :child="gysList" :index="'gysmc'"-->
                              <!--:index_val="'gysbm'" :val="popContent.gysbm" :search="true" :name="'popContent.gysbm'">-->
                <!--</select-input>-->
            <!--</li>-->
            <li>
                <i>生产厂家</i>
                <input  id="sccj" type="text" class="label-input " v-model="popContent.sccj" @keydown="nextFocus($event)">

            </li>
            <li>
                <i>机身号</i>
                <input type="text" class="label-input " v-model="popContent.jsh" @keydown="nextFocus($event)">
            </li>
            <li>
                <i>折旧类型</i>
                <select-input @change-data="resultChange" :child="zjList" :index="'zjmc'"
                              :index_val="'zjbm'" :val="popContent.zjbm" :search="true" :name="'popContent.zjbm'">
                </select-input>
            </li>
            <li>
                <i>入库数量</i>
                <input  id="rksl" type="number" class="label-input " v-model="popContent.rksl" @keydown="nextFocus($event)">
            </li>

            <li>
                <i>设备进价</i>
                <input type="text" class="label-input " v-model="popContent.jj"  @keydown="nextFocus($event)">
                <em class="xstz">元</em>
            </li>
            <li>
                <i>生产批号</i>
                <input type="text" class="label-input " v-model="popContent.scph"  @keydown="nextFocus($event)">
            </li>
            <li>
                <i>强检日期</i>
                <input v-model="popContent.qjrq" class="zui-input  times3" id="_qjrq"  @click="showTime('_qjrq','qjrq')" @keyup="setTime($event,'qjrq')"
                       @keydown.13="addData($event)">
            </li>
            <li>
                <i>保修日期</i>
                <input v-model="popContent.bxrq" class="zui-input  times4" id="_bxrq"  @click="showTime('_bxrq','bxrq')" @keyup="setTime($event,'bxrq')"
                       @keydown.13="addData($event)" >
            </li>
            <li>
                <i>生产日期</i>
                <input v-model="popContent.scrq" class="zui-input  times1" id="_scrq"  @click="showTime('_scrq','scrq')" @keyup="setTime($event,'scrq')"
                       @keydown="nextFocus($event)"  >
            </li>
            <li>
                <i>有效期至</i>
                <input v-model="popContent.yxqz" class="zui-input  times2" id="_yxqz"  @click="showTime('_yxqz','yxqz')" @keyup="setTime($event,'yxqz')"
                       @keydown.13="addData($event)" >
            </li>
            <li>
                <i>设备产地</i>
                <input type="text" class="label-input" v-model="popContent.cd" @keydown="nextFocus($event)" disabled="disabled">
            </li>
            <!--   <li>
                       <i>库房单位</i>
                      <select-input @change-data="resultChange" @keydown="nextFocus($event)" :child="wzkfList"
                                    :index="'wzkfmc'" :index_val="'wzkfbm'" :val="popContent.kfdwmc" :search="true"
                                    :name="'popContent.kfdwmc'">
                      </select-input>
              </li> -->
            <li>
                <i>单位</i>
                <input type="text" class="label-input  background-h" v-model="popContent.lydwmc" disabled>
            </li>
            <li>
                <i>外观质量</i>
                <select-input @change-data="resultChange" :disable="isQTRK2" :child="wgzlList"
                              :index="'popContent.wgzl'" :val="popContent.wgzl"
                              :name="'popContent.wgzl'">
                </select-input>
            </li>
            <li>
                <i>验收结论</i>
                <select-input @change-data="resultChange" :disable="isQTRK2" :child="ysjlList"
                              :index="'popContent.ysjl'" :val="popContent.ysjl"
                              :name="'popContent.ysjl'">
                </select-input>
            </li>
            <li>
                <i>注册编号</i>
                <input type="text" class="label-input" v-model="popContent.zcbh" @keydown="nextFocus($event)">
            </li>
            <li>
                <i>注册证号</i>
                <input type="text" class="label-input" v-model="popContent.zczh"  @keydown.13="save">
            </li>
            <li>
                <i>验收报告</i>
                <input type="file" class="label-input"  @change="getFile($event,1)">
            </li>
            <li>
                <a href=""    id ="ysbgname" target="view_window"></a>
            </li>
            <li>
                <i>设备申请书</i>
                <input type="file" class="label-input" @change="getFile($event,2)">
            </li>
            <li>
                <a href=""   id ="sbsqsname" target="view_window"></a>
            </li>
            <li>
                <i>采购合同</i>
                <input type="file"  class="label-input"   @change="getFile($event,3)">
            </li>
            <li>
                <a href=""   id ="htname" target="view_window"></a>
            </li>
			
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="addData">保存</button>
    </div>
</div>
<script src="rkgl.js?v=2"></script>
</body>

</html>
