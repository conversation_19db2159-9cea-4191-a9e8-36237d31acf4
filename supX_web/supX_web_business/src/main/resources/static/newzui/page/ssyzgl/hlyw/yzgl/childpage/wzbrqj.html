<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>危重病人抢救</title>
    <link href="childpage/wzbrqj.css" rel="stylesheet">
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
    <link rel="stylesheet" href="/newzui/pub/css/print.css" media="print"/>
</head>
<body>
<main id="content" class="padd-l-10 padd-r-10 flex-container flex-dir-c flex-one over-auto" v-if="pageShow">
    <div v-for="(list,index) in 2" :style="{'marginTop':index>=1?'35px':''}">
        <div >
            <p class="swtl">危重抢救{{index+1}}</p>
            <p class="swtl-hr"></p>
        </div>
        <div class="flex-container" >
            <div class="width973" >
                <ul class="swtl-item">
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-date swtl-icon"></span>
                            <span class="icon-text color-green">起止时间</span>
                        </div>
                        <div class="padd-b-15">
                            <p class="date swtl-text-content color-333" v-if="editText">2020年12月12日 12:55:12</p>
                            <div class="zui-date relative swtl-text-content" v-show="!editText"  style="width: 360px">
                                <i class="datenox icon-rl"></i>
                                <input class="zui-input  color-333 text-indent-0" id="timeVal" style="padding-top: 0" value="2020年12月12日 12:55:12">
                            </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-ch swtl-icon"></span>
                            <span class="icon-text color-f4b26b">参加人员</span>
                        </div>
                        <div  class="flex-container margin-b-15 padd-b-15 swtl-text-content">
                            <div @mouseleave="hoverName()" class="Headimg " :class="!editText?'HeadPortrait HeadStart':'HeadImg'" v-for="(list,$index) in 1" :id="list">
                                <p  @mouseenter="hoverName(true,$index,$event)" class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span class="iconfont  icon-upload col6_span1" v-if="!editText"   @mouseenter="tipsShow=true"
                                  @mouseleave="tipsShow=false">
                                    <i v-show="tipsShow"  class="tips">添加主持人</i>
                                </span>
                            <div @mouseleave="hoverName()" class="Headimg" :class="!editText?'HeadPortrait HeadStart':''" v-for="(list,$index) in 6" :id="list">
                                <p @mouseenter="hoverName(true,$index,$event)"  class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="color-757c83 font12">刘医生</p>
                            </div>
                            <span class="iconfont  icon-upload col6_span1" v-if="!editText"   @mouseenter="tipsShow1=true"
                                  @mouseleave="tipsShow1=false">
                                    <i v-show="tipsShow1" class="tips">添加参与人</i>
                                </span>
                            <div class="hoverAvter" v-show="userName" :style="[{left:objabsolute.left+'px'},{top:objabsolute.top+'px'}]">
                                <span class="djzt">科主任</span>
                                <p class="headImg margin-t-10" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)">
                                <p class="username text-center margin-t-5 margin-b-5 font-16">刘医生</p>
                                <div class="flex-container flex-jus-c">
                                    <span class="color-ff5c63 font12 padd-r-10">56岁</span>
                                    <span class="color-green font12 padd-r-10">外科</span>
                                    <span class="color-757c83 font12">主任医师</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-zj swtl-icon"></span>
                            <span class="icon-text color-72bc1a">抢救过程</span>
                        </div>
                        <div class="padd-b-15">
                            <p v-if="editText" class="swtl-text-content color-333">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1"  class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                    </li>
                    <li class="list VerticalLine">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-qz swtl-icon"></span>
                            <span class="icon-text color-00a7ff">现场照片</span>
                        </div>
                        <div class="padd-b-15 margin-t-5">
                            <ul class="Photo swtl-text-content flex-container">
                                <li class="PhotoImg icon-Photo iconfont" v-for="(list,index) in 8" @click="preview" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)"></li>
                            </ul>
                        </div>
                    </li>
                    <preview :preview-update="previewshow" :src="'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532503254326&di=a2175d25c9f4ccc582e3ae37531ec0a4&imgtype=0&src=http%3A%2F%2Fwww.wallcoo.com%2Fcartoon%2FKitsunenoir_Design_Illustration_V%2Fwallpapers%2F2560x1440%2Fkim-holtermand-reflections.jpg'" @preview-hide="previewHide"></preview>
                    <li class="list">
                        <div class="flex-container flex-align-c">
                            <span class="icon-img-jl swtl-icon"></span>
                            <span class="icon-text color-f4b26b">记录者</span>
                        </div>
                        <div class="padd-b-15">
                            <p v-if="editText" class="swtl-text-content color-333">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</p>
                            <textarea v-if="!editText" rows="1" class="zui-input swtl-text-content text-indent-0 color-333" @keydown="rowsJ($event)">1、 病情评估。护士双手拍打患者双肩并呼唤患者，判断有无反应；以耳听、面感、眼观法评估患者呼吸情况，若无反应即刻进行心肺复苏。</textarea>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="flex-container relative" style="width: 3.5%" >
                <i class="iconfont icon-iocn46"></i>
                <span class="font14 cursor" @click="editShow" style="position: absolute;right: 0;z-index: 11111;">编辑</span>
            </div>
        </div>
    </div>
</main>
<div class="flex-container flex-jus-c flex-one flex-align-c" v-if="pageShow" id="rescue">
    <div>
        <div class="fqtlbg" >
            <div v-if="fqqjShow">
                <div class="point point1"></div>
                <div class="point point2"></div>
                <div class="point point3"></div>
            </div>
            <div class="bg-center " :class="success?'Success':!success?'start':success==undefined?'fail':''" @click="!success?rescueClick():''"></div>
            <p class="color-1abc9c margin-t-5 text-center">{{fqqj}}</p>
        </div>
        <div class="flex-container" v-if="fqqjShow" style="margin-top: -29px">
            <div class="HeadPortrait" :class="fail?'startFail':!success?'startFail':''" v-for="list in objAvatar" :id="list">
                <p class="headImg" style="background-image: url(https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg)"></p>
                <p class="color-f2a654 font12">刘医生</p>
            </div>
            <span class="iconfont  icon-upload col6_span1"></span>
        </div>
        <div class="flex-container flex-jus-c margin-t-10">
            <button v-if="fqqjShow && fqsucc" class="tong-btn btn-parmary" @click="successShow">确定</button>
            <button v-if="fqqjShow" class="tong-btn btn-parmary-f2a" @click="failShow">发起失败测试</button>
            <button v-if="fail" class="tong-btn btn-parmary-f2a" @click="clear">取消</button>
            <button v-if="fail" class="tong-btn btn-parmary" @click="successShow">再次发起</button>
        </div>
    </div>
</div>
<div class="side-form  pop-width"  :class="{'ng-hide':type}" v-cloak id="brzcList" role="form">
    <div class="fyxm-side-top flex-between">
        <span>添加抢救医生</span>
        <span class="fr closex ti-close"></span>
    </div>
    <div class="tong-search">
        <div class="top-form">
            <label class="top-label font14">科室</label>
            <select-input style="width: 122px;" @change-data="resultChange" :not_empty="false"
                          :child="jzType_tran" :index="popContent.jzbz" :val="popContent.jzbz"
                          :name="'popContent.jzbz'">
            </select-input>
        </div>
    </div>
    <div class="ksys-side" style="padding:0 8px 0 9px">
        <div class="zui-table-view">
            <div class="zui-table-header">
                <table class="zui-table" >
                    <thead>
                    <tr>
                        <th>
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>医生姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-m"><span>科室</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>职称</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index" v-for="(item, $index) in 25"  @dblclick="edit($index)">
                        <td>
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td ><div class="zui-table-cell cell-s"><span>周立军</span></div></td>
                        <td ><div class="zui-table-cell cell-m"><span>全科</span></div></td>
                        <td ><div class="zui-table-cell cell-s"><span>科主任</span></div></td>
                    </tr>
                    </tbody>
                </table>
                <!--<p v-if="jsonList.length==0" class=" noData text-center zan-border">暂无数据...</p>-->
            </div>
        </div>
    </div>
    <div class="ksys-btn zui-table-tool" style="height: 66px">
        <button class="root-btn btn-parmary-d9" @click="Sure">确定</button>
        <button class="root-btn btn-parmary" @click="cancel">取消</button>
    </div>
</div>
<script type="text/javascript" src="childpage/wzbrqj.js"></script>
</body>
</html>
