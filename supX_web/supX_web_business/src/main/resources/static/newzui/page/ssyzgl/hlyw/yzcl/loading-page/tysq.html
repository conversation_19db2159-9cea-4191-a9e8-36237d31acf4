<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/newzui/css/icon.css">
    <link rel="stylesheet" href="tysq.css">
</head>
<body class="skin-default">
<div class="wrapper" id="loadingPage" v-cloak>
    <div class="bryz-list">
        <header class="userNameBg flex-container flex-align-c flex-wrap-w">
                        <span class="userName" v-text="brInfo.brxm"></span>
                        <span class="sex text" v-text="brxb_tran[brInfo.brxb]"></span>
                        <span class="nl text">{{brInfo.nl}}{{xtwhnldw_tran[brInfo.nldw]}}</span>
                        <span class="cwh text" v-text="'床位号：'+ brInfo.rycwbh +'号'"></span>
                        <span class="zyh text" v-text="'住院号：'+ brInfo.zyh"></span>
                        <span class="bq text" v-text="'科室：'+ brInfo.ryksmc"></span>
                        <span class="ys text" v-text="'医师：'+ brInfo.zyysxm"></span>
                        <span class="brzt text" v-text="'病人状态：'+zyzt_tran[brInfo.zyzt]"></span>
                        <span class="bz text" v-text="'病种：'+ brInfo.ryzdmc"></span>
        </header>
        <tabs :num="num" :tab-child="[{text:'退药申请'},{text:'退药申请明细'}]" @tab-active="tabBg"></tabs>
            <div class="flex-container flex-align-c padd-b-10 padd-l-10 padd-t-10">
                <div class="flex-container flex-align-c" v-if="brlistjson.csqx && !brlistjson.csqx.N03004200707">
                    <span class="ft-14 padd-r-5">选择药房</span>
                    <select-input class="wh120" @change-data="resultChange_ks"
                                  :not_empty="false"
                                  :child="allKs"
                                  :index="'yfmc'"
                                  :index_val="'yfbm'"
                                  :val="jsContent.yfbm"
                                  :name="'jsContent.yfbm'"
                                  :search="true"></select-input>
                </div>
                <div class="flex-container flex-align-c padd-l-10">
                    <span class="ft-14 padd-r-5">时间</span>
                    <input class="zui-input  wh220" placeholder="不限定时间范围" id="timeVal"/>
                </div>
                <div class="flex-container flex-align-c padd-l-10" v-if="num == 0">
                    <span class="ft-14 padd-r-5">检索</span>
                    <input class="zui-input wh182" placeholder="药品名称/拼音"
                           type="text" v-model="text" @keydown.enter="getData()"/>
                </div>
            </div>
            <div key="a" v-if="num==0" class="zui-table-view padd-r-10 padd-l-10">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th v-if="brlistjson.csqx && !brlistjson.csqx.N03004200707">
                                <div class="zui-table-cell cell-s"><span>药房名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>药品名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品种类</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品零价</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>发药数量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>退药数量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l"><span>已登记退药数量</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>申请退药数量</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>用药时间</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(item, $index) in fymxList"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex},{'bg-red':item.sfty==1}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @dblclick="editComputed(item)"
                            @click="checkSelect([$index,'some','jsonList'],$event)"
                            :tabindex="$index"
                            ref="list">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td v-if="brlistjson.csqx && !brlistjson.csqx.N03004200707">
                                <div class="zui-table-cell cell-s" v-text="item.yfmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left title text-over-2"
                                     v-text="item.ryypmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.ypzlmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.fysl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.tysl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l" v-text="item.ysqsl"></div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s padd-l-5 padd-r-5" >
                                    <input class="zui-input text-center" style="height: 28px;"
                                           v-model="item.sqsl" @input="tyslchange(item)">
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s" v-text="fDate(item.yyrq,'date')"></div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                               <span class="flex-center padd-t-5" v-if="item.tysqdh">
                                    <em class="width30"><i class="icon-dysqblack" data-title="打印退药申请单"
                                                           @click="printTysqd(item.tysqdh)"></i></em>
                                </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <!--左侧固定-->
                <div class="zui-table-fixed table-fixed-l">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in fymxList"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                :tabindex="$index"
                                ref="list">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-s">
                                    <div class="zui-table-cell cell-s"><span>申请退药数量</span></div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell cell-s"><span>发药时间</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in fymxList"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)"
                                :tabindex="$index"
                                ref="list">
                                <td class="cell-s">
									
                                    <div class="zui-table-cell cell-s" style="padding: 0 10px;">
                                        <input class="zui-input" style="height: 28px;text-align: center;"
                                               v-model="item.sqsl" @input="tyslchange(item)">
                                    </div>
                                </td>
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s" v-text="fDate(item.yyrq,'date')"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <div class="flex-container tymx flex-one padd-l-10 padd-r-10" key="b" v-if="num==1">
                <div class=" zui-table-view  padd-r-10 padd-l-10    ">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-l">退药单号</div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body flex-one"  @scroll="scrollTable($event)">
                        <table class="zui-table table-width50">
                            <!-- v-if="jsonList.length!=0" -->
                            <tbody>
                            <tr :tabindex="$index"
                                :class="[{'table-hovers':$index===activeIndex3,'table-hover':$index === hoverIndex3}]"
                                @mouseenter="switchIndex('hoverIndex3',true,$index)"
                                @mouseleave="switchIndex()"
                                @click="getTydPerson(item.tymx,$index),switchIndex('activeIndex3',true,$index)"
                                v-for="(item, $index) in treeTydList" class="tableTr2">
                                <th>
                                    <div class="zui-table-cell cell-l">{{item.tysqdh}}</div>
                                </th>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            <div class=" zui-table-view padd-r-10 padd-l-10">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'tydmxList'" :type="'all'"
                                                :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-s"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>床号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>剂型</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell  cell-xl text-left"><span>药品名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l"><span>规格</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>申请数量 </span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>审核数量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>已退数量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>单位</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body flex-one"  @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <!-- v-if="jsonList.length!=0" -->
                        <tbody>
                        <tr :tabindex="$index" v-for="(item, $index) in tydmxList" class="tableTr2">
                            <!--@dblclick="edit($index)"双击回调-->
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'tydmxList'" :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-s " v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.rycwbh">08</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.brxm">顾声明</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.jxmc">001093</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ryypmc">
                                    氯化钠注射剂
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l" v-text="item.ypgg">100ml*100</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.sqsl">2</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.hdsl">0</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.tysl">0</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yfdwmc">袋</div>
                            </td>

                        </tr>
                        </tbody>
                    </table>
                    <!-- <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p> -->
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn action-bar fixed" >
        <button v-if="num == 0" v-waves class="zui-btn btn-danger xmzb-db qxsh" @click="closePage">取消</button>
        <button v-if="num == 0" v-waves class="zui-btn btn-primary xmzb-db" @click="saveTy()">退药</button>
        <!-- <button v-if="num == 0" v-waves class="zui-btn btn-primary xmzb-db" @click="openTy()">退药审核</button> -->
        <button v-if="num == 1" v-waves class="zui-btn   btn-parmary-b   xmzb-db  " v-waves   @click="notCheck()">作废</button>
        <button v-if="num == 1" v-waves class="zui-btn   btn-parmary-f2a xmzb-db  " v-waves   @click="printTysqd()">打印</button>
    </div>
</div>


<script src="tysq.js"></script>
</body>
</html>
