var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        popContent: {},
        userInfo:{},
        jsonList: [],
        yfkfList: [],
        ryList :[],
        ksList:[],
        zkksList:[],
        zkztList:[{
            'zkztbm':1,
            'zkztmc':'正常'
        },{
            'zkztbm':2,
            'zkztmc':'报修'
        },{
            'zkztbm':3,
            'zkztmc':'送检'
        }],
        mbModel: false,
        param: {
            'page': 1,
            'rows': 100,
            'order': 'desc',
            'shzfbz': 1,
            'kfbm': '',
            'rkfs': '01',//01-出库
            'beginrq': null,
            'endrq': null,
            'parm': ''
        },
        zkparam: {
            'page': 1,
            'rows': 100,
            'order': 'desc',
            'shzfbz': 1,
            'parm': ''
        }
    },
    updated:function () {
        changeWin()
    },
    mounted: function () {
        this.getKs();
        this.getUserInfo();
    },
    methods: {
        //科室
        getKs: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                function (data) {
                    wrapper.ksList = data.d.list;
                    wrapper.zkksList = data.d.list;

                });
        },
        //加载采购人员
        getCgry: function () {
            console.log(123);
            var parm = {ksbm: this.userInfo.ksbm};
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&json='+JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        wrapper.ryList = data.d.list;
                        wrapper.popContent.rybm = data.d.list[0].rybm;
                    } else {
                        malert("获取采购人员失败！", 'top', 'defeadted');
                    }
                });
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this.param, 'ksbm', val[0]);
            Vue.set(this.param, 'ksmc', val[4]);
            this.getData();
        },
        //组件选择下拉框之后的回调
        resultKsdjChange: function (val) {
            Vue.set(this.zkparam, 'ksbm', val[0]);
            Vue.set(this.zkparam, 'ksmc', val[4]);
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    this.userInfo = json.body.d;
                    console.log(this.userInfo.ksbm);
                });
        },
        //双击修改有效期和批次停用
        edit: function(index) {
            this.getCgry();
             this.mbModel = true;
             if(this.jsonList[index].cfd != null){
                 Vue.set(this.popContent, 'cfd', wrapper.jsonList[index].cfd);
             }else{
                 Vue.set(this.popContent, 'cfd', "123");
             }
             console.log(wrapper.jsonList[index]);
             Vue.set(this.popContent, 'zkzt', wrapper.jsonList[index].zkzt);
            if(this.jsonList[index].ksglry != null){
                Vue.set(this.popContent, 'ksglry', wrapper.jsonList[index].ksglry);
            }
            this.popContent.yljgbm = wrapper.jsonList[index].yljgbm;
            this.popContent.scph = wrapper.jsonList[index].scph;
            this.popContent.sbbm = wrapper.jsonList[index].sbbm;
            this.popContent.ksbm = wrapper.jsonList[index].ksbm;
        },
        saveData: function() {
            if(!this.popContent.cfd) {
                malert('请输入存放地','top','defeadted');

                return;
            }
            if(!this.popContent.zkzt) {
                malert('请输入设备状态','top','defeadted');
                return;
            }
            if(!this.popContent.ksglry) {
                malert('请输入科室管理人员编码','top','defeadted');
                return;
            }

            console.log(this.popContent);


            $.getJSON("/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=updateKs&parm=" +
                JSON.stringify(this.popContent),
                function(data) {
                    if(data.a == 0) {
                        malert('保存成功','top','success');
                        wrapper.getData();
                    } else {
                        malert('上传数据失败','top','defeadted');
                    }
                })
        },
        getData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=kskc&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                }
            });
        }
    },
});

laydate.render({
    elem: '.todate'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        // wrapper.param.time = value
        wrapper.param.beginrq = value.slice(0, 10);
        wrapper.param.endrq = value.slice(13, 23);
        wrapper.getData();
    }
});

