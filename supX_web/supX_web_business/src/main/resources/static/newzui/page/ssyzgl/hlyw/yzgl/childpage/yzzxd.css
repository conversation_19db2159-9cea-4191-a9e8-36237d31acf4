.tableDiv table{
	table-layout: fixed;/*只有定义了table下面的td才起作用*/
}
.tableDiv table td{
	max-width: 96px;
	overflow: hidden;/*内容超出宽度是隐藏超出部分的内容*/
	text-overflow: ellipsis;/*这两个配合使用（超出部分用...代替）*/

}
.left{
	float: left;
	width: 22%;
	font-size: 12px;
	font-family: 'Microsoft JhengHei UI';
	overflow-y: auto;
}
#bodyMenu{
	border-left: 3px solid #dfdfdf;
	border-right: 3px solid #dfdfdf;
	width: 84%;
	/* 注释这个height的原因是有这个height的时候执行单不能滚动。 */
	/* height: 100%; */
}
.zui-input{
text-indent: 0;
}
.right{
	margin: 5px 0 0 5px;
	width: 14%;
	height: calc(100% - 8px);
}
.jsxx_search{
	color: #fff;
	float: right;
	margin-right: 26px;
	background: #1AB394;
}
.pop{
	display: inline-block;
}
.CFUse {
	position: relative;
	background-color: #FFFFFF;
	box-shadow: #333 2px 2px 8px;
	width: 800px;
	height: 450px;
	margin: 0 auto;
}
.popTable th{
	width: auto;
}

.popTable td{
	width: 100%;
}

.brList{
	margin-top: 8px;
	margin-left: 8px;
	height: calc(100% - 176px);
	width: calc(100% - 16px);
}

.zxTime{
	border: 0;
	padding: 10px 0;
}
.fyxm-tab{
	height: auto;
}

#bodyMenu .wh180 {
	width: 180px !important;
	margin: 0 0 !important;
}

#rightPcxx .tableDiv{
	height: 100%;
}

.InfoMenu{
	position: relative;
	border-top: 1px dashed #aaa;
	/*width: 100%;*/
}

#bodyMenu th{
	width: 44px;
}

#bodyMenu td{
	width: auto;
}

.yzzxTable{
	width: 95%;
	/*margin-top: 10px;*/
	font-size: 12px;
	/*height: calc(100% - 62px);*/
}


.printBtu{
	position: absolute;
	right: 0;
}
.table_tab1 td {
	border: 1px solid #CCCCCC;
	height: 34px;
	font-size: 14px;
	padding: 0 5px;
}
.table_tab1 th {
	background-color: #FFFFFF;
	border: 0;
}
#bodyMenu th {
	width: 44px;
}
.patientTable td, th {
	height: 24px;
	
	font-size: 14px;
	cursor: default;
	white-space: nowrap;
}
.enter_tem1{
	border: 0;
	padding: 0 10px;
}

.table_tab1{
	overflow: inherit;
	display: initial;
}

.table_tab1 th{
	background-color: #FFFFFF;
	border: 0;
}

.zxdDiv{
	height: calc(100% - 90px);
	overflow: scroll;
}

.popCenter{
	overflow: scroll;
	height: 100%;
	width: 100%;
	background-color: white;
}

.sameStart{
	position: absolute;
	border-top: 1px solid #000000;
	border-right: 1px solid #000000;
	border-left: 0;
	border-bottom: 0;
	width: 10px !important;
	height: 50%;
	right: 50%;
	bottom: 0;
}
.sameEnd{
	position: absolute;
	border-top: 0;
	border-right: 1px solid #000000;
	border-left: 0;
	border-bottom: 1px solid #000000;
	width: 10px !important;
	height: 50%;
	right: 50%;
}
.same{
	position: absolute;
	border-top: 0;
	border-right: 1px solid #000000;
	border-left: 0;
	border-bottom: 0;
	width: 10px !important;
	height: 100%;
	right: 50%;
	top: 0
}

.yzd-way{
	width: auto !important;
	margin-left: 18px;
}

.yzd-table td, .yzd-table th{
	position: relative;
}

.YzTabTable th:nth-child(n+2){
	min-width: 97px;
}

.pslr ul{
	min-width: 85px;
	width: 85px;
	top: 28px;
}

.pslr td{
	overflow: inherit !important;
}

.yzd-table-blank span{
	width: 100% !important;
	height: 50%;
	text-align: center !important;
}

.yzd-table-blank span:first-child{
	border-bottom: 1px solid #999
}

.tzyzqm{
	height: 28px;
	text-align: left;
}

.tzyzqm span{
	margin: 0 60px 0 20px;
	float: none !important;
	display: inline !important;
}
/*
.ysDiv{
	margin: 10px 0 0 0;
}
.yzd-brInfo{
	margin: 3px 0;
}
*/


.padding-t2{
	padding-top: 2%;
}

.yzclSearch {
	/* background-color: #79D6FF; */
	color: #000000;
	border-bottom: 4px solid #eeeeee;
	border-right: 4px solid #eeeeee;
	margin-top: 0;
}

.YPTable {
	padding-left: 4%;
}

.YPTable td {
	border: 1px solid #bbbbbb;
	height: 25px;
	font-size: 12px;
}

.YPTable th {
	font-size: 12px;
}

.YPTable tr:nth-child(n+2):hover {
	background-color: #EAF2FF;
}

.CFTabTable {
	float: left;
	padding: 0;
	width: calc(100% - 10px);
	height: calc(100% - 214px);
	border-right: 4px solid #EEEEEE;
	overflow: auto;
	margin-left: 6px;
}

.CFTabTable table {
	/*border-left: 1px solid #bbbbbb;*/
	/*border-right: 1px solid #bbbbbb;*/
	/*border-top: 1px solid #bbbbbb;*/
}

.CFTabTable tr {
	border-bottom: 1px solid #bbbbbb;
}

.CFTabTable td {
	border: 1px solid #CCCCCC;
	padding: 6px;
}

.toolMenu img {
	width: 20px;
	margin-bottom: 4px;
}

.yzcl_context {
	float: left;
	margin-top: 44px;
	width: 100%;
}

.YZInfo {
	width: 70%;
	margin-left: 8px;
	float: left;
}

.YZChoice {
	display: inline-block;
	background-color: #eee;
	width: 100%;
}

.YZChoice > div {
	float: left;
	position: relative;
	margin: 5px 10px 5px 4px;
}

.YZChoice > div > input {
	width: 90px;
}

.YZChoice > div > span {
	position: absolute;
	top: 3px;
	font-size: 14px;
}

.personInfo {
	display: inline-block;
}

.personInfo > div {
	float: left;
	margin-right: 10px;
}

.yzdItem {
	margin-top: 20px;
	text-align: center;
}

.table_tab1 tr td:first-child {
	text-align: center;
}

.table_tab1 {
	height: auto;
}

.table_tab1 th {
	background-color: #eeeeee;
	border-left: 1px solid #eee;
	border-right: 1px solid #eee;
}

.AllYZ {
	height: calc(100% - 260px);
	overflow: scroll;
}

.AllYZSH {
	height: calc(100% - 270px);
	overflow: scroll;
}

.AllYZCX {
	height: calc(100% - 160px);
	overflow: scroll;
}

.YZInfo textarea {
	/* margin-top: 10px; */
	height: 70px;
	width: 100%;
	background-color: #eeeeee;
}

.patientBaseInfo {
	width: calc(30% - 14px);
	background-color: #eeeeee;
	float: left;
	margin-left: 6px;
}

.baseTitle {
	border-bottom: 1px solid #bbbbbb;
	padding: 6px 0 6px 12px;
}

.baseInfo div {
	width: 100%;
	margin: 2px;
	display: inline-block;
	position: relative;
}

.baseInfo span {
	display: inline-block;
	background-color: #FFFFFF;
	float: left;
	padding: 4px;
}

.baseInfo span:first-child {
	width: 50px;
	margin-right: 2px;
	padding-left: 20px;
}

.baseInfo span:nth-child(2) {
	width: calc(100% - 90px);
	min-height: 15px;
}

.baseInfo span:nth-child(3) {
	position: absolute;
	left: 0;
	color: #F7D063;
	top: 2px;
}

.ChildtablePage {
	border-top: 4px solid #EEEEEE;
	border-right: 4px solid #EEEEEE;
	background: linear-gradient(to bottom, #ffffff 0, #ffffff 100%);
	width: 230px;
	padding: 4px 0;
}

.toolMenu {
	margin: 4px 4px 0 8px;
	width: 99%;
	display: block;
}

.InfoMenu {
	padding: 10px 0 0 8px;
}

#demoTable {
	border-top: 2px solid #1AB394 !important;
}

#demoTable td {
	overflow: inherit;
}

.selectGroup {
	position: absolute;
	top: 34px;
	left: 6px;
	max-width: 630px;;
	overflow-x: scroll;
}

.searchTableDiv {
	overflow-x: visible;
}

.bookMark {
	border-radius: 0;
}

.bookMarkDiv_selected div:first-child {
	background: #1AB394;
}


#my_div {
	overflow: scroll;
	width: calc(100% - 18px);
	height: calc(100% - 68px);
}

#searchBr {
	width: 140px;
	height: 23px;
}

.notEmptySign {
	margin-top: 5px;
	right: 3px;
}

.yzcz {
	display: inline-block;
	border: 0;
	width: auto;
	padding: 0;
}

.yzcz > button {
	background-color: #029377;
}

.yzcz:hover > ul {
	display: block;
}

.yzcz ul {
	display: none;
	position: absolute;
	width: 90px;
	margin: -1px 0 0 4px;
	background-color: #fff;
	border: 1px solid #029377;
	z-index: 100;
}

.yzcz ul > li {
	padding: 6px 4px;
}

.yzcz ul > li:hover {
	background-color: #1AB394;
	color: #FFFFFF;
	cursor: pointer;
}

.CFUse {
	position: relative;
	background-color: #FFFFFF;
	box-shadow: #333 2px 2px 8px;
	width: 800px;
	height: 450px;
	margin: 0 auto;
}

.CFUse img {
	position: absolute;
	top: 8px;
	right: 8px;
}

.CFUseTitle {
	font-size: 16px;
	background-color: #eeeeee;
	padding: 6px 0 6px 12px;
	text-align: left;
}

.searchCFUse {
	margin: 10px 0 0 0;
	display: inline-block;
	width: 100%;
}

.searchCFUse > div {
	margin-left: 16px;
}

.searchCFUse select {
	width: 100px;
}

.YzTabTable {
	float: left;
	overflow: scroll;
	padding: 0;
	margin: 10px 0 0 16px;
	width: 30%;
}

.yzd {
	margin-top: 44px;
}

.yzd .toolMenu > div {
	float: left;
	padding: 5px 16px;
	margin-right: 20px;
	cursor: pointer;
}

.yzd .toolMenu > div:hover {
	border-bottom: 2px solid #1AB394;
}

.yzd_select {
	border-bottom: 2px solid #1AB394;
}

.yzdTitle {
	font-size: 22px;
	text-align: center;
}

.yzd-brInfo{
	display: flex;
	width: 871px;
	margin: 0 auto;
}


.yzd-brInfo > div{
		white-space: nowrap;
	    font-size: 14px;
	    margin-right: 20px;
	    text-align: left;
}
.yzd-ysInfo {
	display: flex;
	justify-content: center;
}
.yzd-ysInfo > div {
	white-space: nowrap;
	display: flex;
	font-size: 14px;
	margin-right: 20px;
}
.yzd-ysInfo > div > span {
	display: block;
	float: left;
}

.yzd-ysInfo > div > span:last-child {
	text-align: left;
	width: 120px;
}

.tablePage span {
	display: inherit;
}

.yzd-table table {
	border-collapse: collapse;
	margin: 0 auto;
	font-size: 14px;
	height: 861px;
}
.yz-tables{
		border-collapse: collapse;
		margin: 0 auto;
		font-size: 14px;
		height: 861px;
		width:845px;
	}
.kfyz-tables{
		border-collapse: collapse;
		margin: 0 auto;
		font-size: 14px;
		height: 861px;
		width:845px;
	}
.yzd-table td, .yzd-table th {
	border: 1px solid #000;
	    font-weight: 500;
	    height: 42px;
	    line-height: 16px;
	    
	    white-space: nowrap;
	    text-align: center;
}
.empty_tr{
	width: 30px;
	height: 42px;
	border: 0px solid #000;
}
.empty_tb{
	width: 30px;
	height: 126px;
	border: 0px solid #000;
}
.yzd-table td .yzd-name {
	/*display: block;
	width: 198px;*/
	    font-size: 14px;
	    text-align: left;
	    margin-left: 2px;
	    word-break: break-all;
	    white-space: normal;
	word-wrap : break-word ;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	
}
.cqyzd, .lsyzd {
	text-align: center;
	overflow: scroll;
}

.ysDiv {
	width: 100%;
	text-align: center;
	/*position: absolute;*/
	bottom: 0;
}
.emptyDiv {
	width: 100%;
	text-align: center;
	bottom: 0;
}
.yzd-table{
	height: 861px;
}
.pageDiv{
	text-align: center;
}

.selectGroup td {
	text-align: left;
}
.same {
	border-right: 1px solid #000000;
}

.yzd-name {
	float: left;
	height: 42px;
}

.yzd-sm {
	float: right !important;
}
.sameStart {
	position: absolute;
	border-top: 1px solid #000000;
	border-right: 1px solid #000000;
	border-left: 0;
	border-bottom: 0;
	width: 10px !important;
	height: 50%;
	right: 50%;
	bottom: 0;
}

.sameEnd {
	position: absolute;
	border-top: 0;
	border-right: 1px solid #000000;
	border-left: 0;
	border-bottom: 1px solid #000000;
	width: 10px !important;
	height: 50%;
	right: 50%;
	top: 0;
}

.same {
	position: absolute;
	border-top: 0;
	border-right: 1px solid #000000;
	border-left: 0;
	border-bottom: 0;
	width: 10px !important;
	height: 100%;
	right: 50%;
	top: 0;
}

.yzd-way {
	width: auto !important;
	margin-left: 18px;
}

.yzd-table td, .yzd-table th {
	position: relative;
}

.yzcl_xmmc .selectGroup th:nth-child(3) {
	min-width: 300px;
}

.yzcl_xmmc .selectGroup th:nth-child(4) {
	min-width: 150px;
}

.yzd-name {
	height: auto;
}

.zcy-title {
	font-size: 20px;
	font-weight: 600;
}

.zcy-brxx {
	text-align: left;
	margin-top: 4px;
}

.zcy-brxx span {
	margin-left: 6px;
}

.zcy-item {
	float: left;
	margin: 0 30px;
	height: 50px;
}

.zcy-item > div {
	position: relative;
	width: 50px;
	height: 26px;
}

.zcy-item input, .zcy-item select {
	float: left;
	height: 24px;
}

.zcy-ypxx {
	position: absolute;
	bottom: 50px;
	right: 10px;
	text-align: right;
}

.zcy-ypxx span {
	margin: 0 4px;
}

.zcy-ypxx input {
	margin-bottom: 6px;
}

.zcy-ewxx {
	position: absolute;
	width: 100%;
	bottom: 10px;
}

.zcy-ewxx span {
	float: left;
	margin-left: 20px;
}
.syt-col-box.col-one{
	width: 52mm;
}
.syt-col-box.col-three{
	width: 160mm;
}
.syt-col-box .syt-item{
	width: 50mm;
	/*height: 30mm;  !*去掉了高度展示，渲染不会出错，不知道打印如何*!*/
	margin: 1mm;
	/*overflow: hidden;*/
	font-size: 3.7mm;
	color: #000;
}

.border-bottom{
	border-bottom:1px solid #333;
}
.syt-col-box .syt-item .font-bold{
	font-weight: bold;
}
.syt-col-box .syt-item .syt-top{
	margin-top: 2mm;
}
.syt-col-box .syt-item .syt-center{
	font-size: 3mm;
	padding: 1mm 2mm;
}
.syt-col-box .syt-center > div{
	padding-right: 2.5mm;
	border-right: 1px solid #000;
	margin-bottom: 2px;
}
.syt-col-box .syt-center > div:first-of-type,
.syt-col-box .syt-center > div:last-of-type{
	border-right: none;
	position: relative;
}
.syt-col-box .syt-center > div:first-of-type::after,
.syt-col-box .syt-center > div:last-of-type::after{
	content: "";
	position: absolute;
	right: 0;
	width: 1mm;
	height: 2mm;
	border: 0px solid #000;
	border-right-width: 1px;
}
.syt-col-box .syt-center > div:first-of-type::after{
	border-top-width: 1px;
	top: 2mm;
}
.syt-col-box .syt-center > div:last-of-type::after{
	border-bottom-width: 1px;
}
.syt-col-box .syt-bottom{
	margin: 0 4mm;
	border-bottom: 2px solid #000;
	font-size: 3mm;
}
.bqcydj_model{
	height: 200px;
	min-height: 100px;
}
.bqcydj_model input{
	height: 28px;
}
.zxdDiv .zui-table-view{
	margin-bottom: 60px;
	height: auto;
}


.height-32{
	height: 32px;
}
.ypxmgs{
	height:42px;
	max-height: 42px;
	overflow: hidden;
}
.zsorsy{
	height:126px;
	max-height: 126px;
	overflow: hidden;
}
.tz-start::before {
    bottom: -7px;
    top: 7px;
	right:-5px;
    border-top: 1px solid #000000;
}

.tz-center::before {
    bottom: -7px;
    top: -7px;
	right:-5px;
}

.tz-stop::before {
    bottom: 14px;
    top: 7px;
	right:-5px;
    border-bottom: 1px solid #000000;
}
.tz-start, .tz-center, .tz-stop {
    width: 95%;
    position: relative;
}
.xdyz{
	width: 100px;
}
.yznr{
	width: 190px
}
.yzcdqm{
	width: 50px
}
.tzsj{
	width: 50px
}
.zxrqsjqm{
	width: 360px
}
.hzxm{
	width: 80px;
}
.hzxb{
	width: 40px;
}
.hznl{
	width: 66px;
}
.hzbs{
	width: 230px;
}
.hzch{
	width: 55px;
}
.hzzyh{
	width: 135px;
}
.kfxdyz{
		width:100px;
	}
	
	.kfyznr{
		width: 190px;
	}
	.kfyzcdqm{
		width: 50px;
	}
	.kftzsj{
		width: 100px;
	}
	.kfrq{
		width: 50px;
	}
	.kfzxrqsjqm{
		width: 50px;
	}
	.kfzao{
		width: 50px;
	}
	.kfzhong{
		width: 50px;
	}
	.kfwan{
		width: 50px;
	}
	.kfwu{
		width: 50px;
	}