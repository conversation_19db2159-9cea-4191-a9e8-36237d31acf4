<div class="symd kss" id="symd">
    <div class="flex-container flex-align-c padd-b-10 padd-t-10">
        <span class="whiteSpace padd-r-5 ft-14">当前药品：</span>
        <span class="whiteSpace ft-14 color-wtg">{{popContent.ypmc || popContent.xmmc}} {{popContent.ypgg || popContent.xmgg}}</span>
    </div>
    <div class="flex-container flex-align-c padd-b-10 padd-t-10">
        <span class="whiteSpace padd-r-5  ft-14">使用目的：</span>
        <div class="flex-container flex-align-c">
            <select-input @change-data="resultChange"
                          :child="symh_tran" :index="popContent.symd" :val="popContent.symd"
                          :name="'popContent.symd'">
            </select-input>
            <span class="whiteSpace ft-14 padd-r-5 padd-l-10">手术名称</span>
            <input  class="zui-input" id="ssmc" type="text" :value="popContent['ssmc']"
                   @keydown="changeDown($event,'searchCon','ssmc','ssbm')" @input="change1(false,$event.target.value,'ssmc')"
                   placeholder="手术名称">
            <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them"
                          :them_tran="them_tran"
                          @click-one="checkedOneOut" @click-two="selectOne1">
            </search-table>
        </div>
    </div>
    <div class="flex-container flex-align-c padd-b-10 padd-t-10">
        <span class="whiteSpace ft-14 padd-r-5">使用情况：</span>
        <div class="flex-container flex-align-c">
            <vue-checkbox class="padd-r-10" @result="reCheckOne" :new-text="'联合用药'" :val="'popContent.syqk'"  :new-value="popContent.syqk"></vue-checkbox>
            <span class="whiteSpace ft-14 padd-r-5">联合用药联数</span>
            <select-input @change-data="resultChange"
                          :child="lhyy_tran" :index="popContent.lhyyls" :val="popContent.lhyyls"
                          :name="'popContent.lhyyls'">
            </select-input>
        </div>
    </div>
    <div class="flex-container padd-b-10 padd-t-10">
        <span class="whiteSpace padd-r-5 ft-14">使用原因：</span>
        <textarea rows="3" cols="100" data-notEmpty="true" class="ypStyle padd-t-5 padd-b-5" v-model="popContent.syyy"></textarea>
    </div>
</div>
<script type="text/javascript">
    var  symd= new Vue({
        el: '#symd',
        mixins: [baseFunc,tableBase],
        components: {
            'search-table': searchTable,
        },
        data: {
            Content:{},
            popContent:{},
            changeVal:false,
            selectContent:{},
            searchCon: [],
            them_tran: {'jb': dic_transform.data.ssjb_tran},
            them: {'手术编码': 'ssbm', '手术名称': 'ssmc', '拼音代码': 'pydm', '手术级别': 'jb'},
            selSearch:-1,
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            variable:{},
            symh_tran:{
                '0':'非手术预防使用',
                '1':'手术预防使用',
                '3':'治疗使用',
            },
            lhyy_tran:{
                '0':'无',
                '1':'I联',
                '2':'II联',
                '3':'III联',
                '4':'>IV联',
                '5':'>IV联',
            },
        },
        mounted:function(){
            this.variable=typeof(zcy) == "undefined" ? hzList: zcy
            this.popContent=Object.assign(this.popContent,this.variable.popContent)
            this.popContent.symd='0'
            this.$forceUpdate()
            this.getSymd()
        },
        methods: {
            getSymd:function(){
                $.getJSON('/actionDispatcher.do?reqUrl=New1YlfwptGssgl&types=selectBySymd&brid='+userNameBg.Brxx_List.brid+'&ypbm='+this.popContent.ypbm || this.popContent.xmbm+'',function (json) {
                    if(json.a =='0'){
                        symd.popContent=Object.assign(symd.popContent,json.d)
                    }
                })
            },
            reCheckOne: function (val) {
                this.popContent.syqk=val[1];
                this.$forceUpdate()
            },
            //当输入值后才触发
            change1: function (add, val) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.page.parm = val;
                this.popContent['ssmc']=val
                var str_param = {parm: this.page.parm, page: this.page.page, rows: 30};
                //手术编码
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ssbm' + '&json=' + JSON.stringify(str_param), function (data) {
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            symd.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        symd.searchCon = data.d.list;
                    }
                    symd.changeVal = true;
                    symd.page.total = data.d.total;
                    symd.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
            },
            //检索
            changeDown: function (event, searchCon,mc,bm,index) {
                this.inputUpDown(event, 'searchCon', 'selSearch');
                this.Content = this[searchCon][this.selSearch]
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (this.changeVal) {
                        Vue.set(this.popContent, bm, this.Content['ssbm']);
                        Vue.set(this.popContent, mc, this.Content['ssmc']);
                        this.nextFocus(event);
                        $(".selectGroup").hide();
                        this.searchCon=[];
                        this.selSearch = -1;
                        this.$forceUpdate()
                    } else {
                        this.selSearch = -1;
                        this.nextFocus(event);
                    }
                }
            },
            selectOne1: function (item) {
                if (item == null) {
                    this.page.page++;
                    this.change1(true, this.popContent['ssmc']);
                } else {
                    Vue.set(this.popContent, 'ssbm', item['ssbm']);
                    Vue.set(this.popContent, 'ssmc', item['ssmc']);
                    this.$forceUpdate()
                    this.searchCon=[];
                    this.selSearch = -1
                    $(".selectGroup").hide();
                }
            },
        },
    });
</script>

