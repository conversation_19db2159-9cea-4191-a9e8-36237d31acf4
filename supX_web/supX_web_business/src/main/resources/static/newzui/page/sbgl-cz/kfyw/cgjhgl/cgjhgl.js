
function getData() {
    wrapper.getData()
}

var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        popContent: {},
        search: '',
        kfList: [],
        zhuangtai: {
            '0': '待审核',
            '1': '已审核',
            '2': '未通过',
            '3': '已作废'
        },
        totlePage: 0,
        param: {
            page: 1,
            rows: 10,
            parm: '',
            beginrq: null,
            endrq: null
        },
        jsonList: [],
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        this.getKfData();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal'
            , trigger: 'click',
            type: 'datetime',
            value: this.param.beginrq
            , theme: '#1ab394'
            , done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime'
            , trigger: 'click',
            value: this.param.endrq
            , theme: '#1ab394'
            , done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
    },
    methods: {
        //开单
        topNewPageFun: function () {
            var obj = {};
            console.log(2)
            Vue.set(obj, 'kfbm', this.param.sbkf);
            Vue.set(obj, 'kfList', this.kfList);
            this.Verify(obj);
        },
        //库房
        resultRydjChange: function (val) {
            Vue.set(this.param, 'sbkf', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
        },
        //加载库房名称
        getKfData: function () {
            var parm = {
                page: 1,
                rows: 20000,
                sort: 'sbkfbm',
                tybz: '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmSbkf&types=queryAll&json=" + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        wrapper.kfList = data.d.list;
                        console.log(wrapper.kfList);
                        //默认库房
                        wrapper.param.sbkf = data.d.list[0].sbkfbmbm;
                        //获取入库单List
                       // wrapper.getData();
                    } else {
                        malert("获取库房失败！", 'top', 'defeadted');
                    }
                });
        },
        //审核
        sh: function (index) {
            var obj = {};
            //物资库房转换库房单位
            Vue.set(obj, 'kfbm', this.param.sbkf);
            Vue.set(obj, 'kfList', this.kfList);
            Vue.set(obj, 'cgjh', this.jsonList[index]);
            Vue.set(obj, 'sh', this.jsonList[index].shzfbz == 0);
            Vue.set(obj, 'dy', this.jsonList[index].shzfbz != 0);
            this.Verify(obj);
        },
        //跳转
        Verify: function (obj) {
            sessionStorage.setItem('obj', JSON.stringify(obj));
            this.topNewPage('采购计划开单', 'page/sbgl/kfyw/cgjhgl/cgjhkd.html');
        },
        //作废2018/07/09二次作废弹窗提示
        Refuse: function (index) {
            var obj = this.jsonList[index];
            if (common.openConfirm("<div>确定作废采购计划单号-" + obj.jhdh + "-采购计划单吗？<div class=\"flex-container flex-align-c\">\n" +
                "<span class=\"ft-14 whiteSpace padd-r-5\">作废原因</span><textarea rows=\"3\" cols=\"100\"id=\"zfyy\" class=\"padd-t-5 padd-b-5 padd-l-5 padd-r-5 wh100MAx\"></textarea>\n" +
                "</div></div>", function () {
                obj.zfyy = $('#zfyy').val()
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywCgjh&types=invalidDj', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        wrapper.getData();
                        malert("作废成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert("作废失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }

        },
        getData: function () {
            this.jsonLit = [];
            //发送请求获取结果
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywCgjh&types=queryDj&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == "0") {
                        wrapper.jsonList = data.d.list;
                        wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
                        malert(data.c, 'top', 'success');
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },
    },

});
