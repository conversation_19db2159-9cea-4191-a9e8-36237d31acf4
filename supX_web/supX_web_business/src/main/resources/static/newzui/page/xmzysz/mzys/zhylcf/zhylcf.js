    var yjkmtableInfo = new Vue({
        el: '#wrapper',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            jsonList: [],//
            totlePage: 0,
            param: {
                page: 1,
                rows: 100,
                sort: 'zhyzbm',
                order: 'asc',
                lx: '1',
                parm: '',
            },
        },
        mounted:function(){
            this.getData();
        },
        methods: {
            AddMdel: function () {
                wap.title = '新增组合医嘱';
                wap.open();
                wap.popContent = {};
            },
            //删除
            remove: function (index) {
                var zhyzList = [];
                var zhyz = {};
                if (index != null) {
                    zhyz.zhyzbm = this.jsonList[index].zhyzbm;
                    zhyz.ypbz = this.jsonList[index].ypbz;
                    zhyzList.push(zhyz);
                } else {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            zhyz.zhyzbm = this.jsonList[i].zhyzbm;
                            zhyz.ypbz = this.jsonList[i].ypbz;
                            zhyzList.push(JSON.parse(JSON.stringify(zhyz)));
                        }
                    }
                }
                var json = '{"list":' + JSON.stringify(zhyzList) + '}';
                if (common.openConfirm("确认删除该条信息吗？", function () {
                    yjkmtableInfo.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=delete&', json).then(function (data) {
                        if (data.body.a == 0) {
                            yjkmtableInfo.isChecked=[]
                            malert("删除成功", "top", "success")
                            yjkmtableInfo.getData();
                        } else {
                            malert("删除失败", "top", "defeadted")
                        }
                    }, function (error) {
                        console.log(error);
                    });
                })) {
                    return false;
                }
            },
            //编辑修改根据num判断
            edit: function (num) {
                wap.title = '编辑组合医疗处方';
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
            },
            openPage:function (text,url,item) {
                sessionStorage.zhylyz=JSON.stringify(item)
                console.log(item);
                this.topNewPage(text,url)
            },
            getData: function () {
                common.openloading('#zui-table')
                this.param.yyz = userId;
                this.param.ypbz = '0';
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=query&parm=" + JSON.stringify(this.param), function (json) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (json.a == 0) {
                        common.closeLoading()
                        yjkmtableInfo.totlePage = Math.ceil(json.d.total / yjkmtableInfo.param.rows);
                        yjkmtableInfo.jsonList = json.d.list;
                    }

                });
            },
        },
    });
    var wap = new Vue({
        el: '#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data: {
            ksbmList: [],//科室集合
            rybmList: [],//人员集合
            cflxList: [],//处方类型集合
            yfList: [],  //药房集合
            title: '',
            ifClick: true,
            num: 0,
            csContent: {},
            popContent: {},
        },
        methods: {
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');
            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg');
                $(".side-form").removeClass('ng-hide');
            },
            //下拉框科室加载
            yyksSelect: function () {
                this.param.rows = 20000;
                this.param.sort = '';
                this.param.order = '';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify(this.param), function (json) {
                    wap.ksbmList = json.d.list;
                });
            },
            //下拉框人员加载
            yyrSelect: function () {
                this.param.rows = 20000;
                this.param.sort = '';
                this.param.order = '';
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm&dg=" + JSON.stringify(this.param), function (json) {
                    wap.rybmList = json.d.list;
                });
            },
            //下拉框处方类型加载
            cflxSelect: function () {
                this.param.rows = 20000;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=cflx&dg=" + JSON.stringify(this.param), function (json) {
                    wap.cflxList = json.d.list;
                });
            },

            //下拉框药房加载
            yfSelect: function () {
                this.param.rows = 20000;
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf&dg=" + JSON.stringify(this.param), function (json) {
                    wap.yfList = json.d.list;
                });
            },

            //保存
            saveData: function () {
                wap.popContent.ypbz = '0';
                var json = JSON.stringify(wap.popContent);
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=save',
                    json).then(function (data) {
                    if (data.body.a == 0) {
                        yjkmtableInfo.getData();
                        wap.closes();
                        malert("保存成功", "top", "success");
                    } else {
                        malert("上传数据失败", "top", "defeadted");
                    }
                }, function (error) {
                    console.log(error);
                });

            },
        },
        mounted:function () {
            this.yyksSelect();
            this.yyrSelect();
            this.cflxSelect();
            this.yfSelect();
        }
    });






