.show-ws-box .show-ws{
  width: 210mm;
  padding: 0 10mm;
  color: #000!important;
}
.show-ws-box .show-ws .header-top,
.show-ws-box .show-ws .ws-title{
 text-align: center;
 letter-spacing: 0.8em
}

.show-ws-box .show-ws .header-top{
  font-size: 16px;
}
.show-ws-box .show-ws .ws-title{
  font-size: 8mm;
  border-bottom: 2px solid #000;
}
.ryhlpgd .print-inside-avoid{
  line-height: 36px;
  padding: 2px 0;
}
.ryhlpgd .print-inside-avoid .zui-input{
  width: 20mm;
  text-indent: 0;
  display: inline-block;
  color: #000;
}
.ryhlpgd .print-inside-avoid .zui-input.xy{
  width: 10mm;
  padding: 0 2px;
}
.ryhlpgd .col-xxl-3 .zui-input{
  width: 25mm;
}
.zui-input-inline .zui-select-inline .zui-input{
  text-indent: 0;
}

@media print{
  .zui-input{
    border: 1px solid transparent!important;
  }
}