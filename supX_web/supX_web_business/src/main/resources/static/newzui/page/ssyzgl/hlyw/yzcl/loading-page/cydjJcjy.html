<style type="text/css">
    #print-box {
        display: none;
    }

    .print-box {
        width: 50mm;
        /*//height: 29mm;*/
        font-size: 2.5mm;
        padding: 0 0.5mm 0;
        line-height: 1;
        color: #000;
        page-break-after: always;
        overflow: hidden;
    }

    .marg-b-1mm {
        margin-bottom: 0.5mm;
    }

    .print-box .jcjyxmmc {
        line-height: 1.1;
        height: 6.93mm;
        overflow: hidden;
    }

    .print-box .top-p span:not(:last-of-type) {
        margin-right: 1.5mm;
    }

    .print-box .print-barcode-img {
        width: 80%;
        height: 7mm;
        margin: 0 auto;
        display: block;
    }

    @media print {
        @page {
            /*size: User defined;*/
            size: 55mm 27mm;
            margin:0.7mm 0.5mm 0.5mm 0.5mm
        }
    }
</style>
<div id="print-box" class="printShow isTransform" v-cloak>
    <div class="print-box" v-for="item in showList">
		<div>
			<p class="flex-container">
			    <span class="flex-one text-over" v-text="item.jyxh">检验序号</span>
			    <span class="flex-one text-right" v-text="fDate( item.sqrq, 'date')">申请日期</span>
			</p>
        <p class="flex-container top-p marg-b-1mm">
            <span class="text-over" v-text="item.brxm">病人姓名</span>
            <span v-text="brxb_tran[item.xb]">性别</span>
            <span v-text="item.nl + nldw_tran[item.nldw]">年龄</span>
            <span class="flex-one text-over" v-text="item.sjksmc">申请科室</span>
            <span>{{item.cwh}}床</span>
        </p>
        <p class=" jcjyxmmc" v-text="item.fymc">费用名称</p>
        <p class="flex-container ">
            <span class="flex-one text-over" v-text="item.bah">病案号</span>
            <span class="flex-one text-right text-over">{{item.sgys}}&ensp;{{item.ybmc}}&ensp;{{jzbz_tran[item.jzbz]}}</span>
        </p>
        
		<img class="print-barcode-img marg-b-1mm" jsbarcode-format="CODE128" :jsbarcode-value="item.jyxh"
		     jsbarcode-margin="0"
		     jsbarcode-displayValue="false" jsbarcode-height="14" alt="病人条码">
		</div>
    </div>
</div>
<script type="text/javascript">
    window.cydjJcjy = new Vue({
        el: '#print-box',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            showList: [],
            jzbz_tran:{
                '0':'',
                '1':'急'
            },

        },
    })
</script>
