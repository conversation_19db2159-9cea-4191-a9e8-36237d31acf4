<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>报告单</title>
    <link href="brPage/bgd.css" rel="stylesheet">
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
</head>
<body>
<div class="bgd flex-container">
    <div class=" zui-table-view flex-container flex-dir-c flex-one" id="brRyList01" >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s "><span>检验项目</span></div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s "><span>病员姓名</span></div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s "><span>打印</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body flex-one" data-no-change @scroll="scrollTable($event)">
            <table v-if="jsonList.length!=0" class="zui-table table-width50">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in jsonList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkOne($index)"
                    class="tableTr2">
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s">
                      		<span  v-text="item.jyxmmc"></span>
                        </div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s ">
                           	<span  v-text="item.brxm"></span>
                        </div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s title ">
                        	<span  v-text="item.isprint =0 ? '是': '否'"></span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <p v-if="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>
        </div>
    </div>

    <div class="Template flex-container flex-jus-c printShow" style="width:83%"></div>
</div>
<script type="text/javascript" src="brPage/bgd.js"></script>
</body>
</html>