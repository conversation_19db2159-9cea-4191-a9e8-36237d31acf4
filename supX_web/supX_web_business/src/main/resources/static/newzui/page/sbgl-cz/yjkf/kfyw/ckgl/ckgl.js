var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        kfList: [],
        ylbm: 'N050080012002',
        totlePage: 0,
        zhuangtai: {
            '0': '待审核',
            '1': '已审核',
            '2': '未通过',
            '3': '已作废'
        },
        ckfs: {
            '01': '出库',
            '02': '退货',
            '03': '报损',
            '04': '盘亏出库'
        },
        jsonList: [],
    },
    mounted: function () {
        this.getKfData();
        this.getKs();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
    },
    methods: {
        LingYaoD: function () {
            brzcList.open();
        },
        getKs: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
                function (data) {
                    brzcList.ksList = data.d.list;
                    brzcList.param.ksbm =  data.d.list[0].ksbm;
                });
        },
        //审核
        sh: function (index) {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.sbkf);
            Vue.set(obj, 'kfList', this.kfList);
            Vue.set(obj, 'ckd', this.jsonList[index]);
            Vue.set(obj, 'ksbm', this.jsonList[index].ksbm);
            Vue.set(obj, 'sh', this.jsonList[index].shzfbz=="0");
            Vue.set(obj,'dy',(this.jsonList[index].shzfbz !="0"));
            this.Verify(obj);
        },
        //跳转
        Verify: function (obj) {
            sessionStorage.setItem('obj', JSON.stringify(obj));
            console.log(obj);
            this.topNewPage('出库开单', 'page/sbgl/yjkf/kfyw/ckgl/ckkd.html');
        },
        //作废2018/07/09 二次弹窗作废提示
        Refuse: function (index) {
            var obj = this.jsonList[index];
            if (common.openConfirm("<div>确定作废物资出库单号-" + obj.ckdh + "-出库单吗？<div class=\"flex-container flex-align-c\">\n" +
                "<span class=\"ft-14 whiteSpace padd-r-5\">作废原因</span><textarea rows=\"3\" cols=\"100\" id=\"zfyy\" class=\"padd-t-5 padd-b-5 padd-l-5 padd-r-5 wh100MAx\"></textarea>\n" +
                "</div></div>", function () {
                obj.zfyy=$('#zfyy').val()
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywCkd&types=invald', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("作废成功！", 'top', 'success');
                        wrapper.getData();
                    } else {
                        malert("作废失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }


        },
        getData: function () {
            Vue.set(this.param, 'ckfs', '01');
            delete this.param.shzfbz
            delete this.param.ckbz
            // this.param.shzfbz=''
            // this.param.ckbz='';//出库标志
            //发送请求获取结果
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywCkd&types=query&parm=' + JSON.stringify(this.param), function (data) {
                if (data.a == "0") {
                    wrapper.jsonList = data.d.list;
                    wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
                    malert(data.c, 'top', 'success');
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },
        //开单
        openNewPage: function () {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.sbkf);
            Vue.set(obj, 'kfList', this.kfList);
            wrapper.Verify(obj);
        },
        //库房list
        getKfData: function () {
            // 请求库房的api
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=sbkf', function (data) {
                if (data.a == 0) {
                    console.log(data.d.list);
                    wrapper.kfList = data.d.list;
                    console.log(data.d.list[0].sbkfbm)
                    Vue.set(wrapper.param, 'sbkf', data.d.list[0].sbkfbm);
                    wrapper.getData();
                } else {
                    malert("获取库房列表失败");
                }
            });
        },
        //库房
        resultRydjChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.kf_Query(val[0]);
        },
    }
});
function getData() {
    wrapper.getData()
}
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifClick: true,
        nums: 1,
        ksList:[],
        lymxList:[],
        lydList:[],
        popContent:{},
    },
    mounted:function(){
    },
    methods: {
        //关闭
        closes: function () {
            this.nums=1
        },
        resultChange:function(val){
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData()
        },
        open: function () {
            this.nums=0
            this.getData()
        },
        getData:function(){
            //发送请求获取结果
            this.param.rows=1000;
            // this.param.beginrq=null;
            // this.param.endrq=null;
            this.param.shzfbz='1';//已审核
            this.param.ckbz='0';//出库标志
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywSld&types=queryDj' + '&parm=' + JSON.stringify(this.param),
                function(data) {
                    if(data.a == "0") {
                        brzcList.lydList = data.d.list;
                    } else {
                        malert(data.c);
                    }
                });
        },
        getMx: function (item) {
            this.popContent=item;
            var obj = {
                ksbm: this.param.ksbm,
                sldh: item.sldh,
                kcXtph: item.kcXtph
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywSld&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 || data.body.a == 0) {
                        brzcList.lymxList = data.d
                    } else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },
        //提交
        tjCom: function () {
            var ckd = {};
            Vue.set(ckd, 'sbkf', wrapper.param.sbkf);//库房编码
            Vue.set(ckd, 'lyr', this.popContent.jbry);//采购员
            Vue.set(ckd,'sldh',this.popContent.sldh);//领用单号
            Vue.set(ckd, 'lydw', this.popContent.ksbm);
            Vue.set(ckd, 'ksbm', this.popContent.ksbm);//领用科室
            this.lymxList.ksbm = this.popContent.ksbm;

            for(var i=0;i<this.lymxList.length;i++){
                this.lymxList[i].sbkf=this.lymxList[i].kfbm
                // this.lymxList[i].kfdw=this.lymxList[i].lydw
                this.lymxList[i].cksl=this.lymxList[i].slsl
            }

            //新增操作
            var obj = {
                list: {
                    ckd: ckd,
                    ckdmx: this.lymxList,
                    sbkf: this.popContent.kfbm,
                    lyke: this.popContent.ksbm

                }
            }
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywCkd&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("保存成功！", 'top', 'success');
                        this.closes();//关闭当前领药单
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
    }
});




