function tabBg(page, index, openUrl) {
    if (yzclLeft.isChecked.length < 1) {
        malert("请选择一个病人之后再操作！", "top", "defeadted");
        return;
    }
    if (yzclLeft.tabIndex[index] == 'one' && yzclLeft.notempty(yzclLeft.isChecked).length > 1) {
        yzclLeft.isChecked = [];
        yzclLeft.arr = []
        // var maxIndexOf=yzclLeft.timeArr.indexOf(Math.max.apply(Math, yzclLeft.timeArr))
        //  if(maxIndexOf!= -1){
        Vue.set(yzclLeft.isChecked, 0, true);
        yzclLeft.arr.push(yzclLeft.brlist[0].zyh)
        // }
    } else {

        yzclLeft.setZyh('brlist');
    }

    // if (index != 8 && index == 2 && index >= 4) {
    //     var list = [];
    // if (yzclLeft.isChecked.length < 1 || yzclLeft.isChecked == null) {
    //     malert("请选择一个病人之后再操作！", "top", "defeadted");
    //     return;
    // }
    for (var i = 0; i < yzclLeft.brlist.length; i++) {
        if (yzclLeft.isChecked[i]) {
            // list.push(yzclLeft.brlist[i]);
            var list = yzclLeft.brlist[i]
        }
    }
    // if (list.length > 1) {
    //     malert("此操作只针对单人！", "top", "defeadted");
    //     yzclLeft.isChecked = []
    //     return;
    // }
    // yzclLeft.HszbrItem = list[0]
    yzclLeft.HszbrItem = list
    // sessionStorage.setItem("HszbrItem", JSON.stringify(list[0]));
    sessionStorage.setItem("HszbrItem", list);
    // sessionStorage.setItem("HszZxdzyhs", JSON.stringify(list));
    // }
    // else if (index == 8) {
    //     var zyhs = [];
    //     for (var i = 0; i < yzclLeft.brlist.length; i++) {
    //         if (yzclLeft.isChecked[i]) {
    //             var obj = {};
    //             obj.zyh = yzclLeft.brlist[i].zyh;
    //             zyhs.push(obj);
    //         }
    //     }
    //     if (zyhs.length < 1 || zyhs == null) {
    //         malert("请选择病人后再操作！", "top", "defeadted");
    //         return
    //     }
    //     sessionStorage.setItem("HszZxdzyhs", JSON.stringify(zyhs));
    // }
    if (index == 0) {
        yzclRight.queryYzxx()
    }
    yzclRight.index = index;
    yzclLeft.index = index
    if (page != undefined) {
        $("#loadingPage001").load(page + ".html", '', function () {
        }).fadeIn(300)
    } else {
        if (openUrl) {
            yzclRight[openUrl]()
            return false;
        } else {
            $("#loadingPage001").html("");
        }
        yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
		yzclRight.shtime = yzclRight.getDate() || getTodayDateTime()
    }
}

var yzclLeft = new Vue({
        el: '.yzclLeft',
        mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
        data: {
            jsContent: {
                ksbm: '',
                zyType: 'zy',
                hldj: '0',
                bkzt: '0',
                mtbr: '0',
            },
            openLeft: true,
            csContent: {},
            kslist: [],
            arr: [],
            brlist: [],
            num: 0,
            isChecked: [],
            timeArr: [],
            zyhs: [],
            index: 0,
            tabIndex: {
                '0': 'some',
                '1': 'one',
                '2': 'one',
                '3': 'one',
                '4': 'one',
                '5': 'one',
                '6': 'some',
                '7': 'one',
                '8': 'one',
                '9': 'some',
                '10': 'one',
                '11': 'some',
                '12': 'some',
            },
            tableheight: null,
            caqxContent: {
                N03003200125: '1',
                N03003200139: '1',
                N03003200141: '1',
            },//参数权限对象
            HszbrItem: {},
            zrs: null,
            ifClick: true,
            NoticeObj: {},
            getSucess: false,
            refreshYe: true,
			
			
        },
        mounted: function () {
            window.addEventListener("storage", function (e) {
                if (e.key == "NoticeObj" + userId && e.newValue !== e.oldValue) {
                    yzclLeft.getNotice()
                }
            });

        },
        watch: {
            'isChecked': function () {
                if (this.notempty(this.isChecked).length == this.brlist.length) {
                    this.isChecked.every(function (el) {
                        if (el === true) {
                            return yzclLeft.isCheckAll = true
                        } else {
                            return yzclLeft.isCheckAll = false
                        }
                    })
                } else {
                    return yzclLeft.isCheckAll = false
                }
            }
        },
        methods: {
			
			getHs:function(){
				let obj = {hsbz: '1'};
				this.updatedAjax("/actionDispatcher.do?reqUrl=GetDropDown&types=rybmjkac&json=" + JSON.stringify(obj), function (json) {
				    if (json.a == "0") {
						let list = json.d.list;
						console.log(yzclLeft.jsContent.ksbm)
						yzclRight.zxhslist = jsonFilter(list, "ksbm", yzclLeft.jsContent.ksbm);
						yzclRight.rybm=userId
						yzclRight.ryxm=userName
				    }
				});
			},
            getNotice: function () {
                yzclLeft.NoticeObj = JSON.parse(sessionStorage.getItem('NoticeObj' + userId));
                if (yzclLeft.NoticeObj != null && yzclLeft.NoticeObj.msgtype != null) {
                    yzclLeft.getSucess = false;
                    var type = yzclLeft.NoticeObj.msgtype
                    yzclLeft.jsContent.zyType = yzclLeft.NoticeObj.zyType || 'zy';
                    yzclLeft.jsContent.ksbm = yzclLeft.NoticeObj.ksbm;
                    if(type !='6'){
                        yzclLeft.jsContent.jsVal=yzclLeft.NoticeObj.zyh
                    }else {
                        yzclLeft.jsContent.jsVal='';
                    }
                    yzclLeft.getHzlb();
                    yzclLeft.isChecked = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.brlist[i].zyh == yzclLeft.NoticeObj.zyh) {
                            yzclLeft.isChecked[i] = true;
                            UserHzxx.brxxContent = yzclLeft.brlist[i]
                            yzclLeft.HszbrItem = yzclLeft.brlist[i]
                            break;
                        }
                    }
					                    switch (type) {
                        //审核新医嘱
                        case '0':
                            yzclRight.num = 1;
                            tabBg(undefined, 0)
                            break;
                        //停嘱审核
                        case '1':
                            yzclRight.num = 5;
                            tabBg(undefined, 0)
                            break;
                        //执行
                        case '2':
                            yzclRight.num = 2;
                            tabBg(undefined, 0)
                            break;
                        //申领
                        case '3':
                            yzclRight.num = 3;
                            tabBg(undefined, 0)
                            break;
                        //执行单
                        case '4':
                            tabBg('childpage/yzzxd', 6);
                            break;
                        //医嘱单
                        case '5':
                            tabBg('childpage/yzd', 7);
                            break;
                        //体温单
                        case '6':
                            tabBg('childpage/scsj', 5);
                            break;
                        //护理记录
                        // case '7':
                        //     tabBg('childpage/hljl', 5);
                        //     break;
                        // case '8':
                        //     tabBg('childpage/wzhljl', 4);
                        //     break;
                        case '9':
                            tabBg('childpage/fyqd', 4);
                            break;
                        case '10': // 检查检验
                            tabBg('childpage/jcjy', 1);
                        //     break;
                        // case '11': // @yqq update 护理风险评估
                        //     tabBg('childpage/hlfxpg', 6);
                            break;
                        case '12':
                            yzclRight.num = 4;
                            tabBg(undefined, 0)
                            break;
                        case '13':
                            yzclRight.num = 5;
                            tabBg(undefined, 0)
                            break;
                        case '14':
                            yzclRight.num = 6;
                            tabBg(undefined, 0)
                            break;
                        case '15':
                            yzclRight.num = 7;
                            tabBg(undefined, 0)
                            break;
						case '16':
						    yzclRight.num = 8;
						    tabBg(undefined, 0)
						    break;	
                    }
                    ;
                    yzclLeft.$forceUpdate()
                    sessionStorage.removeItem("NoticeObj" + userId);
                }
            }
            ,
            getHzlb: function () {
                var ajaxUrl = '/actionDispatcher.do?reqUrl=',
                    parm = {};
                //是否输入了病人姓名或者住院号
                if (this.jsContent.jsVal !== '') {
                    parm.parm = this.jsContent.jsVal;
                } else {
                    parm.parm = null;
                }
                // 是否选择的护理等级
                if (this.jsContent.hldj !== '') {
                    parm.hldj = this.jsContent.hldj;
                }
                //是否选择了病况
                if (this.jsContent.bkzt !== '') {
                    parm.bqdj = this.jsContent.bkzt;
                }
                parm.mtbbz = null;
                if (this.jsContent.mtbr == '1') {
                    parm.mtbbz = '1';
                } else if (this.jsContent.mtbr == '2') {
                    parm.mtbbz = '0';
                }
                parm.page = 1;
                parm.rows = 20000;
                // 在院状态
                switch (this.jsContent.zyType) {
                    case 'zy': //在院
                        parm.ryks = this.jsContent.ksbm;
                        parm.sfkj = '1';
                        // parm.sort1 = '1';
                        parm.order2 = '1';
                        ajaxUrl = ajaxUrl + 'New1ZyysYsywYzcl&types=zyhzxx&parm=' + JSON.stringify(parm);
                        break;
                    case 'bqcy': //出院
                        parm.ryks = this.jsContent.ksbm;
                        ajaxUrl = ajaxUrl + 'New1ZyysYsywYzcl&types=bqcyhzxx&parm=' + JSON.stringify(parm);
                        break;
                    case 'jscy': //出院
                        parm.ryks = this.jsContent.ksbm;
                        ajaxUrl = ajaxUrl + 'New1ZyysYsywYzcl&types=Rcyhzxx&parm=' + JSON.stringify(parm);
                        break;
                    // case 'cy': //出院
                    //     parm.ryks = this.jsContent.ksbm;
                    //     ajaxUrl = ajaxUrl + 'New1ZyysYsywYzcl&types=cyhzxx&parm=' + JSON.stringify(parm);
                    //     break;
                    case 'zk': //转院
                        ajaxUrl = ajaxUrl + 'New1HszHlywYzclCx&types=zchz&ksbm=' + this.jsContent.ksbm;
                        break;
                }
                this.updatedAjax(ajaxUrl, function (json) {
                    if (json.a == 0) {
                        var data = json.d.list;
                        //@压缩包   我在这里加一个排序，希望大家不要动
                        yzclLeft.brlist = yzclLeft.filterSort(data, 's', 'xssx');
                        yzclLeft.zrs = data.length;
                        yzclLeft.getSucess = true;
                    } else {
                        malert("获取患者列表失败!" + json.c, 'top', 'defeadted');
                        yzclLeft.getSucess = true;
                    }
                }, function (error) {
                    malert(error, 'top', 'defeadted');
                });
            }
            ,
            //获取参数权限
            getCsqx: function () {
                var ksId = yzclLeft.jsContent.ksbm;
                var parm = {
                    "ylbm": 'N030042002',
                    "ksbm": ksId
                };
				                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0 && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03004200201": //申领药品是否判断库存  0=不判断,1=判断
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100101 = csjson.csz;
                                    }
                                    break;
                                case "N03004200202": //取消审核医嘱权限  0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100102 = csjson.csz;
                                    }
                                    break;
                                case "N03004200203": //审核转科医嘱是否停用执行医嘱0=否，1=是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100103 = csjson.csz;
                                    }
                                    break;
                                case "N03004200204": //审核出院医嘱是否停用执行医嘱0=否，1=是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100104 = csjson.csz;
                                    }
                                    break;
                                case "N03004200206": //医嘱是否语音提示0=否，1=是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100106 = csjson.csz;
                                    }
                                    break;
                                case "N03004200207": //取消停嘱审核权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100107 = csjson.csz;
                                    }
                                    break;
                                case "N03004200208": //确定出院权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100108 = csjson.csz;
                                    }
                                    break;
                                case "N03004200209": //取消出院权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100109 = csjson.csz;
                                    }
                                    break;
                                case "N03004200210": //取消执行长期医嘱权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100110 = csjson.csz;
                                    }
                                    break;
                                case "N03004200211": //取消药品申领权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100111 = csjson.csz;
                                    }
                                    break;
                                case "N03004200212": //退药申请权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100112 = csjson.csz;
                                    }
                                    break;
                                case "N03004200213": //费用记账权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100113 = csjson.csz;
                                    }
                                    break;
                                case "N03004200214": //记账退费权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100114 = csjson.csz;
                                    }
                                    break;
                                case "N03004200215": //退费申请权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100115 = csjson.csz;
                                    }
                                    break;
                                case "00900100116": //更改主管医生权限0=无，1＝有
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100116 = csjson.csz;
                                    }
                                    break;
                                case "N03004200216": //医嘱扣费是否判断余额0=不判断，1＝判断
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100117 = csjson.csz;
                                    }
                                    break;
                                case "N03004200218": //主管医生选择范围0=默认当前病区医生，1=全院医生
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100118 = csjson.csz;
                                    }
                                    break;
                                case "N03004200219": //病区出院是否判断检查检验是否扣费、药品发药等0=不判断，1=判断
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100119 = csjson.csz;
                                    }
                                    break;
                                case "N03004200220": //申领药品是否同时发药0=否，1=是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100120 = csjson.csz;
                                    }
                                    break;
                                case "N03004200221": //病区退药申请后是否自动审核0=否，1=是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100121 = csjson.csz;
                                    }
                                    break;
                                case "N03004200222": //病人帐户默认开始统计时间0-当天 1-入院日期
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100122 = csjson.csz;
                                    }
                                    break;
                                case "N03004200223": //长期医嘱是否每天可以多次执行0=不允许，1=允许
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100123 = csjson.csz;
                                    }
                                    break;
                                case "N03004200232": //住院中药是否运行单个取消0=不允许，1=允许
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs00900100232 = csjson.csz;
                                    }
                                    break;
                                case "N03004200236": //申请退药默认药房
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.cs004200236 = csjson.csz;
                                    }
                                    break;
                                case "N03004200233": // 护士站是否使用取消执行功能
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200233 = csjson.csz;
                                        if (csjson.csz == '1') {
                                            yzclRight.tabArr.push({text: '取消执行'});
                                        }
                                    }
                                    break;
                                case "N03004200238": // 护士站医嘱审核是否输入密码
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200238 = csjson.csz;
                                    }
                                    break;
                                case "N03004200243": // 护士站医嘱单格式 0 默认 1 宁蒗要求
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200243 = csjson.csz;
                                    }
                                    break;
                                case "N03004200247": // 审核医嘱/停嘱审核时间选择
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200247 = csjson.csz;
                                    }
                                    break;
                                case "N03004200245": //长期药品医嘱首次执行是否领第二天的药 0-否 1-是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200245 = csjson.csz;
                                        yzclRight.caqxContent.N03004200245 = csjson.csz;
                                    }
                                    break;
                                case "N03004200248": //执行单打印是否使用帆软 0-否 1-是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200248 = csjson.csz;
                                    }
                                    break;
                                case "N03004200250": //是否更换药品申领与取消执行医嘱位置
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200250 = csjson.csz;
                                        yzclRight.caqxContent.N03004200250 = csjson.csz;
                                    }
                                    break;
                                case "N03004200251": //是否增加体温单上边距 0-否 1-是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200251 = csjson.csz;
                                        yzclRight.caqxContent.N03004200251 = csjson.csz;
                                    }
                                    break;
                                case "N03004200252": //是否根据时间开始药房切换
                                    if (csjson.csz) {
                                        yzclRight.caqxContent.N03004200252 = JSON.parse(csjson.csz);
                                    }
                                    break;
                                case "N03004200254": //是否允许修改删除其它人的护理记录 0-否 1-是
                                    if (csjson.csz) {
                                        yzclRight.caqxContent.N03004200254 = csjson.csz;
                                    }
                                    break;
                                case "N03004200258": //执行单是否护士双签名 0-否 1-是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200258 = csjson.csz;
                                    }
                                    break;
                                case "N03004200260": // 医嘱单停嘱时间是否显示  1-全部显示 2 长期显示 3 临时显示
                                    yzclLeft.caqxContent.N03003200141 = csjson.csz;
                                    break;
                                case "N03004200261": //费用清单精简显示（一行两个费用）
                                    yzclLeft.caqxContent.N03004200261 = csjson.csz;
                                    break;
                                 case "N03004200262": //打印方式 0 帆软 1 网页
                                    yzclLeft.caqxContent.N03004200262 = csjson.csz;
                                    break;
                                case "N03004200265": //体温单不显示诊断和医生护士签名
                                    yzclLeft.caqxContent.N03004200265 = csjson.csz;
                                    break;
                                case "N03004200267": //护士站费用清单默认开始时间
                                    yzclLeft.caqxContent.N03004200267 = csjson.csz;
                                    yzclLeft.caqxContent.N03004200267 = csjson.csz;
                                    break;
                                case "N03004200268": //护士站费用清单默认结束时间
                                    yzclLeft.caqxContent.N03004200268 = csjson.csz;
                                    yzclLeft.caqxContent.N03004200268 = csjson.csz;
                                    break;
                                case "N03004200269": //医嘱单是否省略显示 0-否 1-是
                                    yzclLeft.caqxContent.N03004200269 = csjson.csz;
                                    yzclLeft.caqxContent.N03004200269 = csjson.csz;
                                    break;
                                case "N03004200270": //护理记录入量选项
                                    if (csjson.csz) {
                                        yzclRight.caqxContent.N03004200270 = csjson.csz;
                                    }
                                    break;
                                case "N03004200271": //护理记录出量选项
                                    if (csjson.csz) {
                                        yzclRight.caqxContent.N03004200271 = csjson.csz;
                                    }
                                    break;
                                case "N03004200275": //
                                    if (csjson.csz) {
                                        yzclRight.caqxContent.N03004200275 = csjson.csz;
                                    }
                                    break;
                                case "N030042002734": //审核医嘱是否停用其他医嘱
                                    if (csjson.csz) {
                                        yzclRight.caqxContent.N030042002734 = csjson.csz;
                                    }
                                break;
                                case "N030042002736": //临时医嘱单打印最下面是否显示执行医生等信息 '0-允许，1-不允许
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N030042002736 = csjson.csz;
                                    }
                                    break;
                                case "N03004200707": //药房是否显示 0 显示- 1 不显示
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03004200707 = csjson.csz;
                                    }
                                    break;
                                case "N03004200708": //医生签名时间是否显示 1 不显示- 0 不显示
                                    if (csjson.csz) {
                                        yzclRight.caqxContent.N03004200708 = csjson.csz;
                                    }
                                    break;
                                case "N03004200709": //护士站担保患者金额、担保人:0不可担保 1可以
                                    if (csjson.csz) {
                                        UserHzxx.caqxContent.N03004200709 = csjson.csz;
                                    }
                                    break;
                                case "N03004200277": //护理风险评估是否全部显示
                                    if (csjson.csz) {
                                                                                yzclRight.caqxContent.N03004200277 = csjson.csz;
                                    }
                                    break;
                                case "N03004200278": //皮下注射是否归为皮试结果录入 0否 1是
                                    if (csjson.csz) {
                                                                                yzclRight.caqxContent.N03004200278 = csjson.csz;
                                        yzclLeft.caqxContent.N03004200278 = csjson.csz;
                                    }
                                    break;
                            }
                        }
                    } else {
                        malert("参数权限获取失败" + json.c, "top", "defeadted");
                    }
                });

                //获取参数权限
                parms = {
                    "ylbm": 'N030032001',
                    "ksbm": yzclLeft.jsContent.ksbm
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parms), function (json) {
                    if (json.a == 0 && json.d) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03003200101": //医嘱默认药房  --下医嘱和申领药品时默认，为空则取本地配置文件药房编码
                                    if (csjson.csz) {
                                        Vue.set(yzclLeft.caqxContent, 'cs03003200101', csjson.csz)
                                    }
                                    break;
                                case "N03003200125": // 医嘱单打印是否需要展示时间和医师和护士
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03003200125 = csjson.csz;
                                    }
                                    break;
                                case "N03003200133": // 医嘱单打印是否需要展示时间和医师和护士
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03003200133 = csjson.csz;
                                    }
                                    break;
                                case "N03003200139": // 医嘱单停嘱时间是否显示 0-否 1-是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03003200139 = csjson.csz;
                                    }
                                    break;
                                case "N03003200146": // 用药说明根据指定长度拆分为多条医嘱
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03003200146 = csjson.csz;
                                    }
                                    break;
                                case "N03003200154": // 护士站明细查询条件筛选
                                    if (csjson.csz) {
                                        yzclRight.N03003200154 = csjson.csz;
                                    }
                                    break;
                                case "N03003200189": // 临时诊疗医嘱是否分行显示，0否，1是
                                    if (csjson.csz) {
                                        yzclLeft.caqxContent.N03003200189 = csjson.csz;
                                    }else {
                                        yzclLeft.caqxContent.N03003200189 = '0';
                                    }
                                    break;
                            }
                        }

                    } else {
                        malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                    }
                });
            }
            ,
            //    下拉table选择回调
            selectCB: function (item, type) {
                if (item[0] !== this.jsContent[type]) {
                    this.jsContent[type] = item[0];
                    var name_index = item[0];
                    this.jsContent.ksmc = yzclLeft.kslist[name_index];
                    this.getHzlb();
                    this.isCheckAll = false;
                    this.isChecked = []
                    yzclRight.rightJsonList=[];
                    this.arr = [];
                    this.zyhs = [];
                    this.HszbrItem = {}
                }
            }
            ,
            queryPatient: function (index) {
                UserHzxx.brxxContent = this.brlist[index];
                UserHzxx.brxxContent.dbje1=UserHzxx.brxxContent.dbje
                UserHzxx.brxxContent.dbr1=UserHzxx.brxxContent.dbr
            }
            ,


            //医嘱查询接口
            queryYz: function (zyhs) {
                                if (zyhs.length == 0) {
                    yzclRight.yeList = [];
                    yzclRight.yebh = '';
                    malert("请选择病人后再进行此操作！", "top", "defeadted");
                    return
                }
                if (yzclLeft.refreshYe) {
                    yzclRight.yeList = [];
                    yzclRight.getYexx(zyhs);
                }
                yzclLeft.refreshYe = false;
                var zyh = JSON.stringify(zyhs);
                //对象清空
                yzclRight.rightJsonList = [];
                if (yzclRight.num == 0) {//医嘱查询
                    yzclRight.rightJsonList = [];
                    common.openBar('#wrapper');
                    var parm = {
                        // ksbm: yzclLeft.jsContent.ksbm,
                        ypbz: (yzclRight.ypbz ? yzclRight.ypbz : ''),
                        yzlx: (yzclRight.yzlx ? yzclRight.yzlx : ''),
                        ystzbz: (yzclRight.ystzbz ? yzclRight.ystzbz : ''),
						zxks : yzclRight.zxks
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=yzcx&zyh=' + zyh + '&parm=' + JSON.stringify(parm) + "&yebh=" + yzclRight.yebh, function (json) {
                        if (json.a == '0' && json.d != null) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                }
                            }
                            common.closeLoading();
                            yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
                        } else {
                            malert(json.c, 'top', 'defeadted')
                            common.closeLoading()
                        }

                    }, function (error) {
                        console.log(error);
                        malert(error, 'top', 'defeadted')
                        common.closeLoading()
                    });
                } else if (yzclRight.num == 1) {//医嘱审核
                    yzclRight.rightJsonList = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.isChecked[i]) {
                            if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
                                malert("病人已出院，无法执行此操作", "top", "defeadted");
                                return;
                            }
                        }
                    }
                    common.openBar('#wrapper');
					var parm = {
						zxks : yzclRight.zxks
					};
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=yzsh&zxks=' + yzclRight.zxks + '&zyh=' + zyh+ '&parm=' + JSON.stringify(parm) + '&yebh=' + yzclRight.yebh, function (json) {
                        if (json.a == '0' && json.d != null) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                var yzxx = json.d.list[i].yzxx.filter(function (item) {
                                    return item.ksrq
                                });
                                json.d.list[i].yzxx = yzxx;


                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                }
                            }
                            common.closeLoading()
                            yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
                        } else {
                            malert(json.c, 'top', 'defeadted')
                            common.closeLoading()
                        }
                    }, function (error) {
                        console.log(error);
                        malert(error, 'top', 'defeadted')
                        common.closeLoading()
                    });
                } else if (yzclRight.num == 2) {//医嘱执行
                    yzclRight.rightJsonList = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.isChecked[i]) {
                            if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
                                malert("病人已出院，无法执行此操作", "top", "defeadted");
                                return;
                            }
                        }
                    }
                    common.openBar('#wrapper');
					var parm = {
						zxks : yzclRight.zxks
					};
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=yzzx&zxks=' + yzclRight.zxks + '&yzlx='+yzclRight.shyzlx+'&zyh=' + zyh+ '&parm=' + JSON.stringify(parm) + '&yebh=' + yzclRight.yebh+'&zxyzts='+yzclRight.zxyzts,
                        function (json) {
                            if (json.a == '0' && json.d != null) {
								
								let zxlist = json.d.list;
								
								var tptime = new Date();
								tptime = tptime.setDate(tptime.getDate()+Number(yzclRight.zxyzts));
								
								for (let i = 0; i < zxlist.length; i++) {
									
									let tpList = [];
									for (let j = 0; j < zxlist[i].yzxx.length; j++) {
										
										if(yzclRight.ypbz == '0'){
											if(zxlist[i].yzxx[j].ypbz !='1' && zxlist[i].yzxx[j].ksrq<=tptime){
												tpList.push(zxlist[i].yzxx[j])
											}
										}else if(yzclRight.ypbz == '1'){
											if(zxlist[i].yzxx[j].ypbz =='1' && zxlist[i].yzxx[j].ksrq<=tptime){
												tpList.push(zxlist[i].yzxx[j])
											}
										}else{
											if(zxlist[i].yzxx[j].ksrq<=tptime){
												tpList.push(zxlist[i].yzxx[j])
											}
										}
									}
									zxlist[i].yzxx = tpList;
								}
								
								
								
                                yzclRight.rightJsonList = common.addSameGroupClass(zxlist);
                                common.closeLoading()
                            } else {
                                malert(json.c, 'top', 'defeadted')
                                common.closeLoading()
                            }
                        }, function (error) {
                            console.log(error);
                            malert(error, 'top', 'defeadted')
                            common.closeLoading()
                        });
                } else if (yzclRight.num == 3) {//药品申领
                    yzclRight.rightJsonList = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.isChecked[i]) {
                            if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
                                malert("病人已出院，无法执行此操作", "top", "defeadted");
                                return;
                            }
                        }
                    }
                    common.openBar('#wrapper');
					var parm = {
						zxks : yzclRight.zxks
					};
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=ypsl&zxks=' + yzclRight.zxks + '&zyh=' + zyh+ '&parm=' + JSON.stringify(parm) + '&yfbm=' + yzclRight.popContent.yfbm,
                        function (json) {
                            if (json.a == '0' && json.a != null) {
                                for (var i = 0; i < json.d.list.length; i++) {
                                    json.d.list[i].ypfyhj = 0;
                                    for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                        json.d.list[i].yzxx[int].no = i;
                                        json.d.list[i].ypfyhj += Number(json.d.list[i].yzxx[int].fyje || 0);
                                    }
                                }
                                yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
                                common.closeLoading()
                            } else {
                                malert(json.c, 'top', 'defeadted')
                                common.closeLoading()
                            }

                        }, function () {
                            malert(error, 'top', 'defeadted')
                            common.closeLoading()
                        });
                } else if (yzclRight.num == 4) {//取消申领
                    yzclRight.rightJsonList = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.isChecked[i]) {
                            if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
                                malert("病人已出院，无法执行此操作", "top", "defeadted");
                                return;
                            }
                        }
                    }
                    common.openBar('#wrapper');
					var parm = {
						zxks : yzclRight.zxks
					};
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=qxypsl&zxks=' + yzclRight.zxks + '&zyh=' + zyh+ '&parm=' + JSON.stringify(parm),
                        function (json) {
                            if (json.a == '0' && json.d != null) {
                                for (var i = 0; i < json.d.list.length; i++) {
                                    json.d.list[i].ypfyhj = 0;
                                    for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                        json.d.list[i].yzxx[int].no = i;
                                        json.d.list[i].ypfyhj += Number(json.d.list[i].yzxx[int].fyje || 0);
                                    }
                                }
                                yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
                                common.closeLoading()
                            } else {
                                malert(json.c, 'top', 'defeadted')
                                common.closeLoading()
                            }

                        }, function (error) {
                            console.log(error);
                            malert(error, 'top', 'defeadted')
                            common.closeLoading()
                        });
                } else if (yzclRight.num == 5) {//停嘱申领
                    yzclRight.rightJsonList = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.isChecked[i]) {
                            if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
                                malert("病人已出院，无法执行此操作", "top", "defeadted");
                                return;
                            }
                        }
                    }
                    common.openBar('#wrapper');
					var parm = {
						zxks : yzclRight.zxks
					};
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=hstzsh&zxks=' + yzclRight.zxks + '&zyh=' + zyh+ '&parm=' + JSON.stringify(parm),
                        function (json) {
                            if (json.d != null && json.d.list.length > 0) {
                                for (var i = 0; i < json.d.list.length; i++) {
                                    for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                        json.d.list[i].yzxx[int].no = i;
                                    }
                                }
                                yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
                                common.closeLoading()
                            } else {
                                malert(json.c, 'top', 'defeadted')
                                common.closeLoading()
                            }

                        }, function (error) {
                            console.log(error);
                            common.closeLoading()
                        });
                }
                else if (yzclRight.num == 6) {//查询待审核的作废医嘱
                    yzclRight.rightJsonList = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.isChecked[i]) {
                            if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
                                malert("病人已出院，无法执行此操作", "top", "defeadted");
                                return;
                            }
                        }
                    }
                    common.openBar('#wrapper');
					var parm = {
						zxks : yzclRight.zxks
					};
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=cxzfyzsh&zxks=' + yzclRight.zxks + '&zyh=' + zyh+ '&parm=' + JSON.stringify(parm), function (json) {
                        if (json.a == '0' && json.d != null) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                }
                            }
                            common.closeLoading()
                            yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
                        } else {
                            malert(json.c, 'top', 'defeadted')
                            common.closeLoading()
                        }
                    }, function (error) {
                        console.log(error);
                        malert(error, 'top', 'defeadted')
                        common.closeLoading()
                    });

                }
                else if (yzclRight.num == 7) {//查询待审核的撤消医嘱
                    yzclRight.rightJsonList = [];
                    for (var i = 0; i < yzclLeft.brlist.length; i++) {
                        if (yzclLeft.isChecked[i]) {
                            if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
                                malert("病人已出院，无法执行此操作", "top", "defeadted");
                                return;
                            }
                        }
                    }
                    common.openBar('#wrapper');
					var parm = {
						zxks : yzclRight.zxks
					};
                    $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=cxcxyzsh&zxks=' + yzclRight.zxks + '&zyh=' + zyh+ '&parm=' + JSON.stringify(parm), function (json) {
                        if (json.a == '0' && json.d != null) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                for (var int = 0; int < json.d.list[i].yzxx.length; int++) {
                                    json.d.list[i].yzxx[int].no = i;
                                }
                            }
                            common.closeLoading()
                            yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
                        } else {
                            malert(json.c, 'top', 'defeadted')
                            common.closeLoading()
                        }
                    }, function (error) {
                        console.log(error);
                        malert(error, 'top', 'defeadted')
                        common.closeLoading()
                    });

                }else if(yzclRight.num == 8){
					yzclRight.rightJsonList = [];
					for (var i = 0; i < yzclLeft.brlist.length; i++) {
					    if (yzclLeft.isChecked[i]) {
					        if (yzclLeft.brlist[i].zyzt == '1' || yzclLeft.brlist[i].zyzt == '2') {
					            malert("病人已出院，无法执行此操作", "top", "defeadted");
					            return;
					        }
					    }
					}
					common.openBar('#wrapper');
					let tpmethod = 'queryAll';
					var searchzyh = [];
					            for (var i = 0; i < zyhs.length; i++) {
					                searchzyh.push(zyhs[i].zyh);
					}
					
					
					
					var parm = {
					                beginrq: yzclRight.zxyztime+" 00:00:00",
					                endrq: yzclRight.zxyztime+" 23:59:59",
					                yzlx: yzclRight.shyzlx,
					                searchzyh: searchzyh,
					            };
					$.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types='+tpmethod+'&parm=' + JSON.stringify(parm), function (json) {
					                if (json.a == 0) {
					                   yzclRight.rightJsonList = common.addSameGroupClass(json.d.list);
					                   common.closeLoading()
					                } else {
										common.closeLoading()
					                    malert(json.c, 'top', 'defeadted')
										common.closeLoading()
					                }
					            }, function (error) {
					                console.log(error);
					            });
				}
            }
            ,
			 GetDateStr:function(AddDayCount) { 
				if(AddDayCount){
					AddDayCount = parseInt(AddDayCount); 
				}else{
					AddDayCount = 0;
				}
				

			   var dd = new Date();

			   dd.setDate(dd.getDate()+AddDayCount);//获取AddDayCount天后的日期

			   var y = dd.getFullYear(); 

			   var m = (dd.getMonth()+1)<10?"0"+(dd.getMonth()+1):(dd.getMonth()+1);//获取当前月份的日期，不足10补0

			   var d = dd.getDate()<10?"0"+dd.getDate():dd.getDate();//获取当前几号，不足10补0

			   return y+"-"+m+"-"+d; 

			},
            setZyh: function (brlist) {
                this.zyhs = [];
                this.arr = [];
                for (var i = 0; i < this[brlist].length; i++) {
                    if (this.isChecked[i]) {
                        var obj = {};
                        obj.zyh = this[brlist][i].zyh;
                        this.zyhs.push(JSON.parse(JSON.stringify(obj)));
                        this.arr.push(this[brlist][i].zyh)
                    }
                }
            }
            ,
            reCheckBoxZyh: function (val) {
                yzclLeft.refreshYe = true;
                this.zyhs = [];

                if (val[1] != 'all') this.activeIndex = val[0];
                // if(yzclRight.index == 8){
                //     val[0] = 'one';
                // }
                if (val[0] == 'some') {
                    this.timeArr[val[1]] = new Date().getTime()
                    this.arr = []
                    yzclRight.rightFlag = false;
                    Vue.set(this.isChecked, val[1], val[2]);
                    if (!val[2]) this.isCheckAll = false;
                    if (val[2]) {
                        // for (var i = 0; i < this.brlist.length; i++) {
                        //     if (this.isChecked[i]) {
                        //         var obj = {};
                        //         obj.zyh = this.brlist[i].zyh;
                        //         this.zyhs.push(obj);
                        //         this.arr.push(this.brlist[i].zyh)
                        //     }
                        // }
                        this.queryPatient(val[1]);
                        this.setZyh('brlist');
                        UserHzxx.index = 0;
						
						sessionStorage.setItem("psjglr_HszbrItem", JSON.stringify(this.zyhs));
						if(yzclRight.index =='6' ){
							var iframe_home = document.querySelector('#loadingPage001')
							window.zxdWindow()
						}else{
							this.queryYz(this.zyhs);
						}
                        
						
						
                        
						
						
                        
                        // yzclRight.timeTabArr = getTodayDateTime()
                        //                    yzclRight.rightJsonList.push(this.brlist[val[1]]);
                    } else {
                        
                        this.setZyh('brlist');
						sessionStorage.setItem("psjglr_HszbrItem", JSON.stringify(this.zyhs));
						
                        if(yzclRight.index =='6' ){
                        	var iframe_home = document.querySelector('#loadingPage001')
							window.zxdWindow()
                        }else{
                        	this.queryYz(this.zyhs);
                        }
                    }
                    this.$nextTick(function () {
                        yzclRight.rightFlag = true
                    });
                    console.log(this.isChecked)
                    this.HszbrItem = this.brlist[val[1]]
                    sessionStorage.setItem("HszbrItem", JSON.stringify(this.brlist[val[1]]));
                } else if (val[0] == 'all') {
                    this.timeArr = []
                    this.zyhs = [];
                    this.arr = [];
                    this.isChecked = [];
                    this.isCheckAll = val[2];
                    console.log(this.isCheckAll);
                    if (val[1] == null) val[1] = "jsonList";
                    if (this.isCheckAll) {
                        for (var i = 0; i < this[val[1]].length; i++) {
                            this.timeArr[i] = new Date().getTime()
                            Vue.set(this.isChecked, i, true);
                            var obj = {};
                            obj.zyh = this.brlist[i].zyh;
                            this.zyhs.push(obj);
                            this.arr.push(this.brlist[i].zyh)
                        }
                        this.queryYz(this.zyhs);
                    } else {
                        this.arr = []
                        this.isChecked = [];
                        this.queryYz([]);
                    }
                } else if (val[0] == 'one') {
                    this.timeArr = []
                    this.timeArr[val[1]] = new Date().getTime()
                    this.arr = []
                    this.isChecked = [];
                    Vue.set(this.isChecked, val[1], val[2]);
                    this.HszbrItem = this.brlist[val[1]]
                    this.$forceUpdate()
                    var openUrl;
                    if(yzclRight.index == 5){//体温单进入时候，默认选择成人
                        this.brlist[val[1]].yebh = '000';
                        tabBg('childpage/scsj', 5, openUrl);
                        // if(right.pageType == '1'){//刷新体温单
                        //     right.loadCon(1);
                        // }
                    }
                    sessionStorage.setItem("HszbrItem", JSON.stringify(this.brlist[val[1]]));
                    // sessionStorage.setItem("psjglr_HszbrItem", JSON.stringify([this.brlist[val[1]]]));
                    if(yzclRight.index == 7){//医嘱单进入时候，刷新婴儿信息
                        tabBg('childpage/yzd', 7, openUrl);
                    }
                    if(yzclRight.index == 4){//费用清单
                        tabBg('childpage/fyqd', 4, openUrl);
                    }
                }
            }
            ,
            tablebody: function (evnet) {
                this.$nextTick(function () {
                    return this.tableheight = $('body').outerHeight() - $(this.$refs.tableBody).offset().top - this.$refs.yzclBtoom.offsetHeight - 15;
                })
            }
            ,
            getKs: function (cb) {
                var dg = {
                    "page": 1,
                    "rows": 20000,
                    "sort": "",
                    "order": "asc",
                };
                var json = {
                	"zyks":"1"
                }
                
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json="+JSON.stringify(json)+"&dg=" + JSON.stringify(dg), function (json) {
                    if (json.a == 0 && json.d != null) {
                        var rlksList = [];
                        for (var i = 0; i < json.d.list.length; i++) {
                            if (json.d.list[i].bqbm != null) {
                                rlksList.push(json.d.list[i]);
                            }
                        }
                        length = rlksList.length;
                        ksList = {};
                        for (var i = 0; i < length; i++) {
                            ksList[rlksList[i].ksbm] = rlksList[i].ksmc;
                        }
                        yzclLeft.kslist = ksList;
                        yzclLeft.jsContent.ksbm = rlksList[0].ksbm;
                        yzclLeft.jsContent.ksmc = rlksList[0].ksmc;
                        cb && cb(ksList);
                    } else {
                        malert("获取科室列表失败!" + json.c, 'top', 'defeadted')
                    }
                });
            }
            ,
        }
        ,
    })
;

var yzclRight = new Vue({
    el: '.yzclRight',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data: {
        ypContent: {},
        cxObj: {"cx":"1","cxyj":"同意撤销"},
        jsonList: [],
        errorText: [],
        Jcbg_List: [],
        yeList: [],
        title: '异常病人信息备注【已经取消勾选】',
        isChecked: [],
        cx_tran:{
            '1':"同意",
            '2':"拒绝"
        },
        tabArr: [{text: '医嘱查询'}, {text: '审核医嘱'}, {text: '发送医嘱'}, {text: '药品申领'}, {text: '取消申领（查询）'}, {text: '停嘱审核'}, {text: '作废审核'}, {text: '撤消审核'}, {text: '执行医嘱'}],
        rightJsonList: [],
        num: 0,
        brListWinSize: { // 显示区域的边界
            top: null,
            bottom: null
        },
        popContent: {
            yfbm: ''
        },
        openLeft: true,
        errBr: false,
        yzcxShow: false,
        mxcx: false,
        watchDate: 0,
		zxyzts: "0",
        timeTabArr: '',
		shtime: '',
		zxyztime:'',
        brPosition: [0],
        errorModelText: [],
        yzzxInfoList: [],//真正的列表
        brItemListSumHeight: 0, // 所有病人加起来的高度
        rightFlag: true,
        index: 0,
        isOver: false,//是否全选
        auditingData: [],
        yzlx: '2',
        yfpz: '0',
        ypbz: '2',
        ystzbz: '9',
		shyzlx:'1',
		shyzlxz:true,
        yzlx_tran: {
            "1": "长期",
            "0": "临时",
            "2": "全部"
        },
		shyzlx_tran: {
		    "1": "长期",
		    "0": "临时"
		},
        ypfy_tran: {
            "0": "非药品",
            "1": "药品",
            "2": "全部",
            "3": "特殊药品",
        },
        ystzbz_tran: {
            '0': '正常',
            '1': '停嘱',
            '9': '全部',
        },
        tabParentText: [
            {'onclick': undefined, 'text': '医嘱管理'},
            {'onclick': 'childpage/jcjy', 'text': '检查检验'},
            {'onclick': undefined, 'text': '电子病历', openUrl: 'dzbl'},
            {'onclick': undefined, 'text': '临床路径'},
            // {'onclick': 'childpage/hlws', 'text': '护理文书'},
            // {'onclick': 'childpage/wzhljl', 'text': '危重护理记录'},
            // {'onclick': 'childpage/hljl', 'text': '护理记录'},
            // {'onclick': 'childpage/hlfxpg', 'text': '护理风险评估'},
            {'onclick': 'childpage/fyqd', 'text': '费用清单'},
            {'onclick': 'childpage/scsj', 'text': '体温单'},
            {'onclick': 'childpage/yzzxd', 'text': '执行单'},
            {'onclick': 'childpage/yzd', 'text': '医嘱单'},
            {'onclick': 'childpage/zxjh', 'text': '执行计划'},
        ],
        scrollFn: function () {
        },
        isAllFlag: false,
        passwordModel: false,
        fyjl: false,
        password: '',
        caqxContent: {},
        yebh: 'all',
        fyjlList: [],
        _laydate: '',
        beginrq: '',
        endrq: '',
        searchzyh: '',
        bydList: [],
        searchsldh: [],
        sldList: [],
        fymx_tran: {
            '0': '未发药',
            '1': '已发药'
        },
        bydh: '0',
        fymxid: '0',
        bydh_tran: {
            '0': '未摆药',
            '1': '已摆药'
        },
        type_tran: {
            '0': '明细',
            '1': '汇总'
        },
        type: '0',
        sqdmxList: [],
        isCheckBoxByd: [],
        isCheckBydAll: '',
        N03003200154: '0',
        zyh: '',
        clearId:null,
        clearIDS:null,
		clearShIDS:null,
		jcDateList:[],
		wzxfy:false,
		fyxm_List:[],
		isZxChecked:[],
		isZxCheckAll:false,
		wzxyzList:[], 
		wzxtype : null,
		yszxsjType:false,
		yszxTime : null,
		zxhslist:[],
		rybm:null,
		ryxm:null,
		zxks:'',
    },
    created: function () {
        this.scrollFn = this.brScrollFn();
        this.Wf_init()
		this.getUserInfo();
    },

    computed: {
        yfbm: function () {
            if (yzclLeft.caqxContent.cs03003200101) {
                for (var i = 0; i < this.Yf_List.length; i++) {
                    if (this.Yf_List[i].yfbm == yzclLeft.caqxContent.cs03003200101) {
                        this.popContent.yfbm = this.Yf_List[i].yfbm
                        this.popContent.yfmc = this.Yf_List[i].yfmc
                    }
                }
            } else {
                if (this.Yf_List.length != 0) {
                    this.popContent.yfbm = this.Yf_List[0].yfbm
                    this.popContent.yfmc = this.Yf_List[0].yfmc
                }
            }
        }
    },
    watch: {
        'rightJsonList': function (newValue, OldValue) {
            var _list = newValue, _length = newValue.length, _brPosition = [0], _brItemListSumHeight = 0;
            if (newValue.length > 0) {
                common.openloading('#wrapper')
                for (var i = 0; i < newValue.length; i++) {
                    if (this.rightJsonList[i].yzxx.length == 0) {
                        this.rightJsonList.splice(i, 1)
                        continue;
                    }
                    this.isAllFlag = false;
                    for (var int = 0; int < newValue[i].yzxx.length; int++) {
                        // 计算当前元素定位
                        var _yzxxListLength = _list[i].yzxx.length;
                        _brPosition[i + 1] = (93 + _yzxxListLength * 40) + (_brPosition[i] == undefined ? _brPosition[i - 1] : _brPosition[i]);
                        // 计算总高度
                        if (i === _length - 1) {
                            // + 70 + _yzxxListLength * 40
                            _brItemListSumHeight = _brPosition[i + 1];
                        }
                        if(!yzclLeft.caqxContent.cs00900100123) {
                            if (newValue[i].yzxx[int].numb >= 1) {
                                this.isAllFlag = true;
                            }
                        }
                    }
                    this.rightJsonList[i].isAllFlag = this.isAllFlag
                }
                // 初始化显示边界
                this.$nextTick(function () {
                    var _height = $("#wrapper").height();
                    this.brListWinSize = {
                        top: 0 - _height,
                        bottom: _height * 2
                    }
                });
                yzclRight.brItemListSumHeight = _brItemListSumHeight;
                yzclRight.brPosition = this.notempty(_brPosition);
                common.closeLoading()
				if(this.num!=8){
					this.isOverClick(true)
				}
                
            }
        },
    },
    updated: function () {
        changeScroll()
    },
    mounted: function () {
        Mask.newMask(this.MaskOptions('timeTabArr'));
        this.timeTabArr = this.getDate() || getTodayDateTime();
		this.shtime = this.getDate() || getTodayDateTime();
        var myDate = new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()), 'date') + ' 00:00:00';
        this.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        this.initTime(60000);
		this.zxyztime = this.fDate(myDate.setDate(myDate.getDate()), 'date');
    },
    methods: {
		getUserInfo: function () {
		    this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
		        .then(function (json) {
		            yzclRight.zxks = json.body.d.ksbm;
		        });
		},
		yzzxsjszClose:function(){
			yzclRight.yszxsjType=false;
			yzclLeft.ifClick = true;
		},
		showZxUpdateDate: function (index, event) {
		    var _laydate = {
		        elem: '#yszxTime'
		        , show: true //直接显示
		        , format: 'yyyy-MM-dd HH:mm:ss'
		        , type: 'datetime'
		        , theme: '#1ab394',
		        done: function (value, data) {
		            yzclRight.yszxTime = value;
		        }
		    };
		    laydate.render(_laydate)//初始化时间插件
		},
		reZxCheckBox: function (val) {
		            var that = this
		            if (val[1] !== 'all') this.activeIndex = val[0];
		            if (val[0] == 'some') {
		                Vue.set(this.isZxChecked, val[1], val[2]);
		                if (that.notempty(this.isZxChecked).length == this[val[3]].length) {
		                    this.isZxChecked.every(function (el) {
		                        if (el === true) {
		                            return that.isZxCheckAll = true
		                        } else {
		                            return that.isZxCheckAll = false
		                        }
		                    })
		                }
		                console.log(this.isZxChecked)
		            } else if (val[0] == 'one') {
		                this.isZxCheckAll = false;
		                this.isZxChecked = [];
		                Vue.set(this.isZxChecked, val[1], val[2]);
		            } else if (val[0] == 'all') {
		                this.isZxCheckAll = val[2];
		                console.log(this.isZxCheckAll);
		                if (val[1] == null) val[1] = "jsonList";
		                if (this.isZxCheckAll) {
		                    for (var i = 0; i < this[val[1]].length; i++) {
		                        Vue.set(this.isZxChecked, i, true);
		                        // this.isChecked[i] = true;
		                    }
		                } else {
		                    this.isZxChecked = [];
		                }
		            }
		        }
		        ,
				
				saveZxFy:function(type){
					let param = [];
					for (var i = 0; i < this.isZxChecked.length; i++) {
							if(this.isZxChecked[i]){
								if(type =='1'){
									param.push({
										zyh:this.fyxm_List[i].zyh,
										fyid:this.fyxm_List[i].fyid,
										yxbz:'1',
										})
								}else{
									param.push({
											zyh:this.fyxm_List[i].zyh,
											fyid:this.fyxm_List[i].fyid,
											zfbz:'1',
											zfry:userId,
											zfryxm:userName,
											zfsj:this.fDate(new Date())
										})
								}
								
							}
						}
					console.log(param)
					
					$.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=updateWzxbatch&parm=' + JSON.stringify(param), function (json) {
					    if (json.a == '0') {
							malert('修改成功', 'top', 'success');
							yzclRight.isZxChecked = [];
							$.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=queryWzxfy&zyh=' + yzclRight.auditingData[0].zyh, function (json) {
													    if (json.a == '0') {
													        if(json.d.length>0){
																yzclRight.fyxm_List = json.d
															}else{
																yzclRight.wzxfy = false;
																yzclLeft.confirm(1);
															}
													    }
									})
							
					    }else{
							malert(json.c, 'top', 'defeadted');
							return false
						}
					})
					
				},
				
		reZxCheckChange: function (val) {
			
		    for (var i = 0; i < this.fyxm_List.length; i++) {
		        Vue.set(this.isZxChecked, val[1], val[2]);
		    }
		    if (!val[2]) this.isZxCheckAll = false;
		},
		
		zxyztsClick :function(zxyzts){
			this.zxyzts = zxyzts;
			yzclRight.queryYzxx();
			
		},
        initTime:function (num){
         this.clearIDS= setInterval(function () {
               clearInterval(yzclRight.clearId)
                yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
            }, num)
			this.clearShIDS= setInterval(function () {
			      clearInterval(yzclRight.clearId)
			       yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
			   }, num)
        },
        setTime(event){
            this.timeTabArr=event.target.value
            console.log(this.timeTabArr)
        },
        showDate1:function(){
            clearInterval(yzclRight.clearIDS)
            yzclRight.initTime(60000);
            laydate.render({
                elem: '#timeTabArr',
                rigger: 'click',
                type: 'datetime'
                , show: true, //直接显示
                theme: '#1ab394',
                done: function (value, data) { //回调方法
                    yzclRight.timeTabArr = value;
                    yzclRight.watchDate = 1;
                }
            });
        },
		shDate:function(){
		    clearInterval(yzclRight.clearShIDS)
		    yzclRight.initTime(60000);
		    laydate.render({
		        elem: '#shtime',
		        rigger: 'click',
		        type: 'datetime'
		        , show: true, //直接显示
		        theme: '#1ab394',
		        done: function (value, data) { //回调方法
		            yzclRight.shtime = value;
		            yzclRight.watchDate = 1;
		        }
		    });
		},
		zxyzDate:function(){
		    clearInterval(yzclRight.clearShIDS)
		    yzclRight.initTime(60000);
		    laydate.render({
		        elem: '#zxyztime',
		        rigger: 'click',
		         show: true, //直接显示
		        theme: '#1ab394',
		        done: function (value, data) { //回调方法
		            yzclRight.zxyztime = value;
		            yzclRight.watchDate = 1;
					yzclRight.queryYzxx();
					
					
		        }
		    });
		},
        getDate: function () {
            var date, that = this;
            this.updatedAjax("/actionDispatcher.do?reqUrl=GetDropDown&types=hqfwqsj", function (json) {
                if (json.a == 0) {
                    date = that.fDate(json.d);  //服务器时间获取
                } else {
                    // malert("服务器时间获取失败", 'top', 'defeadted');
                }
            });
            return date
        },
        commonResultChange: function (val) {
			            var type = val[2][val[2].length - 1];
            switch (type) {
                case "yzlx":
                    this.yzlx = val[0]
                    yzclRight.queryYzxx();
                    break;
                case "ypbz":
                    this.ypbz = val[0]
                    yzclRight.queryYzxx();
                    break;
                case "ystzbz":
                    this.ystzbz = val[0];
                    yzclRight.queryYzxx();
                    break;
                case 'yfbm':
                    Vue.set(this['popContent'], "yfbm", val[0]);
                    Vue.set(this['popContent'], "yfmc", val[4]);
                    Vue.set(this['popContent'], "yflx", yzclRight.Yf_List[val[5]].yflx);
                    this.$forceUpdate();
                    break;
                case "yebh":
                    this.yebh = val[0];
                    yzclRight.queryYzxx();
                    break;
				case "shyzlx":
					this.shyzlx = val[0];
					if(this.shyzlx == '1'){
						this.shyzlxz = true;
					}else{
						this.shyzlxz = false;
					}
					yzclRight.queryYzxx();
					break;
				case "rybm":
					yzclRight.rybm= val[0];
					yzclRight.ryxm= val[4];
					this.$forceUpdate();
					break;
            }
        },
        //初始化数据
        Wf_init: function () {
            var yf_dg = {page: 1, rows: 1000, sort: "yfbm", order: "asc", parm: ""};
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmYf&types=query&dg=' + JSON.stringify(yf_dg) + '&json=' + JSON.stringify({tybz: '0'}), function (data) {
                if (data.a == 0) {
                    yzclRight.Yf_List = data.d.list;  //绑定药房
                    yzclRight.popContent.yfbm = data.d.list[0].yfbm;  //绑定药房
                    yzclRight.popContent.yfmc = data.d.list[0].yfmc;  //绑定药房
                    yzclRight.popContent.yflx = data.d.list[0].fylx;  //绑定药房
                } else {
                    malert('获取药房失败', 'top', 'defeadted');
                }
            });
        },
        openLeftFun: function () {
            yzclLeft.openLeft = !yzclLeft.openLeft
            yzclRight.openLeft = !yzclRight.openLeft
        },
        openUrl: function () {
            if (yzclLeft.arr.length == 0) {
                malert('请选择一个患者查看报告单', 'top', 'defeadted');
                return false;
            } else if (yzclLeft.arr.length > 1) {
                malert('请选择一个患者查看报告单', 'top', 'defeadted');
                return false;
            }
            this.jvbgdYz();
            var yzxh = [];
            for (var i = 0; i < this.Jcbg_List.length; i++) {
                if (this.Jcbg_List[i].jcfl == '1') {
                    yzxh.push(this.Jcbg_List[i].yzxh + this.Jcbg_List[i].zlbm)
                }
            }
            window.open(window.top.J_tabLeft.obj.xwpacsdz + "/ClinicList.aspx?colid0=3078&colvalue0=~in~" + yzxh.join(',') + "");
        },
        jvbgdYz: function () {
            var ksbm = yzclLeft.jsContent.ksbm;   //科室
            var zyh = yzclLeft.arr[0];  //住院号
            var ystzbz = '%';
            var hstzbz = null;
            var zfbz = null;
            var shbz = null;
            /***************************以上为标记代码******************************/
            if (ystzbz == '%') {//全部
                zfbz = "0";
                ystzbz = '';
            }
            if (ksbm == null || ksbm == undefined || ksbm == "") {
                malert("病人科室不能为空！，请选择科室.", 'top', 'defeadted');
                return;
            }
            if (zyh == null || zyh == undefined || zyh == "") {
                malert("请选择病人！", 'top', 'defeadted');
                return;
            }
            this.Jcbg_List = [];   //先清空再说
            var parm_ksbm = {
                ksbm: ksbm,
                yzlx: '',
                ystzbz: ystzbz,
                hstzbz: hstzbz,
                zfbz: zfbz,
                shbz: shbz,
            };
            var parm_zyh = [{
                zyh: zyh
            }];
            this.updatedAjax('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzxx&parm=' + JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh), function (json) {
                if (json.a == '0') {
                    yzclRight.Jcbg_List = json.d.list;
                }
            })
        },
        brScrollFn: function (flag) {
            if (flag) {
                common.openloading('#wrapper')
            }
            var timer = null;
            return function () {
                clearTimeout(timer)
                timer = setTimeout(function () {
                    var _el = $("#wrapper"),
                        _height = _el.height(),
                        _top = _el.scrollTop();
                    yzclRight.brListWinSize = {
                        top: _top - _height,
                        bottom: _top + _height * 2
                    }
                    common.closeLoading()
                }, 20);
            }
        },
		reCheckOne: function (val) {
            Vue.set(this, val[0], val[1])
            // tableInfo.getBrData()
			yzclRight.queryYzxx();
			
			
			
        },
        isOverClick: function (isOver) {
            var isAllFlag = false;
            this.rightJsonList.forEach(function (br) {
                if (!br.isAllFlag) {
                    br.isCheckAll = isOver;
                } else {
                    isAllFlag = true;
                }
                br.yzxx.forEach(function (yz) {
                    if(!yzclLeft.caqxContent.cs00900100123){
                        if (yz.numb >= 1) {}
                    }
                    else {
						if(yz.numb>0&&yzclRight.num==2){
							yz.isChecked = false;
							
						}else{
							yz.isChecked = isOver;
								if (yzclRight.num == "1" && isOver && yz.bzsm){
													       malert("您选中的医嘱有备注信息："+yz.bzsm, 'right', 'defeadted');
													    }
							
						}
                        
                    }
                });
            });
            if (!isAllFlag) {
                this.isOver = isOver;
            } else {
                this.isOver = !isOver;
            }
        },
        bqcy: function () {
            var list = [];
            if (yzclLeft.isChecked.length < 1 || yzclLeft.isChecked == null) {
                malert("请选择一个病人之后再操作！", "top", "defeadted");
                return;
            }
            for (var i = 0; i < yzclLeft.brlist.length; i++) {
                if (yzclLeft.isChecked[i]) {
                    list.push(yzclLeft.brlist[i]);
                }
            }
            if (list.length > 1) {
                malert("此操作只针对单人！", "top", "defeadted");
                return;
            }
            var parm = {
                zyh: list[0].zyh,
                ksbm: yzclLeft.jsContent.ksbm
            };
            if (!confirm("你确定要出院【患者：" + list[0].brxm + " 住院号：" + list[0].zyh + "】的病人吗？")) {
                return false;
            }
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=bqcy',
                JSON.stringify(parm))
                .then(function (data) {
                    if (data.body.a == "0") {
                        malert("病区出院成功！", "top", "success");
                        yzclLeft.getHzlb();
                    } else {
                        malert(data.body.c, "top", "defeadted");
                    }
                });
        },
        qxbqcy: function () {
            var list = [];
            if (yzclLeft.isChecked.length < 1 || yzclLeft.isChecked == null) {
                malert("请选择一个病人之后再操作！", "top", "defeadted");
                return;
            }
            for (var i = 0; i < yzclLeft.brlist.length; i++) {
                if (yzclLeft.isChecked[i]) {
                    list.push(yzclLeft.brlist[i]);
                }
            }
            if (list.length > 1) {
                malert("此操作只针对单人！", "top", "defeadted");
                return;
            }
            if (!confirm("你确定要取消出院【" + (list[0].zyh) + "】住院号的病人吗？")) {
                return false;
            }
            qxbqcy.qxbqcyBrContent = {
                zyh: list[0].zyh,
                cyks: yzclLeft.jsContent.ksbm
            };
            qxbqcy.initCw("qxbqcy", list[0].rycwbh);
        },
        qxbqcyOperation: function () {
            if (qxbqcy.sfCxfc) {
                qxbqcy.qxbqcyBrContent.cwid = qxbqcy.jkacContent.cwid;
                qxbqcy.qxbqcyBrContent.cwbh = qxbqcy.jkacContent.cwbh;
            }
            common.openloading();
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=qxbqcy',
                JSON.stringify(qxbqcy.qxbqcyBrContent))
                .then(function (data) {
                    if (data.body.a == "0") {
                        malert("取消病区出院成功！", 'top', 'success');
                        yzclLeft.brlist = [];
                        yzclLeft.getHzlb();
                        qxbqcy.Class = true;
                        qxbqcy.cwList = [];
                        qxbqcy.jkacContent = {};
                        common.closeLoading();
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                        yzclLeft.brlist = [];
                        yzclLeft.getHzlb();
                        common.closeLoading();
                    }
                });
        },
        ghys: function () {
            var list = [];
            if (yzclLeft.isChecked.length < 1 || yzclLeft.isChecked == null) {
                malert("请选择一个病人之后再操作！", "top", "defeadted");
                return;
            }
            for (var i = 0; i < yzclLeft.brlist.length; i++) {
                if (yzclLeft.isChecked[i]) {
                    list.push(yzclLeft.brlist[i]);
                }
            }
            if (list.length > 1) {
                malert("此操作只针对单人！", "top", "defeadted");
                return;
            }
            ghys.getYsData();
            ghys.isShow = true;
            ghys.brInfo = list[0];
        },
        qccl: function (index) {
            var list = [];
            if (yzclLeft.isChecked.length < 1 || yzclLeft.isChecked == null) {
                malert("请选择一个病人之后再操作！", "top", "defeadted");
                return;
            }
            for (var i = 0; i < yzclLeft.brlist.length; i++) {
                if (yzclLeft.isChecked[i]) {
                    list.push(yzclLeft.brlist[i]);
                }
            }
            if (list.length > 1) {
                malert("此操作只针对单人！", "top", "defeadted");
                return;
            }
            qccl.getYsData();
            qccl.getCwData();
            qccl.brInfo = list[0];
            qccl.open();
        },
        // 血糖报告的跳转
        xtbgUrl:function (){
            var zyh = yzclLeft.HszbrItem.zyh;
            var brid = yzclLeft.HszbrItem.brid;
            var ryrq = this.fDate(yzclLeft.HszbrItem.ryrq,'date');
            var dqrq = this.fDate(new Date(),'date');
            console.log('血糖报告,zyh:' + zyh + ',brid:' + brid + ',ryrq:' + ryrq);
            var url = 'http://220.220.220.15:81/dms/Login.aspx?isAutoLogin=1&autoLoginUrl=Pages/Report/Patient_FixedRecord.aspx';
            var param = '?' +
                        // 'PatientBedNo=' + brid + '&'
                        'PatientCode=' + zyh
                        + '&StartDate=' + ryrq
                        + '&EndDate=' + dqrq;
            window.open(url + param);
        },
        hzPrint:function(){
            var ypsllsh = [];
            for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                var map = yzclRight.rightJsonList[i]['yzxx'].reduce(function (p, c, index, array) {
                    if (c.isChecked) [p[c.sldh] = p[c.sldh] || [], p[c.sldh].push(c), p][2]
                    return p
                }, {});
                ypsllsh = (Object.keys(map).map(i => map[i]));
            }
            if (ypsllsh.length <= 0) {
                malert("请先选择要打印的内容!", "top", "defeadted");
                return;
            }
            for (var j = 0; j < ypsllsh.length; j++) {
                var reportlets = "[{reportlet: 'yfgl%2Fyfgl_sldhzdy.cpt',yljgbm:'" + jgbm + "',sldh:'" + ypsllsh[j][0].sldh + "',zyh:'" + ypsllsh[j][0].zyh + "'}]";
                if (!FrPrint(reportlets, null)) {
                    window.print();
                }
            }
        },
        //药品申领单打印
        printYpsld: function () {
            var ypsllsh = [];
            for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                var map = yzclRight.rightJsonList[i]['yzxx'].reduce(function (p, c, index, array) {
                    if (c.isChecked) [p[c.sldh] = p[c.sldh] || [], p[c.sldh].push(c), p][2]
                    return p
                }, {});
                ypsllsh = (Object.keys(map).map(i => map[i]));
            }
            if (ypsllsh.length <= 0) {
                malert("请先选择要打印的内容!", "top", "defeadted");
                return;
            }
            for (var j = 0; j < ypsllsh.length; j++) {
                var reportlets = "[{reportlet: 'yfgl%2Fyfgl_slddy.cpt',yljgbm:'" + jgbm + "',sldh:'" + ypsllsh[j][0].sldh + "',zyh:'" + ypsllsh[j][0].zyh + "'}]";
                if (!FrPrint(reportlets, null)) {
                    window.print();
                }
            }
        },
        gdfy: function (index) {
            var list = [];
            if (yzclLeft.isChecked.length < 1 || yzclLeft.isChecked == null) {
                malert("请选择一个病人之后再操作！", "top", "defeadted");
                return;
            }
            for (var i = 0; i < yzclLeft.brlist.length; i++) {
                if (yzclLeft.isChecked[i]) {
                    list.push(yzclLeft.brlist[i]);
                }
            }
            if (list.length > 1) {
                malert("此操作只针对单人！", "top", "defeadted");
                return;
            }
            sessionStorage.setItem("gdfy", JSON.stringify(list[0]));
            this.topNewPage('固定费用', 'page/hsz/hlyw/bygl/gdfy.html');
        },

        //针对今日出院审核之后进行批量更新医嘱操作
        jrcy: function () {
            if(yzclRight.caqxContent.N030042002734 =='1'){
                return false;
            }
            //判断是否需要停掉之前所有未停医嘱
            var yzfl_tran={
                '1':'1',
                '2':'2',
                '3':'3',
                '4':'4',
                '5':'5',
            };
            for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                var tzyz = false;//针对医嘱类型是否停嘱
                var jzrq = null;
                for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                    if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                        if (yzfl_tran[yzclRight.rightJsonList[i]['yzxx'][j].yzfl]) {
                            tzyz = true;
                            jzrq = yzclRight.rightJsonList[i]['yzxx'][j].ksrq;
                        }
                    }
                }
                //执行停止所有未停医嘱
                if (tzyz) {
                    var wtyzList = [];
                    var ksbm = yzclRight.rightJsonList[i].ryks;   //科室
                    var zyh = yzclRight.rightJsonList[i].zyh;  //住院号
                    var yzlx = '';
                    var ystzbz = '0';
                    var parm_ksbm = {
                        ksbm: ksbm,
                        yzlx: yzlx,
                        ystzbz: ystzbz,
                    };
                    var parm_zyh = [{
                        zyh: zyh
                    }];
                    $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzxx&parm=' + JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh)
                        , function (json) {
                            if (json.a == '0') {
                                wtyzList = json.d.list;
                                if (wtyzList.length < 1) {
                                    //                    	            break;
                                } else {
                                    var fhwtyzList = [];
                                    for (var k = 0; k < wtyzList.length; k++) {
                                        if (wtyzList[k].ksrq < jzrq) {
                                            if (wtyzList[k].shbz == '1') {
                                                fhwtyzList.push(wtyzList[k]);
                                            } else {
                                                malert(wtyzList[k].xmmc + "医嘱未审核，请直接作废或进行审核！");
                                            }
                                        }
                                    }
                                    if (fhwtyzList.length > 0) {
                                        var tzjson = {
                                            list: [
                                                {
                                                    yzxx: fhwtyzList,          //医嘱信息
                                                    lczd: []              //诊断信息（检查，检验的诊断）
                                                }
                                            ]
                                        };
                                        yzclRight.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzstop', JSON.stringify(tzjson)).then(function (data) {
                                            if (data.body.a == 0) {
                                                malert("医嘱停止申请成功", 'top', 'success');
                                            } else {
                                                malert("医嘱停止申请失败：" + data.body.c, 'top', 'defeadted');
                                            }
                                        });
                                    }
                                }
                            }
                        });
                }

            }

        },

        // 弹出审核医嘱输入密码框
        confirm: function (type) {
            if (yzclLeft.caqxContent.N03004200238 == '1') {
                yzclRight.passwordModel = true;
                this.$nextTick(function () {
                    $('#mmyz').focus()
                })
            } else {
                yzclRight.auditing(type);
            }
        },
		
		//************************打印检查检验申请单start***************************
		printJcJy: function () {
		    // 获取选中检查检验的信息
		    			var jcList = [], jyList = [],zlList = [], dyStr = '';
			for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
			    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
			        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
						if (yzclRight.rightJsonList[i]['yzxx'][j]['jcfl'] == '1') {
						    jcList.push(yzclRight.rightJsonList[i]['yzxx'][j]);
						    if (yzclRight.rightJsonList[i]['yzxx'][j]['dycs'] > 0) {
						        dyStr += "【" + yzclRight.rightJsonList[i]['yzxx'][j].xmmc + "】、";
						    }
						        yzclRight.jcDateList.push(yzclRight.rightJsonList[i]['yzxx'][j]);
						        if(!yzclRight.rightJsonList[i]['yzxx'][j]['dycs']){
						            yzclRight.isJcShow=true
						        }
						            yzclRight.jcbz=true;
						} else if (yzclRight.rightJsonList[i]['yzxx'][j]['jcfl'] == '2') {
						    jyList.push(yzclRight.rightJsonList[i]['yzxx'][j]);
						    if (yzclRight.rightJsonList[i]['yzxx'][j]['dycs'] > 0) {
						        dyStr += "【" + yzclRight.rightJsonList[i]['yzxx'][j].xmmc + "】、";
						    }
						} else if (yzclRight.rightJsonList[i]['yzxx'][j]['jcfl'] == '3') {
						    zlList.push(yzclRight.rightJsonList[i]['yzxx'][j]);
						    if (yzclRight.rightJsonList[i]['yzxx'][j]['dycs'] > 0) {
						        dyStr += "【" + yzclRight.rightJsonList[i]['yzxx'][j].xmmc + "】、";
						    }
						}
					}
				}
			}
		    if (jcList.length == 0 && jyList.length == 0 && zlList.length == 0) {
		        malert('没有检查检验的项目', 'top', 'defeadted');
		        return false
		    }
		
		        if (dyStr.length > 0) {
		            //提示已打印
		            common.openConfirm(dyStr.substr(0, dyStr.length - 1) + "已打印，是否重复打印？", function () {
		                yzclRight.updateDycs(jcList, jyList,zlList);
		
		
		            }, null)
		        } else {
		            yzclRight.updateDycs(jcList, jyList,zlList);
		        }
		    
		
		}
		,
		//更新打印次数
		updateDycs: function (jcList, jyList,zlList) {
		    //调用更新打印次数
		    var yzxxList = jcList.concat(jyList);
		    var json = {
		        list: [{yzxx: yzxxList}]
		    };
		    this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=updateDycs', JSON.stringify(json)).then(function (data) {
		        if (data.body.a == 0) {
		            this.doPrintJcJy(jcList, jyList,zlList);
		            
		        } else {
		            malert("更新打印次数失败:" + data.body.c, 'top', 'defeadted');
		        }
		    });
		},
		//更新检查检验打印显示信息
		updateJcJyxx: function (jcList) {
		
		    var json = {
		        list: [{yzxx: jcList}]
		    };
		    this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=updateJcdyxx', JSON.stringify(json)).then(function (data) {
		        if (data.body.a == 0) {
		            this.doPrintJcJy(jcList);
		        } else {
		            malert("更新打印次数失败:" + data.body.c, 'top', 'defeadted');
		        }
		    });
		},
		//执行打印
		doPrintJcJy: function (jcList, jyList,zlList) {
		    var jyxhid = '';
		    var jcxhid = '';
			var zlxhid = '';
		    var reportlets = '', str = '';
		    reportlets = '[' + str + ']';
		
		    if (jcList.length != 0) {
					let map = new Map();
		            for (var i = 0; i < jcList.length; i++) {
						if(map.size>0){
							if(map.get(jcList[i].mbbm)){
								let tpxhids =map.get(jcList[i].mbbm)+(jcList[i].xhid + ",")
								map.delete(jcList[i].mbbm)
								map.set(jcList[i].mbbm,tpxhids)
							}else{
								if(jcList[i].mbbm){
									map.set(jcList[i].mbbm,jcList[i].xhid + ",")
								}else{
									jcxhid += (jcList[i].xhid + ",")
								}
								
							}
						}else{
							if(jcList[i].mbbm){
								map.set(jcList[i].mbbm,jcList[i].xhid + ",")
							}else{
								jcxhid += (jcList[i].xhid + ",")
							}
						}
		            }
					if(jcxhid){
						str += "{reportlet: 'fpdy%2Fzyys%2Fzyys_jcsqd.cpt',xhids:'" + jcxhid + "'},"
					}
					map.forEach(function(key){
					　　console.log("key",key)  //输出的是map中的value值
						str += "{reportlet: 'fpdy%2Fzyys%2Fzyys_jcsqd.cpt',xhids:'" + key + "'},"
					})
					console.log(str)
		    }
		    if (jyList.length != 0) {
				let map = new Map();
						            for (var i = 0; i < jyList.length; i++) {
										if(map.size>0){
											if(map.get(jyList[i].jyfdbh)){
												let tpxhids =map.get(jyList[i].jyfdbh)+(jyList[i].xhid + ",")
												map.delete(jyList[i].jyfdbh)
												map.set(jyList[i].jyfdbh,tpxhids)
											}else{
												if(jyList[i].jyfdbh){
													map.set(jyList[i].jyfdbh,jyList[i].xhid + ",")
												}else{
													jyxhid += (jyList[i].xhid + ",")
												}
												
											}
										}else{
											if(jyList[i].jyfdbh){
												map.set(jyList[i].jyfdbh,jyList[i].xhid + ",")
											}else{
												jyxhid += (jyList[i].xhid + ",")
											}
										}
						            }
									if(jyxhid){
										str += "{reportlet: 'fpdy%2Fzyys%2Fzyys_jysqd.cpt',xhids:'" + jyxhid + "'},"
									}
									map.forEach(function(key){
									　　console.log("key",key)  //输出的是map中的value值
										str += "{reportlet: 'fpdy%2Fzyys%2Fzyys_jysqd.cpt',xhids:'" + key + "'},"
									})
									console.log(str)
		  //           for (var i = 0; i < jyList.length; i++) {
		  //               jyxhid += (jyList[i].xhid + ",")
		  //           }
		  //           str += "{reportlet: 'fpdy%2Fzyys%2Fzyys_jysqd.cpt',xhids:'" + jyxhid + "'},"
		    }
		if (zlList.length != 0) {
		        for (var i = 0; i < zlList.length; i++) {
		            zlxhid += (zlList[i].xhid + ",")
		        }
		        str += "{reportlet: 'fpdy%2Fzyys%2Fzyys_zlsqd.cpt',xhids:'" + zlxhid + "'},"
		}
		        str = str.substr(0, str.length - 1);
		        reportlets = '[' + str + ']';
		        console.log(reportlets);
		        if (FrPrint(reportlets)) {
		            this.isChecked = [];
		            return;
		        }
		
		},
		tsypPrint: function () {
					            var jcxhid = "yzxh:'", str = '',xhid="xhid:'";
					let zyxh = '';
            let tsyp = '';
					for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
		                for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
		                    if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
								if(yzclRight.rightJsonList[i]['yzxx'][j].yfbm =='03'){
									if(zyxh == ''){
										zyxh= yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'] + ",";
									}else if(zyxh && zyxh.indexOf(yzclRight.rightJsonList[i]['yzxx'][j]['yzxh']) ==-1){
										zyxh += yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'] + ",";
									}
								}else{
									jcxhid += yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'] + ","
									let tpxhid = yzclRight.rightJsonList[i]['yzxx'][j]['xhid'];
									if(tpxhid && tpxhid.indexOf("_") != -1 && this.num =='4'){
										tpxhid  = tpxhid.substr(0,tpxhid.indexOf("_"))
									}
									xhid += tpxhid + ","
                                    if(yzclRight.rightJsonList[i]['yzxx'][j]['tsyp'] && yzclRight.rightJsonList[i]['yzxx'][j]['tsyp']>0){
                                        tsyp = yzclRight.rightJsonList[i]['yzxx'][j]['tsyp'];
                                    }
								}
		                    }
		                }
						}
						if(xhid =="xhid:'" && jcxhid=="yzxh:'" && zyxh ==''){
							malert('请选择需要打印的特殊药品', 'top', 'defeadted')
							return false;
						}else if(xhid !="xhid:'" && jcxhid!="yzxh:'"){

                            if(tsyp == '2'){
                                str += "{reportlet: 'fpdy%2Fzyys%2Ftsyp.cpt'," + jcxhid + "',"+xhid+"'},";
                            }else{
                                str += "{reportlet: 'fpdy%2Fzyys%2Ftsypmj.cpt'," + jcxhid + "',"+xhid+"'},";
                            }


						}
						
		                
						if(zyxh && zyxh != ''){
							let tpzyxh = zyxh.split(",")
							for (let i = 0; i < tpzyxh.length; i++) {
								if(tpzyxh[i] != ''){
									str += "{reportlet: 'fpdy%2Fyfgl%2Fzcycf.cpt',yzxh:'" + tpzyxh[i] + "'},"
								}
							}
						}
						str = str.substr(0, str.length - 1);
		                reportlets = '[' + str + ']';
		                if (FrPrint(reportlets,null,null,true)) {
		                    return;
		                }
						
		        },
		
		
		//************************打印检查检验申请单end***************************
		
		
        ypztcx: function () {
            this.mxcx = true;
            this.getSldh()
        },
        getDataChange: function () {
            if (this.searchsldh.length == 0) {
                malert("请勾选申领单号！", "top", "defeadted");
                yzclRight.sldList = [];
                return false;
            }
            var parm = {"ksbm": yzclLeft.jsContent.ksbm, "zyh": this.zyh};
            if (this.N03003200154 == '0') {
                parm.fymxid = this.fymxid;
            }
            if (this.N03003200154 == '1') {
                parm.bydh = this.bydh;
            }
            var searchsldh = JSON.parse(JSON.stringify(yzclRight.searchsldh))
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1HszHlywYzclCx',
                    types: this.type == '0' ? 'queryFymx' : 'queryFyhz',
                    searchsldh: JSON.stringify(yzclRight.notempty(searchsldh)),
                    parm: JSON.stringify(parm)
                }
            }).then(function (json) {
                if (json.body.a == 0 && json.body.d) {
                    yzclRight.sldList = json.body.d.list
                }
            });
        },
        getChange: function (val) {
            this[val[2][0]] = val[0];
            this.getDataChange()
        },
        getSldh: function () {
            var parm = {
                "ksbm": yzclLeft.jsContent.ksbm,
                "beginrq": this.beginrq,
                'endrq': this.endrq,
                'zyh': this.searchzyh
            };
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1HszHlywYzclCx',
                    types: 'cxsldlb',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json) {
                if (json.body.a == 0 && json.body.d && json.body.d.list.length > 0) {
                    yzclRight.bydList = json.body.d.list
                }
            })
        },
        reCheckBoxByd: function (val) {
            if (val[0] == 'all') {
                this.isCheckBydAll = val[2];
                if (this.isCheckBydAll) {
                    for (var i = 0; i < yzclRight.bydList.length; i++) {
                        Vue.set(this.isCheckBoxByd, i, true);
                        Vue.set(this.searchsldh, i, this.bydList[i].sldh);
                    }
                } else {
                    this.isCheckBoxByd = [];
                    this.searchsldh = [];
                }
            } else if (val[0] == 'some') {
                Vue.set(this.isCheckBoxByd, val[1], val[2]);
                if (val[2]) {
                    Vue.set(this.searchsldh, val[1], this.bydList[val[1]].sldh);
                } else {
                    Vue.delete(this.searchsldh, val[1]);
                }
            }

            this.getDataChange();
        },
        showDate: function (elem, code) {
            this._laydate = {
                elem: elem
                , show: true //直接显示
                , rigger: 'click'
                , type: 'datetime'
                , theme: '#1ab394',
                done: function (value, data) {
                    yzclRight[code] = value;
                    yzclRight.getSldh();
                }
            };
            laydate.render(this._laydate)//初始化时间插件
        },
        queryFyjl: function (item) {
            if (this.num == 0) {
                var parm = {"zyh": item.zyh, "xmbm": item.xmbm, 'yzxh': item.yzxh, 'mxxh': item.mxxh};
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'New1HszCxtjFyqd',
                        types: 'queryFyqdMxxm',
                        parm: JSON.stringify(parm)
                    }
                }).then(function (json) {
                    if (json.body.a == 0 && json.body.d && json.body.d.list.length > 0) {
                        yzclRight.fyjl = true;
                        yzclRight.fyjlList = json.body.d;
                        yzclRight.fyjlList.jzrqfysl = 0;
                        for (var i = 0; i < yzclRight.fyjlList.list.length; i++) {
                            yzclRight.fyjlList.jzrqfysl += yzclRight.fyjlList.list[i].fysl;
                        }
                    }
                })
            }
        },
        // 点击确认进行密码验证，审核医嘱操作
        submit: function () {
            if (!yzclRight.password) {
                malert("请输入密码！", "bottom", "defeadted");
                return;
            }
            var loginPassword = JSON.parse(this.getcookie('user_info'+userId)).password || JSON.parse(this.getcookie('user_info'+userId)).czykl;
            // 验证密码是否正确
            if (loginPassword == yzclRight.password) {
                yzclRight.auditing('1');
                yzclRight.passwordModel = false;
                yzclRight.password = '';
            } else {
                malert("密码错误，请重新输入！", "bottom", "defeadted");
                return;
            }
        },
        deleteWs:function(){
            for (var i = 0; i <yzclLeft.isChecked.length ; i++) {
                if(yzclLeft.isChecked[i]){
                    for (var j = 0; j < window.top.navli.noticeList.length; j++) {
                        if(window.top.navli.noticeList[j].msgtype == 0){
                            var sendmsg = {
                                msgtype: '9',
                                ksbm: window.top.navli.noticeList[j].ksbm,
                                yljgbm: jgbm,
                                yqbm: yqbm,
                                msg: "delete",
                                toaddress: '护士站',
                                sbid: window.top.navli.noticeList[j].sbid,
                                ylbm: 'N030042002',
                            };
                            window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                        }
                    }
                }
            }

        },
		timeXg:function(times){
            var tptime = new Date();
            if(times){
                tptime = new Date(times);
            }
			tptime = tptime.setDate(tptime.getDate()+Number(yzclRight.zxyzts));
			
			
			
			return yzclLeft.fDate(tptime,'AllDate');
		},
        /*业务操作统一入口*/
        auditing: function (type) {
                        console.log("type:"+type);
            if (!yzclLeft.ifClick) return; //如果为false表示已经点击了不能再点
            //console.log('yzwhich:'+yzcltoolMenu.yzwhich);
            var time = this.timeTabArr;
            yzclLeft.ifClick = false;
            if (type == 1) {//医嘱审核
                this.errorText = []
                var xgsjxh = ''; //用于获取需要修改审核时间的序号
                this.auditingData = [];
				let jcList = [], jyList = [],zlList = [];
				
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    if (yzclRight.rightJsonList[i].rycwbh == null) {
                        malert("病人：" + yzclRight.rightJsonList[i].brxm + ",住院号：" + yzclRight.rightJsonList[i].zyh + "未接科安床，无法完成操作！", "top", "defeadted");
                        yzclLeft.ifClick = true;
                        return
                    }
                    var xhids=[];
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            // @yqq
                            //ysb 不能直接取下嘱时间
                            var obj = {
                                "xhid": yzclRight.rightJsonList[i]['yzxx'][j].xhid,
                                "shsj": '',
                                "zyh": yzclRight.rightJsonList[i]['yzxx'][j].zyh
                            };
                            if(yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'].indexOf("yp")!='-1'){
                                xhids.push(yzclRight.rightJsonList[i].zyh+'_'+yzclRight.rightJsonList[i]['yzxx'][j]['yzxh']+yzclRight.rightJsonList[i]['yzxx'][j]['fzh'])
                            }else if(yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'].indexOf("yl")!='-1'){
                                xhids.push(yzclRight.rightJsonList[i].zyh+'_'+yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'])
                            }
                            if (yzclRight.rightJsonList[i]['yzxx'][j].yzlx == '0' && yzclLeft.caqxContent.N03004200247 == '1') {
                                obj.shsj = yzclRight.rightJsonList[i]['yzxx'][j].ksrq;
                            } else {
                                // if (yzclRight.watchDate == 0) {
                                //     obj.shsj = yzclRight.rightJsonList[i]['yzxx'][j].ksrq;
                                // } else {
                                obj.shsj = time;
                                // }
                            }
                            yzclRight.auditingData.push(obj);
							
							if (yzclRight.rightJsonList[i]['yzxx'][j]['jcfl'] == '1') {
							    jcList.push(yzclRight.rightJsonList[i]['yzxx'][j]);
							} else if (yzclRight.rightJsonList[i]['yzxx'][j]['jcfl'] == '2') {
							    jyList.push(yzclRight.rightJsonList[i]['yzxx'][j]);
							} else if (yzclRight.rightJsonList[i]['yzxx'][j]['jcfl'] == '3') {
							    zlList.push(yzclRight.rightJsonList[i]['yzxx'][j]);
							    
							}
							
							
                            if (xgsjxh.length == 0) {
                                var ms = new Date(time).getTime() - yzclRight.rightJsonList[i]['yzxx'][j].ksrq;
                                if (ms < 0) {
                                    malert("病人：" + yzclRight.rightJsonList[i].brxm + "，医嘱序号--" + (j + 1) + "--开始时间" + yzclLeft.fDate(yzclRight.rightJsonList[i]['yzxx'][j].ksrq, 'datetime') + "超过当前时间,不允许审核!", 'top', 'defeadted')
                                    yzclLeft.ifClick = true;
                                    return false
                                }
                                var intervalHour = Math.floor(ms / 1000 / 60 / 60);
                                if (intervalHour > 1) {//判断如果下医嘱时间与当前时间超过一小时则提示
                                    xgsjxh += "病人：" + yzclRight.rightJsonList[i].brxm + "，医嘱序号--" + (j + 1) + "--当前审核时间与下医嘱时间超过一小时确认审核吗？<br/>";
                                    continue;
                                }
                            }
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱审核！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                //用于判断需要修改审核时间
                common.openBar('#wrapper')

                if (xgsjxh != '') {
                    common.closeLoading();
                    yzclLeft.ifClick = true;
                    if (common.openConfirm(xgsjxh, function () {
						var parm = {
							zxks : yzclRight.zxks
						};
                        yzclRight.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzsh&ksbm=&zxks=' + yzclRight.zxks+ '&parm=' + JSON.stringify(parm),
                            JSON.stringify({list: yzclRight.auditingData})).then(function (data) {
                            if (data.body.a == 0) {
                                                                if (data.body.d && data.body.d.list.length > 0) {
                                    for (var i = 0; i < data.body.d.list.length; i++) {
                                        for (var j = 0; j < yzclLeft.brlist.length; j++) {
                                            if (data.body.d.list[i].zyh == yzclLeft.brlist[j].zyh) {
                                                Vue.set(yzclLeft.isChecked, j, false);
                                            }
                                        }
                                        yzclRight.errorText.push(data.body.d.list[i].error)
                                    }
                                    yzclRight.errBr = true
                                }
                                yzclLeft.ifClick = true;
                                                                malert("审核成功", "bottom", "success");
                                if(xhids.length>0){
                                    for(var i=0;i<xhids.length;i++){
                                        var sendmsg = {
                                            msgtype: '9',
                                            ksbm: yzclLeft.jsContent.ksbm,
                                            yljgbm: jgbm,
                                            yqbm: yqbm,
                                            msg: "delete",
                                            toaddress: '',
                                            sbid: xhids[i],
                                            ylbm: 'N030042002',
                                            pagename:"护士站",
                                        }
                                        window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                                    }
                                }
                                yzclRight.jrcy();
                                if(yzclRight.rightJsonList.length==0){
                                    yzclRight.num = 2;
                                }
                                yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
								yzclRight.queryYzxx();//刷新
								common.closeLoading()
								if (jcList.length > 0 || jyList.length > 0 ) {
									common.openConfirm("是否打印检查检验单？", function () {
										let tpzlList = [];
									    yzclRight.updateDycs(jcList, jyList,tpzlList);
									}, null)
								}
                            } else {
                                yzclLeft.ifClick = true;
								common.closeLoading()
								if(data.body.c.indexOf('未执行') != -1){
									yzclRight.wzxtype = true
									
									$.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=queryWzxfy&zyh=' + yzclRight.auditingData[0].zyh, function (json) {
													    if (json.a == '0') {
													        if(json.d.length>0){
																yzclRight.wzxfy = true;
																yzclRight.fyxm_List = json.d
															}
													    }
									})
								}else{
									malert(data.body.c, "bottom", "defeadted");
								}
								
                            }
                        }, function (error) {
                            yzclLeft.ifClick = true;
                            console.log(error);
                        });
                    })) {
                        return false;
                    }
                } else {
					var parm = {
						zxks : yzclRight.zxks
					};
                    this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzsh&ksbm=&zxks=' + yzclRight.zxks+ '&parm=' + JSON.stringify(parm),
                        JSON.stringify({list: this.auditingData})).then(function (data) {
                        if (data.body.a == 0) {
                            if (data.body.d && data.body.d.list.length > 0) {
                                for (var i = 0; i < data.body.d.list.length; i++) {
                                    for (var j = 0; j < yzclLeft.brlist.length; j++) {
                                        if (data.body.d.list[i].zyh == yzclLeft.brlist[j].zyh) {
                                            Vue.set(yzclLeft.isChecked, j, false);
                                        }
                                    }
                                    yzclRight.errorText.push(data.body.d.list[i].error)
                                }
                                yzclRight.errBr = true
                            }
                            malert("审核成功", "bottom", "success");
                            if(xhids.length>0){
                                for(var i=0;i<xhids.length;i++){
                                    var sendmsg = {
                                        msgtype: '9',
                                        ksbm: yzclLeft.jsContent.ksbm,
                                        yljgbm: jgbm,
                                        yqbm: yqbm,
                                        msg: "delete",
                                        toaddress: '',
                                        sbid: xhids[i],
                                        ylbm: 'N030042002',
                                        pagename:"护士站",
                                    }
                                    window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                                }
                            }
                            yzclLeft.ifClick = true;
                            yzclRight.jrcy();
                            if(yzclRight.rightJsonList.length==0){
                                yzclRight.num = 2;
                            }
                            yzclRight.queryYzxx();//刷新
                            common.closeLoading()
							if (jcList.length != 0 || jyList.length != 0 ) {
								common.openConfirm("是否打印检查检验单？", function () {
								    let tpzlList = [];
								    yzclRight.updateDycs(jcList, jyList,tpzlList);
								}, null)
							}
                        } else {
                            yzclLeft.ifClick = true;
                            common.closeLoading()
                            if(data.body.c.indexOf('未执行') != -1){
                            	yzclRight.wzxtype = true
                            	
                            	$.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=queryWzxfy&zyh=' + yzclRight.auditingData[0].zyh, function (json) {
                            					    if (json.a == '0') {
                            					        if(json.d.length>0){
                            								yzclRight.wzxfy = true;
                            								yzclRight.fyxm_List = json.d
                            							}
                            					    }
                            	})
                            }else{
                            	malert(data.body.c, "bottom", "defeadted");
                            }
                        }
                    }, function (error) {
                        yzclLeft.ifClick = true;
                        console.log(error);
                    });
                }


            }
            else if (type == 2) {//取消审核
                this.auditingData = [];
                if (yzclLeft.caqxContent.cs00900100107 == '0') {
                    malert("对不起，您无权取消审核！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return
                }
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    if (yzclRight.rightJsonList[i].rycwbh == null) {
                        malert("病人：" + yzclRight.rightJsonList[i].brxm + ",住院号：" + yzclRight.rightJsonList[i].zyh + "未接科安床，无法完成操作！", "top", "defeadted");
                        return
                    }
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            yzclRight.auditingData.push({"xhid": yzclRight.rightJsonList[i]['yzxx'][j].xhid});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱取消审核！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                common.openBar('#wrapper')
                //console.log("yzsh:"+JSON.stringify({list:this.auditingData}));
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=qxyzsh&zxks=' + yzclRight.zxks,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("取消审核成功", "bottom", "success");
                        yzclLeft.ifClick = true;
                        yzclRight.num = 1
                        yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
                        yzclRight.queryYzxx();//刷新
                        common.closeLoading()
                    } else {
                        //malert("取消审核失败", "top", "defeadted");
                        malert(data.body.c, "bottom", "defeadted");
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                    }
                }, function (error) {
                    yzclLeft.ifClick = true;
                    console.log(error);
                });
            }
            else if (type == 3) {//医嘱执行
                this.title = '异常病人信息备注【已经取消勾选】';
                this.errorText = []
                this.auditingData = [];
                var zxListBak = [];
                var ypyzxd = [];
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            this.auditingData.push({
                                "xhid": yzclRight.rightJsonList[i]['yzxx'][j].xhid,
                                "zxsj": yzclRight.timeXg(),
                                "zyh": yzclRight.rightJsonList[i]['yzxx'][j].zyh
                            });
                            zxListBak.push({"numb": yzclRight.rightJsonList[i]['yzxx'][j].numb});

                        }
                    }
                }

                if (this.auditingData.length <= 0) {
                    malert("无医嘱执行！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                if (zxListBak.length <= 0) {
                    malert("无医嘱执行！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                if (yzclLeft.caqxContent.cs00900100123 == '1') {
                    common.openBar('#wrapper')
                    //console.log("yzsh:"+JSON.stringify({list:this.auditingData}));
					var parm = {
											zxks : yzclRight.zxks
										};
                    this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzx&ksbm=&zxks=' + yzclRight.zxks+ '&parm=' + JSON.stringify(parm),
                        JSON.stringify({list: this.auditingData})).then(function (data) {
                        if (data.body.a == 0) {
                            if (data.body.d && data.body.d.list.length > 0) {
                                for (var i = 0; i < data.body.d.list.length; i++) {
                                    for (var j = 0; j < yzclLeft.brlist.length; j++) {
                                        if (data.body.d.list[i].zyh == yzclLeft.brlist[j].zyh) {
                                            Vue.set(yzclLeft.isChecked, j, false);
                                            //yzclLeft.reCheckBox(['some',j,false,'brlist'])
                                        }
                                    }
                                    yzclRight.errorText.push(data.body.d.list[i].error)
                                }
                                yzclRight.errBr = true
                            }
                            yzclRight.num = 3
                            malert("医嘱执行成功", "bottom", "success");
                            yzclLeft.ifClick = true;
                            yzclRight.queryYzxx();//刷新
                            common.closeLoading()
                        } else {
                            // malert("执行失败", "top", "defeadted");
                            malert(data.body.c, "top", "defeadted");
                            yzclLeft.ifClick = true;
                            common.closeLoading()
                        }
                    }, function (error) {
                        console.log(error);
                    });
                } else {
                    var length = 0;
                    for (var i = 0; i < zxListBak.length; i++) {
                        if (zxListBak[i].numb > 0) {
                            //                                malert("第" + (i + 1) + "条医嘱今日已执行！无法再次执行，请检查后再执行！", "top", "defeadted");
                            this.auditingData.splice(i - length, 1);
                            length = length + 1;
                            //                                return false
                        }
                    }
                    if (this.auditingData.length <= 0) {
                        yzclLeft.ifClick = true;
                        zxListBak = [];
                        malert("不存在今日未执行医嘱！，当前不允许执行多次", 'bottom', 'defeadted');
                        return;
                    }
                    common.openBar('#wrapper')
					//+"&zxyzts="+this.zxyzts
					var parm = {
											zxks : yzclRight.zxks
										};
					this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=yzzx&ksbm=&zxks=' + yzclRight.zxks+ '&parm=' + JSON.stringify(parm),
                        JSON.stringify({list: this.auditingData})).then(function (data) {
                        if (data.body.a == 0) {
                            if (data.body.d && data.body.d.list.length > 0) {
                                for (var i = 0; i < data.body.d.list.length; i++) {
                                    for (var j = 0; j < yzclLeft.brlist.length; j++) {
                                        if (data.body.d.list[i].zyh == yzclLeft.brlist[j].zyh) {
                                            Vue.set(yzclLeft.isChecked, j, false);
                                            // yzclLeft.reCheckBox(['some',j,false,'brlist'])
                                        }
                                    }
                                    yzclRight.errorText.push(data.body.d.list[i].error)
                                }
                                yzclRight.errBr = true
                            }
                            yzclRight.num = 3
                            malert("医嘱执行成功", "bottom", "success");
                            yzclLeft.ifClick = true;
                            yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
                            yzclRight.queryYzxx();//刷新
                            zxListBak = [];
                            common.closeLoading();
                        } else {
                            // malert("执行失败", "top", "defeadted");
                            malert(data.body.c, "bottom", "defeadted");
                            yzclLeft.ifClick = true;
                            common.closeLoading()
                        }
                    }, function (error) {
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                        console.log(error);
                    });
                }
            }
            else if (type == 4) {//取消执行
                this.auditingData = [];
                if (yzclLeft.caqxContent.cs00900100110 == '0') {
                    malert("对不起，您无权取消执行", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return
                }
                var err = "";
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            //临时医嘱是否可取消执行。0-不允许，1-允许
                            if  (!yzclRight.caqxContent.N03004200275 == false && yzclRight.caqxContent.N03004200275 == "0" && yzclRight.rightJsonList[i]['yzxx'][j].yzlx == "0"){
                                err = "没有取消临时医品医嘱权限！";
                                continue;
                            }
                            this.auditingData.push({"zxlsh": yzclRight.rightJsonList[i]['yzxx'][j].ypzxlsh});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱取消执行！"+err, "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                common.openBar('#wrapper')
                //console.log("qxyzzx:"+JSON.stringify({list:this.auditingData}));
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=qxyzzx&ksbm=&zxks=' + yzclRight.zxks,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("取消执行成功", "bottom", "success");
                        yzclLeft.ifClick = true;
                        yzclRight.num = 2
                        yzclRight.queryYzxx();//刷新
                        common.closeLoading()
                    } else {
                        //malert("取消执行失败", "top", "defeadted");
                        malert(data.body.c, "bottom", "defeadted");
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                    }
                }, function (error) {
                    yzclLeft.ifClick = true;
                    console.log(error);
                    common.closeLoading()
                });
            }
            else if (type == 5) {//药品申领
                if (yzclRight.caqxContent.N03004200252) {
                    var N03004200252 = yzclRight.caqxContent.N03004200252, d = new Date(), hours = d.getHours(),
                        minutes = d.getMinutes(),
                        hM = (hours < 10 ? '0' + hours : hours + ':' + (minutes < 10 ? '0' + minutes : minutes));
                    if (hours < 12) {
                        if (hM > N03004200252[0] && hM < N03004200252[1]) {
                            yzclRight.yfpz = '1';
                            yzclRight.popContent.yflx = '0';
                            yzclRight.popContent.yfbm = N03004200252[5];
                            yzclRight.popContent.yfmc = '住院西药房';
                        } else {
                            yzclRight.yfpz = '1';
                            yzclRight.popContent.yflx = '0';
                            yzclRight.popContent.yfbm = N03004200252[4];
                            yzclRight.popContent.yfmc = '门诊西药房';
                        }
                    } else {
                        if (hM > N03004200252[2] && hM < N03004200252[3]) {
                            yzclRight.yfpz = '1';
                            yzclRight.popContent.yflx = '0';
                            yzclRight.popContent.yfbm = N03004200252[5];
                            yzclRight.popContent.yfmc = '住院西药房';
                        } else {
                            yzclRight.yfpz = '1';
                            yzclRight.popContent.yflx = '0';
                            yzclRight.popContent.yfbm = N03004200252[4];
                            yzclRight.popContent.yfmc = '门诊西药房';
                        }
                    }
                }
                this.auditingData = [];
                this.errorText = [];
                $.ajaxSettings.async = true;
                common.openBar('#wrapper')
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            this.auditingData.push(yzclRight.rightJsonList[i]['yzxx'][j]);
                        }
                    }
                }
                if (this.errorText.length > 0) {
                    this.title = '药品申领提示';
                    this.errBr = true;
                }
                var yfbm = "";
                var yflx = "";
                var yfmc = "";
                if (yzclRight.yfpz == '1') {
                    yfbm = yzclRight.popContent.yfbm;
                    yflx = yzclRight.popContent.yflx;
                    yfmc = yzclRight.popContent.yfmc;
                }
				var parm = {
										zxks : yzclRight.zxks
									};
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=ypsl&ksbm=&yflx=' + yflx + '&yfbm=' + yfbm + '&yfmc=' + yfmc + '&zxks=' + yzclRight.zxks + '&type=' + yzclRight.yfpz+ '&parm=' + JSON.stringify(parm),
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        yzclLeft.ifClick = true;
                        if (data.body.d != null && data.body.d != '') {
                            malert(data.body.d, "bottom", "defeadted");
                        } else {
                            malert("药品申领成功", "bottom", "success");
                            if (data.body.c && data.body.c.indexOf("成功") == -1) {
                                yzclRight.title = '药品申领提示';
                                yzclRight.errBr = true;
                                yzclRight.errorText.push(data.body.c)
                                yzclLeft.ifClick = true;
                            }
                        }
                        yzclRight.num = 4
                        yzclRight.queryYzxx();//刷新
                        common.closeLoading()
                    } else {
                        // malert("申领失败", "top", "defeadted");
                        yzclRight.title = '药品申领提示';
                        yzclRight.errBr = true;
                        yzclRight.errorText.push(data.body.c)
                        // malert(data.body.c, "bottom", "defeadted");
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                    }
                }, function (error) {
                    yzclLeft.ifClick = true;
                    console.log(error);
                    common.closeLoading()
                });
            }
            else if (type == 6) {//取消申领
                common.openBar('#wrapper')
                this.auditingData = [];
                if (yzclLeft.caqxContent.cs00900100111 == '0') {
                    malert("对不起，您无权取消申领！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return
                }
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            this.auditingData.push({"ypzxlsh": yzclRight.rightJsonList[i]['yzxx'][j].ypzxlsh});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无药品取消申领！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
					var parm = {
						zxks : yzclRight.zxks
					};
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=qxypsl&ksbm=&zxks=' + yzclRight.zxks+ '&parm=' + JSON.stringify(parm),
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("取消药品申领成功", "bottom", "success");
                        yzclLeft.ifClick = true;
                        yzclRight.num = 3
                        yzclRight.queryYzxx();//刷新
                        common.closeLoading()
                    } else {
                        //malert("取消申领失败", "top", "defeadted");
                        malert(data.body.c, "bottom", "defeadted");
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                    }
                }, function (error) {
                    yzclLeft.ifClick = true;
                    console.log(error);
                    common.closeLoading()
                });
            }
            else if (type == 7) {//停嘱审核
                common.openBar('#wrapper')
                this.auditingData = [];
                var tzshzyh = [];
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    var tzshzyhs = "";
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            // @yqq
                            var obj = {
                                "xhid": yzclRight.rightJsonList[i]['yzxx'][j].xhid,
                                "hstzsj": ''
                            };
                            if (yzclLeft.caqxContent.N03004200247 == '1') {
                                obj.hstzsj = yzclRight.rightJsonList[i]['yzxx'][j].ystzsj;
                            } else {
                                obj.hstzsj = time;
                            }
                            this.auditingData.push(obj);
                            //选中医嘱复制住院号
                            tzshzyhs =  yzclRight.rightJsonList[i].zyh;
                        }
                    }
                    if (!tzshzyhs == false){
                        tzshzyh.push(tzshzyhs);
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无停嘱审核！", "top", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
				var parm = {
										zxks : yzclRight.zxks
									};
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=hstzsh&ksbm=&zxks=' + yzclRight.zxks+ '&parm=' + JSON.stringify(parm),
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("医嘱停嘱审核成功", "bottom", "success");
                        if(tzshzyh>0) {
                            for (var i = 0; i < tzshzyh.length; i++) {
                                var sendmsg = {
                                  msgtype: '9',
                                    ksbm: yzclLeft.jsContent.ksbm,
                                    yljgbm: jgbm,
                                    yqbm: yqbm,
                                    msg: "delete",
                                    toaddress: '',
                                    sbid: tzshzyh[i],
                                    ylbm: 'N030042002',
                                    pagename:"护士站",
                                }
                                window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                            }
                        }
                        yzclLeft.ifClick = true;
                        yzclRight.queryYzxx();//刷新
                        yzclRight.num = 0
                        yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
                        common.closeLoading()
                    } else {
                        //malert("停嘱审核失败", "bottom", "defeadted");
                        malert(data.body.c, "bottom", "defeadted");
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                    }
                }, function (error) {
                    yzclLeft.ifClick = true;
                    console.log(error);
                    common.closeLoading()
                });
            }
            else if (type == 8) { // 取消执行
                this.auditingData = [];
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            this.auditingData.push({"zxlsh": yzclRight.rightJsonList[i]['yzxx'][j].zxlsh});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱取消执行！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                common.openBar('#wrapper')
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=qxyzzx&ksbm=&zxks=' + yzclRight.zxks,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("取消执行成功", "bottom", "success");
                        yzclLeft.ifClick = true;
                        yzclRight.num = 0
                        yzclRight.queryYzxx();//刷新
                        common.closeLoading()
                    } else {
                        malert(data.body.c, "bottom", "defeadted");
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                    }
                }, function (error) {
                    yzclLeft.ifClick = true;
                    console.log(error);
                    common.closeLoading()
                });
            }
            else if (type == 9) {//审核医嘱作废
                                this.auditingData = [];
                var xhids=[];
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            this.auditingData.push({"yzxx": yzclRight.rightJsonList[i]['yzxx'][j]});
                        }
                        if(yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'].indexOf("yp")!='-1'){
                            xhids.push(yzclRight.rightJsonList[i].zyh+'_'+yzclRight.rightJsonList[i]['yzxx'][j]['yzxh']+yzclRight.rightJsonList[i]['yzxx'][j]['fzh'])
                        }else if(yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'].indexOf("yl")!='-1'){
                            xhids.push(yzclRight.rightJsonList[i].zyh+'_'+yzclRight.rightJsonList[i]['yzxx'][j]['yzxh'])
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无医嘱审核作废！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                common.openBar('#wrapper')
                this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=shyzzf&ksbm&zxks=' + yzclRight.zxks,
                    JSON.stringify({list: this.auditingData})).then(function (data) {
                    if (data.body.a == 0) {
                        malert("审核作废成功", "bottom", "success");
                        if(xhids.length>0){
                            for(var i=0;i<xhids.length;i++){
                                var sendmsg = {
                                    msgtype: '9',
                                    ksbm: yzclRight.zxks,
                                    yljgbm: jgbm,
                                    yqbm: yqbm,
                                    msg: "delete",
                                    toaddress: '',
                                    sbid: xhids[i]+'/14',
                                    ylbm: 'N030042002',
                                    pagename:"护士站",
                                }
                                window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                            }
                        }
                        yzclLeft.ifClick = true;
                        yzclRight.num = 0
                        yzclRight.queryYzxx();//刷新
                        common.closeLoading()
                    } else {
                        malert(data.body.c, "bottom", "defeadted");
                        yzclLeft.ifClick = true;
                        common.closeLoading()
                    }
                }, function (error) {
                    yzclLeft.ifClick = true;
                    console.log(error);
                    common.closeLoading()
                });
            } else if (type == 10) {//撤消医嘱审核
                this.auditingData = [];
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
                            this.auditingData.push({"yzxx": yzclRight.rightJsonList[i]['yzxx'][j]});
                        }
                    }
                }
                if (this.auditingData.length <= 0) {
                    malert("无撤消医嘱审核！", "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    return;
                }
                this.yzcxShow=true;
            } else if (type == 11) {//执行医嘱
                let ypzxlshdate = [];
				let zlzxlshdate = [];
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
							if(yzclRight.rightJsonList[i]['yzxx'][j].ypbz !='1'){
								zlzxlshdate.push(yzclRight.rightJsonList[i]['yzxx'][j].zxlsh);
							}else{
								ypzxlshdate.push(yzclRight.rightJsonList[i]['yzxx'][j].zxlsh);
							}
                            
                        }
                    }
                }
				if(ypzxlshdate.length<=0 && zlzxlshdate.length<=0){
					malert("请选择需要执行的医嘱！", "top", "defeadted");
					yzclLeft.ifClick = true;
					return false;
				}
				yzclLeft.getHs();
				yzclRight.yszxTime =this.fDate(new Date());
				
				
				
				yzclRight.yszxsjType = true;
				return false;
            } else if (type == 12) {//执行医嘱
                let ypbh = [];
				let zlbh = [];
                for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
                    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
                        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
							if(yzclRight.rightJsonList[i]['yzxx'][j].ypbz !='1'){
								zlbh.push({
									xhid :yzclRight.rightJsonList[i]['yzxx'][j].xhid,
									zyh : yzclRight.rightJsonList[i]['yzxx'][j].zyh
								});
							}else{
								ypbh.push({
									xhid :yzclRight.rightJsonList[i]['yzxx'][j].xhid,
									zyh : yzclRight.rightJsonList[i]['yzxx'][j].zyh
								});
							}
                            
                        }
                    }
                }
				if(ypbh.length<=0 && zlbh.length<=0){
					malert("请选择需要驳回的医嘱！", "top", "defeadted");
					yzclLeft.ifClick = true;
					return false;
				}
				
				
				
				common.openBar('#wrapper')
				this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=bhyzzf&ksbm=&zxks=' + yzclRight.zxks,
				JSON.stringify({
					ypbh:ypbh,
					zlbh:zlbh,
				})).then(function (data) {
				    if (data.body.a == 0) {
				        malert("驳回作废成功", "bottom", "success");
				        
				        yzclLeft.ifClick = true;
				        yzclRight.num = 0
				        yzclRight.queryYzxx();//刷新
				        common.closeLoading()
				    } else {
				        malert(data.body.c, "bottom", "defeadted");
				        yzclLeft.ifClick = true;
				        common.closeLoading()
				    }
				}, function (error) {
				    yzclLeft.ifClick = true;
				    console.log(error);
				    common.closeLoading()
				});
				
				
            }
        },
		
		saveEndTime : function(){
			let ypzxlshdate = [];
			let zlzxlshdate = [];
			for (var i = 0; i < yzclRight.rightJsonList.length; i++) {
			    for (var j = 0; j < yzclRight.rightJsonList[i]['yzxx'].length; j++) {
			        if (yzclRight.rightJsonList[i]['yzxx'][j].isChecked) {
						if(yzclRight.rightJsonList[i]['yzxx'][j].ypbz !='1'){
							zlzxlshdate.push(yzclRight.rightJsonList[i]['yzxx'][j].zxlsh);
						}else{
							ypzxlshdate.push(yzclRight.rightJsonList[i]['yzxx'][j].zxlsh);
						}
			            
			        }
			    }
			}
			if(ypzxlshdate.length<=0 && zlzxlshdate.length<=0){
				malert("请选择需要执行的医嘱！", "top", "defeadted");
				yzclLeft.ifClick = true;
				return false;
			}
			
			let yzsj = yzclRight.yszxTime;
			if(!yzsj){
				yzsj = getTodayDateTime();
			}
			
			let tpuserName = userName;
			let tpuserId = userId;
			if(yzclRight.rybm){
				tpuserId = yzclRight.rybm;
				tpuserName = yzclRight.ryxm;
			}
			var parm_str = {
			                sjzxsj: yzsj,
			                sjzxhs: tpuserId,
							sjzxhsxm: tpuserName,
							ypzxlsh: ypzxlshdate.join(),
							ylzxlsh: zlzxlshdate.join(),
							ksbm: yzclRight.zxks,
			            };
			if(ypzxlshdate.length>0){
				let tpmethod = 'saveSjYpzx';
				
				
				
				$.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types='+tpmethod+'&parm=' + encodeURI(JSON.stringify(parm_str)),
				    function (json) {
				        if (json.a == '0') {
				            if(zlzxlshdate.length>0){
								tpmethod = 'saveSjZlzx';
								
								$.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types='+tpmethod+'&parm=' + encodeURI(JSON.stringify(parm_str)),
								    function (json) {
								        if (json.a == '0') {
												malert("保存成功", 'top', 'success');
												yzclLeft.ifClick = true;
												yzclRight.yszxsjType = false;
												yzclRight.queryYzxx();
												
								        } else {
								            malert("保存失败", 'top', 'defeadted');
											yzclLeft.ifClick = true;
								        }
								    });
							}else{
								malert("保存成功", 'top', 'success');
								yzclLeft.ifClick = true;
								yzclRight.yszxsjType = false;
								yzclRight.queryYzxx();
								
							}
				        } else {
				            malert("保存失败", 'top', 'defeadted');
							yzclLeft.ifClick = true;
				        }
				    });
			}
			if(zlzxlshdate.length>0){
				let tpmethod = 'saveSjZlzx';
				$.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzzxdCx&types='+tpmethod+'&parm=' + encodeURI(JSON.stringify(parm_str)),
				    function (json) {
				        if (json.a == '0') {
								malert("保存成功", 'top', 'success');
								yzclLeft.ifClick = true;
								yzclRight.yszxsjType = false;
								yzclRight.queryYzxx();
								
								
								
				        } else {
				            malert("保存失败", 'top', 'defeadted');
							yzclLeft.ifClick = true;
				        }
				    });
			}
		},
		
        // yzcxFun:function(){
        //     this.cxSuccess()
        // },
        cxSuccess:function(){ //撤销确认进入这里
            this.yzcxShow=false;
            console.log(this.cxObj) //撤销变量值
            common.openBar('#wrapper')
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszHlywYzclZx&types=hscxsh&ksbm=' + yzclRight.zxks+"&cxshbz="+this.cxObj.cx+"&cxshyj="+this.cxObj.cxyj+'['+this.cx_tran[this.cxObj.cx]+']',
                JSON.stringify({list: this.auditingData})).then(function (data) {
                if (data.body.a == 0) {
                    malert("审核撤消医嘱成功", "bottom", "success");
                    yzclLeft.ifClick = true;
                    yzclRight.num = 0
                    yzclRight.queryYzxx();//刷新
                    common.closeLoading()
                } else {
                    malert(data.body.c, "bottom", "defeadted");
                    yzclLeft.ifClick = true;
                    common.closeLoading()
                }
            }, function (error) {
                yzclLeft.ifClick = true;
                console.log(error);
                common.closeLoading()
            });
        },
        tabPage: function (page, index, openUrl) {
			
            tabBg(page, index, openUrl)
        },
        dzbl: function () {
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + yzclLeft.HszbrItem.brid + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！', 'top', 'success')
                } else {
                    malert('信息上传失败失败：' + data.body.c, 'top', 'defeadted')
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=" + yzclLeft.HszbrItem.zyh + "&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    malert('信息上传成功！', 'top', 'success')
                } else {
                    malert('信息上传失败失败：' + data.body.c, 'top', 'defeadted')
                }
            });
            var sxdz = "";
            // var user = "";
            var password = "";
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    yzclLeft.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                    sxdz = yzclLeft.csContent.blSxdz;
                    // user = userId;
                }
            });
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                if (json.a == "0") {
                    password = json.d.password;
                }
            });
            if (!sxdz) {
                malert("书写地址为空，打开病历失败！", 'top', 'defeadted');
                return
            }
            if (!userId) {
                malert("用户名为空，打开病历失败！！", 'top', 'defeadted');
                return
            }
            if (!password) {
                malert("用户密码为空，打开病历失败！", 'top', 'defeadted');
                return
            }
            if (!yzclLeft.HszbrItem.zyh) {
                malert("请先选择病人后再书写病历！", 'top', 'defeadted');
                return
            }
            var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=" + yzclLeft.HszbrItem.zyh + ",userid=" + userId + ",password=" + password + ",lyzyhmz=0,blhhl=1";
            window.open(url);
        },
        tabBg: function (index) {
			
            this.num = index;
            this.activeBrListIndex=undefined;
            this.activeIndex=undefined;
            this.hoverBrListIndex=undefined;
            this.hoverIndex=undefined;
            this.popContent.yfbm = yzclLeft.caqxContent.cs03003200101
            this.yfpz = '0'
            yzclRight.timeTabArr = yzclRight.getDate() || getTodayDateTime()
            this.$forceUpdate()
        },
        qtflj: function () {
            UserHzxx.index = 1;
            UserHzxx.$nextTick(function () {
                // UserHzxx.xh()
            })
        },
        refresh: function () {
            location.reload()
        },
        queryYzxx: function () {
                        var zyhs = [];
            for (var i = 0; i < yzclLeft.brlist.length; i++) {
                if (yzclLeft.isChecked[i]) {
                    var obj = {};
                    obj.zyh = yzclLeft.brlist[i].zyh;
                    zyhs.push(obj);
                }
            }
            yzclLeft.queryYz(zyhs);
        },
        setCheckData: function (brIndex, yzIndex, fzh, yzStatus, sfcyType) {
            for (var i = 0; i < this.rightJsonList[brIndex].yzxx.length; i++) {
                if (this.rightJsonList[brIndex].yzxx[yzIndex].sfcy == '1' && yzclLeft.caqxContent.cs00900100232 == '1') {
                    if (fzh != 0 || fzh != '') {
                        if (fzh == this.rightJsonList[brIndex].yzxx[i][sfcyType]) {
                            Vue.set(this.rightJsonList[brIndex].yzxx[i], 'isChecked', !this.rightJsonList[brIndex].yzxx[i].isChecked);
							if (yzclRight.num == "1" && !this.rightJsonList[brIndex].yzxx[yzIndex].isChecked && this.rightJsonList[brIndex].yzxx[yzIndex].bzsm){
								malert("您选中的医嘱有备注信息："+this.rightJsonList[brIndex].yzxx[yzIndex].bzsm, 'right', 'defeadted');
							}
							
                        }
                    } else if (fzh == 0 || fzh == '') {
                        this.rightJsonList[brIndex].yzxx[yzIndex].isChecked = yzStatus;
						if (yzclRight.num == "1" && !this.rightJsonList[brIndex].yzxx[yzIndex].isChecked  && this.rightJsonList[brIndex].yzxx[yzIndex].bzsm){
							malert("您选中的医嘱有备注信息："+this.rightJsonList[brIndex].yzxx[yzIndex].bzsm, 'right', 'defeadted');
						}
                        break;
                    }
                } else {
                    if (fzh != 0 || fzh != '') {
                        if (fzh == this.rightJsonList[brIndex].yzxx[i][sfcyType] && this.rightJsonList[brIndex].yzxx[yzIndex]['ksrq'] == this.rightJsonList[brIndex].yzxx[i]['ksrq']) {
                            // Vue.set(this.rightJsonList[brIndex].yzxx[i], 'isChecked', true);
                            Vue.set(this.rightJsonList[brIndex].yzxx[i], 'isChecked', !this.rightJsonList[brIndex].yzxx[i].isChecked);
							if (yzclRight.num == "1" && !this.rightJsonList[brIndex].yzxx[yzIndex].isChecked  && this.rightJsonList[brIndex].yzxx[yzIndex].bzsm){
								malert("您选中的医嘱有备注信息："+this.rightJsonList[brIndex].yzxx[yzIndex].bzsm, 'right', 'defeadted');
							}
                        }
                    } else if (fzh == 0 || fzh == '') {
                        this.rightJsonList[brIndex].yzxx[yzIndex].isChecked = yzStatus;
						if (yzclRight.num == "1" && yzStatus && this.rightJsonList[brIndex].yzxx[yzIndex].bzsm){
							malert("您选中的医嘱有备注信息："+this.rightJsonList[brIndex].yzxx[yzIndex].bzsm, 'right', 'defeadted');
						}
                        break;
                    }
                }
            }
        },
        checkSelectSon: function (brIndex, yzIndex) {
			if(this.rightJsonList[brIndex].yzxx[yzIndex].numb>0&&this.num==2){
				return
			}
			
            if(!yzclLeft.caqxContent.cs00900100123){
                if (this.rightJsonList[brIndex].yzxx[yzIndex].numb >= 1) return
            }
            var sfcyType = '';
            if (this.rightJsonList[brIndex].yzxx[yzIndex].sfcy == '0' || this.rightJsonList[brIndex].yzxx[yzIndex].sfcy == null) {
                sfcyType = 'fzh';
            } else {
                sfcyType = 'yzxh';
            }
            var yzStatus = !this.rightJsonList[brIndex].yzxx[yzIndex].isChecked;
            var fzh = this.rightJsonList[brIndex].yzxx[yzIndex][sfcyType];//临时分组号
            if (!this.rightJsonList[brIndex].yzxx[yzIndex]['isChecked']) {
                this.setCheckData(brIndex, yzIndex, fzh, yzStatus, sfcyType)
            } else {
                if (this.rightJsonList[brIndex].yzxx[yzIndex].sfcy == '0' || this.rightJsonList[brIndex].yzxx[yzIndex].sfcy == null) {
                    this.setCheckData(brIndex, yzIndex, fzh, yzStatus, sfcyType) //同组的要一起取消勾选，否则输液贴打印会出问题
                    // Vue.set(this.rightJsonList[brIndex].yzxx[yzIndex], 'isChecked', false);
                } else {
                    this.setCheckData(brIndex, yzIndex, fzh, yzStatus, sfcyType)
                }
            }
            if (yzStatus) {
                var yzIsOverCk = true;
                for (var x = 0; x < this.rightJsonList[brIndex].yzxx.length; x++) {
                    if (!this.rightJsonList[brIndex].yzxx[x].isChecked) {
                        yzIsOverCk = false;
                        break;
                    }
                }
                this.rightJsonList[brIndex].isCheckAll = yzIsOverCk;
                var isOverCk = true;
                for (var x = 0; x < this.rightJsonList.length; x++) {
                    if (!this.rightJsonList[x].isCheckAll) {
                        isOverCk = false;
                        break;
                    }
                }
                this.isOver = isOverCk;
            } else {
                this.rightJsonList[brIndex].isCheckAll = false;
                this.isOver = false;
            }
            this.$forceUpdate()
        },
        reCheckBoxSon: function () {
            console.log("点击"+yzclRight.num);
            if (arguments.length == 1) {
                var isCheckAll = this.rightJsonList[arguments[0]].isCheckAll ? false : true,
                    yzshInfo = this.rightJsonList[arguments[0]],
                    yzxxList = yzshInfo.yzxx;

                this.rightJsonList[arguments[0]].isCheckAll = isCheckAll;
                for (var i = 0; i < yzxxList.length; i++) {
					if(yzxxList[i].numb>0&&yzclRight.num==2){
						this.rightJsonList[arguments[0]].yzxx[i].isChecked = false;
						continue;
					}
                    if(!yzclLeft.caqxContent.cs00900100123){
                        if (yzxxList[i].numb >= 1) {
                            continue;
                        }
                    }
					if (yzclRight.num == "1" && isCheckAll && this.rightJsonList[arguments[0]].yzxx[i].bzsm){
					       malert("您选中的医嘱有备注信息："+this.rightJsonList[arguments[0]].yzxx[i].bzsm, 'right', 'defeadted');
					    }
					
                    //领查询临时医嘱全选不自动选中
                    if (!(yzclRight.num == "3" && this.rightJsonList[arguments[0]].yzxx[i].yzlx == "0") && isCheckAll)
                    {
                        this.rightJsonList[arguments[0]].yzxx[i].isChecked = isCheckAll;
                    }
					if(!isCheckAll){
						this.rightJsonList[arguments[0]].yzxx[i].isChecked = isCheckAll;
					}
                }
            } else if (arguments.length == 2) {
                this.activeBrListIndex = arguments[0];
                this.activeIndex = arguments[1];
            }

            var isOverCk = true;
            for (var x = 0; x < this.rightJsonList.length; x++) {
                if (!this.rightJsonList[x].isCheckAll) {
                    isOverCk = false;
                    break;
                }
            }
            this.isOver = isOverCk;
            this.$forceUpdate();
            console.log("-------------------------------");
            console.log(this.rightJsonList);
        },
        getYexx: function (zyhs) {
            var parm = {
                yljgbm: jgbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' + JSON.stringify(parm) + "&zyh=" + JSON.stringify(zyhs), function (json) {
                if (json.a == '0') {
                    if (json.d.list && json.d.list.length > 0) {
                        var qsxcList = [];
                        var all = {
                            yexm: '全部',//如果有影响请还原上面代码，注释本行代码
                            yebh: 'all',
                        };
                        qsxcList.push(all);
                        var cr = {
                            yexm: '成人',
                            yebh: '000',
                        };
                        qsxcList.push(cr);
                        for (var i = 0; i < json.d.list.length; i++) {//为了让全部在最上面
                            qsxcList.push(json.d.list[i]);
                        }
                        yzclRight.yeList = qsxcList;//亲属选择
                    }
                }
            });
        },
    },
});
yzclLeft.getKs(function () {
    //	yzclLeft.jsContent.zyType = 'zy';
    yzclLeft.getCsqx();
    yzclLeft.NoticeObj = JSON.parse(sessionStorage.getItem('NoticeObj' + userId));
    if (yzclLeft.NoticeObj != null && yzclLeft.NoticeObj.msgtype != null) {
        yzclLeft.getNotice();
    } else {
        yzclLeft.getHzlb();
    }
});
var UserHzxx = new Vue({
    el: '#UserHzxx',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data: {
        index: 0,
        xhitem: 1,
        bxLbList:[],
        brFbList:[],
        brxxContent: {},
        caqxContent: {},
    },
    mounted: function () {
        this.readyData(false, "brfb", "brFbList");
        this.readyData(false, "bxlb", "bxLbList");
    },
    methods: {
        //下拉框改变之后回调
        resultzcChange: function (val) {
            $.ajaxSettings.async = false;
            console.log(val[1]);
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "fbbm": //费别
                    //先获取费别的值
                    Vue.set(this.brxxContent, "brfb", val[0]);
                    Vue.set(this.brxxContent, "fbbm", val[0]);
                    Vue.set(this.brxxContent, "brfbmc", val[4]);

                    //设置保险类别
                    Vue.set(this.brxxContent, "bxlbbm", this.listGetName(UserHzxx.brFbList, this.brxxContent.brfb, 'fbbm', 'bxlbbm'));
                    if (val[0] == "01") {
                        Vue.set(this.brxxContent, 'bxbr', '0');
                    } else {
                        Vue.set(this.brxxContent, 'bxbr', '1');
                    }
                    break;
                default:
                    break;
            }
        },
        readyData: function (req, types, listName) {
            if (!req) {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types, function (json) {
                    if (json.a == 0) {
                        UserHzxx[listName] = json.d.list;
                    } else
                        malert(types + "查询失败", 'top', 'defeadted');
                });
            } else {
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=" + types + "&json=" + JSON.stringify(req), function (json) {
                    if (json.a == 0)
                        UserHzxx[listName] = json.d.list;
                    else
                        malert(types + "查询失败", 'top', 'defeadted');
                });
            }
        },
        xh: function () {
            this.$nextTick(function () {
                if ($('.xunhuanlist').length > 0) {
                    this.xhitem = parseInt((parseInt(this.$refs.brzcList.clientHeight) - parseInt($('.xunhuanlist').offset().top)) / 30)
                }
            })

        },
        save:function () {
            if(this.brxxContent.dbje == this.brxxContent.dbje1||this.brxxContent.dbr == this.brxxContent.dbr1){
                this.index = 0
                return  false;
            }
            this.$http.post('/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=update',JSON.stringify(this.brxxContent)).then(function (json) {
                if(json.body.a == 0){
                    this.index = 0
                    malert(json.body.c,'top')
                }else {
                    malert(json.body.c,'top','defeadted')
                }
            })
        }
    },
});
var ghys = new Vue({
    el: '#ghys',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data: {
        isShow: false,
        isif: false,
        zgysList: [],
        doctor: null,
        brInfo: {},
        jsonListData: [],
    },
    methods: {
        getYsData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
                if (json.a == "0") {
                    ghys.jsonListData = json.d.list;
                    //                     //0=默认当前病区医生，1=全院医生
                    //                     if (yzclLeft.caqxContent.cs00900100118 == '0') {
                    //                         //过滤医生
                    ghys.zgysList = jsonFilter(ghys.jsonListData, "ksbm", yzclLeft.jsContent.ksbm);
                    //                     } else {
                    //                    	 ghys.zgysList = json.d.list;
                    //                     }
                }
            });
        },


        closes: function () {
            this.isShow = false
        },

        saveData: function () {
            this.brInfo['zyys'] = this.doctor;
            this.brInfo['zyysxm'] = this.listGetName(this.zgysList, this.doctor, 'rybm', 'ryxm');
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=hzgys&parm=" + JSON.stringify(this.brInfo), function (json) {
                if (json.a == "0") {
                    ghys.isShow = false;
                    yzclLeft.getHzlb();
                    malert("更新数据成功");
                }
            });
        },
    },
});
$(window).resize(function () {
    UserHzxx.xh()
});

function changeScroll() {
    var tablebodyy = $('.zui-table-body');
    for (var i = 0; i < tablebodyy.length; i++) {
        if ($(tablebodyy[i]).offset().top > 0 && $(tablebodyy[i]).attr('data-no-change') === undefined) { //如果有data-no-change属性则不计算
            $(tablebodyy[i]).attr('data-no-change');
            var zuiheight = $('body').outerHeight() - $(tablebodyy).eq(i).offset().top - 10;
            if ($(tablebodyy).eq(i).offset().top < $('body').outerHeight()) {
                if ($(tablebodyy).eq(i).parents('.yzclRight').find('.zui-table-tool')) {
                    zuiheight = zuiheight - $(tablebodyy).eq(i).parents('.yzclRight').find('.zui-table-tool').outerHeight() - 10
                }
                $(tablebodyy[i]).css({
                    'height': zuiheight,
                    'overflow-y': 'auto'
                });
            }
        }
    }
    //判断滚动区域横向滚动条是否出现
    var tabBodys = $('.zui-table-view > .zui-table-header');
    for (var x = 0; x < tabBodys.length; x++) {
        var table = $('table', tabBodys[x]);
        if ($(tabBodys[x]).width() < table.width()) {
            $(tabBodys[x]).siblings(".zui-table-fixed").show();
        } else {
            $(tabBodys[x]).siblings(".zui-table-fixed").hide();
        }
    }
}

//接科按床pop
var jkac = new Vue({
    el: '#jkac',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        title: '接科安床',
        ryListData: [],
        cwListData: [],
        zgysList: [],//主管医生列表
        zgys: '02',
        cwList: [],//床位列表
        cw: '01',
        jkacContent: {},
        ifClick: true,//重复点击判断
        Class: true
    },
    methods: {

        //关闭
        closes: function () {
            this.Class = true
            jkac.jkacContent = {};
        },

        //打开
        open: function () {
            this.Class = false;
        },

        //接科安床
        saveJkac: function () {
            if (!this.ifClick) {
                return;
            }//保存提交限制只允许一次
            this.ifClick = false;
            if (jkac.jkacContent.zyh == undefined || jkac.jkacContent.zyh == null || jkac.jkacContent.zyh == "") {
                malert("请先选择需接科的患者!", 'top', "defeadted");
                this.ifClick = true;
                return;
            }

            jkac.jkacContent.ksbm = hzlb.acContent.ksbm;
            jkac.jkacContent.ksmc = hzlb.acContent.ksmc;

            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=jkac',
                JSON.stringify(jkac.jkacContent))
                .then(function (data) {
                    if (data.body.a == "0") {
                        malert("接科安床成功！", 'top', "success");
                        jkac.Class = true
                        jkac.jkacContent = {};
                        $("#jkac.side-form").removeClass('side-form-bg');
                        $("#jkac.side-form").addClass('ng-hide');
                    } else {
                        console.log("error:" + data.body.c);
                        malert(data.body.c, 'top', "defeadted");
                    }
                    this.ifClick = true;
                    hzlb.getHzData();
                    hzlb.getYsCw();
                });
        },
    }
});

//转科按床pop
var zkac = new Vue({
    el: '#zkac',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        title: '转科按床',
        ryListData: [],
        cwListData: [],
        zgysList: [],
        zgys: '02',
        cwList: [],
        cw: '01',
        zkhcContent: {},
        Kslist: [],
        ifClick: true,//重复点击判断
        Class: true,
        sqlist: [],//转科申请列表
        sfsq: false,//是否申请
    },
    methods: {
        //关闭
        closes: function () {
            zkac.Class = true
            zkac.zkhcContent = {};
        },

        //打开
        open: function () {
            zkac.Class = false
        },

        //获取患者是否已申请转科换床
        getSfsq: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=HszByglCwgl&types=queryJzkjl&parm={"zyh":"' + zkac.zkhcContent.zyh + '","zfbz":"0"}',
                function (json) {
                    if (json.a == "0") {
                        zkac.sqList = json.d.list;
                    } else {
                        malert(json.c, 'top', "defeadted");
                    }
                });
        },

        //转科换床
        saveZkhc: function () {
            $.ajaxSettings.async = false;
            if (!this.ifClick) {
                return;
            }//保存提交限制只允许一次
            this.ifClick = false;
            if (zkac.zkhcContent.zyh == undefined || zkac.zkhcContent.zyh == null || zkac.zkhcContent.zyh == "") {
                malert("请先选择需接科的患者!", 'top', "defeadted");
                this.ifClick = true;
                return;
            }
            zkac.getSfsq();
            for (var i = 0; i < zkac.sqList.length; i++) {
                if (zkac.sqList[i].jrbz == '0') {
                    zkac.sfsq = true;
                }
            }
            if (zkac.sfsq) {
                malert("该患者已申请，请不要重复操作！", 'top', "defeadted");
                this.ifClick = true;
                return;
            }
            zkac.zkhcContent.ksmc = hzlb.acContent.ksmc;

            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=zkhc',
                JSON.stringify(zkac.zkhcContent))
                .then(function (data) {
                    if (data.body.a == "0") {
                        malert("转科换床申请成功！", 'top', "defeadted");
                        zkac.Class = true;
                        $("#zkac.side-form").removeClass('side-form-bg');
                        $("#zkac.side-form").addClass('ng-hide');
                        zkac.closes;
                    } else {
                        console.log("error:" + data.body.c);
                        malert(data.body.c, 'top', "defeadted");
                    }
                    this.ifClick = true;
                    hzlb.getHzData();
                    hzlb.getYsCw();
                });
        },
    }
});

//迁床处理
var qccl = new Vue({
    el: '#qccl',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        title: '迁床处理',
        zgysList: [],
        cwList: [],
        cwid: null,
        ifClick: true,//重复点击判断
        Class: true,
        brInfo: {},
        jsonListData: [],
        qcContent: {},
    },
    updated: function () {
        changHeight()
    },
    methods: {
        getCwData: function () {
            //床位查询
            parm = {"ywbz": "1"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=query&parm=" + JSON.stringify(parm),
                function (json) {
                    qccl.cwList = [];
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            qccl.cwList = jsonFilter(json.d.list, "ksbm", yzclLeft.jsContent.ksbm);
                        }
                    } else {
                        malert(json.c, 'top', "defeadted");
                    }
                });
        },
        getYsData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=rybm", function (json) {
                if (json.a == "0") {
                    qccl.jsonListData = json.d.list;
                    //                    //0=默认当前病区医生，1=全院医生
                    //                    if (yzclLeft.caqxContent.cs00900100118 == '0') {
                    //                        //过滤医生
                    qccl.zgysList = jsonFilter(qccl.jsonListData, "ksbm", yzclLeft.jsContent.ksbm);
                    //                    } else {
                    //                   	 ghys.zgysList = json.d.list;
                    //                    }
                }
            });
        },

        //关闭
        closes: function () {
            qccl.Class = true
            qccl.brInfo = {};
            qccl.qcContent = {};
        },

        //打开
        open: function () {
            qccl.Class = false
        },


        //转科换床
        saveQccl: function () {
            if (!this.ifClick) {
                return;
            }//保存提交限制只允许一次
            this.ifClick = false;
            var parm = {
                'zyh': qccl.brInfo.zyh,
                'qccwid': qccl.brInfo.rycwid,
                'qrcwid': qccl.qcContent.cwid,
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1HszByglCwgl&types=Qccl',
                JSON.stringify(parm))
                .then(function (data) {
                    if (data.body.a == "0") {

                        //新加代码，调用护理接口
                        if (window.top.J_tabLeft.obj.hljkurl != null && window.top.J_tabLeft.obj.hljkurl != undefined) {
                            var hljkurl = window.top.J_tabLeft.obj.hljkurl;//参数权限表中维护的护理接口地址
                            var inJson = {
                                yljgbm: jgbm,
                                zyh: qccl.brInfo.zyh
                            }
                            var params = {
                                yljgbm: jgbm,
                                zyh: qccl.brInfo.zyh,
                                types: 'hzxx',
                                method: 'ADT_A02',
                                inJson: JSON.stringify(inJson)
                            }
                            this.postAjax(hljkurl, JSON.stringify(params), function (result) {
                                if (result.code == 0) {
                                    console.log("护理接口调取成功!");
                                } else {
                                    console.log("护理接口调取失败:" + result.msgInfo);
                                    console.log("护理接口调取失败的住院号为:" + params.zyh)
                                }
                            })
                        }

                        qccl.closes();
                        yzclLeft.getHzlb();
                        malert("更新数据成功", "top", "success");
                    } else {
                        malert("申请失败" + json.c, "top", "defeadted");
                    }
                    this.ifClick = true;
                });
        },
    }
});
var qxbqcy = new Vue({
    el: '#qxbqcy',
    mixins: [baseFunc, tableBase, dic_transform, mformat],
    data: {
        title: '取消病区出院',
        ryListData: [],
        cwListData: [],
        zgysList: [],//主管医生列表
        zgys: '02',
        cwList: [],//床位列表
        cw: '01',
        jkacContent: {},
        ifClick: true,//重复点击判断
        Class: true,
        sfCxfc: true,
        them_tran2: {},
        qxbqcyBrContent: {},
        page: {
            page: 1,
            rows: 20,
            total: null
        },
    },
    methods: {
        initCw: function (type, cwbh) {
            var parm = {"ywbz": "1"};
            var dg = {
                rows: 20000,
                page: 1
            };
            common.openloading();
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=query&parm=" + JSON.stringify(parm) + "&dg=" + JSON.stringify(dg),
                function (json) {
                    qxbqcy.cwList = [];
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            qxbqcy.cwListData = json.d.list;
                            qxbqcy.cwList = jsonFilter(qxbqcy.cwListData, "ksbm", yzclLeft.jsContent.ksbm);
                            if (type == 'qxbqcy') {
                                for (var i = 0; i < qxbqcy.cwList.length; i++) {
                                    if (qxbqcy.cwList[i].cwbh == cwbh) {//若果空床位中有该需要取消病区出院的病人的床位
                                        yzclRight.qxbqcyOperation();
                                        return false;
                                    }
                                }
                                qxbqcy.showCxfc();
                                common.closeLoading();
                                return false;
                            }
                        }
                        common.closeLoading();
                    } else {
                        malert(json.c, 'top', "defeadted");
                        common.closeLoading();
                    }
                });
        },
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "cwid":
                    qxbqcy.jkacContent.cwid = val[0];
                    qxbqcy.jkacContent.cwbh = val[4];
                    this.$forceUpdate();
                    break;
            }
        },
        showCxfc: function () {
            this.Class = false;
        },
        closes: function () {
            this.Class = true;
            qxbqcy.cwList = [];
            qxbqcy.jkacContent = {};
        },
        //由于原床位被占用，需要重新分配床位，避免一个床位两个人
        saveJkac: function () {
            if (!qxbqcy.jkacContent.cwid) {
                malert("请选择床位号！", "top", "defeadted");
                return;
            }
            yzclRight.qxbqcyOperation();
        },
    }
});
