@CHARSET "UTF-8";

.twd-title{
    font-size:24px;
    font-weight:bold;
    text-align: center;
    width: 676px;
    padding: 10px 0;
}
.printShow{
		font-family: '宋体';
	}
canvas{
    cursor: default;
}

table{
    border-collapse: collapse;
    /*overflow: hidden;*/
    font-size: 12px;
    border: 0;
}

table tr td:first-child{
    padding-left: 4px;
    border-left: 0;
}

td{
    height: 14px;
    margin: 0;
    padding: 0;
    border-left: 1px solid #555;
    border-bottom: 1px solid #555;
}

#table_1 tr td:first-child{
    width: 84px;
    border-right: 2px solid #888;
}

#table_1 td{
    width: 84px;
}

#table_2{
    margin-top: -5px;
        width: 100%;
}

#table_2 tr:first-child{
    border-top: 1px solid #888;
}

#table_2 td{
    height: 28px;
    text-align: center;
    min-width: 14px;
    max-width: 14px;
    white-space:nowrap;
}

#table_2 tr td:first-child{
    width: 84px;
    border-right: 2px solid #888;
    min-width: 84px;
    max-width: 84px;
    padding: 0;
    margin: 0;
}

#table_3{
    width: 100%;
}
#table_3 tr td:first-child{
    border-right: 2px solid #888;
	    min-width: 84px;
	    max-width: 84px;
}

#table_3 td{
    height: 19px;
    width: 42px;
    max-width: 42px;
}

#table_4 td{
    height: 19px;
    width: 84px;
}

#table_4 tr td:first-child{
    width: 84px;;
    border-right: 2px solid #888;
}

.td_top{
    vertical-align: top;
    color: #0D09FF;
}

.td_bottom{
    vertical-align: bottom;
    color: #0D09FF;
}
.st_border{
    width: 50%;
    border-left: 1px solid #888;
    height: 100%;
}
.st_border_bottom{
    border-bottom: 1px solid #888;
}
@media print {
    .enter_tem1{
        border: 0;
    }
    #twd {
        width: 760px;
    }
}
.personInfo {
    width: 676px;
    height: 30px;
    display: flex;
    justify-content: space-between;
}
.personInfo p{
    margin: 0 5px 0 0;
    height: 22px;
}
.height100{
    height: 100%;
}
.width-ls{
	letter-spacing: -1.5px;
	text-align: center !important;
}
