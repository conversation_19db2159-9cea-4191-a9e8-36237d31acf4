<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出库开单</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ckgl.css" rel="stylesheet"/>
</head>
<body class="skin-default">
<div class="background-box">
    <div class="wrapper" v-cloak id="wrapper">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" :disabled="(queryObj.sh || queryObj.dy)" @click="AddMdel()">添加设备</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getMx()">刷新</button>
<!--                 <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button> -->
            </div>
            <div class="tong-search tong-padded">
                <div class="jbxx">
                    <div class="jbxx-size">
                        <div class="jbxx-position">
                            <span class="jbxx-top"></span>
                            <span class="jbxx-text">基本信息</span>
                            <span class="jbxx-bottom"></span>
                        </div>
                        <div class="zui-form padd-l24 padd-t-20">
                            <div class="zui-inline">
                                <label class="zui-form-label">库房</label>
                                <div class="zui-input-inline wh122 margin-f-l25">
                                    <select-input :disable="(queryObj.sh || queryObj.dy)" @change-data="resultChange"
                                                  :not_empty="false" :child="kfList"
                                                  :index="'sbkfmc'" :index_val="'sbkfbm'"
                                                  :val="popContent.kfbm" :search="true" :name="'popContent.kfbm'"
                                                  :index_mc="'kfmc'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="zui-inline">
                                <label class="zui-form-label">领用人员</label>
                                <div class="zui-input-inline wh122 margin-l-5">
                                    <select-input :disable="(queryObj.sh || queryObj.dy)" @change-data="resultChange"
                                                  :not_empty="false" :child="ryList"
                                                  :index="'ryxm'" :index_val="'rybm'"
                                                  :val="popContent.rybm" :search="true" :name="'popContent.rybm'"
                                                  :index_mc="'rymc'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="zui-inline">
                                <label class="zui-form-label">领用科室</label>
                                <div class="zui-input-inline wh122 margin-l-5">
                                    <select-input :disable="(queryObj.sh || queryObj.dy)" @change-data="resultChange"
                                                  :not_empty="false" :child="ksList"
                                                  :index="'ksmc'" :index_val="'ksbm'"
                                                  :val="popContent.ksbm" :search="true" :name="'popContent.ksbm'"
                                                  :index_mc="'ksmc'">
                                    </select-input>
                                </div>
                            </div>
                            <div class="zui-inline" style="width:30%;">
                                <label class="zui-form-label">备注</label>
                                <div class="zui-input-inline margin-f-l25">
                                    <input class="zui-input" :disabled="(queryObj.sh || queryObj.dy)" placeholder="请输入备注" type="text" v-model="popContent.bzms" />
                                </div>
                            </div>
                        </div>
                        <div class="rkgl-kd">
                            <span>开单日期:<i class="color-yzf">{{queryObj.sh ? fDate(popContent.zdrq,'AllDate') : fDate(new Date(),'AllDate')}}</i></span>
                            <span>开单人：<i class="color-dsh">{{popContent.zdrmc}}</i></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view">
            <div class="zui-table-header ">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'mxList'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                        </th>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl text-left"><span>设备名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备机身号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备进价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备现价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备产地</span></div></th>
                        <!-- <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th> -->
                        <th v-if="!queryObj.ckd" class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr v-for="(item,$index) in mxList"   @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','mxList'],$event)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
 								<!--复选框,绑数据放开-->
                                <input-checkbox @result="reCheckBox" :list="'mxList'"
                                :type="'some'" :which="$index"
                                :val="isChecked[$index]">
                               </input-checkbox>
                            </div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.sbmc">设备名称</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jsh">设备机身号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.sbgg">设备规格</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cksl">出库数量</div>
                        </td>

                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jj">设备进价</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.xj">设备现价</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.scph">设备批号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">有效期至</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cd">设备产地</div>
                        </td>
                        <!-- <td>
                            <div class="zui-table-cell cell-s" v-text="item.kfdwmc">库房单位</div>
                        </td> -->
                        <td  class="cell-s" v-if="!queryObj.ckd" >
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>

                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>

                            </span>
                            </div>
                        </td>


                        <!--暂无数据提示信息,绑数据放开-->
                        <!--<p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>-->
                    </tr>
                    </tbody>
                </table>
            </div>
        <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m">
                                <span>
                                <input-checkbox @result="reCheckBox" :list="'mxList'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                                </span>
                            </div>
                            </th>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in mxList" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','mxList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <!--复选框,绑数据放开-->
                                    <input-checkbox @result="reCheckBox" :list="'mxList'"
                                    :type="'some'" :which="$index"
                                    :val="isChecked[$index]">
                                    </input-checkbox>
                            </div>
                            </td>
                            <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r" v-if="!queryObj.ckd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in mxList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','mxList'],$event)">
                            <td  class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"  @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
          	                 	</span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="action-bar flex-container flex-jus-e fixed rkgl-position">
                <button class="tong-btn btn-parmary-d9 xmzb-db margin-r-10" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="!queryObj.sh && !queryObj.dy">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="sh()" v-show="queryObj.sh">审核</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="dy()" v-show="queryObj.dy">打印</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="tmdy()" v-show="queryObj.dy">条码打印</button>
            </div>
        </div>
    </div>
    <div class="side-form  pop-548" :class="{'ng-hide':num==0}"  v-cloak  id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--编辑药品-->
        <div class="ksys-side">
            <ul class="tab-edit-list tab-edit2-list">

                <li>
                        <i>设备名称</i>
                        <input class="zui-input" :value="popContent.sbmc" @keydown="changeDown($event,'sbmc')"
                           @input="change($event, 'sbmc', $event.target.value)" id="sbmc"/>
	                    <search-table :message="searchCon" :selected="selSearch" :current="dg.page" :rows="dg.rows" :total="total"
	                                  :them="them" :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne">
	                    </search-table>
                </li>
                <li>
                        <i>设备规格</i>
                        <input type="text" v-model="popContent.sbgg" class="label-input background-h" disabled="disabled"/>
                </li>
                <li>
                    <i>机身号</i>
                    <input type="text" v-model="popContent.jsh" class="label-input background-h" disabled="disabled"/>
                </li>
                <li>
                     <i>出库数量</i>
                     <input type="text" v-model="popContent.cksl" class="label-input "  disabled="disabled">
                </li>
                <li>
                    <i>库存数量</i>
                    <input type="number" v-model="popContent.kcsl" class="label-input" disabled="disabled"/>
                </li>
                <li>
                        <i>生产批号</i>
                        <input type="text" class="label-input " v-model="popContent.scph" disabled>
                </li>
                <li class="position">
                    <i>设备进价</i>
                    <input type="text" v-model="popContent.jj" class="label-input background-h" disabled >
                    <em class="cm">元</em>
                </li>
                <li class="position">
                    <i>设备现价</i>
                    <input type="text" v-model="popContent.xj" class="label-input background-h" disabled >
                    <em class="cm">元</em>
                </li>
                <li>
                        <i>有效期至</i>
                        <input type="text" id="yxqz" class="label-input text-indent30 yxtimes" v-model="fDate(popContent.yxqz,'date')" @keydown="nextFocus($event)" disabled>
                </li>
                <li>
                        <i>设备产地</i>
                        <input type="text" v-model="popContent.cd" class="label-input background-h"  disabled>
                </li>
                <li>
               			<i>供货单位</i>
	                    <select-input @change-data="resultChange" @keydown="nextFocus($event)" :child="ghgysList"
	                                  :index="'gysmc'" :index_val="'gysbm'" :val="popContent.gysbm" :search="true"
	                                  :name="'popContent.gysbm'" disabled>
	                    </select-input>
                </li>
                <li>
                        <i>注册编号</i>
                        <input type="text" class="label-input" v-model="popContent.zcbh" disabled>
                </li>
                <li>
                        <i>注册证号</i>
                        <input type="text" class="label-input"  v-model="popContent.zczh" disabled>
                </li>
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
            <button class="tong-btn btn-parmary xmzb-db" @click="preview()" >条码机</button>

        </div>
    </div>


</div>
<!--侧边窗口-->

<script src="ckkd.js"></script>

</body>

</html>
