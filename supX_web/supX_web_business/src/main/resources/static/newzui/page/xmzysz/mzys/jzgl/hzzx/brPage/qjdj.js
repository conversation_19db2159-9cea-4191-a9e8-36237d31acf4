var qjdj = new Vue({
    el: '#qjdj',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popContent: {
            sqks: '00000',
            sqksmc: '妇产科',
            type: '1'
        },
        userInfo:{},
        lgdjxx: {},
        jbthem: {'疾病编码': 'jbmb', '疾病名称': 'jbmc', '拼音代码': 'pydm'},
        zythem: {'中医疾病编码': 'jbbm', '中医疾病名称': 'jbmc', '拼音代码': 'pydm'},
        ghKsList: [],
        qjksList:[{
            "ksbm":"1113",
            "ksmc":"急诊科(抢救)",
        }],
        jbsearchCon: [],
        zysearchCon: [],
        allBmContent: {},
        jbContent: {},
        selSearch: -1,
        page: {page: 1, rows: 10, total: null},
        is_csqx: {},
    },
    components: {
        'jbsearch-table': searchTable,
        'zysearch-table': searchTable,
        'msearch-table': searchTable,
        'blzdsearch-table': searchTable,
        'byxsearch-table': searchTable
    },
    created: function () {

    },
    mounted: function () {
        laydate.render({
            elem: '#djsj'
            , trigger: 'click'
            , theme: '#1ab394'
            , format: 'yyyy-MM-dd HH:mm:ss'
            , done: function (value, data) {
                qjdj.popContent.djrq = value
            }
        });
        this.getCsqx();
        this.getUserInfo();
        this.getKs();
        this.queryQjdj();
    },
    methods: {
        // 获取操作员用户信息
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    this.userInfo = json.body.d;
                });
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            parm = {
                "ylbm": 'N030032001',
                "ksbm": userNameBg.qxks
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03003200150": //门诊抢救科室配置
                                    if (csjson.csz) {
                                        qjdj.is_csqx.N03003200150 = csjson.csz;
                                    }
                                    break;

                            }
                        }

                    }
                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });
        },
        doCheck: function (type) {
            this.popContent[type] = !parseInt(this.popContent[type]) ? 1 : 0
            this.$forceUpdate()
        },
        change: function (add, type, val) {
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.allBmContent[type] = val;
            if (this.allBmContent[type] == undefined || this.allBmContent[type] == null) {
                this.page.parm = "";
            } else {
                this.page.parm = this.allBmContent[type];
            }
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
            //西医
            if (type.indexOf("xy") >= 0) {
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm'
                    + '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                qjdj.jbsearchCon.push(data.d.list[i]);
                            }
                        } else {
                            qjdj.jbsearchCon = data.d.list;
                        }
                        console.log(qjdj.jbsearchCon);
                        qjdj.page.total = data.d.total;
                        qjdj.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        }
                    });
            }
            //中医
            else if (type.indexOf("zy") >= 0) {
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbzy'
                    + '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                qjdj.zysearchCon.push(data.d.list[i]);
                            }
                        } else {
                            qjdj.zysearchCon = data.d.list;
                        }
                        qjdj.page.total = data.d.total;
                        qjdj.selSearch = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        }
                    });
            }
        },
        //鼠标双击（西医）
        jbselectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.change(true, allBm);
            } else {
                var str = item.jbmb;
                //B95、B96、B97、R65.-、Z37、Z38不能作主要诊断编码
                if (bm == "cyzyzdbm" || bm == "cyzyzdbmBz") {
                    if (str.indexOf('B95') >= 0 || str.indexOf('B96') > 0 || str.indexOf('B97') > 0 || str.indexOf('R65.') > 0 || str.indexOf('Z37') > 0 || str.indexOf('Z38') > 0) {
                        malert("B95、B96、B97、R65.-、Z37、Z38不能作主要诊断编码", 'top', 'defeadted');
                        return;
                    }
                    //烧烫伤面积编码（T31-T32）不能作为主要诊断
                    if (str >= 'T31' && str <= 'T32') {
                        malert("烧烫伤面积编码（T31-T32）不能作为主要诊断", 'top', 'defeadted');
                        return;
                    }
                }
                if (bm.indexOf('cyqtzdbm') >= 0) {
                    //主要诊断为O80时，附加诊断编码只能为Z37或Z38
                    if (this.popContent['cyzyzdbm'].indexOf('o80') >= 0) {
                        if (str.indexOf('Z37') < 0 && str.indexOf('Z38') < 0) {
                            malert("主要诊断为O80时，附加诊断编码只能为Z37或Z38", 'top', 'defeadted');
                            return;
                        }
                    }
                    //当主要编码为O82时，附加诊断编码只能为Z37
                    if (this.popContent['cyzyzdbm'].indexOf('o82') >= 0) {
                        if (str.indexOf('Z37') < 0) {
                            malert("主要诊断为O82时，附加诊断编码只能为Z37", 'top', 'defeadted');
                            return;
                        }
                    }
                }
                //当诊断编码有S00-T98时，损伤的外部原因编码不能为空
                if (str >= "S00" && str <= "T98") {
                    this.pdSsyyIfNull = this.pdSsyyIfNull + 1;
                }

                this.jbContent = item;
                this.popContent[bm] = this.jbContent['jbmb'];
                Vue.set(this.popContent, allBm, this.jbContent['jbmb']);
                Vue.set(this.popContent, mc, this.jbContent['jbmc']);
                this.selSearch = -1
                $(".selectGroup").hide();
                this.$forceUpdate()
            }
        },
        //检索
        changeDown: function (event, type, content, searchCon, modelBm, showBm, modelMc) {
            //全局变量
            bm = modelBm;
            allBm = showBm;
            mc = modelMc;
            // this.nextFocus(event);
            if (this[searchCon][this.selSearch] == undefined) return;
            // this.keyCodeFunction(event,content,searchCon);
            this.inputUpDown(event, searchCon, 'selSearch')
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                this[content] = this[searchCon][this.selSearch]
                if (type.indexOf("xy") >= 0) {
                    var str = qjdj.jbContent['jbmb'];
                    //B95、B96、B97、R65.-、Z37、Z38不能作主要诊断编码
                    if (type == "cyzyzdbm") {
                        if (str.indexOf('B95') >= 0 || str.indexOf('B96') > 0 || str.indexOf('B97') > 0 || str.indexOf('R65.') > 0 || str.indexOf('Z37') > 0 || str.indexOf('Z38') > 0) {
                            malert("B95、B96、B97、R65.-、Z37、Z38不能作主要诊断编码", 'top', 'defeadted');
                            return;
                        }
                        //烧烫伤面积编码（T31-T32）不能作为主要诊断
                        if (str >= 'T31' && str <= 'T32') {
                            malert("烧烫伤面积编码（T31-T32）不能作为主要诊断", 'top', 'defeadted');
                            return;
                        }
                    }
                    if (bm.indexOf('cyqtzdbm') >= 0) {
                        //主要诊断为O80时，附加诊断编码只能为Z37或Z38
                        if (qjdj.popContent['cyzyzdbm'].indexOf('o80') >= 0) {
                            if (str.indexOf('Z37') < 0 && str.indexOf('Z38') < 0) {
                                malert("主要诊断为O80时，附加诊断编码只能为Z37或Z38", 'top', 'defeadted');
                                return;
                            }
                        }
                        //当主要编码为O82时，附加诊断编码只能为Z37
                        if (qjdj.popContent['cyzyzdbm'].indexOf('o82') >= 0) {
                            if (str.indexOf('Z37') < 0) {
                                malert("主要诊断为O82时，附加诊断编码只能为Z37", 'top', 'defeadted');
                                return;
                            }
                        }
                    }
                    //当诊断编码有S00-T98时，损伤的外部原因编码不能为空
                    if (str >= "S00" && str <= "T98") {
                        qjdj.pdSsyyIfNull = qjdj.pdSsyyIfNull + 1;
                    }
                    Vue.set(this.popContent, showBm, this.jbContent['jbmb']);
                    Vue.set(this.popContent, modelMc, this.jbContent['jbmc']);
                }
                //*************************************疾病（中医）
                if (type.indexOf("zy") >= 0) {
                    var str = qjdj.zyContent['jbbm'];
                    //当诊断编码有S00-T98时，损伤的外部原因编码不能为空
                    if (str >= "S00" && str <= "T98") {
                        qjdj.pdSsyyIfNull = qjdj.pdSsyyIfNull + 1;
                    }
                    Vue.set(this.popContent, showBm, this.zyContent['jbbm']);
                    Vue.set(this.popContent, modelMc, this.zyContent['jbmc']);
                }
                this.selSearch = -1
                $(".selectGroup").hide();
                this.nextFocus(event);
                this.$forceUpdate()
            }
        },
        //鼠标双击（中医）
        zyselectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.change(true, allBm);
            } else {
                var str = item.jbmb;
                //当诊断编码有S00-T98时，损伤的外部原因编码不能为空
                if (str >= "S00" && str <= "T98") {
                    qjdj.pdSsyyIfNull = qjdj.pdSsyyIfNull + 1;
                }
                this.zyContent = item;
                this.popContent[bm] = this.zyContent['jbbm'];
                Vue.set(this.allBmContent, allBm, this.zyContent['jbbm']);
                Vue.set(this.popContent, mc, this.zyContent['jbmc']);
                this.selSearch = -1
                $(".selectGroup").hide();
                this.$forceUpdate()
            }
        },

        //查询登记
        queryQjdj: function () {
            var parm = {
                'ghxh': userNameBg.Brxx_List.ghxh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MjzhlLgdj&types=queryLgdjList&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        qjdj.popContent = json.d.list[0];
                        qjdj.lgdjxx = qjdj.popContent;
                    }
                } else {
                    malert("查询失败", 'top', 'defeadted');
                }
            });
        },

        //取消登记
        qxdj: function () {
            this.$http.post('http://localhost:6001/cancelQjdj', JSON.stringify(Object.assign(userNameBg.Brxx_List, qjdj.popContent))).then(function (res) {
                if (res.body.success) {
                    qjdj.popContent = {};
                    malert('取消登记成功!', 'top', 'success');
                } else {
                    malert("取消登记失败:" + res.body.message, "top", "defeadted");
                }
            }, function (error) {
                console.log(error);
            });
        },

        //保存登记
        saveData: function () {
            this.popContent.sqks = userNameBg.Brxx_List.ghks
            this.popContent.sqksmc = userNameBg.Brxx_List.ghksmc
            this.popContent.sqys = userNameBg.Brxx_List.jzys
            this.popContent.sqysxm = userNameBg.Brxx_List.jzysxm
            if (!this.popContent.sqys){
                this.popContent.sqys=this.userInfo.czybm
            }
            if (!this.popContent.sqysxm){
                this.popContent.sqysxm=this.userInfo.czyxm
            }
            this.$http.post('http://localhost:6001/addQjdj', JSON.stringify(Object.assign(userNameBg.Brxx_List, qjdj.popContent))).then(function (res) {
                if (res.body.success) {
                    qjdj.queryQjdj();
                    malert('保存登记成功!', 'top', 'success');
                } else {
                    malert("保存登记失败:" + res.body.message, "top", "defeadted");
                }
            }, function (error) {
                console.log(error);
            });
        },
        getKs: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify({"zyks": "1"}), function (json) {
                if (json.a == 0) {
                    qjdj.ghKsList = [];
                    //qjdj.ghKsList = json.d.list;
                    // @yqq 处理显示指定科室。
                    // if(qjdj.is_csqx.N03003200150){
                    //     let lgks = qjdj.is_csqx.N03003200150.split(',')
                    // 	for(var i = 0;i < json.d.list.length;i++){
                    //         for (let j = 0; j < lgks.length; j++) {
                    //             if(lgks[j] == json.d.list[i].ksbm){
                    //                 qjdj.ghKsList.push(json.d.list[i])
                    //             }
                    //         }
                    //
                    // 	}
                    // }else{
                    // 	qjdj.ghKsList = json.d.list;
                    // }
                    for (var i = 0; i < json.d.list.length; i++) {
                        if (json.d.list[i].ksbm=='0922') {
                            qjdj.ghKsList.push(json.d.list[i])
                            popContent.lgks='0922';
                        }
                    }
                } else {
                    malert("查询失败", 'top', 'defeadted');
                }
            });
        },
    },
})
