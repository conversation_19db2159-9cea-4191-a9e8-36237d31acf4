var zd = new Vue({
	el: '#zd',
	mixins: [dic_transform, tableBase],
	data: {
		jsonList: [],
		dg: {
			page: 1,
			rows: 500,
			sort: "",
			order: "asc",
		},
	},

	methods: {
		getData: function() {
			//准备参数
			var json = {
				'tybz': 0,
			}
			$.getJSON("/actionDispatcher.do?reqUrl=WzkfXtwhwzzd&types=query&json=" +
				JSON.stringify(json) + "&dg=" + JSON.stringify(this.dg),
				function(data) {
					if(data.a == 0) {
						zd.totlePage = Math.ceil(data.d.total / zd.param.rows);
						zd.jsonList = data.d.list;
					} else {

					}

				});
		},
		addData: function() {
			//设置操作参数
			zdPop.op = 'save';
			zdPop.fzblNot = false;
			zdPop.popContent = {};
			zdPop.isShow = true;
		},
		edit: function(num) {
			//设置操作参数
			zdPop.fzblNot = true;
			zdPop.op = 'update';
			if(num == null) {
				for(var i = 0; i < this.isChecked.length; i++) {
					if(this.isChecked[i] == true) {
						num = i;
						break;
					}
				}
				if(num == null) {
					malert("请选中你要修改的数据");
					return false;
				}
			}
			zdPop.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
			var temp = JSON.parse(JSON.stringify(this.jsonList[num]));
			zdPop.xgContent = temp;
			zdPop.isShow = true;
		},
		remove: function() {
			var zdList = [];
			for(var i = 0; i < this.isChecked.length; i++) {
				if(this.isChecked[i] == true) {
					zdList.push(this.jsonList[i]);
				}
			}
			if(zdList.length == 0) {
				malert("请选中您要删除的数据");
				return false;
			}
			if(!confirm("请确认是否删除")) {
				return false;
			}

			var json = {
				'list': zdList
			}
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhwzzd&types=delete', JSON.stringify(json)).then(function(data) {
				if(data.body.a == 0) {
					malert("数据删除成功")
				} else {
					malert("数据删除失败");
				}
				zd.getData();
			}, function(error) {
				console.log(error);
			});
		},
	}
});
zd.getData();

//弹出层
var zdPop = new Vue({
	el: '#zdPop',
	mixins: [dic_transform, baseFunc],
	data: {
		//分页信息
		dg: {
			page: 1,
			rows: 500,
			sort: "",
			order: "asc",
		},
		//物资类别列表
		lbList: [],
		//单位列表
		dwList: [],
		//分装比例是否可修改
		fzblNot: false,
		isShow: false,
		//修改记录
		xgContent: {},
		popContent: {
			//			'bzms': 'test',
			//			'cd': 'ChengDu',
			//			'jj': 15,
			//			'dj': 18,
			//			'fzbl': 3,
			//			'wzgg': '测试规格',
			//			'tybz': '0',

		},
		isKeyDown: null,
		title: '物资字典',
		op: 'save'
	},
	//启动加载
	mounted: function() {
		//准备参数
		var json = {
			'tybz': 0,
		}
		//加载物资类别
		$.getJSON("/actionDispatcher.do?reqUrl=WzkfXtwhWzlb&types=query&json=" +
			JSON.stringify(json) + "&dg=" + JSON.stringify(this.dg),
			function(data) {
				if(data.a == 0) {
					zdPop.lbList = data.d.list;
				} else {
					alert(data.c);
				}
			});
		//加载物资单位
		$.getJSON("/actionDispatcher.do?reqUrl=WzkfXtwhWzdw&types=query&json=" +
			JSON.stringify(json) + "&dg=" + JSON.stringify(this.dg),
			function(data) {
				if(data.a == 0) {
					zdPop.dwList = data.d.list;
				} else {
					alert(data.c);
				}

			});
	},

	methods: {
		saveData: function() {
			//物资名称
			if(this.popContent['wzmc'] == null || this.popContent['wzmc'] == undefined) {
				malert("物资名称不能为空");
				return;
			};
			//物资类别 wz_wzlb
			if(this.popContent['wzlb'] == null || this.popContent['wzlb'] == undefined) {
				malert("物资类别不能为空");
				return;
			};
			//进价
			if(this.popContent['jj'] == null || this.popContent['jj'] == undefined) {
				malert("进价不能为空");
				return;
			};
			//单价
			if(this.popContent['dj'] == null || this.popContent['dj'] == undefined) {
				malert("单价不能为空");
				return;
			};			
			//分装比例
			if(this.popContent['fzbl'] == null || this.popContent['fzbl'] == undefined) {
				malert("分装比例不能为空");
				return;
			};
			//停用标导
			if(this.popContent['tybz'] == null || this.popContent['tybz'] == undefined) {
				malert("停用标志不能为空");
				return;
			};
			
			//保存修改记录
			for(item in this.popContent) {
				if(this.popContent[item] != this.xgContent[item]) {
					this.popContent.xglr = this.xgContent[item] + '=>' + this.popContent[item];
				}
			}

			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhwzzd&types=' + this.op, JSON.stringify(this.popContent)).then(function(data) {
				if(data.body.a == 0) {
					zd.getData();
					zdPop.isShow = false;
					zdPop.isAdd = false;
					malert("数据保存成功")
				} else {
					malert("数据提交失败");
				}
			}, function(error) {
				console.log(error);
			});
		}
	}
});

//验证是否为空
$('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
	if($(this).val() == '' || $(this).val() == null) {
		$(this).addClass("emptyError");
	} else {
		$(this).removeClass("emptyError");
	}
});

//为table循环添加拖拉的div
var drawWidthNum = $(".zd tr").eq(0).find("th").length;
for(var i = 0; i < drawWidthNum; i++) {
	if(i >= 2) {
		$(".zd th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
	}
}