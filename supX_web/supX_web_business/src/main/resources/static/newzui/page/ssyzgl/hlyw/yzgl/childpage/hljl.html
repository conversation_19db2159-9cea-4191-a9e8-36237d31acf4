<link rel="stylesheet" type="text/css" href="childpage/hljl.css">
<link rel="stylesheet" href="childpage/jcjy.css">
<div class="toolMenu padd-b-10">
	<button @click="addData" class="zui-btn btn-primary fa fa-plus">新增</button>
	<button @click="getTwData" class="zui-btn btn-primary-b  ">获取体温数据</button>
	<button @click="getJyData" class="zui-btn btn-primary-b  ">引入检验结果</button>
	<button @click="mrxj()" class="zui-btn btn-primary-b  ">每日小结</button>
	<button :disabled="save" @click="edit()" class="zui-btn btn-primary-b fa fa-edit">保存</button>
	<button :disabled="save" @click="edit(1)" class="zui-btn btn-primary fa fa-plus">修改诊断</button>
	<button @click="hlPrint()" class="zui-btn btn-primary-b fa fa-plus">打印</button>
	<button @click="openCpt()" class="zui-btn btn-primary-b fa fa-plus">预览</button>
	<button @click="getData" class="zui-btn btn-primary-b fa fa-refresh">刷新</button>
	<button @click="remove" class="zui-btn btn-parmary-d2 fa fa-trash-o">删除</button>
	<div class=" padd-l-12 left line-height-12">
		科别：<span class="font-14-654 padd-r-18" v-text="jlxqContent.ryksmc"></span>
		床号：<span class="font-14-654 padd-r-18" v-text="jlxqContent.rycwbh"></span>
		姓名：<span class="font-14-654 padd-r-18" v-text="jlxqContent.brxm"></span>
		年龄：<span class="font-14-654 padd-r-18">{{
		jlxqContent.nl?(jlxqContent.nl + nldw_tran[jlxqContent.nldw]):'' + jlxqContent.nl2?(jlxqContent.nl2 + nldw_tran[jlxqContent.nldw2]):''
		}}</span>
		性别：<span class="font-14-654 padd-r-18"
			v-text="brxb_tran[jlxqContent.brxb]"></span> 住院号：<span
			class="font-14-654 padd-r-18"
			v-text="jlxqContent.brzyh|| jlxqContent.zyh"></span>
	</div>
	<div  class="flex-container padd-l-10  flex-align-c" v-show="hllx ==5">
		<span class="margin-r-10 whiteSpace">婴儿筛选</span>
		<select-input  class="wh120" @change-data="commonResultChange"
					   :child="qsxzList" :index="'yexm'" :index_val="'yebh'" :val="jlxqContent.yebh"
					   :name="'jlxqContent.yebh'" :index_mc="'yexm'" search="true" >
		</select-input>
	</div>
</div>

<div class="flex-container common-css" id="jlxq">
	<div class="zui-table-view  padd-b-40 padd-r-10  wh20" >
		<div class="flex-container  flex-align-c padd-b-10">
			<span  class="font-14 color-wtg padd-r-5 whiteSpace">类型</span>
			<select-input  @change-data="changeHllx"
				:child="hllx_tran" :index="hllx" :val="hllx"
				:name="'hllx'" :search="true"> </select-input>
		</div>
<!--		<div class="flex-container  flex-align-c padd-b-10">-->
<!--			<span class="font-14  padd-r-5 whiteSpace">日期</span>-->
<!--			 <input type="text" data-select="no" class="zui-input padd-l-10 " id="today" v-model="today" />-->
<!--		</div>-->
		<div class="zui-table-header">
			<table class="zui-table table-width50">
				<thead>
					<tr>
						<th>
							<div class="zui-table-cell cell-m">序号</div>
						</th>
						<th>
							<div class="zui-table-cell cell-l">记录日期</div>
						</th>
						<th>
							<div class="zui-table-cell text-left cell-s">操作员</div>
						</th>
					</tr>
				</thead>
			</table>
		</div>
		<div class="zui-table-body flex-one over-auto" @scroll="scrollTable">
			<table class="zui-table table-width50">
				<tbody>
					<tr
						:class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
						@mouseenter="switchIndex('hoverIndex',true,$index)"
						@mouseleave="switchIndex()"
						@click="switchIndex('activeIndex',true,$index),Wf_Click($index)"
						:tabindex="$index" v-for="(item, $index) in hlxxArr">
						<td>
							<div class="zui-table-cell cell-m" v-text="$index+1"></div>
						</td>
						<td>
							<div class="zui-table-cell cell-l"
								v-text="fDate(item.jlsj,'yyyy-MM-dd')"></div>
						</td>
						<td>
							<div class="zui-table-cell cell-s  text-left" v-text="item.czyxm"></div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<div class="over-auto wh81">
		<div
			class="margin-b-10 padd-r-10  padd-l-10 padd-t-10 flex-container flex-align-c">
			<span class="font-14  padd-r-5">记录时间</span> <input type="text"
				class="zui-input wh160" id="jlsj" data-select="no" v-model="jlContent.jlsj" /> <span
				class=" padd-r-10 font-14  padd-r-5 padd-l-5 margin-l-10">时&emsp;&emsp;段</span>
			<select-input class="wh120 margin-r-20" @change-data="resultChangeSd"
				:child="sd_tran" :index="jlContent.clsd" :val="jlContent.clsd"
				:name="'jlContent.clsd'" :search="true"> </select-input>

			<span style="color:red;font-weight: 900" class="font-14  padd-r-5">护理类型：</span>
			<span style="font-weight: 900;font-size: 16px;" class="padd-r-5" v-text="hllx_tran[jlContent.hllx]"></span>
			<!-- <select-input  id="hllxShow" class="wh120" @change-data="resultChangeHllx"
				:child="hllx_tran" :index="jlContent.hllx" :val="jlContent.hllx"
				:name="'jlContent.hllx'" :search="true"> </select-input>	 -->
		</div>

		<div
			class="margin-b-10 padd-r-10  padd-l-10 padd-t-10 flex-container flex-wrap-w">
			<div v-if="jlContent.hllx == '1' || jlContent.hllx == '2' || jlContent.hllx == '4' || jlContent.hllx == '5' "
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">体&emsp;&emsp;温</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.tw" /> <span class="cm">℃ </span>
			</div>
			<div v-if="jlContent.hllx == '1' || jlContent.hllx == '2' || jlContent.hllx == '4' || jlContent.hllx == '5'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">心&emsp;&emsp;率</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.xl" /><span class="cm">次/分</span>
			</div>
			<div v-if="jlContent.hllx == '1' || jlContent.hllx == '2' || jlContent.hllx == '4' "
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">脉&emsp;&emsp;搏</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.mb" /> <span class="cm">次/分</span>
			</div>
			<div v-if="jlContent.hllx == '1' || jlContent.hllx == '2' || jlContent.hllx == '4' || jlContent.hllx == '5'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">呼&emsp;&emsp;吸</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.hx" /> <span class="cm">次/分</span>
			</div>
			<div v-if="jlContent.hllx == '1' || jlContent.hllx == '2' || jlContent.hllx == '4'|| jlContent.hllx == '5'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">血&emsp;&emsp;氧</br>饱&ensp;和&ensp;度
				</span> <input type="number" @keydown="nextFocus($event)"
					class="zui-input wh120" v-model="jlContent.xybhd" /> <span
					class="cm">%</span>
			</div>
			<div
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">血&emsp;&emsp;压</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input "
					style="width: 100px" v-model="jlContent.xyL" /> <span>/</span> <input
					type="number" @keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.xyR" /> <span class="cm">mmHg</span>
			</div>
			<div  v-if="jlContent.hllx == '1' || jlContent.hllx == '4'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">血&emsp;&emsp;糖</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.xt" /> <span class="cm">mmol/L</span>
			</div>
			<div v-if="jlContent.hllx == '1' || jlContent.hllx == '4'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">疼&emsp;&emsp;痛<br>&emsp;ADL</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.ttfs" /> <span class="cm">分</span>
			</div>
			<div v-if="jlContent.hllx == '1' || jlContent.hllx == '4'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">跌倒压疮</span> <input type="number"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.ddycfs" /> <span class="cm">分</span>
			</div>

			<!-- 产科护理记录 start -->
			<div v-if="jlContent.hllx == '2'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">护理级别</span>
					<select-input class="wh120" @change-data="resultChange"
							  :child="hljb_tran" :index="jlContent.hljb" :val="jlContent.hljb"
							  :name="'jlContent.hljb'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '2'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">体&emsp;&emsp;位</span>
					<select-input class="wh120" @change-data="resultChange"
							  :child="cktw_tran" :index="jlContent.cktw" :val="jlContent.cktw"
							  :name="'jlContent.cktw'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '2'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">饮&emsp;&emsp;食</span> <input type="text"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.ckys" />
			</div>
			<div v-if="hllx == '2'||hllx == '5'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">吸&emsp;&emsp;氧</span> <input type="text"
					@keydown="nextFocus($event)" class="zui-input wh120"
					v-model="jlContent.xy" /><span class="cm">L/分</span>
			</div>
			<!-- 产科护理记录 end -->
            <!-- 新生儿 start -->
            <div v-if="jlContent.hllx == '5' "
                 class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">温度/箱</span> <input type="number"
                                                                    @keydown="nextFocus($event)" class="zui-input wh120"
                                                                    v-model="jlContent.xwd" /> <span class="cm">℃ </span>
            </div>
            <div v-if="jlContent.hllx == '5' "
                 class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">温度/辐射台</span> <input type="number"
                                                                      @keydown="nextFocus($event)" class="zui-input wh120"
                                                                      v-model="jlContent.fstwd" /> <span class="cm">℃ </span>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">反&emsp;&emsp;应</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrfy_tran" :index="jlContent.fy" :val="jlContent.fy"
                              :name="'jlContent.fy'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">哭&emsp;&emsp;声</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrks_tran" :index="jlContent.ks" :val="jlContent.ks"
                              :name="'jlContent.ks'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">吸吮力</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrxsl_tran" :index="jlContent.xsl" :val="jlContent.xsl"
                              :name="'jlContent.xsl'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">肤色/面色&emsp;</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrms_tran" :index="jlContent.ms" :val="jlContent.ms"
                              :name="'jlContent.ms'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">肤色/肢端</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrms_tran" :index="jlContent.fszd" :val="jlContent.fszd"
                              :name="'jlContent.fszd'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">呼吸/节律</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrjl_tran" :index="jlContent.hxjl" :val="jlContent.hxjl"
                              :name="'jlContent.hxjl'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">呼吸/三凹症</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrsaz_tran" :index="jlContent.hxsaz" :val="jlContent.hxsaz"
                              :name="'jlContent.hxsaz'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">体&emsp;&emsp;位</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrtw_tran" :index="jlContent.xsetw" :val="jlContent.xsetw"
                              :name="'jlContent.xsetw'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">皮&emsp;&emsp;&emsp;肤</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrpf_tran" :index="jlContent.xsepf" :val="jlContent.xsepf"
                              :name="'jlContent.xsepf'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">惊&emsp;&emsp;厥</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrjj_tran" :index="jlContent.xsejj" :val="jlContent.xsejj"
                              :name="'jlContent.xsejj'" :search="true"> </select-input>
            </div>
            <div  v-if="jlContent.hllx == '5'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
                <span class=" font-14  padd-r-5">温度/肢端</span>
                <select-input class="wh120" @change-data="resultChange"
                              :child="xsrzd_tran" :index="jlContent.xsezd" :val="jlContent.xsezd"
                              :name="'jlContent.xsezd'" :search="true"> </select-input>
            </div>
            <div v-if="jlContent.hllx == '5' "
                 class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
                <span class=" font-14  padd-r-5">&emsp;奶&emsp;&emsp;量</span> <input type="number"
                                                                      @keydown="nextFocus($event)" class="zui-input wh120"
                                                                      v-model="jlContent.xsenl" /> <span class="cm">ml</span>
            </div>
            <!-- 新生儿 end -->
			<!-- 产科分娩经过记录 start -->
			<div v-if="hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">
					<span style="font-weight: 900;font-size: 16px;">宫缩</span>阵缩</span> <input type="text"
						@keydown="nextFocus($event)" class="zui-input wh120"
						v-model="jlContent.gszs" />
			</div>
			<div v-if="hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">&emsp;&emsp;间歇</span> <input type="text"
						@keydown="nextFocus($event)" class="zui-input wh120"
						v-model="jlContent.gsjx" />
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">&emsp;胎方位</span> <!-- <input type="text"
						@keydown="nextFocus($event)" class="zui-input wh120"
						v-model="jlContent.tfw" />-->
				<select-input class="wh120" @change-data="resultChange"
							  :child="tfw_tran" :index="jlContent.tfw" :val="jlContent.tfw"
							  :name="'jlContent.tfw'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5"><span style="font-weight: 900;font-size: 16px;">胎心</span>情况</span>
					<select-input class="wh120" @change-data="resultChange"
							  :child="txqk_tran" :index="jlContent.txqk" :val="jlContent.txqk"
							  :name="'jlContent.txqk'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">&emsp;&emsp;位置</span>
						<select-input class="wh120" @change-data="resultChange"
							  :child="txwz_tran" :index="jlContent.txwz" :val="jlContent.txwz"
							  :name="'jlContent.txwz'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">&emsp;&emsp;次数</span> <input type="text"
						@keydown="nextFocus($event)" class="zui-input wh120"
						v-model="jlContent.txcs" />
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">先露高低</span>
				<select-input class="wh120" @change-data="resultChange"
							  :child="xlgd_tran" :index="jlContent.xlgd" :val="jlContent.xlgd"
							  :name="'jlContent.xlgd'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5"><span style="font-weight: 900;font-size: 16px;">宫口</span>大小</span>
					<select-input class="wh120" @change-data="resultChange"
							  :child="gkdx_tran" :index="jlContent.gkdx" :val="jlContent.gkdx"
							  :name="'jlContent.gkdx'" :search="true"> </select-input>

			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">&emsp;&emsp;厚薄</span>
				<select-input class="wh120" @change-data="resultChange"
							  :child="gkhb_tran" :index="jlContent.gkhb" :val="jlContent.gkhb"
							  :name="'jlContent.gkhb'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">&emsp;&emsp;松紧</span>
				<select-input class="wh120" @change-data="resultChange"
							  :child="gksj_tran" :index="jlContent.gksj" :val="jlContent.gksj"
							  :name="'jlContent.gksj'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">胎膜</span>
				<select-input class="wh120" @change-data="resultChange"
							  :child="tm_tran" :index="jlContent.tm" :val="jlContent.tm"
							  :name="'jlContent.tm'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">检查方式</span>
						<select-input class="wh120" @change-data="resultChange"
							  :child="jcfs_tran" :index="jlContent.jcfs" :val="jlContent.jcfs"
							  :name="'jlContent.jcfs'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5"><span style="font-weight: 900;font-size: 16px;">无痛分娩</span>
					</br>&emsp;手法</span> <input type="text"
						@keydown="nextFocus($event)" class="zui-input wh120"
						v-model="jlContent.wtfmsf" />
			</div>
			<div v-if="jlContent.hllx == '3'"
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
					<span class=" font-14  padd-r-5">&emsp;&emsp;表现</span> <input type="text"
						@keydown="nextFocus($event)" class="zui-input wh120"
						v-model="jlContent.wtfmbx" />
			</div>
			<!-- 产科分娩经过记录 end -->
			<div
				class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class="flex-align-c flex-container font-14  padd-r-5">签&emsp;&emsp;名</span>
				<select-input :search="true" class="wh120"
					@change-data="resultChange" :not_empty="false" :child="hsList"
					:index="'ryxm'" :index_val="'rybm'" :val="jlContent.zrhs"
					:name="'jlContent.zrhs'"> </select-input>
				</div>
			<div  v-if="jlContent.hllx == '1' || jlContent.hllx == '2' || jlContent.hllx == '4'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
				<span class=" font-14  padd-r-5">意&emsp;&emsp;识</span>
				<select-input class="wh120" @change-data="resultChange"
							  :child="ys_tran" :index="jlContent.ys" :val="jlContent.ys"
							  :name="'jlContent.ys'" :search="true"> </select-input>
			</div>
			<div  v-if="jlContent.hllx == '1' || jlContent.hllx == '4'" class="margin-r-10 margin-b-10 padd-r-10 flex-container flex-align-c">
				<span class=" font-14  padd-r-5">中医治疗</span>
				<select-input class="wh120" @change-data="resultChange"
							  :child="istrue_tran" :index="jlContent.zyzl" :val="jlContent.zyzl"
							  :name="'jlContent.zyzl'" :search="true"> </select-input>
			</div>
			<div v-if="jlContent.hllx == '5'"
				 class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">日龄</span>
				<input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.rl" /> <span class="cm">天</span>
			</div>
			<div v-if="jlContent.hllx == '5'"
				 class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
				<span class=" font-14  padd-r-5">胎龄</span>
				<input type="number" @keydown="nextFocus($event)" class="zui-input wh120" v-model="jlContent.tl" />
			</div>
			<div class="flex-container padd-r-10 wh100MAx  margin-b-10">
				<span class=" font-14  padd-r-5 whiteSpace">诊断名称</span>
				<textarea   class=" wh100MAx padd-r-5 padd-l-5 padd-t-5" rows="4"  v-model="jlContent.zdmc"></textarea>
			</div>
		</div>
		<div class="margin-b-10 ">
			<div class="tab-card"  v-if="jlContent.hllx == '1' || jlContent.hllx == '4' ||jlContent.hllx == '5'">
				<div class="tab-card-header">
					<div class="tab-card-header-title font14">入量</div>
				</div>
				<div class="tab-card-body flex-container flex-wrap-w padd-t-10">
					<div v-for="(item, $index) in rlList">
						<div class="margin-b-10 flex-align-c padd-r-10 position flex-container">
							<span class=" font-14  padd-r-5">项&emsp;&emsp;目</span> <input
								type="text" @keydown="nextFocus($event)" class="zui-input wh120"
								v-model="item.rlMcItem"  v-if="rlxxList.length == 0"/>
							<select-input :search="true" class="wh120" v-if="rlxxList.length != 0"
										  @change-data="resultChangeRlxx($index,$event)"  :child="rlxxList"
										  :index="'rlVal'" :index_val="'rlVal'" :val="item.rlMcItem"
										  :name="'item.rlMcItem'"> </select-input>
								<span @click="delRl($index)" class="removeClass">X</span>
						</div>
						<div class="margin-b-10 flex-container position flex-align-c">
							<span class=" font-14  padd-r-5">数&emsp;&emsp;量</span> <input
								type="number" @keydown="nextFocus($event)"
								class="zui-input wh120" v-model="item.rlSlItem"> <span
								class="cm">ml</span>
						</div>
					</div>


					<div class="margin-b-10 flex-container position flex-align-c">
						<span @click="addRl()" class=" font-14 addClass  padd-r-5">+</span>
					</div>
				</div>
			</div>
			<div class="tab-card"  v-if="jlContent.hllx == '1' || jlContent.hllx == '4'||jlContent.hllx == '5'">
				<div class="tab-card-header">
					<div class="tab-card-header-title font14">出量</div>
				</div>
				<div class="tab-card-body flex-container flex-wrap-w padd-t-10" >
					<div v-for="(item, $index) in clList">
						<div class="margin-b-10 padd-r-10 flex-align-c position flex-container" >
							<span class=" font-14  padd-r-5">项&emsp;&emsp;目</span> <input
								type="text" @keydown="nextFocus($event)" class="zui-input wh120" v-if="clxxList.length == 0"
								v-model="item.clMcItem" />
							<select-input :search="true" class="wh120" v-if="clxxList.length != 0"
										  @change-data="resultChangeClxx($index,$event)"  :child="clxxList"
										  :index="'clVal'" :index_val="'clVal'" :val="item.clMcItem"
										  :name="'item.clMcItem'"> </select-input>
							<span @click="delCl($index)" class="removeClass">X</span>
						</div>
						<div
							class="margin-b-10  padd-r-10 flex-container position flex-align-c">
							<span class=" font-14  padd-r-5">数&emsp;&emsp;量</span> <input
								type="number" @keydown="nextFocus($event)"
								class="zui-input wh120" v-model="item.clSlItem"> <span
								class="cm">ml</span>
						</div>
						<div class="margin-b-10 flex-container position flex-align-c">
							<span class=" font-14  padd-r-5">颜色性状</span>
							<input
								type="text" @keydown="nextFocus($event)"
								class="zui-input wh120" v-model="item.clYsxzItem">
						</div>
					</div>

					<div class="margin-b-10 flex-container position flex-align-c">
						<span @click="addCl()" class=" font-14  padd-r-5 addClass">+</span>
					</div>
				</div>
			</div>
			<!-- 产科护理记录 start -->
			<div class="tab-card"  v-if="jlContent.hllx == '2'">
				<div class="tab-card-header">
					<div class="tab-card-header-title font14">产前</div>
				</div>
				<div class="tab-card-body flex-container padd-t-10" style="flex-wrap: wrap;">
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">&emsp;胎方位</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.tfw" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">宫缩情况</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.gsqk" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">宫口开大</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.gkkd" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">&emsp;胎心音</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.txy" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">胎膜破否</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.tmpf" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">羊水情况</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.ysqk" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">胎盘娩出</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.tpmc" />
					</div>
				</div>
			</div>
			<div class="tab-card"  v-if="jlContent.hllx == '2'">
				<div class="tab-card-header">
					<div class="tab-card-header-title font14">产后</div>
				</div>
				<div class="tab-card-body flex-container padd-t-10" style="flex-wrap: wrap;">
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">宫底高度</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.gdgd" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">子宫情况</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.zgqk" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">切口情况</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.qkqk" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">排气排便</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.pqpb" />
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">阴道流血</br>量</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.ydlxl" /><span
								class="cm">ml</span>
					</div>
					<div
						class="margin-r-10 margin-b-10 padd-r-10 flex-container position flex-align-c">
						<span class=" font-14  padd-r-5">尿&emsp;&emsp;量</span> <input type="text"
							@keydown="nextFocus($event)" class="zui-input wh120"
							v-model="jlContent.nl" /><span
								class="cm">ml</span>
					</div>
				</div>
			</div>
			<!-- 产科护理记录 end -->
		</div>
		<div  class="margin-b-10">
			<div class="tab-card">
				<div class="tab-card-header">
					<div class="tab-card-header-title font14">护理记录</div>
				</div>
				<div class="tab-card-body flex-container padd-t-10" style="flex-direction: column;">
					<div style="display: flex;justify-content: center;">
						<button style="width: 150px;margin-top: 10px;"
							@click="yrjl()" class="zui-btn btn-primary fa fa-plus">引入最新记录</button>
					</div>

					<textarea style="height: 200px;line-height: 18px;margin-top: 10px;" class="zui-input"
						v-model="jlContent.bqgcjcs"></textarea>
				</div>
			</div>
		</div>

	</div>
</div>
<model :s="'引入'" :c="'退出'" class="jyjgPop" @default-click="saveData" @result-clear="jyjgShow=false"
	   :model-show="true" @result-close="jyjgShow=false" v-if="jyjgShow" :title="'检验结果'">
	<div class="bqcydj_model flex-container  flex-wrap-w">
		<div id="bgdList" class="zui-table-view printHide flex-container flex-dir-c margin-r-10">
			<div class="zui-table-header">
				<table class="zui-table table-width50">
					<thead>
					<tr>
						<th class="cell-l">
							<div class="zui-table-cell cell-l">项目名称</div>
						</th>
					</tr>
					</thead>
				</table>
			</div>

			<div class="zui-table-body " data-no-change>
				<table class="zui-table table-width50" v-if="djList.length!=0">
					<tbody>
					<tr ref="list" v-for="(item, $index) in djList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
						@mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" :tabindex="$index"
						@click="checkSelect([$index,'one','djList'],$event),getJymx($index)">
						<td>
							<div class="zui-table-cell cell-l" v-text="item.fymc"></div>
						</td>
					</tr>
					</tbody>
				</table>
				<p v-if="djList.length==0" class="noData text-center zan-border">暂无数据...</p>
			</div>
		</div>
		<div class="flex-one position yzgl over-auto  flex-container ">
			<div class="over-auto jybgd">
				<h1 class="text-center">{{yljgmc}}检验报告单</h1>
				<div class="grid-box">
					<div class="col-x-2">姓&emsp;&emsp;名：<em>{{ jybgd.brxm || '' }}</em></div>
					<div class="col-x-3">住&ensp;院&ensp;号：{{ jybgd.bah || '' }}</div>
					<div class="col-x-3">送检医师：</div>
					<div class="col-x-4">申请日期：{{ fDate( jybgd.sqrq , "datetime" ) }}</div>
					<div class="col-x-2">性&emsp;&emsp;别：{{ brxb_tran[ jybgd.xb ] }}</div>
					<div class="col-x-3">床&ensp;位&ensp;号：{{ jybgd.cwh || '' }}</div>
					<div class="col-x-3">样本类型：{{ jybgd.yblx || '' }}</div>
					<div class="col-x-4">采集日期：{{ fDate( jybgd.cyrq , "datetime" ) }}</div>
					<div class="col-x-2">年&emsp;&emsp;龄：{{ jybgd.nl }}{{ nldw_tran[ jybgd.nldw ] }}</div>
					<div class="col-x-3">科&emsp;&emsp;室：<em>{{ jybgd.ksmc }}</em></div>
					<div class="col-x-3">条&ensp;形&ensp;码：{{ jybgd.jyxh }}</div>
					<div class="col-x-4">接收日期：{{ fDate( jybgd.ybhsrq , "datetime" ) }}</div>
					<div class="col-x-2">仪&ensp;检&ensp;号：<em>{{ jybgd.bbbh || ''}}</em></div>
					<div class="col-x-3">类&emsp;&emsp;型：{{ jybgd.yblx || ''}}</div>
					<div class="col-x-3">临床诊断：{{jybgd.lczd || ''}}</div>
					<div class="col-x-4">备&emsp;&emsp;注：{{ jybgd.bz || '' }}</div>
				</div>
				<table class="table-fixed jybgd-table">
					<thead>
					<tr>
						<th class="text-center" style="width:50px;">选择</th>
						<th class="text-center" style="width:50px;">序号</th>
						<th  style="width:150px;">项目名称</th>
						<th style="width:60px;">简称</th>
						<th style="width:150px;">结果</th>
						<th style="width:50px;">状态</th>
						<th style="width:150px;">参考范围</th>
						<th style="width:100px;">单位</th>
					</tr>
					</thead>
					<tbody>
					<tr v-for="(item,index) in jybgdmx">
						<td class="text-center">
							<input-checkbox @result="reCheckBox" :list="'jybgdmx'" :type="'some'" :which="index"
											:val="isChecked[index]">
							</input-checkbox>
						</td>
						<td class="text-center">{{ item.xh }}</td>
						<td>{{ item.zwmc }}</td>
						<td>{{ item.ywmc }}</td>
						<td>{{ item.sjlx == '1' || item.sjlx == '4' ? item.valueN : item.sjlx == '3' ? item.xzjgmc : item.valueT }}</td>
						<td class="color-green" v-if="(item.sjlx == '1' || item.sjlx == '4') &&  item.valueN < item.zxz">↓</td>
						<td class="color-wtg" v-if="(item.sjlx == '1' || item.sjlx == '4') &&  item.valueN > item.zdz">↑</td>
						<td v-if="(item.sjlx == '1' || item.sjlx == '4') &&  item.valueN <= item.zdz && item.valueN >= item.zxz"></td>
						<td>{{ item.ckzT }}</td>
						<td>{{ item.dw }}</td>
					</tr>
					</tbody>
				</table>
				<div class="grid-box">
					<div class="col-x-4">审核时间：{{ fDate( jybgd.shrq , "datetime" ) }}</div>
					<div class="col-x-4">打印时间：{{ fDate( jybgd.djrq , "datetime" ) }}</div>
					<div class="col-x-2">检验者：{{ jybgd.jyrxm }}</div>
					<div class="col-x-2">审核者：{{ jybgd.shryxm }}</div>
					<div class="col-x-12">本测试结果仅对本样本负责！</div>
					<div class="col-x-12">检验设备：日立7180全自动生化分析仪</div>
				</div>
			</div>
		</div>
	</div>
</model>
<script type="applicatspann/javascript" src="childpage/hljl.js"></script>
