var yyffcz = false, pccz = false, zdtj = false, dqindex = null, qjIndex = 0, ypIndex = 0;
var jbM = {'门特病种编码': 'jbbm', '门特病种名称': 'jbmc', '拼音代码': 'pydm'};
var bm = ""; //下拉table选中项的编码
var allBm = ""; //需要展示的编码
var mc = ""; //下拉table选中项的名称
var jb = ""; //手术级别
var selSearch = ""; //下拉table选中项的索引
var gmshow = new Vue({
    el: '#gmsxx',
    data: {
        sfgm: '',
        gms: '',
    },
    created: function () {
        this.sfgm = userNameBg.Brxx_List.sfgm;
        this.gms = userNameBg.Brxx_List.gms;
    },
    methods:{

    },
});
//药房处方类型选择
var chose = new Vue({
    el: '.tong-search',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable,
		'jbsearch-table': searchTable,
    },
    data: {
        Brxx_List:userNameBg.Brxx_List,
        yfyycflx: [],        //药房拥有处方类型
        CflxJosn: [],     //当前处方类型
        XycflxJson: [],   //西医处方类型列表
        ZycflxJson: [],   //中医处方类型列表
        serchContent: {
            yfbm: "",    //药房
            cflxbm: "",   //处方类型
            cflxmc: "", //处方类型名称
            cflb: "1", //默认为西药处方1,西药，2中药
            sflzcf:'0',
        },
        YFJson: [],		//药房列表
        lczd: null,     //获取临床诊断
        searchCon: [],
        InShow: false,//文本框
        json: [],
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        cflxList: [],
        them: { // 检索的标题字段
            '诊断名称': 'zdmc'
        },
		myzdlist : {
					zd1:null,
					zd2:null,
					zd3:null,
					zd4:null,
					zd5:null,
					zd6:null,
				},
		mContent: {},
		msearchCon: [],
		mthem: jbM,
		pagemt: {
		    page: 1,
		    rows: 10,
		    total: null
		},
		mtselSearch4: -1,
		popContent:{
			mtbzmc:'',
			mtbzbm:'',
			
		},
        fjzdlist:[],
        iszdchecked:[],
        sflzcf_tran:{
            '0':'否',
            '1':'是',
        },
        userInfo:[],

    },
    mounted: function () {
        this.getYfyycf(); //获取到药房拥有处方
        let that = this;
        this.$nextTick(function(){
            that.setfjzdlist();
        })
        this.getUserInfo();
    },
    methods: {
        getUserInfo: function () {


            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    let userInfo = json.body.d;
                    chose.userInfo = userInfo;

                });
        },
        openFjzd:function (){
            fjzdPop.fjzdShow=true;
            fjzdPop.fjzd = [{}];

        },
        getZdgxjg:function(){
            if(this.iszdchecked.length>0){
                let zd = '';
                let zd2 = '';
                let zd1 = '';
                for(let key of Object.keys(this.iszdchecked)){
                    console.log(key,this.iszdchecked[key]);
                    if(this.iszdchecked[key]){
                        if(key<90){
                            zd2+='('+this.fjzdlist[key].jbmb+')'+( this.fjzdlist[key].jbmc || '')+","
                        }else{
                            if(key == 90){
                                zd1+='('+this.Brxx_List.jbbm+')'+( this.Brxx_List.jbmc || '')+","
                            }else if(key == 91){
                                zd1+='('+this.Brxx_List.qtzdbm+')'+( this.Brxx_List.qtzdmc || '')+","
                            }else if(key == 92){
                                zd1+='('+this.Brxx_List.qtzdbm1+')'+( this.Brxx_List.qtzdmc1 || '')+","
                            }else if(key == 93){
                                zd1+='('+this.Brxx_List.qtzdbm2+')'+( this.Brxx_List.qtzdmc2 || '')+","
                            }else if(key == 94){
                                zd1+='('+this.Brxx_List.qtzdbm3+')'+( this.Brxx_List.qtzdmc3 || '')+","
                            }else if(key == 95){
                                zd1+='('+this.Brxx_List.qtzdbm4+')'+( this.Brxx_List.qtzdmc4 || '')+","
                            }
                        }
                    }

                }
                zd = zd1+zd2
                if(zd){
                    zd = zd.substring(0,zd.length-1)
                }
                return zd;
            }else{
                return '';
            }
        },
        reCheckBoxZyh: function (val) {
          console.log(val)
            if (val[0] == 'some') {
                Vue.set(this.iszdchecked, val[1], val[2]);
            }
            this.getZdgxjg();
        },
        setfjzdlist:function(){
                        this.fjzdlist = JSON.parse(userNameBg.Brxx_List.fjzd)

        },
		mtchangeDown: function (event, type, content, searchCon, modelBm, showBm, modelMc,selSearch) {
		    //全局变量
		    bm = modelBm;
		    allBm = showBm;
		    selSearch = selSearch;
		    mc = modelMc;
		    this.selectType = type;
		    if (this[searchCon][this[selSearch]] == undefined) return;
		    this.inputUpDown(event,this[searchCon],selSearch)
		    this[content]=this[searchCon][this[selSearch]]
		    //选中之后的回调操作
		    if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
		        
		        //*************************************疾病形态学
		        if (type == "jbMT") {
		            Vue.set(this.popContent, 'mtbzbm', this.mContent['jbbm']);
		            Vue.set(this.popContent, 'mtbzmc', this.mContent['jbmc']);
		        }
		        this.nextFocus(event);
		        $(".selectGroup").hide();
		        this[selSearch]=-1
		    }
		},
		mselectOne: function (item) {
		    if (item == null) {
		        this.page.page++;
		        this.mtchange(true, allBm, this.popContent[allBm]);
		    } else {
						        this.mContent = item;
		        Vue.set(this.popContent, 'mtbzbm', this.mContent['jbbm']);
		        Vue.set(this.popContent, 'mtbzmc', this.mContent['jbmc']);
		        $(".selectGroup").hide();
		    }
		},
		//当输入值后才触发
		mtchange: function (add, type, val,selSearch,mc,bm,tpevent) {
		    this.selectType = type;
		    if (!add) this.page.page = 1;
		    var _searchEvent = $(tpevent.target.nextElementSibling).eq(0);
		    this.popContent[mc] = val;
		     this.page.parm = val;
		     this.popContent[bm]='';
            this.page.ghxh = userNameBg.Brxx_List.ghxh;
		    //病理诊断
		    if (type == "jbMT") {
		        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbMT'
		            + '&json=' + JSON.stringify(this.page),
		            function (data) {
		                if (add) {
		                    for (var i = 0; i < data.d.list.length; i++) {
		                        chose.msearchCon.push(data.d.list[i]);
		                    }
		                } else {
		                    chose.msearchCon = data.d.list;
		                }
		                chose.pagemt.total = data.d.total;
		                chose.mtselSearch4 = 0;
		                if (data.d.list.length > 0 && !add) {
		                    $(".selectGroup").hide();
		                    _searchEvent.show();
		                    return false;
		                }
		            });
		    }
		
		},

        showTime:function (el,code){
            laydate.render({
                elem: '#'+el
                , show: true //直接显示
                , type: 'date'
                , theme: '#1ab394',
                done: function (value, data) {
                    Vue.set(chose.serchContent,code,value)
                }
            })
        },
        // 这里是下拉框查询的
        searching: function (add, val) {
            this.lczd = val;
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var str_param = {
                parm: this.lczd,
                page: this.page.page,
                rows: this.page.rows,
                rybm: userId,
                ksbm: userNameBg.Brxx_List.ghks
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglLczd&types=query&parm=" + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        if (json.d.list == null || json.d.list == "") {
                            $(".selectGroup").hide();
                        }
                        if (add) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                chose.searchCon.push(json.d.list[i]);
                            }
                        } else {
                            chose.searchCon = json.d.list;
                        }
                        chose.page.total = json.d.total;
                        chose.selSearch = 0;
                        if (json.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {

                        malert("查询失败  " + json.c, 'top', 'deteadted');


                    }
                });
        },
        //鼠标双击（病人挂号信息）
        selectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.searching(true, this.lczd);
            } else {
                $(".selectGroup").hide();
                this.lczd = item.zdmc;
            }
        },
        //回车
        changeDown: function (event) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'json', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {

                this.lczd = this.json.zdmc;
            }
        },
        filterFun:function (list,filterList){
            for (var i = list.length - 1; i >= 0; i--) {
            for (var j = filterList.length - 1; j >= 0; j--) {
                if(list[i].cflxbm == filterList[j].cflxbm){
                    list.splice(i,1)
                }}
            }
            return list
        },
        //获取药房拥有处方
        getYfyycf: function () {
			console.log(this.Brxx_List)
            var mjxzcf = '';
            var yfcf_dg = {page: 1, rows: 100, sort: "cflxbm", order: "asc", parm: ""};
            this.updatedAjax('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmYfyycflx&types=query&dg=' + JSON.stringify(yfcf_dg), function (data) {
                if (data.a == 0) {
                    //获取当前登录人信息
                    this.updatedAjax("/actionDispatcher.do?reqUrl=New1CommonUtil&types=queryCurrentUser",function (json) {
                        mjxzcf = json.d.mjxzcf;
                        console.log('麻精限制处方',mjxzcf);
                    });
                    if(Array.isArray(JSON.parse(mjxzcf))){
                        this.yfyycflx = this.filterFun(data.d.list,JSON.parse(mjxzcf));
                        console.log('处方排除结果',this.yfyycflx);
                    }else {
                        this.yfyycflx = data.d.list;
                    }
                    //药房检索处方类型
                    this.cfglData();
                } else {
                    malert('获取药房拥有处方' + data.c, 'top', 'defeadted')
                }
            });
        },
        //获取药房
        Wf_getYF: function () {
            var yf_dg = {page: 1, rows: 1000, sort: "", order: "desc", parm: ""};
            var yf_json = {tybz:"0"};
            this.updatedAjax('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmYf&types=query&dg=' + JSON.stringify(yf_dg)+'&json=' + JSON.stringify(yf_json), function (data) {
                if (data.a == 0) {
                    this.YFJson = data.d.list;  //绑定药房
                    if (zcy.csqxContent.N03001200172 == '1') {//开启科室药房限制过滤
                        var ksyksList = [];
                        if (data.d && data.d.list && data.d.list.length > 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                if (!data.d.list[i].ksyks || (data.d.list[i].ksyks && data.d.list[i].ksyks.indexOf(userNameBg.Brxx_List.ghks) != -1)) {
                                    ksyksList.push(data.d.list[i])
                                }
                            }
                            this.YFJson = ksyksList;
                        }
                    }
					var ksyksList = [];
					if (data.d && data.d.list && data.d.list.length > 0) {
					    for (var i = 0; i < data.d.list.length; i++) {
					        if (data.d.list[i].yfbm != '02') {
					            ksyksList.push(data.d.list[i])
					        }
					    }
					    this.YFJson = ksyksList;
					}
					
					
					
                    if (fyxmTab.csqx.cs01006400108 != null && fyxmTab.csqx.cs01006400108 != undefined
                        && fyxmTab.csqx.cs01006400108 != "") {
                        brjzFoot.hlyyCheckUrl = fyxmTab.csqx.cs01006400108 + "check";
                    } else {
                        brjzFoot.hlyyCheckUrl = "http://127.0.0.1:8082/hlyy/check";//默认加载第一个
                    }


                    if (!this.serchContent.cflxbm) {
                        this.cfglData();
                    }
                } else {


                    malert('获取药房失败' + data.c, 'top', 'defeadted')

                }
            });
        },
        //药品各种处方类型
        Wf_getCflx: function () {
            var cflx_dg = {page: 1, rows: 100, sort: "cflxbm", order: "asc", parm: ""};
            this.updatedAjax('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmCflx&types=query&dg=' + JSON.stringify(cflx_dg), function (data) {
                if (data.a == 0) {
                    //处方类型分类
                    if (data.d.list != null && data.d.list.length > 0) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            //chose.serchContent.cflb = data.d.list[i].cflb;
                            if (data.d.list[i].cflb == "2") {
                                this.ZycflxJson.push(data.d.list[i]);  //中药处方类型
                            } else {
                                this.XycflxJson.push(data.d.list[i]);  //西药处方类型
                            }
                        }
                    }
                } else {

                    malert('获取处方类型失败' + data.c, 'top', 'defeadted')
                }
            });
        },

        //组件选择下拉框之后的回调
        resultLxChange: function (val,type) {
            
            console.log('结果',chose.yfyycflx);
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "yfbm":
                    Vue.set(this.serchContent, 'yfbm', val[0]);
                    chose.clearNullCf();
                    chose.cfglData();
                    this.$nextTick(function () {
                        this.setNotEmpty()
                    })
                    break;
                case "cflxbm":
                    console.log(val);
                    //this.cflxList=this.CflxJosn[val[5]]
                    Vue.set(this.serchContent, 'cflxbm', val[0]);
                    Vue.set(this.serchContent, 'cflxmc', val[4]);
                    Vue.set(this.serchContent, 'mbmc', val[0]);
                    Vue.set(this.serchContent, 'ypws', this.CflxJosn[val[5]]);
                    chose.clearNullCf();
                    chose.getCflb();
                    break;
                default:
                    break;
            }
            if (!type && !zcy.addCf()) {//调用新增处方
                // malert("请先保存新的处方之后再调用！", 'top', 'defeadted')
                return false;
            }
            //  zcy.addCf(); //调用新增处方
        },
        //调用公共方法处理处方过滤问题
        cfglData: function () {
            this.serchContent.cflxbm = '';
            //处方类型默认为西药（根据药房过滤出处方集合）
            this.CflxJosn = jsonFilter(this.yfyycflx, "yfbm", this.serchContent.yfbm);
            //显示药房关联的处方
            if (this.CflxJosn.length != 0) {
                for (var i = 0; i < this.YFJson.length; i++) {
                    if (this.serchContent.yfbm == this.YFJson[i].yfbm) {
                        this.serchContent.cflxbm = this.YFJson[i]['qscflx'];
                    }
                }
                this.getCflb();
            }
        },

        //公用方法获取处方类别
        getCflb: function () {
            for (var i = 0; i < this.CflxJosn.length; i++) {
                if (this.serchContent.cflxbm == this.CflxJosn[i].cflxbm) {
                    this.serchContent.cflb = this.CflxJosn[i].cflb;
                    this.serchContent.dmcfbz = this.CflxJosn[i].dmcfbz;
                }
            }
            if (this.serchContent.cflb == 2) {
                zcy.zyShow = true;
                zcy.xyShow = false;
                brzcList.cfmbsfcy = 1;
                this.serchContent.ypws = zcy.listGetName(this.ZycflxJson, this.serchContent.cflxbm, 'cflxbm', 'ypws');
                zcy.serchContent.cfzdts = zcy.listGetName(this.ZycflxJson, this.serchContent.cflxbm, 'cflxbm', 'cfzdts');
            } else {
                zcy.zyShow = false;
                zcy.xyShow = true;
                brzcList.cfmbsfcy = 0;
                this.serchContent.ypws = zcy.listGetName(this.XycflxJson, this.serchContent.cflxbm, 'cflxbm', 'ypws');
                zcy.serchContent.cfzdts = zcy.listGetName(this.XycflxJson, this.serchContent.cflxbm, 'cflxbm', 'cfzdts');
            }
        },
        //公用方法清空现有为空的处方信息
        clearNullCf: function () {
            for (var i = 0; i < zcy.cfList.length; i++) {
                if (!zcy.cfList[i].cfh) {
                     zcy.cfList.splice(i, 1);
                    // setTimeout(function () {
                    //     Yfwidth();
                    // },100);
                }
            }
        },
    }
});
var fjzdPop = new Vue({
    el: '.fjzdPop',
    components: {
        'search-table': searchTable
    },
    mixins: [baseFunc, tableBase, checkData],
    data: {
        fjzdShow: false,
        fjzd:[],
        popContent:{},
        them:{'疾病编码': 'jbmb', '疾病名称': 'jbmc'},
        queryObj:{
            page:1,
            rows:50,
            sort:'',
            order:'asc'
        },
        searchCon:[],
        selSearch:-1,
    },
    methods: {
        addFun:function (){
            if(this.fjzd.length == 0 ){
                this.fjzd.push({});
            }else if(this.fjzd[this.fjzd.length-1]['jbmb']){
                this.fjzd.push({});
            }
        },
        saveData:function () {

            if(!chose.fjzdlist){
                chose.fjzdlist = [];
            }

            for (var i = this.fjzd.length - 1; i >= 0; i--) {

                if (!this.fjzd[i].jbmb) {
                    this.fjzd.splice(i, 1);
                }else {
                    chose.fjzdlist.push(this.fjzd[i]);

                }
            }

            var fjzdmx='';
            for (var i = chose.fjzdlist.length - 1; i >= 0; i--) {

                fjzdmx+=chose.fjzdlist[i].jbmb+chose.fjzdlist[i].jbmc+','
            }


            var str_param = {
                ghxh: userNameBg.Brxx_List.ghxh,
                brid: userNameBg.Brxx_List.brid
            };

            let that = this;

            that.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1GhglGhywBrgh',
                    types: 'queryOne',
                    parm: JSON.stringify(str_param)
                }
            }).then(function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    let zdxx = json.body.d.list[0].ghxx;
                    zdxx.fjzd=chose.fjzdlist;
                    userNameBg.Brxx_List.fjzd = JSON.stringify(chose.fjzdlist);

                    userNameBg.Brxx_List.rybh = zdxx.rybh

                    zdxx.fjzdxm=zdxx.jbbm+zdxx.jbmc+','+fjzdmx;

                    var parm = {
                        Ghxx: zdxx,
                        Zcxx: null
                    };
                    var json = JSON.stringify(parm);
                    that.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=updateBrjbxxAndGhxx', json).then(function (data) {

                    })


                }
            })





            chose.$forceUpdate();




            this.fjzdShow=false
        },
        change:function (add,index,mc,bm,val,types){
            if (!add) this.queryObj.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.fjzd[this.allIndex][mc] = val;
            this.queryObj.parm = val;
            this.fjzd[this.allIndex][bm]='';
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types='+types+'&json=' + JSON.stringify(this.queryObj), function (data) {
                if (add) {
                    fjzdPop.searchCon=fjzdPop.selSearch.concat(data.d.list)
                } else {
                    fjzdPop.searchCon = data.d.list;
                }
                fjzdPop.page.total = data.d.total;
                fjzdPop.selSearch= 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                    return false;
                }
            });
        },
        changeDown:function (event,index,zdms,zdbm,qtzd){
            this.allIndex=index;
            if (this['searchCon'][this['selSearch']] == undefined) return;
            this.inputUpDown(event,this['searchCon'],'selSearch')
            this['popContent']=this['searchCon'][this['selSearch']];
            if(event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter'){
                this.fjzd[index]=this.popContent;
                $(".selectGroup").hide();
                this.selSearch= -1;
                this.addFun()
                this.$nextTick(function (){
                    this.nextFocus(event);
                })
            }
        },
        checkedTwoOut:function (item){
            if(!item){
                ++this.queryObj.page;
                this.change(true,this.allIndex,'jbmc','jbbm',this.fjzd[this.allIndex]['jbmc'])
            }else {
                this.fjzd[this.allIndex]=item;
                $(".selectGroup").hide();
                this.selSearch= -1;
                this.$forceUpdate()
            }
        },
    }
})
//处方部分
var zcy = new Vue({
    el: '.dzcf',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer,checkData],
    components: {
        'search-table': searchTable, //西药
        'search-table2': searchTable, //中药
        'search-table3': searchTable, //中药
        'search-table4': searchTable, //中药
    },
    data: {
        Brxx_List:userNameBg.Brxx_List,
        ysbm:{
            '01':true,
            '02':true,
        },
        num: 0,
        kssmd: {},  //抗生素使用目的
        serchContent: {},
        djsum: 0,
        tabShow: false,
        kssModelShow: false,
        kss: false,
        addShow: [],
        cfShow: false,
        showExtra: false, //附加费
        xyShow: true,//西药处方
        zyShow: false,//中药处方
        fjfyIsShow: true, //附加费用是否显示
        dmsmxx: false, //毒麻处方实名信息
        showFjfyInput: true,//是否顯示附加費用輸入框
        yfcfmc: '',
        sfxz: true,
        isChecked: [],
        jsonList: [],
        checkAll: false,
        isCheck: [],
        isShow: false,
        editIndex: null,
        extraChargesList: [],  //添加的附加费集合
        cfList: [],    //西药处方列表
        PfxxJson: [],   //西药配方信息
        zcyList: [],     //中药配方集合
        cfcontent: {}, //当前处方信息
        ypyf: '',
        PcData: {},    //频次
        csqxContent: {
            N05001200274:{},
        },
        csContent1: {},
        YyffData: {},  //用药方法
        ZyYyffData: [], //中药用药方法
        zhuangtai: {
            "0": '待发药',
            '5': '待收费'
        },
        isStop: false,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        page2: {
            page: 1,
            rows: 20,
            total: null
        },
        page3: {
            page: 1,
            rows: 20,
            total: null
        },
        leftNum: 0,
        popContent: {}, //暂存下拉table选中对象
        selSearch: 0,
        selSearch4: 0,
        selSearch3: 0,
        searchCon4: [],
        searchCon3: [],
        searchCon2: [],
        searchCon: [],

        them_tran: {
            'zdksbz': dic_transform.data.zdksbz_tran,
            'gwyp': dic_transform.data.gwyp_tran,
            'kssjb': dic_transform.data.kssjb_tran
        },
        them: {
            '药品名称': 'ypmc',
            '商品名': 'ypspm',
            '产地名称': 'cdmc',
            '药品规格': 'ypgg', '库存': 'kcsl', '药房单位': 'fydwmc1',
            '价格': 'yplj', '医保统筹': 'ybtclbmc', '农保统筹': 'nbtclbmc', '高危药品': 'gwyp', '基药标志': 'jbywbz', '抗生素等级': 'kssjb','医保国家码':'ybgjm'
        },
        them_tran2: {},
        them2: {
            '药品名称': 'ypmc', '药品规格': 'ypgg', '库存': 'kcsl', '药房单位': 'yfdwmc',
            '价格': 'yplj', '医保统筹': 'ybtclbmc', '农保统筹': 'nbtclbmc'
        },
        them_tran3: {'zxdlx': dic_transform.data.zxdlx_tran},
        them3: {
            '方法编码': 'yyffbm',
            '方法名称': 'yyffmc',
            '拼音简码': 'pydm',
            '执行单类型': 'zxdlx'
        },
        them_tran4: {'tybz': dic_transform.data.stopSign},
        them4: {
            '频次编码': 'pcbm',
            '频次名称': 'pcmc',
            '拉丁名称': 'ldmc',
            '次数': 'cs',
            '停用标志': 'tybz'
        },
        color: {
            '0': '',
            '1': 'red',
            '2': 'red',
            '3': 'red',
        },
        selSearch2: 0,
        zxdlx: null,
        kssContent: {},
        sfxxjzyy:false,


    },
    created: function () {
        this.sfjz();
    },
    mounted: function () {

    },
    updated: function () {
            changHeight()
    },
    watch: {
        'zcyList': {
            deep: true,
            handler: function (newVal, oldVal) {
                immediate: true;
                brjzFoot.sum = 0
                this.setSum();
            }
        },
        'cfcontent.zyfs': {
            handler: function () {
                immediate: true;
                brjzFoot.sum = 0
                this.setSum()
            }
        },
        'PfxxJson': {
            deep: true,
            handler: function (newVal, oldVal) {
                immediate: true;
                brjzFoot.sum = 0
                this.setSunFun()
            }
        }
    },
    methods: {
        sfjz:function(){
            if(this.Brxx_List.ghksmc.indexOf('急诊') != -1){
                this.sfxxjzyy = true;
            }else{
                this.sfxxjzyy = false;
            }
        },
        showJzyyDate: function (index, event) {
            var elm = elm = 'jzyyrq' + index;

            Mask.newMask(this.MaskOptions(elm));

            this._laydate = laydate.render({
                elem: '.'+elm
                , show: true //直接显示
                , type: 'datetime'

                , theme: '#1ab394',
                done: function (value, data) {
                    zcy.PfxxJson[index].jzyyrq = value;
                    zcy.$forceUpdate();


                }
            })
        },
		showksDate: function (index, event,lx) {
			var elm = '';
			var mrrq = '';
			if(lx=='1'){
				elm = 'startdate' + index;
				mrrq = zcy.PfxxJson[index].mtksrq
			}else{
				elm = 'enddate' + index;
				mrrq = zcy.PfxxJson[index].mtjsrq
			}
		    Mask.newMask(this.MaskOptions(elm));
		    
		    this._laydate = laydate.render({
		        elem: '.'+elm
		        , show: true //直接显示
		        , type: 'date'
				,format: 'yyyy-MM-dd'
		        , theme: '#1ab394',
		        done: function (value, data) {
		            					if(lx=='1'){
						zcy.PfxxJson[index].mtksrq = value;
						let dates = new Date(value);
                        dates=dates.setDate(dates.getDate()+(Number(zcy.PfxxJson[index].yyts)-1));
                        dates=new Date(dates);
                        zcy.PfxxJson[index].mtjsrq = zcy.fDate(dates,'date')


					}else{
						zcy.PfxxJson[index].mtjsrq = value
					}
                    zcy.$forceUpdate();


		        }
		    })
		},
        init:function(){
            this.$nextTick(function () {
                var zcyParm = {"page": 1, "rows": 100, "sort": "", "order": "asc"}
                $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmzcyyysm&types=query&dg=" + JSON.stringify(zcyParm), function (json) {
                    zcy.jsonList = json.d.list;
                });
            })
        },
        saveKssSymd: function () {
            if (!this.empty_sub('kss')) {
                return false;
            }
            if (this.popContent.kssjb == 2) {
                if(symd.popContent.symd == '1' && !symd.popContent.ssbm){
                    malert('使用目的为手术预防使用，手术名称必填', 'top', 'defeadted')
                    return false
                }
                var obj = symd.popContent
            } else if (this.popContent.kssjb >= 3) {
                sqKss.savaData()
                return false
            }
            Vue.set(this.PfxxJson,ypIndex,obj)
            this.kssModelShow = false
        },
        resultChangeKss: function (val) { //抗生素使用目的
            Vue.set(this.PfxxJson[ypIndex], 'kjywsymd', val[0]);
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.kss = false
            $('#yyjl' + ypIndex).focus()
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N030012001',
                "ksbm": userNameBg.Brxx_List.ghks
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N03001200148": //是否顯示附件費用
                                if (csjson.csz) {
                                    if (csjson.csz == '1') {
                                        this.showFjfyInput = false;
                                    }
                                }
                                break;
                            case "N03001200121": //是否对抗生素药品使用限制0-否，1-是
                                if (csjson.csz) {
                                    this.csqxContent.cs003003200118 = csjson.csz;
                                }
                                break;
                            case "N03003200126": //是否对抗肿瘤药品使用限制0-否，1-是
                                if (csjson.csz) {
                                    this.csqxContent.cs003003200126 = csjson.csz;
                                }
                                break;
                            case "N03003200127": //是否对药品种类中成药使用限制0-否，1-是
                                if (csjson.csz) {
                                    this.csqxContent.cs003003200127 = csjson.csz;
                                }
                                break;
                            case "N03001200103":
                                if (csjson.csz) {
                                    chose.serchContent.yfbm = csjson.csz;
                                }
                                break;
                            case "N03001200123": //是否使用汤头功能
                                if (csjson.csz) {
                                    this.csqxContent.N03001200123 = csjson.csz;
                                }
                                break;
                            case "N03001200127": // 保存后处方是否可以修改
                                if (csjson.csz) {
                                    this.csqxContent.N03001200127 = csjson.csz;
                                    brjzFoot.N03001200127 = csjson.csz;
                                }
                                break;
                            case "N03001200128": // 云南东软医保智能审核
                                if (csjson.csz) {
                                    this.csqxContent.N03001200128 = csjson.csz;
                                    brjzFoot.N03001200128 = csjson.csz;
                                    if (csjson.csz == '1') {
                                        brjzFoot.getbxlb();
                                    }
                                }
                                break;
                            case "N03001200129": // 毒麻处方实名信息
                                if (csjson.csz) {
                                    this.csqxContent.N03001200129 = csjson.csz;
                                }
                                break;
                            case "N03001200107": //是否强行限定药品处方位数0=否,1=是
                                if (csjson.csz) {
                                    this.csqxContent.N03001200107 = csjson.csz;
                                }
                                break;
                            case "N03001200141": //门诊处方增加是否允许修改处方总量0=不允许,1=允许修改
                                if (csjson.csz) {
                                    this.csqxContent.N03001200141 = csjson.csz;
                                }
                                break;
                            case "N03001200142": //门诊处方打印是否预览 0-否 1-是
                                if (csjson.csz) {
                                    this.csqxContent.N03001200142 = csjson.csz;
                                }
                                break;
                            case "N03001200143": //处方保存时判断医院所在省份，0贵州，1其他
                                if (csjson.csz) {
                                    this.csqxContent.cs03001200143 = csjson.csz;
                                }
                                break;
                            case "N03001200144": //贵州医药监管路径
                                if (csjson.csz) {
                                    this.csqxContent.cs03001200144 = csjson.csz;
                                }
                                break;
                            case "N03001200152": //门诊处方打印格式根据类型自定义
                                if (csjson.csz) {
                                    this.csqxContent.N03001200152 = csjson.csz;
                                }
                                break;
                            case "N03001200153": //门诊医生打印输液卡是否掉帆软//暂时没用
                                if (csjson.csz) {
                                    this.csqxContent.N03001200153 = csjson.csz;
                                }
                                break;
                            case "N03001200156": //是否默认基本剂量
                                if (csjson.csz) {
                                    this.csqxContent.N03001200156 = csjson.csz;
                                }
                                break;
                            case "N03001200157": //是否默认保存打印
                                if (csjson.csz) {
                                    this.csqxContent.N03001200157 = csjson.csz;
                                }
                                break;
                            case "N03001200158": //年龄小于14默认儿童处方
                                if (csjson.csz) {
                                    this.csqxContent.N03001200158 = csjson.csz;
                                }
                                break;
                            case "N03001200165": //限制早期妊娠终止药品种类编码（维护了就限制）
                                if (csjson.csz) {
                                    this.csqxContent.N03001200165 = csjson.csz;
                                }
                                break;
                            case "N03001200166": //限制中期妊娠终止药品种类编码（维护了就限制）
                                if (csjson.csz) {
                                    this.csqxContent.N03001200166 = csjson.csz;
                                }
                                break;
                            case "N03001200167": //可开早期终止妊娠药品人员（人员编码，英文逗号隔开）
                                if (csjson.csz) {
                                    this.csqxContent.N03001200167 = csjson.csz;
                                }
                                break;
                            case "N03001200168": //可开中期终止妊娠药品人员（人员编码，英文逗号隔开）
                                if (csjson.csz) {
                                    this.csqxContent.N03001200168 = csjson.csz;
                                }
                                break;
                            case "N03001200169": //限制麻醉与第一类精神药品编码（药品编码，英文逗号隔开）
                                if (csjson.csz) {
                                    this.csqxContent.N03001200169 = csjson.csz;
                                }
                                break;
                            case "N03001200170": //可开麻醉与第一类精神药品人员（人员编码，英文逗号隔开）
                                if (csjson.csz) {
                                    this.csqxContent.N03001200170 = csjson.csz;
                                }
                                break;
                            case "N03001200172": //是否开启过滤科室材料房 0-否 1-是
                                if (csjson.csz) {
                                    this.csqxContent.N03001200172 = csjson.csz;
                                }
                                break;
                            case "N03001200174": //中药处方笺地址
                                if (csjson.csz) {
                                    this.csqxContent.N03001200174 = csjson.csz; //brPage/ynPrintPage
                                }
                                break;
                            case "N03001200175": //中药处方笺地址
                                this.csqxContent.N03001200175 = !csjson.csz ? 1 : csjson.csz;
                                break;
                            case "N05001200260": //精麻处方的处方号是否单独生成
                                if (csjson.csz) {
                                    this.csqxContent.N05001200260 = csjson.csz;
                                    brjzFoot.N05001200260 = csjson.csz;
                                }
                                break;
                            case "00600100121": //建档档案请求接口
                                if (csjson.csz) {
                                    this.csqxContent.cs00600100121 = csjson.csz;
                                }
                                break;
                            case "N050012002701": //用药方法 || 频次
                                if (csjson.csz) {
                                    this.csqxContent.N050012002701 = csjson.csz;
                                }
                                break;
                            case "N05001200273": //（急：3天；麻精：7天）
                                if (csjson.csz) {
                                    this.csqxContent.N05001200273 = csjson.csz;
                                }
                                break;
                            case "N05001200274": //处方类型对应精神药品标志
                                if (csjson.csz) {
                                    this.csqxContent.N05001200274 =  eval("("+csjson.csz+")");
                                }
                                break;
                            case "N05001200276": //合理用药跳转
                                if (csjson.csz) {
                                    brjzFoot.csqxContent.N05001200276 =  csjson.csz;
                                }
                                break;
                            case "N05001200263": //是否提示国家基本药物
                                if (csjson.csz) {
                                    zcy.csqxContent.N05001200263 = csjson.csz;
                                    brjzFoot.N05001200263 = csjson.csz;
                                }
                                break;
                        }
                    }
                    if (this.csqxContent.N03001200158 == '1') {//台江年龄小于14默认儿童处方
                        if (userNameBg.Brxx_List.brnl <= 14) {
                            chose.serchContent.yfbm = '01';
                            chose.CflxJosn = jsonFilter(chose.yfyycflx, "yfbm", chose.serchContent.yfbm);
                            chose.serchContent.cflxbm = '08';
                        } else {
                            if (userNameBg.Brxx_List.mryfbm) {
                                chose.serchContent.yfbm = userNameBg.Brxx_List.mryfbm;
                                chose.CflxJosn = jsonFilter(chose.yfyycflx, "yfbm", chose.serchContent.yfbm);
                            }
                            if (userNameBg.Brxx_List.mrcfbm) {
                                chose.serchContent.cflxbm = userNameBg.Brxx_List.mrcfbm;
                            }
                        }
                        chose.getCflb();
                    } else {
                        if (userNameBg.Brxx_List.mryfbm) {
                            chose.serchContent.yfbm = userNameBg.Brxx_List.mryfbm;
                            chose.CflxJosn = jsonFilter(chose.yfyycflx, "yfbm", chose.serchContent.yfbm);
                        }
                        if (userNameBg.Brxx_List.mrcfbm) {
                            chose.serchContent.cflxbm = userNameBg.Brxx_List.mrcfbm;
                            chose.getCflb();
                        }
                    }
                } else {
                    malert('参数权限获取失败' + json.c, 'top', 'defeadted')
                }
            });
        },
        //频次
        selectOne4: function (item) {
            this.popContent = item;
            Vue.set(zcy.PfxxJson[qjIndex], 'yypc', this.popContent.pcbm);
            Vue.set(zcy.PfxxJson[qjIndex], 'yypcmc', this.popContent.pcmc);
            Vue.set(zcy.PfxxJson[qjIndex], 'yypccs', this.popContent.cs);
            for (var j = 0; j < zcy.PfxxJson.length; j++) {
                if (zcy.PfxxJson[qjIndex].fzh != undefined && zcy.PfxxJson[j].fzh != undefined) {
                    if (zcy.PfxxJson[qjIndex].fzh > 0 && zcy.PfxxJson[j].fzh > 0 && zcy.PfxxJson[j].fzh == zcy.PfxxJson[qjIndex].fzh) {
                        Vue.set(zcy.PfxxJson[j], 'yypc', zcy.PfxxJson[qjIndex].yypc);
                        Vue.set(zcy.PfxxJson[j], 'yypcmc', zcy.PfxxJson[qjIndex].yypcmc);
                        Vue.set(zcy.PfxxJson[j], 'yypccs', zcy.PfxxJson[qjIndex].yypccs);
                        Vue.set(zcy.PfxxJson[j], 'yyff', zcy.PfxxJson[qjIndex].yyff);
                        Vue.set(zcy.PfxxJson[j], 'yyts', zcy.PfxxJson[qjIndex].yyts);
                        Vue.set(zcy.PfxxJson[j], 'sysd', zcy.PfxxJson[qjIndex].sysd);
                        Vue.set(zcy.PfxxJson[j], 'sysddw', zcy.PfxxJson[qjIndex].sysddw);
                        //公用方法计算用量
                        //zcy.pubJS(j);
                    }
                }

            }
            //公用方法计算用量
            zcy.pubJS(qjIndex);
            this.selSearch4 = 0;
            this.nextFocus(event);
            $(".selectGroup").hide();
        },
        //用药方法(双击)
        selectOne3: function (item) {
            this.popContent = item;
            Vue.set(zcy.PfxxJson[qjIndex], 'yyff', this.popContent.yyffbm);
            Vue.set(zcy.PfxxJson[qjIndex], 'yyffmc', this.popContent.yyffmc);
            zcy.zxdlx = this.popContent.zxdlx;
            for (var j = 0; j < zcy.PfxxJson.length; j++) {
                if (zcy.PfxxJson[qjIndex].fzh != undefined && zcy.PfxxJson[j].fzh != undefined) {
                    if (zcy.PfxxJson[qjIndex].fzh > 0 && zcy.PfxxJson[j].fzh > 0 && zcy.PfxxJson[j].fzh == zcy.PfxxJson[qjIndex].fzh) {
                        Vue.set(zcy.PfxxJson[j], 'yypc', zcy.PfxxJson[qjIndex].yypc);
                        Vue.set(zcy.PfxxJson[j], 'yypccs', zcy.PfxxJson[qjIndex].yypccs);
                        Vue.set(zcy.PfxxJson[j], 'yyff', zcy.PfxxJson[qjIndex].yyff);
                        Vue.set(zcy.PfxxJson[j], 'yyts', zcy.PfxxJson[qjIndex].yyts);
                        Vue.set(zcy.PfxxJson[j], 'sysd', zcy.PfxxJson[qjIndex].sysd);
                        Vue.set(zcy.PfxxJson[j], 'sysddw', zcy.PfxxJson[qjIndex].sysddw);
                        //公用方法计算用量
                        //zcy.pubJS(j);
                    }
                }
            }
            this.selSearch3 = 0;
            this.nextFocus(event);
            $(".selectGroup").hide();
        },
        //用药方法（检索）
        Wf_change3: function (index, type, val) {
            this.popContent = {};
            qjIndex = index;
            Vue.set(this.PfxxJson[index],type,val)
            //var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var _searchEvent = $("#yyffmc_" + index + " + div");
            var pram = this.PfxxJson[index][type];
            if (pram == null || pram == "") {
                this.page2.parm = "";
            } else {
                //截取首尾空格
                pram = trimStr(pram);
                //截取首尾空格
                pram = trimStr(pram);
                this.page2.parm = pram;
            }
            var str_param = {parm: this.page2.parm, page: this.page2.page, rows: this.page2.rows};
            $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmyyff&types=query&dg=" + JSON.stringify(str_param), function (data) {
                if (data.a == 0) {
                    zcy.searchCon3 = data.d.list;
                    zcy.selSearch3 = 0;
                    if (data.d.list.length > 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                } else {
                    malert("查询失败  " + json.c, 'top', 'defeadted');
                }
            });
        },
        //频次
        Wf_change4: function (index, type, val) {
            qjIndex = index;
            Vue.set(this.PfxxJson[index],type,val)
            //var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var _searchEvent = $("#yypcmc_" + index + " + div");
            var pram = this.PfxxJson[index][type];
            if (pram == null || pram == "") {
                this.page3.parm = "";
            } else {
                //截取首尾空格
                pram = trimStr(pram);
                //截取首尾空格
                pram = trimStr(pram);
                this.page3.parm = pram;
            }
            var pc_dg = {
                parm: this.page3.parm,
                page: this.page3.page,
                rows: this.page3.rows,
                sort: "sor",
                order: "asc"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yzpc&dg=" + JSON.stringify(pc_dg), function (json) {
                zcy.searchCon4 = json.d.list;
                zcy.selSearch4 = 0;
                if (json.d.list.length != 0) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                } else {
                    $(".selectGroup").hide();
                }
            });
        },

        //用药方法（回车）
        Wf_changeDown3: function (index, event, type) {
            this.prevFocus(event);
            // var _searchEvent = $(event.target.nextElementSibling).eq(0);
            // this.keyCodeFunction(event, 'popContent', 'searchCon3');
            this.inputUpDown(event, this.searchCon3, "selSearch3");
            this.popContent = this.searchCon3[this.selSearch3]
            qjIndex = index;
            //赋值
            if (event.keyCode == 13) {
                if (this.popContent == undefined) return
                if (this.popContent.yyffbm) {
                    Vue.set(zcy.PfxxJson[index], 'yyff', this.popContent.yyffbm);
                    Vue.set(zcy.PfxxJson[index], 'yyffmc', this.popContent.yyffmc);
                    zcy.zxdlx = this.popContent.zxdlx;
                }
                zcy.pubJS(qjIndex);
                //下拉框回车后回调.
                //属性改变之后分组号相同的一起改变
                for (var j = 0; j < zcy.PfxxJson.length; j++) {
                    if (zcy.PfxxJson[index].fzh != undefined && zcy.PfxxJson[j].fzh != undefined) {
                        if (zcy.PfxxJson[index].fzh > 0 && zcy.PfxxJson[j].fzh > 0 && zcy.PfxxJson[j].fzh == zcy.PfxxJson[index].fzh) {
                            Vue.set(zcy.PfxxJson[j], 'yypc', zcy.PfxxJson[index].yypc);
                            Vue.set(zcy.PfxxJson[j], 'yypcmc', zcy.PfxxJson[index].yypcmc);
                            Vue.set(zcy.PfxxJson[j], 'yypccs', zcy.PfxxJson[index].yypccs);
                            Vue.set(zcy.PfxxJson[j], 'yyffmc', zcy.PfxxJson[index].yyffmc);
                            Vue.set(zcy.PfxxJson[j], 'yyff', zcy.PfxxJson[index].yyff);
                            Vue.set(zcy.PfxxJson[j], 'yyts', zcy.PfxxJson[index].yyts);
                            Vue.set(zcy.PfxxJson[j], 'sysd', zcy.PfxxJson[index].sysd);
                            Vue.set(zcy.PfxxJson[j], 'sysddw', zcy.PfxxJson[index].sysddw);

                            //公用方法计算用量
                            //zcy.pubJS(j);
                        }
                    }
                }
                this.selSearch3 = 0;
                this.nextFocus(event);
                $(".selectGroup").hide();
            }
        },

        //频次
        Wf_changeDown4: function (index, event, type, value) {
            this.prevFocus(event);
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            // this.keyCodeFunction(event, 'popContent', 'searchCon4');
            this.popContent = this.searchCon4[this.selSearch4]
            this.inputUpDown(event, this.searchCon4, "selSearch4");
            qjIndex = index;
            //赋值
            if (event.keyCode == 13) {
                if (this.popContent == undefined) return
                Vue.set(zcy.PfxxJson[index], 'yypc', this.popContent.pcbm);
                Vue.set(zcy.PfxxJson[index], 'yypcmc', this.popContent.pcmc);
                Vue.set(zcy.PfxxJson[index], 'yypccs', this.popContent.cs);
                if (zcy.PfxxJson[parseInt(index)]['fzh'] && zcy.PfxxJson[parseInt(index)]['fzh'] > 0) {
                    this.Wf_fzh(parseInt(index), true)
                } else {
                    zcy.pubJS(index);
                }
                this.selSearch4 = -1;
                this.$nextTick(function () {
                    this.nextSelect(event, 1)
                    // $("#yzhm").focus();
                })
                $(".selectGroup").hide();
            }
        },
        setSunFun:function (){
            console.log("setSunFun");
            brjzFoot.sum=0;
            for (var i = 0; i < this.PfxxJson.length; i++) {
                if (zcy.PfxxJson[i].cfyl != undefined && zcy.PfxxJson[i].yplj != undefined) {
					var n1=new BigNumber(zcy.accMul(zcy.PfxxJson[i].cfyl,(zcy.PfxxJson[i].yplj || 0)));
					brjzFoot.sum = this.MathAdd(brjzFoot.sum,n1.toFixed(2))
                    //brjzFoot.sum += parseFloat((zcy.PfxxJson[i].cfyl * zcy.PfxxJson[i].yplj));
                    //console.log("brjzFoot.sum:"+brjzFoot.sum+"===>cfyl:"+parseInt(zcy.PfxxJson[i].cfyl || 0)+"====>yplj:"+zcy.PfxxJson[i].yplj);
                }
            }
            brjzFoot.sum=this.MathFun(brjzFoot.sum)
        },
        setSum: function () {
            //中药付数
            var zyfs = parseInt(Number(zcy.cfcontent.zyfs || 0)).toFixed(2);
            for (var i = 0; i < this.zcyList.length; i++) {
                //根据中药付数*剂量计算用药总量
                this.zcyList[i].cfyl = Math.ceil(parseInt(this.zcyList[i].yyjl || 0) / parseInt(this.zcyList[i].jbjl || 0)) * zyfs;
                
                if (this.zcyList[i].cfyl != undefined && this.zcyList[i].yplj != undefined) {
					var n1=new BigNumber(zcy.accMul(this.zcyList[i].cfyl,(this.zcyList[i].yplj || 0)));
					console.log(n1.toFixed(2))
					this.djsum = this.MathAdd(this.djsum,n1.toFixed(2))
                    //this.djsum += parseFloat(((parseInt(this.zcyList[i].cfyl || 0)) * (this.zcyList[i].yplj || 0)));
                    //console.log("this.djsum:"+this.djsum+"===>cfyl:"+parseInt(this.zcyList[i].cfyl || 0)+"====>yplj:"+this.zcyList[i].yplj);
                }
            }
            brjzFoot.djsum=this.MathFun(brjzFoot.djsum)
            brjzFoot.sum = this.djsum;//* parseInt(Number(zcy.cfcontent.zyfs || 0)).toFixed(2)
            this.djsum = 0
            return
        },
        ypyfFun: function (n) {
            this.cfcontent.yysmcf = n
        },
        resultChangeMb: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            Vue.set(this[val[2][0]], val[3], val[4]);
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
            zcy.$forceUpdate();
        },
        move: function (direction) {
            if (direction == 'left') {
                if ($('#myscrollbox  ul')[0].scrollLeft >= this.leftNum) {
                    this.leftNum += 100
                    $('#myscrollbox  ul').scrollLeft(+this.leftNum);
                }
            } else if (direction == 'right') {
                if ($('#myscrollbox  ul')[0].scrollLeft <= this.leftNum) {
                    this.leftNum = this.leftNum - 100
                    $('#myscrollbox  ul').scrollLeft(this.leftNum);
                }
            }
        },
        tabShowActive: function () {
            if (this.cfList.length >= 6) {
                zcy.tabShow = true;
            } else {
                zcy.tabShow = false;
            }
        },
        //同组直接跳判断
        nextFocusJl: function (event, index,yyjl) {
            if (event.keyCode == 13) {
                if (zcy.PfxxJson.length > 1
                    && zcy.PfxxJson[index].fzh
                    && zcy.PfxxJson[index].fzh != 0
                    && zcy.PfxxJson[index].yyff != null
                    && (!zcy.PfxxJson[index - 1].fzh || (zcy.PfxxJson[index - 1].fzh == zcy.PfxxJson[index].fzh))
                ) {
                    zcy.addYh();//调用新增一行药品
                } else {
                    if(!yyjl){
                        return  false
                    }
                    this.$nextTick(function () {
                        this.nextSelect(event)
                    })
                }
                return false
            } else if (event.keyCode == 37) {
                if (zcy.PfxxJson.length > 1 && zcy.PfxxJson[index].fzh && zcy.PfxxJson[index].fzh != 0 && zcy.PfxxJson[index].yyff != null) {
                    zcy.addYh();//调用新增一行药品
                } else {
                    this.$nextTick(function () {
                        this.nextSelect(event)
                    })
                }
                return false
            } else if (event.keyCode == 39) {
                if (zcy.PfxxJson.length > 1 && zcy.PfxxJson[index].fzh && zcy.PfxxJson[index].fzh != 0 && zcy.PfxxJson[index].yyff != null) {
                    zcy.addYh();//调用新增一行药品
                } else {
                    this.$nextTick(function () {
                        this.nextSelect(event)
                    })
                }
                return false
            }
        },
        //用药方法判断
        nextFocusYyff: function (event,num) {
            
            if (event.keyCode == 13) {
                
                //判断总量是否大于库存数
                if (zcy.PfxxJson[num].cfyl > zcy.PfxxJson[num].sjkc) {
                    malert("药品库存不足", 'top', 'defeadted');
                    //this.PfxxJson[index].ypbm = ''
                    //this.PfxxJson[index].ypmc = ''
                    //this.PfxxJson[index].yyjl = ''
                    $('#ypmc' + num).focus()
                    return;
                }
                if (parseInt(zcy.zxdlx) != 3) {
                    zcy.addYh2();
                } else {
                    this.$nextTick(function () {
                        this.nextSelect(event)
                    })
                }
                return false
            } else if (event.keyCode == 37) {
                if (zcy.zxdlx != '3') {
                    zcy.addYh2();
                } else {
                    this.$nextTick(function () {
                        this.nextSelect(event)
                    })
                }
                return false
            } else if (event.keyCode == 39) {
                if (zcy.zxdlx != '3') {
                    zcy.addYh2();
                } else {
                    this.$nextTick(function () {
                        this.nextSelect(event)
                    })
                }
                return false
            }
        },
        //西药新增一行
        addYh: function () {
            if (zcy.csqxContent.N03001200107 == '1') {
                var ypws = 5;
                if (chose.serchContent.ypws) {
                    ypws = chose.serchContent.ypws;
                }
                if (this.PfxxJson.length >= ypws) {
                    malert("处方药品位数超过限定数量: " + ypws, 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
            }
            if (zcy.cfcontent.cfh == undefined) {
                malert("请先添加处方", 'top', 'defeadted');
                return false;
            }
            if (zcy.cfcontent.cfh == "") {
                //初始化配方
                var yppf = {};
                if (zcy.PfxxJson.length > 0) {
                    if (zcy.PfxxJson[zcy.PfxxJson.length - 1].ypbm) {
                        var fzh = zcy.PfxxJson[zcy.PfxxJson.length - 1].fzh;
                        if (fzh > 0) {
                            yppf.fzh = fzh;
                            yppf.yyffmc = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyffmc;
                            yppf.yyff = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyff;
                            yppf.yypcmc = zcy.PfxxJson[zcy.PfxxJson.length - 1].yypcmc;
                            yppf.yypc = zcy.PfxxJson[zcy.PfxxJson.length - 1].yypc;
                            yppf.yypccs = zcy.PfxxJson[zcy.PfxxJson.length - 1].yypccs;
                            yppf.yyts = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyts;
                            yppf.sysd = zcy.PfxxJson[zcy.PfxxJson.length - 1].sysd;
                            yppf.sysddw = zcy.PfxxJson[zcy.PfxxJson.length - 1].sysddw;
                            //yppf.yyts = '1';
                            // yppf.yypc='01';
                        }

                        if(chose.Brxx_List.fbbm=='40'){
                            yppf.yyts = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyts;
                            yppf.mtyyts = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyts;
                            yppf.mtksrq = zcy.PfxxJson[zcy.PfxxJson.length - 1].mtksrq;
                            yppf.mtjsrq = zcy.PfxxJson[zcy.PfxxJson.length - 1].mtjsrq;
                        }


                        Vue.set(zcy.PfxxJson,zcy.PfxxJson.length,yppf)
                        console.log(zcy.PfxxJson)
                    }
                } else {
                    console.log(yppf)
                    Vue.set(zcy.PfxxJson,zcy.PfxxJson.length,yppf)
                }
                var cou = zcy.PfxxJson.length - 1;
                setTimeout(function () {   //延时0.1秒执行
                    $("#fzh" + cou).focus();
                    $("input[name='text']").each(function () {
                        $(this).removeClass('input-border');
                        $(this).attr('disabled', false)
                    });
                    var str = $('#myscrollbox > ul:last-child');
                    $(str).addClass('active');
                    $(str).siblings().removeClass('active');
                }, 100);
            } else {
                malert("该处方已保存不能进行配方的添加操作", 'top', 'defeadted');
            }
        },
        addYh2: function () {
            if (zcy.csqxContent.N03001200107 == '1') {
                var ypws = 5;
                if (chose.serchContent.ypws) {
                    ypws = chose.serchContent.ypws;
                }
                if (this.PfxxJson.length >= ypws) {
                    malert("处方药品位数不能超过限定数量 " + ypws + '个处方', 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
            }
            var yppf = {};
            if (zcy.PfxxJson.length > 0) {

                if (zcy.PfxxJson[zcy.PfxxJson.length - 1].ypbm) {
                    var fzh = zcy.PfxxJson[zcy.PfxxJson.length - 1].fzh;
                    if (fzh > 0) {
                        yppf.fzh = fzh;
                        yppf.yyffmc = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyffmc;
                        yppf.yyff = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyff;
                        yppf.yypcmc = zcy.PfxxJson[zcy.PfxxJson.length - 1].yypcmc;
                        yppf.yypc = zcy.PfxxJson[zcy.PfxxJson.length - 1].yypc;
                        yppf.yypccs = zcy.PfxxJson[zcy.PfxxJson.length - 1].yypccs;
                        yppf.yyts = zcy.PfxxJson[zcy.PfxxJson.length - 1].yyts;
                        yppf.sysd = zcy.PfxxJson[zcy.PfxxJson.length - 1].sysd;
                        yppf.sysddw = zcy.PfxxJson[zcy.PfxxJson.length - 1].sysddw;
                        //yppf.yyts = '1';
                    }
                    zcy.PfxxJson.push(yppf);
                }
            } else {
                zcy.PfxxJson.push(yppf);
            }
            var cou = zcy.PfxxJson.length - 1;
            setTimeout(function () {   //延时0.1秒执行
                $("#fzh" + cou).focus();
                // zcy.num=zcy.PfxxJson.length-1
                $("input[name='text']").each(function () {
                    $(this).removeClass('input-border');
                    $(this).attr('disabled', false)
                });
            }, 100);
        },

        hover: function (index, event) {
            if (event.shiftKey) {
                this.numClass = null;
                zcy.checkAll = false;
                Vue.set(zcy.isCheck, index, true)
            } else {
                zcy.checkAll = false
                zcy.isCheck = [];
                this.numClass = index;
            }
        },
        tabBg: function (index) {
            this.num = index;
            // Yfwidth();
        },


        //查询抗生素申请记录
        getKssSq: function (item) {
            if (item.kssjb && item.kssjb > 2 && panel.is_csqx.cs003003200118 == '1') {
                var ypbm = null;
                if (item.xmbm) {
                    ypbm = item.xmbm;
                } else {
                    ypbm = item.ypbm;
                }
                var json = {
                    rows: 50000,
                    zyh: userNameBg.Brxx_List.zyh,
                    ypbm: ypbm
                };
                var result;
                $.ajax({
                    type: "get",
                    url: '/actionDispatcher.do?reqUrl=New1ZyysYsywKsssyba&types=query&parm=' + JSON.stringify(json),
                    cache: false,
                    async: false,
                    success: function (data) {
                        var json = JSON.parse(data)
                        if (json.a == 0) {
                            if (json.d.list.length > 0) {
                                zcy.kssContent = json.d.list[0];
                            }
                            result = zcy.pubKss(item)
                            return result
                        } else {
                            malert("获取项目列表失败！", data.c, 'top', 'defeadted');
                            return true
                        }
                    }

                });
            } else {
                return true
            }

        },

        pubKss: function (json) {
            if (json.kssjb && json.kssjb != '1' && this.csqxContent.cs003003200118 == '1') {
                //针对抗生素药物的判定（住院医生（01）一级（2），主治医生（03）一（2）、二（3）级，副主任医师（02）和主任医师（01）一（2）、二（3）、三（4）级）
                if (window.top.J_tabLeft.jszgdm >= 4 || !window.top.J_tabLeft.jszgdm) {
                    if (json.kssjb == '3' || json.kssjb == '4') {
                        malert(json['ypmc'] + "属于" + zcy.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                        return false;
                    }
                } else if (window.top.J_tabLeft.jszgdm == '03') {
                    if (json.kssjb == '3' || json.kssjb == '4') {//根据抗生素级别需要申请
                        if (!zcy.kssContent) {
                            malert(json['ypmc'] + "属于" + zcy.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                            return false;
                        } else if (zcy.kssContent.spbz != 1) {//审核通过才能使用
                            malert(json['ypmc'] + "属于" + zcy.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                            return false
                        }
                    }
                } else if (window.top.J_tabLeft.jszgdm == '02') {
                    if (json.kssjb == '4') {
                        malert(json['ypmc'] + "属于" + zcy.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                        return false;
                    }
                }
                zcy.kss = true;
                setTimeout(function () {
                    zcy.$refs.kss.setLiShow(zcy.$refs.kss.$refs.inputFu);
                    zcy.$refs.kss.setLiFirst(zcy.$refs.kss.$refs.inputFu);
                }, 1000);
                return true
            } else {
                return true
            }
        },

        //获取袋子费
        Wf_getDzf: function () {
            //先查找配方中是否已存在煎药袋
            var jyfparm = {
                page: 1,
                rows: 10,
                parm: 'Y02090588'
            };
            var json = {
                yfbm: chose.serchContent.yfbm,
                fybzdw: fyxmTab.csqx.cs03001200132

            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(jyfparm) + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (data.d.list.length > 0) {
                        //parseInt(this.zcyList[i].yyjl || 0 ) / parseInt(this.zcyList[i].jbjl || 0 ) * zyfs;
                        var zyfs = parseInt(Number(zcy.cfcontent.zyfs || 0)).toFixed(2);
                        var ypjson = data.d.list[0];
                        ypjson.yyjl = 1;
                        ypjson.jbjl = 1;
                        zcy.zcyList.push(ypjson);
                    }
                }
            });
        },

        //获取频次编码
        Wf_getPcData: function () {
            var pc_dg = {page: 1, rows: 100, sort: "sor", order: "asc", parm: ""};
            $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmpc&types=query&dg=" + JSON.stringify(pc_dg), function (json) {
                if (json.a == 0) {
                    zcy.PcData = json.d.list;  //频次下拉窗口绑定数据
                } else {

                    malert('频次列表查询失败' + json.c, 'top', 'defeadted')
                    return;
                }
            });
        },
        //获取西药用药方法
        Wf_getYyffData: function (req, list) {
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yyff" + '&json=' + JSON.stringify(req),
                function (json) {
                    if (json.a == 0) {
                        zcy[list] = json.d.list;
                    } else {
                        malert('用药方法列表查询失败' + json.c, 'top', 'defeadted')
                        return;
                    }
                });
        },
        //值改变事件
        //药品
        Wf_change: function (add, index, type, val) {
		                this.popContent = {};
            dqindex = index;
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            // var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var parm = "";

            if(!val){
                return false;
            }

            if (type == "xy") {
                var _searchEvent = $("#ypmc" + index + " + div");
                this.PfxxJson[index].ypbm = null;
                this.PfxxJson[index].ypmc = null;
                this.PfxxJson[index]['ypmc'] = val;
                this.page.parm = trimStr(encodeURIComponent(val));
                if(chose.serchContent.yfbm != "01" && chose.serchContent.yfbm!='02' )
                {
                    var json = {
                        yfbm: chose.serchContent.yfbm,
                        cflx: chose.serchContent.cflxbm,
                        fybzdw: fyxmTab.csqx.cs03001200132,
                        jslyp: this.csqxContent.N05001200274[chose.serchContent.cflxbm] || '0',
                        sfsf:1,
                    };
                }else{
                    var json = {
                        yfbm: chose.serchContent.yfbm,
                        cflx: chose.serchContent.cflxbm,
                        fybzdw: fyxmTab.csqx.cs03001200132,
                        jslyp: this.csqxContent.N05001200274[chose.serchContent.cflxbm] || '0',
                    };
                }

                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(this.page) + '&json=' + JSON.stringify(json), function (data) {
                    for (var n = 0; n < data.d.list.length; n++) {
                        if (fyxmTab.csqx.cs03001200132 == "1") {
                            data.d.list[n].fydwmc1 = data.d.list[n].fydwmc || data.d.list[n].yfdwmc;
                            data.d.list[n].sjkc = Math.floor((data.d.list[n].sjkc || 0) / (data.d.list[n].fyjl || 1));
                            data.d.list[n].kcsl = Math.floor((data.d.list[n].kcsl || 0) / (data.d.list[n].fyjl || 1));
                        } else {
                            data.d.list[n].fydwmc1 = data.d.list[n].yfdwmc;
                        }
                        if (data.d.list[n].gjjbyw == '1') {
                            data.d.list[n].jbywbz = '国基'
                            if (data.d.list[n].sbjbyw == '1') {
                                data.d.list[n].jbywbz = '国基•省补'
                            }
                        } else if (data.d.list[n].sbjbyw == '1') {
                            data.d.list[n].jbywbz = '省补'
                        } else {
                            data.d.list[n].jbywbz = '否'
                        }
                    }
                    if (add) {//不是第一页则需要追加
                        for (var i = 0; i < data.d.list.length; i++) {
                            zcy.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        zcy.searchCon = data.d.list;
                    }
                    zcy.page.total = data.d.total;
                    zcy.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
            } else if (type == "zy") {
                var _searchEvent = $("#xmsr_" + index + " + div");
                this.zcyList[index].ypbm = null;
                this.zcyList[index].ypmc = null;
                this.zcyList[index]['ypmc'] = val;
                this.page.parm = trimStr(val);
                var json = {
                    yfbm: chose.serchContent.yfbm,
                    cflx: chose.serchContent.cflxbm
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(this.page) + '&json=' + JSON.stringify(json), function (data) {
                    if (add) {//不是第一页则需要追加
                        for (var i = 0; i < data.d.list.length; i++) {
                            zcy.searchCon2.push(data.d.list[i]);
                        }
                    } else {
                        zcy.searchCon2 = data.d.list;
                    }
                    zcy.page.total = data.d.total;
                    zcy.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
            }
        },
        //键盘事件
        changeDown: function (index, event, type, searchCon) {
            // this.prevFocus(event)
            if (type == 'zy') {
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
					this.nextFocus(event, 1);
                    if (this.zcyList[index]['ypbm'] && index == this.zcyList.length - 1 && !event.srcElement.value == '') {
                        //this.add(event, true, 1)
                        //return;
                        // this.nextFocus(event,5);
                    } else if (this.zcyList[index]['ypbm'] && !event.srcElement.value == '') {
                        //this.nextFocus(event, 1);
                    }
                }
            }
            //赋值
            this.inputUpDown(event, searchCon, 'selSearch');
            this.popContent = this[searchCon][this.selSearch];
            ypIndex = index;
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if(this.PfxxJson[index] && this.PfxxJson[index].ypbm){
                    this.nextSelect(event)
                }else if(!this.popContent){
                    malert("请输入药品检索", "top", "defeadted")
                    return false
                }
                // if (this.popContent.ypbm) {
                // this.add()
                // this.nextFocus(event);
                // this.nextFocus(event,5);
                // }
                //zcy.pubKss(zcy.popContent); //判断抗生素是否允许使用
                if (type == "xy") {
                    if( this.popContent && this.popContent.sfsh == '1'){
                        malert(""+this.popContent.ypmc+"需要审核，请知晓", "top", "defeadted")
                    }

                    if (this.PfxxJson && this.PfxxJson[index]['ypbm'] != undefined) {
                        this.nextFocus(event)
                    } else {
                        // if (!this.pubKss(zcy.popContent)) return; //对抗生素药物使用的判定
                        var i = null;
                        for (var j = 0; j < this.PfxxJson.length; j++) {
                            if (this.popContent.ypbm && this.popContent.ypbm == this.PfxxJson[j].ypbm) {
                                i = j;
                            }
                        }
                        //先暂存数据
                        var pfxx = this.PfxxJson[index];
                        if (i != null) {
                            malert("第【" + (i + 1) + "】行药品【" + this.popContent.ypmc + "】已经存在", 'top', 'defeadted')
                            // ypfz();
                            // common.openConfirm("第【" + (i + 1) + "】行药品【" + this.popContent.ypmc + "】已经存在，确定需要添加吗？", ypfz);
                            // $('input').blur();
                        } else {
                            // ypfz();
                        }
                        // function ypfz() {
                        this.PfxxJson[index] = this.popContent;
                        if (this.csqxContent.N03001200156 == '1') {
                            this.PfxxJson[index].yyjl = this.popContent.jbjl;
                        }
                        if (zcy.csqxContent.N03001200165) {
                            if (this.popContent.ypzl == zcy.csqxContent.N03001200165) {
                                if (!zcy.zzrsywValidate('zaoqi')) {
                                    malert("您无权限开具此类早期终止妊娠药物！", "top", "defeadted");
                                    this.PfxxJson[index] = {};
                                    return false;
                                }
                            }
                        }
                        if (zcy.csqxContent.N03001200166) {
                            if (this.popContent.ypzl == zcy.csqxContent.N03001200166) {
                                if (!zcy.zzrsywValidate('zhongqi')) {
                                    malert("您无权限开具此类中期终止妊娠药物！", "top", "defeadted");
                                    this.PfxxJson[index] = {};
                                    return false;
                                }
                            }
                        }
                        if (zcy.csqxContent.N03001200169) {
                            if (zcy.csqxContent.N03001200169.indexOf(this.popContent.ypzl) != -1) {
                                if (!zcy.mzdyljsywValidate()) {
                                    malert("您无权限开具此类麻醉或第一类精神药物！", "top", "defeadted");
                                    this.PfxxJson[index] = {};
                                    return false;
                                }
                            }
                        }
                        this.$forceUpdate()
                        this.nextFocus(event);
                        // }
                        //进行数据的处理
                        this.PfxxJson[index].fzh = this.PfxxJson[index].fzh || pfxx.fzh;

                        this.PfxxJson[index].yypcmc = this.PfxxJson[index].yypcmc || pfxx.yypcmc;
                        this.PfxxJson[index].yypc = this.PfxxJson[index].yypc || pfxx.yypc;
                        this.PfxxJson[index].yypccs = this.PfxxJson[index].yypccs || pfxx.yypccs;
                        this.PfxxJson[index].yyffmc = this.PfxxJson[index].yyffmc || pfxx.yyffmc;
                        this.PfxxJson[index].yyff = this.PfxxJson[index].yyff || pfxx.yyff;
                        this.PfxxJson[index].sysd = this.PfxxJson[index].sysd || pfxx.sysd;
                        this.PfxxJson[index].sysddw = this.PfxxJson[index].sysddw || pfxx.sysddw;

                        


                        if(chose.Brxx_List.fbbm=='40'){
                            this.PfxxJson[index].yyts = this.PfxxJson[index].yyts || pfxx.mtyyts;
                            this.PfxxJson[index].mtksrq = this.PfxxJson[index].mtksrq || pfxx.mtksrq;
                            this.PfxxJson[index].mtjsrq = this.PfxxJson[index].mtjsrq || pfxx.mtjsrq;
                        }else{
                            this.PfxxJson[index].yyts = pfxx.yyts || 1;
                        }


                    }
                    if (this.csqxContent.cs003003200118 == '1' && this.PfxxJson[index].kssjb == 2) {
                        this.kssModelShow = true;
                        this.$nextTick(function () {
                            loadPage('.kssChildModel', '/newzui/page/CommonPage/kss/symd', function () {
                            });
                        })
                    }
                    if (this.csqxContent.cs003003200118 == '1' && !this.ysbm[window.top.J_tabLeft.jszgdm] && !this.getKss(this.PfxxJson[index].ypbm) &&this.PfxxJson[index].kssjb >= 3) {
                        this.kssModelShow = true;
                        this.$nextTick(function () {
                            loadPage('.kssChildModel', '/newzui/page/CommonPage/kss/sqKss', function () {
                                zcy.PfxxJson[index]={}
                            });
                        })
                    }
                }
                else if (type == "zy") {
                    if (this.zcyList && this.zcyList[index]['ypbm'] != undefined) {
                        this.nextFocus(event, 1)

                    } else {
											var i = null;
					                        for (var j = 0; j < this.zcyList.length; j++) {
					                            if (this.popContent.ypbm == this.zcyList[j].ypbm) {
					                                i = j;
					                            }
					                        }
					                        if (i != null) {
					                            malert("第【" + (i + 1) + "】行药品【" + this.popContent.ypmc + "】已经存在", 'top', 'defeadted')
					                            
					                        } else {
					                            
					                        }
											this.zcyList[index] = this.popContent;
											                        this.$forceUpdate()
											                        if (this.zcyList[index]['ypbm'] && index == this.zcyList.length - 1 && !event.srcElement.value == '') {
											                            //this.add(event, true, 1)
											                        } else if (!event.srcElement.value == '') {
											                            this.nextFocus(event, 1);
											                        }
                        
                    }
                }
                this.selSearch = -1;
                $(".selectGroup").hide();
            }
        },
        //终止妊娠药物验证
        zzrsywValidate: function (type) {
            var right = false;
            if (type == 'zaoqi') {
                if (zcy.csqxContent.N03001200167 && (zcy.csqxContent.N03001200167.indexOf(userId) != -1)) {
                    right = true;
                }
            } else {
                if (zcy.csqxContent.N03001200168 && (zcy.csqxContent.N03001200168.indexOf(userId) != -1)) {
                    right = true;
                }
            }
            return right;
        },
        //麻醉第一类精神药物验证
        mzdyljsywValidate: function () {
            var right = false;
            if (zcy.csqxContent.N03001200170 && (zcy.csqxContent.N03001200170.indexOf(userId) != -1)) {
                right = true;
            }
            return right;
        },
        //鼠标西藥双击选中事件
        selectOne1: function (item) {
		                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.Wf_change(true, dqindex, 'xy', this.PfxxJson[dqindex]['ypmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                this.popContent = item
                var i = null;
                for (var j = 0; j < zcy.PfxxJson.length; j++) {
                    if (item.ypbm == zcy.PfxxJson[j].ypbm) {
                        i = j;
                    }
                }
                var pfxx = zcy.PfxxJson[dqindex];
                if (i != null) {
                    malert("第【" + (i + 1) + "】行药品【" + this.popContent.ypmc + "】已经存在", 'top', 'defeadted')
                    // common.openConfirm("第【" + (i + 1) + "】行药品【" + item.ypmc + "】已经存在，确定需要添加吗？", ypfz2);
                    // $('input').blur();
                } else {
                    // ypfz2()
                }
                // function ypfz2() {
                zcy.PfxxJson[dqindex] = item;
                if (this.csqxContent.N03001200156 == '1') {
                    this.PfxxJson[dqindex].yyjl = item.jbjl;
                }
                if (zcy.csqxContent.N03001200165) {
                    if (item.ypzl == zcy.csqxContent.N03001200165) {
                        if (!zcy.zzrsywValidate('zaoqi')) {
                            malert("您无权限开具此类早期终止妊娠药物！", "top", "defeadted");
                            zcy.PfxxJson[dqindex] = {};
                            return false;
                        }
                    }
                }
                if (zcy.csqxContent.N03001200166) {
                    if (item.ypzl == zcy.csqxContent.N03001200166) {
                        if (!zcy.zzrsywValidate('zhongqi')) {
                            malert("您无权限开具此类中期终止妊娠药物！", "top", "defeadted");
                            zcy.PfxxJson[dqindex] = {};
                            return false;
                        }
                    }
                }
                if (zcy.csqxContent.N03001200169) {
                    if (zcy.csqxContent.N03001200169.indexOf(item.ypzl) != -1) {
                        if (!zcy.mzdyljsywValidate()) {
                            malert("您无权限开具此类麻醉或第一类精神药物！", "top", "defeadted");
                            zcy.PfxxJson[dqindex] = {};
                            return false;
                        }
                    }
                }
                zcy.$forceUpdate()
                // }
                //进行数据的处理
                this.PfxxJson[dqindex].fzh = this.PfxxJson[dqindex].fzh || pfxx.fzh;

                this.PfxxJson[dqindex].yypcmc = this.PfxxJson[dqindex].yypcmc || pfxx.yypcmc;
                this.PfxxJson[dqindex].yypc = this.PfxxJson[dqindex].yypc || pfxx.yypc;
                this.PfxxJson[dqindex].yypccs = this.PfxxJson[dqindex].yypccs || pfxx.yypccs;
                this.PfxxJson[dqindex].yyffmc = this.PfxxJson[dqindex].yyffmc || pfxx.yyffmc;
                this.PfxxJson[dqindex].yyff = this.PfxxJson[dqindex].yyff || pfxx.yyff;
                this.PfxxJson[dqindex].sysd = this.PfxxJson[dqindex].sysd || pfxx.sysd;
                this.PfxxJson[dqindex].sysddw = this.PfxxJson[dqindex].sysddw || pfxx.sysddw;

                

                if(chose.Brxx_List.fbbm=='40'){
                    this.PfxxJson[dqindex].yyts = this.PfxxJson[dqindex].yyts || pfxx.mtyyts;
                    this.PfxxJson[dqindex].mtksrq = this.PfxxJson[dqindex].mtksrq || pfxx.mtksrq;
                    this.PfxxJson[dqindex].mtjsrq = this.PfxxJson[dqindex].mtjsrq || pfxx.mtjsrq;
                }else{
                    this.PfxxJson[dqindex].yyts = pfxx.yyts || 1;
                }

                this.selSearch = -1;
                if (this.csqxContent.cs003003200118 == '1' && this.PfxxJson[dqindex].kssjb == 2) {
                    this.kssModelShow = true;
                    this.$nextTick(function () {
                        loadPage('.kssChildModel', '/newzui/page/CommonPage/kss/symd', function () {
                        });
                    })
                }

                if (this.csqxContent.cs003003200118 == '1' && !this.ysbm[window.top.J_tabLeft.jszgdm] && !this.getKss(this.PfxxJson[dqindex].ypbm) && this.PfxxJson[dqindex].kssjb >= 3) {
                    this.kssModelShow = true;
                    this.$nextTick(function () {
                        loadPage('.kssChildModel', '/newzui/page/CommonPage/kss/sqKss', function () {
                            zcy.PfxxJson[dqindex]={}
                        });
                    })
                }
                this.nextFocus(event);
                $(".selectGroup").hide();
            }
        },
        getKss:function(ypbm){
            var result;
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1YlfwptGssgl&types=ksssyqx&ysbm=" + userId+"&ypbm=" + ypbm+"&zyh=" + userNameBg.Brxx_List.ghxh, function (data) {
                if (data.a == 0) {
                    result=data.d
                } else {
                }
            })
            return result
        },
        //鼠标中药双击选中事件
        selectOne2: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.Wf_change(true, dqindex, 'zy', this.zcyList[dqindex]['ypmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                // if (!zcy.pubKss(item)) return; //对抗生素药物使用的判定
                //zcy.pubKss(item); //判断抗生素是否允许使用
                var i = null;
                for (var j = 0; j < zcy.zcyList.length; j++) {
                    if (item.ypbm == zcy.zcyList[j].ypbm) {
                        i = j;
                    }
                }
                if (i != null) {
                    malert("第【" + (i + 1) + "】行药品【" + this.popContent.ypmc + "】已经存在", 'top', 'defeadted')
                    // common.openConfirm("第【" + (i + 1) + "】行药品【" + item.ypmc + "】已经存在，确定需要添加吗？", ypfz3);
                    // $('input').blur();
                } else {
                    // ypfz3()
                }
                // var yyff = zcy.zcyList[dqindex].yyff;
                // var yyffbm = zcy.zcyList[dqindex].yyffbm;
                // var yyffmc = zcy.zcyList[dqindex].yyffmc;

                zcy.zcyList[dqindex] = item;
                zcy.$forceUpdate()

                // zcy.zcyList[dqindex].yyff = yyff;
                // zcy.zcyList[dqindex].yyffbm = yyffbm;
                // zcy.zcyList[dqindex].yyffmc = yyffmc;


                this.selSearch = -1;
                this.nextFocus(event, 3);
                $(".selectGroup").hide();
            }
        },

        setLczd: function () {

            var str_param = {
                ghxh: userNameBg.Brxx_List.ghxh,
                brid: userNameBg.Brxx_List.brid
            };

            let that = this;

            that.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1GhglGhywBrgh',
                    types: 'queryOne',
                    parm: JSON.stringify(str_param)
                }
            }).then(function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    let zdxx = json.body.d.list[0].ghxx;
                    zdxx.fjzd=chose.fjzdlist;

                    userNameBg.Brxx_List.rybh = zdxx.rybh

                    userNameBg.Brxx_List.qtzdbm = zdxx.qtzdbm;
                    userNameBg.Brxx_List.qtzdbm1 = zdxx.qtzdbm1;
                    userNameBg.Brxx_List.qtzdbm2 = zdxx.qtzdbm2;
                    userNameBg.Brxx_List.qtzdbm3 = zdxx.qtzdbm3;
                    userNameBg.Brxx_List.qtzdbm4 = zdxx.qtzdbm4;
                    userNameBg.Brxx_List.qtzdmc = zdxx.qtzdmc;
                    userNameBg.Brxx_List.qtzdmc1 = zdxx.qtzdmc1;
                    userNameBg.Brxx_List.qtzdmc2 = zdxx.qtzdmc2;
                    userNameBg.Brxx_List.qtzdmc3 = zdxx.qtzdmc3;
                    userNameBg.Brxx_List.qtzdmc4 = zdxx.qtzdmc4;

                    var zd ='';
                    (userNameBg.Brxx_List.zdsm1 || '')
                    if(userNameBg.Brxx_List.zdsm1){
                        zd+=userNameBg.Brxx_List.zdsm1
                    }
                    if(userNameBg.Brxx_List.jbbm){
                        zd+='('+userNameBg.Brxx_List.jbbm+')'+userNameBg.Brxx_List.jbmc
                    }
                    if(userNameBg.Brxx_List.zdsm2){
                        zd+= userNameBg.Brxx_List.zdsm2
                    }
                    if(userNameBg.Brxx_List.qtzdbm){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm+')'+( userNameBg.Brxx_List.qtzdmc || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm1){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm1+')'+( userNameBg.Brxx_List.qtzdmc1 || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm2){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm2+')'+( userNameBg.Brxx_List.qtzdmc2 || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm3){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm3+')'+( userNameBg.Brxx_List.qtzdmc3 || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm4){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm4+')'+( userNameBg.Brxx_List.qtzdmc4 || '')
                    }
                    if(userNameBg.Brxx_List.fjzd){
                        let fjzd = JSON.parse(userNameBg.Brxx_List.fjzd)
                        for (let i = 0; i < fjzd.length; i++) {
                            zd+=',('+fjzd[i].jbmb+')'+( fjzd[i].jbmc || '')
                        }

                    }
                    chose.lczd = zd.replace(/,null/g,"");

                }
            })



        },



        //新增加处方
        addCf: function (event) {
			
            //先将编辑处方号清空
            if (brjzFoot.edit_cfh != '') {
                brjzFoot.edit_cfh = ''
            }

            chose.InShow = true;
            // brjzFoot.saveShow=true;
            // brjzFoot.editTitle='保存 ';
            var fhvalue = true;
            //判断是否存在没有保存的处方信息
            if (zcy.cfList.length > 0) {
                for (var i = 0; i < zcy.cfList.length; i++) {
                    if (zcy.cfList[i].cfh == null || zcy.cfList[i].cfh == "") {
                        // malert('请先保存新处方', 'top', 'defeadted');
                        return false;
                    }
                }
            }
            if (event) {
                if (userNameBg.Brxx_List.mryfbm) {
                    chose.serchContent.yfbm = userNameBg.Brxx_List.mryfbm;
                    chose.CflxJosn = jsonFilter(chose.yfyycflx, "yfbm", chose.serchContent.yfbm);
                }
				
                if (userNameBg.Brxx_List.mrcfbm) {
                    chose.serchContent.cflxbm = userNameBg.Brxx_List.mrcfbm;
                    chose.getCflb();
                }
            }

            //进行一系列清空
            zcy.cfcontent = {};  //清空当前处方信息
            zcy.PfxxJson = [];    //清空配方列表内容
            zcy.zcyList = [];
            zcy.extraChargesList = [];    //清空附加费用列表信息
            //获取疾病信息
            if (userNameBg.Brxx_List.jbmc) {
                // @yqq 台江需求：将诊断描述和其他诊断全部显示在临床诊断中
                this.setLczd()
            } else {
                //去传输过来的疾病信息
                if (zcy.cfList.length > 0) {
                    chose.lczd = zcy.cfList[zcy.cfList.length - 1].lczd;
                }
            }

        // if(userNameBg.Brxx_List.fbbm =='40' || userNameBg.Brxx_List.fbbm =='41'){
            chose.myzdlist = {
                zd1:null,
                zd2:null,
                zd3:null,
                zd4:null,
                zd5:null,
                zd6:null,
            }
            chose.iszdchecked=[]
            chose.popContent = {
                mtbzmc:'',
                mtbzbm:'',

            }
            chose.lczd = '';
        // }
            chose.serchContent.sflzcf = '0'

            zcy.fjfyIsShow = true; //显示添加附加费用按钮
			
			if(chose.serchContent.cflxbm == '01' && userNameBg.Brxx_List.ghksmc.indexOf('急诊') != -1){
				chose.serchContent.cflxbm = '06';
			}
			
            chose.serchContent.cflxmc = zcy.listGetName(chose.CflxJosn, chose.serchContent.cflxbm, 'cflxbm', 'cflxmc');
            zcy.cfcontent = {
                cflxbm: chose.serchContent.cflxbm,
                cflxmc: chose.serchContent.cflxmc,
                cfh: '',
                dmcfbz: chose.serchContent.dmcfbz
            };
            zcy.cfcontent.index = zcy.cfList.length;  //当前处方行数
            zcy.cfList.push({cflxbm: chose.serchContent.cflxbm, cflxmc: chose.serchContent.cflxmc, cfh: ''});  //增加一个处方
            brjzFoot.msgContent = {
                cfh: '空',
                cfje: 0,
                CF_Fjhj: 0
            }; //清空底部金额
            //获取主病
            if (userNameBg.Brxx_List.zyzh != null) {
                Vue.set(zcy.cfcontent, 'zyzh', userNameBg.Brxx_List.zyzh);
            } else {
                //去传输过来的主病信息
                if (zcy.cfList.length > 0) {
                    Vue.set(zcy.cfcontent, 'zyzh', zcy.cfList[zcy.cfList.length - 1].zyzh);
                }
            }
            //获取症型
            if (userNameBg.Brxx_List.zyzf != null) {
                Vue.set(zcy.cfcontent, 'zyzf', userNameBg.Brxx_List.zyzf);
            } else {
                //去传输过来的症型信息
                if (zcy.cfList.length > 0) {
                    Vue.set(zcy.cfcontent, 'zyzf', zcy.cfList[zcy.cfList.length - 1].zyzf);
                }
            }
            //判断是西药还是中药处方
            // && chose.serchContent.cflxbm =='02'
            if (chose.serchContent.cflb == 2) {//中药处方
                zcy.zyShow = true;
                zcy.xyShow = false;
                zcy.cfcontent.zyfs = 1;
                this.add();
            } else {
                zcy.zyShow = false;
                zcy.xyShow = true;
                zcy.addYh();//调用新增一行药品
            }

            if (chose.serchContent.dmcfbz == 1) {
                //毒麻处方标志
                dmPop.popShow = true;
                dmPop.popTitle = '毒麻处方提示';
                if (zcy.csqxContent.N03001200129 == 1) {
                    zcy.dmsmxx = true;
                }
            } else {
                zcy.dmsmxx = false;
            }

            setTimeout(function () {
                $("input[name='text']").each(function () {
                    $(this).removeClass('input-border');
                    $(this).attr('disabled', false)
                })
                zcy.tabShowActive()
                zcy.num = zcy.cfList.length - 1
                zcy.addShow[zcy.num] = true;
                zcy.$forceUpdate()
            }, 50);

            return fhvalue;
        },
        //附加费用
        AddFy: function () {
            $('.side-form').css({'width': '320px'});
            brzcList.open();
            brzcList.title = '添加附加费用';
            brzcList.saveTitle = '保存';
            brzcList.cfShow = false;
            brzcList.ypShow = false;
            brzcList.wShow = false;
            brzcList.zdShow = false;
            brzcList.hzShow = false;
            brzcList.lsShow = false;
            brzcList.tjShow = true;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'hidden'})
            $(".blRight").hide();
            brzcList.Wf_getFjfy();
        },
        //附加费数量改变
        Wf_fjfChange: function (index) {
            if (zcy.extraChargesList[index].fysl <= 0) {
                malert("附加费用数量格式不正确", 'top', 'defeadted');
                zcy.extraChargesList[index].fysl = 1;
            }
            var fyje = zcy.extraChargesList[index].fydj * zcy.extraChargesList[index].fysl;
            Vue.set(zcy.extraChargesList[index], 'fyje', fyje);     //附加费金额
            zcy.$forceUpdate();

        },
        //删除附加费
        removeExtra: function (index) {
            if (brjzFoot.msgContent.cfh == undefined || brjzFoot.msgContent.cfh == null || brjzFoot.msgContent.cfh == '') {
                malert("已保存处方不能增加或删除附加费用信息！", 'top', 'defeadted');
                return;
            }
            zcy.extraChargesList.splice(index, 1);  //删除当前行附加费
        },
        //处方调用
        Cfdy: function () {
            brzcList.open();
            $('.side-form').css({'width': '805px'});
            brzcList.saveTitle = '确定'
            brzcList.title = '处方调用';
            brzcList.cfShow = true;
            brzcList.wShow = true;
            brzcList.tjShow = false;
            brzcList.ypShow = false;
            brzcList.zdShow = false;
            brzcList.hzShow = false;
            brzcList.lsShow = false;
            brzcList.cfList = []
            this.param.page='1'
            brzcList.getTemData();
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'hidden'})
            //此处判断是否有未保存的处方信息
            // for (var i = 0; i < zcy.cfList.length; i++) {
            //     if (zcy.cfList[i].cfh == null || zcy.cfList[i].cfh == "") {
            //         zcy.cfList.splice(i, 1);
            //         zcy.PfxxJson.splice(i, 1);
            //         zcy.Wf_selectCFMX(zcy.cfList[zcy.cfList.length - 1].cfh, zcy.cfList.length - 1);
            //         setTimeout(function () {
            //             Yfwidth();
            //         },100)
            //     }
            // }
            // zcy.num = zcy.cfList.length - 1

        },
        //历史处方
        Lscf: function () {
            brzcList.open();
            $('.side-form').css({'width': '930px'});
            brzcList.saveTitle = '引用'
            brzcList.title = '历史处方 红色为作废处方';
            brzcList.cfShow = false;
            brzcList.wShow = true;

            brzcList.tjShow = false;
            brzcList.ypShow = false;
            brzcList.hzShow = false;
            brzcList.zdShow = false;
            brzcList.lsShow = true;
            this.param.page='1'
            brzcList.getLsTemData();
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'hidden'})
            // $('.blRight').hide();
            //此处判断是否有未保存的处方信息
            // for (var i = 0; i < zcy.cfList.length; i++) {
            // if (zcy.cfList[i].cfh == null || zcy.cfList[i].cfh == "") {
            // zcy.cfList.splice(i, 1);
            // zcy.PfxxJson = [];
            // setTimeout(function () {
            //     Yfwidth();
            // },100)
            // }
            // }
        },
        mtcfFun:function (){
            brzcList.open();
            $('.side-form').css({'width': '805px'});
            brzcList.getFaList();
            brzcList.$forceUpdate();
            brzcList.saveTitle = '引用';
            brzcList.title = '门特处方';
            brzcList.cfShow = false;
            brzcList.wShow = true;
            brzcList.tjShow = false;
            brzcList.ypShow = false;
            brzcList.hzShow = false;
            brzcList.mtch = true;
            brzcList.lsShow = false;
        },
        //诊断处方
        zdCf: function () {
            brzcList.open();
            $('.side-form').css({'width': '805px'});
            brzcList.saveTitle = '确定';
            brzcList.title = '诊断处方';
            brzcList.cfShow = false;
            brzcList.wShow = true;
            brzcList.tjShow = false;
            brzcList.ypShow = false;
            brzcList.hzShow = false;
            brzcList.zdShow = true;
            brzcList.lsShow = false;
            brzcList.getLczdDate();
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'hidden'})
        },
        //药品选择
        Ypxz: function () {
            //判断当前处方是否是新开处方
            if (zcy.cfcontent.cfh == null || zcy.cfcontent.cfh == "") {
                for (var i = 0; i < zcy.PfxxJson.length; i++) {
                    if (zcy.PfxxJson[i].ypbm == undefined || zcy.PfxxJson[i].ypbm == null || zcy.PfxxJson[i].ypbm == "") {
                        zcy.PfxxJson.splice(i, 1);  //直接删除
                    }
                }
                for (var i = 0; i < zcy.zcyList.length; i++) {
                    if (zcy.zcyList[i].ypbm == undefined || zcy.zcyList[i].ypbm == null || zcy.zcyList[i].ypbm == "") {
                        zcy.zcyList.splice(i, 1);  //直接删除
                    }
                }
                brzcList.title = '药品选择';
                brzcList.saveTitle = '确定';
                $('.side-form').css({'width': '805px'});
                brzcList.open();
                brzcList.ypShow = true;
                brzcList.wShow = true;
                brzcList.cfShow = false;
                brzcList.hzShow = false;
                brzcList.zdShow = false;
                $('.blRight').hide();
                $('.hzzx-top').css({'z-index': '0'})
                $('body').css({'overflow': 'hidden'})
                brzcList.lsShow = false;
                brzcList.tjShow = false;
                brzcList.Wf_getYpxx();
            } else {
                malert("已保存处方不能进行配方信息的添加！", 'top', 'defeadted');
                return false;
            }

        },

        //根据挂号序号检索中药、西药处方信息
        Wf_selectCF: function (num) {
            this.cfList = [];  //清空所有处方集合
            this.cfcontent = {};  //清空当前处方信息
            this.PfxxJson = [];    //清空配方列表内容
            this.zcyList = [];
            this.extraChargesList = [];    //清空附加费用列表信息
            this.fjfyIsShow = false; //显示添加附加费用按钮
            var ghxh = userNameBg.Brxx_List.ghxh;
            //判断挂号序号是否为空
            if (!ghxh) {
                return;
            }
            //后台查询数据
            var parm = {
                bah: ghxh
            };
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectCF&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        this.xycfList = [];
                        for (var i = 0; i < data.d.list.length; i++) {
                            if (data.d.list[i].fybz == '0' && data.d.list[i].kfbz == '0') {
                                data.d.list[i].fybz = '0'
                            }
                            if (data.d.list[i].cfje >= 0) {
                                this.cfList.push(data.d.list[i]);  //西药处方目录
                            }
                        }
                        // if (num == undefined || num == null) {
                        //给第一张处方赋值
                        var cfh = data.d.list[0].cfh;
                        this.Wf_selectCFMX(cfh, zcy.num);             //查询处方明细
                        // setTimeout(function () {            //加载完处方后加载该js
                        //     Yfwidth();
                        // },50)
                        // } else {
                        //     //给第一张处方赋值
                        //     var cfh = data.d.list[zcy.cfList.length - 1].cfh;
                        //     zcy.Wf_selectCFMX(cfh, zcy.cfList.length - 1);             //查询处方明细
                        //     // setTimeout(function () {      //加载完处方后加载该js
                        //     //     Yfwidth();
                        //     // },50)
                        // }
                    } else {
                        this.xycfList = [];
                        this.PfxxJson = [];    //清空配方编辑区
                        this.zcyList = [];
                        this.addCf();
                    }
                } else {
                    malert("处方查询失败" + data.c, 'top', 'defeadted');
                }
            })
        },

        // 根据处方号查询药品信息
        Wf_selectCFMX: function (cfh, index) {
            chose.myzdlist = {
            				zd1:null,
            				zd2:null,
            				zd3:null,
            				zd4:null,
            				zd5:null,
            				zd6:null,
            			}

            chose.iszdchecked =[]


						chose.popContent = {
							mtbzmc:'',
							mtbzbm:'',
							
						}
                        if (cfh != '') {    //不知道你为啥写这个，给你注释了，
                            zcy.addShow[index] = false;
                            chose.serchContent.yfbm = this.cfList[index].yfbm;
                            chose.cfglData();
            				
            				if(this.cfList[index].lczd.indexOf(chose.Brxx_List.jbbm) != -1){
            					Vue.set(chose.iszdchecked,'90','true')
            				}
            				if(chose.Brxx_List.qtzdbm && this.cfList[index].lczd.indexOf(chose.Brxx_List.qtzdbm) != -1){
            					Vue.set(chose.iszdchecked,'91','true')
            				}
            				if(chose.Brxx_List.qtzdbm1 && this.cfList[index].lczd.indexOf(chose.Brxx_List.qtzdbm1) != -1){
            					Vue.set(chose.iszdchecked,'92','true')
            				}
            				if(chose.Brxx_List.qtzdbm2 && this.cfList[index].lczd.indexOf(chose.Brxx_List.qtzdbm2) != -1){
            					Vue.set(chose.iszdchecked,'93','true')
            				}
            				if(chose.Brxx_List.qtzdbm3 && this.cfList[index].lczd.indexOf(chose.Brxx_List.qtzdbm3) != -1){
            					Vue.set(chose.iszdchecked,'94','true')
            				}
            				if(chose.Brxx_List.qtzdbm4 && this.cfList[index].lczd.indexOf(chose.Brxx_List.qtzdbm4) != -1){
            					Vue.set(chose.iszdchecked,'95','true')
            				}



                            chose.fjzdlist = JSON.parse(userNameBg.Brxx_List.fjzd)
                            console.log(chose.fjzdlist)

                            if(chose.fjzdlist){
                                for (let i = 0; i < chose.fjzdlist.length; i++) {
                                    if(chose.fjzdlist[i].jbmb && this.cfList[index].lczd.indexOf(chose.fjzdlist[i].jbmb) != -1){
                                        Vue.set(chose.iszdchecked,i+'','true')
                                    }
                                }
                            }


            				
                            chose.lczd = this.cfList[index].lczd;
							chose.popContent.mtbzbm = this.cfList[index].mtbzbm;
							chose.popContent.mtbzmc = this.cfList[index].mtbzmc;

                            chose.serchContent.sflzcf = this.cfList[index].sflzcf;
                        } else {
                        }
            // zcy.addShow[index] = false;
            zcy.PfxxJson = [];    //清空配方列表内容
            zcy.zcyList = [];
            zcy.num = index;
            // chose.resultLxChange([this.cfList[index].yfbm, null, ['serchContent', 'yfbm'], undefined, '']);
            // this.addShow[index] = false
            zcy.extraChargesList = [];    //清空附加费用列表信息
            brjzFoot.msgContent = {};
            chose.serchContent.cflxbm = this.cfList[index].cflx;
            //对当前处方进行赋值
            zcy.cfcontent = JSON.parse(JSON.stringify(zcy.cfList[index]));     // 当前处方明细信息
            if (zcy.cfcontent.isXyOrZy == '1') { //中医
                zcy.zyShow = true;
                zcy.xyShow = false;
                //取当前处方类型的打印模板
            } else {
                zcy.zyShow = false;
                zcy.xyShow = true;
                //取当前处方类型的打印模板
            }
            if (zcy.cfcontent.dmcfbz == 1 && zcy.csqxContent.N03001200129 == 1) {
                zcy.dmsmxx = true;
            } else {
                zcy.dmsmxx = false;
            }
            if (!chose.lczd) {
                this.setLczd()
                // chose.lczd = zcy.cfcontent.lczd;
            }
            zcy.cfcontent.index = index;
            if (!cfh) {
                //判断属于中医还是西医
                if (chose.serchContent.cflb == "2") {
                    zcy.zyShow = true;
                    zcy.xyShow = false;
                    zcy.zcyList = [];                               // 如果处方号为空，就清空配方编辑区内容isXyOrZy
                    this.add();
                    return;
                } else {
                    zcy.zyShow = false;
                    zcy.xyShow = true;
                    zcy.PfxxJson = [];                               // 如果处方号为空，就清空配方编辑区内容isXyOrZy
                    zcy.addYh();                                     //重新调用新增一行
                    return;
                }

            }
            var pram = {
                cfh: cfh,
                ryghxh: userNameBg.Brxx_List.ghxh,
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectCFMX&parm=" + JSON.stringify(pram), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        for (var n = 0; n < data.d.list.length; n++) {
                            if (fyxmTab.csqx.cs03001200132 == "1") {
                                data.d.list[n].fydwmc1 = data.d.list[n].fydwmc || data.d.list[n].yfdwmc;
                            } else {
                                data.d.list[n].fydwmc1 = data.d.list[n].yfdwmc;
                            }

                        }
                        if (zcy.cfcontent.isXyOrZy == '1') { //中医
                            zcy.zyShow = true;
                            zcy.xyShow = false;
                            zcy.zcyList = data.d.list;              // 配方编辑区内容列表赋值

                        } else {
                            zcy.zyShow = false;
                            zcy.xyShow = true;
                            zcy.PfxxJson = data.d.list;              // 配方编辑区内容列表赋值
                        }

                        // 处理左上角的收费标志（暂时放着不知道标志是啥）
                        if (zcy.cfcontent.kfbz == "0") {

                        } else if (zcy.cfcontent.kfbz == "1") {

                        }

                    }
                } else {
                    malert("处方明细查询失败", 'top', 'defeadted');
                }
            });
            //对底部信息进行赋值
            brjzFoot.$set(brjzFoot.msgContent, 'cfh', cfh);
            brjzFoot.$set(brjzFoot.msgContent, 'CF_Fjhj', 0);
            brjzFoot.$set(brjzFoot.msgContent, 'cfje', zcy.cfcontent.cfje);
            brjzFoot.$set(brjzFoot.msgContent, 'cfysxm', zcy.cfcontent.cfysxm);
            // 根据处方号查询处方附加费
            var parm = {
                ryghxh: userNameBg.Brxx_List.ghxh,
                yzhm: cfh,
                zfbz: '0'
            };

            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectFJF&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        var fjf_json = data.d.list;          // 配方编辑区内容赋值
                        var fjjehj = 0;
                        for (var i = 0; i < fjf_json.length; i++) {
                            fjf_json[i].mxfybm = fjf_json[i].mxfyxmbm;
                            fjf_json[i].mxfymc = fjf_json[i].mxfyxmmc;
                            fjjehj += fjf_json[i].fyje;

                        }
                        brjzFoot.$set(brjzFoot.msgContent, 'CF_Fjhj', fjjehj);
                        zcy.extraChargesList = fjf_json;
                    }
                } else {
                    malert("处方附加费查询失败", 'top', 'defeadted');
                }
            });
        },

        //**********************中药处理***********************8
        //中药单次用量回车事件
        nextAdd: function (index, event) {
            if (event.keyCode == 13) {
                // var _input = $(".zcyItem input").not(":disabled,input[type=checkbox],input[type=date]");
                // for (var i = 0; i < _input.length; i++) {
                //     if (_input.eq(i)[0] == event.currentTarget) {
                //         _input.eq(i + 1).focus();
                //     }
                //     if (this.zcyList.length - 1 == event.currentTarget.tabIndex) {
                //         this.add(event)
                //     }
                // }
                if (this.zcyList[index + 1]) {
                    this.nextFocus(event, 4);
                }
            }
        },
        //最后一个输入框回车添加中药输入框
        /* autoAdd: function (event, index) {
             if (this.zcyList.length <= 19) {
                 if (event.keyCode == 13) {
                     if (this.zcyList.length == index + 1) {
                         this.add();
                         setTimeout(function () {
                             $("#xmsr_" + (index + 1)).focus();
                         }, 100);
                     } else {
                         zcy.editIndex = index + 1;
                         setTimeout(function () {
                             $("#xmsr_" + zcy.editIndex).focus();
                         }, 100);
                     }
                 }
             }else{
                 malert("一张处方最多允许保存20位草药！");
             }
         },*/
        //添加中药配方按钮
        add: function (event, flag, num) {
            if (zcy.cfcontent.cfh == undefined) {
                malert("请新增一张处方再进行配方的添加", 'top', 'defeadted');
                return false;
            }
            //判断是否是新处方
            if (zcy.cfcontent.cfh == null || zcy.cfcontent.cfh == "") {
                // if (this.zcyList.length <= 19) {
                //药品位数判定
                if (zcy.csqxContent.N03001200107 == '1') {
                    var ypws = 24;
                    if (chose.serchContent.ypws) {
                        ypws = chose.serchContent.ypws;
                    }
                    if (this.zcyList.length >= ypws) {
                        malert("处方药品位数超过限定数量: " + ypws, 'top', 'defeadted');
                        return;
                    }
                }

                if (this.zcyList.length == 0 || this.zcyList[zcy.zcyList.length - 1]['ypbm'] != null) {
                    this.zcyList.push({});
                    this.editIndex = this.zcyList.length - 1;
                    if (this.zcyList.length == 1) {
                        if (this.zcyList[0]['ypmc'] == null || this.zcyList[0]['ypmc'] == "") {
                            this.zcyList[0]['ypmc'] = "";
                        }
                        // }else{
                        //     Vue.set(this.zcyList[this.zcyList.length - 1], 'yyffmc', this.zcyList[0].yyffmc);
                        //     Vue.set(this.zcyList[this.zcyList.length - 1], 'yyff', this.zcyList[0].yyff);
                        //     Vue.set(this.zcyList[this.zcyList.length - 1], 'yyffbm', this.zcyList[0].yyffbm);
                    }
                    for (var i = 1; i < this.zcyList.length - 1; i++) {
                        if (this.zcyList[i]['ypmc'] == null || this.zcyList[i]['ypmc'] == "") {
                            this.zcyList[i]['ypmc'] = "";
                        }
                    }

                    setTimeout(function () {
                        if (flag == true) {
                            console.log(121)
                            zcy.nextFocus(event, num);
                        } else {
                            $("#xmsr_" + (zcy.zcyList.length - 1)).focus();
                        }
                    }, 100);
                    return true
                } else {
                    malert("药品名称你忘记输入了", 'top', 'defeadted');
                    return false
                }
                // } else {
                //     malert("一张处方最多允许保存20位草药！", 'top', 'defeadted');
                // }
            } else {
                malert("已保存处方不能进行配方的添加", 'top', 'defeadted');
                return false
            }

        },

        //用药方法改变事件
        resultChange_yyff: function (val) {
            Vue.set(this.zcyList[val[2][1]], [val[2][2]], val[4]);
            Vue.set(this.zcyList[val[2][1]], 'yyff', val[0]);
            Vue.set(this.zcyList[val[2][1]], 'yyffbm', val[0]);
            if (this.zcyList[parseInt([val[2][1]]) + 1]) {
                this.nextFocus(val[1], 3);
            }
        },

        initYpmc: function () {
            for (var i = 0; i < this.zcyList.length; i++) {
                if (this.zcyList[i]['ypmc'] == null || this.zcyList[i]['ypmc'] == "") {
                    this.zcyList[i]['ypmc'] = "点击修改";
                }
                if (this.zcyList[i]['xmmc'] == null || this.zcyList[i]['xmmc'] == "") {
                    this.zcyList[i]['xmmc'] = "点击修改";
                }
            }
        },
        editDown: function (index) {
            if (this.zcyList[index]['ypmc'] == null || this.zcyList[index]['ypmc'] == "") {
                this.zcyList[index]['ypmc'] = "点击修改";
            }
            if (this.zcyList[index]['xmmc'] == null || this.zcyList[index]['xmmc'] == "") {
                this.zcyList[index]['xmmc'] = "点击修改";
            }
            // this.editIndex = null;
        },
        editStart: function (index) {
            this.initYpmc(index);
            if (this.zcyList[index]['ypmc'] == "双击修改") {
                this.zcyList[index]['ypmc'] = null;
            }
            if (this.zcyList[index]['xmmc'] == "点击修改") {
                this.zcyList[index]['xmmc'] = null;
            }
            this.editIndex = index;
        },

        //删除中药单个药品
        remove: function (num) {
            if (zcy.zcyList[num].cfh) {
                malert("已保存处方不能增加或删除配方信息！", 'top', 'defeadted');
                return;
            }
            if (zcy.zcyList[num].ypmc == undefined) {
                zcy.zcyList[num].ypmc = "";
            }
            common.openConfirm("确定要删除第【" + (num + 1) + "】行，药品：【" + zcy.zcyList[num].ypmc + "】吗？", deletezyyp);

            function deletezyyp() {
                if (!zcy.zcyList[num].cfh) {
                    malert("删除成功！", 'top', 'success');
                    zcy.zcyList.splice(num, 1);
                    zcy.pubgetzyCfzje();
                    return;
                }
                //扣费标志
                if (zcy.cfcontent.kfbz == "1") {
                    malert("该处方已经扣费，不能删除！", 'top', 'defeadted');
                    return;
                }

                var deletone = {
                    cfh: zcy.zcyList[num].cfh,
                    mxxh: zcy.zcyList[num].mxxh
                }
                var Yppf = zcy.zcyList;
                if (Yppf == null || Yppf.length - 1 <= 0) {
                    malert("处方中至少应有一个配方！", 'top', 'defeadted');
                    return;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=deleteOnePf&parm=" + JSON.stringify(deletone), function (json) {
                    if (json.a == 0) {
                        malert("删除成功！", 'top', 'success');
                        zcy.zcyList.splice(num, 1);
                        //zcy.Wf_Save(); //保存
                    } else {
                        malert('查询失败' + json.c, 'top', 'defeadted')
                    }
                });

            }
        },

        Wf_fzh: function (index, flag) {
            if (zcy.PfxxJson[index]['fzh'] && zcy.PfxxJson[index]['fzh'] != '') {
                //判断分组号与集合是否相同，相同时频次，用法相同
                for (var i = 0; i < zcy.PfxxJson.length; i++) {
                    if (zcy.PfxxJson[index]['fzh'] > 0 && zcy.PfxxJson[i].fzh > 0 && zcy.PfxxJson[i].fzh == zcy.PfxxJson[index]['fzh']) {
                        if (flag) {
                            Vue.set(zcy.PfxxJson[i], 'yypc', zcy.PfxxJson[index].yypc);
                            Vue.set(zcy.PfxxJson[i], 'yypcmc', zcy.PfxxJson[index].yypcmc);
                            Vue.set(zcy.PfxxJson[i], 'yypccs', zcy.PfxxJson[index].yypccs);
                            Vue.set(zcy.PfxxJson[i], 'yyffmc', zcy.PfxxJson[index].yyffmc);
                            Vue.set(zcy.PfxxJson[i], 'yyff', zcy.PfxxJson[index].yyff);
                            Vue.set(zcy.PfxxJson[i], 'yyts', zcy.PfxxJson[index].yyts);
                            Vue.set(zcy.PfxxJson[i], 'sysd', zcy.PfxxJson[index].sysd);
                            Vue.set(zcy.PfxxJson[i], 'sysddw', zcy.PfxxJson[index].sysddw);
                        } else {
                            Vue.set(zcy.PfxxJson[index], 'yypc', zcy.PfxxJson[i].yypc);
                            Vue.set(zcy.PfxxJson[index], 'yypcmc', zcy.PfxxJson[i].yypcmc);
                            Vue.set(zcy.PfxxJson[index], 'yypccs', zcy.PfxxJson[i].yypccs);
                            Vue.set(zcy.PfxxJson[index], 'yyffmc', zcy.PfxxJson[i].yyffmc);
                            Vue.set(zcy.PfxxJson[index], 'yyff', zcy.PfxxJson[i].yyff);
                            Vue.set(zcy.PfxxJson[index], 'yyts', zcy.PfxxJson[i].yyts);
                            Vue.set(zcy.PfxxJson[index], 'sysd', zcy.PfxxJson[i].sysd);
                            Vue.set(zcy.PfxxJson[index], 'sysddw', zcy.PfxxJson[i].sysddw);
                        }

                    }
                    zcy.pubJS(i);
                }
            } else {
                Vue.set(zcy.PfxxJson[index], 'yypc', null);
                Vue.set(zcy.PfxxJson[index], 'yyff', null);
                Vue.set(zcy.PfxxJson[index], 'yyts', null);
                Vue.set(zcy.PfxxJson[index], 'sysd', null);
                Vue.set(zcy.PfxxJson[index], 'sysddw', null);
                Vue.set(zcy.PfxxJson[index], 'yypcmc', null);
                Vue.set(zcy.PfxxJson[index], 'yypccs', null);
                Vue.set(zcy.PfxxJson[index], 'yyffmc', null);
            }
            zcy.$forceUpdate()
        },
        //西药：处方编辑区的值 改变事件
        Wf_YppfChange: function (val, num) {
            var index = "";
            //先获取到操作的哪一个
            if (typeof val == 'object') {//判断是否是属于对象（下拉框）
                var types = val[2][val[2].length - 1];
                index = val[2][1];
                if (types == "yypc") {//频次
                    Vue.set(zcy.PfxxJson[index], 'yypc', val[0]);
                    Vue.set(zcy.PfxxJson[index], 'yypcmc', val[4]);
                    //公用方法计算用量
                    if (zcy.PfxxJson[parseInt(index)]['fzh'] && zcy.PfxxJson[parseInt(index)]['fzh'] > 0) {
                        this.Wf_fzh(parseInt(index), true)
                    } else {
                        zcy.pubJS(index);
                    }
                } else if (types == "yyff") {
                    Vue.set(zcy.PfxxJson[index], 'yyff', val[0]);
                    Vue.set(zcy.PfxxJson[index], 'yyffmc', val[4]);
                    this.zxdlx = zcy.listGetName(zcy.YyffData, val[4], 'yyffmc', 'zxdlx');//获取执行单类型
                    if (zcy.PfxxJson[parseInt(index)]['fzh'] && zcy.PfxxJson[parseInt(index)]['fzh'] > 0) {
                        this.Wf_fzh(parseInt(index), true)
                    } else {
                        zcy.pubJS(index);
                    }
                } else if (types == "sysddw") {
                    Vue.set(zcy.PfxxJson[index], 'sysddw', val[0]);
                }
                //} else if (typeof val == 'string') { //备注说明
            } else if (val == 'yysm') { //备注说明
                index = num;
                if (index == zcy.PfxxJson.length - 1) {
                    zcy.addYh(); //调用新增一行
                } else {
                    $("#ypmc" + (index + 1)).focus();
                }

            } else {//属于输入框
                index = val;
                //公用方法计算用量
                zcy.pubJS(index);
            }
            this.$forceUpdate();
            if (val[1]) {
                this.nextFocus(val[1]);
            }
        },
        //处方用量改变事件
        cfylChange: function (num) {
            
            //计算发药库量
            if (fyxmTab.csqx.cs03001200132 == "1") {
                var fysl = zcy.PfxxJson[num].fysl;
                var fyjl = zcy.PfxxJson[num].fyjl;  //服药剂量
                if (fysl == null) {
                    fysl = 1;
                }
                if (fyjl == null) {
                    fyjl = 1;
                }
                zcy.PfxxJson[num].cfyl = fysl * fyjl; //计算发药数量
            } else {
                zcy.PfxxJson[num].cfyl = zcy.PfxxJson[num].fysl;
            }
            //判断总量是否大于库存数
            if (zcy.PfxxJson[num].cfyl > zcy.PfxxJson[num].sjkc) {
                malert("药品库存不足", 'top', 'defeadted');
                //this.PfxxJson[index].ypbm = ''
                //this.PfxxJson[index].ypmc = ''
                //this.PfxxJson[index].yyjl = ''
                $('#ypmc' + index).focus()
                return;
            }
            //获取实时费用总和
            zcy.pubgetCfzje();
        },

        //公用方法计算用量
        pubJS: function (index) {
            
            if(this.PfxxJson[index].yyts>this.serchContent.cfzdts){
                malert('处方最多'+this.serchContent.cfzdts+'天', 'top', 'defeadted');
                this.PfxxJson[index].yyts=this.serchContent.cfzdts
            }
            if(this.csqxContent.N05001200273 == '1'){
                if(chose.serchContent.cflxbm == '06'){
                    if(zcy.PfxxJson[index].yyts >3){
                        malert("急诊处方建议开三天", 'top', 'defeadted');
                    }
                }
                if(chose.serchContent.cflxbm == '04'){
                    if(zcy.PfxxJson[index].yyts >7){
                        malert("麻精处方建议开七天", 'top', 'defeadted');
                    }
                }
            }
			if(zcy.PfxxJson[index].yyts >=7){
			    zcy.PfxxJson[index].yysm = '病情需要'
			}else{
				if(zcy.PfxxJson[index].yysm && zcy.PfxxJson[index].yysm == '病情需要'){
					zcy.PfxxJson[index].yysm = '';
				}
			}

			if(zcy.PfxxJson[index].mtksrq){
                let dates = new Date(zcy.PfxxJson[index].mtksrq);
                dates=dates.setDate(dates.getDate()+(Number(zcy.PfxxJson[index].yyts)-1));
                dates=new Date(dates);
                zcy.PfxxJson[index].mtjsrq = zcy.fDate(dates,'date')
            }


			
            var cs = zcy.listGetName(zcy.PcData, zcy.PfxxJson[index].yypc, 'pcbm', 'cs');//获取频次次数
            var zxfs = zcy.listGetName(zcy.PcData, zcy.PfxxJson[index].yypc, 'pcbm', 'zxfs');//获取执行方式
            var jbjl = zcy.PfxxJson[index].jbjl;  //药品基本剂量
            var yyjl = zcy.PfxxJson[index].yyjl;  //用药剂量
            var yyts = zcy.PfxxJson[index].yyts;  //用药天数
            if(zxfs == '1'){
                yyts=Math.ceil(yyts/7)
            }
            var kcfbz = zcy.PfxxJson[index].kcfbz;  //可拆分标志
            var kcsl = zcy.PfxxJson[index].kcsl;    //库存数量
            var fyjl = zcy.PfxxJson[index].fyjl;  //服药剂量

            if (!cs || cs <= 0) {
                cs = 1;
            }
            if (!jbjl || jbjl <= 0) {
                jbjl = 1;
            }
            // if (!yyjl  || yyjl <= 0) {
            //     yyjl = 1;
            // }
            if (!yyts || yyts <= 0) {
                yyts = 1;
            }
            if (!kcfbz || kcfbz == "") {
                kcfbz = '0';
            }
            if (!fyjl || fyjl <= 0) {
                fyjl = 1;
            }
            if (kcfbz == "0") {
                var cfyl = Math.ceil((yyjl / jbjl).toFixed(4)) * cs * yyts;  //自动算药品总量
            } else {
                var ll = ((yyjl * cs * yyts) / jbjl).toFixed(4);
                var cfyl = Math.ceil(ll);  //自动算药品总量
            }
            var fysl = Math.ceil(cfyl / fyjl);  //计算服药剂量
            //判断总量是否大于库存数
            if (fysl > kcsl) {
                malert("药品库存不足,使用量:"+fysl+"库存量:"+kcsl, 'top', 'defeadted');
                //this.PfxxJson[index].ypbm = ''
                //this.PfxxJson[index].ypmc = ''
                //this.PfxxJson[index].yyjl = ''
                $('#ypmc' + index).focus()
                return;
            }
            if (fyxmTab.csqx.cs03001200132 == "1") {
                zcy.PfxxJson[index].fysl = fysl;     //给赋药总量赋值用于显示
                zcy.PfxxJson[index].cfyl = fysl * fyjl;     //给总量赋值
            } else {
                zcy.PfxxJson[index].cfyl = cfyl;     //给总量赋值
                zcy.PfxxJson[index].fysl = cfyl;     //给总量赋值 用于显示
            }

            // }

            //获取实时费用总和
            zcy.setSunFun();
            zcy.pubgetCfzje();
        },

        //公用实时显示西药处方总金额
        pubgetCfzje: function () {
            var cfje = 0;
            for (var i = 0; i < zcy.PfxxJson.length; i++) {
                if (fyxmTab.csqx.cs03001200132 == "1" && zcy.PfxxJson[i].fydw != null) {
                    cfje += zcy.PfxxJson[i].fyjl * zcy.PfxxJson[i].yplj * zcy.PfxxJson[i].cfyl;
                } else {
                    cfje += zcy.PfxxJson[i].cfyl * zcy.PfxxJson[i].yplj;
                }
            }
            Vue.set(brjzFoot.msgContent, 'cfje', this.fDec(cfje, 2));     //给总量赋值
        },

        //计算中药金额
        pubgetzyCfzje: function () {
            var cfje = 0;
            for (var i = 0; i < zcy.zcyList.length; i++) {
                console.log(zcy.zcyList[i]);
                cfje += zcy.zcyList[i].cfyl * zcy.zcyList[i].yplj;
            }
            brjzFoot.msgContent.cfje = 0;
            Vue.set(brjzFoot.msgContent, 'cfje', this.fDec(cfje, 2));     //给总量赋值
        },


        //中药：处方编辑区的值 改变事件
        Wf_zyYppfChange: function (index, event) {
            var jbjl = zcy.zcyList[index].jbjl;  //药品基本剂量
            var yyjl = zcy.zcyList[index].yyjl;  //用药剂量
            var zyfs = zcy.zcyList.zyfs;  //中药副数
            var kcsl = zcy.zcyList[index].kcsl;    //库存数量
            if (jbjl == null || jbjl == undefined || jbjl <= 0) {
                jbjl = 1;
            }
            if (yyjl == null || yyjl == undefined || yyjl <= 0) {
                yyjl = 1;
            }
            if (zyfs == null || zyfs == undefined || zyfs <= 0) {
                zyfs = 1;
            }
            var cfyl = Math.ceil(yyjl / jbjl) * zyfs;  //自动算药品总量

            if (cfyl > kcsl) {
                malert("药品总量超过限额", 'top', 'defeadted');
                return;
            }
            Vue.set(zcy.zcyList[index], 'cfyl', cfyl);     //给总量赋值
            zcy.pubgetzyCfzje();
            if (event.keyCode == 13) {
                if (this.zcyList[index + 1]) {
                    this.nextFocus(event, 2);
                }else{
					this.add(event, true, 2);
				}
            }

        },

        //中药：用药副数改变时
        Wf_yyfsChange: function () {
            zcy.setSum();
            /*
            var zyfs = zcy.cfcontent.zyfs;  //中药副数
            for (var i = 0; i < zcy.zcyList.length; i++) {
                var jbjl = zcy.zcyList[i].jbjl;  //药品基本剂量
                var yyjl = zcy.zcyList[i].yyjl;  //用药剂量
                if (jbjl == null || jbjl == undefined || jbjl <= 0) {
                    jbjl = 1;
                }
                if (yyjl == null || yyjl == undefined || yyjl <= 0) {
                    yyjl = 1;
                }
                if (zyfs == null || zyfs == undefined || zyfs <= 0) {
                    zyfs = 1;
                }
                var cfyl = Math.ceil(yyjl / jbjl) * zyfs;  //自动算药品总量
                Vue.set(zcy.zcyList[i], 'cfyl', cfyl);     //给总量赋值
            }
            zcy.pubgetzyCfzje();
            */
        },


        //删除西药单行药品
        removeNow: function (num) {
            if (zcy.PfxxJson[num].cfh) {
                malert("已保存处方不能增加或删除配方信息！", 'top', 'defeadted');
                return;
            }
            if (zcy.PfxxJson[num].ypmc == undefined) {
                zcy.PfxxJson[num].ypmc = "";
            }
            common.openConfirm("确定要删除第【" + (num + 1) + "】行，药品：【" + zcy.PfxxJson[num].ypmc + "】吗？", sccf);

            function sccf() {
                if (zcy.PfxxJson[num].cfh == undefined && zcy.PfxxJson[num].cfh == null) {
                    malert("删除成功！", 'top', 'success');
                    zcy.PfxxJson.splice(num, 1);
                    //获取实时费用总和
                    zcy.pubgetCfzje();
                    return;
                }
                //扣费标志
                if (zcy.cfcontent.kfbz == "1") {
                    malert("该处方已经扣费，不能删除！", 'top', 'defeadted');
                    return;
                }
                var deletone = {
                    cfh: zcy.PfxxJson[num].cfh,
                    mxxh: zcy.PfxxJson[num].mxxh
                }
                var Yppf = zcy.PfxxJson;
                if (Yppf == null || Yppf.length - 1 <= 0) {
                    malert("处方中至少应有一个配方！", 'top', 'defeadted');
                    return;
                }
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=deleteOnePf&parm=" + JSON.stringify(deletone), function (json) {
                    if (json.a == 0) {
                        malert("删除成功！", 'top', 'success');
                        zcy.PfxxJson.splice(num, 1);

                        //zcy.Wf_Save(); //保存
                    } else {

                        malert('查询失败' + json.c, 'top', 'defeadted', 'top', 'defeadted')
                    }
                });

            }

        },

        showDbrPop: function () {
            dbrPop.show();
        },


        //**********删除处方（作废西药中药处方）
        delCf: function () {
            //如果处方号为空，直接移出当前元素
            if (zcy.cfcontent.cfh == null || zcy.cfcontent.cfh == undefined || zcy.cfcontent.cfh == "") {
                zcy.cfList.splice(zcy.cfcontent.index, 1);
                zcy.zcyList = [];
                zcy.PfxxJson = [];
                zcy.extraChargesList = [];
                // setTimeout(function () {
                //     Yfwidth();
                // },100);
                zcy.Wf_selectCF();   //刷新一下病人处方 信息
                return;
            }
            //扣费标志
            if (zcy.cfcontent.kfbz == "1") {
                malert("该处方已经扣费，不能删除！", 'top', 'defeadted');
                return;
            }

            if(zcy.cfcontent.cfys != chose.userInfo.czybm ){
                malert("当前处方医生为"+zcy.cfcontent.cfysxm+",你无权作废该处方", 'top', 'defeadted');
                return false;
            }


            //给用户确认
            common.openConfirm("确定要删除当前处方吗？", sccf);

            function sccf() {
                //删除处方
                var parm = {
                    list: [{
                        cfh: zcy.cfcontent.cfh
                    }]
                };
                zcy.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Delete_Mzcf', JSON.stringify(parm)).then(function (data) {
                    if (data.body.a == 0) {
                        malert('处方作废成功', 'top', 'success')
                        zcy.cfList.splice(zcy.cfcontent.index, 1);
                        zcy.Wf_selectCF();   //刷新一下病人处方 信息
                        // setTimeout(function () {
                        //     Yfwidth();
                        // },100)
                    } else {
                        malert('处方作废失败：' + data.body.c, 'top', 'defeadted')
                    }
                });
                
            }


        },
        clickExport:clickExport,
        //药品说明书

        clickOnTest(ypname,ypbm){
            //console.log(ypbm)
            //clickExport(ypname,this.userId,this.userName,this.ksbm)
            clickExport(ypname,ypbm,21,22,22)
        },

        //中药专论
        clickOnZy(ypname,ypbm){
           zhongyao(ypname,ypbm)
        },
        //重要信息
        importInfo(ypname,ypbm){
            importInfoClick(ypname,ypbm)
        },

    }
});

//底部工具栏
var brjzFoot = new Vue({
    el: '.brjz-foot',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer, checkData],
    data: {
        Brxx_List:userNameBg.Brxx_List,
        isbjcf:false,//是否是编辑处方
        cfhparam:{},
        parm: {},
        hlyyCheckUrl: '',
        hlyyYpmx: [],
        hlyynewYpmx: {
            dcjl: 0,
            pcbm: '',
            rcs: 0,
            ypbm: ''
        },
        saveShow: true,
        editTitle: '编辑',
        sum: 0,
        msgContent: {},  //底部需要的信息
        ifClick: true,
        printList: [],
        edit_cfh: '',
        csqxContent: {},
        CflxdyzlList: [], //处方类型对应药品种类
        cfdymb: '',
        N03001200127: '',
        N03001200128: '',
		isBshow:true,
		isHd:true,
        bxurl:'',
        bxlbbm:'',
    },

    mounted:function(){
        this.Wf_getCfdyzl();
		//this.qxcl();
        this.bxsjshow();
    },
    methods: {
		qxcl:function(){
			if(userNameBg.Brxx_List.wcbz =='1'){
				this.isBshow = false;
			}else if(userNameBg.Brxx_List.ghrq && userNameBg.Brxx_List.ghxq){
				var date=new Date(userNameBg.Brxx_List.ghrq);
				var nowDate = new Date();
				 date=new Date(date.setDate(date.getDate()+userNameBg.Brxx_List.ghxq));
				 if(date>nowDate){
					 if(userNameBg.Brxx_List.jzys == userId){
						 this.isBshow = true;
					 }else{
						this.isBshow = false; 
					 }
				 }else{
					 this.isBshow = false;
				 }
				 console.log(this.isBshow)
			}
			
			
		},
        //获取对方类型对应药品种类
        Wf_getCfdyzl: function () {
			
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmCflxdyzl&types=query&dg=" + JSON.stringify({page: 1, rows: 200}), function (json) {
                if(json.a=='0' && json.d.list){
                    this.CflxdyzlList = json.d.list;
                }
            });
        },
        // 获取保险类别信息
        getbxlb: function () {
            var param = {bxjk: "008"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json="
                + JSON.stringify(param), function (json) {
                if (json.a == 0) {
                    if (json.d.list.length > 0) {
                        brjzFoot.bxlbbm = json.d.list[0].bxlbbm;
                        brjzFoot.bxurl = json.d.list[0].url;
                        brjzFoot.yndrZnshInit();
                    }
                } else {
                    malert("保险类别查询失败!" + json.c, 'top', 'defeadted')
                }
            });
        },

        //云南东软医保智能审核初始化
        yndrZnshInit: function () {
            $.post(brjzFoot.bxurl + "/znsh/init", {parm: userNameBg.Brxx_List.jzysxm}, function (json) {
                if (json.code == 0) {
                    malert("初始化成功!");
                } else {
                    malert("初始化失败！" + json.msg, 'top', 'defeadted');
                }
            });
        },

        //云南东软医保智能审核
        yndrZnsh: function () {
            var cfh = zcy.cfcontent.cfh;
            if (cfh == null || cfh == "") {
                malert("当前没有处方可以审核", 'top', 'defeadted');
                return false;
            }
            var mzjzxx = {
                'xm': userNameBg.Brxx_List.brxm,
                'ghxh': userNameBg.Brxx_List.ghxh,
                'ryrq': userNameBg.Brxx_List.ghrq,
                'jzysbm': zcy.cfcontent.cfys,
                'jzysxm': zcy.cfcontent.cfysxm,
                'ksbm': userNameBg.Brxx_List.ghks,
                'ksmc': userNameBg.Brxx_List.ghksmc,
                'sfzh': userNameBg.Brxx_List.sfzh,
                'zdbm': userNameBg.Brxx_List.jbbm,
                'zdmc': userNameBg.Brxx_List.jbmc,
                'yllb': '11'
            }

            var parm = {
                'mzjzxx': JSON.stringify(mzjzxx),
                'cfxx': zcy.cfcontent.cfh
            };
            // $.post("http://localhost:9001" + "/znsh/znshForMz", parm, function (json) {
            $.post(brjzFoot.bxurl + "/znsh/znshForMz", parm, function (json) {
                if (json.code == 0) {
                    var result = json.data;
                    if (result.dmsg) {
                        // malert("智能审核存在疑点信息：" + result.dmsg,'top','defeadted');
                        var ydxxList = result.ydxxList;
                        for (var i = 0; i < ydxxList.length; i++) {
                            malert(ydxxList[i].xmmc + ydxxList[i].msg, 'top', 'defeadted');
                        }
                    } else {
                        malert("智能审核成功，无疑点信息!");
                    }
                } else {
                    malert("智能审核异常！" + json.msg, 'top', 'defeadted');
                }
            });
        },

        editYpcf: function () {
		    //    		//先判断是否允许编辑
            if (zcy.cfcontent.kfbz == "1") {
                malert("该处方已经扣费不允许进行操作", 'top', 'defeadted');
                return false;
            }
            if(zcy.cfcontent.cfys != chose.userInfo.czybm && zcy.cfcontent.jszgdm< chose.userInfo.jszgdm){
                malert("当前处方医生为"+zcy.cfcontent.cfysxm+",你无权编辑该处方", 'top', 'defeadted');
                return false;
            }
            zcy.addShow[zcy.num] = true;

            zcy.fjfyIsShow = true;
            // 将处方号置为空
            brjzFoot.edit_cfh = brjzFoot.msgContent.cfh;
            brjzFoot.cfhparam = {
                list: [{
                    cfh: brjzFoot.msgContent.cfh
                }]
            };
            brjzFoot.isbjcf=true;
            brjzFoot.msgContent.cfh = '空';
            zcy.cfcontent.cfh = "";
            if (zcy.cfcontent.isXyOrZy == '1') { //中医
                for (var i = 0; i < zcy.zcyList.length; i++) {
                    zcy.zcyList[i].cfh = null;
                    // zcy.pubJS(i);
                }
                // chose.serchContent.yfbm = brjzFoot.listGetName(chose.YFJson, '中药房', 'yfmc', 'yfbm');
                // chose.serchContent.cflb = '2';
                chose.getCflb();
            } else {
                for (var i = 0; i < zcy.PfxxJson.length; i++) {
                    zcy.PfxxJson[i].cfh = null;
                    zcy.pubJS(i);
                }
                // chose.serchContent.cflb = '1';
                // chose.serchContent.yfbm = brjzFoot.listGetName(chose.YFJson, '西药房', 'yfmc', 'yfbm');
                chose.getCflb();
            }
            for (var i = 0; i < zcy.extraChargesList.length; i++) {
                zcy.extraChargesList[i].fyjlid = null;
            }


        },

        //对已有处方进行编辑
        edit: function () {
            //先判断是否允许编辑
            if (zcy.cfcontent.kfbz == "1") {
                malert("该处方已经扣费不允许进行操作", 'top', 'defeadted');
                return false;
            }
            zcy.tabShowActive()
            this.editTitle = '保存';
            brjzFoot.saveShow = false;
            zcy.addShow[zcy.num] = true;
            $("input[name='text']").each(function () {
                $(this).removeClass('input-border');
                $(this).attr('disabled', false)
            })
        },

        /**
         * 四川事前智能审核
         */
        scZnsh: function (parm) {
            //医嘱信息
            var znshOrderArr = [];
            for (var i = 0; i < parm.list[0].Yppf.length; i++) {
                var pfxx = parm.list[0].Yppf[i];
                var znshOrder = {
                    orderId: "", //医嘱id
                    orderDoctorId: userNameBg.Brxx_List.jzys, //医生id
                    prescriptionCode: "", //处方号
                    groupCode: pfxx.fzh, //组编号
                    isRepeat: '0',  //是否为长期医嘱
                    orderType: '1',  //医嘱类型 1-药品 2-诊疗 3-材料
                    detailType: '', //收费类别
                    actionType: '2', //医嘱行为 1-住院 2-出院带药   3-观察医嘱 999-其他
                    medicareItemCode: '',   //医保编码
                    medicareItemName: '',   //医保名称
                    medicareDrugForm: '',   //医保剂型
                    hospitalItemCode: pfxx.ypbm,   //院内编码
                    hospitalItemName: pfxx.ypmc,    //院内名称
                    hospitalDrugForm: pfxx.jxmc,    //院内剂型
                    transationItemCode: '', //映射项目编码
                    orderQuantity: pfxx.cfyl,  //处方用量
                    orderPrice: pfxx.yplj,  //单价
                    orderAmount: pfxx.cfy * pfxx.yplj, //总费用
                    selfAmount: 0,  //个人自费金额
                    specification: pfxx.ypgg, //规格
                    dosage: pfxx.yyjl + pfxx.jldwmc, //单次剂量
                    specUnit: pfxx.yfdwmc, //数量规格的单位
                    startDate: this.fDate(new Date(), 'date'), //开始日期
                    startTime: this.fDate(new Date(), 'time'), //开始时间
                    stopDate: this.fDate(new Date(), 'date'), //停止日期
                    stopTime: '23:59:59', //停止时间
                    issueOrderDeptId: userNameBg.Brxx_List.ghks, //科室id
                    issueOrderDeptName: userNameBg.Brxx_List.ghksmc, //科室名称
                };
                znshOrderArr.push(znshOrder);
            }
            //就诊信息
            var znshJzxxArr = [];
            var znshJzxxObj = {
                encounterId: userNameBg.Brxx_List.ghxh, //就诊id
                hospitalCode: '', //机构代码
                hospitalName: '', //机构名称
                hospitalType: '', //机构类型
                hospitalStyle: '', //机构登记
                deptId: userNameBg.Brxx_List.ghks, //病区标志
                roomNo: '', //房间号
                bedNo: '',  //床位
                admissionDate: this.fDate(userNameBg.Brxx_List.ghrq, 'date'), //入院日期 yyyy-mm-dd
                admissionTime: this.fDate(userNameBg.Brxx_List.ghrq, 'time'), //入院时间
                dischargeDate: this.fDate(new Date(), 'date'), //出院日期 yyyy-mm-dd
                dischargeTime: '23:59:59', //出院时间
                diagnosisCode: parm.list[0].Ghxx.jbbm, //主诊断编码
                diagnosis: parm.list[0].Ghxx.jbmc, //主诊断名称
                encounterDoctorId: userNameBg.Brxx_List.jzys, //医生标志
                admissionDeptId: userNameBg.Brxx_List.ghks, //入院科室id
                admissionDepartmentName: userNameBg.Brxx_List.ghksmc, //入院科室名称
                dischargeDeptId: userNameBg.Brxx_List.ghks, //出院科室id
                dischargeDepartmentName: userNameBg.Brxx_List.ghksmc, //出院科室名称
                encounterType: '11', //就诊类别 11-门诊 40-购药 80-其他 21-住院
                actualLengthOfStay: '1', //实际住院天数
                pregnantFlag: '', //3-妊娠期 4-哺乳期
                cost: '', //总费用
                selfCost: '', //自费金额
                deductibleCost: '', //自付金额
                individualAccountCost: '', //个账支付金额
                assistanceCost: '', //救助金支付金额
                overallCost: '', //统筹支付金额
                costCount: '', //结算次数
                insuranceCode: '', //险种代码 L001-社保 L002-新农合 L003-省直
                diagnoses: [{    //诊断信息
                    diagnoseId: '1',    //诊断id
                    diagnoseDoctorId: userNameBg.Brxx_List.jzys, //医生id
                    diagnoseCategory: '1', // 1-入院诊断 2-出院诊断
                    diagnoseType: '0', // 0-主要诊断 1-第一辅助诊断 2-...
                    diagnoseCode: parm.list[0].Ghxx.jbbm,   //诊断编码
                    diagnoseName: parm.list[0].Ghxx.jbmc,   //诊断名称
                    content: parm.list[0].Ghxx.jbmc,    //诊断内容
                    admissionCondition: '',  //入院病情
                    diagnoseDate: this.fDate(parm.list[0].Ghxx.jzsj, 'date'), //接诊日期
                    diagnoseTime: this.fDate(parm.list[0].Ghxx.jzsj, 'time'), //接诊时间
                }],
                orders: znshOrderArr //医嘱信息
            };
            znshJzxxArr.push(znshJzxxObj);
            //患者信息
            var znshHzxx = {
                patientId: userNameBg.Brxx_List.brid, //病人id
                name: userNameBg.Brxx_List.brxm, // 病人姓名
                sex: userNameBg.Brxx_List.brxb, // 病人性别 1-男 2-女 999-其他
                dob: this.fDate(userNameBg.Brxx_List.csrq, 'date'), //出生日期 yyyy-mm-dd
                currEncounterId: userNameBg.Brxx_List.ghxh, //就诊id
                encounters: znshJzxxArr
            };

            this.postAjax(window.top.J_tabLeft.obj.scZnshUrl, JSON.stringify(znshHzxx), function (json) {
                brjzFoot.ifClick = true;
                if (json.status == "OK") {
                    var znshTip = '';
                    for (var i = 0; i < json.result.length; i++) {
                        znshTip += json.result[i].message + "\n";
                    }
                    if (znshTip) {
                        common.openConfirm("智能审核事前提醒：<br><br>" + znshTip + "<br><br>是否继续保存处方？", function () {
                            brjzFoot.finalSaveCf();
                        }, function () {
                        });
                    } else {
                        brjzFoot.finalSaveCf();
                    }
                } else {
                    common.openConfirm("智能审核事前提醒：<br><br>" + json.message + "<br><br>是否继续保存处方？", function () {
                        brjzFoot.finalSaveCf();
                    }, function () {
                    });
                }
            }, function (e) {
                malert("智能审核接口已断开，请联系管理员！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                brjzFoot.finalSaveCf();
            });
        },

        /**
         * @zh 药监平台接口
         * @param parm
         */
        yyjgpt: function (parm) {
            //贵州的门急诊处方需要进行审核，审核后需要开方医生确认后才继续保存,0贵州，1其他
            var cfList = [];
            for (var i = 0; i < parm.list[0].Yppf.length; i++) {
                var pfxx = parm.list[0].Yppf[i];
                var pfContent = {
                    clinic_dept_code: userNameBg.Brxx_List.ghks,  //就诊科室代码 必填 String
                    clinic_dept_name: userNameBg.Brxx_List.ghksmc,  //就诊科室名称 必填 String
                    patient_local_id: userNameBg.Brxx_List.brid,  //患者本地唯一 ID 必填 String
                    prescribe_serial_id: "",   //处方明细序号 必填 (后台添加)
                    prescribe_no: "",  //处方编号 必填String 按照某一特定编码规则赋予门(急)诊处方 的顺序号(后台添加)
                    prescription_medicine_no: (i + 1),  //处方药品组号String 由系统从 1 开始根据自然递增的原则赋予每条新增医嘱的顺序号
                    outpatient_no: userNameBg.Brxx_List.ghxh, //门(急)诊号 必填String 按照某一特定编码规则赋予门(急)诊就诊对象的顺序号
                    visit_count: "1",   //就诊次数 必填 String
                    name: userNameBg.Brxx_List.brxm,  //患者姓名 必填 String
                    birth_date: this.fDate(userNameBg.Brxx_List.csrq, 'date'),    //出生日期 必填 String 患者出生当日的公元纪年日期的完整描述 形如”19850513”
                    gender_code: userNameBg.Brxx_List.brxb,   //性别代码 必填 String 附录 12 RC001
                    phone_number: userNameBg.Brxx_List.lxrdh,  //电话号码String 患者本人的电话号码,包括国际、国内区号和分机号
                    idcard: userNameBg.Brxx_List.sfzh,    //注册证件号码必填 String 患者的注册证件上的唯一法定标识符
                    idcard_code: userNameBg.Brxx_List.sfzjlx,   //注册证件类型代 码String 附录 7CV02.01.101在【CV02.01.101 身份证件类别代码】范围内
                    age_year: userNameBg.Brxx_List.brnl,  //年龄（岁）必填 Int 患者年龄满 1 周岁的实足 年龄,为患者出生后按照 日历计算的历法年龄,以 实足年龄的相应整数填 写
                    age_month: "",// 年龄（月） Int 患者不满 1 周岁的,填写 月龄
                    presciption_dep_code: userNameBg.Brxx_List.ghks,  //处方开立科室代 码必填 String RC023
                    prescribe_input_date: getTodayDate(),  //处方开立日期 必填 String
                    doctor_code: userNameBg.Brxx_List.jzys, // 医生代码 必填 String 处方开立医师的职工编 码
                    doctor_name: userNameBg.Brxx_List.jzysxm,   //医生名称 必填 String 处方开立医师的姓名
                    prescription_identification_no: chose.serchContent.cflxbm,    //处方类别代码 必填 String 附录 13 CT05.10.007
                    trialparty_pha_code: "",   //审方药师代码 String
                    trialparty_pha_name: "",   //审方药师名称 String 对门急诊处方的适宜性、规范性等进行审核的药剂师（针对处方点评）
                    drug_id: pfxx.ypbm,   //药品（项目）编 必填 String 医院内部编码
                    drug_name: pfxx.ypmc, //药物（项目）名称必填 String 药物通用名称
                    drug_specifications: pfxx.ypgg,   //药物规格 必填 String 药物规格的描述,如 0.25g
                    drug_dosage_code: "",  //药物剂型代码 String药物剂型类别在特定编码体系中的代码参照附录 1CV08.50.002
                    drug_dosage_name: "",  //药物剂型名称 String
                    drug_use_dose: pfxx.yyjl, //药物使用次剂量 double(中/西药)单次使用药物的剂量
                    drug_use_dose_unit: pfxx.jldwmc,    //药物使用剂量单位String(中/西药)标识药物剂量的计量单位
                    drug_use_frequency_code: "",   //药物使用频次代码String标识单位时间内药物使用的 次 数 参 照 附 录 2CV06.00.228
                    drug_use_frequency: "",    //药物使用-频率 String
                    drug_use_route_code: "",   //药物使用途径代码String(中/西药)药物使用途径在特定编码体系中的代码参照附录 3 CV06.00.102
                    drug_use_route_name: "",   //药物使用-途径 String
                    drug_use_total_dose: pfxx.cfyl,   //药物使用总剂量 double 服药者在一段时间内累计服用某药物的剂量总计
                    drug_use_days: pfxx.yyts, //服药天数 int
                    yplb: "",  //药品种类 int 1 西药 2 中成药 3 草药4 麻精药(后台处理)
                    antibacterials_flag: (!pfxx.kssjb || pfxx.kssjb == '1') ? "2" : "1",   //抗菌药物标志 必填 String 附录 14 RC016
                    drug_procurement_code: pfxx.ypbm,
                    drugs_unit: pfxx.yfdwmc,    //药物开立数量单位必填 String
                    unit_price: pfxx.yplj,    //单价 必填 double
                    prescription_drug_amount: this.fDec(pfxx.cfyl * pfxx.yplj, 2),  //处方药品金额 必填 double
                    tot_amount: "",    //总价 必填 double
                    quantity: pfxx.cfyl,  //数量 必填 int
                    reg_sn: userNameBg.Brxx_List.ghxh,    //挂号序号 String
                    disease_diagnosis_code: userNameBg.Brxx_List.jbbm,    //疾病诊断编码 必填 String疾病分类及手术-疾病分 类与代码国家临床版 2.0
                    yljgbm: jgbm,
                };
                if (chose.serchContent.cflb == "2") {//草药
                    pfContent.therapeutic_therapy = "";   //治则治法 String 根据辨证结果采用的治则治法名称术语
                    pfContent.chinese_medicine_prescription = pfxx.yyffmc;//中药饮片处方 String 中药饮片处方的详细描述
                    pfContent.chinese_drink_tablet_number = parm.list[0].Ypcf.zyfs;   //中 药 饮 片 剂 数(剂)int 本次就诊给患者所开中药饮片的剂数,计量单位为
                    pfContent.chinese_herbalmedi_method = parm.list[0].Ypcf.yysmcf; //中药饮片煎煮法 String 中药饮片煎煮方法描述
                    pfContent.chinese_medicine_treat_method = parm.list[0].Ypcf.yysm; //中药用药方法 String 中药的用药方法的描述
                    pfContent.chinese_diagnosis_code = parm.list[0].Ypcf.zyzh;    //中医病名代码 String 患者所患疾病在中医病名特定分类体系中的代码参照 GB/T 15657-1995
                    pfContent.chinese_syndrome_desc = parm.list[0].Ypcf.zyzf; //中医证候代码 String 参照 GB/T 15657-1995
                    pfContent.yplb = "3";  //药品种类 int 1 西药 2 中成药 3 草药4 麻精药
                }
                cfList.push(pfContent);
            }
            //this.postAjax(zcy.csqxContent.cs03001200144, JSON.stringify(cfList), function (json) {
            brjzFoot.$http.post(zcy.csqxContent.cs03001200144, JSON.stringify(cfList)).then(function (json) {
                brjzFoot.ifClick = true;
                if (json.success == "T") {
                    malert(json.body.error_msg, 'top', 'success');
                    brjzFoot.finalSaveCf();
                } else {
                    common.openConfirm("药监品台提示：<br><br>" + json.body.error_msg + "<br><br>是否继续保存处方？", function () {
                        brjzFoot.finalSaveCf();
                    }, function () {
                    });
                }
            }, function (e) {
                malert("药监平台接口已断开，请联系管理员！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                brjzFoot.finalSaveCf();
            });
        },
        openHyll:function (){
            window.open(this.csqxContent.N05001200276)
        },
        tysq:function (ystysqbz){
            if(zcy.cfcontent.fybz != '1'){
                malert("当前处方没发药，请直接作废处方", 'top', 'defeadted');
                return  false
            }
            var obj = JSON.stringify({
                "cfh": brjzFoot.msgContent.cfh,
                "ystysqbz":ystysqbz
            });
            this.$http.post('/actionDispatcher.do?reqUrl=New1YfbYfywCftyzf&types=cfsh', obj)
                .then(function (data) {
                    if (data.body.a == 0) {
						if(ystysqbz =='1'){
							malert("申请退药成功", 'top', 'success');
						}else{
							malert("取消退药申请成功", 'top', 'success');
						}
                        
                    } else {
                        if(ystysqbz =='1'){
                        	malert("申请退药失败", 'top', 'defeadted');
                        }else{
                        	malert("取消退药申请失败", 'top', 'defeadted');
                        }
                        
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        //保存处方信息
        save: function () {
            if (!brjzFoot.ifClick) return; //如果为false表示已经点击了不能再点
            brjzFoot.ifClick = false;
            if (zcy.cfcontent.kfbz == "1") {
                malert('已扣费不能进行修改', 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return false;
            }
            if (brjzFoot.msgContent.cfh == undefined) {
                brjzFoot.msgContent.cfh = '空';
            }
            if (brjzFoot.msgContent.cfh != '空') {
                malert('该处方已保存，请勿重复点击！', 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return false;
            }

            /**
             * @yqq修改保存后数据处理
             */
            if (zcy.csqxContent.N03001200127 == '1') {
                for (var i = 0; i < zcy.extraChargesList.length; i++) {
                    if (zcy.extraChargesList[i].fylb) {
                        zcy.extraChargesList[i].lbbm = zcy.extraChargesList[i].fylb;
                    }
                }
            }

            //循环判断配方信息是否完善
            var Yppf = [];
            if (chose.serchContent.cflb != "2") { //西药判断
                var ypbm_pd, ypmc_pd, dcjl_pd, yyff_pd, pc_pd, ts_pd, zl_pd;
                for (var i = 0; i < zcy.PfxxJson.length; i++) {
                    ypbm_pd = zcy.PfxxJson[i].ypbm;    //药品编码
                    ypmc_pd = zcy.PfxxJson[i].ypmc;    //药品名称
                    dcjl_pd = zcy.PfxxJson[i].yyjl;    //单次剂量
                    yyff_pd = zcy.PfxxJson[i].yyff;    //用法
                    pc_pd = zcy.PfxxJson[i].yypc;    //频次
                    ts_pd = zcy.PfxxJson[i].yyts;    //天数
                    zl_pd = zcy.PfxxJson[i].cfyl;    //总量
                    if (fyxmTab.csqx.cs03001200132 == "1" && zcy.PfxxJson[i].fydw != null) {
                        zl_pd = zcy.PfxxJson[i].cfyl * zcy.PfxxJson[i].fyjl;    //总量
                    } else {
                        zl_pd = zcy.PfxxJson[i].cfyl;    //总量
                    }

                    var row = i + 1;
                    //药品编码判断
                    if (!ypbm_pd) {
                        zcy.PfxxJson.splice(i, 1);  //药品编码为空时移出当前行
                        continue;  //跳出本次循环
                    }

                    //单次剂量判断
                    if (!dcjl_pd || dcjl_pd <= 0) {
                        malert("第【" + row + "】行药品名称【" + ypmc_pd + "】的【单次剂量】不能等于0或为空", 'top', 'defeadted');
                        brjzFoot.ifClick = true;
                        return;
                    }


                    //用药方法判断
                    if (!yyff_pd) {
                        malert("第【" + row + "】行药品名称【" + ypmc_pd + "】的【用药方法】不能为空", 'top', 'defeadted');
                        brjzFoot.ifClick = true;
                        return;
                    }

                    //频次判断
                    if (!pc_pd) {
                        malert("第【" + row + "】行药品名称【" + ypmc_pd + "】的【频次】不能为空", 'top', 'defeadted');
                        brjzFoot.ifClick = true;
                        return;
                    }

                    //天数
                    if (ts_pd == null || ts_pd == undefined || ts_pd <= 0) {
                        zcy.PfxxJson[i].yyts = 1;  //天数为空或小于1的时候就默认为1天
                    }


                    if (chose.userInfo.jszgdm =='04') {
                        if (zcy.PfxxJson[i].kssjb == '3' || zcy.PfxxJson[i].kssjb == '4') {//根据抗生素级别需要申请
                            malert(ypmc_pd + "属于" + chose.kssjb_tran[zcy.PfxxJson[i]['kssjb']] + "不允许使用", 'top', 'defeadted');
                            brjzFoot.ifClick = true;
                            return false;
                        }
                    } else if (chose.userInfo.jszgdm == '03') {//主治医师
                        if (zcy.PfxxJson[i].kssjb == '3' || zcy.PfxxJson[i].kssjb == '4') {//根据抗生素级别需要申请
                            malert(ypmc_pd + "属于" + chose.kssjb_tran[zcy.PfxxJson[i]['kssjb']] + "不允许使用", 'top', 'defeadted');
                            brjzFoot.ifClick = true;
                            return false;
                        }
                    } else if (chose.userInfo.jszgdm == '02') {//副主任
                        if (zcy.PfxxJson[i].kssjb == '4') {//特殊抗生素时需要申请
                            malert(ypmc_pd + "属于" + chose.kssjb_tran[zcy.PfxxJson[i]['kssjb']] + "不允许使用", 'top', 'defeadted');
                            brjzFoot.ifClick = true;
                            return false;
                        }
                    }


                    if(chose.Brxx_List.fbbm=='40'){
                        if(!zcy.PfxxJson[i].mtksrq){
                            malert("门特病人第【" + row + "】行药品名称【" + ypmc_pd + "】的【用药开始日期】不能为空", 'top', 'defeadted');
                            brjzFoot.ifClick = true;
                            return;
                        }else if(!zcy.PfxxJson[i].mtjsrq){
                            malert("门特病人第【" + row + "】行药品名称【" + ypmc_pd + "】的【用药结算日期】不能为空", 'top', 'defeadted');
                            brjzFoot.ifClick = true;
                            return;
                        }

                        zcy.PfxxJson[i].mtksrq = zcy.fDate(zcy.PfxxJson[i].mtksrq,'date')
                        zcy.PfxxJson[i].mtjsrq = zcy.fDate(zcy.PfxxJson[i].mtjsrq,'date')
                    }

                    //总量
                    /*
                    if (!zl_pd || zl_pd <= 0) {
                        malert("第【" + row + "】行药品名称【" + ypmc_pd + "】的【药品总量】不能为空", 'top', 'defeadted');
                        brjzFoot.ifClick = true;
                        return;
                    }
                     */
                    //获取用药频次名称
                    zcy.PfxxJson[i].yypcmc = brjzFoot.listGetName(zcy.PcData, pc_pd, 'pcbm', 'pcmc');
                    //获取用药方法名称
                    zcy.PfxxJson[i].yyffmc = brjzFoot.listGetName(zcy.YyffData, yyff_pd, 'yyffbm', 'yyffmc');
                }
                //配方信息
                Yppf = zcy.PfxxJson;

                //是否强行限定药品处方位数
                console.log(Yppf);
                console.log(JSON.stringify(Yppf));


            }
            else { //中药
                /*
                //处理煎药袋费
                //先查找配方中是否已存在煎药袋
                var jyfparm = {
                    page:1,
                    rows:10,
                    parm:'Y02090588'
                };
                var json = {
                    yfbm: chose.serchContent.yfbm
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(jyfparm) + '&json=' + JSON.stringify(json), function (data) {
                    if (data.a == 0) {
                        if (data.d.list.length > 0){
                            //parseInt(this.zcyList[i].yyjl || 0 ) / parseInt(this.zcyList[i].jbjl || 0 ) * zyfs;
                            var zyfs = parseInt(Number(zcy.cfcontent.zyfs || 0)).toFixed(2);
                            var ypjson = data.d.list[0];
                            ypjson.yyjl = 1;
                            ypjson.jbjl = 1;
                            zcy.zcyList.push(ypjson);
                        }
                    }
                });
                */
                //计算中药金额
                zcy.Wf_yyfsChange();
                console.log(zcy.zcyList);
                //循环判断配方信息是否完善
                var ypbm_pd, ypmc_pd, dcjl_pd, zl_pd, yyff_pd;
                for (var i = 0; i < zcy.zcyList.length; i++) {
                    ypbm_pd = zcy.zcyList[i].ypbm;    //药品编码
                    ypmc_pd = zcy.zcyList[i].ypmc;    //药品名称
                    yyff_pd = zcy.zcyList[i].yyff;    //用法
                    dcjl_pd = zcy.zcyList[i].yyjl;    //单次剂量
                    zl_pd = zcy.zcyList[i].cfyl;    //总量

                    var row = i + 1;

                    // @yqq 判断是否有相同的药品

                    //药品编码判断
                    if (ypbm_pd == null || ypbm_pd == undefined || ypbm_pd == "") {
                        zcy.zcyList.splice(i, 1);  //药品编码为空时移出当前行
                        continue;  //跳出本次循环
                    }

                    //单次剂量判断
                    if (!dcjl_pd || dcjl_pd <= 0) {
                        malert("第【" + row + "】行药品名称【" + ypmc_pd + "】的【单次剂量】不能等于0或为空", 'top', 'defeadted');
                        brjzFoot.ifClick = true;
                        return;
                    }


                    //总量
                    if (!zl_pd || zl_pd <= 0) {
                        // malert("第【" + row + "】行药品名称【" + ypmc_pd + "】的【药品总量】不能为空", 'top', 'defeadted');
                        malert("中药剂数不能为空", 'top', 'defeadted');
                        brjzFoot.ifClick = true;
                        return;
                    }
                    //获取用药方法名称
                    zcy.zcyList[i].yyffmc = brjzFoot.listGetName(zcy.ZyYyffData, yyff_pd, 'yyffbm', 'yyffmc');
                }
                //配方信息
                Yppf = zcy.zcyList;
            }
            if (Yppf == null || Yppf.length <= 0) {
                malert("配方内容为空！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return;
            }

            //处方主表
            var yfbm = chose.serchContent.yfbm;
            var yfmc = brjzFoot.listGetName(chose.YFJson, yfbm, 'yfbm', 'yfmc');
            if (!yfbm) {
                malert("请选择药房！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return;
            }

            var cflxbm = chose.serchContent.cflxbm;
            var cflxmc = brjzFoot.listGetName(chose.CflxJosn, cflxbm, 'cflxbm', 'cflxmc');
            if (!cflxbm) {
                malert("请选择处方类型！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return;
            }

            //门诊诊断
			var lczd = '';
			var mtbzbm = '';
			var mtbzmc = '';
            lczd = chose.getZdgxjg();
				if(chose.Brxx_List.fbbm=='40' || chose.Brxx_List.fbbm=='41'){


					if(chose.popContent.mtbzbm){
						mtbzbm = chose.popContent.mtbzbm;
						mtbzmc = chose.popContent.mtbzmc;
					}else{
						malert("特殊病人病种不能为空！", 'top', 'defeadted');
						brjzFoot.ifClick = true;
						return;
					}
					
					
				}else{
					// lczd = chose.lczd;
				}
            if (!lczd) {
                malert("临床诊断不能为空！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return;
            }

            //药品处方主表
            var Ypcf = {}; //获取当前操作处方对象
            Ypcf.lczd = lczd;
			Ypcf.mtbzbm = mtbzbm;
			Ypcf.mtbzmc = mtbzmc;
            // if(fyxmTab.csqx.cs03001200132 == '1'){
            //     Ypcf.mzks = fyxmTab.dqks;
            // }
            //属于中医处方
            Ypcf.zyfs = zcy.cfcontent.zyfs;
            Ypcf.cfrq = chose.serchContent.cfrq;
            Ypcf.yysm = $('#zybzsm input').val();
            Ypcf.zyzh = zcy.cfcontent.zyzh;
            Ypcf.zyzf = zcy.cfcontent.zyzf;
			if (chose.serchContent.cflb == "2") {
			Ypcf.zytt = zcy.zcyList[0].ypzl
			}	
            //毒麻处方实名信息
            // if (zcy.csqxContent.N03001200129 == 1) {
            if (zcy.cfcontent.dmcfbz == 1 && zcy.csqxContent.N03001200129 == 1) {
                if ((!zcy.cfcontent.lxrxm || !zcy.cfcontent.lxrdh || !zcy.cfcontent.lxrsfzh) && (!userNameBg.Brxx_List.brxm || !userNameBg.Brxx_List.sjhm || !userNameBg.Brxx_List.sfzjhm)) {
                    malert("实名信息不完整！", 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    dbrPop.show();
                    return;
                }

                Ypcf.lxrxm = zcy.cfcontent.lxrxm;
                Ypcf.lxrdh = zcy.cfcontent.lxrdh;
                Ypcf.lxrsfzh = zcy.cfcontent.lxrsfzh;
            }

            if (chose.serchContent.cflb == "2") {
                if (!Ypcf.zyfs) {
                    malert("中医副数不能为空！", 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
            }
            if (!Ypcf.zyfs) {
                Ypcf.zyfs = 0;
            }
            if (!zcy.cfcontent.cfh) {//处方号不存在则进行添加操作
                Ypcf.yfbm = yfbm;
                Ypcf.yfmc = yfmc;
                Ypcf.cflx = cflxbm;
                Ypcf.cflxmc = cflxmc;
                Ypcf.yysmcf = $('#zybzsm input').val();
            } else {//处方号存在则进行修改操作
                Ypcf.cfh = zcy.cfcontent.cfh;
                Ypcf.yfbm = zcy.cfcontent.yfbm;
                Ypcf.yfmc = zcy.cfcontent.yfmc;
                Ypcf.cflx = zcy.cfcontent.cflx;
                Ypcf.cflxmc = zcy.cfcontent.cflxmc;
                Ypcf.yysmcf = $('#zybzsm input').val();
            }
            Ypcf.sflzcf = chose.serchContent.sflzcf;
            /**
             * @yqq 修改时 用药附加费处理
             */
            if (zcy.csqxContent.N03001200127 == '1') {
                for (var q = 0; q < zcy.extraChargesList.length; q++) {
                    if ('用药方法附加费用' == zcy.extraChargesList[q].bzsm) {
                        zcy.extraChargesList.splice(q, 1);
                        q--;
                    }
                }
            }
            //入参
            brjzFoot.parm = {
                list: [{
                    Yppf: Yppf,
                    Ypcf: Ypcf,
                    Ghxx: userNameBg.Brxx_List,
                    Zcxx: userNameBg.Brxx_List,
                    Fjf: zcy.extraChargesList,
                    editFfh: brjzFoot.edit_cfh,
                    jmcf: brjzFoot.N05001200260 || "0"//1表示要单独生成处方号，0否
                }]

            };
            if (fyxmTab.csqx.N03001200173 == '1') {
                brjzFoot.parm.list[0].Ghxx.mzks = fyxmTab.dqks;
                brjzFoot.parm.list[0].Ghxx.mzksmc = fyxmTab.dqksmc;
            }


            for (var i = 0; i < zcy.PfxxJson.length; i++) {
                this.hlyynewYpmx.dcjl = zcy.PfxxJson[i].yyjl
                var cs = zcy.listGetName(zcy.PcData, zcy.PfxxJson[i].yypc, 'pcbm', 'cs');//获取频次次数
                this.hlyynewYpmx.rcs = cs;
                this.hlyynewYpmx.pcbm = zcy.PfxxJson[i].pcbm;
                this.hlyynewYpmx.ypbm = zcy.PfxxJson[i].ypbm;
                this.hlyyYpmx.push(JSON.parse(JSON.stringify(this.hlyynewYpmx)));
            }

            var cfh = "";
            if (!zcy.cfcontent.cfh) {
                cfh = zcy.cfcontent.cfh;
            } else {
                cfh = Ypcf.cfh;
            }

            //验证处方类型、药品种类是否对应；引用处方模板可能会导致处方类型和药品种类对应错误
            var ypzlSet = new Set();
            for (var i = 0; i < this.CflxdyzlList.length; i++) {
                if (Ypcf.cflx == this.CflxdyzlList[i].cflxbm) {
                    ypzlSet.add(this.CflxdyzlList[i].ypzlbm);
                }
            }
            for (var i = 0; i < Yppf.length; i++) {
                if (!ypzlSet.has(Yppf[i].ypzl) && !ypzlSet.has(Yppf[i].ypzlbm)) {
                    malert(Yppf[i].ypmc + ' 药品种类与处方类型不对应', 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
            }

            console.log("-- 处方对应种类验证完成 --");
			brjzFoot.isHd = true;
            var checkParm = {
                ly: "门诊",
                csid: 1,
                brid: userNameBg.Brxx_List.brid,
                cfh: cfh,
                zdmc: lczd,
                czybm: userId,
                ksbm: userNameBg.Brxx_List.ghks,
                nl: brjzFoot.parm.list[0].Zcxx.brnl,
                ypmx: this.hlyyYpmx
            }
            // brjzFoot.$http.post(this.hlyyCheckUrl, JSON.stringify(checkParm)).then(function (data) {
            //     //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
            //     if (json.code != 1) {
            //         malert(json.msg, 'top', 'defeadted');
            //     }
            //     // if (data.body.a == 0) {
            //     //     malert('处方保存成功', 'top', 'success');
            //     //     chose.InShow = false;
            //     //     brjzFoot.ifClick = true;
            //     //     //根据挂号序号再次查询处方信息
            //     //     zcy.Wf_selectCF(1);
            //     //     //执行成功才执行以下操作
            //     //     zcy.saveShow = true;
            //     //     // this.editTitle='编辑';
            //     //     zcy.addShow[zcy.num] = false;
            //     //     $("input[name='text']").each(function () {
            //     //         $(this).attr('disabled', true)
            //     //         $(this).addClass('input-border');
            //     //     });
            //     //
            //     // } else {
            //     //     malert('处方保存失败：' + data.body.c, 'top', 'defeadted')
            //     //     brjzFoot.ifClick = true;
            //     // }
            // });

            if (window.top.J_tabLeft.obj.hlyy == 1) {
				
                if (zcy.csqxContent.cs03001200144) {
                    this.yyjgpt(brjzFoot.parm);
                    return;
                }
                //四川事前智能审核
                if (window.top.J_tabLeft.obj.scZnshUrl) {
                    this.scZnsh(brjzFoot.parm);
                    return;
                }
                //如果是编辑处方，在编辑之前先作废原有处方，防止在途库存冲突 处方作废
                if(brjzFoot.isbjcf && brjzFoot.cfhparam !=null){
                    zcy.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Delete_Mzcf', JSON.stringify(brjzFoot.cfhparam)).then(function (data) {
                        if (data.body.a == 0) {
                            
                            // brjzFoot.finalSaveCf();
							brjzFoot.finalHlyy(Yppf);
                            brjzFoot.isbjcf=false;
                        } else {
                            malert('处方编辑保存失败：' + data.body.c, 'top', 'defeadted')
                        }
                    });
                }else {
                    //此处可跳过审查
                    //brjzFoot.finalSaveCf();
					brjzFoot.finalHlyy(Yppf);
                }
            } else {
                if (zcy.csqxContent.cs03001200144) {
                    this.yyjgpt(brjzFoot.parm);
                    return;
                }
                //四川事前智能审核
                if (window.top.J_tabLeft.obj.scZnshUrl) {
                    this.scZnsh(brjzFoot.parm);
                    return;
                }
                //如果是编辑处方，在编辑之前先作废原有处方，防止在途库存冲突
                if(brjzFoot.isbjcf && brjzFoot.cfhparam !=null){
                    zcy.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Delete_Mzcf', JSON.stringify(brjzFoot.cfhparam)).then(function (data) {
                        if (data.body.a == 0) {
                            
                            brjzFoot.finalSaveCf();
                            brjzFoot.isbjcf=false;
                        } else {
                            malert('处方编辑保存失败：' + data.body.c, 'top', 'defeadted')
                        }
                    });
                }else {
                    brjzFoot.finalSaveCf();
                }


                // brjzFoot.finalSaveCf();
            }
        },
		
		finalHlyy:function(Yppf){
			//合理用药未启用
			var cflist = []
			// var pram = {
			//     cfh: '',
			//     ryghxh: userNameBg.Brxx_List.ghxh
			// }
			// //根据挂号序查询处方
			// $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectCFMX&parm=" + JSON.stringify(pram), function (data) {
			//     if (data.a == 0) {
			//         if (data.d.list != null && data.d.list.length > 0) {
			//             for (var n = 0; n < data.d.list.length; n++) {
			//                 cflist.push(data.d.list[n]);
			//             }
			//         }
			        for (var i = 0; i < Yppf.length; i++) {
			            cflist.push(Yppf[i]);
			        }
			        zcy.loadScript('/newzui/pub/js/McInterface.js', function () {
			            //药品则调用合理用处方审查
			            console.log('病人信息：',userNameBg.Brxx_List)
						HisZScreenData(userNameBg.Brxx_List, '2', cflist);
						
						
			            
			        })
			//     }
			// });
		},
        finalSaveCf: function () {
			console.log("zjbc--------------------brjzFoot.isHd"+brjzFoot.isHd)
			if(!brjzFoot.isHd){
				return false;
			}
			$('#mcDivShade').remove();
			
            brjzFoot.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Save_Mzcf&ksbm=' + userNameBg.Brxx_List.ghks, JSON.stringify(brjzFoot.parm)).then(function (data) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (data.body.a == 0) {
                    malert('处方保存成功', 'top', 'success');
					brjzFoot.isHd = false;
                    chose.InShow = false;
                    brjzFoot.ifClick = true;
                    //根据挂号序号再次查询处方信息
                    zcy.setSunFun();//提醒前行计算金额
                                         if(fyxmTab.csqx.N05001200270 && userNameBg.Brxx_List.sfzjhm){
                         userNameBg.payWx('notifyOutpatientFee',data.body.d.list[0].cfje)
                     }
                    /**
                     * @yqq修改保存后数据处理
                     */
                    if (zcy.csqxContent.N03001200127 == '1') {
                        // @yqq
                        zcy.addShow[zcy.num] = false;
                    }
                    zcy.cfcontent.cfh = data.body.d.list[0].cfh;
                    if (zcy.csqxContent.N03001200157 == '1') {
                        if (data.body.d.list[0].zyfs >= 1) {
                            zcy.cfcontent.isXyOrZy = '1';
                        } else {
                            zcy.cfcontent.isXyOrZy = '0';
                        }
                        brjzFoot.printCf();
                    }
                    var orderNumbers = {
                        orderNumbers: [zcy.cfcontent.cfh]
                    }
                    this.postAjaxT(window.top.J_tabLeft.obj.spdUrl + '/spd-api/spd/outpatient-prescription', JSON.stringify(orderNumbers),function (data) {
                        if (data.body.a == 0) {

                        }
                    },function (){})
                    zcy.num = 0; // 将选中样式执为0 第一个
                    zcy.Wf_selectCF(1);
                    //执行成功才执行以下操作
                    zcy.saveShow = true;
                    // this.editTitle='编辑';
                    zcy.addShow[zcy.num] = false;
                    $("input[name='text']").each(function () {
                        $(this).attr('disabled', true)
                        $(this).addClass('input-border');
                    });

                    /**
                     * @yqq修改保存后数据处理
                     */
                    if (zcy.csqxContent.N03001200127 == '1') {
                        /**
                         * @yqq编辑后保存成功后将edit_cfh置空
                         */
                        //删除处方
                        if (!brjzFoot.edit_cfh) {
                            return
                        }
                        var parm = {
                            list: [{
                                cfh: brjzFoot.edit_cfh
                            }]
                        };
                        zcy.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Delete_Mzcf', JSON.stringify(parm)).then(function (data) {
                            if (data.body.a == 0) {
                                //malert('处方作废成功', 'top', 'success')
                                zcy.cfList.splice(zcy.cfcontent.index, 1);
                                zcy.Wf_selectCF();   //刷新一下病人处方 信息
                                // setTimeout(function () {
                                //     Yfwidth();
                                // },100)
                            } else {
                                //malert('处方作废失败：' + data.body.c, 'top', 'defeadted')
                            }
                        });
                    }


                } else {
                    malert('处方保存失败：' + data.body.c, 'top', 'defeadted')
                    brjzFoot.ifClick = true;
                }
            });
        },
        //保存为模板
        saveModel: function () {
            pop.yysmSelect();
            pop.mbZhyzContent = {
                zhyzlx: '1',
                ypbz: '1'
            };
            pop.popShow = true;
            pop.bcTitle = '保存为模板';
            pop.bcShow = true;
            $(".blRight").css({
                'z-index': '0'
            })
//            if(zcy.cfcontent.yysmbm!=null){
//            	 pop.mbZhyzContent.zyyf=zcy.cfcontent.yysmbm;
//            }
            if (zcy.cfcontent.zyzh != null) {
                pop.mbZhyzContent.zz = zcy.cfcontent.zyzh;
            }
            if (zcy.cfcontent.zyzf != null) {
                pop.mbZhyzContent.zf = zcy.cfcontent.zyzf;
            }
        },
        bxsjshow: function () {
            var that = this;
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
                    that.bxlbbm = json.d.list[0].bxlbbm;
                    that.bxurl = json.d.list[0].url;

                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted')
                }
            });


        },
        ybjsd:function(){
            window.insuranceGbUtils.qd();
            window.insuranceGbUtils.init();
            if (window.insuranceGbUtils.initStatus) {

                $.ajaxSettings.async = false;

                let str_parm={
                    yzhm:zcy.cfcontent.cfh
                }


                $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + brjzFoot.bxurl + "&bxlbbm=" + brjzFoot.bxlbbm + "&types=mzjy&method=getFyYbjsjl&parm=" + JSON.stringify(str_parm),
                    function (json) {
                    console.log(json)
                        if(json.a=='0' && json.d){
                            let res = JSON.parse(json.d);
                            let param_print = {
                                "psn_no": res.RYBH, //人员编号
                                "setl_id": res.JSID, //结算id
                                "mdtrt_id": res.JZID, //就诊id
                                "mdtrtarea_admvs": window.insuranceGbUtils.mdtrtarea_admvs,
                                "fixmedins_code": window.insuranceGbUtils.fixmedins_code,
                                "fixmedins_name": window.insuranceGbUtils.fixmedins_name,
                                "med_type": res.YLLB,
                            }
                            let sfyd = '0';
                            if(res.JZID.indexOf('519900Y') != -1 || res.JZID.indexOf('519900G') != -1){
                                sfyd = '1'
                            }
                            window.insuranceGbUtils.print(param_print,sfyd);
                        }

                    }
                );


            }
        },
        ybmxcx:function(){
                window.insuranceGbUtils.qd();
                window.insuranceGbUtils.init();
                if (window.insuranceGbUtils.initStatus) {

                    $.ajaxSettings.async = false;

                    let str_parm={
                        yzhm:zcy.cfcontent.cfh
                    }

                    $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + brjzFoot.bxurl + "&bxlbbm=" + brjzFoot.bxlbbm + "&types=mzjy&method=getFyYbjsjl&parm=" + JSON.stringify(str_parm),
                        function (json) {
                            console.log(json)
                            if(json.a=='0' && json.d){
                                let res = JSON.parse(json.d);
                                let param_5204 = {
                                    data:{
                                        "psn_no": res.RYBH, //人员编号
                                        "setl_id": res.JSID, //结算id
                                        "mdtrt_id": res.JZID, //就诊id
                                        "exp_content":{
                                            "card_token":''
                                        },
                                    }
                                }
                                let sfyd = '0';
                                if(res.JZID.indexOf('519900Y') != -1 || res.JZID.indexOf('519900G') != -1){
                                    sfyd = '1'
                                }

                                let data = window.insuranceGbUtils.call1("5204",param_5204,res.INSUPLCADMDVS,sfyd);

                                if(data && data.length>0){

                                    historyPop.fyxx = data
                                    historyPop.popShow = true;
                                }

                            }


                        }
                );



            }
        },

        printZlCf: function () {
            var cfh = zcy.cfcontent.cfh;
            if (cfh == null || cfh == "") {
                malert("当前没有处方可以打印", 'top', 'defeadted');
                return false;
            }
            var parm = {
                cfh: cfh
            };
            //帆软打印
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            var reportlets = "";
            if (zcy.cfcontent.isXyOrZy == "0") {  //西药
                reportlets = "[{reportlet: 'fpdy" + frpath + "yfgl" + frpath + "yfgl_cffj.cpt',yljgbm:'" + jgbm + "',ghxh:'" + userNameBg.Brxx_List.ghxh + "',yzhm:'" + cfh + "'}]"
            }else{
                malert("治疗单仅限于西药处方类型！");
                 return false;
            }
            if(!window.top.J_tabLeft.obj.FRorWindow){
                if (FrPrint(reportlets, null, null)) {
                    return;
                }
            }
        },
        //打印处方票据
        printCf: function () {
            //保存电子处方时是否打印0=无，1=有
            /* if (panel.csqxContent.cs00600100110 == '0') {
                 malert("暂时没有打印权限！");
                 return false;
             }*/
			 
            var cfh = zcy.cfcontent.cfh;
            if (cfh == null || cfh == "") {
                malert("当前没有处方可以打印", 'top', 'defeadted');
                return false;
            }
            var parm = {
                cfh: cfh
            };
            //帆软打印
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            var ghxh = zcy.cfcontent.bah;
            var reportlets = "";
            if (zcy.cfcontent.isXyOrZy == "0") {  //西药
                reportlets = "[{reportlet: 'fpdy" + frpath + "yfgl" + frpath + "yfgl_cfj.cpt',yljgbm:'" + jgbm + "',ghxh:'" + ghxh + "',cfh:'" + cfh + "'}]"
            } else {
                //如果维护了中药处方笺网页打印格式，则优先取网页打印
                if (zcy.csqxContent.N03001200174) {
                    loadPage('.ynPrintPage', zcy.csqxContent.N03001200174, function () {
                        // $('.ynPrintPage').html('')
                    });
                    return false;
                }
                reportlets = "[{reportlet: 'fpdy" + frpath + "yfgl" + frpath + "yfgl_zycfj%28mz%29zb.cpt',yljgbm:'" + jgbm + "',ghxh:'" + ghxh + "',cfh:'" + cfh + "'}]"
            }
            if (zcy.cfcontent.isXyOrZy == '1') { //中医
                //取当前处方类型的打印模板
                brjzFoot.cfdymb = zcy.listGetName(chose.ZycflxJson, chose.serchContent.cflxbm, 'cflxbm', 'mbmc');
            } else {
                //取当前处方类型的打印模板
                brjzFoot.cfdymb = zcy.listGetName(chose.XycflxJson, chose.serchContent.cflxbm, 'cflxbm', 'mbmc');
            }
            var isPopUp = null;
            //取自定义类型打印格式模板
            if (zcy.csqxContent.N03001200152 == '1') {
                if (!brjzFoot.cfdymb) {
                    malert("请维护当前处方类型打印模板！", 'top', 'defeadted');
                    return false;
                }
                reportlets = "[{reportlet: '" + brjzFoot.cfdymb + "',yljgbm:'" + jgbm + "',ghxh:'" + ghxh + "',cfh:'" + cfh + "'}]";
            }
            if (zcy.csqxContent.N03001200142 == '1') {//打印预览
                isPopUp = true;
            }
            if(!window.top.J_tabLeft.obj.FRorWindow){
                if (FrPrint(reportlets, null, null, isPopUp, zcy.csqxContent.N03001200175)) {
                    return;
                }
            }
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=printCf&parm=" + JSON.stringify(parm), function (json) {
                if (json.d != null) {
                    brjzFoot.printList = json.d.list;
                    // 性别转换
                    brjzFoot.printList[0]['brxb'] = brjzFoot.brxb_tran[brjzFoot.printList[0]['brxb']];
                    // 年龄转换
                    brjzFoot.printList[0]['nldw'] = brjzFoot.nldw_tran[brjzFoot.printList[0]['nldw']];
                    //处方类型名称
                    brjzFoot.printList[0]['cflxmc'] = json.d.list[0].cflxmc;
                    // 增加其他诊断
                    if (brjzFoot.printList[0]['qtzdmc'] != null) {
                        brjzFoot.printList[0]['lczd'] = brjzFoot.printList[0]['lczd'] + ',' + brjzFoot.printList[0]['qtzdmc']
                    }
                    if (zcy.cfcontent.isXyOrZy == "0") {// 西医
                        var num = Math.ceil(brjzFoot.printList[0]['yppfList'].length / 5);
                        for (var i = 0; i < num; i++) {
                            var cfList = [];
                            for (var j = 0; j < 5; j++) {
                                if (brjzFoot.printList[0]['yppfList'][i * 5 + j] != null) {
                                    cfList.push(brjzFoot.printList[0]['yppfList'][i * 5 + j]);
                                }
                            }
                            brjzFoot.print(cfList);
                        }
                    } else if (zcy.cfcontent.isXyOrZy == "1") {// 中药
                        num = Math.ceil(brjzFoot.printList[0]['yppfList'].length / 20);
                        for (var a = 0; a < num; a++) {
                            cfList = [];
                            for (var b = 0; b < 20; b++) {
                                if (brjzFoot.printList[0]['yppfList'][a * 20 + b] != null) {
                                    cfList.push(brjzFoot.printList[0]['yppfList'][a * 20 + b]);
                                }
                            }
                            brjzFoot.doPrintZycf(cfList);
                        }
                    }
                }
            });
        },
		xtxx :function(){
			xtxxPop.show();
		},
        historyypxx:function(){
            historyPop.popShow = true;
        },

        //处方笺
        print: function (cfList) {
            // 查询打印模板
            var json = {repname: '处方笺'};
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                if (json.d.length == 0) {
                    json.d[0] = printTemplets.getTempletByName('处方笺');
                }
                // 清除打印区域
                brjzFoot.clearArea(json.d[0]);
                // 绘制模板的canvas
                brjzFoot.drawList = JSON.parse(json.d[0]['canvas']);
                brjzFoot.creatCanvas();
                brjzFoot.reDraw();
                // 为打印前生成数据
                brjzFoot.printContent(brjzFoot.printList[0]);
                brjzFoot.printTrend(cfList);
                // 开始打印
                window.print();
            });
        },
        // 中药调用打印
        doPrintZycf: function (zycfList) {
            // 查询打印模板
            var json = {repname: '中药处方笺'};
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                brjzFoot.clearArea(json.d[0]);
                // 绘制模板的canvas
                brjzFoot.drawList = JSON.parse(json.d[0]['canvas']);
                brjzFoot.creatCanvas();
                brjzFoot.reDraw();
                // 为打印前生成数据
                brjzFoot.printContent(brjzFoot.printList[0]);
                brjzFoot.printTrend(zycfList);
                // 开始打印
                window.print();
            });
        },

        // 打印输液卡
        printSyk: function () {
            if (window.top.J_tabLeft.obj.FRorWindow == '1') {
                zhcx.getSy();
            } else {
                var frpath = "";
                if (window.top.J_tabLeft.obj.frprintver == "3") {
                    frpath = "%2F";
                } else {
                    frpath = "/";
                }
                var reportlets = "[{reportlet: 'fpdy" + frpath + "yfgl" + frpath + "yfgl_zld.cpt',yljgbm:'" + jgbm + "',cfh:'" + zcy.cfcontent.cfh + "'}]";
                if (!FrPrint(reportlets)) {
                    zhcx.getSy();
                    return;
                }
            }

        },
        userdzbl: function () {
            if (!userNameBg.Brxx_List.brid || !userNameBg.Brxx_List.ghxh) {
                malert("请选择患者!", 'top', 'defeadted');
                return;
            }
            ;
            //写注册信息
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + userNameBg.Brxx_List.brid + "&json=" + JSON.stringify(this.param), function (json) {
                    if (json.a == "0") {
                        //写就诊信息
                        $.getJSON(
                            "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=M" + userNameBg.Brxx_List.ghxh + "&json=" + JSON.stringify(zcy.param), function (json) {
                                if (json.a == "0") {
                                    $.ajaxSettings.async = false;
                                    var sxdz = "";
                                    var user = "";
                                    var password = "";
                                    //取病历参数
                                    $.getJSON("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(zcy.param), function (json) {
                                        if (json.a == "0") {
                                            zcy.csContent1 = JSON.parse(JSON.stringify(json.d.list[0]));
                                            sxdz = zcy.csContent1.blSxdz;
                                            user = userId;
                                            //取操作员信息
                                            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                                                if (json.a == "0") {
                                                    password = json.d.password;
                                                }
                                            });
                                            if (!sxdz) {
                                                malert("书写地址为空，打开病历失败！", 'top', 'defeadted');
                                                return
                                            }
                                            if (!user) {
                                                malert("用户名为空，打开病历失败！！", 'top', 'defeadted');
                                                return
                                            }
                                            if (!password) {
                                                malert("用户密码为空，打开病历失败！", 'top', 'defeadted');
                                                return
                                            }
                                            var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=M" + userNameBg.Brxx_List.ghxh + ",userid=" + user + ",password=" + password + ",lyzyhmz=1,blhhl=0"; //0医生 1 护理
                                            window.open(url);
                                        }
                                    });

                                } else {
                                    malert("患者信息上传失败失败：" + json.c, 'top', 'defeadted')
                                }
                            });


                    } else {
                        malert("患者信息上传失败失败：" + json.c, 'top', 'defeadted')
                    }
                });
        },
        getHzJksjkdaInfor:function(){
            if (!userNameBg.Brxx_List.sfzh){
                malert("该病人身份证信息缺失无法调用患者健康档案，请先补齐患者身份证号！");
                return;
            }
            if(!searchLeft.caqxContent.cs00600100122||!searchLeft.caqxContent.cs00600100121){
                malert("请联系管理员维护健康档案请求参数！");
                return;
            };
            var user= window.btoa(zcy.csqxContent.cs00600100122.split('_')[0]);
            var password= window.btoa(zcy.csqxContent.cs00600100122.split('_')[1]);
            var organizationcode= window.btoa(zcy.csqxContent.cs00600100122.split('_')[2]);
            var url = zcy.csqxContent.cs00600100121;
            var param = 'user='+user+'&password='+password+'&mpiId/idCard/cardNo='+window.btoa(userNameBg.Brxx_List.sfzh)+"&organizationcode="+window.btoa(organizationcode)+"&docid="+ userId;
            window.open(url+'/ehrview/redirect?'+param)
        },
        getHzJk360Infor:function(){
            var dataMid;
            if (userNameBg.Brxx_List.sfzh){
                dataMid={page:0, size:10, specification: '{"SFZHM":"'+userNameBg.Brxx_List.sfzh+'"}'}
            }else {
                dataMid={page:0, size:10, specification: '{"JKKKH": "'+userNameBg.Brxx_List.brid+'","YLJGDM":"'+jgbm+'"}'}
            }
            $.ajax("/hzzsy/patient/mdmreport", {
                method:'POST',
                //  将数据编码为表单模式
                contentType:'application/x-www-form-urlencoded; charset=UTF-8',
                //  数据必须为JS对象，不可是字符串
                data : dataMid,
                success : function(datas) {
                    console.log(datas)
                    var dat = JSON.parse(datas);
                    window.open(window.location.protocol+"//"+window.location.hostname+":50800/?key=7935e8fd879feb8a392e8fd4a3a29495&mid=" + dat.data.list[0].mdmId+"&ksbm="+userNameBg.Brxx_List.ksbm+"&ksmc="+ $('#ksList option:selected').text()+"&ysbm="+userId+"&ysmc="+userId)
                }
            })
        },
        getHzJksxzzInfor:function(){
            var user={"patientname":userNameBg.Brxx_List.brxm,
                "gender":userNameBg.Brxx_List.brxb,
                "age":userNameBg.Brxx_List.brnl,
                "unit":userNameBg.Brxx_List.nldw,
                "profilesnumber":userNameBg.Brxx_List.ghxh,
                "homeaddress":userNameBg.Brxx_List.jzdmc,
                "cardtype":userNameBg.Brxx_List.sfzjlx,
                "iccard":userNameBg.Brxx_List.sfzh,
                "phonenumber":userNameBg.Brxx_List.sjhm,
                "transfertype":'1',
                "appointmenttype":'0',
                "transferclass":'1',
                "orgcode":jgbm,
                "deptcode":userNameBg.Brxx_List.ghks,
                "doctorcode":userId,
                "patientid":userNameBg.Brxx_List.brid,
                "guardina":'',
                "guardianphonenumber":'',
                "remark":userNameBg.Brxx_List.bzsm,
                "mxbbz":userNameBg.Brxx_List.sfmbbr,
                "t":userNameBg.Brxx_List.tw,
                "p":userNameBg.Brxx_List.mb,
                "r":userNameBg.Brxx_List.hx,
                "bp":userNameBg.Brxx_List.xySsy+'/'+userNameBg.Brxx_List.xySzy,
                "sp02":'',
                "icd10":userNameBg.Brxx_List.jbbm,
                "glu":userNameBg.Brxx_List.xt,
                "birthdate":userNameBg.Brxx_List.csrq.toString().substring(0,10)
            };

            window.open("http://"+location.hostname+":8889/#/zzsq?user="+JSON.stringify(user)+'&yljgbm='+jgbm+'&czybm='+userId+'&treatytype=3')
        },
    }

});

//附加费用处理
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 0,
        scrollLeft: 0,
        title: '',
        sfcy: '',
        scollType: true,
        total: null,
        cfShow: false,//处方调用
        saveTitle: '',
        activeIndex1: undefined,
        hoverIndex1: undefined,
        bcTitle: '',
        ypShow: false,//药品选择
        tjShow: false,//添加附加费用
        wShow: false,//侧滑宽度
        hzShow: false,
        zdShow: false,//诊断处方
        lsShow: false,//历史处方
        fjfjs: "", //附加费用检索
        FjfyJson: [], //附加费用集合
        lsCfList: [], //历史处方处方集合
        lsCfMxList: [],  //历史处方处方明细集合
        lscfjs: "", //历史处方检索
        cfList: [], //处方模板处方集合
        cfMxList: [],//处方模板处方明细集合
        cfdyjs: "",  //处方调用检索
        YPJson: [], //药品集合
        jsypxx: "", //检索药品信息
        dg: {page: 1, rows: 20, sort: "", order: "asc", parm: ""},//分页信息
        selSearch: -1,
        nums: 1,
        lczdJsonList: [], //临床诊断集合
        lczdValue: "", //临床诊断检索内容
        cfmbIndex: '',//选中的处方模板index
        lscfIndex: '',//选中的历史处方index
        beginrq: '',
        endrq: '',
        cfmbsfcy: 0, //针对处方模板判断是否中草药用的
        lscfcxqx: '1', //历史处方判断查询当前病人处方还是当前医生所有处方
        lscfTmp: {},
        cfyyly_tran:{
            '1':'科室',
            '2':'个人'
        },
        cfyylyidx:'2',
        cssfbh:false,
    },
    updated: function () {
        changeWin();
        changHeight();
		
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()), 'date') + ' 00:00:00';
        this.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    brzcList.beginrq = value ;
                } else {
                    panel.beginrq = '';
                }
                //获取一次列表
                brzcList.getLsTemData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    brzcList.endrq = value ;
                } else {
                    panel.endrq = '';
                }
                //获取一次列表
                brzcList.getLsTemData();
            }
        });
		
    },
    methods: {
        hoverMouse1: function (type, index) {
            this.hoverIndex1 = type ? index : undefined;
        },
        activeMousel(index) {
            this.activeIndex1 = index
        },
        //关闭
        closes: function () {
            brzcList.nums = 1;
			zcy.addShow[zcy.num] = true;
            this.isChecked = [];
            this.isCheckAll = false;
            this.activeIndex = undefined;
            this.activeIndex1 = undefined;
            this.hoverIndex1 = undefined;
            this.hoverIndex = undefined;
            $('.blRight').show();

        },
        open: function () {
            //$(".side-form").removeClass('ng-hide');
            brzcList.nums = 0;
            //$(".side-form-bg").addClass('side-form-bg')
        },

        //添加处方诊断信息
        addcfZd: function () {
            pop.bcTitle = '新增诊断信息';
            pop.popShow = true;
            pop.bcShow = false;
            $(".blRight").css({
                'z-index': '0'
            })
        },

        //编辑处方诊断
        bjEdit: function (num) {
            pop.bcTitle = '编辑诊断信息';
            pop.popShow = true;
            pop.bcShow = false;
            $(".blRight").css({
                'z-index': '0'
            })
            if (num == null) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        num = i;
                        break;
                    }
                }
                if (num == null) {
                    malert("请选中你要修改的数据");
                    return false;
                }
            }
            pop.lczdPopContent = JSON.parse(JSON.stringify(this.lczdJsonList[num]));
        },

        //删除
        remove: function (num) {
            var lczdList = [];
            var lczd = {};
            lczd.id = this.lczdJsonList[num].id;
            lczdList.push(lczd);
            if (lczdList.length == 0) {
                malert("请选中您要删除的数据", 'top', 'defeadted');
                return false;
            }
            if (!confirm("请确认是否删除")) {
                return false;
            }
            var json = '{"list":' + JSON.stringify(lczdList) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglLczd&types=delete&',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert("删除成功", 'top', 'success');
                    brzcList.getLczdDate();
                } else {
                    malert("删除失败", 'top', 'defeadted')
                }
            }, function (error) {
                console.log(error);
            });
        },

        //查询临床诊断集合
        getLczdDate: function () {
            brzcList.lczdJsonList = [];
            //后台查询数据
            var parm = {
                rybm: userId,
                ksbm: userNameBg.Brxx_List.ghks,
                parm: this.lczdValue
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglLczd&types=query&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        brzcList.lczdJsonList = data.d.list;  //临床诊断列表赋值
                    }
                }
            });
        },


        //获取药品信息
        Wf_getYpxx: function () {
            var cflxbm = chose.serchContent.cflxbm;
            var yfbm = chose.serchContent.yfbm;
            if (!cflxbm || cflxbm.length == 0) {
                malert("请选择处方类型编码", 'top', 'defeadted');
                return false;
            }

            if (!yfbm) {
                malert("请选择药房", 'top', 'defeadted');
                return false;
            }

            var json = {
                yfbm: yfbm,
                cflx: cflxbm
            }
            brzcList.dg.rows = 50;
            brzcList.dg.parm = brzcList.jsypxx;
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(brzcList.dg) + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    brzcList.YPJson = [];  //先清空
                    if (data.d.list.length > 0) {
                        brzcList.YPJson = data.d.list;
                        // $("#searchYp").focus();  //检索框自动获取焦点
                    }
                } else {
                    malert('获取药品列表失败：' + data.c, 'top', 'defeadted')
                }
            });
        },
        //检索药品/明细费用
        Wf_YpChange: function () {
            //回车选择药品
            if (event.keyCode == 13) {
                brzcList.save('确定');
            }
            //方面键向下
            else if (event.keyCode == 40) {
                if ((this.YPJson.length - 1) == this.selSearch) {
                    this.selSearch = 0;
                } else {
                    this.selSearch++;
                }
                this.isCheckAll = false;
                this.isChecked = [];
                this.isChecked[this.selSearch] = true;
            }
            //方面键向上
            else if (event.keyCode == 38) {
                if (this.selSearch == 0) {
                    this.selSearch = this.YPJson.length - 1;
                } else {
                    this.selSearch--;
                }
                this.isCheckAll = false;
                this.isChecked = [];
                this.isChecked[this.selSearch] = true;
            } else {
                brzcList.Wf_getYpxx(); //检索药品
                this.selSearch = -1;
            }

        },


        //获取处方附加费用列表
        Wf_getFjfy: function () {
            var dg_fjf = {age: '1', rows: '200', sort: 'mxfybm', order: 'asc', parm: brzcList.fjfjs};
            var json = {
                ffylb: '5',
                fylx: '1',
                ypfy: '0'
            };
            brzcList.FjfyJson = [];  //先清空
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmMxfyxm&types=query&dg=' + JSON.stringify(dg_fjf) + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (data.d.list.length > 0) {
                        brzcList.FjfyJson = data.d.list;
                    }
                } else {
                    malert('获取附加费列表失败：' + data.c, 'top', 'defeadted')
                }
            });
        },
        //**************************历史处方调用开始*******************************
        //根据病人id检索出该病人的所有挂号信息
        /*getByBrid: function () {
            var parmGhxx = {
                brid: userNameBg.Brxx_List.brid
            };
            // 请求后台查询门诊挂号详情
            $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryByMzsf&parm=" + JSON.stringify(parmGhxx), function (json) {
                // 注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (json.a == 0) {
                    brzcList.searchghxh = [];
                    if (json.d != null && json.d != "") {
                        for (var i = 0; i < json.d.length; i++) {
                            brzcList.searchghxh.push(json.d[i].ghxh);
                        }
                        //调用根据挂号序号查询历史处方信息
                        brzcList.getLsTemData();
                    } else {
                        malert('未查到相关记录', 'top', 'defeadted')
                    }
                } else {

                    malert(json.c, 'top', 'defeadted')
                }
            });
        },*/

        getData: function () {
            this.getLsTemData();
        },

        getCxqxChange: function (val) {
            if (val) {
                brzcList.lscfcxqx = val[0];
            }
            this.getLsTemData();
        },

        // 调用历史处方的API
        getLsTemData: function () {
            brzcList.lsCfList = [];
            brzcList.lsCfMxList = [];
            var ghxx = {
                //cfys: userId,
                zfbz: "0",
                parm: this.lscfjs,
                rows: 99999,
                page: this.param.page
            };
            //判断查当前病人处方还是医生全部病人处方
            if (brzcList.lscfcxqx == '1') {
                //ghxx.searchghxh=brzcList.searchghxh;
                ghxx.rows = 100;
                ghxx.brid = userNameBg.Brxx_List.brid;
            } else {
                ghxx.beginrq = this.beginrq;
                ghxx.endrq = this.endrq;
            }

            console.log(JSON.stringify(ghxx));
            //后台查询数据
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectCF&parm=" + JSON.stringify(ghxx), function (json) {
                if (json.a == 0) {
                    if (json.d.list != null && json.d.list.length > 0) {
                        brzcList.lsCfList = json.d.list;  //处方目录
                        brzcList.totlePage = Math.ceil(json.d.total / brzcList.param.rows);
                    }
                }
            });
        },
        // 根据历史处方参数调用处方明细（单击）
        getLsTemMxData: function (index) {
            this.lscfIndex = index;
            this.activeIndex = index;
            this.isChecked = [];
            this.isCheckAll = false;
            var cfh = this.lsCfList[index].cfh;
            var yfbm = this.lsCfList[index].yfbm;
            var cflx = this.lsCfList[index].cflx;
            var json = {
                cfh: cfh,
                yfbm: yfbm,
                cflx: cflx
            };
            this.lscfTmp.yysmcf = this.lsCfList[index].yysmcf;
            this.lscfTmp.zytt = this.lsCfList[index].zytt;

            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=lscfKc&json=' + JSON.stringify(json), function (data) {
                brzcList.cfMxList = [];
                if (data.d != null) {
                    brzcList.lsCfMxList = data.d.list;
                }
            });
        },
        // 双击历史处方添加药品
        addLsCfMb: function (index) {
			console.log(zcy.cfList.length)
            //全选
            this.isCheckAll = true;
            for (var i = 0; i < brzcList.lsCfMxList.length; i++) {
                Vue.set(brzcList.isChecked, i, true);
            }
            this.save('引用', index);
			console.log(zcy.cfList.length)
        },
        getFaList: function () {
            this.MtcfJsonList=[];
            this.isChecked=[];
            var param = {ghxh: userNameBg.Brxx_List.ghxh, page: 1, rows: 99999, id: userNameBg.Brxx_List.id};
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=queryZlfa&parm=" + JSON.stringify(param), function (json) {
                if (json.a =='0' && json.d && json.d.list.length != 0) {
                    this.MtcfJsonList = json.d.list;
                }
            });
        },
        resultlyChange: function (val) {
            console.log(val[0])
            this.cfyylyidx = val[0]
            this.cssfbh = true;
            this.$forceUpdate()
            this.getTemData();
        },
        //*******************************处方模板调用开始***********************************
        // 调用处方模板的API
        getTemData: function (event) {
            brzcList.cfList = [];
            brzcList.cfMxList = [];

            var parm = {
                rows: 100,
                sort: 'zhyzbm',
                order: 'desc',
                lx: '1',
                page: this.param.page,
                yyz: '',
                yyks: userNameBg.Brxx_List.ghks,
                parm: this.cfdyjs,
                cflxbm: chose.serchContent.cflxbm,
                sfcy: brzcList.cfmbsfcy,
                ypbz: '1'
            };

            if(brzcList.cfyylyidx =='1'){
                parm.yyz='';
            }else{
                parm.yyz=chose.userInfo.czybm;
            }

            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=queryCfmb&parm=" + JSON.stringify(parm), function (json) {
                if(json.a=='0'){
                    if (json.d.list.length != 0) {
                        // brzcList.cfList = brzcList.cfList.concat(json.d.list);

                        if(brzcList.cssfbh){
                            brzcList.cfList = json.d.list;
                        }else{
                            brzcList.cfList = brzcList.cfList.concat(json.d.list);
                        }

                        brzcList.total = json.d.total;
                        brzcList.scollType = true;
                    }else{
                        brzcList.cfList = [];
                        brzcList.total = 0;
                        brzcList.scollType = true;
                    }
                }

            });
        },
        scrollGata(event) {
            if (event.srcElement.scrollHeight - event.srcElement.scrollTop === event.srcElement.clientHeight) {
                if (event.target.scrollLeft < 0 || this.scrollLeft == event.target.scrollLeft) {
                    if (event.target.scrollTop > this.scrollTop) {
                        if (this.scollType) {
                            this.scollType = false
                            if (this.cfList.length < this.total) {
                                if (this.uilPageBottom() == true) {
                                    this.param.page = this.param.page + 1;
                                    this.getTemData();
                                }
                            } else {
                                // this.loadData='暂无更多数据...'
                            }
                        }
                    }
                }
            }
            this.scrollLeft = event.target.scrollLeft;
            this.scrollTop = event.target.scrollTop
        },
        //根据处方模板参数查询明细
        getTemMxData: function (index) {
            this.cfmbIndex = index;
            brzcList.isChecked = []
            brzcList.isCheckAll = false
            this.activeIndex1 = index
            var zhyzbm = this.cfList[index].zhyzbm;
            var yfbm = this.cfList[index].yfbm;
            var cflxbm = this.cfList[index].cflxbm;
            this.sfcy = this.cfList[index].sfcy;
            var json = {
                zhyzbm: zhyzbm,
                yfbm: yfbm,
                cflxbm: cflxbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=zhyzYp&dg&json=' + JSON.stringify(json), function (data) {
                brzcList.cfMxList = [];
                if (data.d != null) {
                    brzcList.cfMxList = data.d.list;
                }
            });
        },
        // 双击处方模板添加药品
        addCfMb: function (index) {
            //全选
            this.sfcy = this.cfList[index].sfcy;
            this.isCheckAll = true;
            for (var i = 0; i < brzcList.cfMxList.length; i++) {
                Vue.set(brzcList.isChecked, i, true);
            }
            this.save('确定', index);
        },


        //保存
        save: function (saveTitle, index) {

			console.log(zcy.cfList.length)
			
			let tpcflist = JSON.parse(JSON.stringify(zcy.cfList));
            if (brzcList.title == '添加附加费用' && saveTitle == '保存') {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        this.FjfyJson[i].fysl = 1;
                        this.FjfyJson[i].fyje = this.FjfyJson[i].fydj;
                        zcy.extraChargesList.push(this.FjfyJson[i]);
                    }
                }
                this.closes();
                return;
            }
            else if (brzcList.title == '处方调用' && saveTitle == '确定') {
                var pfxx = [];
                //进行数据的判断
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        if (brzcList.cfMxList[i].kcsl <= 0) {
                            malert("选中药品中【" + brzcList.cfMxList[i].ypmc + "】库存告急", 'top', 'defeadted');
                            // brzcList.cfMxList.splice(i, 1);
                            continue;
                        } else {
                            console.log(brzcList.cfMxList[i]);
                            var jbjl = brzcList.cfMxList[i].jbjl;  //药品基本剂量
                            var yyjl = brzcList.cfMxList[i].yyjl;  //用药剂量
                            var zyfs = zcy.cfcontent.zyfs;
                            if (jbjl == null || jbjl == undefined || jbjl <= 0) {
                                jbjl = 1;
                            }
                            if (yyjl == null || yyjl == undefined || yyjl <= 0) {
                                yyjl = 1;
                            }
                            if (zyfs == null || zyfs == undefined || zyfs <= 0) {
                                zyfs = 1;
                            }
                            var cfyl;
                            if (brzcList.cfMxList[i].yyzl) {
                                cfyl = brzcList.cfMxList[i].yyzl;
                            } else {
                                cfyl = Math.ceil(yyjl / jbjl) * zyfs;  //自动算药品总量
                            }
                            brzcList.cfMxList[i].cfyl = cfyl;
                            brzcList.cfMxList[i].fysl = cfyl;
                            pfxx.push(brzcList.cfMxList[i]);  //赋值给处方
                        }
                    }
                }

                index = this.cfmbIndex;
                if (fyxmTab.csqx.cs03001200132 == "1") {
                    chose.resultLxChange([brzcList.cfList[index].yfbm, null, ['serchContent', 'yfbm'], undefined, ''], true);
                } else {
                    //chose.resultLxChange([pfxx[0].yfbm, null, ['serchContent', 'yfbm'], undefined, ''], true);
                }
				//2020-12-24注释
                //chose.resultLxChange([pfxx[0].yfbm, null, ['serchContent', 'yfbm'], undefined, '']);
                //chose.resultLxChange([brzcList.cfList[index].cflxbm, null, ['serchContent', 'cflxbm'], undefined, brzcList.cfList[index].cflxmc], true);
                if (this.sfcy == '1') { //中医
                    //chose.resultLxChange([brzcList.cfMxList[0].yfbm, null, ['serchContent', 'yfbm'], undefined, '中药房']);
					//2020-12-24注释
                    // if (fyxmTab.csqx.cs03001200132 != "1") {
                    //     chose.resultLxChange([chose.serchContent.yfbm, null, ['serchContent', 'yfbm'], undefined, '']);
                    //     chose.resultLxChange([brzcList.cfList[index].cflxbm, null, ['serchContent', 'cflxbm'], undefined, brzcList.cfList[index].cflxmc], true);

                    // }
                    for (var j = zcy.zcyList.length - 1; j >= 0; j--) {
                        if (zcy.zcyList[j] && !zcy.zcyList[j].ypbm) {
                            zcy.zcyList.splice(j, 1);
                        }
                    }
                    for (var n = 0; n < pfxx.length; n++) {
                        zcy.zcyList.push(pfxx[n]);
                    }
                    // Vue.set(zcy, 'zcyList', pfxx);
                    zcy.zyShow = true;
                    zcy.xyShow = false;
                    zcy.cfcontent.yysmbm = brzcList.cfList[index].zyyf;
                    zcy.cfcontent.zyzh = brzcList.cfList[index].zz;
                    zcy.cfcontent.zyzf = brzcList.cfList[index].zf;
                    zcy.cfcontent.zyzf = brzcList.cfList[index].zf;
                    zcy.pubgetzyCfzje();
                } else {
                    // chose.serchContent.yfbm = 1
                    // chose.serchContent.cflb = 1
                    // chose.serchContent.cflxbm = 'A'
                    // chose.resultLxChange([brzcList.cfMxList[0].yfbm, null, ['serchContent', 'yfbm'], undefined, '']);
                    // chose.resultLxChange([brzcList.cfMxList[0].yfbm, null, ['serchContent', 'yfbm'], undefined, '']);
                    zcy.zyShow = false;
                    zcy.xyShow = true;
                    // zcy.PfxxJson=[];
                    for (var i = zcy.PfxxJson.length - 1; i >= 0; i--) {
                        if (zcy.PfxxJson[i] && !zcy.PfxxJson[i].ypbm) {
                            zcy.PfxxJson.splice(i, 1);
                        }
                    }
                    for (var j = 0; j < pfxx.length; j++) {
                        /*if(pfxx[j].yyts==undefined||pfxx[j].yyts==null){
                            pfxx[j].yyts = 1;     //默认天数
                        }*/
                        if (pfxx[j].yypc == undefined || pfxx[j].yypc == null) {
                            pfxx[j].yypc = "01";  //默认频次次
                        }

                        if (fyxmTab.csqx.cs03001200132 == "1" && pfxx[j].fydwmc != null) {
                            // pfxx[j].fysl=pfxx[j].cfyl/pfxx[j].fyjl;
                            pfxx[j].fysl = pfxx[j].yyzl;
                            pfxx[j].fydwmc1 = pfxx[j].fydwmc;
                            pfxx[j].cfyl = pfxx[j].cfyl
                        } else {
                            pfxx[j].fysl = pfxx[j].yyzl;
                            pfxx[j].fydwmc1 = pfxx[j].yfdwmc
                        }
                        zcy.PfxxJson.push(pfxx[j])
                        zcy.pubJS(zcy.PfxxJson.length - 1)
                        // if( pfxx[j].fyjl >0){
                        //     pfxx[j].fysl=pfxx[j].fyjl;
                        //     pfxx[j].fydwmc1 = pfxx[j].fydwmc
                        // }else{
                        //     pfxx[j].fydwmc1 = pfxx[j].yfdwmc
                        // }
                    }
                    zcy.PfxxJson = pfxx;              // 配方编辑区内容列表赋值
                    //获取实时费用总和
                    zcy.pubgetCfzje();
                }

				Vue.set(chose.serchContent, 'cflxbm', brzcList.cfList[index].cflxbm);
				Vue.set(chose.serchContent, 'cflxmc', brzcList.cfList[index].cflxbm);
                this.closes();

                return;
            }
            else if (brzcList.title == '药品选择' && saveTitle == '确定') {
                if (zcy.csqxContent.N03001200107 == '1') {
                    var ypws = 5;
                    if (chose.serchContent.ypws) {
                        ypws = chose.serchContent.ypws;
                    }
                    var a = 0;
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            a = a + 1;
                        }
                    }
                    var total = a + zcy.PfxxJson.length;
                    if (total > ypws) {
                        malert("选择的药品不能超过处方限定数量" + ypws, 'top', 'defeadted');
                        brjzFoot.ifClick = true;
                        return;
                    }
                    // if (zcy.PfxxJson.length >= ypws) {
                    //     malert("处方药品位数超过限定数量: " + ypws, 'top', 'defeadted');
                    //     brjzFoot.ifClick = true;
                    //     return;
                    // }
                }
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        // if (!zcy.pubKss(this.YPJson[i])) return; //对抗生素药物使用的判定
                        //zcy.pubKss(this.YPJson[i]); //判断抗生素是否允许使用
                        //判断药品是否已经存在
                        var ypbm = this.YPJson[i].ypbm;
                        if (zcy.PfxxJson != null && zcy.PfxxJson != undefined && zcy.PfxxJson.length > 0) {
                            for (var j = 0; j < zcy.PfxxJson.length; j++) {
                                if (ypbm == zcy.PfxxJson[j].ypbm) {
                                    var r = mconfirm("第【" + (j + 1) + "】行药品【" + this.YPJson[i].ypmc + "】已经存在，确定需要添加吗？");
                                    if (r == false) {
                                        return false;
                                    }
                                }
                            }
                        }
                        //判断是属于中药处方还是西药处方
                        if (chose.serchContent.cflb == 2) {//中药处方
                            zcy.zcyList.push(this.YPJson[i]);
                        } else {
                            zcy.PfxxJson.push(this.YPJson[i]);  //赋值给处方
                            //zcy.PfxxJson[zcy.PfxxJson.length - 1].yyts = 1;     //默认天数
                            zcy.PfxxJson[zcy.PfxxJson.length - 1].yypc = "01";  //默认频次
                        }

                    }
                }
                this.isChecked = [];
                brzcList.jsypxx = null;
                return;
            }
            else if (brzcList.title == '历史处方 红色为作废处方' && saveTitle == '引用') {
                // if (!zcy.addCf()) {//调用新增处方
                //     malert("请先保存新的处方之后再调用！", 'top', 'defeadted')
                //     return false;
                // }
				console.log(zcy.cfList.length)
                var pfxx = [];
                //进行数据的判断
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        if (brzcList.lsCfMxList[i].kcsl <= 0) {
                            malert("选中药品中【" + brzcList.lsCfMxList[i].ypmc + "】库存告急", 'top', 'defeadted');
                            // brzcList.lsCfMxList.splice(i, 1);
                            continue;
                        } else {
                            pfxx.push(brzcList.lsCfMxList[i]);  //赋值给处方
                        }
                    }
                }
console.log(zcy.cfList.length)
                index = this.lscfIndex;
                chose.resultLxChange([pfxx[0].yfbm, null, ['serchContent', 'yfbm'], undefined, ''], true);
                chose.resultLxChange([brzcList.lsCfList[index].cflx, null, ['serchContent', 'cflxbm'], undefined, brzcList.lsCfList[index].cflxmc], true);
				zcy.cfList = tpcflist
				console.log(zcy.cfList.length)
                if (brzcList.lsCfList[index]['isXyOrZy'] == '1') { //中医
				console.log(zcy.cfList.length)
                    zcy.PfxxJson = [];
                    for (var j = zcy.zcyList.length - 1; j >= 0; j--) {
                        if (zcy.zcyList[j] && !zcy.zcyList[j].ypbm) {
                            zcy.zcyList.splice(j, 1);
                        }
                    }
                    for (var n = 0; n < pfxx.length; n++) {
                        zcy.zcyList.push(pfxx[n]);
                    }
                    zcy.zyShow = true;
                    zcy.xyShow = false;
					console.log(zcy.cfList.length)
                    zcy.pubgetzyCfzje();
                    zcy.cfcontent.yysmcf = this.lscfTmp.yysmcf;
                    zcy.cfcontent.zytt = this.lscfTmp.zytt;
                    zcy.cfcontent.zyzf = brzcList.lsCfList[index].zyzf;
                    zcy.cfcontent.zyzh = brzcList.lsCfList[index].zyzh;
					console.log(zcy.cfList.length)
                    // zcy.zxksList, zcy.jcxmMxJson[i].zxks, 'ksbm', 'ksmc'
                    zcy.cfcontent.yysmbm = zcy.listGetName(zcy.jsonList, zcy.cfcontent.yysmcf, 'yysmmc', 'yysmbm');
                    // zcy.jsonList
                    this.lscfTmp = {};
					console.log(zcy.cfList.length)
                    //zcy.setSum();
                } else {
                    zcy.zcyList = [];
                    zcy.zyShow = false;
                    zcy.xyShow = true;
                    pfxx.sort(function (a, b) {
                        return a.fzh - b.fzh
                    })
                    for (var j = zcy.PfxxJson.length - 1; j >= 0; j--) {
                        if (zcy.PfxxJson[j] && !zcy.PfxxJson[j].ypbm) {
                            zcy.PfxxJson.splice(j, 1);
                        }
                    }
                    for (var n = 0; n < pfxx.length; n++) {
                        zcy.PfxxJson.push(pfxx[n]);
                    }
                    // 配方编辑区内容列表赋值
                    for (var i = 0; i < zcy.PfxxJson.length; i++) {
                        if (zcy.PfxxJson[i].yypc == undefined || zcy.PfxxJson[i].yypc == null) {
                            zcy.PfxxJson[i].yypc = "01";  //默认频次次
                        }
                        if (zcy.PfxxJson[i].fyjl > 0) {
                            zcy.PfxxJson[i].fysl = zcy.PfxxJson[i].fyjl;
                            zcy.PfxxJson[i].fydwmc1 = zcy.PfxxJson[i].fydwmc
                        } else {
                            zcy.PfxxJson[i].fydwmc1 = zcy.PfxxJson[i].yfdwmc
                        }
                        zcy.pubJS(i);
                    }
                    //获取实时费用总和
                    zcy.pubgetCfzje();
                }
                this.closes();
                return;
            }
            else if (brzcList.title == '门特处方' && saveTitle == '引用') {
                var pfxx = [];
                //进行数据的判断
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        var obj = {
                            ypmc: brzcList.MtcfJsonList[i].zlorypmc,
                            ypbm: brzcList.MtcfJsonList[i].zlorypbm,
                            ypgg: brzcList.MtcfJsonList[i].gg,
                            yyts: brzcList.MtcfJsonList[i].yka368,
                            yyjl: brzcList.MtcfJsonList[i].dl,
                            fysl: brzcList.MtcfJsonList[i].yyzl,
                        }
                        pfxx.push(obj);  //赋值给处方

                    }
                }
                zcy.PfxxJson=pfxx;
                this.closes()
            }
        }
    }
});
var zhcx = new Vue({
    el: '#cqyzd',
    mixins: [dic_transform, tableBase, baseFunc, mformat],
    data: {
        FRorWindow:window.top.J_tabLeft.obj.FRorWindow,
        syShow: false,
        printList: '',
        printYist: '',
        printItem: '',
        printItarr: [],
        dests: [],
        ArryFive:[],//5个一组
        x:0,
    },

    methods: {
        setMC: function (item) {
            return !item.zxdlx ? item.yyffmc : this.zxdlx_tran[item.zxdlx]
        },
        isShowItem: function (index) {
            if (this.printYist[index + 1] == null) {
                return true;
            }
            if (this.printYist[index]['fzh'] == this.printYist[index + 1]['fzh'] && this.printYist[index]['fzh'] != 0) {
                if (this.printYist[index]['yyffmc'] == this.printYist[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        },
        toIndex: function (index) {
            return index += this.printYist.length;
        },
        sameSE: function (index) {
            var fzh = this.printYist[index]['fzh'];
            // index = this.toIndex(index);
            // if (index >= this.printYist.length) return null;
            if (fzh == 0) return false;
            // if (index == 0 && fzh == this.jsonList[num]['yzxx'][index + 1] || this.jsonList[num]['yzxx'][index]['fzxh']) {
            if (index == 0 && fzh == this.printYist[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.printYist.length - 1) {
                var nextFzh = this.printYist[index + 1]['fzh'];
                var prvFzh = this.printYist[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'end'
        },
        tzqm: function (index) {
            if (this.sameSE(index) != 'start' && this.sameSE(index) != 'all') {
                return true;
            } else {
                return false;
            }
        },
        getSy1:function(){
            $('.brjz').removeClass('printHide');
            zhcx.dests=[];
            zhcx.printItarr=[];
            var cfh = zcy.cfcontent.cfh;
            if (!cfh) {
                malert("当前没有输液卡可以打印");
                return false;

            }
            var parm = {
                cfh: cfh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=MzysZlglBrjz&types=printSyt&parm=" + JSON.stringify(parm), function (json) {
                if (json.d != null) {
                    console.log(json.d);
                    zhcx.printList = json.d;
                    var dataList = new Array();
                    var newList = new Array();
                    zhcx.printYist = json.d.yppfList;
                    for(var i = 0; i < zhcx.printYist.length; i ++){
                        dataList.push( zhcx.printYist[i].fzh );
                        if(zhcx.printYist[i].yysm==null||zhcx.printYist[i].yysm==""){
                            for(var j = 0; j <zhcx.printYist.length; j ++){
                                if(parseFloat(zhcx.printYist[i].fzh) == parseFloat(zhcx.printYist[j].fzh)){
                                    if(zhcx.printYist[j].yysm!=null||zhcx.printYist[j].yysm!="")
                                        zhcx.printYist[i].yysm=zhcx.printYist[j].yysm;
                                }
                            }
                        }

                    }
                    dataList = zhcx.unique1(dataList);
                    for (i = 0; i < dataList.length; i ++) {
                        newList.push({"fzh": dataList[i], "list": new Array()});
                    }

                    for(var i = 0; i < zhcx.printYist.length; i ++){
                        for(var j = 0; j < newList.length; j ++){
                            if(parseFloat(newList[j].fzh) == parseFloat(zhcx.printYist[i].fzh)){
                                newList[j].list.push( zhcx.printYist[i] );
                                break;
                            }
                        }
                    }
                    for(var i=0;i<newList.length;i++){
                        zhcx.dests.push(newList[i].fzh);
                        zhcx.printItarr.push(JSON.parse(JSON.stringify(newList[i].list)))
                    }
                    zhcx.x = zhcx.printItarr.length % 5 ? Math.ceil(zhcx.printItarr.length / 5):(zhcx.printItarr.length / 5);
                    zhcx.syShow = true;
                    $('.printArea').html('')
                    zhcx.$nextTick(function () {
                        zhcx.syShow = false
                        window.print();
                    })

                }

            });
        },
        getSy: function () {
            if(window.top.J_tabLeft.obj.FRorWindow == '1'){
                this.getSy1();
                return false;
            }
            var cfh = zcy.cfcontent.cfh;
            if (!cfh) {
                malert("当前没有输液卡可以打印", 'top', 'defeadted');
                return false;
            }
            var parm = {
                cfh: cfh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=printSyt&parm=" + JSON.stringify(parm), function (json) {
                if (json.d != null) {
                    zhcx.printList = json.d;
                    var newList = [];
                    //处方日期
                    zhcx.printYist = json.d.yppfList;
                    for (var j = 0; j < zhcx.printYist.length; j++) {
                        if (zhcx.printYist[j].sysd == null || zhcx.printYist[j].sysd == undefined) {
                            zhcx.printYist[j].sysd = " ";
                        }
                        if (zhcx.printYist[j].sysddw == null || zhcx.printYist[j].sysddw == undefined) {
                            zhcx.printYist[j].sysddw = " ";
                        }
                        if (zhcx.printYist[j].yyffmc == null) {
                            zhcx.printYist[j].yyffmc = " ";
                        }
                        if (zhcx.tzqm(j)) {
                            newList.push(zhcx.printYist[j]);
                            newList.push({'fzh': null, 'ksrq': zhcx.printYist[j]['ksrq']});
                        } else {
                            newList.push(zhcx.printYist[j]);
                        }
                        // }
                    }
                    zhcx.printYist = newList
                    console.log(zhcx.printItarr);
                    console.log(zhcx.dests);
                    zhcx.syShow = true
                    $('.printArea').html('')
                    zhcx.$nextTick(function () {
                        window.print();
                        zhcx.printItem = [];
                        zhcx.syShow = false
                    })
                    console.log('打印')
                }

            });


        },
        unique1: function (array) {
            var n = []; //一个新的临时数组
            //遍历当前数组
            for (var i = 0; i < array.length; i++) {
                //如果当前数组的第i已经保存进了临时数组，那么跳过，
                //否则把当前项push到临时数组里面
                if (n.indexOf(array[i]) == -1) n.push(array[i]);
            }
            return n;

        },
    },
    filters: {
        formDate: function (value) {
            var d = new Date(value);
            return (d.getFullYear()) + '-' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '-' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate())
        },
        formDatehanzi: function (value) {
            var d = new Date(value);
            return (d.getFullYear()) + '年' + ((d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1) + '月' + (d.getDate() < 10 ? '0' + d.getDate() : d.getDate()) + '日'
        },
        formAverage: function (list, value) {
            var num = 0
            for (var i = 0; i < list.length; i++) {
                num = num + list[i][value]
            }
            return num / 2
        },
    },
})


//模板信息
var pop = new Vue({
    el: '#pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        bcShow: false,
        bcTitle: '',
        lczdPopContent: {}, //临床诊断对象
        ifClick: true,
        mbZhyzContent: {},  //另存模板对象
        yysmList: [],
    },
    methods: {
        yysmSelect: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmzcyyysm&types=query&dg=" + JSON.stringify(this.param), function (json) {
                pop.yysmList = json.d.list;
            });
        },
        //保存
        saveMb: function () {
            pop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
            if (this.bcShow == false) { //临床诊断
                if (!pop.ifClick) return;
                pop.ifClick = false;
                this.lczdPopContent.rybm = userId;
                this.lczdPopContent.ksbm = userNameBg.Brxx_List.ghks;
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglLczd&types=save',
                    JSON.stringify(this.lczdPopContent))
                    .then(function (data) {
                        if (data.body.a == 0) {
                            malert("数据更新成功", 'top', 'success');
                            pop.lczdPopContent = {};
                            pop.ifClick = true;
                            brzcList.getLczdDate();
                        } else {
                            malert("数据失败", 'top', 'defeadted');
                            pop.ifClick = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
            } else {  //另存模板
                if (!pop.mbZhyzContent.zhyzmc) {
                    malert("请输入医嘱名称", 'top', 'defeadted');
                    pop.popShow = true;
                    return;
                }
                if (!pop.ifClick) return;
                pop.ifClick = false;
                if (!zcy.cfcontent.cfh) {
                    malert("当前无处方模板可以保存", 'top', 'defeadted');
                    return;
                }
                pop.mbZhyzContent.yfbm = chose.serchContent.yfbm;
                pop.mbZhyzContent.cflxbm = chose.serchContent.cflxbm;
                pop.mbZhyzContent.yyks = userNameBg.Brxx_List.ghks;
                pop.mbZhyzContent.yyz = userId;
                pop.mbZhyzContent.insertbz = "1";
                var parm = {};
                if (zcy.cfcontent.isXyOrZy == '1') { //中医
                    parm = {
                        list: [
                            {
                                zhyz: pop.mbZhyzContent,
                                yppfs: zcy.zcyList
                            }
                        ]
                    };
                } else {
                    parm = {
                        list: [
                            {
                                zhyz: pop.mbZhyzContent,
                                yppfs: zcy.PfxxJson
                            }
                        ]
                    };
                }
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=insertCmb', JSON.stringify(parm)).then(function (data) {
                    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                    if (data.body.a == 0) {
                        malert("处方保存成功", 'top', 'success');
                        pop.ifClick = true;
                        brzcList.getTemData();         //处方模板查询处方
                    } else {
                        malert("处方保存失败：" + data.body.c, 'top', 'defeadted');
                        pop.ifClick = true;
                    }
                });
            }

        },
        //取消
        cancel: function () {
            pop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
        },
    },
})

var dmPop = new Vue({
    el: '#dmPop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        popTitle: '',
    },
    methods: {
        //打印毒麻处方告知书
        print: function () {
            dmPop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
            // 调用帆软打印 毒麻处方同意书
            var frpath = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                frpath = "%2F";
            } else {
                frpath = "/";
            }
            var reportlets = "[{reportlet: 'fpdy" + frpath + "yfgl" + frpath + "dmcf_tys.cpt'}]";
            console.log(reportlets);
            if (FrPrint(reportlets)) {
                return;
            }

            if (zcy.csqxContent.N03001200129 == 1) {
                dbrPop.show();
            }
        },
        //取消
        cancel: function () {
            dmPop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})

            if (zcy.csqxContent.N03001200129 == 1) {
                dbrPop.show();
            }
        },
    },
});

var dbrPop = new Vue({
    el: '#dbrPop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        popTitle: '实名认证信息',
        dbrxx: {}, //代办人信息
        dmbrxx: {} //毒麻本人信息
    },

    methods: {
        //保存
        saveDbrxx: function () {

            if ((!dbrPop.dmbrxx.brxm || !dbrPop.dmbrxx.sjhm || !dbrPop.dmbrxx.sfzjhm) && (!dbrPop.dbrxx.xm || !dbrPop.dbrxx.sfzh || !dbrPop.dbrxx.lxdh)) {
                malert("请至少完整填写 本人 或者 代办人 信息！", 'top', 'defeadted');
                return;
            }

            if (!dbrPop.dbrxx.xm && !dbrPop.dmbrxx.brxm) {
                malert("姓名不能为空！", 'top', 'defeadted');
                return false;
            }

            //座机验证
            var zjyz = /^([0-9]{3,4})?[0-9]{7,8}$/;
            //手机验证
            var sjyz = /^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[02356789][0-9]{8}|147[0-9]{8}|1349[0-9]{7})$/;

            if (dbrPop.dbrxx.lxdh && dbrPop.dbrxx.lxdh !='-') {
                var value = dbrPop.dbrxx.lxdh.trim();
                if (!zjyz.test(value) && !sjyz.test(value)) {
                    malert("联系电话格式不正确！", 'top', 'defeadted');
                    return false;
                }
            } else if (dbrPop.dmbrxx.sjhm && dbrPop.dmbrxx.sjhm !='-') {
                var value2 = dbrPop.dmbrxx.sjhm.trim();
                if (!zjyz.test(value2) && !sjyz.test(value2)) {
                    malert("联系电话格式不正确！", 'top', 'defeadted');
                    return false;
                }
            } else {
                malert("联系电话不能为空！", 'top', 'defeadted');
                return false;
            }


            if (dbrPop.dbrxx.sfzh && dbrPop.dbrxx.sfzh !='-') {
                if (dbrPop.dbrxx.sfzh.length != 18) {
                    malert("身份证件号码长度非法！", 'top', 'defeadted');
                    return false;
                }
            } else if (dbrPop.dmbrxx.sfzjhm && dbrPop.dmbrxx.sfzjhm !='-') {
                if (dbrPop.dmbrxx.sfzjhm.length != 18) {
                    malert("身份证件号码长度非法！", 'top', 'defeadted');
                    return false;
                }
            } else {
                malert("身份证号不能为空！", 'top', 'defeadted');
                return false;
            }
            if(!userNameBg.Brxx_List.sfzjhm){
                this.setUserData()
            }
            if (this.dbrxx.xm) {
                zcy.cfcontent.lxrxm = this.dbrxx.xm;
            }
            if (this.dbrxx.lxdh) {
                zcy.cfcontent.lxrdh = this.dbrxx.lxdh;
            }
            if (this.dbrxx.sfzh) {
                zcy.cfcontent.lxrsfzh = this.dbrxx.sfzh;
            }
            if (this.dmbrxx.brxm) {
                userNameBg.Brxx_List.brxm = this.dmbrxx.brxm;
            }
            if (this.dmbrxx.sjhm) {
                userNameBg.Brxx_List.sjhm = this.dmbrxx.sjhm;
            }
            if (this.dmbrxx.sfzjhm) {
                userNameBg.Brxx_List.sfzjhm = this.dmbrxx.sfzjhm;
            }
            dbrPop.popShow = false;


        },
        setUserData:function (){
            var parm={
                sfzjhm:this.dmbrxx.sfzjhm,
                brid:userNameBg.Brxx_List.brid
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=updateMjcf&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                }
            });
        },
        //取消
        cancel: function () {
            dbrPop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
        },

        //reset
        show: function () {
            if (zcy.cfcontent.lxrxm) {
                this.dbrxx.xm = zcy.cfcontent.lxrxm;
            } else {
                this.dbrxx.xm = '-';
            }

            if (zcy.cfcontent.lxrdh) {
                this.dbrxx.lxdh = zcy.cfcontent.lxrdh;
            } else {
                this.dbrxx.lxdh = '-';
            }

            if (zcy.cfcontent.lxrsfzh) {
                this.dbrxx.sfzh = zcy.cfcontent.lxrsfzh;
            } else {
                this.dbrxx.sfzh = '-';
            }

            this.dmbrxx.sfzjhm = userNameBg.Brxx_List.sfzh;
            this.dmbrxx.sjhm = userNameBg.Brxx_List.sjhm;
            this.dmbrxx.brxm = userNameBg.Brxx_List.brxm;

            dbrPop.popShow = true;
        },


    },






    //vue
});
var xtxxPop = new Vue({
    el: '#xtxxPop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        popTitle: '血透信息',
        xytxparam: {
			ghxh:userNameBg.Brxx_List.ghxh,
		}, //
		bxlbbm:null,
		bxurl:null,
    },

    methods: {
		
        //保存
        saveXtxx: function () {
			console.log(xtxxPop.xytxparam)
            
			
            $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + xtxxPop.bxurl + "&bxlbbm=" + xtxxPop.bxlbbm + "&types=mzjy&method=xytx&parm="
                + JSON.stringify(xtxxPop.xytxparam),
                function (json) {
                    if (json.a == 0) {
						xtxxPop.xytxparam = {
							ghxh:userNameBg.Brxx_List.ghxh,
						}
                        xtxxPop.popShow = false;
                        malert("血透信息保存成功!", "right", "success");
                    } else {
                        malert("血透信息保存失败  " + json.c, 'right', 'defeadted');
                    }
                });


        },
        
        //取消
        cancel: function () {
            xtxxPop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
        },

        //reset
        show: function () {
            var that = this;
            var param = {bxjk: "B07"};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryBxlb&types=query&json=" + JSON.stringify(param), function (json) {
                if (json.a == 0 && json.d.list.length > 0) {
            		that.bxlbbm = json.d.list[0].bxlbbm;
            		that.bxurl = json.d.list[0].url;
            		// that.bxurl = 'http://172.16.111.161:10015/interface/cdyhyb/post'
					
					$.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + xtxxPop.bxurl + "&bxlbbm=" + xtxxPop.bxlbbm + "&types=mzjy&method=getXytx&parm="
					    + JSON.stringify(xtxxPop.xytxparam),
					    function (json) {
					        if (json.a == 0) {
								if(json.d){
									xtxxPop.xytxparam = JSON.parse(json.d)
								}else{
									xtxxPop.xytxparam = {
										ghxh:userNameBg.Brxx_List.ghxh,
									}
								}
					            xtxxPop.popShow = true;
					        }
					    });
					
					
            		
                } else {
                    malert("保险类别查询失败!" + json.c, 'right', 'defeadted')
                }
            });

            
        },


    },






    //vue
});


var historyPop = new Vue({
    el: '#historyPop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        popTitle: '费用信息',
        brxx:userNameBg.Brxx_List,
        fyxx:[],
        sfxmdj:{
            '01':'甲类',
            '02':'乙类',
            '03':'丙类',
        }
    },

    methods: {
        //取消
        cancel: function () {
            historyPop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
        },


    },
    //vue
});


//合理用药回调
function cfdealwithhis(result) {
	console.log("hlyy--------------------brjzFoot.isHd"+brjzFoot.isHd)
			if(!brjzFoot.isHd){
				return false;
			}
			
    /*result:审查的警示级别 4-关注（黄灯）3-慎用（橙灯）2-严重（红灯） 1-禁忌（黑灯）
          0-没问题（蓝灯）
         -100:加载客户端dll失败     -99:超时                  -10：服务端返回ERROR
         -9：无开嘱和停嘱时间       -5：出院带药不参与审查    -4：已停嘱
         -3：已作废*/

    // if ( 1 == result || 2 == result || 3 == result || 4 == result) {
    //     brjzFoot.ifClick = true;
    //     //malert("该处方存在！", 'top', 'defeadted');
	// 	let tips = '';
	// 	if ( 1 == result){
	// 		tips='禁忌'
	// 	}else if(2 == result){
	// 		tips='严重'
	// 	}else if(3 == result ){
	// 		tips='慎用'
	// 	}else{
	// 		tips='关注'
	// 	}
	// if (common.openConfirm("处方存在"+tips+"用药，确认是否保存", function () {
	// 						sendDate()
	// 					})) {
	// 						malert("您已取消保存！", 'top', 'defeadted');
	// 						return;
	// 					}
    //
    // }else {
    //     sendDate()
    // }

    sendDate()

}

function sendDate(){
	//保存数据
	brjzFoot.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Save_Mzcf&ksbm=' + userNameBg.Brxx_List.ghks, JSON.stringify(brjzFoot.parm)).then(function (data) {
	    //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
	    if (data.body.a == 0) {
	        malert('处方保存成功', 'top', 'success');
	        chose.InShow = false;
	        brjzFoot.ifClick = true;
	        //根据挂号序号再次查询处方信息
	brjzFoot.isHd = false;
	        /**
	         * @yqq修改保存后数据处理
	         */
	        if (zcy.csqxContent.N03001200127 == '1') {
	            // @yqq
	            zcy.addShow[zcy.num] = false;
	        }
            if(fyxmTab.csqx.N05001200270 && userNameBg.Brxx_List.sfzjhm){
                userNameBg.payWx('notifyOutpatientFee',data.body.d.list[0].cfje)
            }
	        zcy.num = 0; // 将选中样式执为0 第一个
	        zcy.Wf_selectCF(1);
	        //执行成功才执行以下操作
	        zcy.saveShow = true;
	        // this.editTitle='编辑';
	        zcy.addShow[zcy.num] = false;
	        $("input[name='text']").each(function () {
	            $(this).attr('disabled', true)
	            $(this).addClass('input-border');
	        });
	
	
	        /**
	         * @yqq修改保存后数据处理
	         */
	        if (zcy.csqxContent.N03001200127 == '1') {
	            /**
	             * @yqq编辑后保存成功后将edit_cfh置空
	             */
	            //处方号为空直接返回
	            if (!brjzFoot.edit_cfh) {
	                return
	            }
	            var parm = {
	                list: [{
	                    cfh: brjzFoot.edit_cfh
	                }]
	            };
	            zcy.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Delete_Mzcf', JSON.stringify(parm)).then(function (data) {
	                if (data.body.a == 0) {
	                    //malert('处方作废成功', 'top', 'success')
	                    zcy.cfList.splice(zcy.cfcontent.index, 1);
	                    zcy.Wf_selectCF();   //刷新一下病人处方 信息
	                    // setTimeout(function () {
	                    //     Yfwidth();
	                    // },100)
	                } else {
	                    //malert('处方作废失败：' + data.body.c, 'top', 'defeadted')
	                }
	            });
	        }
	
	
	    } else {
	        malert('处方保存失败：' + data.body.c, 'top', 'defeadted')
	        brjzFoot.ifClick = true;
	    }
	});
}

var change = false;

function remove(obj) {
    $(obj).parent().remove();
}

var check = '', copy = [];
document.onkeydown = function (event) {
    if (event.ctrlKey && event.keyCode === 65) {
        console.log('ctrl + a')
        zcy.checkAll = true
        zcy.numClass = null
    }
    if (event.ctrlKey && event.keyCode === 67) {
        console.log('ctrl + c')
        if (zcy.checkAll) {
            check = zcy.xycfList
        } else if (zcy.numClass != null) {
            check = zcy.xycfList[zcy.numClass]
        } else if (zcy.isCheck.length > 0) {
            for (var i = 0; i < zcy.isCheck.length; i++) {
                if (zcy.isCheck[i] == true) copy.push(JSON.parse(JSON.stringify(zcy.xycfList[i])))
            }
            check = copy
            copy = []
        }
    }
    if (event.ctrlKey && event.keyCode === 86) {
        console.log('ctrl + v');
        if (check != '' || zcy.numClass) {
            zcy.xycfList = zcy.xycfList.concat(JSON.parse(JSON.stringify(check)))
        }
    }
}
//页面加载完统一加载



function changHeight() {
    var tablebodyy = $('.zuiTableBody');
    for (var i = 0; i < tablebodyy.length; i++) {
        // if($(tablebodyy[i]).attr('data-no-change-height') === "")continue;
        var zuiheight = $('body').outerHeight() - $(tablebodyy).eq(i).offset().top - 145;
        $(tablebodyy).eq(i).attr('data-no-change-height', '')
        if ($(tablebodyy).eq(i).parent().is('.zui-table-fixed')) {
            zuiheight = zuiheight - 12
        }
        $(tablebodyy[i]).css({
            'height': zuiheight,
            'overflow-y': 'auto'
        });

    }
}

//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});

function loadPage(dom, name, cb) {
    $(dom).load(name + '.html', function () {
        cb && cb()
    });
}

 function clickExport(ypname,ypbm,userId,userName,deptId){
    //console.log('药品编码：',ypbm)
     if (ypname){
         MCInit(userId,userName,deptId,"deptName")
         //查询初始化
         var drug = new Params_MC_queryDrug_In();
         drug.ReferenceCode = ypbm;//"PAT0000000358";//"16035+注射用盐酸吡硫醇+粉针剂+200mg+山西振东泰盛制药有限公司"; //"22305"; //药品编码
         drug.CodeName = ypname;//"注射用盐酸吡硫醇";  //药品名称
         MC_global_queryDrug = drug;

         MDC_DoRefDrug(11)

         //console.log(userNameBg.Brxx_List)
     }
}

function zhongyao(ypname,ypbm){
    console.log('中药材编码：',ypbm)
    if (ypname){
        MCInit('00000','yp','21',"deptName")
        //查询初始化
        var drug = new Params_MC_queryDrug_In();
        drug.ReferenceCode = ypbm;//"PAT0000000358";//"16035+注射用盐酸吡硫醇+粉针剂+200mg+山西振东泰盛制药有限公司"; //"22305"; //药品编码
        drug.CodeName = ypname;//"注射用盐酸吡硫醇";  //药品名称
        MC_global_queryDrug = drug;

        MDC_DoRefDrug(24)
    }
}

function importInfoClick(ypname,ypbm){
    //console.log('重要信息西药：',ypbm)
    if (ypname){
        MCInit('00000','yp','21',"deptName")
        //查询初始化
        var drug = new Params_MC_queryDrug_In();
        drug.ReferenceCode = ypbm;//"PAT0000000358";//"16035+注射用盐酸吡硫醇+粉针剂+200mg+山西振东泰盛制药有限公司"; //"22305"; //药品编码
        drug.CodeName = ypname;//"注射用盐酸吡硫醇";  //药品名称
        MC_global_queryDrug = drug;

        MDC_DoRefDrug(51)
    }
}
//export {clickExport}

function MCInit(userId,userName,deptId,deptName) {
    var pass = new Params_MC_PASSclient_In();
    pass.HospID = "0";//1303";   //医院编码
    pass.UserID = userId;
    pass.UserName = userName;
    pass.DeptID = deptId;
    pass.DeptName = deptName;
    pass.CheckMode = "P_H_A_R_M_H_E_A_D|&*^&%$SPECIAL_FOR_PASSUSERTOOL";//MC_global_CheckMode;
    MCPASSclient = pass;
}
function passim_init(hiscode,doctor){
    	var im = new Params_MC_PASSIM_In();
	im.hiscode = hiscode; //传入hiscode
	im.doctor = doctor; //传入医生编码
	im.Init();      //初始化IM
}
// function HisQueryData() {
//     var drug = new Params_MC_queryDrug_In();
//     drug.ReferenceCode = "1001010101";//"PAT0000000358";//"16035+注射用盐酸吡硫醇+粉针剂+200mg+山西振东泰盛制药有限公司"; //"22305"; //药品编码
//     drug.CodeName = "qqq片";//"注射用盐酸吡硫醇";  //药品名称
//     MC_global_queryDrug = drug;
// }
zcy.init()
zcy.getCsqx();
chose.Wf_getYF();
chose.Wf_getCflx();//获取处方类型
zcy.Wf_selectCF();
zcy.Wf_getPcData();
zcy.Wf_getYyffData({sfcy: 0}, 'YyffData');
zcy.Wf_getYyffData({sfcy: 1}, 'ZyYyffData');
passim_init('000001',userId);
