/**
 * Created by mash on 2017/11/24.
 */
var toolMenu = new Vue({
    el: '.toolMenu',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        index2: 0,
		save:false,
		N03004200254:yzclRight.caqxContent.N03004200254,
        jlxqContent:yzclLeft.HszbrItem,
    },
	mounted:function (){
		Mask.newMask(this.MaskOptions('today'));
	},
    methods: {
    	addData:function(){
    		jlxq.clearData();
    	},
        remove:function () {
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=deleteFxpg&parm=' + encodeURIComponent(JSON.stringify(jlxq.fxpgjg)), function (json) {
                if (json.a == '0') {
                    malert("删除成功！", 'right', 'success');
                    jlxq.getData();
                    jlxq.clearData();
                }else{
                    malert("删除失败！", 'right', 'defeadted');
				}
            });
        },
    	edit:function(){
    		jlxq.fxpgjg.zyh = toolMenu.jlxqContent.zyh;
    		jlxq.fxpgjg.pgryxm = jlxq.hsList[0]['ryxm'];
    		if(jlxq.fxpgjg.pglx == '5'){
    			jlxq.fxpgjg.jsqmsj = jlxq.fxpgjg.pgrq;
    		}
    		if(!jlxq.fxpgjg.id){
    			this.save=true;
    			$.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=addFxpg&parm=' + encodeURIComponent(JSON.stringify(jlxq.fxpgjg)), function (json) {
                    if (json.a == '0') {
    					toolMenu.save=false;
                        malert("保存成功！", 'right', 'success');
                        jlxq.getData();
                        jlxq.clearData();
                    } else {
                    	toolMenu.save=false;
                        malert("保存失败！", 'right', 'defeadted');
                    }
                });
    		}else{
    			this.save=true;
    			$.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=updateFxpg&parm=' + encodeURIComponent(JSON.stringify(jlxq.fxpgjg)), function (json) {
                    if (json.a == '0') {
    					toolMenu.save=false;
                        malert("修改成功！", 'right', 'success');
                        jlxq.getData();
                    } else {
                        malert("修改失败！", 'right', 'defeadted');
                    }
                });
    		}
    	},
    }
});


var jlxq = new Vue({
    el: '#jlxq',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
    	hlfxpg_tran:{
        	'1':'Barthel指数（BI）评定量表',
        	'2':'Braden压疮危险因素评估、监控表',
        	'3':'导管滑脱风险评估、监控表',
        	'4':'跌倒/坠床风险评估、监控表',
        	'5':'疼痛评估、监控表'
        },
        hlfxlx:'1',
		N03004200277:yzclRight.caqxContent.N03004200277,
        hsList: [],
        today:'',
        fxpgjg:{
        	zf: 0,
        	pglx: this.hlfxlx
        },
        pgjlArr:[]
    },
    created: function () {
        var user = JSON.parse(sessionStorage.getItem('yljgOrUser' + userId));
        if(user){
			this.hsList.push({
				rybm: userId,
				ryxm: user.czyxm
			});
			this.fxpgjg.pgry= this.hsList[0]['rybm'];
			this.fxpgjg.pglx= this.hlfxlx;
		}

        this.getData();
    },
    updated:function(){
        changeWin()
    },
    mounted: function () {
    	this.fxpgjg.pgrq = getTodayDateTime();
    	this.fxpgjg.jsqmsj = getTodayDateTime();
    	this.today = this.fDate(getTodayDateTime(),'date');
    	laydate.render({
            elem: '#jlsj',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
            	jlxq.fxpgjg.pgrq = value;
            }
        });
    	laydate.render({
    		elem: '#today',
    		rigger: 'click',
    		type: 'date',
    		theme: '#1ab395',
    		done: function (value, data) { //回调方法
    			jlxq.today = value;
    		}
    	});
    	laydate.render({
    		elem: '#jsqmsj',
    		rigger: 'click',
    		type: 'date',
    		theme: '#1ab393',
    		done: function (value, data) { //回调方法
    			jlxq.fxpgjg.jsqmsj = value;
    		}
    	});
    },
    watch:{
    	"today": function( newVal ){
    		this.getData();
    		this.clearData();
        }
    },
    methods: {
    	// 获取数据
    	getData:function(){
    		if(yzclRight.caqxContent.N03004200277=="1"){
				var parm = {
					zyh: toolMenu.jlxqContent.zyh,
					pglx: this.hlfxlx
					// pgrq: this.today
				};
			}else{
				var parm = {
					zyh: toolMenu.jlxqContent.zyh,
					pglx: this.hlfxlx,
					pgrq: this.today
				};
			}
    		$.ajaxSettings.async = false;
            this.$http.get('/actionDispatcher.do', {params:{reqUrl:'HszHlywWzhljl',types:'queryFxpg',parm:JSON.stringify(parm)}}).then( function (json) {
                if (json.body.a == '0' && json.body.d) {
                	this.pgjlArr = json.body.d.list;
					Vue.set(jlxq.fxpgjg, 'pgjg', json.body.d.list[0].pgjg);
                }
            });
    	},

    	changeHlfxlx:function(val){
    		this.hlfxlx = val[0];
    		this.fxpgjg = {
            	zf: 0,
            	pglx: this.hlfxlx,
            	pgrq: getTodayDateTime(),
            	pgry: jlxq.hsList[0]['rybm'],
            	zyh: toolMenu.jlxqContent.zyh
            };

    		this.getData();
    	},

    	Wf_Click: function (index) {
    		        	$('#hllxShow').addClass("disable");
            this.index2 = index;
            this.fxpgjg = this.pgjlArr[index];
            this.fxpgjg.pgrq = this.fDate(this.fxpgjg.pgrq,'datetime');
            this.calculateScore();
            console.log(this.fxpgjg)
        },

    	// 计算每个评估总分
    	calculateScore:function(){
    		    		var zf = 0;
    		if(this.hlfxlx == '1'){
    			for (var i = 1; i <= 10; i++) {
    				var score = parseFloat(jlxq.fxpgjg['pgxm' + i] ? jlxq.fxpgjg['pgxm' + i] : 0);
    				zf = zf + score;
				}
				this.pdlb(zf);
    		}
    		if(this.hlfxlx == '2'){
    			for (var i = 11; i <= 33; i++) {
    				var score = parseFloat(jlxq.fxpgjg['pgxm' + i] ? jlxq.fxpgjg['pgxm' + i] : 0);
    				zf = zf + score;
				}
				this.ycjkb(zf);
    		}
    		if(this.hlfxlx == '3'){
    			for (var i = 34; i <= 56; i++) {
    				var score = parseFloat(jlxq.fxpgjg['pgxm' + i] ? jlxq.fxpgjg['pgxm' + i] : 0);
    				zf = zf + score;
				}
				this.dghtfxpgb(zf);
    		}
    		if(this.hlfxlx == '4'){
    			for (var i = 57; i <= 84; i++) {
    				var score = parseFloat(jlxq.fxpgjg['pgxm' + i] ? jlxq.fxpgjg['pgxm' + i] : 0);
    				zf = zf + score;
				}
				this.ddfxpgb(zf);
    		}
    		Vue.set(jlxq.fxpgjg, 'zf', zf);
    	},
		pdlb:function(tem){
    					if(tem<=0){
				Vue.set(jlxq.fxpgjg, 'pgjg', '');
			}
			if(0<tem&&tem<=40){
				Vue.set(jlxq.fxpgjg, 'pgjg', '重度依赖');
			}
			if(41<=tem&&tem<=60){
				Vue.set(jlxq.fxpgjg, 'pgjg', '中度依赖');
			}
			if(61<=tem&&tem<=99){
				Vue.set(jlxq.fxpgjg, 'pgjg', '轻度依赖');
			}
			if(tem>99){
				Vue.set(jlxq.fxpgjg, 'pgjg', '无需依赖');
			}
		},
		ycjkb:function(tem){
						if(tem<=0){
				Vue.set(jlxq.fxpgjg, 'pgjg', '');
				Vue.set(jlxq.fxpgjg, 'hlcs', '');
			}
			if(0<tem&&tem<=9){
				Vue.set(jlxq.fxpgjg, 'pgjg', '非常危险');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①');
			}
			if(10<=tem&&tem<=12){
				Vue.set(jlxq.fxpgjg, 'pgjg', '高度危险');
				Vue.set(jlxq.fxpgjg, 'hlcs', '②');
			}
			if(13<=tem&&tem<=14){
				Vue.set(jlxq.fxpgjg, 'pgjg', '中度危险');
				Vue.set(jlxq.fxpgjg, 'hlcs', '③');
			}
			if(15<=tem&&tem<18){
				Vue.set(jlxq.fxpgjg, 'pgjg', '低度危险');
				Vue.set(jlxq.fxpgjg, 'hlcs', '④');
			}
			if(tem>=18){
				Vue.set(jlxq.fxpgjg, 'pgjg', '有发生压疮的危险');
				Vue.set(jlxq.fxpgjg, 'hlcs', '⑤,⑥,⑦');
			}
		},
		dghtfxpgb:function(tem){
						if(tem<=0){
				Vue.set(jlxq.fxpgjg, 'pgjg', '');
				Vue.set(jlxq.fxpgjg, 'hlcs', '');
			}

			if(1<=tem&&tem<=2){
				Vue.set(jlxq.fxpgjg, 'pgjg', '');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①,②');
			}
			if(3<=tem&&tem<=4){
				Vue.set(jlxq.fxpgjg, 'pgjg', '');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①,②,③,④');
			}
			if(tem>=5){
				Vue.set(jlxq.fxpgjg, 'pgjg', '有导管滑脱的危险');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①,②,③,④,⑤,⑥');
			}
		},
		ddfxpgb:function(tem){
						if(tem<=0){
				Vue.set(jlxq.fxpgjg, 'pgjg', '');
				Vue.set(jlxq.fxpgjg, 'hlcs', '');
			}
			if(1<=tem&&tem<=2){
				Vue.set(jlxq.fxpgjg, 'pgjg', '');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①,②');
			}
			if(3<=tem&&tem<=6){
				Vue.set(jlxq.fxpgjg, 'pgjg', '轻度危机');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①,②,③,④');
			}
			if(7<=tem&&tem<=10){
				Vue.set(jlxq.fxpgjg, 'pgjg', '中度危机');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①,②,③,④,⑤,⑥');
			}
			if(11<=tem&&tem<=15){
				Vue.set(jlxq.fxpgjg, 'pgjg', '高度危机');
				Vue.set(jlxq.fxpgjg, 'hlcs', '①,②,③,④,⑤,⑥,⑦,⑧,⑨,⑩');
			}
		},
    	clearData:function(){
    		this.fxpgjg = {
                	zf: 0,
                	pglx: this.hlfxlx,
                	pgrq: getTodayDateTime(),
                	pgry: jlxq.hsList[0]['rybm'],
                	zyh: toolMenu.jlxqContent.zyh
                };
    	},
    }
})

yzclLeft.$watch('HszbrItem', function (newVal,oldVal) {
	console.log(12)
	jyjgPop.jybgdmx=[];
	jyjgPop.jybgd={};
	jyjgPop.djList=[];
	jlxq.hlxxArr=[];
    if(newVal.zyh && newVal.zyh != oldVal.zyh && this.index==6){
        toolMenu.jlxqContent = newVal;
        jlxq.jlContent = newVal;
		jyjgPop.Brxx_List = newVal;
        jlxq.IsZyh = newVal.zyh;
        jlxq.getJlData();
		jyjgPop.getJyjg();
    }
})
