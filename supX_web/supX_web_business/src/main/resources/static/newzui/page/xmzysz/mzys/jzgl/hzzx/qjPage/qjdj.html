<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>接诊管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="../../../../css/icon.css">
    <link href="qjdj.css" rel="stylesheet" />
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
    <!--入院登记添加记录视图begin-->
    <div class="over-auto printHide" id="qjdj_info" style="height: 100%;padding-bottom: 66px;">
        <!--入院登记信息录入 功能按钮begin-->
        <div class="panel">
            <div class="tong-top flex-container flex-align-c">
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="refresh()">刷新</button>
                <!--<button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="">清空</button>
                <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click="loadIdCard()"><i
                        class="icon-width icon-dsfz"></i>读身份证
                </button>
                <button class="tong-btn btn-parmary-b icon-ff paddig-left" @click="loadYibaoPage()" id="readYibao"><i
                        class="icon-width icon-dybk"></i>读医保卡
                </button>-->
            </div>
        </div>
        <!--入院登记信息录入 功能按钮end-->

        <!--入院登记信息录入 详细begin-->
        <div id="scrollbar" class="rydj-info-box no-scrollbar contextInfo">
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">患者信息</div>
                </div>
                <div class="tab-card-body">
                    <div class="zui-form grid-box">
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">卡片类型</label>
                            <select-input @change-data="commonResultChange" :not_empty="false"
                                          :child="ylklxList" :index="'zymc'" :index_val="'zybm'"
                                          :val="popContent.brjbxxModel.ylklx"
                                          :name="'popContent.ylklx'" :search="true" :disable="ylkxx">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">卡号</label>
                            <div class="zui-input-inline">
                                <input @mousewheel.prevent class="zui-input" type="number"
                                       v-model="popContent.brjbxxModel.ylkh" id="ylkh" :disabled="ylkxx"
                                       @keydown="nextFocus($event)"
                                       data-notEmpty="false">
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">患者姓名</label>
                            <div class="zui-input-inline">
                                <div>
                                    <input type="text" v-model.trim="popContent.brjbxxModel.brxm" :disabled="readonly"
                                           id="brxm" class="zui-input"
                                           @keydown="nextFocus($event,'',true)" data-notEmpty="true"
                                           @blur="setDm(popContent.brjbxxModel.brxm)">
                                </div>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label  ">拼音代码</label>
                            <div class="zui-input-inline">
                                <input placeholder="请输入拼音代码" disabled class="zui-input"
                                       v-model="popContent.pydm"
                                       @keydown="nextFocus($event,'',true)" id="pydm">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label ">五笔简码</label>
                            <div class="zui-input-inline">
                                <input placeholder="请输入五笔简码" disabled class="zui-input"
                                       v-model="popContent.wbjm"
                                       @keydown="nextFocus($event,'',true)" id="wbjm">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline  col-xxl-3">
                            <label class="zui-form-label font-bolder red">性别</label>
                            <select-input :not_empty="true"
                                          @change-data="resultRydjChange"
                                          :not_empty="false"
                                          :child="brxb_tran"
                                          :val="popContent.brjbxxModel.brxb"
                                          :name="'popContent.brjbxxModel.brxb'"
                                          :disable="readonly">
                            </select-input>
                        </div>
                        <div class="zui-inline  col-xxl-3">
                            <label class="zui-form-label">证件类型</label>

                            <select-input @change-data="resultRydjChange" :not_empty="false"
                                          :child="zjlxList" :index="'zymc'" :index_val="'zybm'"
                                          :val="popContent.brjbxxModel.sfzjlx"
                                          :name="'popContent.brjbxxModel.sfzjlx'" :index_mc="'zybmmc'" :search="false"
                                          :disable="readonly" id="zjlx">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">证件号码</label>
                            <div class="zui-input-inline">
                                <input @blur="sfjhm" id="sfjhm" placeholder="请输入证件号码" data-notEmpty="false"
                                       class="zui-input"
                                       v-model.trim="popContent.brjbxxModel.sfzjhm"
                                       @keydown="nextFocus($event)"
                                       id="sfjhm" :disabled="readonly">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">出生日期</label>
                            <div class="zui-input-inline zui-date">
                                <i class="datenox fa-calendar"></i>
                                <input v-model="popContent.brjbxxModel.csrq" id="csrq"
                                       class="zui-input" @keydown="nextFocus($event)" data-notEmpty="true">
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">年龄</label>
                            <div class="zui-input-inline">
                                <div class="col-xxl-3" style="padding-right: 5px;">
                                    <input id="brnl" placeholder="" data-notEmpty="true" class="zui-input"
                                           :title="popContent.nl"
                                           v-model.trim="popContent.nl" @blur="brnlFun"
                                           @keydown.enter="setCsrq($event)" :disabled="readonly">
                                    <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                                </div>
                                <div class="col-xxl-3">
                                    <select-input
                                            :search="true"
                                            @change-data="resultRydjChange"
                                            :not_empty="false"
                                            :child="nldw_tran"
                                            :index="popContent.nldw"
                                            :val="popContent.nldw"
                                            :name="'popContent.nldw'" id="nldw">
                                    </select-input>
                                </div>
                                <div class="col-xxl-3" style="padding-right: 5px;">
                                    <input id="brnl2" placeholder="" data-notEmpty="false" class="zui-input"
                                           :title="popContent.nl2"
                                           v-model.trim="popContent.nl2" @blur="brnlFun"
                                           @keydown.enter="setCsrq($event)" :disabled="readonly">
                                    <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                                </div>
                                <div class="col-xxl-3">
                                    <select-input
                                            :search="true"
                                            @change-data="resultRydjChange"
                                            :not_empty="false"
                                            :child="nldw_tran"
                                            :index="popContent.nldw2"
                                            :val="popContent.nldw2"
                                            :name="'popContent.nldw2'" id="nldw2">
                                    </select-input>
                                </div>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">联系电话</label>
                            <div class="zui-input-inline">
                                <input placeholder="请输入联系电话" data-notEmpty="true" class="zui-input"
                                       :title="popContent.brjbxxModel.sjhm"
                                       v-model.trim="popContent.brjbxxModel.sjhm"
                                       @keydown="nextFocus($event)" id="sjhm" :disabled="readonly"
                                       onkeyup="value=value.replace(/[^\d]/g,'')">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">国籍</label>
                            <select-input @change-data="resultRydjChange" :not_empty=true
                                          :child="gjList" :index="'gjmc'" :index_val="'gjbm'"
                                          :val="popContent.brjbxxModel.brgj"
                                          :name="'popContent.brjbxxModel.brgj'" :index_mc="'brgjmc'" :search="true"
                                          :disable="readonly" id="gj">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">民族</label>
                            <select-input @change-data="resultRydjChange" :not_empty="true"
                                          :child="mzList" :index="'mzmc'" :index_val="'mzbm'"
                                          :val="popContent.brjbxxModel.brmz"
                                          :name="'popContent.brjbxxModel.brmz'" :index_mc="'brmzmc'" :search="true"
                                          :disabled="readonly" id="mz">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">婚姻状况</label>
                            <select-input @change-data="resultRydjChange" :not_empty="true"
                                          :child="hyzkList" :index="'hyzkmc'" :index_val="'hyzkbm'"
                                          :val="popContent.brjbxxModel.hyzk"
                                          :name="'popContent.brjbxxModel.hyzk'" :index_mc="'hyzkmc'" :search="true"
                                          :disabled="readonly" id="hyzk">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">职业</label>
                            <select-input @change-data="resultRydjChange" :not_empty="true"
                                          :child="zyList" :index="'zymc'" :index_val="'zybm'"
                                          :val="popContent.brjbxxModel.zybm"
                                          :name="'popContent.brjbxxModel.zybm'" :index_mc="'zybmmc'" :search="true"
                                          :disable="readonly" id="zy">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">患者类型</label>
                            <select-input @change-data="resultRydjChange" :not_empty="false"
                                          :child="ryhzlx_tran" :index="popContent.brjbxxModel.hzlx"
                                          :val="popContent.brjbxxModel.hzlx"
                                          :name="'popContent.brjbxxModel.hzlx'" :disable="readonly" id="hzlx">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">联系人</label>
                            <div class="zui-input-inline">
                                <input placeholder="请输入联系人" data-notEmpty="false" class="zui-input"
                                       :title="popContent.brjbxxModel.lxrxm"
                                       v-model.trim="popContent.brjbxxModel.lxrxm"
                                       @keydown="nextFocus($event)" :disabled="readonly" id="lxr">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red" style="line-height: 1;padding: 2px 10px 2px 0;">联&ensp;系&ensp;人关&emsp;&emsp;系</label>
                            <select-input @change-data="resultRydjChange" :not_empty="false"
                                          :child="lxrgxList" :index="'lxrgxmc'" :index_val="'lxrgxbm'"
                                          :val="popContent.brjbxxModel.lxrgx"
                                          :search="true" :name="'popContent.brjbxxModel.lxrgx'" :disable="readonly"
                                          id="lxrgx">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red" style="line-height: 1;padding: 2px 10px 2px 0;">联&ensp;系&ensp;人电&emsp;&emsp;话</label>
                            <div class="zui-input-inline">
                                <input @mousewheel.prevent v-model.trim="popContent.brjbxxModel.lxrdh" type="number"
                                       class="zui-input"
                                       data-notEmpty="false" @keydown="nextFocus($event)" :disable="readonly"
                                       onkeyup="value=value.replace(/[^\d]/g,'')" id="lxrdh">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-6">
                            <label class="zui-form-label">居住地</label>
                            <div class="col-xxl-4" style="padding-right: 6.66666666px;">
                                <select-input @change-data="resultRydjChange" :not_empty="false"
                                              :child="provinceList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                              :val="popContent.brjbxxModel.jzdsheng"
                                              :search="true"
                                              :name="'popContent.brjbxxModel.jzdsheng'" :index_mc="'jzdshengmc'" :phd="'省'">
                                </select-input>
                            </div>
                            <div class="col-xxl-4" style="padding: 0 3.33333333px;">
                                <select-input @change-data="resultRydjChange" :data-notEmpty="true"
                                              :child="cityList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                              :val="popContent.brjbxxModel.jzdshi"
                                              :search="true"
                                              :name="'popContent.brjbxxModel.jzdshi'" :index_mc="'jzdshimc'" :phd="'市'">
                                </select-input>
                            </div>
                            <div class="col-xxl-4" style="padding-left: 6.66666666px;">
                                <select-input @change-data="resultRydjChange" :data-notEmpty="true"
                                              :child="countyList" :index="'xzqhmc'" :index_val="'xzqhbm'"
                                              :val="popContent.brjbxxModel.jzdxian"
                                              :search="true"
                                              :name="'popContent.brjbxxModel.jzdxian'" :index_mc="'jzdxianmc'" :phd="'区/县'">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-6">
                            <label class="zui-form-label">详细地址</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" v-model.trim="popContent.brjbxxModel.jzdmc" id="jzdmc"
                                       :disabled="readonly" data-notEmpty="true"
                                       @keydown="nextFocus($event),scrollButtom()" data-notEmpty="false">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">腕带类型</label>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="sfcr_tran" :index="popContent.sfcr"
                                          :val="popContent.sfcr"
                                          :name="'popContent.sfcr'">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">门特患者</label>
                            <select-input @change-data="resultRydjChange" :not_empty="false"
                                          :child="istrue_tran" :index="popContent.brjbxxModel.mtbbz"
                                          :val="popContent.brjbxxModel.mtbbz"
                                          :name="'popContent.brjbxxModel.mtbbz'" :disable="readonly" id="mtbbz">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">门特个人编号</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" v-model.trim="popContent.brjbxxModel.djbh" id="djbh"
                                       :disabled="readonly" data-notEmpty="false"
                                       @keydown="nextFocus($event),scrollButtom()" data-notEmpty="false">
                                <!--<input type="text" name="phone" class="zui-input" check="required"/>-->
                            </div>
                        </div>
                    <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">病案号</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" v-model.trim="popContent.brjbxxModel.bah" id="bah" @blur="isBah" type="number"
                                       :disabled="true" data-notEmpty="false"
                                       @keydown="nextFocus($event),scrollButtom()" data-notEmpty="false">

                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="tab-card">
                <div class="tab-card-header">
                    <div class="tab-card-header-title">入院信息</div>
                </div>
                <div class="tab-card-body">
                    <div class="grid-box zui-form">

                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">病人费别</label>
                            <select-input @change-data="resultRydjChange" :not_empty="true"
                                          :child="brfbList" :index="'fbmc'" :index_val="'fbbm'" :val="popContent.brfb"
                                          :name="'popContent.brfb'" ref="autofocus" :search="true" :index_mc="'brfbmc'"
                                          :disable="readonly">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">保险类别</label>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="bxlbList" :index="'bxlbmc'" :index_val="'bxlbbm'" :val="popContent.bxlbbm"
                                          :name="'popContent.bxlbbm'" :search="true" :index_mc="'bxlbmc'"
                                          :disable="readonly">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">医保卡号</label>
                            <div class="zui-input-inline">
                                <input @mousewheel.prevent type="number" v-model.trim="popContent.ybkh" class="zui-input"
                                       @blur="ybkhBlur" data-notEmpty="false" @keydown="nextFocus($event)">
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label  font-bolder red">入院诊断</label>
                            <div class="zui-input-inline">

                                <input class="zui-input" :id="caqxContent.cs00700100158"
                                       :data-notEmpty="caqxContent.cs00700100158 ? true : false"
                                       :value="jbbmContent.jbmc"
                                       @keydown="changeDown($event,'jbbm','jbbmContent','jbsearchCon')"
                                       @input="change(false,'jbbm',$event.target.value)">
                                <jbsearch-table :message="jbsearchCon" :selected="selSearch"
                                                :them="jbthem" :page="page"
                                                @click-one="checkedOneOut" @click-two="selectJbbm">
                                </jbsearch-table>
                            </div>
                        </div>
                        <div v-if="caqxContent.N05002200158 == '1'" class="zui-inline col-xxl-3">
                            <label class="zui-form-label">中医入院<br/>诊断</label>
                            <div class="zui-input-inline">

                                <input class="zui-input" :id="caqxContent.N05002200158"
                                       :data-notEmpty="caqxContent.N05002200158 ? true : false"
                                       v-model="popContent.ryzyzdmc"
                                       @keydown="changeDown_zy($event,'jbbm','jbbmContentZy','jbsearchConZy')"
                                       @input="change_zy(false,'jbbm',$event.target.value)">
                                <jbsearch-table :message="jbsearchConZy" :selected="selSearchZy"
                                                :them="jbthemZy" :page="page"
                                                @click-one="checkedOneOut" @click-two="selectJbbmZy">
                                </jbsearch-table>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">入院科室</label>
                            <div class="zui-input-inline" id="ryks-box">
                                <select-input @change-data="resultRydjChange" :not_empty="true"
                                              :child="zyksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.ryks"
                                              :name="'popContent.ryks'" :search="true" :index_mc="'ryksmc'"
                                              :disable="readonly">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label">住院医生</label>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="zyysList" :index="'ryxm'" :index_val="'rybm'" :val="popContent.zyys"
                                          :name="'popContent.zyys'" :search="true" :index_mc="'zyysxm'"
                                          :disable="csqxjyzyys">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">入院日期</label>
                            <div class="zui-input-inline zui-date">
                                <input class="zui-input " v-model.trim="popContent.brjbxxModel.ryrq" id="timeRyrq"
                                       data-notEmpty="true">
                            </div>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label  font-bolder red">门诊医生</label>
                            <input id="ypcd" class="zui-input" v-model="popContent.mzysxm"
                                   @keydown="changeDown1($event,$event.target.value)"
                                   @input="change1(false, $event.target.value)">
                            <search-table :page="queryStr" :message="searchCon1" :selected="selSearch1" :them="them1"
                                          :them_tran="them_tran1" @click-one="checkedOneOut"
                                          @click-two="selectOne1">
                            </search-table>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">入院情况</label>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="ryqk_tran" :index="popContent.ryqk" :val="popContent.ryqk"
                                          :name="'popContent.ryqk'" :disable="readonly">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">入院途径</label>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="rytj_tran" :index="popContent.rytj" :val="popContent.rytj"
                                          :name="'popContent.rytj'" :disable="readonly">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder red">入院类型</label>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="med_typeList" :index="popContent.med_type" :val="popContent.med_type"
                                          :name="'popContent.med_type'" :disable="readonly">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder ">就医类型</label>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="jyrqlx_tran" :index="popContent.mdtrtGrpType" :val="popContent.mdtrtGrpType"
                                          :name="'popContent.mdtrtGrpType'" :search="true">
                            </select-input>
                        </div>

                        <div class="zui-inline col-xxl-3">
                            <label class="zui-form-label font-bolder ">就医特殊属性</label>
                            <select-input @change-data="resultChange" :not_empty="false"
                                          :child="jytssx_tran" :index="popContent.iptPsnSpType" :val="popContent.iptPsnSpType"
                                          :name="'popContent.iptPsnSpType'" :search="true">
                            </select-input>
                        </div>

                        <div class="zui-inline col-xxl-3" v-if="gsjkptBm!=0">
                            <label class="zui-form-label">转诊手续</label>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="zzsx_tran" :index="popContent.zzsxqq" :val="popContent.zzsxqq"
                                          :name="'popContent.zzsxqq'">
                            </select-input>
                        </div>
                        <div class="flex-container  padd-r-20 col-xxl-3" v-if="gsjkptBm!=0">
                            <label class="font-14 color-1c2024 whiteSpace">不予享受扶<br/>贫政策情况</label>
                            <select-input @change-data="resultChange" :not_empty="true"
                                          :child="fpzc_tran" :index="popContent.byxsfpqk" :val="popContent.byxsfpqk"
                                          :name="'popContent.byxsfpqk'">
                            </select-input>
                        </div>
                        <div class="zui-inline col-xxl-6">
                            <label class="zui-form-label">备注说明</label>
                            <div class="zui-input-inline">
                                <input class="zui-input" v-model.trim="popContent.bzsm" id="bzsm" data-notEmpty="false">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!--入院登记信息录入 详细end-->

        <!--入院登记信息录入 浮动功能按钮区begin-->
        <div class="action-bar fixed zui-table-tool">
            <button class="zui-btn btn-primary xmzb-db" @click="saveData">抢救入院</button>
            <button class="zui-btn btn-default xmzb-db" @click="quxiao">返回列表</button>
        </div>

    </div>
    <!--入院登记添加记录视图end-->
    <script type="application/javascript" src="/newzui/pub/js/insuranceUtil.js"></script>
    <script type="text/javascript" src="qjdj.js"></script>
</body>
</html>
