
var tableSwitch = new Vue({
    el: "#table-switch",
    data: {
        num: 0
    },
    methods: {
        tabActive: function (index) {
            console.log(index);
            this.num = index
        },
    },
});
var menuButt = new Vue({
    el: "#menu-butt",
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        ksbm: null,
        caqxContent: {
            csN03004200244:'0'
        },
        sum: 0,
        timeOut: null,
        mjzbz:'0',
    },
    mounted: function () {
        this.moun();
        window.addEventListener("storage", function (e) {
            if (e.key == "cyksbm" && e.newValue !== e.oldValue) {
                menuButt.moun();
            }
        });
        this.getCsqx();
    },
    computed: {
        show: function () {
            //return tableSwitch.num ? false : true;
            return tableSwitch.num;
        }
    },
    methods: {
        getCsqx: function () {
            var parm = {
                "ylbm": 'N030042002',
                ksbm: this.ksbm,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d && json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03004200227": //调用获取检验申请方式 0 默认原始方法 1 webserverce服务
                                    if (csjson.csz != null || csjson.csz != undefined || csjson.csz != "") {
                                        menuButt.caqxContent.cs03004200227 = csjson.csz;
                                    }
                                    break;
                                case "N03004200244": //0-程序打印 1-帆软打印
                                    if (csjson.csz != null || csjson.csz != undefined || csjson.csz != "") {
                                        menuButt.caqxContent.csN03004200244 = csjson.csz;
                                    }
                                    break;
                            }
                        }
                }
            })
        },
        moun: function () {
            this.ksbm = JSON.parse(sessionStorage.getItem("cyksbm")).ksbm;
            this.mjzbz = JSON.parse(sessionStorage.getItem("cyksbm")).mjzbz;
            console.log(this.ksbm);
        },
        getData: function () {
            if (tableSwitch.num == 0) {
                dydj.getData();
            } else if(tableSwitch.num == 1){
                cydj.getData();
            } else{
                //cxdy.getData();
            }
        },
        printList:function(){
            if(cydj.jsonList.length == 0){
                malert('请选择需要打印的数据','top','defeadted');
                return false;
            }
            window.print()
        },
        //获取申请
        getCyData: function () {
            var parm = {
                ksbm: menuButt.ksbm
            }
            if (tableSwitch.num == 0) {
                parm.beginrq = dydj.beginrq;
                parm.endrq = dydj.endrq;
            } else {
                parm.beginrq = cydj.beginrq;
                parm.endrq = cydj.endrq;
            }
            if (menuButt.caqxContent.cs03004200227 == "1") {//调用webservice接口服务获取采样登记
                parm.ksbm = menuButt.ksbm;
                $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqsqByWebService&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        malert("获取成功！");
                        menuButt.getData();
                    } else {
                        menuButt.getData();
                        common.closeLoading();
                    }
                });
            } else {
                if (tableSwitch.num == 0) {
                    common.openloading('#cydj');
                } else {
                    common.openloading('#cxdj');
                }
                $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqsq&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        malert("获取成功！");
//				    common.closeLoading();
                        menuButt.getData();
                    } else {
//					malert("失败！", "top", "defeadted");
                        menuButt.getData();
                        common.closeLoading();
                    }
                });
            }

        },
        //项目合并
        xmhb: function () {
            if (!dydj.ifClick) return; //如果为false表示已经点击了不能再点
            dydj.ifClick = false;
            var xmhbData = [];
            for (var i = 0; i < dydj.jsonList.length; i++) {
                if (dydj.checkedSelect[i]) {
                    xmhbData.push(dydj.jsonList[i]);
                }
            }
            if (xmhbData.length <= 0) {
                malert("无样本登记", "top", "defeadted");
                dydj.ifClick = true;
                return;
            }
            //逻辑判断
            var brxm = "";
            var jyxhs = [];
            for (var i = 0; i < xmhbData.length; i++) {
                brxm = xmhbData[0];
                if (xmhbData[i] != xmhbData[0]) {
                    malert("合并项目姓名:[" + xmhbData[i] + "]与[" + xmhbData[0] + "]不符，不允许合并!");
                    dydj.ifClick = true;
                    return;
                }
                jyxhs.push(xmhbData[i].jyxh);
            }
            var parm = {
                shbz: '0',
                ksbm: menuButt.ksbm,
                jyxhs: jyxhs,
                mjzbz: menuButt.mjzbz
            };
            console.log(parm);
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_sqcx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    console.log(json.d.list);
                    common.closeLoading();
                } else {
                    malert("查询失败", "top", "defeadted");
                    common.closeLoading();
                }
            });
            return;

        },
        //采样登记
        insert: function () {
            if (!cydj.ifClick) return; //如果为false表示已经点击了不能再点
            cydj.ifClick = false;
            var cydjData = [];
            for (var i = 0; i < cydj.jsonList.length; i++) {
                if (cydj.checkedSelect[i]) {
                    cydjData.push(cydj.jsonList[i]);
                }
            }
            if (cydjData.length <= 0) {
                malert("无样本登记", "top", "defeadted");
                cydj.ifClick = true;
                return;
            }
            this.$http.post('/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_cydj',
                JSON.stringify({list: cydjData})).then(function (data) {
                if (data.body.a == 0) {
                        //menuButt.printInit()
                        malert("获取成功");
                        menuButt.getData();
                        cydj.ifClick = true;

                } else {
                    malert("获取失败！" + data.body.c, "top", "defeadted");
                    cydj.ifClick = true;
                }
            }, function (error) {
                malert("失败！" + error, "top", "defeadted");
                cydj.ifClick = true;
            });
        },
        printUpdated: function () {
            var saveList = []
            if (tableSwitch.num == 1) {
                for (var i = 0; i < cydj.checkedSelect.length; i++) {
                    if (cydj.checkedSelect[i] == true) {
                        saveList.push({
                            jyxh: cydj.jsonList[i].jyxh,
                            dybz: '1'
                        })
                    }
                }
            } else if (tableSwitch.num == 0){
                for (var i = 0; i < dydj.checkedSelect.length; i++) {
                    if (dydj.checkedSelect[i] == true) {
                        saveList.push({
                            jyxh: dydj.jsonList[i].jyxh,
                            dybz: '1'
                        })
                    }
                }
            }
            var json = '{"list":' + JSON.stringify(saveList) + '}';
            this.postAjax('/actionDispatcher.do?reqUrl=YzPacsNew&types=updateDybz',
                json,function (data) {
                    if (data.a == 0) {
                        malert("更新成功", 'top', 'success');
                    } else {
                        malert("更新失败", 'top', 'defeadted')
                    }
                });
        },
        printInit: function () {
            this.printUpdated()
            var _printCheckBox = [], _dyfs = 1,
                _list = [],
                _showList = [];
            if (tableSwitch.num == 1) { // 采样登记
                _printCheckBox = cydj.checkedSelect;
                _dyfs = cydj.dyfs
                _list = cydj.jsonList;
            } else if (tableSwitch.num == 0) { // 打印登记
                _printCheckBox = dydj.checkedSelect;
                _dyfs = dydj.dyfs
                _list = dydj.jsonList;
            }
            for (var i = 0; i < _printCheckBox.length; i++) {
                console.log(_list[i]);
                if (_printCheckBox[i]) {
                    for (var j = 0; j < _dyfs; j++) {
                        _showList.push(_list[i]);
                    }
                }
            }
            loadOpenPage('cydjJcjy',function () {
                window.cydjJcjy.showList = _showList;
                window.cydjJcjy.$nextTick(function () {
                    JsBarcode(".print-barcode-img").options({
                        width: 1,
                        valid: function (type) {
                            menuButt.sum++;
                            clearTimeout(menuButt.timeOut);
                            menuButt.timeOut = setTimeout(function () {
                                if (window.cydjJcjy.showList.length === menuButt.sum) {
                                    menuButt.print();
                                }
                            }, 10);
                        }
                    }).init();
                });
            })
        },
        print: function () {
            if (menuButt.caqxContent.csN03004200244 == '0') {
                malert("正在打印！请稍后....");
                $('.wrapper').addClass('printHide')
                window.print();
                $('#loadingPrint').html('')
                $('.wrapper').removeClass('printHide')
                this.sum = 0;
                menuButt.getData()
            } else {
                //帆软打印
                var frpath = "";
                if (window.top.J_tabLeft.obj.frprintver == "3") {
                    frpath = "%2F";
                } else {
                    frpath = "/";
                }
                var reportlets = [];

                for (var ii = 0; ii < this.showList.length; ii++) {
                    reportlets.push({
                        reportlet: 'fpdy' + frpath + 'hsz' + frpath + 'syt_jytm.cpt',
                        jyxh: '' + this.showList[ii].jyxh + '',
                        sgys: '' + this.showList[ii].sgys + '',
                        bah: '' + $.trim(this.showList[ii].bah)+ '',
                        ksmc: '' + this.showList[ii].sjksmc + '',
                        brxm: '' + this.showList[ii].brxm + '',
                        fymc: '' + this.showList[ii].fymc + '',
                        sqrq: '' + this.fDate(this.showList[ii].sqrq, 'date') + '',
                        cwh: '' + this.showList[ii].cwh + '',
                        brxb: '' + this.brxb_tran[this.showList[ii].xb] + '',
                        brnl: '' + this.showList[ii].nl + this.nldw_tran[this.showList[ii].nldw] + '',
                        jzbz: '' + this.jzbz_tran[this.showList[ii].jzbz] + ''
                    });
                }

                var jytmPrint = null;
                window.top.J_tabLeft.csqxparm.csbm = "N010024013";
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                    malert("正在打印！请稍后....");
                    if (json.a == 0) {
                        console.log(json.d);
                        if (json.d != null && json.d != undefined && json.d.length > 0) {
                            jytmPrint = json.d[0].csz;
                        }

                        if (!FrPrint(reportlets, jytmPrint)) {
                            window.print();
                        }
                        menuButt.getData()
                    } else {
                        window.print();
                    }
                });

                this.sum = 0;
            }
        }
    }
});
var dydj = new Vue({
    el: "#dydj",
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        beginrq: '',
        endrq: '',
        dyfs: 1,
        jsonList: [],
        popContent: {
            'PrintType': '0'
        },
        IsPrint_tran: {
            '0': '未打印',
            '1': '已打印',
        },
        checkedSelect: [],
        printChecked: [],
        cyChecked: [],
        jzChecked:{'0':false,'1':true},
        ifClick: true,
    },
    computed: {
        show: function () {
            //return tableSwitch.num ? false : true;
            return tableSwitch.num;
        }
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.beginrq = this.fDate(myDate, 'date') + ' 00:00:00';
        this.endrq = this.fDate(myDate.setDate(myDate.getDate() + 1), 'date') + ' 23:59:59';
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#dydj-time-begin',
            type: 'datetime',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    dydj.beginrq = value;
                } else {
                    dydj.beginrq = '';
                }
                //获取一次列表
                dydj.getData();
            }
        });
        laydate.render({
            elem: '#dydj-time-end',
            type: 'datetime',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    dydj.endrq = value;
                } else {
                    dydj.endrq = '';
                }
                //获取一次列表
                dydj.getData();
            }
        });
    },
    methods: {
        resultChangeData: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData()
        },
        getData: function () {
            dydj.checkedSelect = [];
            dydj.printChecked = [];
            dydj.cyChecked = [];
            common.openloading('#dydj');
            var parm = {
                beginrq: dydj.beginrq,
                endrq: dydj.endrq,
                ztbz: '0',
                ksbm: menuButt.ksbm,
                //dybz: this.popContent.PrintType,
                mjzbz: menuButt.mjzbz
            };
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_sqcx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    dydj.jsonList = json.d.list;
                    for (var i = 0; i < dydj.jsonList.length; i++) {
                        if (dydj.jsonList[i].dybz != null && dydj.jsonList[i].dybz != 0) {
                            dydj.printChecked[i] = true
                        }
                    }
                    common.closeLoading();
                } else {
                    malert("查询失败", "top", "defeadted");
                    common.closeLoading();
                }
            });
        },
        checkedSelectFn: function (val) {
            this.checkedSelect[val[1]] = val[2];
            this.$forceUpdate();
        },
        reCheckBoxAll: function (val) {
            this.isCheckAll = val[2];
            console.log(this.isCheckAll);
            if (val[1] == null) val[1] = "jsonList";
            if (this.isCheckAll) {
                for (var i = 0; i < this[val[1]].length; i++) {
                    Vue.set(this.checkedSelect, i, true);
                }
            } else {
                this.checkedSelect = [];
            }
        },
        printCheckedFn: function (val) {
            this.printChecked[val[1]] = val[2];
            this.$forceUpdate();
        },
        cyCheckedFn: function (val) {
            this.cyChecked[val[1]] = val[2];
            for (var i = 0; i < dydj.jsonList.length; i++) {
                if (dydj.jsonList[i].bah == dydj.jsonList[val[1]].bah) {
                    this.cyChecked[i] = val[2];
                }
            }
            this.$forceUpdate();
        },
        jzCheckedFn: function (val) {
            this.jzChecked[val[1]] = val[2];
            this.$forceUpdate();
        }
    }
});

var cydj = new Vue({
    el: "#cydj",
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        beginrq: '',
        endrq: '',
        dyfs: 1,
        jsonList: [],
        popContent: {
            PrintType: '0',
            ybhsbz:'0',
            parm:'',
        },
        checkedSelect: [],
        printChecked: [],
        cyChecked: [],
        IsPrint_tran: {
            '0': '未打印',
            '1': '已打印',
        },
        ifClick: true,
    },
    updated:function(){
        changeWin()
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.beginrq = this.fDate(myDate, 'date') + ' 00:00:00';
        this.endrq = this.fDate(myDate, 'date') + ' 23:59:59';
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#cydj-time-begin',
            type: 'datetime',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    cydj.beginrq = value;
                } else {
                    cydj.beginrq = '';
                }
                //获取一次列表
                cydj.getData();
            }
        });
        laydate.render({
            elem: '#cydj-time-end',
            type: 'datetime',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    cydj.endrq = value;
                } else {
                    cydj.endrq = '';
                }
                //获取一次列表
                cydj.getData();
            }
        });
    },
    computed: {
        show: function () {
            //return tableSwitch.num ? true : false;
            return tableSwitch.num;
        }
    },
    methods: {
        reCheckBoxAll: function (val) {
            this.isCheckAll = val[2];
            console.log(this.isCheckAll);
            if (val[1] == null) val[1] = "jsonList";
            if (this.isCheckAll) {
                for (var i = 0; i < this[val[1]].length; i++) {
                    Vue.set(this.checkedSelect, i, true);
                }
            } else {
                this.checkedSelect = [];
            }
        },
        resultChangeData: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData();
        },
        getData: function () {
            cydj.checkedSelect = [];
            cydj.printChecked = [];
            cydj.cyChecked = [];
            common.openloading('#cydj');
            var parm = {
                beginrq: cydj.beginrq,
                endrq: cydj.endrq,
                ksbm: menuButt.ksbm,
                //dybz: this.popContent.PrintType,
                mjzbz: menuButt.mjzbz,
                ybhsbz:this.popContent.ybhsbz,
                parm:this.popContent.parm,
                ztbz:"1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_sqcx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    var arr = [];
                    for (var i = 0; i < json.d.list.length; i++) {
                        if (json.d.list[i].dybz != null && json.d.list[i].dybz != 0) {
                            cydj.printChecked[i] = true
                        }
                        console.log(json.d.list[i].bah.replace(/(^\s*)|(\s*$)/g,'').length)
                        if(json.d.list[i].bah.replace(/(^\s*)|(\s*$)/g,'').length == 10){
                        	arr.push(json.d.list[i]);
                        }
                    }
                    cydj.jsonList = arr;
                    common.closeLoading();
                } else {
                    malert("查询失败", "top", "defeadted");
                    common.closeLoading();
                }
            });
        },
        checkedSelectFn: function (val) {
            this.checkedSelect[val[1]] = val[2];
            this.$forceUpdate();
        },
        printCheckedFn: function (val) {
            this.printChecked[val[1]] = val[2];
            this.$forceUpdate();
        },
        cyCheckedFn: function (val) {
            this.cyChecked[val[1]] = val[2];
            this.$forceUpdate();
        }
    }
});

var cxdj = new Vue({
    el: "#cxdj",
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        beginrq: '',
        endrq: '',
        dyfs: 1,
        jsonList: [],
        popContent: {
            PrintType: '0',
            ybhsbz:'0',
            parm:'',
        },
        checkedSelect: [],
        printChecked: [],
        cyChecked: [],
        IsPrint_tran: {
            '0': '未打印',
            '1': '已打印',
        },
    },
    updated:function(){
        changeWin()
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.beginrq = this.fDate(myDate, 'date') + ' 00:00:00';
        this.endrq = this.fDate(myDate, 'date') + ' 23:59:59';
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#cxdj-time-begin',
            type: 'datetime',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    cxdj.beginrq = value;
                } else {
                    cxdj.beginrq = '';
                }
                //获取一次列表
                cxdj.getData();
            }
        });
        laydate.render({
            elem: '#cxdj-time-end',
            type: 'datetime',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    cxdj.endrq = value;
                } else {
                    cxdj.endrq = '';
                }
                //获取一次列表
                cxdj.getData();
            }
        });
    },
    computed: {
        show: function () {
            //return tableSwitch.num ? true : false;
            return tableSwitch.num;
        }
    },
    methods: {
        reCheckBoxAll: function (val) {
            this.isCheckAll = val[2];
            console.log(this.isCheckAll);
            if (val[1] == null) val[1] = "jsonList";
            if (this.isCheckAll) {
                for (var i = 0; i < this[val[1]].length; i++) {
                    Vue.set(this.checkedSelect, i, true);
                }
            } else {
                this.checkedSelect = [];
            }
        },
        resultChangeData: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData();
        },
        getData: function () {
            cxdj.checkedSelect = [];
            cxdj.printChecked = [];
            cxdj.cyChecked = [];
            common.openloading('#cxdj');
            var parm = {
                beginrq: cxdj.beginrq,
                endrq: cxdj.endrq,
                ksbm: menuButt.ksbm,
                //dybz: this.popContent.PrintType,
                mjzbz: menuButt.mjzbz,
                ybhsbz:this.popContent.ybhsbz,
                parm:this.popContent.parm,
                ztbz:"1"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_sqcx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    var arr = [];
                    for (var i = 0; i < json.d.list.length; i++) {
                        if (json.d.list[i].dybz != null && json.d.list[i].dybz != 0) {
                            cxdj.printChecked[i] = true
                        }
                        console.log(json.d.list[i].bah.replace(/(^\s*)|(\s*$)/g,'').length)
                        if(json.d.list[i].bah.replace(/(^\s*)|(\s*$)/g,'').length == 10){
                            arr.push(json.d.list[i]);
                        }
                    }
                    cxdj.jsonList = arr;
                    common.closeLoading();
                } else {
                    malert("查询失败", "top", "defeadted");
                    common.closeLoading();
                }
            });
        },
        checkedSelectFn: function (val) {
            this.checkedSelect[val[1]] = val[2];
            this.$forceUpdate();
        },
        printCheckedFn: function (val) {
            this.printChecked[val[1]] = val[2];
            this.$forceUpdate();
        },
        cyCheckedFn: function (val) {
            this.cyChecked[val[1]] = val[2];
            this.$forceUpdate();
        }
    }
});


function loadOpenPage(name,cb) {
    $('#loadingPrint').load(name + '.html',function () {
        cb && cb()
    });
}
