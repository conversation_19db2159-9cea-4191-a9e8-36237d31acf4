<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="wzhl.css">
</head>
<body class="skin-default">
<div class="wrapper no-scrollbar flex-container flex-dir-c">
    <header class="userNameBg" id="userInfo" v-cloak="">
        <div class="flex">
            <div class="userNameImg">
                <span v-show="brItem.nljd==1">
                    		<img src="/newzui/pub/image/maleBaby.png">
                    	</span>
                    	<span v-show="brItem.nljd==2">
                    		<img src="/newzui/pub/image/femalebaby.png">
                    	</span>
                    	<span v-show="brItem.nljd==3">
                    		<img src="/newzui/pub/image/Group <EMAIL>">
                    	</span>
                    	<span v-show="brItem.nljd==4">
                    		<img src="/newzui/pub/image/Group <EMAIL>">
                    	</span>
                    	<span v-show="brItem.nljd==5">
                    		<img src="/newzui/pub/image/juvenile.png">
                    	</span>
                    	<span v-show="brItem.nljd==6">
                    		<img src="/newzui/pub/image/maid.png">
                    	</span>
                    	<span v-show="brItem.nljd==7">
                    		<img src="/newzui/pub/image/youth.png">
                    	</span>
                    	<span v-show="brItem.nljd==8">
                    		<img src="/newzui/pub/image/woman.png">
                    	</span>
                    	<span v-show="brItem.nljd==9">
                    		<img src="/newzui/pub/image/grandpa.png">
                    	</span>
                    	<span v-show="brItem.nljd==10">
                    		<img src="/newzui/pub/image/grandma.png">
                    	</span>
                        <span v-show="brItem.nljd==11">
                            <img src="/newzui/pub/image/<EMAIL>">
                        </span>
            </div>
            <div class="text-color">
                 <p class="userHeader">
                        <span class="userName" v-text="brItem.brxm"></span>
                        <span class="sex text" v-text="brxb_tran[brItem.brxb]"></span>
                     <span class="nl text">{{brItem.nl}}{{xtwhnldw_tran[brItem.nldw]}}</span>
                    </p>
                    <div class="userCwh">
                        <span class="cwh text" v-text="'床位号：'+ brItem.rycwbh +'号'"></span>
                        <span class="zyh text" v-text="'住院号：'+ brItem.zyh"></span>
                        <span class="bq text" v-text="'科室：'+ brItem.ryksmc"></span>
                        <span class="ys text" v-text="'医师：'+ brItem.zyysxm"></span>
                        <span class="brzt text" v-text="'病人状态：'+zyzt_tran[brItem.zyzt]"></span>
                        <span class="bz text" v-text="'病种：'+ brItem.ryzdmc"></span>
                    </div>
                    <!--<div class="userCwh">-->
                        <!--<span class="fyhj text" v-text="'费用合计：'+ brItem.fyhj"></span>-->
                        <!--<span class="yjhj text" v-text="'预交合计：'+ brItem.yjhj"></span>-->
                        <!--<span class="zyts text" v-text="'住院天数：'+ brItem.zyts"></span>-->
                        <!--<span class="phone text" v-text="'联系电话：'+ brItem.sjhm"></span>-->
                        <!--<span class="sfz text"  v-text="'身份证：'+ brItem.sfzjhm"></span>-->
                    <!--</div>-->
                    <!--<div class="userFooter">-->
                        <!--<span class="hl text" v-text="'护理等级：'"></span>-->
                        <!--<span class="wz text">危重：危重</span>-->
                    <!--</div>-->
                    <div>
                        <p class="heaf text">更多详细信息>></p>
                    </div>
            </div>
        </div>
    </header>
    <div class="menu printHide flex-container flex-jus-sp flex-align-c" id="menu" v-cloak="">
        <div class="col-xxl-8" v-show="num==0">
            <div class="tong-search printHide">
                <div class="zui-form">
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label ">时间</label>
                        <div class="zui-input-inline">
                            <i class="icon-position icon-rl"></i>
                            <input class="zui-input todate wh240 text-indent20" id="timeVal"  v-model="lrsj"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xxl-8" v-show="num==1">
            <div class="tong-search printHide">
                <div class="zui-form">
                    <div class="zui-inline padd-l-54">
                        <label class="zui-form-label ">时间段</label>
                        <div class="zui-input-inline">
                            <i class="icon-position icon-rl"></i>
                            <input class="zui-input todate wh240 text-indent20" placeholder="不限定时间范围" id="timeVal2"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-right menu flex-container flex-align-c flex-jus-e">
            <span class="fa butt-hover fa-th-large" :class="{'active': num==0}" @click="show(0)"></span>
            <span class="relative fenge"></span>
            <span class="fa butt-hover fa-bars" :class="{'active': num==1}" @click="show(1)"></span>
        </div>
    </div>
    <div class="content-box flex-one over-auto no-scrollbar" id="lr" v-if="isShow" v-cloak="" style="padding-bottom: 66px;">
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">监测信息</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box">
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">意识</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="jlContent.ys">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">体温</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <i class="danwei">℃</i>
                            <input class="zui-input" v-model="jlContent.tw">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">脉搏</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <i class="danwei">次/分</i>
                            <input class="zui-input" v-model="jlContent.mb">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">呼吸</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <i class="danwei">次/分</i>
                            <input class="zui-input"  v-model="jlContent.hx">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">心率</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <input class="zui-input" v-model="jlContent.xdjcXl">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-6">
                        <label class="zui-form-label">血压</label>
                        <div class="zui-input-inline pop-input-box grid-box">
                            <div class="col-xxl-6" style="padding-right: 12px;">
                                <div class="zui-input-inline position pop-input-box" style="margin-right: 0;">
                                    <input class="zui-input"  v-model="jlContent.xyL">
                                    <i class="danwei">mmhg</i>
                                    <i class="fenge">/</i>
                                </div>
                            </div>
                            <div class="col-xxl-6" style="padding-left: 12px;">
                                <div class="zui-input-inline position pop-input-box" style="margin-right: 0;">
                                    <input class="zui-input" v-model="jlContent.xyR">
                                    <i class="danwei">mmhg</i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">血氧饱和度</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <i class="danwei">%</i>
                            <input class="zui-input" v-model="jlContent.xybhd">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">吸氧方式</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <input class="zui-input" v-model="qtContent.xyfs">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">吸氧</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <i class="danwei">L/min</i>
                            <input class="zui-input" v-model="jlContent.xy">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-3">
                        <label class="zui-form-label">心电检测</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <i class="danwei">L/min</i>
                            <input class="zui-input" v-model="jlContent.xdjcXl">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid-box">
            <div class="col-xxl-6">
                <div class="tab-card">
                    <div class="tab-card-header">
                        <div class="tab-card-header-title">入量</div>
                    </div>
                    <div class="tab-card-body">
                        <div class="zui-form grid-box">
                            <div class="zui-inline col-xxl-12">
                                <label class="zui-form-label">名称</label>
                                <div class="zui-input-inline">
                                    <textarea class="zui-input zui-textarea" style="max-width: 100%; min-height: 100px; max-height: 100px; line-height: 20px"  v-model="jlContent.rlMc"></textarea>
                                </div>
                            </div>
                            <div class="zui-inline col-xxl-12">
                                <label class="zui-form-label">途径</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="jlContent.rlTj">
                                </div>
                            </div>
                            <div class="zui-inline col-xxl-6">
                                <label class="zui-form-label">数量</label>
                                <div class="zui-input-inline position pop-input-box">
                                    <input class="zui-input" v-model="jlContent.rlSl">
                                    <i class="danwei">ml</i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="tab-card">
                    <div class="tab-card-header">
                        <div class="tab-card-header-title">出量</div>
                    </div>
                    <div class="tab-card-body">
                        <div class="zui-form grid-box">
                            <div class="zui-inline col-xxl-12">
                                <label class="zui-form-label">名称</label>
                                <div class="zui-input-inline">
                                    <textarea class="zui-input zui-textarea" style="max-width: 100%; min-height: 100px; max-height: 100px; line-height: 20px" v-model="jlContent.clMc"></textarea>
                                </div>
                            </div>
                            <div class="zui-inline col-xxl-12">
                                <label class="zui-form-label">颜色性状</label>
                                <div class="zui-input-inline">
                                    <input class="zui-input" v-model="jlContent.clYsxz">
                                </div>
                            </div>
                            <div class="zui-inline col-xxl-6">
                                <label class="zui-form-label">数量</label>
                                <div class="zui-input-inline position pop-input-box">
                                    <input class="zui-input" v-model="jlContent.clSl">
                                    <i class="danwei">ml</i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">观察信息</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box">
                    <div class="zui-inline col-xxl-6">
                        <label class="zui-form-label">皮肤情况</label>
                        <div class="zui-input-inline">
                            <input class="zui-input" v-model="jlContent.pfqk">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-6">
                        <label class="zui-form-label">管路护理</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <input class="zui-input" v-model="jlContent.glhl">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-12">
                        <label class="zui-form-label">病情观察及措施</label>
                        <div class="zui-input-inline position pop-input-box ">
                            <textarea class="zui-input zui-textarea" style="max-width: 100%; min-height: 100px; max-height: 100px; line-height: 20px" v-model="jlContent.bqgcjcs"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title">瞳孔</div>
            </div>
            <div class="tab-card-body">
                <div class="zui-form grid-box">
                    <div class="zui-inline col-xxl-4">
                        <label class="zui-form-label">大小(mm)</label>
                        <div class="zui-input-inline position pop-input-box" style="padding-left: 30px;width: 100px;">
                            <i class="danwei" style="left: 0;width: 30px;color: #a7b1c2;">左</i>
                            <input class="zui-input" v-model="jlContent.tkDxZ">
                        </div>
                        <div class="zui-input-inline position pop-input-box" style="padding-left: 30px;width: 100px;">
                            <i class="danwei" style="left: 0;width: 30px;color: #a7b1c2;">右</i>
                            <input class="zui-input"  v-model="jlContent.tkDxY">
                        </div>
                    </div>
                    <div class="zui-inline col-xxl-4">
                        <label class="zui-form-label">光反应</label>
                        <div class="zui-input-inline position pop-input-box" style="padding-left: 30px;width: 100px;">
                            <i class="danwei" style="left: 0;width: 30px;color: #a7b1c2;">左</i>
                            <input class="zui-input" v-model="jlContent.tkGfyZ">
                        </div>
                        <div class="zui-input-inline position pop-input-box" style="padding-left: 30px;width: 100px;">
                            <i class="danwei" style="left: 0;width: 30px;color: #a7b1c2;">右</i>
                            <input class="zui-input" v-model="jlContent.tkGfyY">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-form grid-box">
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">其他1</label>
                <div class="zui-input-inline">
                    <input class="zui-input" v-model="jlContent.qt1">
                </div>
            </div>
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">其他2</label>
                <div class="zui-input-inline position pop-input-box ">
                    <input class="zui-input" v-model="jlContent.qt2">
                </div>
            </div>
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">其他3</label>
                <div class="zui-input-inline">
                    <input class="zui-input" v-model="jlContent.qt3">
                </div>
            </div>
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">小时及总结小时</label>
                <div class="zui-input-inline position pop-input-box ">
                    <input class="zui-input" v-model="qtContent.xsjzjxs">
                </div>
            </div>
            <div class="zui-inline col-xxl-9">
                <label class="zui-form-label">诊断名称</label>
                <div class="zui-input-inline">
                    <input class="zui-input" v-model="jlContent.zdmc">
                </div>
            </div>
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">护理等级</label>
                <div class="zui-input-inline position pop-input-box ">
                    <input class="zui-input" v-model="jlContent.hljb">
                </div>
            </div>
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">责任护士</label>
                <div class="zui-input-inline">
                    <input class="zui-input"  v-model="jlContent.zrhs">
                </div>
            </div>
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">签字时间</label>
                <div class="zui-input-inline position pop-input-box ">
                    <input class="zui-input" v-model="jlContent.zrhsqzsj">
                </div>
            </div>
            <div class="zui-inline col-xxl-3">
                <label class="zui-form-label">夜间标志</label>
                <div class="zui-input-inline position pop-input-box " style="padding: 6px 0;">
                    <!--<input-checkbox @result="reCheckBox" :list="'brListCheckBox'"-->
                                    <!--:type="'some'"-->
                                    <!--:val="isChecked">-->
                    <!--</input-checkbox>-->
                    <input id="yjbz" class="green" type="checkbox" v-model="jlContent.yjbz">
                    <label for="yjbz"></label>
                </div>
            </div>
        </div>
       <div class="ksys-btn action-bar fixed">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closePage">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="edit()"  v-show="add">保存</button>
   	  </div>
    </div>
    <div class="list-box  flex-one over-auto no-scrollbar" id="list" v-if="isShow" v-cloak="">
        <div class="zui-table-view" z-height="full">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                       <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                       <th><div class="zui-table-cell cell-l"><span>记录日期</span></div></th>
                       <th><div class="zui-table-cell cell-l"><span>操作员</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <p v-if="!jlxx_list.length" class="noData  text-center zan-border">暂无数据...</p>
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in jlxx_list"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                    	@click="checkSelect([$index,'some','jsonList'],$event)"
                    	@dblclick="openJl($index)"
                    	:tabindex="$index"
                    	ref="list">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="fDate(item.jlsj,'yyyy-MM-dd')"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.czyxm"></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="ksys-btn action-bar fixed">
            <button v-waves class="zui-btn btn-primary xmzb-db" @click="ylHld()">病员护理单</button>
        </div>
<!--        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->
    </div>

</div>
</body>
<script src="wzhl.js"></script>
</html>
