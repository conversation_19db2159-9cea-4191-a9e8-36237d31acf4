//主体部分
var wrapper=new Vue({
	el:'#wrapper',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data:{
		//*****************公共
		num:0,
		YFSelect: [],//二级库房列表
		pzhList: [], //凭证号集合
		YPZLSelect: [],//材料种类列表
		pdscJsonList: [],//材料列表
		yfSelected: '',//选中的二级库房
		pzNum: '', //选中的凭证号
		qxksbm: '', //权限科室编码
		csqxContent: {},//获取的参数权限对象
		popContent: { //判断方式选择
			pdWay: 1,
			ypzl: 0,
		},
		ypjgshow: true, //材料价格显示
		pdscShow: true, //盘点生成需要显示
		saveShow: true,
		deleteShow: false,
		passShow: false,
		ypzlShow: false, //材料种类显示
		ypmcShow: false, //材料名称
		pzhShow:  false, //凭证号
		pdlrShow: false,
		ypjsShow: false, //材料检索
		ypjsValue: '', //材料检索内容
		isSubmited: false,//是否禁止保存

		//******************************盘点生成
		fyContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		param: {
			page: 1,
			rows: 50,
			sort: '',
			order: 'asc'
		},
		them_tran: {},
		them: {
			'材料编号': 'ypbm',
			'材料名称': 'ypmc',
			'规格': 'ypgg',
			'分装比例': 'fzbl',
			'进价': 'ypjj',
			'零价': 'yplj',
			'库房单位': 'kfdwmc',
			'二级库房单位': 'yfdwmc',
			'二级库房种类': 'ypzlmc',
			'材料剂型': 'jxmc'
		},

		//********************************未核盘点
		whpdList: [], //未核盘点集合
		whpdmxList: [], //未核盘点明细集合
		whpdbSelected: {}, //已选盘点表

		//*******************************盘点录入
		pdlrList: [], //盘点录入列表

		//******************************录入审核
		pdlrShList: [],
		pdlrShmxList: [],
		pdblrSelected:{},

		//******************************盘点完成
		pdwcPddList: [],
		shState: 0,
		json:{
			jjzj:0,
			ljzj:0,
		},
	},
	updated:function(){
		changeWin()
	},
	mounted: function() {
	},
	computed:{
		money:function () {
			var reducers = {
				totalInEuros: function(state, item) {
					return state.jjzj +=  parseFloat(item.ypjjje);
				},
				totalInYen: function(state, item) {
					return state.ljzj += parseFloat(item.ypljje);
				}
			};
			var manageReducers = function(reducers){
				return function(state, item){
					return Object.keys(reducers).reduce(function(nextState, key){
						reducers[key](state, item);
						return state;
					},{})
				}
			}
			var bigTotalPriceReducer = manageReducers(reducers);
			var totals = this.pdscJsonList.reduce(bigTotalPriceReducer, this.json={
				jjzj:0,
				ljzj:0,
			});
		}
	},
	methods:{
		//切换菜单栏
		tabBg:function (n) {
			this.num=n;
			//根据菜单栏的切换隐藏显示
			//0盘点生成,1未核盘点表,2盘点录入,3录入审核,4盘点完成,
			if(this.num==0){
				this.pdscShow=true;
				this.saveShow=true;
				this.deleteShow=false;
				this.passShow=false;
				this.ypzlShow=false;
				this.ypmcShow=false;
				this.pzhShow=false;
				this.pdlrShow=false;
				this.ypjgshow=true;
				this.ypjsShow=false;
			}else if(this.num==1){
				this.pdscShow=false;
				this.saveShow=false;
				this.deleteShow=true;
				this.passShow=false;
				this.ypzlShow=false;
				this.ypmcShow=false;
				this.pzhShow=false;
				this.pdlrShow=false;
				this.ypjgshow=false;
				this.ypjsShow=false;
				wrapper.getWhpdd();
			}else if(this.num==2){
				this.pdscShow=false;
				this.saveShow=true;
				this.deleteShow=false;
				this.passShow=false;
				this.ypzlShow=false;
				this.ypmcShow=false;
				this.pzhShow=true;
				this.pdlrShow=true;
				this.ypjgshow=false;
				this.ypjsShow=true;
				this.getPdlrPzh();
			}else if(this.num==3){
				this.pdscShow=false;
				this.saveShow=false;
				this.deleteShow=true;
				this.passShow=true;
				this.ypzlShow=false;
				this.ypmcShow=false;
				this.pzhShow=true;
				this.pdlrShow=false;
				this.ypjgshow=false;
				this.ypjsShow=true;
				this.getPdlrPzh();
			}else if(this.num==4){
				this.pdscShow=false;
				this.saveShow=false;
				this.deleteShow=true;
				this.passShow=true;
				this.ypzlShow=false;
				this.ypmcShow=false;
				this.pzhShow=true;
				this.pdlrShow=false;
				this.ypjgshow=false;
				this.ypjsShow=false;
				this.getPdlrPzh();
			}
		},
		//获取材料种类集合
		getYpzlData: function(){
			//加载材料种类列表
			$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl',
				function(data) {
					wrapper.YPZLSelect = data.d.list;
				});
		},

		//获取二级库房权限
		getYFData: function() {
			//获取二级库房列表
			var parm = {
				"ylbm": 'N040030020022006',
			};

			//*****************change by Kiter on 21.4.23*************//
			//下拉框获取库房 - 用例:N040100021006
			$.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm={"ylbm": "N040100021006"}', function(data) {
				if(data.a == 0) {
					wrapper.YFSelect = data.d.list;
					if (wrapper.YFSelect.length > 0){
						wrapper.qxksbm = data.d.list[0].ksbm;
						Vue.set(wrapper.yfSelected, 'yfbm', wrapper.YFSelect[0].yfbm);
						Vue.set(wrapper.yfSelected, 'yfmc', wrapper.YFSelect[0].yfmc);
						wrapper.getCsqx();
					}
				} else {
					malert('库房列表获取:' + data.c + ',请查看权限配置','top','defeadted');
				}
			});

			/*$.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm),
				function(data) {
					if(data.a == 0 && data.d) {
						wrapper.YFSelect = data.d.list;
						wrapper.qxksbm = data.d.list[0].ksbm;
						wrapper.yfSelected=data.d.list[0].yfbm;
						wrapper.getCsqx(); //加载完库房再次加载参数权限
					} else {
						malert("二级库房获取失败",'top','defeadted');
					}
				});*/
		},

		//获取参数权限
		getCsqx: function() {
			//获取参数权限
			var parm = {
				"ylbm": 'N040030020022006',
				"ksbm": this.qxksbm
			};
			//获取参数权限信息
			$.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function(json) {
				if(json.a == 0) {
					if(json.d.length > 0) {
						for(var i = 0; i < json.d.length; i++) {
							var csjson = json.d[i];
							switch(csjson.csqxbm) {
								case "N04003002002200601": //盘点表生成权限	0--无,1--有
									if(csjson.csz) {
										wrapper.csqxContent.csN04003002002200602 = csjson.csz;
									}
									break;

								case "N04003002002200602": //盘点数据录入权限	0--无,1--有
									if(csjson.csz) {
										wrapper.csqxContent.csN04003002002200602 = csjson.csz;
									}
									break;
								case "N04003002002200603": //盘点录入数据审核权限	0--无,1--有
									if(csjson.csz) {
										wrapper.csqxContent.csN04003002002200603 = csjson.csz;
									}
									break;

								case "N04003002002200604": //盘点完成确认权限	0--无,1--有
									if(csjson.csz) {
										wrapper.csqxContent.csN04003002002200604 = csjson.csz;
									}
									break;
								case "N04003002002200605": //新增二级库房盘点材料权限	0--无,1--有
									if(csjson.csz) {
										wrapper.csqxContent.csN04003002002200605 = csjson.csz;
									}
									break;
							}
						}
					}
				} else {
					malert("参数权限获取失败"+json.c,'top','defeadted');
				}
			});
		},

		//二级库房改变
		yfChange: function(val) {
			wrapper.yfSelected = val[0];
			this.qxksbm = wrapper.listGetName(wrapper.YFSelect, wrapper.yfSelected, 'yfbm', 'ksbm');;
			wrapper.pdscJsonList = [];
			wrapper.getCsqx();
		},

		//凭证号改变
		resultPzhChange: function(val){
			wrapper.pzNum = val[0];
			if(this.num==3){ //查询录入审核盘点录入单
				//获取盘点表列表
				var json = {
					'pdpzh':wrapper.pzNum,
					'qrzfbz': 0
				};
				//发送请求，查询盘点表
				$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdlr&json=' + JSON.stringify(json),
					function(data) {
						if(data.a == 0 && data.d.length != 0) {
							wrapper.pdlrShList = data.d;
						} else {
							malert(data.c,'top','defeadted');
						}

					});
			}else if(this.num == 4){
				if(!this.pzNum) {
					malert('盘点凭证号获取失败，请重试！', "top",'defeadted');
					return;
				}
				var pdblr={};
				for(var i=0;i<wrapper.pzhList.length;i++){
					if(wrapper.pzhList[i].pdpzh==wrapper.pzNum){
						pdblr=wrapper.pzhList[i];
					}
				}
				//准备参数
				var json = {
					'pdpzh': pdblr.pdpzh,
					'pdlrpzh': pdblr.pdlrpzh,
					'qrzfbz': '1'
				};

				//发送请求，查询盘点表明细
				$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdlrmx&json=' + JSON.stringify(json),
					function(data) {
						if(data.a == 0) {
							wrapper.pdwcPddList = data.d;
						} else {
							malert('盘点表获取失败，请重试', "top",'defeadted');
						}

					});

				//设置参数
				var jsonTem = {
					'pdpzh': json.pdpzh,
				};

				if(!json.pdpzh) {
					malert('盘点凭证号获取失败，请重试！', "top",'defeadted');
					return false;
				}
				//发送请求，查询盘点录入表
				$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdlr&json=' + JSON.stringify(jsonTem),
					function(data) {
						if(data.a == 0 && data.d.length!=0) {
							wrapper.shState = data.d[0].qrzfbz;
						} else {
							malert(data.c, "top",'defeadted');
						}

					});
			}
		},


		//*****************************盘点生成
		//盘点方式改变
		wayChange: function(val) {
			this.popContent.pdWay =val[0];
			if(this.popContent.pdWay == 0 || this.popContent.pdWay == 1) {
				//不显示材料种类选择
				this.ypzlShow = false;
				this.ypmcShow = false;
			} else if(this.popContent.pdWay == 2) {
				//显示材料种类选择
				this.ypzlShow = true;
				this.ypmcShow = false;
			} else if(this.popContent.pdWay == 3) {
				//显示材料名称选择
				this.ypzlShow = false;
				this.ypmcShow = true;
			}
		},

		//材料下拉检索
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'fyContent', 'searchCon');
			//选中之后的回调操作
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				if (type == 'text') {
					Vue.set(this.popContent, 'ypmc', this.fyContent.ypmc);
					Vue.set(this.popContent, 'ypbm', this.fyContent.ypbm);
					this.ypmcInput = this.fyContent.ypmc;
				}
			}
		},

		//当输入值后才触发
		change: function (add, val) {
			this.ypmcInput = val;
			if (!add) this.page.page = 1;       // 设置当前页号为第一页
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			this.page.parm = this.ypmcInput;
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
			$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(str_param),
				function (data) {
					if (data.d.list.length > 0) {
						if (add) {
							for (var i = 0; i < data.d.list.length; i++) {
								wrapper.searchCon.push(data.d.list[i]);
							}
						} else {
							wrapper.searchCon = data.d.list;
						}
					}
					wrapper.page.total = data.d.total;
					wrapper.selSearch = 0;
					if (data.d.list.length > 0 && !add) {
						$(".selectGroup").hide();
						_searchEvent.show();
					}
				});
		},

		//鼠标双击
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;  // 设置当前页号
				this.change(true, this.ypmcInput);           // 传参表示请求下一页,不传就表示请求第一页
			} else {
				Vue.set(this.popContent, 'ypbm', item.ypbm);
				Vue.set(this.popContent, 'ypmc', item.ypmc);
				this.ypmcInput = item.ypmc;
				$(".selectGroup").hide();
			}
		},

		//生成盘点表
		add: function() {
			//非空判断
			if(!this.yfSelected) {
				malert('请选择二级库房！', "top",'defeadted');
				return;
			}
			//清空数据区
			this.pdscJsonList = [];

			var dg = {
				'page': 1,
				'rows': 65536,
				'sort': "",
				'order': "asc",
				'parm': ''
			};
			//设置参数
			var json = {
				'yfbm': this.yfSelected,
			};
			if(this.popContent.pdWay === 2) {
				json.ypzlbm = this.popContent.ypzl;
			} else if(this.popContent.pdWay === 3) {
				json.ypbm = this.popContent.ypbm;
			};
			common.openloading()
			//发送请求，查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=makePdb&dg=' + JSON.stringify(dg) + '&json=' +
				JSON.stringify(json),
				function(data) {
					if(data.a == 0 && data.d.length != 0) {
						common.closeLoading()
						malert('盘点生成成功', "top",'success');
						// wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
						wrapper.pdscJsonList = data.d;
					} else {
						common.closeLoading()
						malert(data.c, "top",'defeadted');
					}
				});
		},

		//****************************************未核盘点
		getWhpdd: function(){
			//获取盘点表列表
			var json = {
				'qrzfbz': 0,
				'yfbm': this.yfSelected
			};
			//发送请求，查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPd&json=' + JSON.stringify(json),
				function(data) {
					wrapper.whpdList = data.d.list;
				});
		},
		//显示未核盘点表细节
		whshowDetail: function(index, item) {
			//保存已选盘点表
			this.pdbSelected = item;
			//准备参数
			var json = item;

			//发送请求，查询盘点表明细
			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdmx&json=' + JSON.stringify(json),
				function(data) {
					wrapper.whpdmxList = Object.freeze(data.d.list);
				});
		},

		//***************************************盘点录入
		//获取凭证号
		getPdlrPzh: function(){
			//获取盘点表列表
			var json = {
				'qrzfbz': 0,
				'yfbm': this.yfSelected
			};
			//发送请求，查询盘点表
			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPd&json=' + JSON.stringify(json),
				function(data) {
					if(data.a == 0) {
						wrapper.pzhList = data.d.list;
					} else {
						malert(data.c, "top",'defeadted');
					}
				});
		},
		getData:function(){
			if(this.num==2){
				this.autoAdd()
			}
		},
		//自动保存
		autoAdd: function() {
			if(!this.pzNum) {
				malert('请选择凭证号', "top",'defeadted');
				return false;
			}
			//准备参数
			var param={
				'pdpzh': this.pzNum,
				'rows': 10000,
				'parm': this.ypjsValue
			};
			//发送请求，查询盘点表明细
			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdmx&json=' + JSON.stringify(param),
				function(data) {
					if(data.a == 0) {
						for(var i = 0; i < data.d.list.length; i++) {
							data.d.list[i].scsl = data.d.list[i].kcsl;
						}
						wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
						wrapper.pdlrList = data.d.list;
					} else {
						malert(data.c, "top",'defeadted');
					}
				});
		},

		//材料盘点单打印
		printYppdd:function(){
			if(!wrapper.whpdList || wrapper.whpdList.length <=0 || !wrapper.whpdList[0].pdpzh){
				malert("无可打印的数据！","top","defeadted");
				return;
			}
			var reportlets = "[{reportlet: 'ejkf%2Fyfgl_pdbdy.cpt',yljgbm:'" + jgbm + "',pdpzh:'" + wrapper.whpdList[0].pdpzh + "',yfbm:'" + wrapper.yfSelected + "'}]";
			if (!FrPrint(reportlets, null)) {
				window.print();
			}
		},
		//新增
		addpdlr: function(){
			wapAddmx.open();
		},
		//添加
		savepdlr: function(){
			wap.open();
		},
		//确认
		queren:function(){
			malert('quren');
		},
		//作废
		zuofei:function(){
			malert('作废');
		},
		//删除明细
		scmx:function(index){
			this.pdlrList.splice(index, 1);
		},
		//**************************************录入审核
		//显示盘点表细节
		lrshShowDetail: function(index, item) {
			//保存已选盘点表
			this.pdblrSelected = item;
			var json = {
				'pdpzh': item.pdpzh,
				'pdlrpzh': item.pdlrpzh,
				'qrzfbz': '0',
				'parm' : this.ypjsValue
			};
			//发送请求，查询盘点表明细
			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdlrmx&json=' + JSON.stringify(json),
				function(data) {
					if(data.a == 0) {
						wrapper.pdlrShmxList = data.d;
					} else {
						malert(data.c, "top",'defeadted');
					}
				});
		},


		//**************************************公用提交方法
		ypjsData: function(){
			if(this.num==2){
				this.autoAdd();
			}else{
				var json = {
					'parm': this.ypjsValue
				}
				this.lrshShowDetail(0, json);
			}
		},
		//刷新
		refresh: function(){
			//0盘点生成,1未核盘点表,2盘点录入,3录入审核,4盘点完成,
			if(this.num==0){
				this.popContent.pdWay = 0;
				for(item in this.pdWayShow) {
					this.pdWayShow[item] = false;
				}
				this.pdscJsonList = [];
			}else if(this.num==1){
				wrapper.pdbSelected={};
				wrapper.whpdmxList = [];
				wrapper.whpdList = [];
				wrapper.getWhpdd();
			}else if(this.num==2){
				wrapper.pdlrList=[];
			}else if(this.num==3){
				wrapper.pdlrShList=[];
				wrapper.pdlrShmxList=[];
				wrapper.pzNum='';
			}else if(this.num==4){

			}
		},

		//保存
		save:function () {
			if(this.num==0){//盘点生成
				//是否禁止保存
				if(this.isSubmited) {
					malert('数据提交中，请稍候！', "top",'defeadted');
					return;
				}
				if(this.pdscJsonList.length == 0) {
					malert('没有可以提交的数据！', "top",'defeadted');
					return;
				}
				//准备参数
				var pdb = {
					'yfbm': this.yfSelected,
					'qrzfbz': '0',
				};

				var json = {
					'list': {
						'pdb': pdb,
						'pdbmx': this.pdscJsonList,
					},
				}
				//是否禁止保存
				this.isSubmited = true;

				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
					JSON.stringify(json))
					.then(function(data) {
						if(data.body.a == 0) {
							wrapper.pdscJsonList = [];
							malert("提交成功",'top','success');
						} else {
							malert(data.body.c, "top",'defeadted');
						}
						//是否禁止保存
						wrapper.isSubmited = false;
					}, function(error) {
						//是否禁止保存
						wrapper.isSubmited = false;
					});
			}else if(this.num==2){//盘点录入
				//是否禁止提交
				if(this.isSubmited) {
					malert('数据提交中，请稍候！', "top",'defeadted');
					return false;
				}
				//非空判断
				if(wrapper.pdlrList.length == 0) {
					malert('没有数据可以提交', "top",'defeadted');
					return false;
				}
				//准备参数
				for(var i = 0; i < wrapper.pdlrList.length; i++) {
					wrapper.pdlrList[i].pdbmxid = wrapper.pdlrList[i].pdbid;
					wrapper.pdlrList[i].lrsl = wrapper.pdlrList[i].scsl;
				}
				//是否禁止提交
				this.isSubmited = true;
				var pdblr={};
				for(var i=0;i<wrapper.pzhList.length;i++){
					if(wrapper.pzhList[i].pdpzh==wrapper.pzNum){
						pdblr=wrapper.pzhList[i];
					}
				}
				if(!pdblr){
					malert('请选择盘点单', "top",'defeadted');
					return false;
				}
				var json = {
					'list': {
						'pdblr': pdblr,
						'pdblrmx': wrapper.pdlrList,
					}
				};
				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePdlr',
					JSON.stringify(json))
					.then(function(data) {
						if(data.body.a == 0) {
							wrapper.pdlrList = [];
							malert("提交成功",'top','success');
						} else {
							malert(data.body.c, "top",'defeadted');
						}
						//是否禁止提交
						wrapper.isSubmited = false;
					}, function(error) {
						console.log(error);
						//是否禁止提交
						wrapper.isSubmited = false;
					});
			}
		},

		//作废
		invalid: function() {
			if(this.num==1){//未核盘点
				//准备参数
				wrapper.pdbSelected.qrzfbz = '2';
				var json = {
					'list': {
						'pdb': wrapper.pdbSelected,
					}
				};
				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
					JSON.stringify(json)).then(function(data) {
					if(data.body.a == 0) {
						malert("作废成功",'top','success');
						wrapper.whpdmxList = [];
						wrapper.whpdList = [];
						wrapper.getWhpdd();
					} else {
						malert(data.c,'top','defeadted');
					}
				}, function(error) {
					console.log(error);
				});
			}else if(this.num==3){//录入审核
				var json = {
					'list': {
						'pdblr': '2',
						'pdblrmx': null,
					}
				};
				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePdlr',
					JSON.stringify(json))
					.then(function(data) {
						if(data.body.a == 0) {
							malert('作废成功','top','success');
						} else {
							malert(data.c, 'top','defeadted');
						}
						//清空数据区
						wrapper.pdlrShList = [];
						wrapper.pdlrShmxList = [];
					}, function(error) {
						console.log(error);
					});
			}else if(this.num==4){//盘点完成
				if(wrapper.shState == 0) {
					malert('请先审核盘点录入表。', "top",'defeadted');
					return false;
				}
				var pdblr={};
				for(var i=0;i<wrapper.pzhList.length;i++){
					if(wrapper.pzhList[i].pdpzh==wrapper.pzNum){
						pdblr=wrapper.pzhList[i];
					}
				}
				//准备参数
				pdblr.qrzfbz = '2';
				//审核盘点表
				json = {
					'list': {
						'pdb': pdblr,
					}
				}
				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
					JSON.stringify(json))
					.then(function(data) {
						if(data.body.a == 0) {
							malert( "作废成功", "top",'success');
							wrapper.pzhList = [];
							wrapper.pdwcPddList = [];

						} else {
							malert(data.body.c, "top",'defeadted');
						}
					}, function(error) {
						console.log(error);
					});
			}
		},

		//审核
		confirm: function() {
			if(this.num==3){//录入审核
				//准备参数
				this.pdblrSelected.qrzfbz = '1';
				if(!wrapper.pdblrSelected){
					return false;
					malert('请选择盘点录入单', "top",'defeadted');
				}
				//准备参数
				var json = {
					'list': {
						'pdblr': this.pdblrSelected,
						'pdblrmx': null,
					}
				};
				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePdlr',
					JSON.stringify(json))
					.then(function(data) {
						if(data.body.a == 0) {
							malert("审核成功",'top','success');
						} else {
							malert(data.c, "top",'defeadted');
						}
						//清空数据区
						wrapper.pdlrShList = [];
						wrapper.pdlrShmxList = [];
					}, function(error) {
						console.log(error);
					});
				//修改实存数量
				wrapper.pdblrSelected.qrzfbz = num;
				//准备数据
				for(var i = 0; i < wrapper.pdlrShmxList.length; i++) {
					wrapper.pdlrShmxList[i].pdbmxid = wrapper.pdlrShmxList[i].pdbid;
					wrapper.pdlrShmxList[i].lrsl = wrapper.pdlrShmxList[i].scsl;

				}
				var json = {
					'list': {
						'pdblr': wrapper.pdblrSelected,
						'pdblrmx': wrapper.pdlrShmxList,
					}
				};

				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=updateScsl',
					JSON.stringify(json))
					.then(function(data) {
						if(data.body.a == 0) {
							wrapper.pdlrShList = [];
							wrapper.pdlrShmxList = [];
							malert("录入表审核成功",'top','success');
						} else {
							malert(data.body.c, "top",'defeadted');
						}
					}, function(error) {
						console.log(error);
					});
			}else if(this.num==4){//盘点完成
				if(wrapper.shState == 0) {
					malert('请先审核盘点录入表。', "top",'defeadted');
					return false;
				}
				var pdblr={};
				for(var i=0;i<wrapper.pzhList.length;i++){
					if(wrapper.pzhList[i].pdpzh==wrapper.pzNum){
						pdblr=wrapper.pzhList[i];
					}
				}
				//准备参数
				pdblr.qrzfbz = '1';
				//审核盘点表
				json = {
					'list': {
						'pdb': pdblr,
					}
				}
				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
					JSON.stringify(json))
					.then(function(data) {
						if(data.body.a == 0) {
							malert( "审核成功", "top",'success');
							wrapper.pzhList = [];
							wrapper.pdwcPddList = [];

						} else {
							malert(data.body.c, "top",'defeadted');
						}
					}, function(error) {
						console.log(error);
					});
			}

		},

		//报表
		autoGen:function () {

		}
	},
});

//右侧弹窗部分新录入盘点单明细
var wap=new Vue({
	el:'#brzcList',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data:{
		type:true,
		ypmcInput: '',
		popContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		them_tran: {},
		them: {
			'生产批号': 'scph',
			'材料编号': 'ypbm',
			'材料名称': 'ypmc',
			'库存数量': 'kcsl',
			'有效期至': 'yxqz',
			'规格': 'ypgg',
			'分装比例': 'fzbl',
			'进价': 'ypjj',
			'零价': 'yplj',
			'库房单位': 'kfdwmc',
			'二级库房单位': 'yfdwmc',
			'效期': 'yxqz',
			'材料剂型': 'jxmc'
		},


	},
	mounted:function () {

	},
	watch: {

	},
	methods:{
		//关闭
		closes: function () {
			this.type=true
		},
		open: function () {
			this.type=false
		},

		//材料下拉检索
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'popContent', 'searchCon');
			//选中之后的回调操作
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				this.ypmcInput = this.popContent.ypmc;
				if(type == 'ypmc') {
					$('#scsl').focus();
				}
			}
		},

		//当输入值后才触发
		change: function (add, val) {
			this.ypmcInput = val;
			if (!add) this.page.page = 1;       // 设置当前页号为第一页
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			this.page.parm = this.ypmcInput;
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
			str_param.pdpzh=wrapper.pzNum;
			$.getJSON('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=queryPdmx&json=' + JSON.stringify(str_param),
				function (data) {
					if (data.d.list.length > 0) {
						if (add) {
							for (var i = 0; i < data.d.list.length; i++) {
								data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
								data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
								wap.searchCon.push(data.d.list[i]);
							}
						} else {
							for (var i = 0; i < data.d.list.length; i++) {
								data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
								data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
							}
							wap.searchCon = data.d.list;
						}
					}
					wap.page.total = data.d.total;
					wap.selSearch = 0;
					if (data.d.list.length > 0 && !add) {
						$(".selectGroup").hide();
						_searchEvent.show();
					}
				});
		},

		//鼠标双击
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;  // 设置当前页号
				this.change(true, this.ypmcInput);           // 传参表示请求下一页,不传就表示请求第一页
			} else {
				this.popContent = item;
				this.ypmcInput = item.ypmc;
				$(".selectGroup").hide();
			}
		},

		//新增一条材料记录
		addOne: function() {
			if (this.popContent.ypmc == null) {
				malert('请输入材料！','top','defeadted');
				return;
			}
			if (this.popContent.scsl == null || this.popContent.scsl == null) {
				malert('请输入材料数量！','top','defeadted');
				return;
			}
			wrapper.pdlrList.push(this.popContent);
			this.popContent = {};
			$("#ypmc").focus();
		},
	}
});

//在当前盘点单上新增明细
var wapAddmx=new Vue({
	el:'#pddsjxz',
	mixins: [dic_transform, baseFunc, tableBase, mformat],
	components: {
		'search-table': searchTable
	},
	data:{
		type:true,
		ghdwList: [], //供货单位
		ypmcInput: '',
		popContent: {},
		searchCon: [],
		selSearch: -1,
		page: {
			page: 1,
			rows: 10,
			total: null
		},
		them_tran: {},
		them: {
			'生产批号': 'scph',
			'材料编号': 'ypbm',
			'材料名称': 'ypmc',
			'规格': 'ypgg',
			'分装比例': 'fzbl',
			'进价': 'ypjj',
			'零价': 'yplj',
			'库房单位': 'kfdwmc',
			'二级库房单位': 'yfdwmc',
			'材料剂型': 'jxmc'
		}

	},
	mounted:function () {
		laydate.render({
			elem: '.times1'
			, trigger: 'click'
			, theme: '#1ab394',
			done: function (value, data) {
				wapAddmx.popContent.scrq = value

			}
		});
		laydate.render({
			elem: '.times2'
			, trigger: 'click'
			, theme: '#1ab394',
			done: function (value, data) {
				wapAddmx.popContent.yxqz = value
			}
		});
	},
	watch: {

	},
	methods:{
		//关闭
		closes: function () {
			this.type=true

		},
		open: function () {
			this.type=false
		},

		//获取供货单位
		getGhdw: function(){
			//初始化页面记载供货单位
			var GhdwDg = {
				page: 1,
				rows: 500,
				sort: "dwbm",
				order: "asc",
				parm: ''
			};

			$.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(GhdwDg),
				function(json) {
					if(json.a == 0) {
						wapAddmx.ghdwList = json.d.list;
					} else {
						malert("供货单位获取失败",'top','defeadted');
					}
				});
		},

		//保存添加
		addNewYp: function () {
			//判断录入数据是否填全
			var inputArr = document.getElementsByClassName("_addData");
			for (var i = 0; i < inputArr.length; i++) {
				if (inputArr[i].value == null || inputArr[i].value == '') {
					inputArr[i].className = 'emptyError';
					malert('数据未输入!','top','defeadted');
					return;
				}
			}
			//添加凭证号
			this.popContent.pdpzh = wrapper.pzNum;
			//准备参数
			var json = this.popContent;
			this.popContent.kcsl = 0; //库存默认为0
			this.popContent.scsl = 0; //实存默认为0
			//添加新增材料到材料明细
			var json = {
				'list': {
					'pdb': null,
					'pdbmx': [this.popContent]
				}
			};
			//发送请求保存数据
			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
				JSON.stringify(json))
				.then(function(data) {
					if(data.body.a == 0) {
						this.popContent = {};
						malert("提交成功",'top','success');
					} else {
						malert(data.body.c,'top','defeadted');
					}
				}, function(error) {
					console.log(error);
				});
			//关闭弹出框
			this.closes();
		},

		//材料下拉检索
		changeDown: function (event, type) {
			if (this['searchCon'][this.selSearch] == undefined) return;
			this.keyCodeFunction(event, 'popContent', 'searchCon');
			//选中之后的回调操作
			if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
				this.ypmcInput = this.popContent.ypmc;
				if(type == 'ypmc') {
					$('#scsl').focus();
				}
			}
		},

		//当输入值后才触发
		change: function (add, val) {
			this.ypmcInput = val;
			if (!add) this.page.page = 1;       // 设置当前页号为第一页
			var _searchEvent = $(event.target.nextElementSibling).eq(0);
			this.page.parm = this.ypmcInput;
			var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
			var bean={
				'pdpzh':wrapper.pzNum
			};
			$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(str_param) + "&parm=" + JSON.stringify(bean),
				function (data) {
					if (data.d.list.length > 0) {
						if (add) {
							for (var i = 0; i < data.d.list.length; i++) {
								data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
								data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
								wapAddmx.searchCon.push(data.d.list[i]);
							}
						} else {
							for (var i = 0; i < data.d.list.length; i++) {
								data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
								data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
							}
							wapAddmx.searchCon = data.d.list;
						}
					}
					wapAddmx.page.total = data.d.total;
					wapAddmx.selSearch = 0;
					if (data.d.list.length > 0 && !add) {
						$(".selectGroup").hide();
						_searchEvent.show();
					}
				});
		},

		//鼠标双击
		selectOne: function (item) {
			if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
				this.page.page++;  // 设置当前页号
				this.change(true, this.ypmcInput);           // 传参表示请求下一页,不传就表示请求第一页
			} else {
				this.popContent = item;
				this.ypmcInput = item.ypmc;
				$(".selectGroup").hide();
			}
		},
		changeDate: function (event, type) {
			//选中之后的回调操作
			if(window.event.keyCode == 13) {
				//获取时间控件对象
				var dateObj = document.getElementById(type);
				//获取日期
				var tempDate = dateObj.value;
				//非空判断
				if(tempDate == null || tempDate == undefined || tempDate == '') {
					malert('请输入' + (type == '_yxqz' ? '有效期' : '生产日期'),'top','defeadted');
					//触发时间控件
					dateObj.click();
					return;
				} else {
					//激活插件检测校验时间
					dateObj.blur();
				}
				//判断格式
				var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
				var regExp = new RegExp(reg);
				if(!regExp.test(tempDate)) {
					malert("日期格式不正确，正确格式为：2017-01-01",'top','defeadted');
					return;
				}
				//跳转下一个输入框
				this.nextFocus(event);
			}
		},
	}


});


//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
wrapper.getYpzlData();
wrapper.getYFData();
wapAddmx.getGhdw();


//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
	var bol = $(e.target).parents().is(".selectGroup");
	if(!bol) {
		$(".selectGroup").hide();
	}

});



