<div id="zd">
	<div class="toolMenu">
		<button @click="getData"><span class="fa fa-refresh"></span>刷新</button>
		<button @click="addData"><span class="fa fa-plus"></span>新增</button>
		<button @click="edit()"><span class="fa fa-edit"></span>修改</button>
		<button @click="remove"><span class="fa fa-trash-o"></span>删除</button>
	</div>
	<div class="tableDiv">
		<table class="patientTable zd" cellspacing="0" cellpadding="0">
			<thead>
				<tr>
					<th class="tableNo"></th>
					<th><input type="checkbox" v-model="isCheckAll" @click="checkAll('jsonList')"></th>
					<th>物资编码</th>
					<th>物资名称</th>
					<th>拼音代码</th>
					<th>产地</th>
					<th>物资规格</th>
					<th>物资单位</th>
					<th>领用单位</th>
					<th>备注描述</th>
					<th>物资类别</th>
					<th>进价</th>
					<th>单价</th>
					<th>停用标导</th>
					<th>分装比例</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<th v-for="item in 15"></th>
				</tr>
				<tr v-for="(item, $index) in jsonList" @click="checkOne($index)" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
					<th class="tableNo" v-text="$index+1"></th>
					<th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)" />
					</th>
					<td v-text="item.wzbm"></td>
					<td v-text="item.wzmc"></td>
					<td v-text="item.pydm"></td>
					<td v-text="item.cd"></td>
					<td v-text="item.wzgg"></td>
					<td v-text="item.kfdwmc"></td>
					<td v-text="item.lydwmc"></td>
					<td v-text="item.bzms"></td>
					<td v-text="item.wzlbmc"></td>
					<td v-text="item.jj"></td>
					<td v-text="item.dj"></td>
					<td v-text="stopSign[item.tybz]"></td>
					<td v-text="item.fzbl"></td>
				</tr>
			</tbody>
		</table>
	</div>

	<div class="tablePage">
		<select v-model="param.rows" @change="getData">
			<option value="10">10</option>
			<option value="20">20</option>
			<option value="30">30</option>
		</select>
		<div class="pageBtu fa fa-angle-left" @click="changePage('prev')"></div>
		第<input v-model="param.page" class="enterPage" @keyup.enter="changePage" type="number">页&nbsp;&nbsp; 共
		<span v-text="totlePage"></span>页
		<div class="pageBtu fa fa-angle-right" @click="changePage('next')"></div>
	</div>
</div>

<div id="zdPop">
	<transition name="pop-fade">
		<div class="pop" v-show="isShow" style="display: none">
			<div class="popCenter">
				<div id="zdPopCon" class="popInfo" style="height: 388px">
					<div class="popTitle dragCSS" v-text="title" onmousedown="drag(event,'zdPopCon')" onmouseup="stopDrag()"></div>
					<table class="popTable" cellspacing="0" cellpadding="0">
						<tr>
							<th>物资编码:</th>
							<td><input v-model="popContent.wzbm" placeholder="自动生成" disabled="disabled"></td>
							<th>物资名称:</th>
							<td><input data-notEmpty="true" v-model="popContent.wzmc" @keydown="nextFocus($event)" @blur="setPYDM(popContent.wzmc, 'popContent', 'pydm')"></td>
							<th>拼音代码:</th>
							<td><input data-notEmpty="true" v-model="popContent.pydm" @keydown="nextFocus($event)"></td>
						</tr>
						<tr>
							<th>物资类别:</th>
							<td>
								<select-input @change-data="resultChange" :child="lbList" :index="'lbmc'" 
								:index_val="'lbbm'" :val="popContent.wzlb" :search="true" :name="'popContent.wzlb'">
								</select-input>
							</td>
							<th>物资规格:</th>
							<td><input data-notEmpty="true" v-model="popContent.wzgg" @keydown="nextFocus($event)"></td>
							<th>库房单位:</th>
							<td>
								<select-input @change-data="resultChange" :child="dwList" :index="'dwmc'" 
								:index_val="'dwbm'" :val="popContent.kfdw" :search="true" :name="'popContent.kfdw'">
								</select-input>
								<!--<select v-model="popContent.kfdw">
									<option v-for="item in dwList" :value="item.dwbm" v-text="item.dwmc"></option>
								</select>-->
							</td>
						</tr>
						<tr>
							<th>领用单位:</th>
							<td>
								<select-input @change-data="resultChange" :child="dwList" :index="'dwmc'" 
								:index_val="'dwbm'" :val="popContent.lydw" :search="true" :name="'popContent.lydw'">
								</select-input>
								<!--<select v-model="popContent.lydw">
									<option v-for="item in dwList" :value="item.dwbm" v-text="item.dwmc"></option>
								</select>-->
							</td>
							<th>分装比例:</th>
							<td><input id="fzbl" type="number" :disabled="fzblNot" data-notEmpty="true" v-model="popContent.fzbl" @keydown="nextFocus($event)"></td>
							<th>进价:</th>
							<td><input type="number" data-notEmpty="false" v-model="popContent.jj" @keydown="nextFocus($event)"></td>
							<tr>
								<th>单价:</th>
								<td><input type="number" data-notEmpty="false" v-model="popContent.dj" @keydown="nextFocus($event)"></td>
								<th>产地:</th>
								<td><input data-notEmpty="false" v-model="popContent.cd" @keydown="nextFocus($event)"></td>
							</tr>
							<tr>
								<th>备注描述:</th>
								<td><input data-notEmpty="false" v-model="popContent.bzms" @keydown="nextFocus($event)"></td>
								<th>停用标志:</th>
								<td>
									<select-input @change-data="resultChange" :not_empty="true" :child="stopSign" :index="popContent.tybz" :val="popContent.tybz" :name="'popContent.tybz'">
									</select-input>
								</td>
							</tr>
					</table>
					<div class="popDoBtu popBtu">
						<button @click="saveData" class=""><span class="fa fa-save"></span>保存</button>
						<button @click="isShow = false" class="cancel"><span class="fa fa-close"></span>取消</button>
					</div>
				</div>
			</div>
		</div>
	</transition>
</div>

<script type="text/javascript" src="zd.js"></script>