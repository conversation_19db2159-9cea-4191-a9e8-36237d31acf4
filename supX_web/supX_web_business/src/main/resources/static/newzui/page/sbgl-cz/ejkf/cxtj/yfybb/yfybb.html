
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药房月报表</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b " >导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                        <select-input @change-data="resultRydjChange" class="wh150 padd-l-10"
                                      :child="yfkfList" :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm"
                                      :name="'param.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh240 text-indent20" placeholder="请选择申请日期" id="timeVal"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label padd-l-20">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input todate wh240" placeholder="请输入关键字" v-model="param.parm"  @keydown.13="goToPage(1)"/>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view ybglTable padd-r-10 padd-l-10" id="utable1" z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">序号</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">种类编码</div>
                    </th>
                    <th v-if="qShow">
                        <div class="zui-table-cell cell-s">种类名称</div>
                    </th>
                    <th v-if="qShow">
                        <div class="zui-table-cell cell-s">期初金额</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">药库入库</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">药房入库</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">盘点入库</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">调拨入库</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">退库出库</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">报损出库</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">盘点出库</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">调拨出库</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">损溢金额</div>
                    </th>
                    <th >
                        <div class="zui-table-cell cell-s">结余金额</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body body-heights" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  @dblclick="edit($index)"  @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlbm"></div></td>
                    <td  v-if="qShow">
                        <div class="zui-table-cell cell-s" v-text="item.ypzlmc">
                    </div>
                    </td>
                    <td  v-if="qShow">
                        <div class="zui-table-cell  cell-s " v-text="item.qcje"></div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kdrk"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.tkrk"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pdrk"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.dbrk"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.lyck"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.thck"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bsck"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pdck"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.syje"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jyje"></div></td>
                    <p v-if="jsonList.length==0" class="  noData margin-top-100 text-center">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>


    </div>

</div>
<script src="yfybb.js"></script>
</body>

</html>
