<div id="wzdl">
	<div class="toolMenu">
		<button @click="getData"><span class="fa fa-refresh"></span>刷新</button>
		<button @click="addData"><span class="fa fa-plus"></span>新增</button>
		<button @click="edit()"><span class="fa fa-edit"></span>修改</button>
		<button @click="remove"><span class="fa fa-trash-o"></span>删除</button>
	</div>
	<div class="tableDiv">
		<table class="patientTable wzdl" cellspacing="0" cellpadding="0">
			<thead>
				<tr>
					<th class="tableNo"></th>
					<th><input type="checkbox" v-model="isCheckAll" @click="checkAll"></th>
					<th>大类编码</th>
					<th>大类名称</th>
					<th>拼音代码</th>
					<th>停用标志</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<th v-for="item in 6"></th>
				</tr>
				<tr v-for="(item, $index) in jsonList" @click="checkOne($index)" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
					<th class="tableNo" v-text="$index+1"></th>
					<th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)" />
					</th>
					<td v-text="item.dlbm"></td>
					<td v-text="item.dlmc"></td>
					<td v-text="item.pyjm"></td>
					<td v-text="stopSign[item.tybz]"></td>
				</tr>
			</tbody>
		</table>
	</div>

	<div class="tablePage">
		<select v-model="param.rows" @change="getData">
			<option value="10">10</option>
			<option value="20">20</option>
			<option value="30">30</option>
		</select>
		<div class="pageBtu fa fa-angle-left" @click="changePage('prev')"></div>
		第<input v-model="param.page" class="enterPage" @keyup.enter="changePage" type="number">页&nbsp;&nbsp; 共
		<span v-text="totlePage"></span>页
		<div class="pageBtu fa fa-angle-right" @click="changePage('next')"></div>
	</div>
</div>

<div id="wzdlPop">
	<transition name="pop-fade">
		<div class="pop" v-show="isShow" style="display: none">
			<div class="popCenter">
				<div id="wzdlPopCon" class="popInfo">
					<div class="popTitle dragCSS" v-text="title" onmousedown="drag(event,'wzdlPopCon')" onmouseup="stopDrag()"></div>
					<table class="popTable" cellspacing="0" cellpadding="0">
						<tr>
							<th>大类编码:</th>
							<td><input data-notEmpty="false" placeholder="自动生成" v-model="popContent.dlbm" @keydown="nextFocus($event)" disabled="disabled">
							</td>
							<th>大类名称:</th>
							<td><input data-notEmpty="true" v-model="popContent.dlmc" @keydown="nextFocus($event)" @blur="setPYDM(popContent.dlmc, 'popContent', 'pydm')">
							</td>
						</tr>
						<tr>
							<th>拼音代码:</th>
							<td><input data-notEmpty="false" v-model="popContent.pydm" @keydown="nextFocus($event)" disabled="disabled">
							</td>
						</tr>
						<tr>
							<th>停用标志:</th>
							<td>
								<select-input @change-data="resultChange" :not_empty="true" :child="stopSign" :index="popContent.tybz" :val="popContent.tybz" :name="'popContent.tybz'">
								</select-input>
							</td>
						</tr>
					</table>
					<div class="popDoBtu popBtu">
						<button @click="saveData" class=""><span class="fa fa-save"></span>保存</button>
						<button @click="isShow = false" class="cancel"><span class="fa fa-close"></span>取消</button>
					</div>
				</div>
			</div>
		</div>
	</transition>
</div>

<script type="text/javascript" src="sbdl.js"></script>