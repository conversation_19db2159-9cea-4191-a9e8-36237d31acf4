var pram = {};
var ksbm='';
var objbydh='';
//科目
var tableInfo = new Vue({
    el: '.zui-table-view',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        jsonList: [],
        yfkfList: [],
        type: 1,
        fylx: 0, //服药方法类型
        useWayShow: true,
        yfkf: 0, //药房库房信息
        param: {
            page: 1,
            rows: 10,
            //sort: 'rkd.rkdh',
            order: 'desc',
            shzfbz: 1,
            kfbm: '',
            rkfs:'01',//01-出库
            beginrq: null,
            endrq: null,
            parm:''
        },
        zhuangtai:{
            "0":'待发药',
            "1":'已发药',
            "2":'部分发药',
            "3":'已退药'
        }


    },

    methods: {
        //获取数据
        getData: function() {
            common.openloading('.zui-table-view');
            if ($("#jsvalue").val() != null && $("#jsvalue").val() != '') {
                this.param.parm = $("#jsvalue").val();
            } else {
                this.param.parm = '';
            }
            if(this.fylx == undefined || this.fylx == null || this.fylx == "") {
                this.fylx = "0";
            }
            pram = {
                ylbm: 'N040030020012002',
                ksbm: toolBar.barContent.ryks, //toolBar在bqby.js内定义
                beginrq: toolBar.param.beginrq,
                endrq: toolBar.param.endrq,
                fylx: this.fylx
            };
            var types = "";
            if(this.type == "0") {
                types = "bydhzwfy";
            } else {
                types = "bydmxwfy";
            }

            //console.log("pram:"+JSON.stringify(pram));
            //请在这里写入查询科室及摆药单号的请求，并传值给menuTree_1.jsonList
            $.getJSON("/actionDispatcher.do?reqUrl=HszHlywFymx&types=" + types + "&parm=" + JSON.stringify(pram), function(json) {
                if(json.a == 0) {
                    //console.log("json:"+JSON.stringify(json.d.list));
                    tableInfo.total = json.d.total;
                    tableInfo.jsonList = json.d.list;
                    console.log(json.d.list);

                } else {
                    malert(json.c,'top','defeadted')
                }
            });
            common.closeLoading()
            // //清空配方明细
            // fydContext.jsonList = [];
            // fydContext.fylist = {};
            // if(fydContext.isMx) {
            //     fydContext.fylist.bt = "医嘱明细发药单";
            // } else {
            //     fydContext.fylist.bt = "医嘱汇总发药单";
            // }

        },
        fayao:function (bydh) {

            fydContext.title='药品明细';
            fydContext.open();
            var param = {
                'bydh':bydh,
                'ksbm':toolBar.barContent.ryks,
            };
            //获取打印数据
            $.getJSON("/actionDispatcher.do?reqUrl=YfbYfywBqby&types=print&parm=" + JSON.stringify(param),
                function(json) {
                    if(json.a == 0) {
                        fydContext.mxList = json.d.djmx;
                        fydContext.times = json.d.dj;

                    } else {
                        alert("摆药单查询失败：" + json.c)
                    }
                });
        },
        //单击
        checkOneMx: function(index,item) {
            this.isCheckMx = [];
            this.isCheckMx[index] = true;

            objbydh=item.bydh;
            console.log(objbydh);

        }
    },


});
//发药单明细信息
var fydContext = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        isMx: true,
        title:'',
        prTitle:'',
        jsonList: [],
        mxList:[],
        times:[],
        barContent:{},
        fylxShow:true,
        num:0,
        fylist: {
            "bt": "医嘱明细发药单"
        },
        AllList:
            [
                {'fylx':'0','yfxzmc':'全部'},
                {'fylx':'1','yfxzmc':'口服'},
                {'fylx':'2','yfxzmc':'输液'},
                {'fylx':'3','yfxzmc':'肌注'},
                {'fylx':'4','yfxzmc':'其他/口服'},
                {'fylx':'5','yfxzmc':'输液/肌注'},
                {'fylx':'6','yfxzmc':'输液/肌注/其他'},
                {'fylx':'7','yfxzmc':'注册/输液'}
            ],
    },
    methods: {
        print:function(){
            $("<link>").attr({ rel: "stylesheet",
                type: "text/css",
                href: "pr.css",
                media:"print"
            }).appendTo("head");
            var dom = document.getElementById('zhcx');
            var dom1 = document.getElementById('zhcx1');
            if(this.num==0){
                fydContext.prTitle='明细发药单';
                $(dom).jqprint({
                    debug: false,
                    importCSS: true,
                    printContainer: true,
                    operaSupport: false
                });

            }else if(this.num==1){
                fydContext.prTitle='汇总发药单';
                $(dom1).jqprint({
                    debug: false,
                    importCSS: true,
                    printContainer: true,
                    operaSupport: false
                });

            }


            // window.print();
        },
        closes:function () {
            $(".side-form").removeClass('side-form-bg');
            $(".side-form").addClass('ng-hide');
        },
        open: function () {
            $(".side-form-bg").addClass('side-form-bg');
            $(".side-form").removeClass('ng-hide');
            Vue.set(fydContext.barContent,'fylx','0');
        },
        tabBg:function (index) {
            this.num=index;
            switch (index){
                case 0:

                    break;
                case 1:
                    break;
                default:
                    break;
            }
        },
        //组件选择下拉框之后的回调
        resultRydjChanges: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "fylx":
                    if(val[0]==0){
                        this.fylxShow=true;
                    }else{
                        this.fylxShow=false;
                    }
                    Vue.set(this.barContent, 'fylx', val[0]);
                    Vue.set(this.barContent, 'yfxzmc', val[4]);
                    // wap.getCsqx();
                    break;
                default:
                    break;
            }
        },
    }
});
var toolBar=new Vue({
        el:'.panel',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
        data:{
            jsonList: [],
            yfkfList: [],
            ksList:[],
            fylx: 0, //服药方法类型
            num:0,
            barContent: {
                dateBegin: getTodayDateBegin(),
                dateEnd: getTodayDateEnd()
            },
            zyksList:[],
            yfkf: 0, //药房库房信息
            param: {
                page: 1,
                rows: 10,
                // sort: 'ckd.ckdh,ypmc',
                order: 'desc',
                shzfbz: 1,
                kfbm: '',
                ckfs:'01',//01-出库
                beginrq: getTodayDateBegin(),
                endrq: getTodayDateEnd(),
                parm:''
            }
        },
        mounted: function () {
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        	laydate.render({
                elem: '#timeVal', 
                type: 'datetime',
                eventElem: '.zui-date', 
                value: this.param.beginrq,
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    toolBar.param.beginrq = value;
                }
            });
            laydate.render({
                elem: '#timeVal1',
                eventElem: '.zui-date',
                value: this.param.endrq,
                type: 'datetime',
                trigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    toolBar.param.endrq = value;
                    tableInfo.getData();
                }
            });
        },
        methods:{
            //检索查询回车键
            searchHc: function() {
                if(window.event.keyCode == 13) {
                    tableInfo.getData();
                }

            },
            tabBg: function(index) {
                this.num=index;
                //这里初始化请求发药单需要的参数
                switch (index){
                    case 0:
                        alert('0');
                        break;
                    case 1:
                        alert('1');
                        break;

                }

            },
            FaYaoEdit:function (obj) {
               /* obj=objbydh;
                if(num == null) {
                    for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            num = i;

                            tableInfo.fayao(obj);
                            break;
                        }
                    }
                    if(num == null) {
                        malert("请选中当前数据",'top','defeadted');
                        return false;
                    }
                }*/
            },
            //刷新
            sx:function () {
                tableInfo.getData();
            },
            //获取药房
            getYfbm: function() {
                pram = {
                    "ylbm": 'N040030020012002'
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(pram), function(json) {
                    if(json.a == 0) {
                        toolBar.ksList = json.d.list;
                        // pop.jsonList = json.d.list;

                        if(json.d.list.length > 0) {
                            toolBar.barContent.yfbm = json.d.list[0].yfbm;
                            toolBar.barContent.yfmc = json.d.list[0].yfmc;
                            toolBar.GetZyksData();//获取科室
                        }
                    } else {
                        malert("药房编码获取失败：" + json.c);
                    }
                });
            },
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                var isTwo = false;
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "yfbm":
                        Vue.set(this.barContent, 'yfbm', val[0]);
                        Vue.set(this.barContent, 'kwmc', val[4]);
                        tableInfo.getData();
                        // wap.getCsqx();
                        break;
                    case "ryks"    :
                        //先获取住院医生的值
                        Vue.set(this.barContent, 'ryks', val[0]);

                        Vue.set(this.barContent, 'ryksmc', val[4]);
                        tableInfo.getData();
                        break;
                    default:
                        break;
                }
            },
            //页面加载时自动获取住院科室Dddw数据
            GetZyksData: function () {
                var bean = {"zyks": "1"};
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&json=" + JSON.stringify(bean), function (json) {
                    if (json.a == 0) {
                        toolBar.zyksList = json.d.list;
                        Vue.set(toolBar.barContent,'ryks',toolBar.zyksList[0].ksbm);
                        tableInfo.getData();
                    } else {
                        malert(json.c, "住院科室列表查询失败");

                    }
                });
            },
            //组件选择下拉框之后的回调
            // resultRydjChange: function (val) {
            //     var isTwo = false;
            //     //先获取到操作的哪一个
            //     var types = val[2][val[2].length - 1];
            //     switch (types) {
            //         case "ryks"    :
            //             //先获取住院医生的值
            //             Vue.set(this.barContent, 'ryks', val[0]);
            //
            //             Vue.set(this.barContent, 'ryksmc', val[4]);
            //             tableInfo.getData();
            //             break;
            //         default:
            //             break;
            //     }
            // },
            //申领科室选择
            Wf_KsChange: function() {
                var obj = event.currentTarget;
                var selected = $(obj).find("option:selected");
                toolbar.lyksmc = selected.text();
            }

        }
    });

//改变vue异步请求传输的格式
    Vue.http.options.emulateJSON = true;
//弹出框保存路径全局变量
    var saves = null;

    //tableInfo.getData();
    toolBar.getYfbm();
    //toolBar.GetZyksData();

window.getTime = function(event, type) {
    if(type == 'star') {
        toolBar.param.beginrq = $(event).val().slice(0,10);
    } else if(type == 'end') {
        toolBar.param.endrq = $(event).val().slice(12,23);
    }
};




