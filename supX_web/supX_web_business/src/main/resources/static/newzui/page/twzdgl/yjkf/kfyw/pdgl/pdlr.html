<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="pdlr">
    <div class="toolMenu toolMenu_2 padd-t-10 flex-container flex-align-c padd-l-10">
        <button @click="save" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-save"></span>保存</button>
        <button @click="autoGen" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-plus"></span>自动生成</button>
        <button @click="addNew()" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-plus"></span>新增</button>
        <div class="flex-container padd-r-10 flex-align-c">
            <span class="padd-r-10">凭证号</span>
            <select class=" wh120 zui-input" v-model="pdb" @change="clearAll">
                <option value="0">-请选择-</option>
                <option v-for="item in pzhList" v-text="item.pdpzh" :value="item"></option>
            </select>
        </div>
        <div class="flex-container flex-align-c">
            <span class="margin-r-10">材料检索</span>
            <input @input="searchYP" class="zui-input wh120" placeholder="输入内容后按回车查询" type="text" v-model="ypjs">
        </div>
    </div>
    <div class="enter_tem1 enter_pdlrlr">
        <div class="tab-card enter_tem1 enter_pdlrlr">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">数据录入</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="grid-box">
                    <div class="toolMenu button0 flex-container padd-b-10">
                        <button @click="add" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-save"></span>添加
                        </button>
                        <button @click="clear" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-plus"></span>清除
                        </button>
                    </div>
                    <div class="flex-container flex-wrap-w">
                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">材料名称</span>
                            <input id="ypmc" class="zui-input wh120" v-model="popContent.ypmc" @keyup="changeDown($event,'ypmc')" @input="change($event,'ypmc')">
                            <search-table :page="page" :message="searchCon" :selected="selSearch" :total="total"
                                          :them="them"
                                          :them_tran="them_tran" :current="dg.page" :rows="dg.rows"
                                          @click-one="checkedOneOut"
                                          @click-two="selectOne">
                            </search-table>
                        </div>
                        <div class="enter_tem1_item padd-r-10  padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">材料编码</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.ypbm">
                        </div>
                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">材料规格</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.ypgg">
                        </div>

                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">分装比例</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.fzbl">
                        </div>
                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">生产批号</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.scph">
                        </div>

                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">二级库房单位</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.yfdwmc">
                        </div>
                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">生产日期</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.scrq">
                        </div>
                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">有效期至</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.yxqz">
                        </div>

                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">材料零价</span>
                            <input class="_inputData zui-input wh120" disabled="disabled" v-model="popContent.yplj">
                        </div>
                        <div class="enter_tem1_item padd-r-10  padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">库存数量</span>
                            <input type="number" class="_inputData wh120 zui-input" disabled="disabled" type="number"
                                   v-model="popContent.kcsl">
                        </div>
                        <div class="enter_tem1_item padd-r-10 padd-b-10 flex-container flex-align-c">
                            <span class="padd-r-5">实存数量</span>
                            <input type="number" class="_inputData wh120 zui-input" @keyup.enter="add" type="number"
                                   v-model="popContent.scsl">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<div class="tab-card enter_pdlrxs">
    <div class="tab-card-header">
        <div class="tab-card-header-title font14">数据显示区</div>
    </div>
    <div class="tab-card-body padd-t-10">
        <div class="grid-box">
            <div class="toolMenu button1 flex-container">
                <button @click="del" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-scissors"></span>删除
                </button>
                <button @click="clear" class="tong-btn btn-parmary  paddr-r5"><span class="fa fa-plus"></span>清除
                </button>
            </div>


            <div class="zui-table-view hzList  padd-t-10">
                <div class="zui-table-header">
                    <table class="zui-table table-width50" >
                        <thead>
                        <tr>
                            <th ><div class="zui-table-cell cell-s"><span>材料编码</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>材料名称</span></div></th>
                            <th><div class="zui-table-cell cell-l"><span>规格</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>产品标准号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>批准文号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>实存数量</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>生产日期</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                            <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50" >
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkOne($index),getIndex($index)"
                            :tabindex="$index"
                            v-for="(item, $index) in jsonList"
                            @dblclick="edit($index)">
                            <td ><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                            <td ><div class="zui-table-cell cell-l title" v-text="item.ypmc"></div></td>
                            <td ><div class="zui-table-cell cell-l title" v-text="item.ypgg"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.cpbzh"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.pzwh"></div></td>
                            <td ><div class="zui-table-cell cell-s title">
                                <input v-model="item.scsl" class="zui-input" type="number"
                                       style="width: 70px;height: inherit;text-align: center;"/>
                            </div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.yfdwmc"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.yplj"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.ljje"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.scph"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.scrq"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.yxqz"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.kfdwmc"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.fzbl"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.cdmc"></div></td>
                            <td ><div class="zui-table-cell cell-s title" v-text="item.ghdwmc"></div></td>
                        </tr>
                        </tbody>
                    </table>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!--新增材料弹出框-->
    <div id="_addNew" class="enter_tem1 addNew" v-show="isShow" style="background: gainsboro; top:8%;left:25%;width: 600px;position: absolute;z-index: 1000">
       <div class="flex-container flex-wrap-w padd-t-10 padd-l-10 padd-r-10">
           <div class="enter_tem1_item padd-r-10  padd-b-10 flex-container flex-align-c">
               <span class="padd-r-5">材料名称</span>
               <input class="wh120 zui-input" v-model="addContent.ypmc" @keyup="changeDownAdd($event)" @input="changeAdd($event)">
               <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                             :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut" @click-two="selectOneAdd">
               </search-table>
           </div>
           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">材料编码</span>
               <input class="_addData wh120 zui-input" disabled="disabled" v-model="addContent.ypbm">
           </div>
           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">材料规格</span>
               <input class="_addData wh120 zui-input" disabled="disabled" v-model="addContent.ypgg">
           </div>

           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">分装比例</span>
               <input class="_addData wh120 zui-input" disabled="disabled" v-model="addContent.fzbl">
           </div>
           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">生产批号</span>
               <input class="_addData wh120 zui-input" @keydown="nextFocus($event)" v-model="addContent.scph">
           </div>
           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">批准文号</span>
               <input class="_addData wh120 zui-input" disabled="disabled" v-model="addContent.pzwh">
           </div>

           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">二级库房单位</span>
               <input class="_addData wh120 zui-input" disabled="disabled" v-model="addContent.yfdwmc">
           </div>
           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">生产日期</span>
               <input type="date" class="_addData wh120 zui-input" v-model="addContent.scrq">
           </div>
           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">有效期至</span>
               <input type="date" class="_addData wh120 zui-input" v-model="addContent.yxqz">
           </div>

           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">材料零价</span>
               <input type="number" class="_addData wh120 zui-input" v-model="addContent.yplj">
           </div>
           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">供货单位</span>
               <select-input class="wh120" @change-data="resultChange"  :child="ghdwList" :index="'dwmc'"
                             :index_val="'dwbm'" :val="addContent.ghdw" :search="true" :name="'addContent.ghdw'">
               </select-input>
           </div>

           <div class="enter_tem1_item padd-b-10 padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">实存数量</span>
               <input type="number" class="_addData wh120 zui-input" @keyup.enter="addNewYp" type="number" v-model="addContent.scsl">
           </div>
           <div class="enter_tem1_item padd-b-10  padd-r-10 flex-container flex-align-c">
               <span class="padd-r-5">产&emsp;&emsp;品<br/>标&ensp;准&ensp;号</span>
               <input class="_addData wh120 zui-input" disabled="disabled" v-model="addContent.cpbzh">
           </div>
       </div>
        <div class="padd-b-10 padd-l-10 flex-container">
            <button class="tong-btn btn-parmary  paddr-r5" @click="addNewYp"><span class="fa fa-plus"></span>保存</button>
            <button class="tong-btn btn-parmary  paddr-r5" @click="clear"><span class="fa fa-plus"></span>清除</button>
            <button class="tong-btn btn-parmary  paddr-r5" @click="close"><span class="fa fa-plus"></span>关闭</button>
        </div>
    </div>
<script type="text/javascript " src="pdlr.js "></script>