var qxksbm = '';
    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data:{
            //打印数据
            printData: {},
            json: {},
            zfShow:true,
            ShShow:false,

            dg: {
                page: 1,
                rows: 20,
                sort: "",
                order: "asc",
                parm: ""
            },
            isCheck: null,
            thdDetail: [], //退货单明细集合
            zhuangtai: {
                "0": "未审核",
                "1": "已审核",
                "2":"已作废",
                "3":"未通过",
            },

            tkd:null,
            thdList: [], //退货单集合
            param:{
                page: 1,
                rows: 10,
                parm: '',
                beginrq: null,
                endrq: null
            },
            modifyIndex:null,
            totlePage:0,
            mxShShow:true,
            ifClick:true,
            isShowpopL:false,
            isTabelShow:false,
            isShowkd:true,
            isShow:false,
            sxShow:false,
            keyWord:'',
            csParm: {},
            zdrq: getTodayDateTime(), //获取制单日期
            zdyxm:'',
            TjShow:true,
            thdContent: {
                ckfs: "02", //退货方式
                lyr: userId //操作人
            },
            jsonList:[],
            rkdList: [], //入库单集合
            KFList: [], //库房
            rkd: {}, //入库单对象
            num:0,
            isUpdate:0,
            popContent:{},
            jyinput: false, //禁用输入框
        },
        updated:function () {
            changeWin()
        },
        computed:{
            money:function () {
                var reducers = {
                    totalInEuros: function(state, item) {
                        return state.jjzj += item.ypjj * parseFloat(item.cksl);
                    },
                    totalInYen: function(state, item) {
                        return state.ljzj += item.yplj * parseFloat(item.cksl);
                    }
                };
                var manageReducers = function(reducers){
                    return function(state, item){
                        return Object.keys(reducers).reduce(function(nextState, key){
                            reducers[key](state, item);
                            return state;
                        },{})
                    }
                }
                var bigTotalPriceReducer = manageReducers(reducers);
                this.jsonList.reduce(bigTotalPriceReducer, this.json={
                    jjzj:0,
                    ljzj:0,
                });
            }
        },
        mounted: function () {
            this.getKFData();
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        },
        methods:{
            showTime:function (el,code){
                laydate.render({
                     show: true, //直接显示
                    elem: '#'+el,
                    type: 'datetime',
                    trigger: 'click',
                    theme: '#1ab394',
                    done: function (value, data) {
                        wrapper.param[code] = value;
                        wrapper.getData();
                    }
                });
            },
            //进入页面加载单据列表信息
            getData: function () {
                common.openloading('.zuiTableBody');
                //清空退货明细信息
                this.thdDetail = [];
                this.jsonList = [];
                this.tkd = null;
                //是否选择库房
                if(!wrapper.popContent.kfbm) {
                    malert("请先择库房!",'top','defeadted');
                    return;
                }

                Vue.set(this.param,'kfbm',wrapper.popContent.kfbm);

                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywThgl&types=queryThd&parm=' + JSON.stringify(this.param),
                    function(data) {
                        if(data.a == 0) {
                            wrapper.thdList = data.d.list;
                            wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
                            common.closeLoading()
                        } else {
                            malert(data.c,'top','defeadted');
                            common.closeLoading()
                        }
                    });
            },
            //入库审核
            passData: function () {
                if (!this.ifClick) return;
                this.ifClick = false;
                //出库单非空判断
                if(this.thdList.length == 0) {
                    return;
                }
                if(!this.popContent.kfbm){
                    malert('请选择库房','top','defeadted');
                    return;
                }
                var json = {
                    "ckdh": this.thdList[this.isCheck]['ckdh'],
                    "kfbm": this.popContent.kfbm,
                };

                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywThgl&types=shThd', JSON.stringify(json))
                    .then(function (data) {
                        if (data.body.a == "0") {
                            wrapper.ifClick = true;
                            var mes=confirm('是否打印退货单');
                            if(mes==true){
                                //帆软打印
                                wrapper.updateDycsAndPrint(json.kfbm, json.ckdh);
                            }else{

                            }
                            wrapper.isShow=false;
                            wrapper.isShowkd=true;
                            wrapper.isShow=false;
                            wrapper.isShowkd=true;
                            this.TjShow=false;
                            wrapper.zfShow=false;
                            wrapper.TjShow=false;
                            wrapper.getData();
                            malert("审核成功！",'top','success')
                        } else {
                            wrapper.ifClick = true;
                            wrapper.getData();
                            malert(data.body.c,'top','defeadted');
                        }
                    });
            },
            print:function (thdh, kfbm) {
                var thdh = this.thdList[this.isCheck]['ckdh'];
                var kfbm = wrapper.popContent.kfbm;

                this.updateDycsAndPrint(kfbm, thdh);
            },

            updateDycsAndPrint: function(kfbm, thdh){
                var parm = {
                    'ckdh' : thdh
                };
                //更新打印次数
                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=updateDycs' + '&parm=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            //调用帆软打印
                            console.log(kfbm+"|"+thdh);
                            var frpath = "";
                            if (window.top.J_tabLeft.obj.frprintver == "3"){
                                frpath = "%2F";
                            }else{
                                frpath = "/";
                            }
                            var reportlets ="[{reportlet: 'fpdy"+frpath+"yjkf"+frpath+"ykgl_thd.cpt',yljgbm:'"+jgbm+"',kfbm:'"+kfbm+"',ckdh:'"+thdh+"'}]";
                            console.log(reportlets);
                            if (FrPrint(reportlets,null)){
                                return ;
                            }
                        }else {
                            malert(data.c, 'top', 'defeadted');
                        }
                    });
            },

            //作废 2018/07/04 二次弹窗确认提示
            invalidData: function (num) {
                if(num!=null&&num!=undefined){
                    this.isCheck=num;
                }
                if (!this.popContent.kfbm) {
                    malert("请选择库房!",'top','defeadted');
                    return;
                }
                if (common.openConfirm("确认作废该条信息吗？", function () {

                    var json = {
                        "ckdh": wrapper.thdList[wrapper.isCheck]['ckdh'],
                        "kfbm": wrapper.popContent.kfbm
                    };

                    this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywThgl&types=zfThd', JSON.stringify(json)).then(function (data) {
                        if (data.body.a == "0") {
                            wrapper.getData();
                            malert("作废成功！",'top','success');
                            wrapper.cancel();
                        } else {
                            malert(data.body.c,'top','defeadted');
                        }
                    });
                })) {
                    return false;
                }
            },
            //编辑
            editIndex:function(index){
                this.isCheck = index;
                wrapper.isShowkd=false;
                wrapper.isShow=true;
                wrapper.isShowpopL=true;
                this.isShow=true;
                this.isShowkd=false;
                this.TjShow=true;
                this.zfShow=true;
                this.ShShow=false;
                wrapper.sxShow=false;
                wrapper.TjShow=false;
                this.mxShShow=true;
                this.ShShow=false;
                wrapper.jyinput=false;
                wrapper.zdyxm=this.thdList[index].zdyxm;
                wrapper.zdrq=this.fDate(this.thdList[index].zdrq,'date');
                wrapper.popContent.kfbm=this.thdList[index].kfbm;
                wrapper.popContent.bzms=this.thdList[index].bzms;
                this.tkd = this.thdList[index];
                this.getMx(this.tkd.ckdh);
            },

            //选中单据信息加载出相对应的单据内容明细
            showDetail: function (index) {
                this.isCheck = index;
                wrapper.isShow=true;
                wrapper.isShowkd=false;
                wrapper.isShowpopL=false;
                this.isShow=true;
                this.isShowkd=false;
                this.TjShow=false;
                this.zfShow=false;
                wrapper.TjShow=false;
                wrapper.sxShow=true;
                this.mxShShow=false;
                wrapper.zdyxm=this.thdList[index].zdyxm;
                wrapper.zdrq=this.fDate(this.thdList[index].zdrq,'date');
                wrapper.popContent.kfbm=this.thdList[index].kfbm;
                wrapper.popContent.bzms=this.thdList[index].bzms;
                if(this.thdList[index].shzfbz=='1' || this.thdList[index].shzfbz=='2'){
                    this.mxShShow=false;
                    wrapper.jyinput=true;
                    this.ShShow=false;
                }else{
                    this.mxShShow=true;
                    wrapper.jyinput=false;
                    this.ShShow=true;
                }
                this.getMx(this.thdList[index]['ckdh']);
            },

            getMx:function(ckdh){
                this.jsonList = [];
                var parm = {
                    "ckdh": ckdh
                };
                Vue.set(this.dg,'parm',ckdh);

                $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywThgl&types=queryThglMxByThdh&dg=' + JSON.stringify(this.dg) + '&parm=' + JSON.stringify(parm),
                    function(data) {
                        wrapper.jsonList = data.d;
                    });
            },
            //库存
            getKc:function(ypbm){
                var json = {
                    "ypbm": ypbm,
                    "kfbm": wrapper.param.kfbm
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp' + '&json=' + JSON.stringify(json), function (data) {
                    if(data.a == 0){
                        if(data.a == 0){
                            if(data.d != null && data.d.list != null){
                                //可用库存，实际库存
                                Vue.set(wap.popContent,'kcsl',data.d.list[0].kcsl);
                                Vue.set(wap.popContent,'kykc',data.d.list[0].sjkc);
                            }
                        }
                    }
                });
            },
            //提交所有材料
            submitAll: function() {
                //是否禁止提交
                if(this.isSubmited) {
                    malert("数据提交中，请稍候！",'top','defeadted');
                    return;
                }
                //判断提交数据正确性
                if(this.jsonList.length <= 0) {
                    malert("没有可提交的数据",'top','defeadted');
                    return;
                }
                //判断是否选择库房
                if(wrapper.popContent.kfbm == null) {
                    malert("库房为必选项",'top','defeadted');
                    return ;
                }
                Vue.set(wap.thdContent,'kfbm',wrapper.popContent.kfbm);
                Vue.set(wap.thdContent,'bzms',wrapper.popContent.bzms);
                //是否禁止提交
                this.isSubmited = true;
                var thd = null;
                if(wrapper.tkd){
                    thd = wrapper.tkd;
                }else{
                    thd = wap.thdContent;
                }
                //准备数据，包括退货单对象和退货单明细对象
                var json = {
                    "list": {
                        "thd": thd,
                        "thdmx": this.jsonList
                    }
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywThgl&types=modify',
                    JSON.stringify(json))
                    .then(function(data) {
                        if(data.body.a == 0) {
                            malert("数据更新成功",'top','success');
                            this.jsonList = [];
                            this.isShow=false;
                            this.isShowkd=true;
                            wrapper.isShowkd=true;
                            wrapper.isShow=false;
                            wrapper.isShowpopL=false;
                            wrapper.getData();
                        } else {
                            malert("数据提交失败"+ data.body.c,'top','defeadted');
                        }
                        //是否禁止提交
                        wrapper.isSubmited = false;
                    }, function(error) {
                        //是否禁止提交
                        wrapper.isSubmited = false;
                    });
            },
            //删除
            scmx: function(index) {
                if (common.openConfirm("确认删除该条信息吗？", function () {
                    wrapper.jsonList.splice(index,1);
                })) {
                    return false;
                }
                // this.jsonList.splice(index,1);
            },
            //双击修改
            edit: function(num) {
                wrapper.isUpdate = 1;
                this.modifyIndex = num;
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
                //库存查询
                this.getKc(wap.popContent.ypbm);

                //时间格式化
                Vue.set(wap.popContent,'scrq',formatTime(wap.popContent.scrq, 'date'));
                Vue.set(wap.popContent,'yxqz',formatTime(wap.popContent.yxqz, 'date'));
                wap.title = "编辑材料";
                wap.open();
            },

            //取消
            cancel:function () {
                this.isShow=false;
                this.isShowkd=true;
                this.sxShow=false;
                this.isShowpopL=false;
                this.isShow=false;
                this.isShowkd=true;
                this.tkd = {};
            },
            kdFun:function (index) {
                this.num=index;
                setTimeout(function () {
                    wap.$refs.autofocus.focus();
                },40);
                switch (wrapper.num){
                    case 0:
                        this.jsonList = [];
                        this.isShowkd=false;
                        this.isShow=true;
                        this.isShowpopL=true;
                        this.isShow=true;
                        this.isShowkd=false;
                        this.TjShow=true;
                        this.zfShow=false;
                        this.ShShow=false;
                        wrapper.sxShow=false;
                        this.mxShShow=true;
                        $('#bzms').attr('disabled',false);
                        var reg = /^[\'\"]+|[\'\"]+$/g;
                        wrapper.zdyxm=sessionStorage.getItem("userName"+userId).replace(reg,'');
                        wrapper.isUpdate = 0;
                        break;
                    case 1:
                        wap.open();
                        wap.title='添加材料';
                        wap.popContent={};
                        wrapper.isUpdate = 0;
                        break;
                }

            },
            getKFData:function () {
                //初始化页面记载库房
                $.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxkf&parm={"ylbm":"N040100011003"}',
                    function (data) {
                        if (data.a == 0) {
                            wrapper.KFList = data.d;
                            if (data.d.length > 0) {
                                qxksbm = data.d[0].ksbm;
                                Vue.set(wrapper.popContent,'kfbm',data.d[0].kfbm);
                                wap.getCsqx(); //加载完库房再次加载参数权限
                                wrapper.getData()
                            }
                        } else {
                            malert("一级库房获取失败",'top','defeadted');
                        }
                    });
            },

            resultRydjChanges: function (val) {
                        Vue.set(this.param, 'kfbm', val[0]);
                        Vue.set(this.param, 'kfmc', val[4]);
                        for(var i = 0 ; i < wrapper.KFList.length ; i++){
                            if(wrapper.KFList[i].kfbm == val[0]){
                                qxksbm = wrapper.KFList[i].ksbm;
                                break;
                            }
                        }
                        wap.getCsqx();
            },

        }
    });

    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        components: {
            'search-table': searchTable
        },
        data:{
            isShowpopL:false,
            iShow:false,
            isTabelShow:false,
            flag:false,
            jsShow:false,
            ghdwList:[],
            popContents:{},
            title:'',
            KSList: [], //领用科室
            KFList: [], //库房
            ypcdList: [], //材料产地
            popContent: {}, //退货单明细对象
            csParm: {},
            zdrq: getTodayDateTime(), //获取制单日期
            dg: {
                page: 1,
                rows: 50,
                sort: "",
                order: "asc",
                parm: ""
            },
            them_tran: {},
            them: {
                '生产批号': 'scph',
                '材料编号': 'ypbm',
                '材料名称': 'ypmc',
                '产地': 'cdmc',
                '库存数量': 'kcsl',
                '有效期至': 'yxqz',
                '规格': 'ypgg',
                '分装比例': 'fzbl',
                '进价': 'ypjj',
                '零价': 'yplj',
                '库房单位': 'kfdwmc',
                '二级库房单位': 'yfdwmc',
                '效期': 'yxqz',
                '材料剂型': 'jxmc'
            },
            thdContent: {
                ckfs: "02", //退货方式
                lyr: userId //操作人
            }, //退货单对象
        },
        mounted:function(){
            // this.getJzData();
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg');
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg');
                $(".side-form").removeClass('ng-hide');
            },

            //获取参数权限
            getCsqx: function () {
                //获取参数权限
                if (qxksbm == undefined && wrapper.rkd.kfbm == undefined) {
                    return;
                }

                var parm = {
                    "ksbm": qxksbm,
                    "ylbm": "N040100011003"
                };
                $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                    if (json.a == 0) {
                        if (json.d.length > 0) {
                            //退货开单打印单据，1-开单保存后打印单据，0-开单保存后不打印单据
                            for(var i = 0; i < json.d.length; i++) {
                                if(json.d[i].csqxbm == 'N04010001100301') {
                                    if(json.d[i].csz == '0') {
                                        wap.csParm.kddy = true;
                                    } else {
                                        wap.csParm.kddy = false;
                                    }
                                }
                                //退货开单审核，0-有开单审核  1-有开单 2-有审核
                                if(json.d[i].csqxbm == 'N04010001100302') {
                                    if(json.d[i].csz == '0') {
                                        wap.csParm.kd = true;
                                        wap.csParm.sh = true;
                                    } else if(json.d[i].csz == '1') {
                                        wap.csParm.kd = true;
                                        wap.csParm.sh = false;
                                    } else {
                                        wap.csParm.sh = true;
                                        wap.csParm.kd = false;
                                    }
                                }
                                //退货单作废权限，1-有 0-无
                                if(json.d[i].csqxbm == 'N04010001100303') {
                                    if(json.d[i].csz == '0') {
                                        wap.csParm.zf = false;
                                    } else {
                                        wap.csParm.zf = true;
                                    }
                                }
                            }
                        }
                        //退货开单切换库房导致页面错乱
                    } else {
                        malert(json.c+"参数权限获取失败",'top','defeadted');
                    }
                });
            },
            //
            //材料名称下拉table检索数据
            changeDown: function (event, type) {
                if(!this.searchCon[this.selSearch])return
                this.keyCodeFunction(event, 'popContent', 'searchCon');
                //选中之后的回调操作
                if(event.code == 'Enter' || event.keyCode == 13 || event.keyCode=='NumpadEnter') {
                    this.nextFocus(event)
                    this.selSearch=-1;
                    $(".selectGroup").hide();
                } else if(type == "cksl" && event.keyCode == 13) {
                    this.addData();
                }
            },
            //当输入值后才触发
            change: function (add,type,value) {
                if(!add)  this.dg.page=1;
                if(!wrapper.param.kfbm) {
                    malert("请先选择库房!",'top','defeadted');
                    return;
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.dg.parm = value;
                var bean = {
                    "kfbm": wrapper.param.kfbm,
                };
                //分页参数
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ykyp&dg=' + JSON.stringify(wap.dg) + '&json=' + JSON.stringify(bean),
                    function (data) {
                        for(var i = 0; i < data.d.list.length; i++) {
                            data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                            data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                            //判断可用库存数量
                            data.d.list[i]['kykc'] = data.d.list[i]['kcsl'] - (data.d.list[i]['wshcks'] == undefined ? 0 : data.d.list[i]['wshcks']);
                        }
                        if(add){
                            wap.searchCon=wap.searchCon.concat(data.d.list);
                        }else {
                            wap.searchCon = data.d.list;
                        }
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if (!add && data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        } else {
                            $(".selectGroup").show();
                        }
                    });
            },

            //双击选中下拉table
            selectOne: function (item) {
                //查询下页
                if (item == null) {
                    this.dg.page++;
                    this.change(true,)
                }else {
                    this.popContent = item;
                    this.selSearch=-1;
                    $("#thsl").focus();
                    $(".selectGroup").hide();
                }
            },

            //添加入库信息
            addData: function () {

                if(!this.popContent.cksl){
                	malert("请填写数量！",'top','defeadted');
                    return false;
                }

               //判断可用库存数量是否大于0（同时判断未审核库存数）
                if(this.popContent.kykc <= 0 || ((this.popContent.kykc - this.popContent.cksl) < 0 )) {
                    var msg = "可用库存不足" + (this.popContent['kykc'] == 0 ? "!" : "，请检查未审核的出库！");
                    malert(msg,'top','defeadted');
                    return false;
                }

                if(wrapper.isUpdate == 0){
                	//添加
                	//相同材料不能重复入库
                    var ypbm = this.popContent.ypbm;
                    for (var i = 0; i < wrapper.jsonList.length; i++) {
                        if (wrapper.jsonList[i].ypbm == ypbm) {
                            malert("材料【" + this.popContent.ypmc + "】已存在,请修改已有数据!",'top','defeadted');
                            return;
                        }
                        //获取退货单明细其他属性
                        this.popContent['zdy'] = userId;
                    }
                    //将数据加入列表
                    wrapper.jsonList.push(this.popContent);

                    wap.popContent = {};
                    $("#ypmc").focus();
                }else{
                	//修改  -+加接口
                	var ckdh = wrapper.jsonList[wrapper.modifyIndex].ckdh;
                    wrapper.$set(wrapper.jsonList,wrapper.modifyIndex,wap.popContent);
                	wap.popContent = {};
                	this.closes();
                }

            },

        }


    });

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol) {
        $(".selectGroup").hide();
    }

});
