/**
 * Created by mash on 2017/11/24.
 */
var toolMenu = new Vue({
    el: '.toolMenu',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        index2: 0,
        jlxqContent:yzclLeft.HszbrItem,
    },
    methods: {
        // 每日小结
        mrxj:function(){
            Vue.set(jlxq.jlContent, 'bqgcjcs', '');
            // 筛选当前日期 24小时内的数据
            console.log(jlxq.jlxx_list)
            console.log(jlxq.jlContent.jlsj)
            var date = new Date(jlxq.jlContent.jlsj);
            var time1 = date.getTime();

            var clList = [];
            var rlList = [];
            for (var j = 0; j < jlxq.jlxx_list.length; j++) {
                var obj = jlxq.jlxx_list[j];
                var sjc = time1 - obj.jlsj;

                if(obj.clMc && obj.clSl && sjc < 24*60*60*1000 && sjc > 0){
                    var clMcArr = obj.clMc.split(',');
                    var clSlArr = obj.clSl.split(',');
                    for (var i = 0; i < clMcArr.length; i++) {
                        clList.push({
                            clMcItem:clMcArr[i] != 'undefined' ? clMcArr[i] : '',
                            clSlItem:clSlArr[i]!= 'undefined' ? clSlArr[i] : '',
                        });
                    }
                }

                if(obj.rlMc && obj.rlSl  && sjc < 24*60*60*1000 && sjc > 0){
                    var rlMcArr = obj.rlMc.split(',');
                    var rlSlArr = obj.rlSl.split(',');
                    for (var i = 0; i < rlMcArr.length; i++) {
                        rlList.push({
                            rlMcItem:rlMcArr[i],
                            rlSlItem:rlSlArr[i]
                        });
                    }
                }
            }
            var msg = '计24小时：';
            var zcl = 0,zrl=0;
            var clMsg = '',clMsg1='';
            var cl = clList.reduce((r, x) => ((r[x.clMcItem] || (r[x.clMcItem] = [])).push(x), r), {});
            var rl = rlList.reduce((r, x) => ((r[x.rlMcItem] || (r[x.rlMcItem] = [])).push(x), r), {});
            for(var arr in cl){
                var jg=cl[arr].reduce((p,e)=>p+(parseInt(e.clSlItem)),0);
                if(!isNaN(jg)){
                    clMsg += (arr+jg+'ml,');
                    zcl += jg;
                }
            }
            for(var arr1 in rl){
                var jg1=rl[arr1].reduce((p,e)=>p+(parseInt(e.rlSlItem)),0);
                if(!isNaN(jg1)){
                    clMsg1 += (arr1+jg1+'ml,');
                    zrl += jg1;
                }
            }
            msg += (';总出量：' + zcl + 'ml,' +clMsg+';总入量:'+ zrl + 'ml,' +clMsg1);


            Vue.set(jlxq.jlContent, 'bqgcjcs', msg);

            malert("计算成功！", 'right', 'success');

        },
        Wf_Click: function (index) {
            this.index2 = index;
            jlxq.jlContent = jlxq.jlxx_list[index];
        },
        openCpt:function () {
            toolMenu.topNewPage('危重护理记录预览', window.top.J_tabLeft.obj.FrUrl+'/FR/ReportServer?reportlet=fpdy%2Fhsz%2Fhsz_wzhljl.cpt&yljgbm=' +
                toolMenu.getQueryVariable('yljgbm') + '&czybm=' + toolMenu.getQueryVariable('czybm') + '&ksbm=' + ksbm+'&zyh='+jlxq.IsZyh)
        },
        print: function () {
            var sytPrint = '', sytSize = '';
            str = "[{reportlet: 'fpdy%2Fhsz%2Fhsz_wzhljl.cpt',zyh:'" + jlxq.IsZyh + "'}]";
            window.top.J_tabLeft.csqxparm.csbm = "N010024010";
            console.log(window.top.J_tabLeft.csqxparm);
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" + JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0) {
                    console.log(json.d);
                    if (json.d != null && json.d != undefined && json.d.length > 0) {
                        sytPrint = json.d[0].csz;
                        sytSize = json.d[0].cszmc;
                    }
                    if (!FrPrint(str, sytPrint, sytSize)) {
                        window.print();
                    }
                } else {
                    window.print();
                }
            })
        },
        getData: function () {
            jlxq.getJlData();
        },

        addData: function () {
            jlxq.jlContent = {
                zdmc:jlxq.jlxx_list[0].zdmc,
                rybm:userId,
                jlsj:this.fDate(new Date(),'datetime')
            };
        },
        getTwData: function () {
            jlxq.getTwData()
        },
        edit: function () {
            if (jlxq.jlContent.yjbz == true) {
                jlxq.jlContent.yjbz = '1'
            } else {
                jlxq.jlContent.yjbz = '0'
            }
            jlxq.jlContent.zyh = jlxq.IsZyh;
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=insert&parm=' + encodeURIComponent(JSON.stringify(jlxq.jlContent)), function (json) {
                if (json.a == '0') {
                    malert("保存成功！", 'top', 'success');
                    jlxq.jlContent = {};
                    jlxq.getJlData();
                } else {
                    malert("保存失败！", 'top', 'defeadted');
                }
            });
        },
        pledit: function () {
            if (jlxq.jlContent.yjbz == true) {
                jlxq.jlContent.yjbz = '1'
            } else {
                jlxq.jlContent.yjbz = '0'
            }
            jlxq.jlContent.zyh = jlxq.IsZyh;
            jlxq.jlContent.gxbz='1';
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=insert&parm=' + encodeURIComponent(JSON.stringify(jlxq.jlContent)), function (json) {
                if (json.a == '0') {
                    malert("更新成功！", 'top', 'success');
                    jlxq.jlContent = {};
                    jlxq.getJlData();
                } else {
                    malert("更新失败！", 'top', 'defeadted');
                }
            });
        },

        remove: function () {
                        if (common.openConfirm("确认删除该条信息吗？", function () {
                $.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=delete&parm=' + encodeURIComponent(JSON.stringify(jlxq.jlContent)), function (json) {
                    if (json.a == '0') {
                        malert("删除成功！", 'top', 'success');
                        jlxq.jlContent = {};
                        jlxq.getJlData();
                    } else {
                        malert("删除失败！", 'top', 'defeadted');
                    }
                });
            })) {
                return false;
            }
        },
    }
});


var jlxq = new Vue({
    el: '#jlxq',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        jlxx_list: [],
        hsList: [],
        total: 0,
        json: {},
        sd_tran: {
            '1': '2',
            '2': '6',
            '3': '10',
            '4': '14',
            '5': '18',
            '6': '22',
        },
        twList: [],
        twList: [],
        twqtObj: {},
        rlList: [{}],
        clList: [{}],
        rlxxList: [],
        clxxList: [],
        IsZyh: yzclLeft.HszbrItem.zyh,
        jlContent: {
            jlsj:getTodayDateTime(),
            rybm:userId
        },
    },

    created: function () {
        this.getJlData();
        this.readyData({"hsbz": "1"}, "rybm", "hsList"); //护士
    },
    mounted:function(){
        Mask.newMask(this.MaskOptions('jlsj'));
        this.changeClsdData();
    },
    updated:function(){
        changeWin()
    },
    watch: {
        "jlContent.jlsj": function (newVal) {
            this.changeClsdData();
            if (!this.jlContent.id) {
                // this.getTwData();
                //this.getHljl();
            }
        }
    },
    methods: {
        // 获取体温数据
        getTwData: function () {
                        var parm = {
                zyh: toolMenu.jlxqContent.zyh,
                ryksbm: toolMenu.jlxqContent.ryks,
                clrq: this.fDate(jlxq.jlContent.jlsj, 'date')
                //yebh: yebh,
            };
            $.ajaxSettings.async = false;
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryByOneBr&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        jlxq.twList = json.d.list;
                    } else {
                        malert(json.c, 'right', 'defeadted');
                    }
                });
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=select&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        //给体温单其他记录对象赋值
                        if (json.d) {
                            jlxq.twqtObj = json.d;
                        } else {
                            jlxq.twqtObj = {};
                        }
                    } else {
                        malert(json.c, 'right', 'defeadted');
                    }
                });
            setTimeout(function () {
                jlxq.getTwObj();
            }, 150)

        },
        getTwObj: function () {
                        if (!this.jlContent.id) {
                for (var i = 0; i < jlxq.twList.length; i++) {
                    if (jlxq.twList[i].clsd == jlxq.jlContent.clsd) {
                        jlxq.jlContent.tw = jlxq.twList[i].tw;
                        jlxq.jlContent.mb = jlxq.twList[i].mb;
                        jlxq.jlContent.hx = jlxq.twList[i].fx;
                        jlxq.jlContent.xl = jlxq.twList[i].xt;
                        jlxq.jlContent.xybhd = jlxq.twList[i].ybhd;
                    }
                }
                if (parseInt(this.sd_tran[this.jlContent.clsd]) >= 2 && parseInt(this.sd_tran[this.jlContent.clsd]) <= 10) {// 上午
                    jlxq.jlContent.xyL = jlxq.twqtObj.swSsy;
                    jlxq.jlContent.xyR = jlxq.twqtObj.swSzy;
                } else {
                    jlxq.jlContent.xyL = jlxq.twqtObj.xwSsy;
                    jlxq.jlContent.xyR = jlxq.twqtObj.xwSzy;
                }
                if (jlxq.twqtObj.dbl || jlxq.twqtObj.dbs || jlxq.twqtObj.xbl || jlxq.twqtObj.xbs) {
                    jlxq.clList = [];
                }
                if (jlxq.twqtObj.dbl || jlxq.twqtObj.dbs) { // 大便
                    jlxq.clList.push({
                        clMcItem: '大便',
                        clSlItem: jlxq.twqtObj.dbl,
                        clYsxzItem: jlxq.twqtObj.dbs
                    });
                }

                if (jlxq.twqtObj.xbl || jlxq.twqtObj.xbs) { // 小便
                    jlxq.clList.push({
                        clMcItem: '小便',
                        clSlItem: jlxq.twqtObj.xbl,
                        clYsxzItem: jlxq.twqtObj.xbs
                    });
                }

                for (var i = 0; i < jlxq.jlxx_list.length; i++) {

                    // 当天如果在某次评估中已经录入大便小便出量 则本天不再拉取数据
                    if (this.jlContent.jlsj.substr(0, 10) == this.fDate(jlxq.jlxx_list[i].jlsj, 'date') &&
                        jlxq.jlxx_list[i].clMc != null) {
                        if (jlxq.jlxx_list[i].clMc.indexOf("大便") != -1
                            || jlxq.jlxx_list[i].clMc.indexOf("小便") != -1) {
                            jlxq.clList = [{
                                clMcItem: '',
                                clSlItem: '',
                                clYsxzItem: ''
                            }];
                        }
                    }


                    // 判断护理记录是否重复
                    if (jlxq.jlxx_list[i].bqgcjcs == jlxq.jlContent.bqgcjcs) {
                        jlxq.jlContent.bqgcjcs = '';
                    }

                }
            }
            this.$forceUpdate();
        },
        changeClsdData: function () {
            // 获取当前时间
            var time = this.fDate(this.jlContent.jlsj, 'time').split(':')[0];
            console.log(time)
            var clsd = '';
            var type = null;
            for (var key in this.sd_tran) {
                var value = this.sd_tran[key];
                var i = parseInt(time) - parseInt(value);
                if (type == null) {
                    clsd = key;
                    type = i;
                } else {
                    if (i >= 0 && Math.abs(i) < Math.abs(type)) {
                        clsd = key;
                        type = i;
                    }
                }
            }
            this.jlContent.clsd = clsd;
        },
        resultChangeSd: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.$forceUpdate();
            // this.getTwData();
        },
        //共用下拉框请求后台
        readyData: function (req, types, listName) {
            this.$http.get("/actionDispatcher.do",{params:{reqUrl:'GetDropDown',types:types,json:JSON.stringify(req)}}).then(function (json, status, xhr) {
                if (json.body.a == 0) {
                    jlxq[listName] = json.body.d.list;
                    jlxq.$forceUpdate()
                } else {
                    malert(types + "查询失败", 'top', 'defeadted');
                }
            }, function (json) {
            })
        },
        yjbz: function () {
            this.jlContent.yjbz = !this.jlContent.yjbz ? 1 : 0
            this.$forceUpdate()
        },
        showDate: function (elm, key) {
            this._laydate = {
                elem: '#' + elm
                , show: true //直接显示
                , type: 'datetime'
                , theme: '#1ab394',
                done: function (value, data) {
                    jlxq.jlContent[key] = value
                }
            }
            laydate.render(this._laydate)//初始化时间插件
        },
        Wf_Click: function (index) {
            this.index2 = index;
            this.jlContent = this.jlxx_list[index];
        },
        getJlData: function () {
            var parm = {
                ksrq: this.json.dateBegin,
                jsrq: this.json.dateEnd,
                zyh: this.IsZyh
            };
            this.$http.get('/actionDispatcher.do',{params:{reqUrl:'HszHlywWzhljl',types:'queryAll',parm:JSON.stringify(parm)}}).then( function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    jlxq.jlxx_list = json.body.d.list;
                }else{
                    malert(json.body.c, 'top', 'defeadted');
                }
            });
        }
    }
})

yzclLeft.$watch('HszbrItem', function (newVal,oldVal) {
    console.log('wzhljl')
    if(newVal.zyh != oldVal.zyh && this.index==4){
        toolMenu.jlxqContent = newVal
        jlxq.jlContent = newVal
        jlxq.IsZyh = newVal.zyh
        jlxq.getJlData()
    }
})
