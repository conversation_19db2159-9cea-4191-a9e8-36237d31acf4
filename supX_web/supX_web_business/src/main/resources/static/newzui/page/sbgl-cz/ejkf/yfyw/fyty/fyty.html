<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>科室领药</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <script type="text/javascript" src="jquery.jqprint-0.3.js"></script>
    <link rel="stylesheet" href="pr.css" media="print"/>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="fyty.css" rel="stylesheet"/>
</head>
<style>

</style>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="jyxm_icon">
    <div class="panel box-fixed">
        <div class="tong-top">
            <!-- <button class="tong-btn btn-parmary  icon-xz1 paddr-r5" @click="FaYaoEdit(num)">发药</button> -->
            <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="sx">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">药房</label>
                    <div class="zui-input-inline wh122 margin-l-7">
                        <select-input @change-data="resultRydjChange"
                                      :child="ksList" :index="'yfmc'" :index_val="'yfbm'" :val="barContent.yfbm"
                                      :name="'barContent.yfbm'" :search="true" :index_mc="'yfmc'" :ksbm="barContent.ksbm" :cflx="barContent.cflx">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">科室</label>
                    <div class="zui-input-inline wh122 margin-f-l20">
                        <select-input @change-data="resultRydjChange"
                                      :child="zyksList" :index="'ksmc'" :index_val="'ksbm'" :val="barContent.ryks"
                                      :name="'barContent.ryks'" :search="true" :index_mc="'ryksmc'" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline   margin-f-l5 flex-container flex-align-c">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请开始日期" id="timeVal"  @keydown="searchHc"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue" @keydown="searchHc()"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view"  z-height="full" style="margin-top: 108px; padding: 0 10px;border: none">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>
                        <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>摆药单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>摆药日期</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>摆药药房</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>关联申请号单</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  :class="[{'table-hovers':isChecked[$index]}]" @click="checkOneMx($index,item),checkSelect([$index,'one','jsonList'],$event)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.bydh">西药收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.fysj,'date')">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ksmc">药品收入</div></td>
                    <td><div class="zui-table-cell cell-s"></div></td>
                    <!--<td><div class="zui-table-cell" v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-wtg':item.shzfbz=='1' ? 'color-ysh':item.shzfbz=='2' ? 'color-dlr':item.shzfbz=='3' ? 'color-wc': '' ">-->

                        <!--</div>-->
                    <!--</td>-->
                    <td>
                        <div class="zui-table-cell cell-s">已摆药</div>
                    </td>
                    <td class="cell-l"><div class="zui-table-cell cell-l">
                        <!--<i class="icon-width icon-dy-h icon-dy" v-show="item.shzfbz!=3"></i>-->
                        <!--<i class="icon-width icon-fy-h icon-fy" v-if="item.shzfbz==1" @click="fayao(item)"></i>-->
                        <!--<i class="icon-width icon-ty-h icon-ty" v-if="'item.shzfbz==2"></i>-->
                        <span class="flex-center">
                                <em class="width30" >
                                  <i class="icon-width icon-fy-h" @click="fayao(item.bydh)" data-title="发药"></i>
                                </em>
                        </span>
                    </div>

                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<div class="side-form ng-hide pop-850" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="slgl-by">
        <i style="width: 33%;text-indent:15px;"><em>摆药单号:</em><em v-text="times.bydh"></em></i>
        <i style="width: 25%"><em>发药时间:</em><em v-text="fDate(times.fysj,'date')"></em></i>
        <i><em>领药科室:</em><em v-text="times.ksmc"></em></i>
        <i><em>摆药药房:</em><em v-text="times.fyrxm"></em></i>
    </div>
    <div class="ksys-side" style="padding-top: 10px;">
        <div class="fyxm-tab">
                    <div><span :class="{'actives':num==0}" @click="tabBg(0)">明细发药单</span></div>
                    <div><span :class="{'actives':num==1}" @click="tabBg(1)">汇总发药单</span></div>
                    <div class="fyty-fr" style="margin-right: -33px;">
                        <i class="color-dsh fyxm-show" style="width: 65px;"><em>用法选择:</em></i>
                        <select-input class="wh182" @change-data="resultRydjChanges"
                                      :child="AllList" :index="'yfxzmc'" :index_val="'fylx'" :val="barContent.fylx"
                                      :name="'barContent.fylx'" :search="true" :index_mc="'yfxzmc'">
                        </select-input>
                    </div>

                 </div>

                 <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==0}" id="zhcx">
                     <h2 class="h2title" v-text="prTitle"></h2>
                     <ul class="cfhj-top all-height">
                         <li>
                         <i>序号</i>
                         <i>住院号</i>
                         <i>病员姓名</i>
                         <i class="text-left">药品名称</i>
                         <i>规格</i>
                         <i>数量</i>
                         <i>单位</i>
                         <i>医师</i>
                         <i>零价</i>
                         </li>
                     </ul>
                     <ul class=" cfhj-content all-height">
                         <li v-for="(item,$index) in mxList">
                             <i v-text="$index+1">1</i>
                             <i class="relative">
                                 <em class="title title-width" v-text="item.zyh" ></em>
                             </i>
                             <i v-text="item.brxm"></i>
                             <i class="relative text-left">
                                 <em class="title " :data-title="item.ryypmc" v-text="item.ryypmc" ></em>
                             </i>
                             <i class="relative">
                                 <em class="title title-width" v-text="item.ypgg" ></em>
                             </i>
                             <i v-text="fDec(item.fysl,2)">频次</i>
                             <i v-text="item.yfdwmc">单位</i>
                             <i v-text="item.ysxm">单位</i>
                             <i v-text="fDec(item.yplj,2)"></i>
                         </li>
                     </ul>
                 </div>
                 <div class="fyxm-size  fyxm-hide" :class="{'fyxm-show':num==1}" id="zhcx1">
                     <h2 class="h2title" v-text="prTitle"></h2>
                     <ul class="cfhj-top all-height mx-top">
                         <li>
                             <i>序号</i>
                             <i class="text-left">药品名称</i>
                             <i>剂型</i>
                             <i>用法</i>
                             <i>规格</i>
                             <i>单位</i>
                             <i>零价</i>
                             <i>发药数量</i>
                             <i>金额</i>
                         </li>
                     </ul>
                     <ul class="cfhj-content all-height mx-top">
                         <li v-for="(item,$index) in mxList">
                             <i v-text="$index+1">1</i>
                             <i class="relative text-left">
                                 <em class="title " v-text="item.ryypmc" ></em>
                             </i>
                             <i v-text="item.jxmc">剂量</i>
                             <i v-text="item.yyffmc">用法</i>
                             <i class="relative">
                                 <em class="title title-width" v-text="item.ypgg" ></em>
                             </i>
                             <i v-text="item.yfdwmc">规格</i>
                             <i v-text="item.yplj">单位</i>
                             <i v-text="fDec(item.fysl,2)">单位</i>
                             <i v-text="fDec(item.fysl * item.yplj,2)">单位</i>
                         </li>
                     </ul>
                 </div>

    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-parmary-f2a xmzb-db" @click="print">打印</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="fyty.js"></script>

</body>

</html>
