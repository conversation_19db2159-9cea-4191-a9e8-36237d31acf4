<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>执行计划</title>
<link href="childpage/zxjh.css" rel="stylesheet" type="text/css"/>
<link rel="stylesheet" href="/pub/css/print.css" media="print"/>
<div class="flex-container  yzgl" >
    <div id="yzglContent" class="  yzgl" :class="num == 0 ?'flex-container': ''" v-cloak>
        <div class="  yzgl " :class="num == 0 ?'flex-container flex-dir-c': ''">
            <div class=" printHide  relative  flex-container flex-wrap-w  margin-r-10 ">
                <div class=" flex-container margin-r-10 flex-align-c" >
                    <span class="ft-14 whiteSpace padd-r-5">执行</br>时间</span>
                    <div class="flex-container flex-align-c">
                        <input class="zui-input todate width162 " data-select="no" v-model="beginrq" placeholder="开始时间" id="beginrq"/>
                        <span class="padd-r-5 ft-14">~</span>
                        <input class="zui-input todate width162 " data-select="no" v-model="endrq" placeholder="结束时间" id="endrq"/>
                    </div>

                    <button class="root-btn btn-parmary" @click="today()">今天</button>
            		<button class="root-btn btn-parmary-71" @click="tomorrow()">明天</button>
                </div>
                <div  class="flex-container flex-align-c">
                    <span class="ft-14 whiteSpace padd-r-5 color-wtg">医嘱</br>类型:</span>
                   <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'药品'" :val="'ypbz0'"  :new-value="ypbz0"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'诊疗'" :val="'ypbz1'"  :new-value="ypbz1"></vue-checkbox>

                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'其他'" :val="'w'"  :new-value="w"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'检查'" :val="'jc'"  :new-value="jc"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'检验'" :val="'jy'"  :new-value="jy"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'治疗'" :val="'zl'"  :new-value="zl"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'手术'" :val="'ss'"  :new-value="ss"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'会诊'" :val="'hz'"  :new-value="hz"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'输血'" :val="'sx'"  :new-value="sx"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'护理'" :val="'hl'"  :new-value="hl"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'膳食'" :val="'sss'"  :new-value="sss"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'自带药'" :val="'zdy'"  :new-value="zdy"></vue-checkbox>
                </div>
                <div  class="flex-container flex-align-c">
                    <span class="ft-14 whiteSpace padd-r-5 color-wtg">频率</br>类型:</span>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'临时'" :val="'yzlx0'"  :new-value="yzlx0"></vue-checkbox>
                    <vue-checkbox class="padd-r-5" @result="getReCheckOne" :new-text="'长期'" :val="'yzlx1'"  :new-value="yzlx1"></vue-checkbox>
                    <button v-if="num==0" class="root-btn btn-parmary" @click="printZxjh()">打印执行计划</button>
                    <button v-if="num==1" class="root-btn btn-parmary" @click="printBgd()">打印变更单</button>
                </div>
            </div>
           <div class="flex-container printHide flex-align-c margin-r-10 padd-t-10 padd-b-10">
               <tabs :num="num" :tab-child="[{'text':'执行计划'},{'text':'医嘱变更'}]"   @tab-active="tabBg" ></tabs>
               <div v-if="num==0" class="flex-container flex-align-c ">
                   <span class="ft-14 whiteSpace padd-r-5">项目</br>名称</span>
                   <input class="zui-input todate width162 " @keydown.enter="queryYz()" v-model="param.parm"/>
               </div>
               <div v-if="num==0" class="flex-container padd-l-10 flex-align-c">
                   <span class="ft-14 whiteSpace padd-r-5">打印</br>状态</span>
                   <select-input class="wh70" @change-data="resultChangezxjh" :not_empty="false"
                                 :child="IsPrint_tran" :index="zxdybz" :val="zxdybz"
                                 :name="'zxdybz'">
                   </select-input>
               </div>
               <div v-if="num==1" class="flex-container flex-align-c">
                   <span class="ft-14 whiteSpace padd-r-5">状态</span>
                   <select-input class="wh122" @change-data="selectGet" :not_empty="false"
                                 :child="ywlx_tran" :index="ywlx" :val="ywlx"
                                 :name="'ywlx'">
                   </select-input>
               </div>
               <div v-if="num==1" class="flex-container padd-l-10 flex-align-c">
                   <span class="ft-14 whiteSpace padd-r-5">打印</br>状态</span>
                   <select-input class="wh90" @change-data="resultChangezxjh" :not_empty="false"
                                 :child="IsPrint_tran" :index="yzbgdybz" :val="yzbgdybz"
                                 :name="'yzbgdybz'">
                   </select-input>
               </div>
               <div  class="flex-container padd-l-10  flex-align-c">
                   <span class="margin-r-10 whiteSpace">婴儿筛选</span>
                   <select-input  class="wh120" @change-data="commonResultChange"
                                  :child="qsxzList" :index="'yexm'" :index_val="'yebh'" :val="jlxqContent.yebh"
                                  :name="'jlxqContent.yebh'" :index_mc="'yexm'" search="true" >
                   </select-input>
               </div>
           </div>

            <div v-if="num==0" id="wrapper" @scroll="scrollFn(true)" class="wh100  position flex-one" :class="{'over-auto ':rightJsonList.length>=2}">
                <div :style="{'height': brItemListSumHeight + 'px'}">
                    <div class="wh100" :style="{'top': brPosition[brListIndex] + 'px'}" style="position: absolute;left:0;" v-for="(brListItem,brListIndex) in rightJsonList">
                        <div v-if="(brListWinSize.top < brPosition[brListIndex] && brPosition[brListIndex] < brListWinSize.bottom) || (brListWinSize.top < brPosition[brListIndex + 1] && brPosition[brListIndex] < brListWinSize.bottom)">
                            <div class="jbxx-size margin-t-5">
                                <div class="jbxx-box padd-l-12">
                                    科别：<span class="font-14-654 padd-r-18" v-text="brListItem.ryksmc"></span>
                                    床号：<span class="font-14-654 padd-r-18" v-text="brListItem.rycwbh"></span>
                                    姓名：<span class="font-14-654 padd-r-18" v-text="brListItem.brxm"></span>
                                    年龄：<span class="font-14-654 padd-r-18" v-text="brxb_tran[brListItem.brxb]"></span>
                                    住院号：<span class="font-14-654 padd-r-18" v-text="brListItem.brzyh==null?brListItem.zyh:brListItem.brzyh"></span>
                                </div>
                            </div>
                            <div class="zui-table-view flex-one  flex-dir-c flex-container">
                                <div class="zui-table-header">
                                    <table class="zui-table table-width50 ">
                                        <thead>
                                        <tr>
                                            <th class="cell-m" >
                                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-m"><span>类型</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-xxl text-left"><span>医嘱名称</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>规格</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-l"><span>给药方式</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-l"><span>计划时间</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-l"><span>频次</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-l"><span>医生嘱托</span></div>
                                            </th>
                                        </tr>
                                        </thead>
                                    </table>
                                </div>
                                <div class="zui-table-body  over-auto" :data-no-change="rightJsonList.length>=2?'true':undefined"
                                     @scroll="scrollTable">
                                    <table class="zui-table table-width50">
                                        <tbody>
                                        <!-- yzlx_tran[yzListItem.yzlx] -->
                                        <tr v-for="(yzListItem, $index) in brListItem.yzxx" :tabindex="$index"
                                            :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index === hoverIndex&&brListIndex===hoverBrListIndex}]"
                                            @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                            @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')">
                                            <td >
                                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-m" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                            </td>
                                            <td  :class="[ yzListItem.tzbj ]">
                                                <div class="zui-table-cell cell-xxl text-over-2 text-left "
                                                     v-text="yzListItem.xmmc"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="yzListItem.syjl"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-l" v-text="yzListItem.jhsj"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-l" v-text="yzListItem.pcmc"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-l" v-text="yzListItem.yssm"></div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <p v-if="brListItem.yzxx.length==0" class="  noData text-center zan-border">暂无数据...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="num==1" id="wrapper" @scroll="scrollFn(true)" class="wh100 position yzgl margin-r-10 " :class="{'over-auto ':rightJsonList.length>=2}">
                <div :style="{'height': brItemListSumHeight + 'px'}">
                    <div class="wh100" :style="{'top': brPosition[brListIndex] + 'px'}" style="position: absolute;left:0;"
                         v-for="(brListItem,brListIndex) in rightJsonList">
                        <div v-if="(brListWinSize.top < brPosition[brListIndex] && brPosition[brListIndex] < brListWinSize.bottom) || (brListWinSize.top < brPosition[brListIndex + 1] && brPosition[brListIndex] < brListWinSize.bottom)">
                            <div class="jbxx-size margin-t-5">
                                <div class="jbxx-box padd-l-12">
                                    科别：<span class="font-14-654 padd-r-18" v-text="brListItem.ryksmc"></span>
                                    床号：<span class="font-14-654 padd-r-18" v-text="brListItem.rycwbh"></span>
                                    姓名：<span class="font-14-654 padd-r-18" v-text="brListItem.brxm"></span>
                                    年龄：<span class="font-14-654 padd-r-18" v-text="brxb_tran[brListItem.brxb]"></span>
                                    住院号：<span class="font-14-654 padd-r-18" v-text="brListItem.brzyh==null?brListItem.zyh:brListItem.brzyh"></span>
                                </div>
                            </div>
                            <div class="zui-table-view flex-one  flex-dir-c flex-container">
                                <div class="zui-table-header">
                                    <table class="zui-table table-width50 ">
                                        <thead>
                                        <tr>
                                            <th class="cell-m" >
                                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s">开始时间</div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-xxl text-left"><span>医嘱名称</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-xl"><span>备注</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>医嘱类型</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>一次剂量</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>一次领量</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>给药方式</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>频次</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>停止时间</span></div>
                                            </th>
                                            <th >
                                                <div class="zui-table-cell cell-s"><span>业务类型</span></div>
                                            </th>
                                        </tr>
                                        </thead>
                                    </table>
                                </div>
                                <div class="zui-table-body  over-auto" :data-no-change="rightJsonList.length>=2?'true':undefined"
                                     @scroll="scrollTable">
                                    <table class="zui-table table-width50">
                                        <tbody>
                                        <!-- yzlx_tran[yzListItem.yzlx] -->
                                        <tr v-for="(yzListItem, $index) in brListItem.yzxx" :tabindex="$index"
                                            :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index === hoverIndex&&brListIndex===hoverBrListIndex}]"
                                            @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                            @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')">
                                            <td >
                                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s"></div>
                                            </td>
                                            <td  :class="[ yzListItem.tzbj ]">
                                                <div class="zui-table-cell cell-xxl text-over-2 text-left "
                                                     v-text="yzListItem.xmmc"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-xl" v-text="yzListItem.bzsm"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" >{{yzListItem.dcjl}}{{yzListItem.jldwmc}}</div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="yzListItem.sl + yzListItem.yfdwmc"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="yzListItem.yyffmc"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ystzsj,'shortY')"></div>
                                            </td>
                                            <td >
                                                <div class="zui-table-cell cell-s" v-text="yzListItem.ywlx"></div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                    <p v-if="brListItem.yzxx.length==0" class="  noData text-center zan-border">暂无数据...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="num==0" class="padd-l-10 printHide">
            <div  class="zui-table-view flex-one padd-b-40 padd-r-10 flex-dir-c flex-container">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <input-checkbox @result="reCheckBox2" :list="'yyffList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l">用药方法</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body flex-one over-auto"  @scroll="scrollTable">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr   @click="checkSelect([$index,'some','yyffList'],$event)"
                              :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                              @mouseenter="hoverMouse(true,$index)"
                              @mouseleave="hoverMouse()"
                              :class="[{{'table-hovers':isCheckedS[$index]}]" :tabindex="$index"
                              v-for="(item, $index) in yyffList" >
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox2" :list="'yyffList'"
                                                :type="'some'" :which="$index"
                                                :val="isCheckedS[$index]">
                                </input-checkbox>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l">{{item.yyffmc}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="childpage/zxjh.js"></script>
