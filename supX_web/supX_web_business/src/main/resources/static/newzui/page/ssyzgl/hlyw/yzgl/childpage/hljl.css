.toolMenu{
    width: 100%;
    margin: 3px 0 0 0;
    border-bottom: 4px solid #eee;
}
#jlxx{
    float: left;
    width: 200px;
    height: calc(100% - 48px);
    overflow: scroll;
    border-right: 4px solid #EEEEEE;
    padding: 3px;
}
#jlxx th, #jlxx td{
    height: 20px;
    font-size: 12px;
}
#jlxq{
    width:100%;
    /*height: calc(100% - 48px);*/
    padding: 3px;
    overflow: hidden;
}

.jlxq_title{
    position: absolute;
    top: -10px;
    background-color: #FFFFFF;
    font-size: 14px;
    min-width: 50px;
    text-align: center;
}

.common-css input{
    text-indent: 0;
}
.cm{
    right: 15px;
}
.line-height-12{
    line-height: 2.3;
}
#model{
    z-index: 110;
}
.bqcydj_model{
    width: auto;
    height: 320px;
    overflow: hidden;
}
.wh20{
	width:20%
}
.wh81{
	width:80%
}
.removeClass{
    color: white;
    cursor: pointer;
    position: absolute;
    left: 172px;
    top: -5px;
    border-radius: 7px;
    height: 17px;
    width: 13px;
    background-color: red;
}
.addClass{
    color: #004b80;
    font-weight: 900;
    font-size: 30px;
    margin-left: 10px;
    cursor: pointer;
}
