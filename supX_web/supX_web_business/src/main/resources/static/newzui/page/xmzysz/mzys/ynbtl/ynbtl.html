<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>死亡讨论</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link href="swtl.css" rel="stylesheet">
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper background-f swtl">
    <div class="panel tong-top flex-container   flex-align-c">
            <button v-waves class="tong-btn btn-parmary" @click="fqtl()"><i class="iconfont iconfont-no-hover icon-icon70  icon-font19"></i>发起讨论</button>
            <button v-waves class="tong-btn btn-parmary-b" @click="getData()"><i class="iconfont icon-iocn56 icon-c1 icon-font19"></i>刷新</button>
            <div class="flex-container   flex-align-c">
                <span class="padd-r-5 whiteSpace font14">状态</span>
                <select-input style="width: 122px;" @change-data="resultChange" :not_empty="false"
                              :child="jzType_tran" :index="popContent.jzbz" :val="popContent.jzbz"
                              :name="'popContent.jzbz'">
                </select-input>
            </div>
            <div class="flex-container padd-l-10  flex-align-c">
                <span class="padd-r-5 whiteSpace font14">检索</span>
                        <input class="zui-input wh180" placeholder="患者姓名/主持人姓名" type="text" id="jsVal" v-model="popContent.parm"/>
            </div>
    </div>
    <div class="zui-table-view hzList padd-r-10 padd-l-10">
        <div class="zui-table-header" >
            <table class="zui-table"ref="tableHeader"style="text-align: center" >
                <thead>
                <tr>
                    <th>
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>身份证号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>年龄</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>讨论时间</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl text-left"><span>讨论地点</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>主持人</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl text-left"><span>参与人员</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>状态</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>

        <div class="zui-table-body" @scroll="scrollTable($event)">
            <vue-scroll :ops="pageScrollOps"   @handle-scroll="handleScroll">
                <table class="zui-table" style="text-align: center">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)" :tabindex="$index" v-for="(item, $index) in jsonList"  @dblclick="newOpenPage($index)">
                        <td ><div class="zui-table-cell cell-m" v-text="$index + 1"><span></span></div></td>
                        <td ><div class="zui-table-cell cell-s"><span v-text="item.brxm"></span></div></td>
                        <td ><div class="zui-table-cell cell-xl"><span v-text="item.sfzjhm==null?'--':item.sfzjhm"></span></div></td>
                        <td ><div class="zui-table-cell cell-s"><span v-text="item.brnl"></span></div></td>
                        <td ><div class="zui-table-cell cell-xl"><span v-text="item.tlsj"></span></div></td>
                        <td ><div class="zui-table-cell cell-xl text-left"><span v-text="item.tldd"></span></div></td>
                        <td ><div class="zui-table-cell cell-s "><span v-text="item.zcrxm"></span></div></td>
                        <td>
                            <span v-for="(list,$index) in (item.cyrList)">【{{list.ryxm}}】</span>
                        </td>
                        <td >
                            <div class="zui-table-cell cell-s color-wtg color-green color-cf3" >
                                <span v-if="item.status == '1'">待接收</span>
                                <span v-if="item.status == '2'">待讨论</span>
                                <span v-if="item.status == '3'">已拒绝</span>
                                <span v-if="item.status == '4'">待审核</span>
                                <span v-if="item.status == '5'">返回处理</span>
                                <span v-if="item.status == '6'">已完成</span>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell flex-center  cell-s">
                                <i class="iconfont icon-iocn12 icon-c75 icon-font20 padd-t-2" v-if="item.status=='1'" data-title="接受" @click="accept($index)"></i>
                                <i class="iconfont icon-iocn29 icon-c75 icon-font20 padd-t-2" v-if="item.status=='1'" data-title="拒绝" @click="refuse($index)"></i>
                                <i class="iconfont icon-iocn46 icon-c75 icon-f  ont20 padd-t-2" v-if="item.status == '5' || item.status=='2' || item.status =='6'" data-title="编辑" @click="newOpenPage($index)"></i>
                                <i class="iconfont icon-iocn45 icon-c75 icon-font20 padd-t-2" v-if="item.status=='4'" data-title="审核" @click="newOpenPage()"></i>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <!--<p v-if="jsonList.length==0" class=" noData text-center zan-border">暂无数据...</p>-->
            </vue-scroll>
        </div>

        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr >
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
    <div class="side-form  pop-width"  :class="{'ng-hide':type}" v-cloak id="brzcList1" role="form">
        <div class="fyxm-side-top flex-between">
            <span>发起死亡讨论</span>
            <span class="fr closex ti-close" @click="close"></span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list1 flex-start">
                <li>
                    <i>讨论患者</i>
                    <input  type="text" :value="popContent.brxm"
                            @input="searching(false,'text',$event.target.value)"
                            @keyDown="changeDown($event,'text')" class="zui-input inp_search wh180"
                            placeholder="请填写姓名/证件号码/手机号"/>
                    </select-input>
                    <search-table :message="searchCon" :selected="selSearch" :them="them" :them_tran="them_tran"
                                  :page="page" @click-one="checkedOneOut" @click-two="selectOne"></search-table>
                </li>
                <li>
                    <i>讨论主持人</i>
                    <div class="flex-container">
                        <div class="HeadPortrait" v-for="list in zcrAvatar" :id="list">
                            <img class="headImg" src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg">
                            <p class="color-f2a654 font12" v-text="list.ryxm"></p>
                        </div>
                        <span class="iconfont  icon-upload col6_span1" @click="tjys(false)"></span>
                    </div>
                </li>
                <li>
                    <i>讨论参与人员</i>
                    <div class="flex-container">
                        <div class="HeadPortrait" v-for="list in objAvatar" :id="list">
                            <img class="headImg" src="https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1532321192435&di=dabff5879d99c51d6274a56806301dff&imgtype=0&src=http%3A%2F%2Ff.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2F3812b31bb051f8195bf514a9d6b44aed2f73e705.jpg">
                            <p class="color-f2a654 font12" v-text="list.ryxm"></p>
                        </div>
                        <span class="iconfont  icon-upload col6_span1" @click="tjys(true)"></span>
                    </div>
                </li>
                <li>
                    <i>讨论时间</i>
                    <div class="zui-date">
                        <span style="margin-top:-2px" class="datenox icon-rl" lay-key="3"></span>
                        <input type="text" class="zui-input " id="timeVal" v-model="popContent.tlsj"/>
                    </div>
                </li>
                <li>
                    <i>讨论地点</i>
                    <input type="text" class="zui-input " v-model="popContent.tldd"/>
                </li>
                <li>
                    <i>讨论目的</i>
                    <input type="text" class="zui-input " v-model="popContent.tlmd"/>
                </li>
                <li>
                    <i>病历摘要</i>
                    <input type="text" class="zui-input " v-model="popContent.blzy"/>
                </li>
            </ul>
        </div>
        <div class="ksys-btn zui-table-tool" style="height: 66px">
            <button v-waves class="root-btn btn-parmary-d9" @click="close">取消</button>
            <button v-waves class="root-btn btn-parmary" @click="submit">确定</button>
        </div>
    </div>
    <div class="side-form  pop-width"  :class="{'ng-hide':type}" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top flex-between">
            <span>添加讨论医生</span>
            <span class="fr closex ti-close" @click="close"></span>
        </div>
        <div class="tong-search">
            <div class="top-form">
                <label class="top-label font14">科室</label>
                <select-input style="width: 122px;" @change-data="resultChange" :not_empty="false"
                              :child="jzType_tran" :index="popContent.jzbz" :val="popContent.jzbz"
                              :name="'popContent.jzbz'">
                </select-input>
            </div>
        </div>
        <div class="ksys-side" style="padding:0 8px 0 9px">
            <div class="zui-table-view">
                <div class="zui-table-header">
                    <table class="zui-table" >
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <!--<input-checkbox @result="reCheckBox" :list="'jsonList'"-->
                                <!--:type="'all'" :val="isCheckAll">-->
                                <!--</input-checkbox>-->
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>医生姓名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-m"><span>科室</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>职称</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','jsonList'],$event)" :tabindex="$index" v-for="(item, $index) in ryList"  @dblclick="edit($index)">
                            <td>
                                <input-checkbox @result="reCheckBox" :list="jsonList"
                                                :type="isMulti" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td ><div class="zui-table-cell cell-s"><span v-text="item.ryxm"></span></div></td>
                            <td ><div class="zui-table-cell cell-m"><span v-text="item.ksmc"></span></div></td>
                            <td ><div class="zui-table-cell cell-s"><span v-text="item.jszwmc"></span></div></td>
                        </tr>
                        </tbody>
                    </table>
                    <!--<p v-if="jsonList.length==0" class=" noData text-center zan-border">暂无数据...</p>-->
                </div>
            </div>
        </div>
        <div class="ksys-btn zui-table-tool" style="height: 66px">
            <button v-waves class="root-btn btn-parmary-d9" @click="close()">取消</button>
            <button v-waves class="root-btn btn-parmary" @click="submit">确定</button>
        </div>
    </div>
</div>
</body>
<script src="/newzui/currentCSS/js/vue/vuescroll.min.js"></script>
<script  src="swtl.js"></script>
</html>
