<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>材料产地</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" rel="stylesheet" href="ypcd.css"/>
</head>
<style>
    .table-hovers-filexd-l {
        border-right: none !important;
    }

    .table-hovers-filexd-r {
        border-left: none !important;
    }

    .table-hovers-filexd-r-child {
        border-right: 1px solid #1abc9c !important;
    }
</style>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
    <div class="wrapper" id="jyxm_icon">
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
                <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
                <button class="tong-btn btn-parmary-b"><i class=" icon-width icon-dc padd-l-25"></i>导出</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline">
                        <label class="zui-form-label ft-14">检索</label>
                        <div class="zui-input-inline margin-f-l25">
                            <input class="zui-input wh180" placeholder="请输入关键字" type="text" id="jsvalue"
                                   v-model="search"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view padd-l-10 padd-r-10" z-height="full">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>产地编码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>产地名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>停用标志</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body " @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr :tabindex="$index" v-for="(item, $index) in jsonList"
                        @click="checkSelect([$index,'one','jsonList'],$event)" :tabindex="$index"
                        @dblclick="edit($index)"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">1</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cdbm">001</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cdmc">西药收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.pyjm">材料收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <div class="switch">
                                    <input type="checkbox" true-value="0" false-value="1" v-model="item.tybz" disabled/>
                                    <label></label>
                                </div>
                            </div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                        <span class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em class="width30">
                                   <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                               </span>
                            </div>
                        </td>
                        <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>

            </div>
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em class="width30">
                                   <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                               </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>
        </div>

    </div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-width" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side">
        <span class="span0">
        <i>产地编码</i>
        <input type="text" class="zui-input border-r4" v-model="popContent.cdbm" @keyup="nextFocus($event)"
               disabled="disabled"/>
        </span>
        <span class="span0">
        <i>产地名称</i>
        <input type="text" class="zui-input border-r4" v-model="popContent.cdmc" @keyup.enter="confirms"
               @input="setPYDM(popContent.cdmc, 'popContent', 'pyjm')"/>
        </span>
        <span class="span0">
        <i>拼音简码</i>
        <input type="text" class="zui-input border-r4" v-model="popContent.pyjm" @keydown="nextFocus($event)"
               disabled="disabled"/>
        </span>
        <span class="margin-top-10 span0">
        <i style="float: left;">状态</i>
        <div class="switch">
         <input type="checkbox" id="tybz" true-value="0" false-value="1" v-model="popContent.tybz"/>
         <label for="tybz"></label>
         </div>
        </span>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<script src="ypcd.js"></script>

</body>

</html>
