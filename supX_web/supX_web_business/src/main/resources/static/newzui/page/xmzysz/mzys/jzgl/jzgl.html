<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!--<meta http-equiv="refresh" content="20">-->
    <title>接诊管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" type="text/css" href="/newzui/css/icon.css">
    <link href="jzgl.css" rel="stylesheet">
</head>
<body class="skin-default background-f" id="jzgl">
<div class="wrapper flex-one flex-container flex-dir-c">
    <div class="panel  " v-cloak>
        <div class="tong-top flex-jus-sb">
            <div class="flex-container flex-align-c">
                <button v-waves class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">刷新</button>
                <button v-waves v-if="csqxContent.N03001200138 == '1'" class="tong-btn btn-parmary icon-add paddr-r5"
                        @click="toGh">补挂号
                </button>
                <button v-waves class="tong-btn btn-parmary icon-add paddr-r5" @click="nextJh()">顺叫[F1]</button>
                <button v-waves class="tong-btn btn-parmary icon-add paddr-r5" @click="restJh()">重叫[F2]</button>
                <vue-checkbox class="padd-r-10" @result="faFun" :new-text="'门特病人'" :val="'param.mtfa'"
                              :new-value="param.mtfa"></vue-checkbox>
            </div>
            <div class="text-right menu-right flex-container flex-align-c flex-jus-e">
                <div v-if="csqxContent.N03001200133 == '1'" class="flex-container flex-align-c padd-r-10">
                    <span class="ft-14 padd-r-5">叫号科室</span>
                    <select-input :search="true" class="wh120" @change-data="queryKsip" :not_empty="false"
                                  :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="jhksbm"
                                  :name="'jhksbm'">
                    </select-input>
                </div>
                <span class="fa butt-hover fa-th-large " :class="{'active':index==0}" @click="tab(0)"></span>
                <span class="relative fenge"></span>
                <span class="fa butt-hover fa-bars" :class="{'active':index==1}" @click="tab(1)"></span>
            </div>
        </div>
        <div class=" flex-container flex-align-c padd-t-10 flex-wrap-w padd-b-10">
            <div class="flex-container flex-align-c padd-l-10">
                <span class="ft-14 padd-r-5">当前科室</span>
                <select-input :search="true" class="wh120" @change-data="ksChange" :not_empty="false"
                              :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="param.ksbm"
                              :name="'param.ksbm'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-l-10">
                <span class="ft-14 padd-r-5">接诊状态</span>
                <select-input :search="true" class="wh120" @change-data="Wf_GetBrList" :not_empty="false"
                              :child="jzType_tran" :index="param.jzbz" :val="param.jzbz"
                              :name="'param.jzbz'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-l-10">
                <span class="ft-14 padd-r-5">病人过滤</span>
                <select-input :search="true" class="wh120" @change-data="Wf_GetBrList" :not_empty="false"
                              :child="mzbrgl_tran" :index="param.brgl" :val="param.brgl"
                              :name="'param.brgl'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-l-10">
                <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                <div class="  flex-container flex-align-c">
                    <input class="zui-input todate wh160 " placeholder="请选择处方开始时间" id="timeVal"/><span
                        class="padd-l-5 padd-r-5">~</span>
                    <input class="zui-input todate wh160 " placeholder="请选择处方结束时间" id="timeVal1"/>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-l-10 ">
                <span class="ft-14 padd-r-5">检索</span>
                <input class="zui-input wh150" placeholder="请输入关键字" type="text" v-model="param.parm"
                       @keyup.enter="Wf_GetBrList(String,$event)"/>
            </div>
        </div>

        <!--<model  :model-show="false"  :show-zoom="true"-->
        <!--v-if="menuTitle" :title="'转诊病人列表'">-->
        <!--<div class="bqcydj_model ">-->
        <!--<div class="flex-container flex-align-c flex-wrap-w padd-b-10">-->
        <!--<span>日期：</span>-->
        <!--<div class="  flex-container flex-align-c">-->
        <!--<input class="zui-input todate wh120 " v-model="ksrq" placeholder="请选择处方开始时间" id="timeVals" @click="showDate('timeVals','ksrq')" />-->
        <!--<span class="padd-l-5 padd-r-5">~</span>-->
        <!--<input class="zui-input todate wh120 " v-model="jsrq" placeholder="请选择处方结束时间" id="timeVal1" @click="showDate('timeVal1','jsrq')" />-->
        <!--</div>-->
        <!--<div v-waves class="tong-btn btn-parmary margin-l-10 btn-parmary-not xmzb-db yellow-bg" @click="getPopData()">查询</div>-->
        <!--</div>-->
        <!--<div class="zui-table-view" >-->
        <!--<div class="zui-table-header">-->
        <!--<table class="zui-table ">-->
        <!--<thead>-->
        <!--<tr>-->
        <!--<th class="cell-m">-->
        <!--<div class="zui-table-cell cell-m">序号</div>-->
        <!--</th>-->
        <!--<th>-->
        <!--<div class="zui-table-cell cell-s">病人姓名</div>-->
        <!--</th>-->
        <!--<th>-->
        <!--<div class="zui-table-cell cell-s">性别</div>-->
        <!--</th>-->
        <!--<th>-->
        <!--<div class="zui-table-cell cell-s">年龄</div>-->
        <!--</th>-->
        <!--<th>-->
        <!--<div class="zui-table-cell cell-s">转诊申请号</div>-->
        <!--</th>-->
        <!--</tr>-->
        <!--</thead>-->
        <!--</table>-->
        <!--</div>-->
        <!--<div class="zui-table-body body-height"  data-no-change>-->
        <!--<table class="zui-table " >-->
        <!--<tbody>-->
        <!--<tr v-for="(item, $index) in userList" class="tableTr2" :tabindex="$index" ref="list"-->
        <!--:class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"-->
        <!--@mouseenter="switchIndex('hoverIndex',true,$index)"-->
        <!--@mouseleave="switchIndex()"-->
        <!--@dblclick="getOne($index,item)">-->
        <!--<td class="cell-m">-->
        <!--<div class="zui-table-cell cell-m" v-text="$index+1">序号</div>-->
        <!--</td>-->
        <!--<td>-->
        <!--<div class="zui-table-cell text-decoration cell-s" v-text="item.patientname" >病人姓名</div>-->
        <!--</td>-->
        <!--<td>-->
        <!--<div class="zui-table-cell cell-s" v-text="brxb_tran[item.gender]"></div>-->
        <!--</td>-->
        <!--<td>-->
        <!--<div class="zui-table-cell cell-s" v-text="item.age"></div>-->
        <!--</td>-->
        <!--<td>-->
        <!--<div class="zui-table-cell cell-l title" style="overflow: hidden" v-text="item.serialnumber"></div>-->
        <!--</td>-->
        <!--<p class="noData  text-center zan-border" v-if="userList.length==0"> 暂无数据</p>-->
        <!--</tr>-->
        <!--</tbody>-->
        <!--</table>-->
        <!--</div>-->
        <!--</div>-->
        <!--</div>-->
        <!--</model>-->
    </div>
    <div class="col-x-12 kp flex-container flex-dir-c flex-one" v-cloak align="center" v-if="index==0">
        <!-- @mousewheel="loadingData($event)" -->
        <div class="kpFlex" ref="kp" @mousewheel="loadingData($event)">
            <div :class="kpIndex==index ? 'active' :''" class=" position userWidth" @click="check(index)"
                 v-for="(list,index) in Brxx_List" v-if="Brxx_List.length!=0">
                <div :class="list.ghks=='0922'?(list.sfqj?(list.sfqj=='1'?'header-text-red':'header-text-yellow'):'header-text-green'):'header-text'" class="flex-container" >
                        <span class="userNameImg">
                                <img :src="nljd[list.nljd]"/>
                        </span>
                    <div>
                        <p class="padd-t-10 padd-b-5 text-left"
                           style="display: flex;justify-content: start;align-items: center;">
                            <span class="userName relative" @click="userGet(list,['brPage/brjz',0,list])"
                                  v-if="list.brxm.length>=5"><i class="title title-width" :data-title="list.brxm"
                                                                v-text="list.brxm"></i></span>
                            <span class="userName relative" @click="userGet(list,['brPage/brjz',0,list])" v-else
                                  v-text="list.brxm"></span>
                            <span class="userName-pin" v-show="list.pkhbz==1"><img
                                    src="/newzui/pub/image/pin.png"></span>
                            <span title="绿色通道病人" class="userName-pin color-green"
                                  v-show="list.lstdbz==1">绿</span>
                            <span class="userName-lc color-7f">
                            <i class="color-dlr padd-r-5" v-text="brxb_tran[list.brxb]" v-if="list.brxb==1"></i>
                            <i style="color:#fa6969" class="padd-r-5" v-text="brxb_tran[list.brxb]" v-else></i>
                            <span v-text="list.brnl"></span><span v-text="nldw_tran[list.nldw]"></span>
                            <span v-if="list.nl2" v-text="list.nl2"></span><span v-text="nldw_tran[list.nldw2]"></span>
                        </span>
                        </p>
                        <p class="text-left color-7f">挂号序号：<span v-text="list.ghxh"></span></p>
                        <p class="text-left color-7f">分诊级别：<span v-text="list.fzjb"></span></p>
                        <p class="text-left color-7f" v-if="list.ghks=='0922'">病区：<span v-if="list.sfqj=='0'">留观区</span>
                            <span v-else-if="list.sfqj=='1'">抢救区</span>
                            <span v-else>诊断区</span></p>
                        <p class="text-left color-7f" v-else>挂号科室：<span v-text="list.ghksmc"></span></p>
                        <p class="text-left color-7f">疾病名称：<span v-text="list.jbmc"></span></p>
                        <p class="text-left color-7f">接诊医生：<span v-text="list.jzysxm"></span></p>
                        <p class="text-left color-7f">挂号时间：<span v-text="fDate(list.ghrq,'time')"></span></p>
                        <p class="padd-b-5 text-left" v-show="list.zt==0">
                            <i class="color-dsh font12">等候{{list.hh}}小时{{list.mm}}分钟</i>
                        </p>
                        <span class="djzt" v-if="list.zt==0"></span>
                        <span class="jzztb" v-if="list.zt==1"></span>
                    </div>
                    <!--                    <span v-if="list.zt==2"></span>-->
                    <span v-if="list.fbbm =='40'" class="mtglbr">门特</span>
                    <span v-if="list.fbbm =='41'" class="mtglbr">两病</span>
                </div>
                <div class="footer-text">
                    <p class="text-left color-wtg">科室序号：<span>{{list.ksxh}}号</span></p>
                    <div class="cz-butt" :id="'cz-'+index" :class="{'active': isActive[index]}" @dblclick.stop>
                        <span class="butt" @click.stop="czClick(index, $event)">操作</span>
                        <transition name="top-bottom-fade">
                            <div class="content flex-container flex-wrap-w" v-show="isActive[index]">
                                <div :data-index="$index" v-if="setShow($index,list) == true"
                                     v-for="(cz, $index) in czList" class="cont-butt" @click="cz.clickBc(index,list)">
                                    {{ setName(cz.name,list ,$index) }}
                                </div>
                            </div>
                        </transition>
                    </div>
                </div>

            </div>
            <div class="" style="margin: 5px 9px 10px 9px;width: 307px;display: inline-block;"
                 v-for="zlist in brk_listD" v-if="Brxx_List.length!=0"></div>
            <div v-if="Brxx_List.length!=0 && !isDoneCb " class=" ysb-green text-center"
                 style="width: 100%;margin:30px 0">{{loadData}}
            </div>
            <p class="noData  text-center " v-if="Brxx_List.length==0" style="width: 100%;"> 暂无数据</p>
        </div>

        <model :s="'确定'" :c="'取消'" @default-click="saveZd" @result-clear="cyzd=false" :model-show="true"
               @result-close="cyzd=false" v-if="cyzd" title="诊断" class="cyzdClass">
            <div class="flex-container bqcydj_model">
                <div style="width: 100%;">
                    <div class="flex-container flex-align-c flex-wrap-w ">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">姓名:{{userObj.brxm}}</span>
                        </div>
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">性别:{{xtwhxb_tran[userObj.brxb]}}</span>
                        </div>
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">年龄:{{userObj.brnl}}{{nldw_tran[userObj.nldw]}}</span>
                        </div>
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5">挂号序号:{{userObj.ghxh}}</span>
                        </div>
                    </div>
                    <div class="zui-table-view  hzList-border flex-container flex-dir-c">
                        <div class="zui-table-header" style="overflow: scroll;">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m">序号</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xxl">诊断描述</div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s ">ICD编码</div>
                                    </th>


                                </tr>
                                <tr @mouseenter="hoverMouse(true,$index)"
                                    @mouseleave="hoverMouse()" v-for="(item, $index) in userObj.qtzd"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                                    <td>
                                        <div class="zui-table-cell cell-m">
                                            <span v-text="$index+1"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell flex-container flex-align-c cell-xxl">
                                            <input class="zui-input height-input" data-notEmpty="false"
                                                   v-model="item.jbmc"
                                                   @keydown="changeDown($event,$index,'jbmc','jbmb','qtzd')"
                                                   @input="change(false,$index,'jbmc','jbbm',$event.target.value,'jbbm')">
                                            <search-table :message="searchCon" :selected="selSearch" :page="queryObj"
                                                          :them="them"
                                                          @click-one="checkedOneOut"
                                                          @click-two="checkedTwoOut">
                                            </search-table>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s  ">
                                            <span v-text="item.jbmb"></span>
                                        </div>
                                    </td>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                    <!--                <div class="flex-container flex-align-c padd-t-10 padd-b-10">-->
                    <!--                    <div class="flex-container flex-align-c padd-r-20 ">-->
                    <!--                        <span class="padd-r-5 whiteSpace ft-14">出院方式:</span>-->
                    <!--                        <select-input :search="true" class="wh160" @change-data="resultChange" :not_empty="true"-->
                    <!--                                      :child="lyfs_tran" :index="userObj.cyfs"-->
                    <!--                                      :val="userObj.cyfs" :name="'userObj.cyfs'">-->
                    <!--                        </select-input>-->
                    <!--                    </div>-->
                    <!--                    <vue-checkbox class="padd-r-10" @result="faFun" :new-text="'确诊'" :val="'userObj.sfqz'" :new-value="userObj.sfqz"></vue-checkbox>-->
                    <!--                    <input class="zui-input todate  " @click="showTime('timeVal61','qzsj')" v-model="userObj.qzsj" placeholder="" id="timeVal61" />-->
                    <!--                </div>-->
                    <!--                <div class="flex-container flex-align-c padd-b-10">-->
                    <!--                    <div class="flex-container flex-align-c padd-r-20 ">-->
                    <!--                        <span class="padd-r-5 whiteSpace ft-14">出院时间:</span>-->
                    <!--                        <input class="zui-input todate wh160 " @click="showTime('timeVal62','cysj')" v-model="userObj.cysj" placeholder="" id="timeVal62" />-->
                    <!--                    </div>-->
                    <!--                </div>    -->
                </div>


        </model>
    </div>

    <div class="zui-table-view  zui-item " v-cloak v-if="index==1">
        <div class="zui-table-header">
            <table class="zui-table ">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">序号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">病人姓名</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">标识</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">性别</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">年龄</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">挂号序号</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">联系电话</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">民族</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">职业</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">身高</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">体重</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">联系人姓名</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">联系人关系</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">联系人电话</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">籍贯</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">市</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">区</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl">现住址学校/单位</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">接诊时间</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">接诊医生</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">接诊科室</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">历史就诊</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">初诊/复诊</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">患者去向</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">发病时间</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">发病地点</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">血糖</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">血压</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">体温</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">呼吸</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">脉搏</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">心率</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl">主诉</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xxl">现病史</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xxl">既往史</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xxl">症状体征</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xxl">过敏史</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xxl">家族史</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">诊断疾病</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">其他诊断</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">其他诊断 1</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">其他诊断 2</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">其他诊断 3</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">其他诊断 4</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">医保诊断</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">主病</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">症型</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">是否传染病</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">是否发热病人</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">发热情况</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l">对应处理</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">报告</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">状态</div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s">备注说明</div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s">操作</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr v-for="(item, $index) in Brxx_List" class="tableTr2" :tabindex="$index" ref="list"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('activeIndex',true,$index)"
                    @dblclick="goBre(item,['brPage/hzzx',0,item])">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell text-decoration cell-s" v-text="item.brxm">病人姓名</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <span class="userName-pin" v-show="item.pkhbz==1">
                          		<img src="/newzui/pub/image/pin.png">
                         	</span>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="brxb_tran[item.brxb]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.brnl+nldw_tran[item.nldw]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l title" style="overflow: hidden" v-text="item.ghxh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.sjhm?item.sjhm:item.lxrdh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.brmzmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.zybmmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.sg"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.tz"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.lxrxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.lxrgx"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.lxrdh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jgmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jgshimc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jgxianmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.jzdmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.jzsj,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.jzysxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ghksmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ghxh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.sffz"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.hzqx"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.fbrq,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.fbdd"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.xtms"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s"
                             v-text="item.xySsy?item.xySsy:'' + '/' + item.xySzy?item.xySzy:''"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.tw"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.hx"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.mb"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.xl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.zs"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xxl" v-text="item.xbs"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xxl" v-text="item.jws"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xxl" v-text="item.zyzztz"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xxl" v-text="item.gms"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xxl" v-text="item.jzs"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.jbmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.qtzdmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.qtzdmc1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.qtzdmc2"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.qtzdmc3"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.qtzdmc4"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ybjbmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.zyzh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.zyzf"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="istrue_tran[item.sfcrb]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="istrue_tran[item.sffrbr]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.frqk"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.dycl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">报告</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <i :class="item.zt==0 ? 'color-wtg': item.zt==1 ? 'color-dlr': 'color-wc'"
                               v-text="jzType_tran[item.zt]"></i>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.bzsm"></div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s flex-container flex-jus-c flex-align-c">
                            <div class="cz-butt" :id="'cz-'+index" :class="{'active': isActive[$index]}" @dblclick.stop>
                                <span class="butt" @click.stop="czClick($index, $event)">操作</span>
                                <transition name="top-bottom-fade">
                                    <div :style="objStyle" class="content flex-container flex-wrap-w"
                                         v-show="isActive[$index]">
                                        <div :data-index="index" v-if="setShow(index,item) == true"
                                             v-for="(cz, index) in czList" class="cont-butt"
                                             @click="cz.clickBc(index,item)">
                                            {{ setName(cz.name,item ,index) }}
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>
                    </td>
                    <p class="noData  text-center zan-border" v-if="Brxx_List.length==0"> 暂无数据</p>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-m">序号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">病人姓名</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in Brxx_List"
                        :tabindex="$index"
                        ref="list"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','Brxx_List'],$event)">
                        <td>
                            <div class="zui-table-cell cell-m">{{$index+1}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell text-decoration cell-s" v-text="item.brxm">病人姓名</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(list, index) in Brxx_List"
                        :tabindex="$index"
                        ref="list"
                        :class="[{'table-hovers':index===activeIndex,'table-hover':index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                    >
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s flex-container flex-jus-c flex-align-c">
                                <div class="cz-butt" :id="'cz-'+index" :class="{'active': isActive[index]}"
                                     @dblclick.stop>
                                    <span class="butt" @click.stop="czClick(index, $event)">操作</span>
                                    <transition name="top-bottom-fade">
                                        <div :style="objStyle" class="content flex-container flex-wrap-w"
                                             v-show="isActive[index]">
                                            <div :data-index="$index" v-if="setShow($index,list) == true"
                                                 v-for="(cz, $index) in czList" class="cont-butt"
                                                 @click="cz.clickBc(index,list)">
                                                {{ setName(cz.name,list ,$index) }}
                                            </div>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
              :next-more="nextMore"></page>
    </div>
</div>
<script type="text/javascript" src="jzgl.js"></script>
</body>
</html>
