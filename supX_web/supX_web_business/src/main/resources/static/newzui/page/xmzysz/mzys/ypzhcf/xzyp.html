<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>新增药品组合处方</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ypzhcf.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel tong-top flex-container   flex-align-c">
            <button v-waves class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button v-waves class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            <button v-waves class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="remove()">删除</button>
            <div class="flex-container   flex-align-c margin-l-10">
                    <label class="whiteSpace margin-r-1 ft-14 padd-r-5">检索</label>
                        <input class="zui-input wh180" placeholder="请输入关键字" v-model="param.parm" type="text" id="jsvalue" @keydown="goToPage(1)"/>
            </div>
    </div>
    <div class="zui-table-view " >
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </span></div></th>
                    <th><div class="zui-table-cell cell-l text-left"><span>组合医嘱名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分组号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>药品名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>频次</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>用药天数</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>用药方法</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单次计量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>用药总量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>输液速度</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>医生说明</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" :tabindex="$index">
                    <td  class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'one'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l text-left" v-text="item.zhyzmc">
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzh">
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 text-left" v-text="item.ypmc">
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.pcmc">
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yyts">
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yyffmc">
                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.dcjl">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yyzl">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.sysd+item.sysddw">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yssm">
                        </div>
                    </td>
                    <td class="cell-s"><div class="zui-table-cell cell-s">
                        <span style="display: flex;justify-content: center;align-items: center;">
                            <em class="width30"><i class="icon-bj" @click="edit($index)" data-title="编辑"></i></em>
                            <em class="width30"><i class="icon-sc icon-font" data-title="删除" @click="remove($index)"></i></em>
                           </span>
                    </div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                              :type="'all'" :val="isCheckAll">
                        </input-checkbox></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                              :type="'some'" :which="$index"
                                                                                              :val="isChecked[$index]">
                        </input-checkbox></div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-s"><div class="zui-table-cell cell-s" >
                            <span style="display: flex;justify-content: center;align-items: center;">
                            <em class="width30"><i class="icon-bj" @click="edit($index)" data-title="编辑"></i></em>
                            <em class="width30"><i class="icon-sc icon-font" data-title="删除" @click="remove($index)"></i></em>
                           </span>
                        </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>


    </div>

</div>
<div class="side-form ng-hide pop-548"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                    <i>组合医嘱</i>
                    <input type="text" class="label-input background-h" disabled  v-model="popContent.zhyzmc" @keydown="nextFocus($event)" />
            </li>
            <li>
                    <i>药品名称</i>
                    <input type="text" class="label-input "  data-notEmpty="true" v-model="ypContent.text" @keydown="changeDown($event,'text')"  @input="searching(false,'text',$event.target.value)" id="ypmc"/>
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
            </li>
            <li>
                    <i>频次</i>
                    <select-input @change-data="resultChange" :not_empty="false"
                                  :child="pcList" :index="'pcmc'" :index_val="'pcbm'" :val="popContent.pcbm"
                                  :name="'popContent.pcbm'" :search="true">
                    </select-input>
            </li>
            <li>
                    <i>用药天数 </i>
                    <input type="number" class="label-input"  v-model="popContent.yyts" @keydown="nextFocus($event)" data-notEmpty="false">
            </li>
            <li>
                    <i>用药方法</i>
                    <select-input @change-data="resultChange" :not_empty="false"
                                  :child="yyffList" :index="'yyffmc'" :index_val="'yyffbm'" :val="popContent.yyff"
                                  :name="'popContent.yyff'" :search="true">
                    </select-input>
            </li>
            <li>
                    <i>单次剂量</i>
                    <input type="number" class="label-input"  v-model="popContent.dcjl" @keydown="nextFocus($event)" data-notEmpty="false">
                    <span style="position: absolute;right: 10px;width: 20px;height: 36px;line-height: 36px;color:#1abc9c" v-text="popContent.jldwmc"></span>
            </li>
            <li>
                    <i>用药总量 </i>
                    <input type="number" class="label-input"  v-model="popContent.yyzl" @keydown="nextFocus($event)"
                           data-notEmpty="false">
                <span style="position: absolute;right: 10px;width: 20px;height: 36px;line-height: 36px;color:#1abc9c" v-text="popContent.yfdwmc"></span>
                    <!--<span style="width: 22px;" v-text="popContent.yfdwmc">ml</span>-->
            </li>
            <li>
                    <i>输液速度 </i>
                    <input type="number" class="label-input"  v-model="popContent.sysd" @keydown="nextFocus($event)" data-notEmpty="false">
            </li>
            <li>
                    <i>输液单位 </i>
                    <select-input @change-data="resultChange" :not_empty="false"
                                  :child="sydw_tran" :index="popContent.sysddw" :val="popContent.sysddw"
                                  :name="'popContent.sysddw'" :search="true">
                    </select-input>
            </li>
            <li>
                    <i>分组号 </i>
                    <select-input @change-data="resultChange" :not_empty="false"
                                  :child="fzh_tran" :index="popContent.fzh" :val="popContent.fzh"
                                  :name="'popContent.fzh'" :search="true">
                    </select-input>
            </li>
            <li>
                    <i>医生说明 </i>
                    <input type="text" class="label-input " v-model="popContent.yssm" @keydown.13="save"/>
            </li>

        </ul>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
    </div>
</div>
<style>
    .side-form-bg{
        background: none;
    }
</style>
<script src="xzyp.js"></script>
</body>
</html>
