var wzdw = new Vue({
	el: '#wzdw',
	mixins: [dic_transform, tableBase],
	data: {
		jsonList: [],
		dg: {
			page: 1,
			rows: 500,
			sort: "",
			order: "asc",
		},
	},
	methods: {
		getData: function() {
			//准备参数
			var json = {
				'tybz': 0,
			}
			$.getJSON("/actionDispatcher.do?reqUrl=WzkfXtwhWzdw&types=query&json=" +
				JSON.stringify(json) + "&dg=" + JSON.stringify(this.dg),
				function(data) {
					if(data.a == 0) {
						wzdw.totlePage = Math.ceil(data.d.total / wzdw.param.rows);
						wzdw.jsonList = data.d.list;
					} else {
						alert(data.c);
					}

				});
		},
		addData: function() {
			//设置操作类型
			wzdwPop.op = 'save';
			wzdwPop.popContent = {};
			wzdwPop.isShow = true;
		},
		edit: function(num) {
			wzdwPop.op = 'update';
			if(num == null) {
				for(var i = 0; i < this.isChecked.length; i++) {
					if(this.isChecked[i] == true) {
						num = i;
						break;
					}
				}
				if(num == null) {
					malert("请选中你要修改的数据");
					return false;
				}
			}
			wzdwPop.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));
			wzdwPop.isShow = true;
		},
		remove: function() {
			var wzdwList = [];
			for(var i = 0; i < this.isChecked.length; i++) {
				if(this.isChecked[i] == true) {
					wzdwList.push(this.jsonList[i]);
				}
			}
			if(wzdwList.length == 0) {
				malert("请选中您要删除的数据");
				return false;
			}
			if(!confirm("请确认是否删除")) {
				return false;
			}

			var json = {
				'list': wzdwList
			}
			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhWzdw&types=delete', JSON.stringify(json)).then(function(data) {
				if(data.body.a == 0) {
					malert("数据删除成功")
				} else {
					malert("数据删除失败");
				}
				wzdw.getData();
			}, function(error) {
				console.log(error);
			});
		},
	}
});
wzdw.getData();

//弹出层
var wzdwPop = new Vue({
	el: '#wzdwPop',
	mixins: [dic_transform, baseFunc],
	data: {
		isShow: false,
		popContent: {},
		isKeyDown: null,
		title: '物资单位',
		op: 'save'
	},
	methods: {
		saveData: function() {
			//非空判断
			if(this.popContent['tybz'] == null || this.popContent['tybz'] == undefined) {
				malert("停用标志输入不正确");
				return;
			};
			if(this.popContent['dwmc'] == null || this.popContent['dwmc'] == undefined) {
				malert("单位名称输入不正确");
				return;
			};

			this.$http.post('/actionDispatcher.do?reqUrl=WzkfXtwhWzdw&types=' + this.op, JSON.stringify(this.popContent)).then(function(data) {
				if(data.body.a == 0) {
					wzdw.getData();
					wzdwPop.isShow = false;
					wzdwPop.isAdd = false;
					malert("数据保存成功")
				} else {
					malert("数据提交失败");
				}
			}, function(error) {
				console.log(error);
			});
		}
	}
});

//验证是否为空
$('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function() {
	if($(this).val() == '' || $(this).val() == null) {
		$(this).addClass("emptyError");
	} else {
		$(this).removeClass("emptyError");
	}
});

//为table循环添加拖拉的div
var drawWidthNum = $(".wzdw tr").eq(0).find("th").length;
for(var i = 0; i < drawWidthNum; i++) {
	if(i >= 2) {
		$(".wzdw th").eq(i).append("<div onmousedown=drawWidth(event) class=drawWidth></div>");
	}
}