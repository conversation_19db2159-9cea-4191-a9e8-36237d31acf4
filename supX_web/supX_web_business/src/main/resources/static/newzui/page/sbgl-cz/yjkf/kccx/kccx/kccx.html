<html>

<head>
    <title>库存查询</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <script src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="kccx.css">
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div  class="wrapper" id="wrapper" v-cloak>
    <div class="tong-top">
        <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        <button class="tong-btn btn-parmary-b  paddr-r5" @click="exportKc()">导出</button>
        <vue-checkbox class="padd-r-20" @result="reCheckOne" :new-text="'按设备汇总'" :val="'queryType'"
                      :new-value="queryType"></vue-checkbox>
    </div>
    <div class="tong-search">
        <div class="flex-container padd-t-10 padd-b-10">
            <div class="flex-container flex-align-c padd-r-10">
                <span class=" ft-14 padd-r-5 whiteSpace">库房</span>
                <select-input class="wh120" @change-data="yfkfChange" :not_empty="false"
                              :child="sbkfList" :index="'sbkfmc'" :index_val="'sbkfbm'" :val="param.sbkf"
                              :name="'param.sbkf'">
                </select-input>
            </div>
            <input v-model="param.parm" class="zui-input  wh150" @keydown.13="goToPage(1)" placeholder="请输入"/>
        </div>
    </div>
    <div class="zui-table-view" v-cloak>
        <!--入库列表-->
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell"><span>序号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-xl"><span>设备编码</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>设备名称</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>设备规格</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>批次停用</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>库存数量</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>设备进价</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>设备批号</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>库房单位</span></div>
                    </th>
                </tr>
                </thead>
            </table>

        </div>
        <div class="zui-table-body ">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item,$index) in jsonList" @click="checkOne($index,item)" @dblclick="edit($index)" :tabindex="$index"   :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('hoverIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('activeIndex',true,$index)">
                    <td class=" cell-m">
                        <div class="zui-table-cell" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl" v-text="item.sbbm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s " v-text="item.sbmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.sbgg"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="stopSign[item.pcty]"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kcsl"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.jj,2)"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.scph"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div>
                    </td>

                    <!--暂无数据提示,绑数据放开-->
                    <p v-show="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
              :next-more="nextMore"></page>
    </div>

    <model  :s="'保存'" :c="'退出'"  @default-click="saveData" @result-clear="mbModel=false" :model-show="true" @result-close="mbModel=false"
            v-if="mbModel" :title="'有效期、批次停用修改'">
        <div class="bqcydj_model">
            <div class="flex-container flex-wrap-w">
                <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                    <span class="padd-r-5 whiteSpace">有效期</span>
                    <input class="zui-input" onclick="WdatePicker({ minDate:'%y-%M-%d' })"
                           @blur="dateForVal($event, 'popContent.xxq')" data-notEmpty="false"
                           v-model="popContent.xxq" @keydown="nextFocus($event)"/>
                </div>
                <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                    <span class="padd-r-5 whiteSpace">批次停用</span>
                    <select-input @change-data="resultChange" :not_empty="true" :child="stopSign"
                                  :index="popContent.xpcty" :val="popContent.xpcty"
                                  :name="'popContent.xpcty'">
                    </select-input>
                </div>
            </div>
        </div>
    </model>
</div>
</body>
<script type="text/javascript" src="kccx.js"></script>

</html>
