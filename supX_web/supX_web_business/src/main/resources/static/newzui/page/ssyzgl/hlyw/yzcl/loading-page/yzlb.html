<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="yzlb.css">
</head>
<body class="skin-default  padd-l-10 padd-r-10 padd-b-10 padd-t-10">
<div class="wrapper" id="loadingPage" v-cloak="">
    <div v-for="(brListItem, brListIndex) in yzzxInfoList" class="bryz-list">
        <header class="userNameBg">
            <div class="flex">
                <div class="userNameImg">
                     <span v-show="brListItem.nljd==1">
                    		<img src="/newzui/pub/image/maleBaby.png">
                    	</span>
                    	<span v-show="brListItem.nljd==2">
                    		<img src="/newzui/pub/image/femalebaby.png">
                    	</span>
                    	<span v-show="brListItem.nljd==3">
                    		<img src="/newzui/pub/image/Group <EMAIL>">
                    	</span>
                    	<span v-show="brListItem.nljd==4">
                    		<img src="/newzui/pub/image/Group <EMAIL>">
                    	</span>
                    	<span v-show="brListItem.nljd==5">
                    		<img src="/newzui/pub/image/juvenile.png">
                    	</span>
                    	<span v-show="brListItem.nljd==6">
                    		<img src="/newzui/pub/image/maid.png">
                    	</span>
                    	<span v-show="brListItem.nljd==7">
                    		<img src="/newzui/pub/image/youth.png">
                    	</span>
                    	<span v-show="brListItem.nljd==8">
                    		<img src="/newzui/pub/image/woman.png">
                    	</span>
                    	<span v-show="brListItem.nljd==9">
                    		<img src="/newzui/pub/image/grandpa.png">
                    	</span>
                    	<span v-show="brListItem.nljd==10">
                    		<img src="/newzui/pub/image/grandma.png">
                    	</span>
                        <span v-show="brListItem.nljd==11">
                            <img src="/newzui/pub/image/<EMAIL>">
                        </span>
                </div>
                <div class="text-color">
               		<p class="userHeader">
                        <span class="userName" v-text="brListItem.brxm"></span>
                        <span class="sex text" v-text="brxb_tran[brListItem.brxb]"></span>
                        <span class="nl text">{{brListItem.nl}}{{xtwhnldw_tran[brListItem.nldw]}}</span>
                    </p>
                    <div class="userCwh">
                        <span class="cwh text" v-text="'床位号：'+ brListItem.rycwbh +'号'"></span>
                        <span class="zyh text" v-text="'住院号：'+ brListItem.brzyh"></span>
                        <span class="bq text" v-text="'科室：'+ brListItem.ryksmc"></span>
                        <span class="ys text" v-text="'医师：'+ brListItem.zyysxm"></span>
                        <span class="brzt text" v-text="'病人状态：'+zyzt_tran[brListItem.zyzt]"></span>
                        <span class="bz text" v-text="'病种：'+ brListItem.ryzdmc"></span>
                    </div>
                    <!--<div class="userCwh">-->
                        <!--<span class="fyhj text" v-text="'费用合计：'+ brListItem.fyhj"></span>-->
                        <!--<span class="yjhj text" v-text="'预交合计：'+ brListItem.yjhj"></span>-->
                        <!--<span class="zyts text" v-text="'住院天数：'+ brListItem.zyts"></span>-->
                        <!--<span class="phone text" v-text="'联系电话：'+ brListItem.lxdh"></span>-->
                        <!--<span class="sfz text"  v-text="'身份证：'+ brListItem.sfzh"></span>-->
                    <!--</div>-->
                    <!--<div class="userFooter">-->
                        <!--<span class="hl text" v-text="'护理等级：'"></span>-->
                        <!--<span class="wz text">危重：危重</span>-->
                    <!--</div>-->
                    <div>
                        <p class="heaf text">更多详细信息>></p>
                    </div>
                </div>
            </div>
            <div class="blRight">
                <span class="yzdImg" data-title="医嘱单" @click="yzd"></span>
                <span class="yzzxjlImg" data-title="医嘱执行记录" @click="yzzxd"></span>
                <span class="psjglrImg" data-title="皮试结果录入" @click="lr"></span>
            </div>
        </header>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">医嘱类型</label>
                    <div class="zui-input-inline wh120">
                        <select-input @change-data="resultChangeYzlx"
                                      :data-notEmpty="true"
                                      :child="yzlx_tran01"
                                      :index="yzlx"
                                      :val="yzlx"
                                      :name="'yzlx'"
                                      :search="true"></select-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view  margin-l-10 margin-r-10">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>医嘱类型</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>下嘱时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xxl text-left"><span>医嘱内容</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>剂量单位</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>用量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>用量单位</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>用法</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>频次</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>医生签名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>护士签名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>医生停嘱时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>护士停嘱时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left title"><span>说明</span></div>
                        </th>
                          <th>
                            <div class="zui-table-cell cell-xl"><span>状态</span></div>
                        </th>
                        <!--
                        <th>
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                        -->
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <p v-if="!brListItem.yzxx.length" class=" noData  text-center zan-border">暂无数据...</p>
                <table v-if="brListItem.yzxx.length" class="zui-table table-width50">
                    <tbody>
                    <!--  -->
                    <tr v-for="(yzListItem,$index) in brListItem.yzxx"
                        :tabindex="$index"
                        @click="reCheckBoxZx(brListIndex,$index)"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        class="tableTr2" ref="list">
                        <!-- 红色red   灰色huise 在tr上 -->
                        <td>
                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                        </td>
                        <td :class="[ yzListItem.tzbj ]">
                            <div class="zui-table-cell cell-xxl text-left title" v-text="yzListItem.xmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="yzListItem.sl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="yzListItem.pcmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="yzListItem.ysqmxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="yzListItem.zxhsxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ystzsj,'shortY')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.hstzsj,'shortY')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left title" v-text="yzListItem.yssm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl zt">
                                <span class="zt-dsh" v-text="yzListItem.yzzt" >待审核</span>
                                <!--<span class="zt-dzx">待执行</span>-->
                                <!--<span class="zt-dly">待领药</span>-->
                                <!--<span class="zt-yly">已领药</span>-->
                                <!--<span class="zt-dtz">待停嘱</span>-->
                                <!--<span class="zt-ytz">已停嘱</span>-->
                            </div>
                        </td>
                        <!--
                        <td>
                            <div class="zui-table-cell cell-s">
                                <span data-title="审核" class="icon-width icon-sh-h"></span>
                                <span data-title="取消审核" class="icon-width icon-quxiaoshenhe"></span>
                                <span data-title="执行" class="icon-width icon-zhixing"></span>
                                <span data-title="取消执行" class="icon-width icon-zhixing"></span>
                                <span data-title="领药" class="icon-width icon-lingyao"></span>
                                <span data-title="退药" class="icon-width icon-tyHsz"></span>
                            </div>
                        </td>
                        -->
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr  v-for="(yzListItem,$index) in brListItem.yzxx" :tabindex="$index"
                            @click="reCheckBoxZx(brListIndex,$index)"
                             :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                             @mouseenter="hoverMouse(true,$index)"
                             @mouseleave="hoverMouse()"
                             class="tableTr2">
                            <td>
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-xl"><span>状态</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr  v-for="(yzListItem,$index) in brListItem.yzxx" :tabindex="$index"
                             @click="reCheckBoxZx(brListIndex,$index)"
                             :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                             @mouseenter="hoverMouse(true,$index)"
                             @mouseleave="hoverMouse()"
                             class="tableTr2">
                            <td>
                                <div class="zui-table-cell cell-xl zt">
                                    <span class="" :class="yzListItem.yzzt=='待审核'?'color-dsh':yzListItem.yzzt=='已审核停嘱'?'color-ysh':yzListItem.yzzt=='已执行'?'zt-dzx':''" v-text="yzListItem.yzzt" >待审核</span>
                                    <!--<span class="zt-dzx">待执行</span>-->
                                    <!--<span class="zt-dly">待领药</span>-->
                                    <!--<span class="zt-yly">已领药</span>-->
                                    <!--<span class="zt-dtz">待停嘱</span>-->
                                    <!--<span class="zt-ytz">已停嘱</span>-->
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="side-form pop-850" style="padding-top: 0;" id="lr" v-cloak="" role="form" :class="{'ng-hide': !isShow}">
    <div class="fyxm-side-top">
        <span>皮试结果录入</span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class=" grid-box">
        <div class="grid-box">
            <div class=" flex margin-l-10">
                <div class="flex margin-b-20 margin-top-10 alignItems margin-l-10">
                    <label class="whiteSpace margin-r-5 ft-14">时间段</label>
                    <div class="zui-input-inline margin-l13">
                        <span class="icon-position icon-rl"></span>
                        <input class="zui-input todate wh220 text-indent20" placeholder="不限定时间范围" id="timeValPs"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view margin-l-10 margin-r-10" z-height="full">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>组号</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>项目名称</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>药品规格</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>单次剂量</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>剂量单位</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>用药方法</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>皮试人员</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>皮试时间</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>皮试结果</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <p v-if="!psYzList.length" class=" noData  text-center zan-border">暂无数据...</p>
                <table v-if="psYzList.length" class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in psYzList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                    	@click="checkSelect([$index,'some','jsonList'],$event)"
                    	:tabindex="$index"
                    	ref="list">
                        <td><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.fzh"></div></td>
                        <td><div class="zui-table-cell cell-xl"  v-text="item.xmmc"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.ypgg"></div></td>
                        <td><div class="zui-table-cell cell-l"  v-text="item.dcjl"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.jldwmc"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.yyffmc"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.pslrry"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="fDate(item.pslrsj,'yyyy-MM-dd')"></div></td>
                        <td>
                        	<div class="zui-table-cell cell-l" over-auto style="height: auto;">
                         			<select-input @change-data="resultChange_save" :not_empty="false" :child="psjg_tran"
                                                  :index="'item.psjg'" :val="item.psjg" :name="$index + '.psjg'"
                                                  :search="true">
                                    </select-input>
                        	</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <!--<div class="zui-table-fixed table-fixed-l">-->
                <!--<div class="zui-table-header">-->
                    <!--<table class="zui-table">-->
                        <!--<thead>-->
                        <!--<tr>-->
                            <!--<th><div class="zui-table-cell cell-m"><span>序号</span></div></th>-->
                        <!--</tr>-->
                        <!--</thead>-->
                    <!--</table>-->
                <!--</div>-->
                <!--<div class="zui-table-body" @scroll="scrollTableFixed($event)">-->
                    <!--<table class="zui-table">-->
                        <!--<tbody>-->
                        <!--<tr v-for="(item, $index) in psYzList"-->
                            <!--:class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"-->
                            <!--@mouseenter="hoverMouse(true,$index)"-->
                            <!--@mouseleave="hoverMouse()"-->
                            <!--@click="checkSelect([$index,'some','jsonList'],$event)"-->
                            <!--:tabindex="$index"-->
                            <!--ref="list">-->
                            <!--<td><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>-->
                        <!--</tr>-->
                        <!--</tbody>-->
                    <!--</table>-->
                <!--</div>-->
            <!--</div>-->

            <!--<div class="zui-table-fixed table-fixed-r">-->
                <!--<div class="zui-table-header">-->
                    <!--<table class="zui-table">-->
                        <!--<thead>-->
                        <!--<tr>-->
                            <!--<th><div class="zui-table-cell cell-l"><span>皮试结果</span></div></th>-->
                        <!--</tr>-->
                        <!--</thead>-->
                    <!--</table>-->
                <!--</div>-->
                <!--<div class="zui-table-body" @scroll="scrollTableFixed($event)">-->
                    <!--<table class="zui-table">-->
                        <!--<tbody>-->
                        <!--<tr v-for="(item, $index) in psYzList"-->
                            <!--:class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"-->
                            <!--@mouseenter="hoverMouse(true,$index)"-->
                            <!--@mouseleave="hoverMouse()"-->
                            <!--@click="checkSelect([$index,'some','jsonList'],$event)"-->
                            <!--:tabindex="$index"-->
                            <!--ref="list">-->
                            <!--<td>-->
                            <!--<div class="zui-table-cell cell-l">-->
                         			<!--<select-input @change-data="resultChange_save" :not_empty="false" :child="psjg_tran"-->
                                                  <!--:index="'item.psjg'" :val="item.psjg" :name="$index + '.psjg'"-->
                                                  <!--:search="true">-->
                                    <!--</select-input>-->
                        	<!--</div>-->
                            <!--</td>-->
                        <!--</tr>-->
                        <!--</tbody>-->
                    <!--</table>-->
                <!--</div>-->
            <!--</div>-->
        </div>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" >确定</button>
    </div>
</div>

<div class="side-form pop-850"  id="yzzxd" v-cloak="" role="form" :class="{'ng-hide': !isShow}">
    <div class="fyxm-side-top">
        <span>医嘱执行单</span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="grid-box yzlb-height flex-container flex-dir-c padd-l-10 padd-r-10">
        <div class="grid-box">
            <div class=" col-x-3 flex padd-l-10">
                <div class="flex margin-b-20 margin-top-10 alignItems margin-l-10">
                    <label class="whiteSpace margin-r-5 ft-14">医嘱类型</label>
                    <select-input cs="true" @change-data="resultChangeYz" :data-notEmpty="false"
                                  :child="yzlx_tran01" :index="jsContent.yzlx" :val="jsContent.yzlx"
                                  :name="'jsContent.yzlx'">
                    </select-input>
                </div>
            </div>
            <div class=" col-x-4 flex ">
                <div class="flex margin-b-20 margin-top-10 alignItems margin-l-10">
                    <label class="whiteSpace margin-r-5 ft-14">执行单类型</label>
                    <select-input @change-data="resultChangeZx" :data-notEmpty="false"
                                  :child="zxdlx_tran2" :index="jsContent.zxdlx" :val="jsContent.zxdlx"
                                  :name="'jsContent.zxdlx'">
                    </select-input>
                </div>
            </div>
            <div class=" col-x-4 flex ">
                <div class="flex margin-b-20 margin-top-10 alignItems margin-l-10">
                    <label class="whiteSpace margin-r-5 ft-14">时间段</label>
                    <div class="zui-input-inline">
                        <span class="icon-position icon-rl"></span>
                        <input class="zui-input todate wh220 text-indent20" placeholder="不限定时间范围" id="timeval"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view ">
            <div class="zui-table-header">
            	<!--<table cellspacing="0" cellpadding="0" style="width: 100%;font-size: 12px;">-->
                <!--&lt;!&ndash; 基本信息 &ndash;&gt;-->
                <!--<tr>-->
                    <!--<th style="width: 32px;padding-left: 24px">姓名:</th>-->
                    <!--<td v-text="item.brxm"></td>-->
                    <!--<th>性别:</th>-->
                    <!--<td v-text="brxb_tran[item.brxb]"></td>-->
                    <!--<th>年龄:</th>-->
                    <!--<td v-text="item.nl"></td>-->
                    <!--<th>科室:</th>-->
                    <!--<td v-text="item.ryksmc"></td>-->
                    <!--<th>床位号:</th>-->
                    <!--<td v-text="item.ywckbh"></td>-->
                    <!--<th>住院号:</th>-->
                    <!--<td v-text="item.zyh"></td>-->
                <!--</tr>-->
            	<!--</table>-->
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>分组号</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>医嘱类型</span></div></th>
                        <th><div class="zui-table-cell cell-xxl text-left"><span>医嘱项目</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>剂量</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>剂量单位</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>执行护士</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>用法</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>频次</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>备注</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>皮试结果</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>皮试人员</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>皮试时间</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body yzzxd-body" @scroll="scrollTable($event)">
                <table class="zui-table table-width50" v-for="item in zxjlList" v-if="zxjlList.length">
                    <tbody>
                    <tr v-for="(zxdItem, $index) in item.yzxx"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)"
                    	:tabindex="$index"
                    	ref="list">
                        <td><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="zxdItem.fzh"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="yzlx_tran[zxdItem.yzlx]"></div></td>
                        <td><div class="zui-table-cell cell-xxl text-left" v-text="zxdItem.xmmc"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="zxdItem.jl"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="zxdItem.jldwmc"></div></td>
                        <td><div class="zui-table-cell cell-l"  v-text="zxdItem.zxhsxm"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="zxdItem.yyffmc"></div></td>
                        <td><div class="zui-table-cell cell-l"  v-text="zxdItem.pcmc"></div></td>
                        <td><div class="zui-table-cell cell-l"></div></td>
                        <td><div class="zui-table-cell cell-l"  v-text="psjg_tran[zxdItem.psjg]"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="zxdItem.pslrry"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="fDate(zxdItem.pslrsj,'yyyy-MM-dd')"></div></td>
                    </tr>
                    </tbody>
                </table>
                <p v-if="!zxjlList.length" class=" noData  text-center zan-border">暂无数据...</p>
            </div>
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50"  v-for="item in zxjlList" v-if="zxjlList.length">
                        <tbody>
                        <tr v-for="(zxdItem, $index) in item.yzxx"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)"
                            :tabindex="$index">
                            <td><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-show="jsContent.zxdlx=='all'" class="zui-btn btn-primary xmzb-db syt" @click="">打印</button>
        <button v-show="jsContent.zxdlx=='syt'" class="zui-btn btn-primary xmzb-db syt" @click="">输液贴打印</button>
        <button class="zui-btn btn-primary xmzb-db" >确定</button>
    </div>
</div>

<script src="yzlb.js"></script>
</body>
</html>
