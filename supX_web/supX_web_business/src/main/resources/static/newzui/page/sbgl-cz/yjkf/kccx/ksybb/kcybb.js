var tableInfo = new Vue({
	el: '#crcx',
	//混合js字典庫
	mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
	data: {
		jsonList: [],
		yfkfList: [],
		yfkf: 0, //药房库房信息
		param: {
			'page': 1,
			'rows': 100,
			'kfbm': '',
			'beginrq': null,
			'endrq': null,
		}
	},
	mounted: function() {
		//初始化页面记载库房
		$.getJSON('/actionDispatcher.do?reqUrl=CsqxAction&types=qxwzkf&parm={"ylbm":"N050080022003"}',
			function(data) {
				if(data.a == 0) {
					if(data.d.length > 0) {
						tableInfo.yfkfList = data.d;
					}
				} else {
					malert("药库获取失败");
				}
			});

	},
	methods: {
		//药房库房改变
		yfkfChange: function() {
			//			if(this.yfkf == 0) return;
			//			//重新获取列表
			this.getData();
		},
		//获取数据
		getData: function() {
			if (tableInfo.param.beginrq ==null || tableInfo.param.endrq == null) {
				malert('请选择日期');
				return;
			}
			//设置库房
			this.param.wzkf = this.yfkf == 0 ? null : this.yfkf
			this.param.kfbm = this.yfkf == 0 ? null : this.yfkf
			$.getJSON("/actionDispatcher.do?reqUrl=WzkfKfywKccx&types=ybb&parm=" + JSON.stringify(this.param), function(json) {
				if(json.a == "0") {
//					tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
//					tableInfo.jsonList = json.d.list;
					tableInfo.jsonList = json.d;
				}
			});
		}
	}
});
window.getTime = function(event, type) {
	if(type == 'star') {
		tableInfo.param.beginrq = $(event).val();
	} else if(type == 'end') {
		tableInfo.param.endrq = $(event).val();
	}
};
