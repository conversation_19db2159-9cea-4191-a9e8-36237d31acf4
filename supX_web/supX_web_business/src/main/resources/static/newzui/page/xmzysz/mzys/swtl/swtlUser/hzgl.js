var userNameBg=new Vue({
    el:'.userNameBg',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        Brxx_List:[],
    },

    methods:{

    },
    create(){

    },
})
var content=new Vue({
    el:'#content',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat,scrollOps],
    data:{
        objAvatar:8,
        edit:false,
        editText:true,
        tipsShow:false,
        tipsShow1:false,
        userName:false,
        previewshow:false,
        Brxx_List:[],
        objabsolute:{},
        popContent:{},
        defaltObj:{
            title:'死亡讨论邀请',
            cs:'background-f4b26b color-8e9694',
            cb:'拒绝',
            sb:'接受'
        },
        subObj:{
            title:'死亡讨论邀请',
            cs:'',
            cb:'',
            sb:''
        },
        refuseObj:{
            title:'拒绝原因',
        },
    },
    mounted(){
        this.popContent.shsj =this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times');
        console.log(this.shsj);
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    content.popContent.shsj = value;
                } else {
                    content.popContent.shsj = '';
                }
            }
        });
    },
    methods:{
        tjys:function(){
            brzcList.type=false
        },
        rowsJ:function(event){
                if(event.keyCode==13){
                    event.srcElement.rows=event.srcElement.rows+1
                }else if(event.keyCode==8){
                    if(event.srcElement.rows!=1){
                        event.srcElement.rows=event.srcElement.rows-1
                    }
                }
        },
        editShow:function(){
            this.editText=!this.editText
        },
        preview:function(){
            this.previewshow=true
        },
        previewHide:function(){
            this.previewshow=false
        },
        hoverName:function (falg,index,event){
            this.objabsolute.left=event!=undefined?event.clientX-80:0
            this.objabsolute.top=event!=undefined?event.clientY+20:0
                this.userName= falg==true ? true:falg
            console.log(this.objabsolute)
            this.$forceUpdate()
        },
        accept(){
            common.openConfirm('确定接受患者：<span class="color-green">'+"李浩然"+'</span>的死亡讨论吗？',function () {

            },function () {
                common.openConfirm('<textarea placeholder="请输入拒绝原因" class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="refuseText"></textarea>',function () {

                },function () {

                },content.refuseObj)
            },this.defaltObj)
        },
        submit(){
            common.openConfirm('确定审核患者：<span class="color-green">'+"李浩然"+'</span>的死亡讨论吗？',function () {

            },function () {
            },this.subObj)
        },
    },
    create(){

    },
})
var brzcList=new Vue({
    el:'#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        type:true,
        popContent:{}
    },
    create(){

    },
    methods:{
        fqtl:function () {

        },
        getData:function(){

        },
        submit:function () {
            this.type=true
        },
        close:function () {
            this.type=true
        }
    },
})