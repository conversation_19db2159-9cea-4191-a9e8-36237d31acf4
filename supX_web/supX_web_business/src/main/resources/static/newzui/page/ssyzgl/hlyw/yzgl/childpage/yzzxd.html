<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>医嘱执行单</title>
<!--<link href="/page/zyys/ysyw/yzblcl/yzblcl.css" rel="stylesheet" type="text/css"/>-->
<link rel="stylesheet" href="childpage/yzzxdprint.css" type="text/css" media="print"/>
<link href="childpage/yzzxd.css" rel="stylesheet" type="text/css" media="screen"/>
<div class="flex-container  padd-l-5   height-100">
    <div class="   flex-container flex-dir-c printHide" id="bodyMenu" v-cloak :data-id="isChecked">
        <!--检索-->
        <div class="enter_tem1 zxTime printHide flex-container flex-align-c printHide" style="padding: 6px 0">
            <span>执行日期:</span>
            <input class="zui-input wh182" type="text" data-select="no" v-model="ksrq" id="dbegin"
                   @keydown="searchListHc">
            <span class="padd-r-5 padd-l-5">至</span>
            <input class="zui-input wh182" type="text" data-select="no" v-model="jsrq" id="dEnd"
                   @keydown="searchListHc">
            <button class="root-btn btn-parmary" @click="today()">今天</button>
            <button class="root-btn btn-parmary-71" @click="tomorrow()">明天</button>
            <!--<label style="margin-left: 30px"><input name="isInHol" type="radio" v-model="yzlx" value="qb" @change="YzlxChange('qb')"><span>全部</span></label>-->
            <div class="flex-container flex-align-c">
                <div class="position padd-r-5" @change="YzlxChange('qb')"><input v-model="picked" type="radio" id="one"
                                                                                 name="one" value="0" class="zui-radio">
                    <label for="one" class="padd-r-5"></label></div>
                <div class="lj-pop-text">全部</div>
            </div>
            <div class="flex-container flex-align-c">
                <div class="position padd-r-5" @change="YzlxChange('ls')"><input v-model="picked" type="radio" id="two"
                                                                                 name="one" value="1" class="zui-radio">
                    <label for="two" class="padd-r-5"></label></div>
                <div class="lj-pop-text">临时</div>
            </div>
            <div class="flex-container flex-align-c">
                <div class="position padd-r-5" @change="YzlxChange('cq')">
                    <input v-model="picked" type="radio"  id="three" name="one" value="5"  class="zui-radio">
                    <label for="three" class="padd-r-5"></label>
                </div>
                <div class="lj-pop-text">长期</div>
            </div>
            <div class="flex-container flex-align-c margin-l-10">
                <span class="font-14  padd-r-5">输液滴速</span>
                <select-input class="wh70" @change-data="resultChangeSysd" :child="sysdfw_tran"
                              :index="searchContent.sysd"
                              :val="searchContent.sysd" :name="'searchContent.sysd'" :search="true">
                </select-input>
            </div>
            <div class="flex-container flex-align-c margin-l-10">
                <span class="font-14  padd-r-5">停嘱</span>
                <select-input class="wh70" @change-data="resultChangeSysd" :child="pdystzbz_tran"
                              :index="searchContent.ystzbz"
                              :val="searchContent.ystzbz" :name="'searchContent.ystzbz'" :search="true">
                </select-input>
            </div>
            <div class="flex-container flex-align-c margin-l-10">
                <span class="margin-r-10 font-14 padd-r-5">是否打印</span>
                <select-input class="wh70" @change-data="resultChangeSysd" :not_empty="false"
                              :child="IsPrint_tran" :index="searchContent.sytprint" :val="searchContent.sytprint"
                              :name="'searchContent.sytprint'" >
                </select-input>
            </div>
			<div class="flex-container flex-align-c margin-l-10"  v-show="num==3">
			    <span class="font-14  padd-r-5">作废</span>
			    <select-input class="wh70" @change-data="resultChangeSysd" :child="Iszf"
			                  :index="searchContent.zfbz"
			                  :val="searchContent.zfbz" :name="'searchContent.zfbz'" :search="true">
			    </select-input>
			</div>
            <!--<label style="margin-left: 10px"><input name="isInHol" type="radio" v-model="yzlx" value="ls" @change="YzlxChange('ls')"><span>临时</span></label>-->
            <!--<label style="margin-left: 10px"><input name="isInHol" type="radio" v-model="yzlx" value="cq" @change="YzlxChange('cq')"><span>长期</span></label>-->
        </div>

        <!-- 功能按钮 -->
        <div class=" printHide flex-container flex-wrap-w flex-align-c InfoMenu padd-t-10 padd-l-10">
            <tabs :num="num" :tab-child="tabArr" @tab-active="tabBg"></tabs>
            <button @click="showLsYz" class="tong-btn btn-parmary-b  paddr-r5">皮试结果录入</button>
			<!--<button @click="showYzZxjl" class="tong-btn btn-parmary-b  paddr-r5">执行修改</button>-->
            <button @click="printFormat" v-if="num !=0" class="tong-btn btn-parmary-b -sx paddr-r5">打印</button>
            <button @click="printFormatA4" v-if="num ==2 || num ==3 || num == 7" class="tong-btn btn-parmary-b -sx paddr-r5">输液卡</button>
            <button @click="printFormatXsk" v-if="num == 7" class="tong-btn btn-parmary-b -sx paddr-r5">巡视卡</button>
            <div class="  flex-container paddr-r5">
                <span class="padd-r-5 ft-14">是否补打</span>
                <input id="yjbz" class="green" type="checkbox" v-model="sfbd">
                <label for="yjbz"></label>
            </div>

            <!--<button @click="printSyt" v-if="num==6"  class="tong-btn btn-parmary-f2a -sx paddr-r5" style="float: right">打印输液贴</button>-->
        </div>
        <!-- 输液贴 -->
        <div v-if="num==7" class="flex-one flex-container flex-dir-c">
            <div class="flex-container printHide padd-t-10 padd-b-10">
                <button class="tong-btn btn-parmary-b paddr-r5" @click="sytNumberOfColumns=1">一栏式</button>
                <button class="tong-btn btn-parmary-b paddr-r5" @click="sytNumberOfColumns=3">三栏式</button>
            </div>
            <div class="over-auto printHide">
                <div class="flex-one syt-col-box flex-container flex-wrap-w "
                     :class="{'col-one': sytNumberOfColumns==1,'col-three': sytNumberOfColumns==3}">
                    <div class="syt-item" v-for="(item,$index) in yzzxdList" :data-index="$index">
                        <div style="display:flex;flex-direction: row;justify-content: center;">
                            <input-checkbox @result="reCheckBox1" :list="'yzzxdList'" :type="'some'" :which="$index"
                                            :val="isChecked1[$index]">
                            </input-checkbox>
                            <span class="margin-l-10">打印次数：{{item.yzxx[0].sytprint}} 次</span>
                        </div>

                        <div class="flex-container syt-top">
                            <span class="flex-one" v-text="item.yzlxmc"></span>
                            <span class="flex-one font-bold" v-text="item.rycwbh"></span>
                            <span class="flex-tow text-center font-bold" v-text="item.brxm"></span>
                            <span class="flex-tow text-center" v-text="item.brnl"></span>
                        </div>
                        <div class="syt-center">
                            <div class="flex-container" :id="mx.fzxh" v-for="mx in item.yzxx">
                                <div class="flex-one">{{mx.xmmc}}({{mx.ypgg}})</div>
                                <div >{{mx.jl}}{{mx.jldwmc}}</div>
                            </div>
                        </div>
                        <div class="syt-bottom flex-container">
                            <div class="flex-one" v-text="item.yyffmc"></div>
                            <div class="flex-one text-center" v-text="item.pcmc"></div>
                            <div class="flex-one text-right" v-text="item.sysd"></div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
        <!-- 医嘱 -->
        <div class="zxdDiv printHide padd-t-10" v-else>
            <!-- 全部医嘱执行单 -->
            <div class="zui-table-view  hzList padd-l-10 padd-r-10" v-for="(item,brListIndex) in yzzxdList"
                 v-if="item.yzxx.length != 0">
                <!-- <div class="flex-container padd-b-5">
                    姓名：<span class="font-14-654 padd-r-18" v-text="item.brxm"></span>
                    性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[item.brxb]"></span>
                    年龄：<span class="font-14-654 padd-r-18">
                    {{(item.nl?(item.nl + nldw_tran[item.nldw]):'' )
		+ (item.nl2?(item.nl2 + nldw_tran[item.nldw2]):'')}}</span>
                    床位号：<span class="font-14-654 padd-r-18">{{item.rycwbh}}床</span>
                    住院号：<span class="font-14-654 padd-r-18">{{item.zyh}}</span>
                </div>-->
				
				<div class="yzd-brInfo" :index="index" primaryKey="yzd-brInfo">
					<div>
					    <span>姓名:</span>
					    <span>{{item.brxm}}</span>
					</div>
					<div>
					    <span>性别:</span>
					    <span>{{brxb_tran[item.brxb]}}</span>
					</div>
					
					<div>
					    <span>年龄:</span>
					    <span>{{item.nl}}{{nldw_tran[item.nldw]}}{{item.nl2?(item.nl2 + '' + nldw_tran[item.nldw2]):''}}</span>
					</div>
				    <div>
				        <span>病室:</span>
				        <span>{{item.ryksmc}}</span>
				    </div>
				    <div>
				        <span>床号:</span>
				        <span>{{item.rycwbh}}</span>
				    </div>
				    <div>
				        <span>住院号:</span>
				        <span>{{item.zyh}}</span>
				    </div>
				</div>
				
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m" v-if="item.yzxx.length>=1">
                                <div class="zui-table-cell cell-m">
                                    <input class="green" type="checkbox" v-model="item.isCheckAll">
                                    <label @click="reCheckBoxSon(brListIndex)"></label>
                                </div>
                            </th>
                            <th class="cell-l">
                                <div class="zui-table-cell cell-l"><span>开始时间</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-m"><span>分组号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell  cell-s"><span>医嘱类型</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xxxl"><span>医嘱项目</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>剂量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">剂量单位</div>
                            </th>
							<th>
							    <div class="zui-table-cell cell-s">发送护士</div>
							</th>
                            <th>
                                <div class="zui-table-cell cell-s">执行护士</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">用法</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">频次</div>
                            </th>
							<th>
							    <div class="zui-table-cell cell-l">停嘱时间</div>
							</th>
                            <!--                            <th><div class="zui-table-cell cell-s">备注</div></th>-->
                            <th>
                                <div class="zui-table-cell cell-s">皮试结果</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">皮试人员</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l">皮试时间</div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div data-no-change class="zui-table-body zuiTableBody" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50" v-if="item.yzxx.length!=0">
                        <tbody>
                        <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            :tabindex="$index"
                            v-for="(zxdItem, $index) in item.yzxx"
                        >
                            <td class="cell-m" v-if="item.yzxx.length>=1">
                                <div class="zui-table-cell cell-m">
                                    <input class="green" type="checkbox" v-model="zxdItem.isChecked">
                                    <label @click="checkSelectSon(brListIndex,$index)"></label>
                                </div>
                            </td>
                            <td class="cell-l">
                                <div class="zui-table-cell cell-l" v-text="fDate(zxdItem.ksrq,'shortYe')"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-m" v-text="zxdItem.fzh"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title " v-text="yzlx_tran[zxdItem.yzlx]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="zxdItem.xmmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="zxdItem.jl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="zxdItem.jldwmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="zxdItem.zxhsxm"></div>
                            </td>
							<td>
							    <div class="zui-table-cell cell-s title" v-text="zxdItem.sjzxhsxm"></div>
							</td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="zxdItem.yyffmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="zxdItem.pcmc"></div>
                            </td>
							<td>
								<div class="zui-table-cell cell-l" v-text="fDate(zxdItem.ystzsj,'shortYe')"></div>
							</td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="psjg_tran[zxdItem.psjg]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s title" v-text="zxdItem.pslrry"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l title"
                                     v-text="fDate(zxdItem.pslrsj,'yyyy-MM-dd')"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <p v-if="item.yzxx.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </div>
            </div>
        </div>
    </div>
    <div class="right printHide" v-cloak id="rightPcxx">
        <div class="zui-table-view flex-one padd-b-40 padd-r-10 flex-dir-c flex-container">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox2" :list="'pcList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">频次名称</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body flex-one over-auto" @scroll="scrollTable">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr @click="checkSelect([$index,'some','brlist'],$event)"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        :class="[{'tableTr': $index%2 == 0},{'table-hovers':isChecked[$index]}]" :tabindex="$index"
                        v-for="(item, $index) in pcList">
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox2" :list="'pcList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{item.pcmc}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div   class=" printFormat">
    <transition name="pop-fade">
        <div class="pophide" :class="{'show':isShow}">
            <div class="popCenter ">
                <div class=" flex-container printHide flex-align-c flex-jus-e">
                    <button v-waves class="fa fa-mail-reply-all margin-t-5 tong-btn btn-parmary-b  paddr-r5 "
                            @click="guanbi"></button>
                    <div class="zui-inline flex-container flex-align-c printHide">
                        <label class="font-14" style="margin-left: 20px;">续打标题</label>
                        <select-input class="wh85 padd0" @change-data="commonResultChange" :child="sfbt_tran" :index="isbt"
                                      :val="isbt" :name="'isbt'" :search="true">
                        </select-input>
                    </div>

                    <button v-waves class="tong-btn btn-parmary-b margin-t-5 paddr-r5 " @click="print"> 打印</button>
                    <button v-waves class="tong-btn btn-parmary-b margin-t-5 paddr-r5 " @click="goOnPrint"> 续打</button>
                    <input class="zui-input height-32 wh70 margin-t-5 goPrintHide margin-r-10" placeholder="打印行数"
                           v-model="xdhs"/>
                </div>
				<div :data-index="index"  class=" cqyzd printShow" v-for="(itemList, index) in jsonList">
                    <div :index="index" class="yzdTitle" primaryKey="yzdTitle">
						<div class="dybt">{{yljgmc}}</div>
						<div class="dybt"><span v-text="title"></span>医嘱执行单<span v-if="clickname !=''">({{qtmc}})</span></div>
                    </div>
                    <div class="yzd-brInfo" :index="index" primaryKey="yzd-brInfo">
						<div class="hzxm">
						    <span>姓名:{{itemList.brxm}}</span>
						    
						</div>
						<div class="hzxb" >
						    <span>性别:{{brxb_tran[itemList.brxb]}}</span>
						    
						</div>
						<div class="hznl" >
						    <span>年龄:{{itemList.nl}}{{nldw_tran[itemList.nldw]}}{{itemList.nl2?(itemList.nl2 + '' + nldw_tran[itemList.nldw2]):''}}</span>
						    
						</div>
						<div class="hzch" >
						    <span>床号:{{itemList.rycwbh}}</span>
						</div>
						<div class="hzzyh" >
						    <span>住院号:{{itemList.zyh}}</span>
						</div>
                        <div class="hzbs" >
                            <span>病室:{{itemList.ryksmc}}</span>
                        </div>
                        
                    </div>
                    

						<div class="yzd-table" v-if="(ryszks=='1006' && clickname =='口服' && isgw=='1') ||clickname =='输液'">
                        <table cellspacing="0" cellpadding="0" class="yz-tables">
							<thead>
                            <tr class="goOnPrintHideTitle" primaryKey="goOnPrintHideTitle">
								
								<th rowspan="1" colspan="3" class="xdyz">下达医嘱</th>
								<th rowspan="2" colspan="1" class="yznr">医嘱内容</th>
								<th rowspan="2" colspan="1" class="yzcdqm" >医嘱</br>查对</br>签名</th>
								<th rowspan="2" colspan="1" class="tzsj">停止时间</th>
								<th rowspan="2" colspan="5" class="zxrqsjqm">执行日期、时间和签名</th>
                            </tr>
                            <tr class="goOnPrintHideTitle" primaryKey="goOnPrintHideTitle">
                                <th class="xdyzrq" colspan="1">日期</th>
								<th class="xdyzsj" colspan="1">时间</th>
								<th class="xdyzqm" colspan="1">签名</th>
                            </tr>
							</thead>
							<tbody :data-index="item[0] && item[0].xmmc && item[0].xmmc !=null ?$index :''" v-for="(item, $index) in itemList.yzxx"
								    
								    :index="getGoOnPrintIndex($index,index)" class="goOnPrintHideData"
								    primaryKey="goOnPrintHideData"
								    @click="setTbChecked($index,index)">
								<tr v-if="!item.emptyItem" >
								    <td  rowspan="3" colspan="1">
								        <span v-text="toTime(item[0].ksrq,1)"></span>
								    </td>
								    <td  rowspan="3" colspan="1" >
								        <span v-text="toTime(item[0].ksrq,2)"></span>
								    </td>
								    <td  rowspan="3" colspan="1">
								        <span v-text="item[0].ysxm"></span>
								    </td>
								    <td  rowspan="3" colspan="1" class="">
										<div class="zsorsy">
								        <span class="yzd-name">
										<div v-for="(itemsList, index) in item" :class="[itemsList.tzbj ]">
											
											<v-template v-if="itemsList.xmmc &&itemsList.jl &&itemsList.jldwmc && (itemsList.xmmc.length+(itemsList.jl.toString()).length+itemsList.jldwmc.length)<=16">
												{{itemsList.xmmc}}&nbsp;<span class="yzjl">{{itemsList.jl}}{{itemsList.jldwmc}}</span>
											</v-template>
											<v-template v-else-if="itemsList.xmmc && itemsList.xmmc.length>16 &&itemsList.jl &&itemsList.jldwmc && (itemsList.xmmc.length+(itemsList.jl.toString()).length+itemsList.jldwmc.length)<32 ">
												{{itemsList.xmmc}}&nbsp;<span class="yzjl">{{itemsList.jl}}{{itemsList.jldwmc}}</span>
											</v-template>
											<v-template v-else>						
												{{itemsList.xmmc}}</br><span class="yzjl">{{itemsList.jl}}{{itemsList.jldwmc}}</span>
											</v-template>
											
											
										</div>
											<div class="yzyysm">
											<span class="yzjl" v-if="item[0].yyff != '10' && item[0].yyffmc" v-text="item[0].yyffmc + '  ' + item[0].sysd + item[0].sysddw">
												
											</span>
											<span  class="yzjl" v-if="item[0].yyff == '10' && item[0].yyffmc" v-text="item[0].yyffmc.substr(0,item[0].yyffmc.length-1)">
												
											</span>
											<span class="yzjl" v-show="item[0].yyff == '10'" >
												{{psjg_tran[item[0].psjg]}})
											</span>
											<span class="yzjl" v-if="item[0].yysm" v-text="item[0].yysm">
											</span>
											
											<span class="yzjl" v-show="item[0].yyff != '10' " v-text="item[0].pcmc+'  '+item[0].tyxx">
												
											</span>
											<span>
												{{item[0].yebh ? '婴儿' :''}}
											</span>
											</div>
										</span>
										
								        </div>
								    </td>
								    
								    <td rowspan="3" colspan="1" class=""></td>
								    <td rowspan="3" colspan="1" class=""></td>
									<td class=""></td>
								    <td class=""></td>
								    <td class=""></td>
								    <td class=""></td>
									<td class=""></td>
								</tr>
								<tr v-if="item.emptyItem">
								    <td  rowspan="3" colspan="1">
								    </td>
								    <td  rowspan="3" colspan="1" >
								    </td>
								    <td  rowspan="3" colspan="1">
								    </td>
								    <td  rowspan="3" colspan="1" class="">
								        
								    </td>
								    
								    <td rowspan="3" colspan="1" class=""></td>
								    <td rowspan="3" colspan="1" class=""></td>
									<td class=""></td>
								    <td class=""></td>
								    <td class=""></td>
								    <td class=""></td>
									<td class=""></td>
								</tr>
								<tr>
									<td class=""></td>
									<td class=""></td>
									<td class=""></td>
									<td class=""></td>
									<td class=""></td>
								</tr>
								<tr>
									<td class=""></td>
									<td class=""></td>
									<td class=""></td>
									<td class=""></td>
									<td class=""></td>
								</tr>
							</tbody>
                        </table>
						
                    </div>

                    <div class="yzd-table" v-else-if=" clickname !='注射' && clickname !='治疗' && clickname !='雾化' && clickname !='护理' && clickname !='其他' ">
                        <table cellspacing="0" cellpadding="0" class="kfyz-tables">
                            <tr class="goOnPrintHideTitle" primaryKey="goOnPrintHideTitle">
								<th rowspan="1" colspan="3" class="kfxdyz">下达医嘱</th>
								<th rowspan="2" colspan="1" class="kfyznr">医嘱内容</th>
								<th rowspan="2" colspan="1" class="kfyzcdqm" >医嘱</br>查对</br>签名</th>
								<th rowspan="2" colspan="1" class="kftzsj">停止</br>时间</th>
								<th rowspan="2" colspan="1" class="kfrq">日</br>期</th>
								<th rowspan="1" colspan="4" class="kfzxrqsjqm">执行日期、时间和签名</th>
								
                            </tr>
                            <tr class="goOnPrintHideTitle" primaryKey="goOnPrintHideTitle">
                                <th class="kfxdyzrq" colspan="1">日期</th>
								<th class="kfxdyzsj" colspan="1">时间</th>
								<th class="kfxdyzqm" colspan="1">签名</th>
								<th class="kfzao" colspan="1">早</th>
								<th class="kfzhong" colspan="1">中</th>
								<th class="kfwan" colspan="1">晚</th>
								<th class="kfwu" colspan="1"> </th>
                            </tr>
                            <tr v-for="(item, $index) in itemList.yzxx"
                                :index="getGoOnPrintIndex($index,index)" class="goOnPrintHideData"
                                primaryKey="goOnPrintHideData"
                                @click="setChecked($index,index)">
                                <td v-if="!item.emptyItem " colspan="1">
                                    <span v-text="toTime(item.ksrq,1)"></span>
                                </td>
                                <td v-if="!item.emptyItem " colspan="1" >
                                    <span v-text="toTime(item.ksrq,2)"></span>
                                </td>
                                <td v-if="!item.emptyItem " colspan="1">
                                    <span v-text="item.ysxm"></span>
                                </td>
                                <td v-if="!item.emptyItem " colspan="1"  :class="[item.tzbj ]">
									<div class="ypxmgs">
									<span class="yzd-name">
                                    <span v-if="clickname == '雾化'">
										<v-template v-if="item.xmmc &&item.jl &&item.jldwmc && (item.xmmc.length+(item.jl.toString()).length+item.jldwmc.length)<=16">
											{{item.xmmc}}&nbsp;<span class="yzjl">{{item.jl}}{{item.jldwmc}}</span>
										</v-template>
										<v-template v-else-if="item.xmmc && item.xmmc.length>16 &&item.jl &&item.jldwmc && (item.xmmc.length+(item.jl.toString()).length+item.jldwmc.length)<32 ">
											{{item.xmmc}}&nbsp;<span class="yzjl">{{item.jl}}{{item.jldwmc}}</span>
										</v-template>
										<v-template v-else>						
											{{item.xmmc}}</br><span class="yzjl">{{item.jl}}{{item.jldwmc}}</span>
										</v-template>
                                    </span>
									<span v-else>
										<v-template v-if="item.xmmc &&item.jl &&item.jldwmc && (item.xmmc.length+(item.jl.toString()).length+item.jldwmc.length)<=16">
											{{item.xmmc}}</br><span class="yzjl">{{item.jl}}{{item.jldwmc}}</span>
										</v-template>
										<v-template v-else-if="item.xmmc && item.xmmc.length>16 &&item.jl &&item.jldwmc && (item.xmmc.length+(item.jl.toString()).length+item.jldwmc.length)<32 ">
											{{item.xmmc}}&nbsp;<span class="yzjl">{{item.jl}}{{item.jldwmc}}</span>
										</v-template>
										<v-template v-else>						
											{{item.xmmc}}&nbsp;<span class="yzjl">{{item.jl}}{{item.jldwmc}}</span>
										</v-template>
									</span>
                                    
									<span  class="yzjl" v-if="item.yyff != '10' && item.yyffmc" v-text="item.yyffmc + '  ' + item.sysd + item.sysddw">
										
									</span>
									<span class="yzjl" v-if="item.yyff == '10' && item.yyffmc" v-text="item.yyffmc.substr(0,item.yyffmc.length-1)">
										
									</span>
									<span class="yzjl" v-show="item.yyff == '10'" >
										{{psjg_tran[item.psjg]}})
									</span>
									<span class="yzjl" v-text="item.yysm">
									</span>
									
									
									<span class="yzjl" v-show="item.yyff != '10' " v-text="item.pcmc+'  '+item.tyxx">
										
									</span>
									<span>
										{{item.yebh ? '婴儿' :''}}
									</span>
									</span>
									</div>
                                </td>
								<td  v-if="item.emptyItem " colspan="1">
								</td>
								<td  v-if="item.emptyItem " colspan="1" >
								</td>
								<td  v-if="item.emptyItem " colspan="1">
								</td>
								<td  v-if="item.emptyItem " colspan="1" class="">
								</td>
								
								
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
								<td class=""></td>
                            </tr>
                        </table>
						
                    </div>

					<div class="yzd-table" v-else-if=" (clickname =='注射' ||clickname =='治疗' ||clickname =='雾化' ||clickname =='护理' || clickname =='其他' )">
                        <table cellspacing="0" cellpadding="0" class="yz-tables">
                            <tr class="goOnPrintHideTitle" primaryKey="goOnPrintHideTitle">
                                <th rowspan="1" colspan="3" class="xdyz">下达医嘱</th>
                                <th rowspan="2" colspan="1" class="yznr">医嘱内容</th>
                                <th rowspan="2" colspan="1" class="yzcdqm" >医嘱</br>查对</br>签名</th>
                                <th rowspan="2" colspan="1" class="tzsj">停止</br>时间</th>
                                <th rowspan="2" colspan="5" class="zxrqsjqm">执行日期、时间和签名</th>
                            </tr>
                            <tr class="goOnPrintHideTitle" primaryKey="goOnPrintHideTitle">
                                <th class="xdyzrq" colspan="1">日期</th>
                                <th class="xdyzsj" colspan="1">时间</th>
                                <th class="xdyzqm" colspan="1">签名</th>
                            </tr>
                            <tbody :data-index="item.xmmc !=null ?$index :''" v-for="(item, $index) in itemList.yzxx"
                                   :index="getGoOnPrintIndex($index,index)" class="goOnPrintHideData"
                                   primaryKey="goOnPrintHideData"
                                   @click="setTbChecked($index,index)">
                            <tr v-if="!item.emptyItem">
                                <td  rowspan="3">
                                    <span v-text="toTime(item[0].ksrq,1)"></span>
                                </td>
                                <td  rowspan="3" >
                                    <span v-text="toTime(item[0].ksrq,2)"></span>
                                </td>
                                <td  rowspan="3">
                                    <span v-if="item[0].ysxm" v-text="item[0].ysxm"></span>
                                </td>
                                <td  rowspan="3" class=" ">
                                    <div class="zsorsyss">
											<span class="yzd-name">
								        
										<div  v-for="(itemsList, index) in item"  :class="[itemsList.tzbj ]">
											<v-template v-if="itemsList.xmmc &&itemsList.jl &&itemsList.jldwmc && (itemsList.xmmc.length+(itemsList.jl.toString()).length+itemsList.jldwmc.length)<=16">
												{{itemsList.xmmc}}&nbsp;<span  class="yzjl">{{itemsList.jl}}{{itemsList.jldwmc}}</span>
											</v-template>
											<v-template v-else-if="itemsList.xmmc && itemsList.xmmc.length>16 &&itemsList.jl &&itemsList.jldwmc && (itemsList.xmmc.length+(itemsList.jl.toString()).length+itemsList.jldwmc.length)<32 ">
												{{itemsList.xmmc}}&nbsp;<span  class="yzjl">{{itemsList.jl}}{{itemsList.jldwmc}}</span>
											</v-template>
											<v-template v-else>						
												{{itemsList.xmmc}}</br><span  class="yzjl">{{itemsList.jl}}{{itemsList.jldwmc}}</span>
											</v-template>
										</div>
										<div class="yzyysm">
										<span class="yzjl" v-if="item[0].yyff != '10' && item[0].yyffmc" v-text="item[0].yyffmc + '  ' + item[0].sysd + item[0].sysddw">
											
										</span>
										<span class="yzjl" v-if="item[0].yyff == '10' && item[0].yyffmc" v-text="item[0].yyffmc.substr(0,item[0].yyffmc.length-1)">
											
										</span>
										<span class="yzjl" v-show="item[0].yyff == '10'" >
											{{psjg_tran[item[0].psjg]}})
										</span>
										<span class="yzjl" v-if="item[0].yysm" v-text="item[0].yysm">
										</span>
										
										<span class="yzjl" v-show="item[0].yyff != '10' " v-text="item[0].pcmc+'  '+item[0].tyxx">
											
										</span>
										<span>
											{{item[0].yebh ? '婴儿' :''}}
										</span>
										</div>
										</span>
                                    </div>
                                </td>

                                <td rowspan="3"></td>
                                <td rowspan="3"></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                            </tr>
                            <tr v-if="item.emptyItem ">
                                <td  rowspan="3">
                                </td>
                                <td  rowspan="3" >
                                </td>
                                <td  rowspan="3">
                                </td>
                                <td  rowspan="3" class="">
                                </td>
                                <td rowspan="3"></td>
                                <td rowspan="3"></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                            </tr>
                            <tr>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                            </tr>
                            <tr>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                                <td class=""></td>
                            </tr>
                            </tbody>
                        </table>
						
                    </div>

                    <div class="pageDiv" style="" primaryKey="pageDiv" v-text="'第  ' + (index + 1) + '  页'"></div>
                </div>
            </div>
        </div>
    </transition>
</div>
<model class="pslr printHide" :s="'确定'" :c="'取消'" @default-click="isShow=false" @result-clear="isShow=false"
       :model-show="true" @result-close="isShow=false" v-if="isShow" :title="'皮试结果录入'">
    <div class="bqcydj_model">
        <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s">姓名</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">床位号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">住院号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l text-left">项目名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">皮试结果</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">皮试人员</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l">皮试时间</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">皮试审核人员</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l">皮试审核时间</div>
                        </th>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body flex-one over-auto" data-no-change @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in psYzList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        <td>
                            <div class="zui-table-cell  cell-s " v-text="item.brxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell  cell-s " v-text="item.rycwbh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell  cell-s " v-text="item.zyh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l text-left" v-text="item.xmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" over-auto>
                                <select-input @change-data="resultChange_save" :not_empty="false" :child="psjg_tran"
                                              :index="'item.psjg'" :val="item.psjg" :name="$index + '.psjg'"
                                              :search="true">
                                </select-input>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" over-auto>

                                <select-input @change-data="resultChange_save" :child="hsListData" :index="'ryxm'"
                                              :name="$index + '.pslrrybm'" :index_val="'rybm'" :val="item.pslrrybm"
                                              :search="true">

                                </select-input>
                            </div>
                            <!--                                                                  <div  class="zui-table-cell cell-s" v-text="item.pslrry">片</div>-->
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">
                                <input class="zui-input" @click="openTime('pslrsj',$index,'pslrsj')" id="pslrsj"
                                       :value="fDate(item.pslrsj,'yyyy-MM-dd')">
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" over-auto>
                                <select-input @change-data="resultChange_save" :child="hsListData" :index="'ryxm'"
                                              :index_val="'rybm'" :val="item.psshrybm" :name="$index + '.psshrybm'"
                                              :search="true"></select-input>
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">
                                <input class="zui-input" @click="openTime('psshsj',$index,'psshsj')" id="psshsj"
                                       :value="fDate(item.psshsj,'yyyy-MM-dd')">
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</model>

<model class="zxlr printHide" :s="'确定'" :c="'取消'" @default-click="isShow=false" @result-clear="isShow=false"
       :model-show="true" @result-close="isShow=false" v-if="isShow" :title="'执行修改'">
    <div class="bqcydj_model">
        <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th>
                            <div class="zui-table-cell cell-s">住院号</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l text-left">项目名称</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s">执行人员</div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l">执行时间</div>
                        </th>
					</tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body flex-one over-auto" data-no-change @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()" v-for="(item, $index) in psYzList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                        
                        <td>
                            <div class="zui-table-cell  cell-s " v-text="item.zyh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l text-left" v-text="item.xmmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" over-auto>

                                <select-input @change-data="resultChange_save" :child="hsListData" :index="'ryxm'"
                                              :name="$index + '.sjzxhs'" :index_val="'rybm'" :val="item.sjzxhs"
                                              :search="true">

                                </select-input>
                            </div>
                            <!--                                                                  <div  class="zui-table-cell cell-s" v-text="item.pslrry">片</div>-->
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l">
                                <input class="zui-input" @click="openTime('sjzxsj',$index,'sjzxsj')" id="sjzxsj"
                                       :value="fDate(item.sjzxsj,'AllDate')">
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</model>

<div class="loadingPrint" id="loadingPrint">

</div>
<script type="text/javascript" src="childpage/yzzxd.js"></script>
