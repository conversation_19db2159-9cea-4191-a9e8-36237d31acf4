var bqcw = new Vue({
    el: '#bqcw',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        cwList: [],
        barContent: {
            bqbm:''
        },
        bqList: [],
        qbbq: {
            'bqbm': '',
            'bqmc': '全部',
        }
    },
    updated: function () {
        changeWin();
    },
    created: function () {
        this.getBqList();
        this.getData();
    },
    methods: {
        //查询床位列表
        getData: function () {
            this.param.bqbm = this.barContent.bqbm
            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCwglCwh&types=queryByHsz&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == 0) {
                    bqcw.totlePage = Math.ceil(json.d.total / bqcw.param.rows);
                    bqcw.cwList = json.d.list;
                } else {
                    malert("床位查询失败：" + json.c);
                }
            });
        },
        //获取病区
        getBqList: function () {
            var dg = {
                "page": 1,
                "rows": 50,
                "sort": "bqbm",
                "order": "asc"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=bq&dg=" + JSON.stringify(dg), function (json) {
                bqcw.bqList = json.d.list;
                bqcw.bqList.push(bqcw.qbbq);
            });
        },
        //触发回调
        resultChangeBq: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData();
        },
    }
});
