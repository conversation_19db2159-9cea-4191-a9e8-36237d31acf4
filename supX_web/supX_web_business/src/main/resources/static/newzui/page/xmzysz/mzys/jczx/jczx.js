//顶部工具栏
var tool = new Vue({
    el:'.wrapper',
    mixins: [dic_transform, baseFunc, tableBase,mformat],
    data: {
        complyShow: true,
        userName: null,
        jsonList: [],
        num: 0,
        totlePage: 0
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                tool.param.beginrq = value;
                tool.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                tool.param.endrq = value;
                tool.getData();
            }
        });
        this.getData();
        this.getUserName();
    },
    methods: {
        getUserName: function () {
            this.userName = JSON.parse(sessionStorage.getItem("userName" + userId));
            console.log(this.userName);
        },
        //刷新操作
        getData: function () {
            var parm = {
                beginrq: this.param.beginrq,
                endrq: this.param.endrq,
                zxbz: this.num,
                brxm: this.param.parm,
                page: this.param.page,
                rows: this.param.rows,
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=queryJc&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    tool.totlePage = Math.ceil(json.d.total / tool.param.rows);
                    tool.jsonList = json.d.list;
                } else {
                    malert(json.c + ",查询失败：", 'top', 'defeadted');
                }
            });
        },
        //执行操作
        Comply: function () {
            var zxlist = [];
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    this.jsonList[i].zxbz = "1";
                    this.jsonList[i].tool = new Date();
                    this.jsonList[i].zxry = tool.userName;
                    zxlist.push(JSON.parse(JSON.stringify(this.jsonList[i])));
                }
            }
            if (zxlist.length == 0) {
                malert("请选中您要退费的数据", 'top', 'defeadted');
                return;
            }
            var json = '{"list":' + JSON.stringify(zxlist) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=jczxUpdate', json)
                .then(function (data) {
                    if (data.body.a == 0) {
                        tool.isChecked = [];
                        tool.isCheckAll = false;
                        malert("执行成功！");
                        tool.getData();
                    } else {
                        malert(data.body.c + ",执行失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        //取消执行操作
        CancelComply: function () {
            var qxzxlist = [];
            for (var i = 0; i < tool.isChecked.length; i++) {
                if (tool.isChecked[i] == true) {
                    tool.jsonList[i].zxbz = "0";
                    tool.jsonList[i].tool = tool;
                    tool.jsonList[i].zxry = tool.userName;
                    qxzxlist.push(JSON.parse(JSON.stringify(tool.jsonList[i])));
                }
            }
            if (qxzxlist.length == 0) {
                malert("请选中您要退费的数据", 'top', 'defeadted');
                return;
            }
            var json = '{"list":' + JSON.stringify(qxzxlist) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzsfSfjsBrfy&types=jczxUpdate', json)
                .then(function (data) {
                    if (data.body.a == 0) {
                        tool.isChecked = [];
                        tool.isCheckAll = false;
                        malert("取消执行成功！", 'top', 'success');
                        tool.getData();
                    } else {
                        malert(data.body.c + ",取消执行失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
        },
        //切换
        tabBg: function (index) {
            this.num = index;
            switch (this.num) {
                case 0:
                    tool.complyShow = true;
                    tool.getData();
                    break;
                case 1:
                    tool.complyShow = false;
                    tool.getData();
                    break;
                default:
                    break;
            }
        },
    }
});










