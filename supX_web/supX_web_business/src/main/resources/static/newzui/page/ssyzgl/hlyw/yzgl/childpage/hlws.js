var hlwsBox = new Vue({
    el: '#hlws-box',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data: {
        wslxList: {
            "0": "入院护理评估单",
        },
        wslx: "0",
        ryjszd: [],
        yljg: '',
        modelContent: {},
        BrxxJson: {},
    },
    mounted: function () {
        this.initData();
    },
    methods: {
        checkFilter: function (val) {
            var returnVal = false;
            for (var i = 0; i < this.ryjszd.length; i++) {
                if (this.ryjszd[i] == val) {
                    returnVal = true;
                    return returnVal;
                }
            }
            return returnVal;
        },
        //初始化数据
        initData: function (newVal) {
            this.BrxxJson = yzclLeft.HszbrItem;
            this.getData();
        },
        print: function () {
            window.print();
        },

        getData: function () {
            var parm = {
                'yljg': this.BrxxJson.yljgmc,
                'modeltype': this.wslx,
                'brsbh': this.BrxxJson.zyh,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=HszHlwsData&types=selectOne" +
                '&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0 && json.d.list[0] != null) {
                            hlwsBox.modelContent = json.d.list[0];
                        } else {
                            hlwsBox.modelContent = {};
                        }
                    } else {
                        malert('模板数据查询失败' + json.c, 'top', 'defeadted')
                        return;
                    }
                });
        },

        saveData: function () {
            if (this.modelContent.creattime != null) {
                $.getJSON("/actionDispatcher.do?reqUrl=HszHlwsData&types=update" +
                    '&parm=' + JSON.stringify(hlwsBox.modelContent),
                    function (json) {
                        if (json.a == 0) {
                            malert("保存成功", 'top', 'success')
                        } else {
                            malert('保存失败', 'top', 'defeadted')
                            return;
                        }
                    });
            } else {
                this.modelContent.modeltype = this.wslx;
                this.modelContent.modelname = "入院护理评估单";
                this.modelContent.yljg = this.BrxxJson.yljgmc;
                this.modelContent.brsbh = this.BrxxJson.zyh;
                $.getJSON("/actionDispatcher.do?reqUrl=HszHlwsData&types=insert" + '&parm=' + JSON.stringify(this.modelContent),
                    function (json) {
                        if (json.a == 0) {
                            malert("保存成功", 'top', 'success')
                        } else {
                            malert('保存失败', 'top', 'defeadted')
                            return;
                        }
                    });
            }
        },
    }
});


yzclLeft.$watch('HszbrItem', function (newVal,oldVal) {
    if(newVal.zyh != oldVal.zyh && this.index==4){
        hlwsBox.initData(newVal);
        console.log('hlws')
    }
})
