var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        WzrkPrint:'',
        tjkdhc:{},
        popContent: {},
        queryData: {},
        kfList: [],
        ryList: [],
        mxList: [],
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        this.initial();
        this.getCgry();
        this.getGhdw();
    },
    methods: {

        //打印
        dy:function(){
            var tjdjh =wrapper.popContent.tjdjh;
            // var yljgbm =jgbm;
            var kfbm = wrapper.popContent.wzkf;
            var reportlets = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                reportlets = "[{reportlet: 'wzdy%2Frcyw%2Ftjdmx.cpt',yljgbm:'"+jgbm+"',tjdjh:'"+tjdjh+"',kfbm'"+kfbm+"'}]";
            } else {
                reportlets = "[{reportlet: 'wzdy/rcyw/tjdmx.cpt',yljgbm:'"+jgbm+"',tjdjh:'"+tjdjh+"',kfbm'"+kfbm+"'}]";
            }
            //帆软打印
            if (!FrPrint(reportlets, wrapper.WzrkPrint)) {
            }
        },

        sh: function () {
            //保存
            console.log(JSON.stringify(wrapper.popContent));
            this.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywTjd&types=confirm', JSON.stringify(wrapper.popContent))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("审核成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
        // 提交所有
        submitAll: function () {
            malert('提交所有', 'top', 'success')
            var tjd = {}
            Vue.set(tjd, 'wzkf', this.popContent.kfbm);//库房编码
            Vue.set(tjd, 'zdr', this.popContent.rybm);//制单人
            Vue.set(tjd, 'bzms', this.popContent.bzms);//备注描述
            var wzkfbms = this.listGetName(this.kfList, this.popContent.kfbm, 'wzkfbm', 'ksbm');
            console.log(JSON.stringify(tjd));
            //新增操作
            var obj = {
                list: {
                    tjd: tjd,
                    tjdmx: this.mxList,
                    wzkf: wzkfbms
                }
            }
            if(JSON.stringify(wrapper.tjkdhc)=='{}'){
                wrapper.tjkdhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
            }else {
                if (JSON.stringify(wrapper.tjkdhc) == JSON.stringify(obj)) {
                    malert("报损单已经保存请不要重复提交!", 'top', 'defeadted');
                    return false;
                } else {
                    wrapper.tjkdhc = JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
                }
            }
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywTjd&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("保存成功！", 'top', 'success');
                        // malert("审核成功！")
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        // 取消取消2018/07/09取消回退上一级页面
        cancel: function () {
            // malert('取消','top','defeadted')
            this.topClosePage('page/wzkf/kfyw/tjgl/tjkd.html', 'page/wzkf/kfyw/tjgl/tjgl.html');
        },
        // 编辑
        edit: function (index) {
            pop.title = '编辑物资'
            pop.open();
            pop.popContent = this.mxList[index];


        },
        // 删除2018/07/09二次删除弹窗提示
        remove: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.mxList.splice(index, 1);
            })) {
                return false;
            }
        },
        // 新增
        AddMdel: function () {
            pop.title = '添加物资'
            pop.open();
            pop.popContent = {};
        },
        //初始化
        initial: function () {
            this.queryData = JSON.parse(sessionStorage.getItem('obj'));
            this.kfList = this.queryData.kfList;
            if (this.queryData.sh || this.queryData.dy) {
                //审核或者打印
                this.popContent = this.queryData.tjd;
                this.popContent.kfbm = this.queryData.kfbm;
                this.getMx(this.queryData.tjd);
                Vue.set(this.popContent, 'rybm', this.popContent.zdr);
            }

        },
        getMx: function () {
            var obj = {
                tjdjh: this.queryData.tjd.tjdjh,
                wzkf: this.popContent.kfbm
            };
            console.log(obj.wzkf);
            $.getJSON('/actionDispatcher.do?reqUrl=New1WzkfKfywTjd&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 &&  data.d.length !=0) {
                        wrapper.mxList = data.d
                        // for (var  i = wrapper.mxList.length - 1; i >=0; i--) {
                        //     if(wrapper.mxList[i].kcsl <=0){
                        //         wrapper.mxList.splice(i,1)
                        //     }
                        // }
                    } else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载采购人员
        getCgry: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    if (data.a == 0) {
                        wrapper.ryList = data.d.list
                        wrapper.popContent.rybm = data.d.list[0].rybm;
                    } else {
                        malert("获取采购人员失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载供货单位
        getGhdw: function () {
            //初始化页面记载供货单位
            var parm = {
                page: 1,
                rows: 20000,
                sort: 'dwbm',
                tybz: '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        pop.ghdwList = json.d.list;
                    } else {
                        malert("供货单位获取失败", 'top', 'defeadted');
                    }
                });
        },
        resultRydjChange: function (val) {
            var isTwo = false;
            //先获取到操作的哪一个
            var types = val[2][val[2].length - 1];
            switch (types) {
                case "kfbm":
                    Vue.set(wrapper.popContent, 'kfbm', val[0]);
                    Vue.set(wrapper.popContent, 'kfmc', val[4]);
                    break;
                case "rybm":
                    Vue.set(wrapper.popContent, 'rybm', val[0]);
                    Vue.set(wrapper.popContent, 'ryxm', val[4]);
                    break;
                default:
                    break;
            }
        }
    }

});


var pop = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        title: '',
        num: 0,
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '物资名称': 'wzmc',
            '物资规格': 'wzgg',
            '物资单位': 'kfdw',
            '分装比例': 'fzbl',
            '进价': 'jj',
            '单价': 'dj',
            '产地': 'cd',
        },
        searchCon: [],
        selSearch: -1,
        popContent: {},
        ghdwList: [],
        wzkfList: wrapper.kfList,
        total: 0,
    },
    methods: {


// 关闭
        closes: function () {
            this.num = 0;

        },
        open: function () {
            this.num = 1;
            // 设置库房
        },
        //保存
        save: function () {
            if (pop.title = '添加物资') {
                //添加
                if (wrapper.mxList.length == 0) {
                    if(pop.popContent.xjj==undefined){
                        malert("现进价填写异常请重新填写！", 'top', 'defeadted');
                        return;
                    }
                    if (pop.popContent.xlj==undefined){
                        malert("现进价填写异常请重新填写！", 'top', 'defeadted');
                        return;
                    }
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }
                for (var i = 0; i < wrapper.mxList.length; i++) {
                    if (wrapper.mxList[i].wzbm == pop.popContent.wzbm && wrapper.mxList[i].scph == pop.popContent.scph) {
                        malert("已有该批号的物资！", 'top', 'defeadted');
                        return;
                    }
                }
                wrapper.mxList.push(pop.popContent);
                pop.popContent = {};
                return;
            } else if (pop.title = '编辑物资') {
                if (wrapper.mxList.length == 0) {
                    wrapper.mxList.push(pop.popContent);
                    pop.popContent = {};
                    return;
                }
                //编辑保存成功关闭弹窗
                pop.popContent = {};
                pop.closes();
                return;
            }
        },

        //药品名称下拉table检索数据
        changeDown: function (event, type) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                $("#rksl").focus();
            }
        },
        //当输入值后才触发
        change: function (event, type, val) {
            var parm = {
                //kfbm: wrapper.popContent.kfbm,
                page: pop.dg.page,
                rows: pop.dg.rows,
                parm: val
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1WzkfXtwhwzzd&types=query' +
                '&json=' + JSON.stringify(parm),
                function (data) {
                    pop.searchCon = data.d.list;
                    //wap.total = data.d.total;
                    pop.selSearch = 0;
                    $(".selectGroup").show();
                });

            this.popContent[type] = val;
            if (wrapper.popContent["kfbm"] == undefined || wrapper.popContent["kfbm"] == null || wrapper.popContent["kfbm"] == "") {
                malert("库房不能为空", 'top', 'defeadted');
                return;
            }
            if (wrapper.popContent["rybm"] == undefined || wrapper.popContent["rybm"] == null || wrapper.popContent["rybm"] == "") {
                malert("采购人员不能为空", 'top', 'defeadted');
                return;
            }
            var _searchEvent = $(event.target.nextElementSibling).eq(0);

        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作

                pop.dg.page++;
                var parm = {
                    page: pop.dg.page,
                    rows: pop.dg.rows,
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&json=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                pop.searchCon.push(data.d.list[i]);
                            }
                            pop.total = data.d.total;
                            pop.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }

            this.popContent = item;
            $(".selectGroup").hide();
        },


    }
});
