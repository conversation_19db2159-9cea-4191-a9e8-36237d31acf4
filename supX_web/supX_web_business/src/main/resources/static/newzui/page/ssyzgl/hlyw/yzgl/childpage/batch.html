
<div id="batch">
    <tabs :num="num" :tab-child="[{'text':'三测数据录入'},{'text':'出入液量录入'}]" @tab-active="tabBg"></tabs>
    <div class="flex-container padd-b-10 padd-t-10">
        <div class="flex-container flex-align-c" v-if="num==0">
            <span class="padd-r-5 ft-14 whiteSpace">时间</span>
            <input class="zui-input time wh180" type="text" v-model="parm.clrq" @click="showTime()">
        </div>
		<div class="flex-container flex-align-c padd-l-10" v-if="num==0">
		    <span class="padd-r-5 ft-14 whiteSpace">时段</span>
		    <select-input @change-data="resultChangeData" :data-notEmpty="false"
		                  :child="twsd_tran" :index="parm.clsd" :val="parm.clsd"
		                  :name="'parm.clsd'">
		    </select-input>
		</div>
        <div class="flex-container flex-align-c padd-l-10" v-if="num==0">
            <span class="padd-r-5 ft-14 whiteSpace">默认测量部位</span>
            <select-input @change-data="setTwbw" :data-notEmpty="false"
                          :child="twbw_tran" :index="twbw" :val="twbw"
                          :name="'twbw'">
            </select-input>
        </div>
		<div class="flex-container flex-align-c padd-l-10">
			<button  @click="yjfz" class="tong-btn btn-parmary  xmzb-db">一键复制</button>
			<button  @click="yjqk" class="tong-btn btn-parmary  xmzb-db">一键清空</button>
		</div>
    </div>
    <div class="zui-table-view">
        <div v-if="num==0" class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <!-- <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>  -->
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>床位号</span></div>
                    </th>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m "><span>姓名</span></div>
                    </th>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m "><span>年龄</span></div>
                    </th>
                    <th class="cell-m">
                            <div class="text-center border-child-60">体温</div>
                    </th>
                    <th class="cell-s">
                        <div class="text-center border-child-60">测量部位</div>
                    </th>
                    <!-- <th class="cell-m">
                         <div class="text-center border-child border-right-none">心率</div>
                    </th>
                    <th class="cell-m">
                         <div class="text-center border-child border-right-none">起搏器</div>
                    </th>
                    <th class="cell-m">
                         <div class="text-center border-child border-right-none">脉搏</div>
                    </th>-->
                    <th class="cell-xl">
                        <div class="flex-container cell-xl flex-align-c flex-wrap-n-w">
                            <div class="text-center border-child-60">心率</div>
                            <div class="text-center border-child-60">起搏器</div>
                            <div class="text-center border-child-60">脉搏</div>
                        </div>
                    </th>
                    <th class="cell-s">
                            <div class="text-center border-child-60">机械通气</div>
                    </th>
                    <th class="cell-m">
                        <div class="text-center border-child-60">呼吸</div>
                    </th>
                    <th  class="cell-s">
                        <div class="zui-table-cell  cell-s border-child-60 "><span>未测原因</span></div>
                    </th>


                    <!-- 其他记录录入 start -->
					<th class="cell-xxl">
                        <div class="text-center cell-xxl  "><span>小便</span></div>
                        <div class="flex-container cell-xxl flex-align-c flex-wrap-n-w jc-sp">
                            <div class="text-center border-child-60">(次)</div>
                            <div class="text-center border-child-60">量</div>
                            <div class="text-center border-child-60">色</div>
                            <div class="text-center border-child-60">导尿</div>
                        </div>
                    </th>
                    <th class="cell-xxl">
                        <div class=" text-center cell-xxl  "><span>大便</span></div>
                        <div class="flex-container cell-xxl flex-align-c flex-wrap-n-w jc-sp">
                            <div class="text-center border-child-60">(次)</div>
                            <div class="text-center border-child-60">色</div>
                            <div class="text-center border-child-60">质</div>
                            <div class="text-center border-child-60">量</div>
                        </div>
                    </th>
                    <th class="cell-l">
                        <div class="text-center cell-xxl   "><span>{{xyType == '1' ? '上午血压' : '下午血压'}}</span></div>
                        <div class="flex-container cell-xxl   flex-align-c flex-wrap-n-w jc-sp">
                            <div class="text-center border-child-60">收缩(mmhg)</div>
                            <div class="text-center border-child-60">舒张(mmhg)</div>
                        </div>
                    </th>
                    <th class="cell-l">
                        <div class="text-center cell-l   "><span>疼痛评估</span></div>
                        <div class="flex-container cell-l   flex-align-c flex-wrap-n-w jc-sp">
                            <div class="text-center border-child">上午</div>
                            <div class="text-center border-child">下午</div>
                        </div>
                    </th>
                    <!-- 其他记录录入 end -->
                </tr>
                </thead>
            </table>
        </div>
        <div v-if="num==0"  class="zui-table-body zuiTableBody" data-no-change @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="hoverMouse(true,$index)"
                    :tabindex="$index"
                    v-for="(item, $index) in twbList"
                >
                    <!--<td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="item.xssx"></div>
                    </td> -->
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="item.rycwbh"></div>
                    </td>
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">{{item.brxm}}</div>
                    </td>
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">{{item.nl }} {{setNldw(item.nldw)}}</div>
                    </td>
                    <td class="cell-m">
                                <input @keydown="nextFocus($event)" class="zui-input  border-child-60" type="number"
                                @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.tw"/>
                    </td>
                     <td class="cell-s">
                         <select-input class="border-child-60" @change-data="changeData($event,$index)" :data-notEmpty="false"
                                       :child="twbw_tran" :index="item.twbw" :val="item.twbw"
                                       :name="'item.twbw'">
                         </select-input>
                    </td>

					<!--<td class="cell-m">
                      <div class="border-child border-right-none">
                                <input @keydown="nextFocus($event,11)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xt"/>
                            </div>
					</td>
					<td class="cell-m">
                      <select-input @change-data="changeData($event,$index)" :data-notEmpty="true"
                                          :child="istrue_tran" :index="item.xtqbq" :val="item.xtqbq"
                                          :name="'item.xtqbq'">
                            </select-input>
					</td>
					<td class="cell-m">
                       <div class="border-child">
                                <input @keydown="nextFocus($event,11)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.mb"/>
                            </div>
					</td>-->
                     <td class="cell-xl">
                        <div class="zui-table-cell cell-xl flex-wrap-n-w flex-container ">
                            <div class="border-child-60">
                                <input  @keydown="nextFocus($event)" class="zui-input" @keydown="nextFocus($event)" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xt"/>
                            </div>
                            <select-input class="border-child-60"  @keydown="nextFocus($event)" @change-data="changeData($event,$index)" :data-notEmpty="true"
                                          :child="istrue_tran" :index="item.xtqbq" :val="item.xtqbq"
                                          :name="'item.xtqbq'">
                            </select-input>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.mb"/>
                            </div>
                        </div>
                    </td>
                    <td class="cell-s">
                            <select-input class="border-child-60" @change-data="changeData($event,$index)" :data-notEmpty="true"
                                          :child="istrue_tran" :index="item.rgfx" :val="item.rgfx"
                                          :name="'item.rgfx'">
                            </select-input>
                    </td>
                    <td class="cell-m">
                                <input @keydown="nextFocus($event)" class="zui-input border-child-60" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.fx"/>
                    </td>
                    <td  class="cell-s">
                        <select-input class="cell-s border-child-60" @change-data="changeData($event,$index,$index)" :data-notEmpty="true"
                                      :child="wcyy_tran" :index="item.wcyy" :val="item.wcyy"
                                      :name="'item.wcyy'">
                        </select-input>
                    </td>
                    <!-- 其他记录录入 start -->
                    <td class="cell-xxl">
                        <div class="zui-table-cell cell-xxl  flex-wrap-n-w flex-container">
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xbc"/>
                            </div>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xbl"/>
                            </div>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xbs"/>
                            </div>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.bldn"/>
                            </div>
                        </div>
                    </td>
                    <td class="cell-xxl">
                        <div class="zui-table-cell cell-xxl flex-wrap-n-w flex-container ">
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbcs"/>
                            </div>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbs"/>
                            </div>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbz"/>
                            </div>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbl"/>
                            </div>
                        </div>
                    </td>
                    <td class="cell-xxl  ">
                        <div class="zui-table-cell cell-xxl   flex-wrap-n-w flex-container ">
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.ssy"/>
                            </div>
                            <div class="border-child-60">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.szy"/>
                            </div>
                        </div>
                    </td>
                    <td class="cell-l  ">
                        <div class="zui-table-cell cell-l  flex-wrap-n-w flex-container ">
                            <div class="border-child">
								<select-input @change-data="changeData($event,$index,$index)" :data-notEmpty="true"
								              :child="ttpg_tran" :index="item.ttpgs" :val="item.ttpgs"
								              :name="'item.ttpgs'">
								</select-input>
								</div>
                            <div class="border-child">
								<select-input @change-data="changeData($event,$index,$index)" :data-notEmpty="true"
								              :child="ttpg_tran" :index="item.ttpgx" :val="item.ttpgx"
								              :name="'item.ttpgx'">
								</select-input>
                                </div>
                        </div>
                    </td>
                    <!-- 其他记录录入 end -->
                </tr>
                </tbody>
            </table>
        </div>
        <div style="bottom: 15px;" class="zui-table-fixed table-fixed-l" v-if="num==0">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <!-- <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th> -->
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>床位号</span></div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m "><span>姓名</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div style="overflow-y: auto;" class="zui-table-body zuiTableBody" data-no-change
                 @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr style="height: 40.22px;" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="hoverMouse(true,$index)"
                        :tabindex="$index"
                        v-for="(item, $index) in twbList"
                    >
                        <!-- <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="item.xssx"></div>
                        </td> -->
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="item.rycwbh"></div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">{{item.brxm}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div v-if="num==1" class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <!-- <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                    </th>-->
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>床位号</span></div>
                    </th>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m "><span>姓名</span></div>
                    </th>
                    <th class="cell-xxl">
                        <div class="text-center cell-xxl  "><span>小便</span></div>
                        <div class="flex-container cell-xxl flex-align-c flex-wrap-n-w">
                            <div class="text-center border-child border-right-none">量</div>
                            <div class="text-center border-child">色</div>
                            <div class="text-center border-child">导尿</div>
                        </div>
                    </th>
                    <th class="cell-xxl">
                        <div class=" text-center cell-xxl  "><span>大便</span></div>
                        <div class="flex-container cell-xxl flex-align-c flex-wrap-n-w">
                            <div class="text-center border-child border-right-none">(次)</div>
                            <div class="text-center border-child border-right-none">色</div>
                            <div class="text-center border-child">质</div>
                            <div class="text-center border-child">量</div>
                        </div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>灌肠前大便</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>灌肠</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>人工肛门</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>引流量(ml)</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>其他出量(ml)</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>饮入量(ml)</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>输入量(ml)</span></div>
                    </th>
                    <th class="cell-l">
                        <div class="text-center  "><span>上午血压</span></div>
                        <div class="flex-container flex-align-c flex-wrap-n-w">
                            <div class="text-center border-child border-right-none">收缩(mmhg)</div>
                            <div class="text-center border-child">舒张(mmhg)</div>
                        </div>
                    </th>
                    <th class="cell-l">
                        <div class="text-center  "><span>下午血压</span></div>
                        <div class="flex-container flex-align-c flex-wrap-n-w">
                            <div class="text-center border-child border-right-none">收缩(mmhg)</div>
                            <div class="text-center border-child">舒张(mmhg)</div>
                        </div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>体重（kg）</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>身高（cm）</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>脉象</span></div>
                    </th>
                    <th class="cell-lxx">
                        <div class="text-center  "><span>舌</span></div>
                        <div class="flex-container flex-align-c flex-wrap-n-w">
                            <div class="text-center border-child border-right-none cell-s">舌苔</div>
                            <div class="text-center border-child">舌质</div>
                        </div>
                    </th>
                    
                    <th>
                        <div class="zui-table-cell  cell-s"><span>电休克(次数)</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell  cell-s"><span>基础代谢</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell  cell-s"><span>其他项目1</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell  cell-s"><span>其他项目2</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell  cell-s"><span>其他项目3</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell  cell-s"><span>其他项目4</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div v-if="num==1" class="zui-table-body zuiTableBody" data-no-change @scroll="scrollTable($event)">
            <table class="zui-table ">
                <tbody>
                <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="hoverMouse(true,$index)"
                    :tabindex="$index"
                    v-for="(item, $index) in twbqtjlList"
                >
                    <!-- <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>-->
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="item.rycwbh"></div>
                    </td>
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="item.brxm"></div>
                    </td>
                    <td class="cell-xxl">
                        <div class="zui-table-cell cell-xxl  flex-wrap-n-w flex-container">
                            <div class="border-child border-right-none">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xbl"/>
                            </div>
                            <div class="border-child border-right-none">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xbs"/>
                            </div>
                            <div class="border-child border-right-none">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.bldn"/>
                            </div>
                        </div>
                    </td>
                    <td class="cell-xxl">
                        <div class="zui-table-cell cell-xxl flex-wrap-n-w flex-container ">
                            <div class="border-child border-right-none">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbcs"/>
                            </div>
                            <div class="border-child">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbs"/>
                            </div>
                            <div class="border-child">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbz"/>
                            </div>
                            <div class="border-child">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dbl"/>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.gcqdbcs"/>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.gc"/>
                        </div>
                    </td>
                    <td>
                        <div class=" cell-s" >
                            <select-input @change-data="changeQtData($event,$index)" :data-notEmpty="true"
                                          :child="rygm_tran" :index="item.rygm" :val="item.rygm"
                                          :name="'item.rygm'">
                            </select-input>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.yll"/>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.qtcl"/>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.yrl"/>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.srl"/>
                        </div>
                    </td>
                    <td class="wh160">
                        <div class="zui-table-cell wh160 flex-wrap-n-w flex-container ">
                            <div class="border-child border-right-none">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.swSsy"/>
                            </div>
                            <div class="border-child">
                                <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.swSzy"/>
                            </div>
                        </div>
                    </td>
                    <td class="wh160">
                        <div class="zui-table-cell wh160 flex-wrap-n-w flex-container ">
                            <div class="border-child border-right-none">
                                <input @keydown="nextFocus($event)" class="zui-input " type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xwSsy"/>
                            </div>
                            <div class="border-child">
                                <input @keydown="nextFocus($event)" class="zui-input " type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.xwSzy"/>
                            </div>
                        </div>
                    </td>

                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.tz"/>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" >
                            <input @keydown="nextFocus($event)" class="zui-input" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.sg"/>
                        </div>
                    </td>
                    <td>
                        <div class=" cell-s" >
                            <select-input @changevalue="resultChangeMx"  @change-data="changeQtData($event,$index)" :child="mx_tran" :index="item.mx"
                                          :val="item.mx" :name="'item.mx'" :search="true">
                            </select-input>
                        </div>

                    </td>
                    <td class="cell-l">
                        <div class=" cell-l flex-wrap-n-w flex-container ">
                                <select-input @changevalue="resultChangeMx" @change-data="changeQtData($event,$index)" :child="st_tran" :index="item.st"
                                              :val="item.st" :name="'item.st'" :search="true">
                                </select-input>
                            <div class="border-child">
                                <select-input @changevalue="resultChangeMx" @change-data="changeQtData($event,$index)" :child="sz_tran" :index="item.sz"
                                              :val="item.sz" :name="'item.sz'" :search="true">
                                </select-input>
                            </div>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" ><input @keydown="nextFocus($event)" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.dxk" class="zui-input"/></div></td>
                    <td><div class="zui-table-cell cell-s" ><input @keydown="nextFocus($event)" type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.jcdx" class="zui-input"/></div></td>
                    <td><div class="zui-table-cell cell-s" ><input @keydown="nextFocus($event)"  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.qtxm1" class="zui-input"/></div></td>
                    <td><div class="zui-table-cell cell-s" ><input @keydown="nextFocus($event)"  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.qtxm2" class="zui-input"/></div></td>
                    <td><div class="zui-table-cell cell-s" ><input @keydown="nextFocus($event)"  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.qtxm3" class="zui-input"/></div></td>
                    <td><div class="zui-table-cell cell-s" ><input @keydown="nextFocus($event)"  @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent v-model="item.qtxm4" class="zui-input"/></div></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="zui-table-fixed table-fixed-l" v-if="num==1">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <!-- <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>-->
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>床位号</span></div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m "><span>姓名</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body zuiTableBody" data-no-change
                 @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="hoverMouse(true,$index)"
                        :tabindex="$index"
                        v-for="(item, $index) in twbqtjlList"
                    >
                        <!--  <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="item.xssx"></div>
                        </td>-->
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="item.rycwbh"></div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">{{item.brxm}}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="childpage/batch.js"></script>
