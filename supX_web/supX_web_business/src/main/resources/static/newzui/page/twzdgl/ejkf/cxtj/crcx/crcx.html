
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出入库查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b " @click="exportMxtz">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">方式</label>
                    <div class="zui-input-inline wh122 margin-f-l20">
                        <select-input @change-data="resultRydjChange"
                                      :child="rkfsList" :index="'rkmc'" :index_val="'rkfs'" :val="param.rkfs"
                                      :name="'param.rkfs'" :search="true" :index_mc="'rkmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline">
                        <input class="zui-input todate wh240 " placeholder="请选择申请日期" id="timeVal"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input  wh240" placeholder="请输入关键字" v-model="param.parm"  @keydown.13="goToPage(1)"/>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10"  :money="money">
       <!--入库单列表-->
        <div class="zui-table-header" v-show="qShow">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>入库单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>入库方式</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>审核日期</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>备注</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " v-show="qShow" @scroll="scrollTable($event)" >
            <table class="zui-table table-width50">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in djList"  :class="[{'table-hovers':$index===activeIndex}]" >
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.rkdh"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="rkfs[item.rklx]"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.zdrxm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')"></div></td>
                    <td><div class="zui-table-cell cell-xl" v-text="item.bzms"></div></td>
                </tr>
                <p v-if="djList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tbody>
            </table>
        </div>
        <!--入库单列表end-->
        <!--入库单明细-->
        <div class="zui-table-header" v-show="rkmxShow">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>药品规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>入库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>进价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " v-show="rkmxShow" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr :tabindex="$index" v-for="(item, $index) in mxList" :class="[{'table-hovers':$index===activeIndex}]" >
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-xl text-over-2">{{item.ypmc}}</div></td>
                    <td><div class="zui-table-cell cell-xl">{{item.ypgg}}</div></td>
                    <td><div class="zui-table-cell cell-s relative">{{item.rksl}}</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypjj"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.ypjj*item.rksl,2)"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yplj"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDec(item.yplj*item.rksl,2)"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.scph"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cdmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.fzbl"></div></td>
                </tr>
                </tbody>
            </table>
        </div>
        <!--入库单明细end-->

        <!--出库单明细end-->
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="rkgl-position" v-show="rkmxShow || ckmxShow">
           <div class="rkgl-fl flex-container">
               <i>药品进价总价: <em class="color-wtg" v-text='fDec(json.jjzj,2)'></em></i>
               <i>药品零价总价: <em class="color-wtg" v-text='fDec(json.ljzj,2)'></em></i>
           </div>
            <div class="rkgl-fr flex-container">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="cancel">返回</button>

           </div>
        </div>

    </div>

</div>
<script src="crcx.js"></script>

</body>

</html>
