<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>修改密码</title>
    <link rel="stylesheet" href="changePsw.css">
    <script type="application/javascript" src="/pub/top.js"></script>
</head>
<body>
<div class="pswDiv">
    <div class="psw">
        <div class="changePsw">
            <div class="title">修改密码</div>
            <div class="pswInput">
                <span>旧密码：</span>
                <input type="password" :class="{'error': error == 1}" v-model="oldPsw">
            </div>
            <div class="pswInput">
                <span>新密码：</span>
                <input type="password" :class="{'error': error == 2}" v-model="newPsw">
            </div>
            <div class="pswInput">
                <span>确认密码：</span>
                <input type="password" :class="{'error': error == 3}" v-model="againPsw">
            </div>
            <input type="button" @click="doPsw" value="确定修改" class="change">
        </div>
    </div>
</div>
</body>
<script src="changePsw.js" type="application/javascript"></script>
</html>
