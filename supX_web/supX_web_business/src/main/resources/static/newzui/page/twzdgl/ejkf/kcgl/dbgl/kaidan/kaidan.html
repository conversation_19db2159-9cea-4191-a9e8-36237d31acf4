<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>调拨管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper" :ks="getKS" :money="money">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel()" >添加材料</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="initial()">刷新</button>
        </div>
        <div class="tong-search tong-padded">
            <div class="jbxx">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">调出科室</label>
                            <div class="zui-input-inline wh122 margin-l-25" style="margin-left: 50px">
                                <select-input @change-data="resultChanges"
                                              :not_empty="false" :child="yfList"
                                              :index="'yfmc'" :index_val="'yfbm'"
                                              :val="popContent.yfbm" :search="true" :name="'popContent.yfbm'"
                                              :index_mc="'yfbm'" >
                                </select-input>
                            </div>
                        </div>
						<div class="zui-inline padd-l-40 margin-f-l10">
						    <label class="zui-form-label">调入科室</label>
						    <div class="zui-input-inline wh122 margin-l-25" style="margin-left: 50px">
						        <select-input @change-data="resultChange"
						                      :not_empty="false" :child="dqyfList"
						                      :index="'yfmc'" :index_val="'yfbm'"
						                      :val="popContent.dbyf" :search="true" :name="'popContent.dbyf'"
						                      :index_mc="'yfbm'" >
						        </select-input>
						    </div>
						</div>

                        <div class="zui-inline padd-l-40 margin-f-l10">
                            <label class="zui-form-label">备注描述</label>
                            <div class="zui-input-inline wh150 margin-l-25">
                                <input class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="popContent.bzms" />
                            </div>
                        </div>
                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i class="color-wtg" v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view ybglTable">
        <div class="zui-table-header ">
            <table class="zui-table ">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-xl"><span>供应商</span></div></th>
                    <th><div class="zui-table-cell cell-l "><span>材料编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>二级库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)" >
            <table class="zui-table ">
                <tbody>
                <tr v-for="(item,$index) in jsonList"   @dblclick="edit($index)" @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2 text-left">{{item.ypmc}}<span class="color-wtg">{{item.ypspm}}</span></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-xl  " v-text="item.ghdwmc">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l  " v-text="item.ypbm">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypgg">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">{{item.cksl}}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc">状态</div>
                    </td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>

        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="zui-table-tool rkgl-position">
             <span class="rkgl-fl">
               <i>材料进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></i>
               <i>材料零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
           </span>

            <span class="rkgl-fr flex-container">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()">提交</button>
           </span>
        </div>
    </div>
</div>
    <div class="side-form ng-hide pop-548"    id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--编辑材料-->
        <div class="ksys-side">
            <ul class="tab-edit-list tab-edit2-list">
                <li>
                        <i>申请日期</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                        <input class="zui-input text-indent20"  disabled="disabled" v-model="fDate(dbdContent.zdrq,'date')">
                </li>
                <li>
                        <i>制单人</i>
                        <select-input @change-data="resultChange"  :child="ryList" :index="'ryxm'"
                                      :index_val="'rybm'" :val="dbdContent.zdr" :search="true" :name="'dbdContent.zdr'">
                        </select-input>
                </li>
                <li>
                        <i>材料名称</i>
                        <input id="ypmc" ref="ypmc" class="label-input" v-model="popContent.ypmc" @keyup="changeDown($event,'ypmc')"
                               @input="change('ypmc',false,$event.target.value)">
                        <search-table :message="searchCon" :selected="selSearch" :page="page"  :them="them" :them_tran="them_tran"
                                      @click-one="checkedOneOut" @click-two="selectOne">
                        </search-table>
                </li>
                <li>
                        <i>材料规格</i>
                        <input type="text" class="label-input background-h" id="cksl" disabled="disabled" v-model="popContent.ypgg" />
                </li>
                <li>
                        <i>商品名</i>
                        <input type="text" class="label-input background-h"  disabled="disabled" v-model="popContent.ypspm" />
                </li>
                <li>
                    <i>供应商</i>
                    <input type="text" class="label-input background-h"  disabled="disabled" v-model="popContent.ghdwmc" />
                </li>
                <li>
                        <i>出库数量</i>
                        <input type="number" class="label-input" @keydown="changeDown($event,'cksl')" v-model="popContent.cksl" />
                </li>
                <li>
                        <i>材料批号</i>
                        <input type="text" class="label-input background-h" disabled="disabled" v-model="popContent.xtph" />
                </li>
                <li>
                        <i>生产批号</i>
                        <input type="number" class="label-input background-h" disabled="disabled" v-model="popContent.scph">
                </li>

                <li>
                        <i>生产日期</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                        <input style="padding-left: 8px;" type="text" class="label-input text-indent20 background-h" disabled="disabled" v-model="fDate(popContent.scrq,'date')">
                </li>
                <li>
                        <i>有效期至</i>
                        <em class="icon-position icon-rl" style="left: 74px;"></em>
                        <input type="text" class="label-input background-h" id="yxqz" disabled="disabled" v-model="fDate(popContent.yxqz,'date')">
                </li>
                <li>
                        <i>材料零价</i>
                        <input type="number" class="label-input background-h" disabled="disabled" v-model="fDec(popContent.yplj,2)">
                </li>
                <li>
                        <i>分装比例</i>
                        <input type="text" class="label-input " disabled="disabled"  v-model="popContent.fzbl">
                </li>
                <li>
                        <i>产地/公司</i>
                        <input type="text" class="label-input background-h" disabled="" v-model="popContent.cdmc">
                </li>
                <li>
                        <i>供货单位</i>
                        <input type="text" class="label-input background-h" disabled=""  v-model="popContent.ghdwmc">
                </li>
                <li>
                        <i>库存数量</i>
                        <input type="text" class="label-input background-h" disabled="" v-model="popContent.kcsl">
                </li>

                <!--<li>
                        <i>备注信息</i>
                        <input type="text" class="label-input " v-model="dbdContent.bzms" @keydown.enter="add">
                </li>-->
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="add">保存</button>
        </div>
    </div>

</div>
<!--侧边窗口-->

<script src="kaidan.js"></script>
</body>

</html>
