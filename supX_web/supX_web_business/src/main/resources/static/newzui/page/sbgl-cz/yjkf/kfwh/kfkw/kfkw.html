<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>库房库位</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<style>

</style>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
<div class="wrapper" id="wrapper" v-cloak>
    <div class="panel ">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5 icon-font15" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b"><i class=" icon-width icon-dc "></i>导出</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">库房</label>
                    <div class="zui-input-inline wh122 margin-l-5">
                        <select-input @change-data="resultRydjChange"
                                      :child="kfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text"  v-model="param.parm" @keydown.13="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view " >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                 :type="'all'" :val="isCheckAll">
                        </input-checkbox></span></div></th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库位编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库位名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  @dblclick="edit($index)" @click="checkSelect([$index,'one','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]" >
                    <td  class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>

                    <td><div class="zui-table-cell cell-s" v-text="item.kwbm">药品收入</div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" ><i v-text="item.kfbm" style="width: 84px;display: block;overflow: hidden;"></i></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" ><i v-text="item.kwmc" style="width: 84px;display: block;overflow: hidden;"></i></div>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pydm">药品收入</div></td>
                    <td>
                        <div class="zui-table-cell cell-s">

                            <div class="switch">
                                <input  type="checkbox" :checked="item.tybz==0?true:false" disabled/>
                                <label></label>
                            </div>
                        </div>

                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em  class="width30">
                                    <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                               </span>
                        </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<div class="side-form ng-hide pop-width"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
        <div class="ksys-side">
            <span class="span0">
                <i>库位编码</i>
                <input type="text" class="zui-input border-r4" v-model="popContent.kwbm" disabled="disabled" placeholder="自动生成"/>
            </span>
            <span class="span0">
                <i>库位名称</i>
                <input type="text" class="zui-input border-r4" v-model="popContent.kwmc" @keydown="nextFocus($event)" @blur="setPYDM(popContent.kwmc,'popContent','pydm')"/>
            </span>
            <span class="span0">
                <i>拼音代码</i>
                <input type="text" class="zui-input border-r4" disabled v-model="popContent.pydm" @keydown="nextFocus($event)"/>
            </span>
            <span  class="margin-top-10 span0">
                <i style="float:left;">状态</i>
                <div class="switch" >
                    <input type="checkbox" true-value="0" false-value="1" v-model="popContent.tybz" id="tybz"/>
                    <label for="tybz"></label>
                </div>
            </span>
        </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>

<script src="kfkw.js"></script>
</body>

</html>
