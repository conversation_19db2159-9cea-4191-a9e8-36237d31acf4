<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药品组合处方</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../css/main.css" rel="stylesheet">
    <link type="text/css" href="ypzhcf.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="wrapper background-f" id="jyxm_icon">
    <div class="panel tong-top flex-container   flex-align-c">
            <button v-waves class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button v-waves class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>
            <button v-waves class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            <div class="flex-container   flex-align-c margin-l-10">
                    <span class="whiteSpace margin-r-1 ft-14 padd-r-5">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="param.parm"
                               id="jsvalue" @keydown="searchHc()"/>
            </div>

    </div>
    <div class="zui-table-view " z-height="full">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>组合医嘱</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-l text-left"><span>组合医嘱名称</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>医嘱类型</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>拥有者</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>拥有科室</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>处方类型</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>类型</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>治法</span></div>
                    </th>
                    <th>
                        <div class="zui-table-cell cell-s"><span>主治</span></div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item, $index) in jsonList" @dblclick="goNew(item.zhyzbm,item.zhyzmc, item.yfbm, item.cflxbm)"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'some','jsonList'],$event)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zhyzbm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l text-left " v-text="item.zhyzmc">

                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.pydm">

                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="zhyzlx_tran[item.zhyzlx]">

                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yyzxm">

                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yyksmc">

                        </div>

                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cflxmc">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="zhyzBylx_tran[item.lx]">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zf">
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.zz">
                        </div>
                    </td>
                    <td class="cell-s">
                        <div class="zui-table-cell cell-s">
                        <span class="flex-center padd-t-5">
                            <em class="width30"><i class="icon-bj" @click="edit($index)" data-title="编辑"></i></em>
                            <em class="width30"><i class="icon-sc icon-font" data-title="删除"
                                                   @click="remove($index)"></i></em>
                           </span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>

        </div>

        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                            </div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <!-- data-no-change -->
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList"
                        :tabindex="$index"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s">
                            <span class="flex-center padd-t-5">
                            <em class="width30"><i class="icon-bj" @click="edit($index)" data-title="编辑"></i></em>
                            <em class="width30"><i class="icon-sc icon-font" data-title="删除"
                                                   @click="remove($index)"></i></em>
                           </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>


        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>


<div class="side-form ng-hide pop-548" style="padding-top: 0;" id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <li>
                <i>组合医嘱名称</i>
                <input type="text" class="label-input" v-model="popContent.zhyzmc" data-notEmpty="false"
                       @keydown="nextFocus($event)" @blur="setPYDM(popContent.zhyzmc,'popContent','pydm')"/>
            </li>
            <li>
                <i>拼音代码</i>
                <input type="text" class="label-input" disabled v-model="popContent.pydm" @keydown="nextFocus($event)"
                       data-notEmpty="false"/>
            </li>
            <li>
                <i>医嘱类型</i>
                <select-input @change-data="resultChange" :not_empty="false" :search="true"
                              :child="zhyzlx_tran" :index="popContent.zhyzlx" :val="popContent.zhyzlx"
                              :name="'popContent.zhyzlx'">
                </select-input>
            </li>
            <li>
                <i>拥有者</i>
                <select-input @change-data="resultChange" :not_empty="false" :search="true"
                              :child="rybmList" :index="'ryxm'" :index_val="'rybm'" :val="popContent.yyz"
                              :name="'popContent.yyz'">
                </select-input>
            </li>
            <li>
                <i>拥有科室</i>
                <select-input @change-data="resultChange" :not_empty="false" :search="true"
                              :child="ksbmList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.yyks"
                              :name="'popContent.yyks'">
                </select-input>
            </li>
            <li>
                <i>处方类型 </i>
                <select-input @change-data="resultChange" :not_empty="true" :search="true"
                              :child="cflxList" :index="'cflxmc'" :index_val="'cflxbm'" :val="popContent.cflxbm"
                              :name="'popContent.cflxbm'">
                </select-input>
            </li>
            <li>
                <i>类型 </i>
                <select-input @change-data="resultChange" :not_empty="false" :search="true"
                              :child="zhyzBylx_tran" :index="popContent.lx" :val="popContent.lx"
                              :name="'popContent.lx'">
                </select-input>
            </li>
            <li>
                <i>中医用法 </i>
                <select-input @change-data="resultChange" :not_empty="false" :child="yysmList" :index="'yysmmc'"
                              :index_val="'yysmbm'" :val="popContent.zyyf" :name="'popContent.zyyf'"
                              :search="true">
                </select-input>
            </li>
            <li>
                <i>治法 </i>
                <input type="text" class="label-input" v-model="popContent.zf" @keydown="nextFocus($event)"
                       data-notEmpty="false"/>
            </li>
            <li>
                <i>是否草药 </i>
                <select-input @change-data="resultChange" :not_empty="false" :search="true"
                              :child="istrue_tran" :index="popContent.sfcy" :val="popContent.sfcy"
                              :name="'popContent.sfcy'">
                </select-input>
            </li>
            <li>
                <i>药房 </i>
                <select-input @change-data="resultChange" :not_empty="true" :search="true"
                              :child="yfList" :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm"
                              :name="'popContent.yfbm'" :search="true">
                </select-input>
            </li>
            <li>
                <i>主治 </i>
                <input type="text" class="label-input" v-model="popContent.zz" @keydown.13="saveData()"
                       data-notEmpty="false"/>
            </li>

        </ul>
    </div>
    <div class="ksys-btn">
        <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveData">保存</button>
    </div>
</div>
<script src="ypzhcf.js"></script>
</body>
</html>
