<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>退货开单</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="pdgl.css" rel="stylesheet"/>
    <script src="scpd.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="wrapper" v-cloak>
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" v-if="!queryData.pdShow" @click="AddMdel">添加物资</button>
                <!--                <button class="tong-btn btn-parmary-b" @click="zdsc"><i class=" icon-width icon-width-top icon-zdsc-l "></i>自动生成</button>-->
                <!--                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="sx">刷新</button>-->
            </div>
            <div class="tong-search tong-padded">
                <div class="jbxx">
                    <div class="jbxx-size">
                        <div class="jbxx-position">
                            <span class="jbxx-top"></span>
                            <span class="jbxx-text">基本信息</span>
                            <span class="jbxx-bottom"></span>
                        </div>
                        <div class="flex-container flex-align-c padd-t-20">
                            <div class="flex-container padd-r-10 padd-l-10 flex-align-c">
                                <span class="ft-14 padd-r-5 whiteSpace">库房</span>
                                    <select-input class="wh122" :disable="queryData.sh" @change-data="resultChange"
                                                  :not_empty="false" :child="kfList"
                                                  :index="'wzkfmc'" :index_val="'wzkfbm'"
                                                  :val="popContent.kfbm" :search="true" :name="'popContent.kfbm'"
                                                  :index_mc="'wzkfmc'">
                                    </select-input>
                            </div>
                            <div class="flex-container flex-align-c padd-r-10" v-if="queryData.pdShow">
                                <span class="ft-14 padd-r-5 whiteSpace">盘点方式</span>
                                    <select-input class="wh122" @change-data="resultChange" :not_empty="false" :child="pdfs_tran"
                                                  :index="'popContent.pdfs'" :val="popContent.pdfs"
                                                  :name="'popContent.pdfs'" :search="true">
                                    </select-input>
                            </div>
                            <!--                            v-if="queryData.pdShow"-->
                            <div class="flex-container flex-align-c padd-r-10" v-if="popContent.pdfs == '1' || popContent.pdfs == '2'">
                                <span class="ft-14 padd-r-5 whiteSpace">盘点类别</span>
                                    <select-input class="wh122" v-if="popContent.pdfs == '1'" @change-data="resultChange"
                                                  :not_empty="false" :child="dlList"
                                                  :index="'dlmc'" :index_val="'dlbm'"
                                                  :val="popContent.dlbm" :search="true" :name="'popContent.dlbm'"
                                                  :index_mc="'dlmc'">
                                    </select-input>
                                    <select-input class="wh122" v-if="popContent.pdfs == '2'" @change-data="resultChange"
                                                  :not_empty="false" :child="lbList"
                                                  :index="'lbmc'" :index_val="'lbbm'"
                                                  :val="popContent.lbbm" :search="true" :name="'popContent.lbbm'"
                                                  :index_mc="'lbmc'">
                                    </select-input>
                            </div>
                            <div class="flex-container padd-r-10 flex-align-c">
                                <span class="ft-14 padd-r-5 whiteSpace ">备注</span>
                                <input class="zui-input wh240" :disabled="queryData.sh" placeholder="请输入备注" type="text"
                                       v-model="popContent.bzms"/>
                            </div>
                            <button v-if="queryData.pdShow" class="tong-btn btn-parmary icon-xz1 paddr-r5"
                                    @click="zdpd()">生成盘点
                            </button>
                        </div>
                        <div class="rkgl-kd">
                            <span>开单日期:<i class="color-yzf">{{queryData.sh ? fDate(popContent.zdrq,'AllDate') : fDate(new Date(),'AllDate')}}</i></span>
                            <span>开单人：<i class="color-dsh">{{popContent.zdrmc}}</i></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view   ">
            <div class="zui-table-header ">
                <table class="zui-table ">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl text-left"><span>物资名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>物资规格</span></div>
                        </th>
                        <!-- <th><div class="zui-table-cell cell-s"><span>退库数量</span></div></th>-->
                        <th>
                            <div class="zui-table-cell cell-s"><span>物资进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>物资零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>物资批号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>物资产地</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>库房单位</span></div>
                        </th>
                        <!-- <th><div class="zui-table-cell cell-s"><span>领用单位</span></div></th>-->
                        <th>
                            <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                        </th>
                        <th v-if="!queryData.scpd" class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table ">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList" @dblclick="edit($index)"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-left" v-text="item.wzmc"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.wzgg">物资规格</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.jj">物资进价</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.dj">物资零价</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.xtph">物资批号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">有效期至</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cd">物资产地</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.kfdwmc">库房单位</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fzbl"> 分装比例</div>
                        </td>
                        <td class="cell-s" v-if="!queryData.scpd">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em  class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                                            @click="edit($index)"></i></em>
                                <em  class="width30"><i class="icon-sc" data-title="删除"
                                                                            @click="delectFun($index)"></i></em>
                            </span>
                            </div>
                        </td>
                        <!--暂无数据提示信息,绑数据放开-->
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--右侧固定-->
            <div class="zui-table-fixed table-fixed-r" v-if="!queryData.scpd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                       @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除"
                                                       @click="delectFun($index)"></i></em>
                            </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="action-bar fixed rkgl-position">
                <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db margin-r-10" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()"
                        v-if="(!queryData.sh && !queryData.dy)">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="sh()" v-if="queryData.sh">审核</button>
               <button class="tong-btn btn-parmary xmzb-db" @click="dy()" v-if="queryData.dy">打印</button>
           </span>
            </div>
        </div>
    </div>
    <div class="side-form  pop-548" :class="{'ng-hide':num==0}" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--编辑药品-->
        <div class="ksys-side">
            <ul class="tab-edit-list tab-edit2-list" style="padding-top:4px !important;">

                <li>
                    <i>物资名称</i>
                    <input class="zui-input" v-model="popContent.wzmc" @keydown="changeDown($event,'wzmc')"
                           @input="change(false, $event.target.value)" id="wzmc"/>
                    <search-table :message="searchCon" :selected="selSearch" :current="dg.page" :rows="dg.rows"
                                  :total="total"
                                  :them="them" :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </li>
                <li>
                    <i>物资编码</i>
                    <input type="text" class="label-input background-h" v-model="popContent.wzbm" disabled="disabled"/>
                </li>
                <li>
                    <i>物资规格</i>
                    <input type="text" class="label-input background-h" v-model="popContent.wzgg" disabled="disabled"/>
                </li>
                <li>
                    <i>分装比例</i>
                    <input type="text" class="label-input background-h" v-model="popContent.fzbl" disabled>
                </li>
                <li>
                    <i>生产批号</i>
                    <input type="text" class="label-input background-h" v-model="popContent.scph" disabled>
                </li>
                <li>
                    <i>有效期至</i>
                    <input type="text" :value="fDate(popContent.yxqz,'date')"
                           class="label-input text-indent30 background-h" disabled>
                </li>
                <li>
                    <i>物资进价</i>
                    <input type="text" class="label-input background-h" v-model="popContent.jj" disabled>
                    <em class="xstz">元</em>
                </li>
                <li>
                    <i>物资零价</i>
                    <input type="text" class="label-input background-h" v-model="popContent.dj" disabled>
                    <em class="xstz">元</em>
                </li>
                <li>
                    <i>物资产地</i>
                    <input type="text" class="label-input background-h" v-model="popContent.cd" disabled>
                </li>
                <li>
                    <i>库房单位</i>
                    <input type="text" class="label-input background-h" v-model="popContent.kfdwmc" disabled>
                </li>

                <li>
                    <i>库存数量</i>
                    <input type="number" class="label-input  background-h" v-model="popContent.kcsl" disabled>
                </li>
                <li>
                    <i>实存数量</i>
                    <input type="number" class="label-input" v-model="popContent.scsl" oninput="clearNoNum(this)">
                </li>
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="save()">保存</button>
        </div>
    </div>

</div>
<!--侧边窗口-->

<script src="scpd.js"></script>

<script language="JavaScript" type="text/javascript">
    function clearNoNum(obj) {

        obj.value = obj.value.replace(/[^\d.]/g, "");  //清除“数字”和“.”以外的字符

        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的

        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");

        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数

        if (obj.value.indexOf(".") < 0 && obj.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额

            obj.value = parseFloat(obj.value);
        }
    }
</script>

</body>

</html>
