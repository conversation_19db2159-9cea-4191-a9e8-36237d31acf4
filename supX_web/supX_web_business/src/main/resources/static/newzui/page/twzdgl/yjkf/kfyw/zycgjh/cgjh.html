<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>采购计划</title>
	
	<script src="vue2_6.js"></script>
	      <link rel="stylesheet" href="tablecss.css">
	      <!-- 引入脚本 -->
	      <script src="xe-utils.js"></script>
	      <script src="vxe-table.js"></script>
    <link href="/lib/css/font-awesome.css" rel="stylesheet" type="text/css"/>
    <link href="/newzui/css/plugins/animate.min.min.css" rel="stylesheet" type="text/css" />
        <link href="/lib/css/font-awesome.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/css/plugins/animate.min.min.css" rel="stylesheet" type="text/css" />
            <link href="/newzui/css/page/kspb.min.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/pub/css/animation.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/css/page/hzgl.min.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/css/page/cwjf.min.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/js/zui/zui.min.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/newcss/fonts/font-awesome.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/css/common.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/css/main.css" rel="stylesheet" type="text/css"/>
            <link href="/newzui/css/login.min.css" rel="stylesheet" type="text/css"/>
            <link rel="stylesheet" href="/newzui/js/plugins/nprogress.css">
            <script type="text/javascript" src="/newzui/js/jquery.min.js"></script>
            
            <script type="text/javascript" src="/lib/vue/vue-resource.min.js"></script>
            <script type="text/javascript" src="/newzui/pub/js/dictionaries.js"></script>
            <script type="text/javascript" src="/lib/time/WdatePicker.js"></script>
            <script type="text/javascript" src="/newzui/js/content.js"></script>
            <script type="text/javascript" src="/pub/js/printTemplets.js"></script>
            <script type="text/javascript" src="/newzui/pub/js/jsg.min.js"></script>
            <script type="application/javascript" src="/newzui/pub/js/echarts.min.js"></script>
            <script src="/newzui/js/plugins/nprogress.js"></script>
            <script src="/newzui/currentCSS/js/vue/vuescroll.min.js"></script>
            <script src="/FR/ReportServer?op=emb&resource=finereport.js"></script>
            <script type="text/javascript" src="/newzui/pub/js/common.js"></script>
            <script type="text/javascript" src="/newzui/pub/js/components.js"></script>
            <script type="text/javascript" src="/lib/jquery.maskedinput.js"></script>,
            <script type="text/javascript" src="/newzui/js/plugins/moment.js"></script>
	<script type="application/javascript" src="tz.js"></script>
    <link type="text/css" href="cgjh.css" rel="stylesheet"/>
	
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box" >
    <div class="wrapper background-f" id="wrapper" v-cloak>
        <div class="panel  printHide" >
            <div class="tong-top">
				
				<button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="cgSh()" v-show="isSh && !isCk">审核</button>
				<button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kdFun(0)" v-show="isShowkd && !isSh && !isCk">开单</button>
				<button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="cancel()"v-show="!isShowkd">取消</button>
				<button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="addRow()"v-show="!isShowkd && !isSh && !isCk">增加行</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="saveFun()"v-show="!isShowkd && !isSh && !isCk">保存</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="sccg()" v-show="!isShowkd && !isSh && !isCk">生成采购</button>
				
            </div>
			
			<div class="tong-search" :class="{'tong-padded':isShow}">
			                <div class="flex-container padd-b-10 flex-align-c" v-show="isShowkd">
			                    
			                    <div class="flex-container flex-align-c padd-r-10">
			                        <span class="ft-14 padd-r-5 whiteSpace ">审核标志</span>
			                        <select-input @change-data="resultChange"
			                                      :child="cglzt_tran"
			                                      class="wh122"
			                                      :index="param.zfbz"
			                                      :val="param.zfbz"
			                                      :name="'param.zfbz'"  >
			                        </select-input>
			                    </div>
								<div class="flex-container flex-align-c padd-r-10">
									<span class="ft-14 padd-r-5 whiteSpace">时间段</span>
									<div class="  flex-container flex-align-c">
										<input class="zui-input  wh160 " placeholder="请选择申请开始日期" id="timeVal" />
										<span class="padd-l-5 padd-r-5">~</span>
										<input class="zui-input todate wh160 " placeholder="请选择申请结束时间" id="timeVal1" />
									</div>
								</div>
			                    <div class="flex-container flex-align-c">
			                        <span class="ft-14 padd-r-5 whiteSpace">检索</span>
			                            <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text" v-model="param.jhdh" />
			                    </div>
			                </div>
			                
			
			            </div>
        </div>
		
		<div class="zui-table-view" >
			
			<vxe-grid
			      border
			      resizable
				  highlight-hover-row
				  highlight-current-row
				  empty-text="没有更多数据了！"
			      ref="xGrid"
			      height="400"
			      :pager-config="tablePage"
			      :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
			      :columns="table"
			      :data="cgList"
				  @cell-click="cellClickEvent"
				  @cell-dblclick="dbclickFun"
				  @page-change="handlePageChange"
			    >
				<template v-slot:operate="{ row }">
					<vxe-button type="text" title="编辑" v-if="row.shzfbz == 0" icon="fa icon-bj" @click="openDetail(row)"></vxe-button>
					<vxe-button type="text" title="删除" v-if="row.shzfbz == 0" icon="fa fa-trash" @click="removeRowEvent(row)"></vxe-button>
					<vxe-button type="text" title="审核" v-if="row.shzfbz == 0" icon="fa icon-sh" @click="showDetail(row)"></vxe-button>
					<vxe-button type="text" title="作废" v-if="row.shzfbz == 0" icon="fa icon-js" @click="invalidData(row)"></vxe-button>
				</template>
				</vxe-grid>
				
				<vxe-grid
							      border
							      resizable
							      ref="xdeGrid"
							      height="300"
							      :columns="detailtable"
							      :data="jsonList"
							    >
								</vxe-grid>
		</div>
		
		
		
</div>

<script src="cgjh.js"></script>
</body>

</html>
