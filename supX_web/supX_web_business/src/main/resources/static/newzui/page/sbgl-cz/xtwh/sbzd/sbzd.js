var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        title: '',
        num: 0,
        showUpload:false,
        jsonList: [],
        totlePage: 0,
        ajaxUrl :{
            '0': 'New1SbglXtwhSbdl',
            '1': 'New1SbglXtwhSblb',
            '2': 'New1SbglXtwhSbdw',
            '3': 'New1SbglXtwhSbzd',
            '4': 'New1SbglXtwhGys',
        },
        param: {
            page: 1,
            rows: 100,
            parm: '',
        }
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        //初始化加载
        this.getData();
    },
    methods: {
        //新增
        AddMdel: function () {
            wap.isUpdate = 0;//区别是新增还是修改
            wap.loadNum();
            wap.open();
            wap.popContent = {};
            switch (this.num) {
                case 0:
                    wap.title = '新增设备大类';
                    break;
                case 1:
                    wap.title = '新增设备类别';
                    break;
                case 2:
                    wap.title = '新增设备单位';
                    break;
                case 3:
                    wap.title = '新增设备字典';
                    break;

            }
        },
        del: function () {
            this.remove(-1);
        },
        //进入页面加载列表信息
        //切换
        tabBg: function (index) {
            this.num = index;
            wap.num = index;
            this.isCkecked = [];
            this.jsonList = [];
            this.getData();
        },
        getData: function () {
            this.jsonList = [];
            this.isChecked = [];
            console.log(123);
            //获取设备大类
            $.getJSON("/actionDispatcher.do?reqUrl=" + this.ajaxUrl[this.num] + "&types=query&json=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.jsonList = json.d.list;
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                } else {
                    malert("获取设备大类失败！", 'top', 'defeadted');
                }
            });

        },
        //删除2018/07/10二次删除弹窗提示
        remove: function (index) {
            var obj = {};
            var list = [];
            if (index == -1) {
                //批量操作
                if (this.isChecked.length == 0) {
                    malert("请选择要删除的项目！", 'top', 'defeadted');
                    return;
                }

                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i]) {
                        list.push(this.jsonList[i]);
                    }
                }

                if (list.length == 0) {
                    malert("请选择要删除的项目！", 'top', 'defeadted');
                    return;
                }

            } else {
                list.push(this.jsonList[index]);
            }
            Vue.set(obj, 'list', list);
                //设备大类
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                        wrapper.$http.post('/actionDispatcher.do?reqUrl='+wrapper.ajaxUrl[wrapper.num]+'&types=delete', JSON.stringify(obj)).then(function (json) {
                            if (json.a == 0 || json.body.a == 0) {
                                malert("删除成功！");
                                wrapper.getData();//重新加载
                            } else {
                                malert("删除失败！", 'top', 'defeadted');
                                return
                            }
                        });
                    })) {
                        return false;
                    }

        },
        //编辑修改根据num判断
        edit: function (num) {
            wap.loadNum();
            wap.open();
            wap.isUpdate = 1;
            switch (this.num) {
                case 0:
                    wap.title = '编辑设备大类';
                    break;
                case 1:
                    wap.title = '编辑设备类别';
                    break;
                case 2:
                    wap.title = '编辑设备单位';
                    break;
                case 3:
                    wap.title = '编辑设备字典';
                    break;
                case 4:
                    wap.title = '编辑设备供应商';
                    $("#yyzzname").text(wrapper.jsonList[num].yyzzname).attr('href','http://172.20.103.63:9081/'+wrapper.jsonList[num].yyzzfiles);
                    $("#jyxkzname").text(wrapper.jsonList[num].jyxkzname).attr('href','http://172.20.103.63:9081/'+wrapper.jsonList[num].jyxkzfiles);
                    $("#sqsname").text(wrapper.jsonList[num].sqsname).attr('href','http://172.20.103.63:9081/'+wrapper.jsonList[num].sqsfiles);
                    break;

            }
            wap.popContent = JSON.parse(JSON.stringify(wrapper.jsonList[num]));

        }
    }
});
var wap = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    components: {
        'search-table': searchTable
    },
    data: {
        nums: 0,//弹窗
        num: undefined,
        popContent: {},
        title: '',
        isUpdate: 0,
        yyzzfiles:null,
        jyxkzfiles:null,
        sqsfiles:null,
        dlList: [],//大类list
        dwList: [],//单位list
        lbList: [],//类别list
    },
    mounted: function () {
        this.getwzdl();
        this.getwzlb();
        this.getwzdw();
    },
    computed:{
        widths:function (){
            if(this.num == 0 || this.num == 1 || this.num == 2){
                $('.side-form').css({'width': '320px'})
            }else {
                $('.side-form').css({'width': '548px'})
            }
        },
    },
    methods: {
        changeFzbl: function (add, val) {
            if (!this.popContent.jj || this.popContent.jj == 0) {
                this.popContent.dj = 0.00;
            } else {
                this.popContent.dj = (this.popContent.jj / val).toFixed(2);
            }
            this.$forceUpdate()
        },
        closes: function () {
            this.nums = 0;
        },
        open: function () {
            this.nums = 1;
        },
        loadNum: function () {
            this.num = wrapper.num;
        },
        getFile: function(event,index) {
            if(index==1){
                this.yyzzfiles = (event.target.files[0]);
            }else if(index==2){
                this.jyxkzfiles = (event.target.files[0]);
            }else{
                this.sqsfiles = (event.target.files[0]);
            }
        },
        //保存
        save: function () {
            var type;
            if (wap.isUpdate == 1) {
                //修改
                type = "update";
            } else {
                //新增
                type = "save";
            }
            switch (wrapper.num) {
                case 0:
                    //新增大类
                    if (wap.popContent.dlmc == null || wap.popContent.dlmc == undefined || wap.popContent.dlmc == "") {
                        malert("请填写大类名称！", 'top', 'defeadted');
                        return;
                    }

                    this.$http.post('/actionDispatcher.do?reqUrl=New1SbglXtwhSbdl&types=' + type, JSON.stringify(wap.popContent)).then(function (json) {
                        if (json.a == 0 || json.body.a == 0) {
                            malert("保存成功！");
                            wap.popContent = {};
                            if (wap.isUpdate == 1) {
                                wap.closes();
                            }
                            wrapper.getData();//重新加载
                        } else {
                            malert("保存失败！", 'top', 'defeadted');
                            return
                        }
                    });
                    break;
                case 1:
                    //新增设备类别
                    if (wap.popContent.dlbm == null || wap.popContent.dlbm == undefined || wap.popContent.dlbm == "") {
                        malert("请选择大类！", 'top', 'defeadted');
                        return;
                    }

                    if (wap.popContent.lbmc == null || wap.popContent.lbmc == undefined || wap.popContent.lbmc == "") {
                        malert("请填写类别名称！", 'top', 'defeadted');
                        return;
                    }

                    this.$http.post('/actionDispatcher.do?reqUrl=New1SbglXtwhSblb&types=' + type, JSON.stringify(wap.popContent)).then(function (json) {
                        if (json.a == 0 || json.body.a == 0) {
                            malert("保存成功！");
                            wap.popContent = {};
                            if (wap.isUpdate == 1) {
                                wap.closes();
                            }
                            wrapper.getData();//重新加载
                        } else {
                            malert("保存失败！", 'top', 'defeadted');
                            return
                        }
                    });
                    break;
                case 2:
                    //新增设备单位
                    this.$http.post('/actionDispatcher.do?reqUrl=New1SbglXtwhSbdw&types=' + type, JSON.stringify(wap.popContent)).then(function (json) {
                        if (json.a == 0 || json.body.a == 0) {
                            malert("保存成功！");
                            wap.popContent = {};
                            if (wap.isUpdate == 1) {
                                wap.closes();
                            }
                            wrapper.getData();//重新加载
                        } else {
                            malert("保存失败！", 'top', 'defeadted');
                            return
                        }
                    });
                    break;
                case 3:
                //设备字典
                this.$http.post('/actionDispatcher.do?reqUrl=New1SbglXtwhSbzd&types=' + type, JSON.stringify(wap.popContent)).then(function (json) {
                    if (json.a == 0 || json.body.a == 0) {
                        malert("保存成功！");
                        wap.popContent = {};
                        if (wap.isUpdate == 1) {
                            wap.closes();
                        }
                        wrapper.getData();//重新加载
                    } else {
                        malert("保存失败！", 'top', 'defeadted');
                        return
                    }
                });
                break;
                case 4:
                    var formData = new FormData();
                    formData.append("jyxkzfiles", this.jyxkzfiles);
                    formData.append("yyzzfiles", this.yyzzfiles);
                    formData.append("sqsfiles", this.sqsfiles);
                    // formData.append("gysmc",wap.popContent.gysmc);
                    // formData.append("pydm",wap.popContent.pydm);
                    // formData.append("frdb",wap.popContent.frdb);
                    // formData.append("gsdz",wap.popContent.gsdz);
                    // formData.append("gsdh",wap.popContent.gsdh);
                    // formData.append("jjlxr",wap.popContent.jjlxr);
                    // formData.append("jjlxdh",wap.popContent.jjlxdh);
                    // formData.append("lxr",wap.popContent.lxr);
                    // formData.append("lxdh",wap.popContent.lxdh);
                    // formData.append("khhdz",wap.popContent.khhdz);
                    // formData.append("yhzh",wap.popContent.yhzh);
                    // formData.append("yx",wap.popContent.yx);
                    // formData.append("tybz",wap.popContent.tybz);
                    // formData.append("bz",wap.popContent.bz);
/*
                    this.$http.post('/actionDispatcher.do?reqUrl=New1FileUpload&types=uploadFile',formData).then(function (data) {
                        if (data.body.a == 0) {
                            content.popContent.xczp = data.body.d;
                            content.popContent.xctp = data.body.d.toString();
                            content.$forceUpdate();
                            malert('操作成功！','','success');
                        } else {
                            malert('操作失败！','','defeadted');
                        }
                    }, function (error) {
                        console.log(error);
                    });*/
                    console.log(formData);
                    var jsonStr = encodeURI(JSON.stringify(wap.popContent));
                    //设备供应商
                    this.$http.post('/actionDispatcher.do?reqUrl=New1SbglXtwhGys&types=' + type+'&json='+jsonStr,formData,JSON.stringify(wap.popContent)).then(function (json) {
                        if (json.a == 0 || json.body.a == 0) {
                            malert("保存成功！");
                            wap.popContent = {};
                            if (wap.isUpdate == 1) {
                                wap.closes();
                            }
                            wrapper.getData();//重新加载
                        } else {
                            malert("保存失败！", 'top', 'defeadted');
                            return
                        }
                    });
                    break;

            }
        },
        getwzdl: function () {
            //加载数据
            var parm = {
                page: 1,
                rows: 2000,
            }
            //获取设备大类
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhSbdl&types=query&json=" + JSON.stringify(parm), function (json) {
                if (json.a == "0") {
                    wap.dlList = json.d.list;
                } else {
                    malert("获取设备大类失败！", 'top', 'defeadted');
                }
            });
        },
        getwzdw: function () {
            //加载数据
            var parm = {
                page: 1,
                rows: 2000,
            }
            //获取设备单位
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhSbdw&types=query&json=" + JSON.stringify(parm), function (json) {
                if (json.a == "0") {
                    wap.dwList = json.d.list;
                } else {
                    malert("获取设备大类失败！", 'top', 'defeadted');
                }
            });
        },
        getwzlb: function () {
            //加载数据
            var parm = {
                page: 1,
                rows: 2000,
            }
            //获取设备类别
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhSblb&types=query&json=" + JSON.stringify(parm), function (json) {
                if (json.a == "0") {
                    wap.lbList = json.d.list;
                } else {
                    malert("获取设备大类失败！", 'top', 'defeadted');
                }
            });
        },
    }


});






