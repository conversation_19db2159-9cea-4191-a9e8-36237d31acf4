<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>票据领用</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="wrapper">
        <div class="panel ">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
            <div class="flex-container flex-align-c padd-b-10 padd-l-10 padd-t-10">
                <div class="flex-container flex-align-c">
                    <span class="ft-14 padd-r-5 whiteSpace">库房</span>
                    <select-input @change-data="resultRydjChange"
                                  :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm"
                                  :name="'popContent.kfbm'" :search="true" :index_mc="'kfmc'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-l-10">
                    <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                    <div class="  flex-container flex-align-c">
                        <input class="zui-input todate wh120 " v-model="param.beginrq" placeholder="请选择开始时间" id="timeVal"/><span
                            class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh120 " v-model="param.endrq" placeholder="请选择结束时间" id="timeVal1"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view ">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <input-checkbox @result="reCheckBox"
                                            :list="'jsonList'"
                                            :type="'all'" :val="isCheckAll">
                            </input-checkbox>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>入库单号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>审核日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>药品名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>药品规格</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>入库数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品批号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药品产地</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>库房单位</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>药房单位</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)"
                        :class="[{'table-hovers':$index===activeIndex}]">
                        <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">1</div>
                        </td>
                        <td class="relative">
                            <div class="zui-table-cell cell-l " :title="item.rkdh">
                                {{item.rkdh}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.shrq,'date')">西药收入</div>
                        </td>
                        <td class="relative">
                            <div class="zui-table-cell cell-xl" :title="item.ypmc">
                                {{item.ypmc}}
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.ypgg">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.rksl">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,2)">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,2)">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.scph">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cdmc">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.kfdwmc">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yfdwmc">药品收入</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fzbl">药品收入</div>
                        </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>

            </div>
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th>
                                <input-checkbox @result="reCheckBox"
                                                :list="'jsonList'"
                                                :type="'all'"
                                                :val="isCheckAll">
                                </input-checkbox>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex}]" class="tableTr2">
                            <td class="cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>

        </div>

    </div>
</div>

<script src="rkcx.js"></script>
</body>
</html>
