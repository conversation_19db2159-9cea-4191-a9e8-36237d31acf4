var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
    data: {
        yfkf: 0, //药房库房信息
        param: {
            'page': 1,
            'rows': 10,
            'order': 'desc',
            'shzfbz': 1,
            'kfbm': '',
            'ckfs': '03',//03-出库  报损
            'beginrq': null,
            'endrq': null,
            'parm': ''
        },
        jsonList: [],
        yfkfList: [],
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        this.getKf();
    },
    methods: {
        getKf: function () {
            //库房列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypkf', function (data) {
                    if (data.a == 0) {
                        Vue.set(wrapper.param, 'kfbm', data.d.list[0].kfbm);
                        wrapper.yfkfList = data.d.list;
                        wrapper.getData();
                    } else {
                        malert(data.c, 'top', 'defeadted');
                    }
                });
        },

        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this.param, 'kfbm', val[0]);
            Vue.set(this.param, 'yfkf', val[0]);
            Vue.set(this.param, 'kfmc', val[4]);
            this.getData();
        },
        getData: function () {
            $.getJSON("/actionDispatcher.do?reqUrl=YkglKfcxCrcx&types=ck&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                }
            });
        }
    },
});
laydate.render({
    elem: '.todate'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        wrapper.param.beginrq = value.slice(0, 10);
        wrapper.param.endrq = value.slice(13, 23);
        wrapper.getData();
    }
});






