.brjz-bottom.hljl {
    border: 1px solid #eeeeee;
    border-bottom: none;
}

.hjli-header {
    background: #fbfbfb;
    height: 43px;
    line-height: 43px;
}

.hljl .hjli-line {
    height: 40px;
    align-items: center;
    justify-content: space-between;
    align-content: center;
    width: 100%;
    cursor: pointer;
    border-bottom: 1px solid #eeeeee;
    display: flex;
}

.hljl .title-text {
    font-size: 14px;
    color: #757c83;
    margin: 0 60px 0 15px;
    width: 96%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    height: 40px;
    line-height: 40px;
    display: inline-block;
}

.text-position {
    width: 36%;
}

.hljl .hljl-date {
    font-size: 14px;
    margin-right: 24px;
    color: #757c83;
    text-align: center;
}

.hljl-left-line {
    margin-left: 15px;
    font-weight: bolder;
    margin-right: 17px;
}

.hljl-right-line {
    margin-right: 12px;
}

.wyc {
    margin-right: 26px;
}

.usermz {
    margin-right: 18px;
}

.green {
    color: #13a950;
}

.red {
    color: #ff4532;
}
.font-bolder{
    font-weight: bold;
}
.twd {
    width: 48.5%;
    margin-left: 8px;
}

.common-right {
    background: #ffffff;
    border: 1px solid #eeeeee;
    height: 244px;
}

.icon-date-left {
    position: relative;
    width: 22px;
    height: 22px;
    background-image: url("/newzui/pub/image/LeftButton.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    display: inline-block;
    margin-right: 9px;
    cursor: pointer;
    vertical-align: text-top;
}

.icon-date-right {
    position: relative;
    width: 22px;
    height: 22px;
    margin-left: 9px;
    background-image: url("/newzui/pub/image/RightButton.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    display: inline-block;
    cursor: pointer;
    vertical-align: text-top;
}

.date-text {
    vertical-align: middle;
}

.EMR {
    margin-left: 11px;
    width: 32.6%;
}

.xnh {
    width: 32.6%;
    margin-left: 11px;
}

.yz {
    width: 32.5%;
}

.EmRbltx {
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    background: #1abc9c;
    border-radius: 17px;
    width: 60px;
    height: 28px;
    line-height: 28px;
    margin-right: 11px;
}

.yellow {
    color: #f2a654;
}

.yellow-bg {
    background-color: #f2a654;
}

.feise-bg {
    background: #718dc7;
}

.feise-bg:hover {
    color: rgba(255, 255, 255, 0.6);
}

.yz-item {
    display: flex;
    align-items: center;
    align-content: center;
    justify-content: center;
    flex-wrap: wrap;
    height: 82%;
}

.yz-list {
    width: 30%;
    text-align: center;
    margin-bottom: 11px;
}

.xnh-list {
    width: 50%;
    text-align: center;
    margin-bottom: 11px;
}

.zxz-number {
    font-size: 30px;
    color: #1abc9c;

}

.zxz-text {
    font-size: 14px;
    color: #757c83;
}

.notEdit {
    border: none;
    background: transparent;
    text-align: center;
}

.notEdit:focus {
    background-color: transparent;
}

.hzgl-top {
    margin-top: -7px;
}

.tong-search .zui-form .hzgl-margin {
    padding: 0 0 8px 20px;
}

.tong-search .zui-form .hzgl-margin-pop {
    padding: 0 0 8px 20px;
}

.hzgl-margin .zui-input {
    background: #ffffff;
    border-radius: 4px;
    width: 120px;
}

.side-form {
    padding-top: 0;
}

.pop-805 {
    width: 805px !important;
}

.pop-content {
    width: 100%;
    float: right;
}

.pop-content .content-right-top,
.pop-content .content-right-list {
    width: 100%;

}

.pop-content .content-right-list {
    overflow: auto;
}

.pop-content .content-right-top i,
.pop-content .content-right-list i,
.pop-content .content-right-top span,
.pop-content .content-right-list span {
    width: 100px;
    text-align: center;
    display: inline-table;
}

.pop-content .content-right-top i em,
.pop-content .content-right-list i em,
.pop-content .content-right-top span em,
.pop-content .content-right-list span em {
    margin-top: 10px;
}

.pop-content li {
    cursor: pointer;
    width: 100%;
}

.pop-content li:hover {
    background: rgba(26, 188, 156, 0.08) !important;
    border: 1px solid #1abc9c;
}

.xmzb-content-right .content-right-top {
    width: auto;
    display: flex;
    justify-content: flex-start;
    border: 1px solid #e9eee6;
    background: #edf2f1;
    height: 40px;
    align-items: center;
    white-space: nowrap;
    /*overflow: auto;*/
}

.ksys-side {
    padding: 26px 15px;
}

.ksys-side .span0 {
    display: flex;
    width: 100%;
    justify-content: center;
    position: relative;
}

.ksys-side4 .span0 {
    display: inherit !important;
    justify-content: flex-start;
    margin-bottom: 15px;
}

.ksys-side .span0 i {
    padding-bottom: 5px;
}

.hzzx-money {
    position: absolute;
    right: 6px;
    width: 20px;
    background: #fff;
    color: #1abc9c;
    top: 32px;
    display: block;
}

.xmzb-content-right .content-right-list li {
    width: max-content;
    /*display: flex;*/
    justify-content: flex-start;
    align-items: center;
    height: 41px;
    border: 1px solid #e9eee6;
    border-top: none;
    cursor: pointer;
    line-height: 40px;
    overflow: hidden;
}

.hzgl-height {
    height: 81%;
    padding: 8px 10px;
    overflow: hidden;
}

.hzgl-wiwidth-one {
    width: 38.3%;
}

.hzgl-wiwidth-two {
    width: 60%;
}

.btn-parmary-not {
    border: none;
    color: #fff;
}

.loadPage .hzgl-flex {
    justify-content: flex-end;
    background: #fff !important;
    display: flex;
    align-items: center;
    border-top: 1px solid #eee;
    height: 66px;
}

h1 {
    font-size: 24px;
    color: #354052;
}

.hzgl-title {
    font-size: 14px;
    color: #7f8fa4;
}

.add-yp-hzgl {
    background: rgba(26, 188, 156, 0.07);
    border: 1px solid #1abc9c;
    border-radius: 4px;
    width: 28px;
    margin-top: -17px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    position: relative;
    float: left;
}

.add-yp-hzgl:before {
    position: absolute;
    content: '+';
    left: 17%;
    margin-top: -1px;
    color: #1abc9c;;
    font-size: 24px;

}

.hzgl-sc {
    position: relative;
}

.hzgl-shanchu {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.hzgl-shanchu:before {
    position: absolute;
    top: -5px;
    content: '';
    background: #dfe3e9;
    width: 20px;
    left: -5px;
    height: 20px;
    z-index: 1;
    border-radius: 100%;
}

.hzgl-shanchu:after {
    position: absolute;
    top: -5px;
    content: '';
    background: #ffffff;
    width: 10px;
    left: -5px;
    height: 3px;
    z-index: 1;
    text-align: center;
    margin: 8px 5px;
}

.zui-form-label {
    width: 70px;
    /*padding:8px 21px 8px 0;*/
}

.zui-form .hzgl-not-margin {
    padding-left: 70px;
    margin-right: 0;

}

.zui-form .margin-b-15 {
    margin-bottom: 15px;
}

.hzgl-btn {
    height: 68px;
    bottom: 0;
    z-index: 12;
    justify-content: space-between;
    line-height: 68px;
    background: #ffffff;
}

.hzgl-btn .left {
    margin-left: 17px;
}

.hzgl-tj {
    margin-right: 50px;
}

.hzgl-not-pad {
    padding: 12px 0 26px 0;
}

.hzgl-ck {
    height: 44px;
    line-height: 33px;
}

.hgzl-border {
    padding-bottom: 14px;
    border: 1px dashed rgba(26, 188, 156, 0.3)
}

.jcjy {
    width: 108px;
    height: 99%;
    font-size: 16px;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.jcjy-active {
    color: #1abc9c;
    background: #ffffff;
    position: relative;
}

.jcjy-active:after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 2px;
    width: 100%;
    background-color: #1abc9c;
}

.jcjy-top-side {
    padding: 0;
}

.twd {
    width: 600px;
    height: 600px;
    border: 1px solid #EEEEEE;
}

.paev {
    margin-right: 68px;
}

.next {
    margin-left: 68px;
}

.paev, .next {
    content: '';
    position: relative;
    width: 68px;
    height: 68px;
    cursor: pointer;
    border-radius: 100%;
    background: rgba(255, 255, 255, 0.10);
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.15);
}

.paev.fa-angle-left:before, .next.fa-angle-right:before {
    position: absolute;
    left: 50%;
    color: #E2E2E2;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 40px;
}

.twd-center {
    justify-content: center;
}

.zui-date .todate {
    padding-right: 10px;
}

.tong-search .zui-form .lclj-padd {
    padding-left: 30px;
}

.lclj-bg {
    background: #fdfdfd;
    border: 1px solid #e9eee6;
    height: 403px;
}

.maxContent {
    width: max-content;
    min-width: 100%;
}

.yzHeader {
    background: #edf2f1;
    border: 1px solid #e9eee6;
    display: flex;

    justify-content: flex-start;
    align-items: center;
}

body, html {
    /*background-color: #ffffff;*/
}

.cssz-list li {
    align-items: end;
}

.yzContent {
    height: 45px;
}

.yzHeader li {
    padding: 9px 15px 7px 16px;
    font-size: 14px;
    color: #333333;
    border: 1px solid #e9eee6;
    text-align: center;
}

.topData {
    width: 138px;
    padding: 9px 15px 7px 16px;
    font-size: 14px;
    color: #333333;
    text-align: center;
}

.yzDataList .leftData {
    background: #fdfdfd;
    border: 1px solid #e9eee6;
    width: 138px;
    font-size: 14px;
    text-align: center;
    border-right: none;
    border-top: none;
    color: #1abc9c;
    display: inline-block;
}

.yzDataList .title, .yzDataList input, .yzDataList .titleText {
    display: inline-block;
    background: #ffffff;
    border: 1px solid #e9eee6;
    height: 45px;
    text-align: center;
    padding: 11px 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    border-top: none;
    border-right: none;
}

.yzDataList .titleText:last-child {
    border-right: 1px solid #e9eee6;
}

.yzDataList .leftData {
    background: #fdfdfd;
    border: 1px solid #e9eee6;
    width: 138px;
    font-size: 14px;
    text-align: center;
    border-right: none;
    border-top: none;
    color: #1abc9c;
    display: inline-block;
}

.headerText {
    font-size: 22px;
    color: #3a3a3a;
    margin-bottom: 10px;
    text-align: center;
}

.headerList {
    font-size: 14px;
    color: #7f8fa4;
    margin-left: 9px;
    margin-bottom: 3px;
    margin-right: 10px;
    text-align: center;
    display: flex;
    justify-content: space-between;
}

.grid-box .right-bg {
    width: 74%;
    margin-left: 9px;
}

.brzilist {
    margin-top: 25px;
}

.value-pgsj {
    background: #ffffff;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    height: 170px;
    width: 100%;
    padding: 9px 12px;
}

.not-align-items {
    align-items: end;
}

.pgmb-text {
    font-size: 12px;
    color: #f2a654;
    text-align: right;
    line-height: 16px;
    cursor: pointer;
    position: relative;
    display: inline-block;
    float: right;
}

.pgmb-text:before {
    position: absolute;
    left: -14px;
    top: 2px;
    content: '';
    width: 12px;
    height: 14px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
    background-image: url("/newzui/pub/image/pgmb.png");
}

.lclj-pop-list {
    height: 96px;
}

.lclj-height {
    height: 60px;
    line-height: 60px;
}

.lclj-list {
    background: #fbfbfb;
}

.fyxm-size {
    height: 100%;
}

.lclj-po {
    position: absolute;
    bottom: 15px;
    right: 18px;
}

.value-lclj {
    color: #767d85;
    font-size: 14px;
    border-radius: 4px;
    padding: 0 10px;
    resize: none;
    overflow: hidden;
    height: 100%;
    width: 93%;
    display: inline-block;
}

.notCursor {
    cursor: default;
}

.lclj-baocun {
    background: #f2a654;
    border-radius: 4px;
    width: 48px;
    cursor: pointer;
    height: 100%;
    text-align: center;
    margin-left: 7px;
}

.un-text {
    width: 14px;
    display: inline-block;
    text-align: center;
    margin-top: 19.5px;
    color: #ffffff;
}

.blur-edit {
    padding: 7px 16px;
    border: 1px solid #dfe3e9;
    border-radius: 4px;
}

.icon-bj:before {
    top: 0;
    position: static;
}

.icon-bj {
    margin: 0 auto;
}

.brjz-dw {
    position: absolute;
    right: 24px;
    top: 3px;
    z-index: 1;
    /*background: #fff;*/
    width: auto;
    display: block;
    height: 30px;
    line-height: 30px;
}

.zui-form .width100 {
    width: 100% !important;
}

.brjz-fl {
    /*width: 70px;*/
    height: 36px;
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
}

.brjz-bottom {
    padding: 0 10px 0 10px;
}

.brjz-foot {
    position: fixed;
    right: 10px;
    left: 10px;
    background: #fff;
    height: 72px !important;
    z-index: 88;
    bottom: 10px;
    display: flex;
    justify-content: start;
    align-items: center;
    border-top: 1px solid #eee;
}

.brjz-foot span {
    padding-left: 20px;
    padding-right: 10px;

}

.rkgl-kd span {
    padding: 0 20px 0 10px;
    float: left;
    display: block;
}

.brjz-root {
    justify-content: flex-end;
}

/*鐢靛瓙澶勬柟*/
.dzcf-width {
    width: 100%;
    float: left;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 10px;
}

.icon-gth {
    width: 15px;
    height: 15px;
    border: 1px solid red;
    border-radius: 15px;
    display: block;
    position: relative;
    margin-right: 10px;
}

.icon-gth:before {
    content: '!';
    position: absolute;
    top: -3px;
    left: -1px;
    width: 15px;
    height: 10px;
    font-weight: 500;
    text-align: center;
    color: red;
}

.dzcf-add {
    width: 100%;
    float: left;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: #1abc9c;
}

.dzcf-add i {
    cursor: pointer;
}

.dzcf-right-top {
    width: 100%;
    float: left;
    background: #edf2f1;
    height: 34px;
    line-height: 34px;
    color: #333;
}

.dzcf-right-top i {
    width: calc((100% - 50px) / 2);
    display: block;
    text-align: center;
    float: left;
}

.dzcf-list li i {
    width: calc((100% - 50px) / 2);
    display: block;
    text-align: center;
    float: left;
}

.dzcf-right-top i:first-child, .dzcf-list i:first-child {
    width: 50px !important;
}

.cfright i, .cfright li i {
    width: calc((100% - 50px) / 4);
}

.dzcf-list {
    width: 100%;
    float: left;
    /*overflow: auto;*/
    /*max-height: 700px;*/
    border: 1px solid #eee;
}

.dzcf-height {
    overflow: auto;
    max-height: 700px;
}

.dzcf-list li {
    line-height: 40px;
    border-top: 1px solid #eee;
    height: 40px;
    width: 100%;

}

.dzcf-list li:hover {
    background: rgba(26, 188, 156, 0.08) !important;
}

.cf-right-list li i, .cf-right-list i {
    width: calc((100% - 50px) / 5);
}

.dzcf-list li:nth-child(2n) {
    background: #fdfdfd;
}

.brjz-tab {
    max-width: 584px;
    display: block;
    float: left;
    overflow: hidden;
    height: 36px;
}

.brjz-span {
    max-width: 584px;
    overflow-x: scroll;
    display: block;
}

.brjz-span div {
    float: left;
}

.tab-left, .tab-right {
    width: 24px;
    height: 36px;
    background: rgba(204, 207, 212, 0.10);
    border: 1px solid #e9eee6;
    display: block;
    line-height: 36px;
    float: left;
    color: #1abc9c;
    cursor: pointer;
    font-size: 12px;
}

.tab-right {
    border-right: none;
    border-left: none;
}

.brjz-add {
    background: #ffffff;
    border: 1px solid #eeeeee;
    width: 34px;
    height: 36px;
    /*position: absolute;*/
    left: 0px;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    float: left;
    font-size: 16px;
    color: #cccfd4;
    cursor: pointer;
    font-weight: bold;
}

.brjz-right-list {
    width: 400px;
    float: right;
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
}

.brjz-right-list li {
    width: 87px;
    text-align: center;
    display: flex;
    justify-content: start;
    align-items: center;
    float: left;
    position: relative;
    cursor: pointer;
}

.dzcf-erj {
    position: absolute;
    top: 18px;
    z-index: 9999;
    left: -7px;
    width: 100px;
    background: #fff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.19);
    border-radius: 4px;
    display: none;
}

.dzcf-erj a {
    display: block;
    line-height: 40px;
}

.dzcf-erj a:hover {
    background: rgba(26, 188, 156, 0.10);
    color: #1abc9c;
}

.brjz-right-list li.cfdyhover:hover .dzcf-erj {
    display: block;
}

.icon-top:before {
    /*top: -2px;*//
}

.padd-l-26 {
    padding-left: 26px !important;
}

.margin-l-8 {
    margin-left: 8px !important;
}

.cf-list-left {
    width: 300px;
    float: left;
}

.cf-list-right {
    width: 473px;
    float: right;
}

.cf-xz i, .cf-xz li i {
    width: calc((100% - 100px) / 5);
}

.cf-xz i:nth-child(2), .cf-xz li i:nth-child(2) {
    width: 50px !important;
}

.ls-top i, .ls-top li i {
    width: calc((100% - 50px) / 4);
}

.jcjy-list i, .jcjy-list li i {
    width: calc((100% - 50px) / 5);
}

.dzcf-cell {
    display: flex;
    justify-content: center;
    align-items: center;
}

.jcjy-position {
    position: fixed;
    right: 0;
    top: 84px;
    z-index: 99;
    width: 80px;
    height: 80px;
}

.jcjy-textarea {
    width: 100%;
    height: 90px;
    border: 1px solid #eee;
    margin-top: 40px;
    float: left;
}

.jcjy-tab {
    width: 100%;
    background: #1abc9c;
    color: #fff;
    height: 46px;
}

.jcjy-tab div {
    border: none !important;
    padding: 0 !important;
}

.jcjy-tab div span {
    display: block;
    width: 100%;
    padding: 0 40px;
    height: 46px;
    line-height: 46px;
    font-size: 16px;
}

.jcjy-tab div span.active {
    color: #1abc9c;
    background: #fff;
    border: none;
}

.closex {
    height: 46px;
    line-height: 46px;
    margin-right: 15px;
    font-size: 16px;
}

.jcjy-mb {
    position: absolute;
    left: 10px;
}

.dzcf-fy {
    width: 100%;
}

.dzcf-fy li {
    width: 46%;
    float: left;
    height: 46px;
}

.dzcf-fy li:nth-child(2n) {
    float: right;
}

.dzcf-rp {
    width: 100%;
    border: 1px dashed #1abc9c;
    padding: 15px;
    /*display: table;*/
    position: relative;

}

.dzcf-rps {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
}

.dzcf-rps img {
    width: 53px;
    height: 52px;
}

.dzcf-span {
    width: 36px;
    height: 36px;
    background: rgba(26, 188, 156, 0.07);
    border: 1px solid #1abc9c;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #1abc9c;
}

.dzcf-tjyp {
    width: 100%;
    float: left;
    margin-top: 15px;
}

.tjyp-yyfs {
    background: #fdfdfd;
    border: 1px solid #e9eee6;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.11);
    width: 248px;
    padding: 10px;
    height: 170px;
    float: left;
    margin-left: 15px;
    margin-bottom: 10px;
    position: relative;
}

.tj-bj {
    width: 225px;
    line-height: 36px;
    float: left;
}

.tj-bj span {
    width: 100%;
    height: 36px;
    border-radius: 4px;
    display: block;
}

.wh205 {
    width: 225px;
}

.tjyp-del {
    background: #dfe3e9;
    width: 20px;
    height: 20px;
    border-radius: 100%;
    display: block;
    position: absolute;
    z-index: 999;
    right: -3px;
    top: -9px;
    cursor: pointer;
}

.tjyp-del:before {
    content: '-';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    border-radius: 100%;
    color: #fff;
    font-size: 28px;
    line-height: 15px;
    font-weight: bold;
    text-align: center;
}

/**/
.fyxm-border div:first-child {
    border-left: none;
}

.fyxm-border div {
    border: none !important;
    padding: 0;
}

.scrollpic {
    /*width:600px;*/
    min-height: 36px;
    position: relative;
    /*display: block;*/
    /*float: left*/
}

#myscroll {
    display: block;
    max-width:600px;
    position: relative;
    height: 36px;
    overflow: hidden;
}

#myscroll #myscrollbox {
    /*display: block;*/
    /*float: left;*/
    /*position:absolute;*/
    /*left: 0;*/
    /*top: 0;*/
    /*width: 100%;*/
}

#myscroll ul {
    display: flex;
    list-style-type: none;
    padding: 0;
    overflow-y: auto;
    width: 100%;
    margin: 0
}
    #myscroll ul::-webkit-scrollbar{
        height: 0;
    }
#myscroll ul li {
    border: 1px solid #eee;
    border-left: none !important;
    height: 36px;
    /*min-width: 100px;*/
    text-align: center;
}

#myscroll ul li a {
    text-align: center;
    position: relative;
    height: 34px;
    white-space: nowrap;
    cursor: pointer;
}

#myscroll ul li:first-child {
    border-left: 1px solid #eee !important;
}

#myscroll ul .active a {
    border-bottom: 2px solid #1abc9c;
    padding-bottom: 4px;
}

#myscroll ul.actives a {
    color: #fff;
    background: #1abc9c;
    display: block;
}

#mybtns a:hover, #myscroll, #mybtns a, #myscroll a:hover .intro, #myscroll a .intro, #myscroll #myscrollbox {
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

/*#left{left: 0;position: absolute;border-right: 1px solid #eee;z-index: 99;background: #f9fafc}*/
/*#right{right:0px;position: absolute;border-left: 1px solid #eee;z-index: 99;background: #f9fafc}*/

.dzcf-js {
    width: 100%;
    float: left;
    padding-bottom: 80px;
}

.dzcf-fl {
    float: left;
}

.dzcf-fr {
    float: right;
}

.addZdCf {
    padding-right: 15px;
    color: rgba(255, 255, 255, 0.56);
}

.xzcfZd {
    width: 100%;
    float: left;
}

.xzcf-list, .detail-list, .detail-list li {
    width: 100%;
    float: left;

}

.xzcf-header {
    height: 36px;
    background: #edf2f1;
    line-height: 36px;
}

.detail-list {
    line-height: 40px;
    border: 1px solid #eee;
}

.xzcf-list i, .detail-list li i {
    width: calc((100% - 50px) / 7);
    display: block;
    float: left;
    text-align: center;
}

.xzcf-list i:first-child, .detail-list i:first-child {
    width: 50px !important;
}

.detail-list li {
    border-top: 1px solid #eee;
}

/*闄勫姞璐圭敤缁勪欢鏍峰紡閲嶆瀯*/
.extraC {
    display: flex;
    align-items: center;
    background: #fdfdfd !important;
    border: 1px solid #e9eee6;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.11);
    /*width: 533px;*/
    height: 44px;
    border-radius: 4px;
    font-size: 14px;
    /*line-height: 44px;*/
    float: left;
    margin-right: 20px;
    padding: 0;
    margin-bottom: 10px;
}

.extraC span:first-child {
    font-size: 14px;
    color: #354052;
    width: 40%;
    display: block;
    float: left;
    padding-left: 10px;
}

.extraCharges input {
    border-radius: 4px;
    width: 56px;
    height: 30px;
    line-height: 30px !important;
    border: 1px solid #eee;
    margin-top: -5px;
}

.extraC span:nth-child(2) {
    font-size: 14px;
    color: #f2a654;
}

.extraC span:nth-child(3) {
    font-size: 14px;
    color: #f2a654;
}

.extraC span:nth-child(4) {
    font-size: 14px;
    color: #f2a654;
}

.extraC span:nth-child(5) {
    background: rgba(26, 188, 156, 0.06);
    border: 1px solid #e9eee6;
    width: 42px;
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    float: right;
}

.extraC span.fa-trash-o:before {
    content: "\f014";
    font-size: 24px;
    color: #757c83;
}

.icon-dysqs:before {
    content: "\e925";
    color: #757c83;
    font-size: 16px;
}

.zcyItem .zcy-width {
    width: 23%;
}

.bgNone input {
    border: none;
    text-align: center;
    background-color: transparent !important;
}

.grid-box .hzgl-width {
    width: 20%;
}

.yzcz {
    display: inline-block;
    border: 0;
    width: auto;
    padding: 0;
}

.yzcz > button {
    background-color: #029377;
}

.yzcz:hover > ul {
    display: block;
}

.yzcz ul {
    display: none;
    position: absolute;
    width: 90px;
    margin: -1px 0 0 4px;
    background-color: #fff;
    border: 1px solid #029377;
    z-index: 100;
}

.yzcz ul > li {
    padding: 6px 4px;
}

.yzcz ul > li:hover {
    background-color: #1AB394;
    color: #FFFFFF;
    cursor: pointer;
}

.icon-xz-h:before {
    content: "\e933";
    color: #767d85 !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.icon-tops:before {
    top: 8px;
    font-size: 13px;
}

.tem {
    position: relative;
    float: left;
    width: 800px;
    height: 500px;
    border: 1px solid green;
    margin-left: 20px;
    margin-top: 20px;
}

.tem .item {
    position: absolute;
    display: inline-block;
    top: 10px;
    margin-bottom: 20px;
    font-size: 14px;
    cursor: default;
    z-index: 100;
}

.printShow {
    position: relative;
    width: 100%;
    height: 100%;
}

.pop-width460 {
    width: 460px;
}

.pop-width520 {
    width: 520px;
}

.pop-width800 {
    width: 800px;
}

/*闈炲鏂�*/
.fypcf-list {
    width: 100%;
    float: left;
}

.fypcf-gou {
    width: 100%;
    display: block;
    float: left;
}

.fypcf-check {
    width: 100%;
    float: left;
    overflow: auto;
    min-height: 400px;

}

.fypcf-check ul {
    width: 48%;
    float: left;
}

.fypcf-check ul li {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wh40 {
    width: 40px !important;
}

.fypcf-check ul.fr {
    float: right;
}

.height36 {
    height: 36px !important;
}

.height36 > input[type='text'] {
    height: 36px !important;
}

.zui-textarea {
    height: 70px !important;
}

.xmzb-db {
    width: auto !important;
    min-width: 75px !important;
}

.zui-brjz {
    width: 100%;
    float: left;
    box-sizing: border-box;
}

.zui-form-labels {
    width: 65px !important;
    font-size: 14px;
    color: #000000;
    text-align: right;
    padding-right: 5px;
}

.zui-inlines {
    width: 25%;
    float: left;
    display: flex;
    justify-content: start;
    align-items: center;
    position: relative;
    padding-left: 3%;
    margin-top: 10px;
    height: 36px;
    box-sizing: border-box;
}

.zui-inlines-b {
    width: 360px;
    float: left;
    display: flex;
    justify-content: start;
    align-items: center;
    position: relative;
    margin-left: 82px;
    margin-top: 10px;
    height: 36px;
}

.zui-inlines-ba {

}

.zui-input-inlines {
    width: 182px;
    position: relative;
    float: left;
}

.justify-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.letter-spacing {
    letter-spacing: 4px;
}

.zui-inlines:first-child {
    /*margin-left:40px;*/
}

.zui-inlines:nth-child(5n+1) {
    /*margin-left:40px;*/
}

.color-7f {
    color: #7f8fa4;
}

.margin-l-9 {
    margin-left: 9px;
}

.zui-margin-26 {
    margin-left: 66px !important;
}

.zui-margin-35 {
    margin-left: 39px !important;
}

.zui-margin-17 {
    margin-left: 61px !important;
}

.zui-mr13 {
    margin-left: 73px !important;
}

.zui-mr13s {
    margin-left: 34px !important;
}

.zui-mr9 {
    margin-left: 34px !important;
}

.zui-inlines-z {
    width: 50% !important;
    float: left;
    display: flex;
    justify-content: start;
    align-items: center;
    position: relative;
    margin-top: 10px;
    padding-left: 3%;
    box-sizing: border-box;
    height: 36px;
}

.zui-inlines-w {
    width: 100%;
    float: left;
    display: flex;
    justify-content: start;
    align-items: center;
    position: relative;
    margin-top: 10px;
    padding-left: 3%;
    box-sizing: border-box;
    height: 36px;
}

.zui-input-inlines-w {
    width: 89%;
}

.wh990 {
    width: 100% !important;
}

.zui-input-inlines-z {
    width: 77% !important;
    position: relative;
}

@media screen and (max-width: 1366px) {
    .zui-inlines:nth-child(5n+1) {
        /*margin-left:26px;*/
    }

    .zui-inlines:nth-child(4n+1) {
        /*margin-left:13px;*/
    }

    .zui-brjz {
        width: 100%;
        float: left;
        box-sizing: border-box;
    }

    .zui-form-labels {
        width: 75px !important;
        font-size: 14px;
        color: #000000;
        text-align: right;
        padding-right: 5px;
    }

    .zui-inlines {
        width: 30%;
        padding: 0 0 0 3%;
        float: left;
        display: flex;
        justify-content: start;
        align-items: center;
        position: relative;
        /*margin-left:26px;*/
        margin-top: 10px;
        height: 36px;
    }

    .zui-inlines-b {
        width: 40%;
        float: left;
        display: flex;
        justify-content: start;
        align-items: center;
        position: relative;
        margin-left: 43px;
        margin-top: 10px;
        height: 36px;
    }

    .zui-inlines-ba {

    }

    .zui-input-inlines {
        width: 182px;
        float: left;
    }

    .justify-center {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .letter-spacing {
        letter-spacing: 4px;
    }

    .zui-inlines:first-child {
        /*margin-left:13px;*/
    }

    .color-7f {
        color: #7f8fa4;
    }

    .zui-margin-26 {
        /*margin-left: 26px !important;*/
    }

    .zui-margin-35 {
        /*margin-left: 35px !important;/*/
    }

    .zui-margin-17 {
        /*margin-left: 17px !important;*/
    }

    .zui-mr9 {
        /*margin-left: 9px !important;*/
    }

    .zui-inlines-z {
        /*width: 515px !important;*/
        float: left;
        display: flex;
        justify-content: start;
        align-items: center;
        position: relative;
        margin-top: 10px;
        /*margin-left:10px;*/
        height: 36px;
    }

    .zui-mr13 {
        margin-left: 13px !important;
    }

    .zui-mr13s {
        margin-left: 13px !important;
    }

    .zui-input-inlines-z {
        width: 50%;
    }

    .zui-inlines-w {
        width: 100%;
        float: left;
        display: flex;
        justify-content: start;
        align-items: center;
        position: relative;
        margin-top: 10px;
        /*margin-left: 13px;*/
        height: 36px;
    }


    .zui-input-inlines-w {
        width: 90%;
    }

    .zui-input-inlines-z {
        width: 50%;
        position: relative;
    }

}

.fyxm-side-top {
    padding: 0 0 0 20px;
}

.fyxm-size .zui-input {
    height: 28px !important;
}

a {
    transition: none;
    -webkit-transition: none;
}

.wh90 {
    width: 90px ;
}
.wh88 {
    width: 88px;
}
.tong-search {
    padding: 10px 0 5px 20px;
}

.wh150 {
    width: 150px ;
}

.wh200 {
    width: 200px ;
}
.ksys-side{
    overflow-y: hidden;
}
.icon-iocn26:before {
    content: "\e919";
    color: #5f5f5f;
}
.icon-iocn9:before {
    content: "\e908";
    color: #5e5e5f;
}
.icon-iocn15:before {
    content: "\e90e";
    color: #5f5f5f;
}
.wh38{
    width: 46%;
}
.wh22{
    width: 37%;
}
.wh7{
    width:24%;
}
.wh23{
    width: 23%;
}
.maxHeight{
    overflow: auto;
    max-height: 100px;
}
.bzsmHeight{
    height: 100px;
}
.zui-input.disabled{
    background-color: #f8f8f8;
    color: #000000;
}
.bqcydj_model_width{
    width: 710px;
    padding: 14px 18px 14px 18px;
    background: rgba(245, 246, 250, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
body .ksys-side-hide{
    overflow: hidden;
}
#model{
    z-index: 1000;
}
.height100{
    height: calc(100% - 46px);
}
.kssChildModel{
    width: 840px !important;
    overflow: auto;
    height: 311px !important;
}

