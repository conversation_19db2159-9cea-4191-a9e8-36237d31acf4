.tab-edit-list > li > label > .label-input {
  text-indent: 10px !important;
}
.ksys-side > .tab-edit-list li > ul.inner > li {
  margin-bottom: 0 !important;
  width: 100%;
}
.zui-select-inline {
  margin-right: 0 !important;
}
.zui-form .zui-form-label {
  left: 5px !important;
}
.icon-width:before {
  width: 24px;
  height: 24px;
  position: absolute;
  left: 0px;
  top: -13px;
}
.ksys-side i.red {
  color: #ff4532;
}
.font-bolder{
  font-weight: bold;
}
.bqcydj_model {
    padding: 0.08648648648648649rem;
    
    overflow: auto;
    background: rgba(255, 255, 255, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}