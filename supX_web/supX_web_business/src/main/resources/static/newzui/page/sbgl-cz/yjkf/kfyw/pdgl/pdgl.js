    //功能选择按钮栏
    var InfoMenu = new Vue({
        el: '.InfoMenu',
        data: {
            domArr:['pdsc','whpdb','pdlr','lrsh','pdwc'],
            which: 0
        },
        methods: {
            loadCon: function () {
                var page=this.domArr[this.which]
                if ( page== 'pdsc') {
                    document.getElementById('_kfbm').value = '';
                }
                var pageDiv = $("#" + page);
                $(".page_div").hide();
                if (pageDiv.length == 0) {
                    $("." + page).load(page + ".html").fadeIn(300);
                } else {
                    $("." + page).load(page + ".html").show();
                }
            },
            tabBg:function (index) {
                this.which=index;
                this.loadCon()
            }
        },
    });
    InfoMenu.loadCon('pdsc');
