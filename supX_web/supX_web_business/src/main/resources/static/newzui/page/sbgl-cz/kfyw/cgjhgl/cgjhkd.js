var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        WzcgjhPrint:'',
        cgjhhc:{},
        popContent: {},
        queryData:{},
        kfList: [],
        ryList: [],
        mxList: [],
        bzms: "",
        selectIndex:null,
    },
    updated: function () {
        changeWin()
    },
    mounted:function(){
        this.initial();
        this.getCgry();
        this.getGys();
    },
    methods: {
        //打印
        dy:function(){
            var jhdh =wrapper.popContent.jhdh;
            // var yljgbm =jgbm;
            var kfbm = wrapper.popContent.kfbm;
            var reportlets = "";
            if (window.top.J_tabLeft.obj.frprintver == "3") {
                reportlets = "[{reportlet: 'wzdy%2Frcyw%2Fcgjhmx.cpt',yljgbm:'"+jgbm+"',jhdh:'"+jhdh+"',kfbm'"+kfbm+"'}]";
            } else {
                reportlets = "[{reportlet: 'wzdy/rcyw/cgjhmx.cpt',yljgbm:'"+jgbm+"',jhdh:'"+jhdh+"',kfbm'"+kfbm+"'}]";
            }
            //帆软打印
            if (!FrPrint(reportlets, wrapper.WzcgjhPrint)) {
            }
        },
        //审核
        sh: function () {
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywCgjh&types=passDj', JSON.stringify(wrapper.popContent))
                .then(function (data) {
                    if (data.body.a == "0") {
                        malert("审核成功！", 'top', 'success');
                        wrapper.cancel()
                        // malert("审核成功！")
                    } else {
                        malert("审核失败", 'top', 'defeadted');
                    }
                });
        },
        // 提交所有
        submitAll: function () {
            malert('提交所有', 'top', 'success')
            var rkd = {}
            console.log($("#cgbz").val());
            Vue.set(rkd, 'sbkf', this.popContent.kfbm);//库房编码
            Vue.set(rkd, 'cgry', this.popContent.rybm);//采购员
            Vue.set(rkd, 'cgbz', $("#cgbz").val());//备注描述
            var ryxms = this.listGetName(this.ryList, this.popContent.rybm, 'rybm', 'ryxm');

            //新增操作
            var obj = {
                list: {
                    dj: rkd,
                    djmx: this.mxList,
                    ryxms: ryxms
                }
            }
            if(JSON.stringify(wrapper.cgjhhc)=='{}'){
                wrapper.cgjhhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
            }else {
                if (JSON.stringify(wrapper.cgjhhc)==JSON.stringify(obj)){
                    malert("采购计划单已经保存请不要重复提交!", 'top', 'defeadted');
                    return false;
                }else {
                    wrapper.cgjhhc=JSON.parse(JSON.stringify(obj));//用于缓存提交的对象，二次提交时防止重复提交
                }
            }
            //保存
            this.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywCgjh&types=save', JSON.stringify(obj))
                .then(function (data) {
                    if (data.body.a == "0") {
                        wrapper.cancel()
                        malert("保存成功！", 'top', 'success');
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                });
        },
        // 取消2018/07/09取消回退上一级页面
        cancel: function () {
            // malert('取消','top','defeadted')
            this.topClosePage('page/sbgl/kfyw/cgjhgl/cgjhkd.html', 'page/sbgl/kfyw/cgjhgl/cgjhgl.html');
            window.top.$("#采购计划管理")[0].contentWindow.getData();
        },
        // 编辑
        edit: function (index) {
            pop.title = '编辑设备';
            this.selectIndex=index;
            pop.popContent = this.mxList[index];
            pop.open();

        },
        // 删除2018/07/09二次弹窗删除提示
        remove: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                wrapper.jsonList.splice(index, 1);
            })) {
                return false;
            }
            // malert('取消','top','defeadted')
            // kd.jsonList.splice(index, 1);
        },
        // 新增
        AddMdel: function () {
            pop.title = '添加设备'
            pop.open();
            pop.popContent = {};
            pop.popContent = {
                'kfdwmc': this.popContent.kfbm,
            };
        },
        //初始化
        initial: function () {
            this.queryData= JSON.parse(sessionStorage.getItem('obj'));
            this.kfList = this.queryData.kfList;
            this.popContent.kfbm = this.queryData.kfbm;
            if (this.queryData.sh || this.queryData.dy) {
                //审核
                this.popContent = Object.assign(this.popContent,this.queryData.cgjh);
                this.getMx();
            }
        },
        getMx: function () {
            var obj = {
                jhdh: this.queryData.cgjh.jhdh,
                wzkf: this.queryData.kfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywCgjh&types=queryMx&parm=' + JSON.stringify(obj),
                function (data) {
                    if (data.a == 0 &&  data.d.length !=0) {
                        wrapper.mxList = data.d
                        // for (var  i = wrapper.mxList.length - 1; i >=0; i--) {
                        //     if(wrapper.mxList[i].kcsl <=0){
                        //         wrapper.mxList.splice(i,1)
                        //     }
                        // }
                    } else {
                        malert("获取明细失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载采购人员
        getCgry: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
                function (data) {
                    if (data.a == 0) {
                        wrapper.ryList = data.d.list;
                        wrapper.popContent.rybm = data.d.list[0].rybm;
                    } else {
                        malert("获取采购人员失败！", 'top', 'defeadted');
                    }
                });
        },
        //加载供应商人员
        getGys: function () {
            var parm = {
                tybz: '0'
            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1SbglXtwhGys&types=query&json=" + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0) {
                        pop.gysList = data.d.list;
                        pop.popContent.gysbm = data.d.list[0].gysbm;
                    } else {
                        malert("获取供应商人员失败！", 'top', 'defeadted');
                    }
                });
        }
    }

});

var pop = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        popContent: {},
        title: '',
        num: 0,
        dg: {
            page: 1,
            rows: 5,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '设备名称': 'sbmc',
            '设备规格': 'sbgg',
            '分装比例': 'fzbl',
            '进价': 'jj',
            '单价': 'dj',
            '产地': 'cd',
        },
        searchCon: [],
        selSearch: -1,
        sbkfList: wrapper.kfList,
        gysList: [],
        total: 0,
    },
    methods: {
        // 关闭
        closes: function () {
            this.num = 0;
        },
        open: function () {
            this.num = 1;
            // 设置库房
        },
        //保存
        save: function () {
            if (this.title == '添加设备') {
                // if(wrapper.mxList.length>0){
                    if(this.popContent.cgsl ==undefined
                        || (parseInt(this.popContent.cgsl)<=0)
                    ){
                        malert("请正确填写采购数量！", 'top', 'defeadted');
                        return;
                    }
                    if(this.popContent.ccjj ==undefined
                        || (parseInt(this.popContent.ccjj)<=0)
                    ){
                        malert("请正确填写采购金额！", 'top', 'defeadted');
                        return;
                    }
                    wrapper.mxList.push(this.popContent);

                // }
                this.popContent = {};
                return;
            } else if (this.title == '编辑设备') {
                Vue.set(wrapper.mxList,wrapper.selectIndex,this.popContent)
                //编辑保存成功关闭弹窗
                this.popContent = {};
                this.closes();
                return;
            }
        },
        //设备名称下拉table检索数据
        changeDown: function (event, type) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var isReq = this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                $("#rksl").focus();
            }
        },
        //当输入值后才触发
        change: function (event, type, val) {
            this.popContent[type] = val;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);

            var parm = {
                kfbm: wrapper.popContent.kfbm,
                page: pop.dg.page,
                rows: pop.dg.rows,
                parm: val
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglXtwhSbzd&types=query' +
                '&json=' + JSON.stringify(parm),
                function (data) {
                    pop.searchCon = data.d.list;
                    //wap.total = data.d.total;
                    pop.selSearch = 0;
                    $(".selectGroup").show();
                });
            if (wrapper.popContent["kfbm"] == undefined || wrapper.popContent["kfbm"] == null || wrapper.popContent["kfbm"] == "") {
                malert("库房不能为空", 'top', 'defeadted');
                return;
            }
            if (wrapper.popContent["rybm"] == undefined || wrapper.popContent["rybm"] == null || wrapper.popContent["rybm"] == "") {
                malert("采购人员不能为空", 'top', 'defeadted');
                return;
            }

        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                pop.dg.page++;
                var parm = {
                    page: pop.dg.page,
                    rows: pop.dg.rows,
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&json=' + JSON.stringify(parm),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                pop.searchCon.push(data.d.list[i]);
                            }
                            pop.total = data.d.total;
                            pop.selSearch = 0;
                        } else {
                            malert('分页信息获取失败', 'top', 'defeadted')
                        }

                    });
                return;
            }
            this.popContent = item;
            $(".selectGroup").hide();
        },
    }
});
