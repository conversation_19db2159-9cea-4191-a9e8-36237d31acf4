var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc],
    data: {
        jsonList: [],
        yfkfList: [],
        yfkf: 0, //药房库房信息
        param: {
            'page': 1,
            'yfbm': '',
            'beginrq': null,
            'endrq': null,
            'parm': '',
        },
    },
    created: function () {
        this.getYf();
    },
    methods: {
        getYf: function () {
            var that=this;
            this.updatedAjax('/actionDispatcher.do?reqUrl=GetDropDown&types=yf', function (data) {
                if (data.a == 0 && data.d) {
                    that.yfkfList = data.d.list;
                    that.param.yfbm=that.yfkfList[0].yfbm
                    that.param.yfmc=that.yfkfList[0].yfmc;
                    that.$forceUpdate();
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
            //获取列表
            this.getData();
        },
        //组件选择下拉框之后的回调
        resultRydjChange: function (val) {
            Vue.set(this.param, 'yfbm', val[0]);
            Vue.set(this.param, 'yfmc', val[4]);
            this.$forceUpdate();
            this.getData();
        },
        getData: function () {
            this.param.yfbm = this.param.yfbm == 0 ? null : this.param.yfbm
            $.getJSON("/actionDispatcher.do?reqUrl=YfbCxtjAll&types=ybb&parm=" + JSON.stringify(this.param), function (json) {
                if (json.a == "0") {
                    wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                }
            });
        }
    }
});

laydate.render({
    elem: '.todate'
    , trigger: 'click'
    , theme: '#1ab394',
    range: true
    , done: function (value, data) {
        wrapper.param.beginrq = value.slice(0, 10);
        wrapper.param.endrq = value.slice(13, 23);
        wrapper.getData();
    }
});




