.yzd_select {
    border-bottom: 2px solid #1AB394;
}

.yzdTitle {
    font-size: 22px;
    text-align: center;
}
.cqyznr{
	width:280px;
}
.lsyznr{
	width:280px;
}
.yzd-brInfo, .yzd-ysInfo {
    display: flex;
    justify-content: center;
    /*color:#7f8fa4;*/
    /*width: 905px;*/
}
.yzd-ysInfo{
    /*margin: 10px 0;*/
}
.fygg{
	height: 39px;
	max-height: 39px;
	width:80px;
	max-width: 80px;
	    font-size: 12px;
	    text-align: left;
	    margin-left: 2px;
	    word-break: break-all;
	    white-space: normal;
	word-wrap : break-word ;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	display: block;
}
.yzd-brInfo > div, .yzd-ysInfo > div {
    font-size: 14px;
    white-space: nowrap;
    margin-right: 20px;
}

.tablePage span{
    display: inherit;
}

.yzd-table table {
    border-collapse: collapse;
    margin: 0 auto;
    font-size: 14px;
}

.yzd-table td, .yzd-table th {
    border-left: 1px solid #999;
    font-weight: 500;
    /* height: 39px;
	max-height: 39px; */
    /*width: 30px;*/
    white-space: nowrap;
    text-align: center;
}
.yzd-table tr{
    font-size: 12px;
    border: 1px solid #333;
}
.yzd-table td .yzd-name {
    width: 220px;
        font-size: 12px;
        text-align: left;
        margin-left: 2px;
        word-break: break-all;
        white-space: normal;
    word-wrap : break-word ;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
	display: block;
}

.cqyzd, .lsyzd {
    text-align: center;
    overflow: scroll;
    height: 100%;
    font-family: '宋体';
    /*height: calc(100% - 36px);*/
}
.cqPrint,.lsPrint{
    font-family: '宋体';
}
.ysDiv {

    width: 100%;
    text-align: center;
    /*position: absolute;*/
    bottom: 0;
}
.toolMenu_yzd div {
    text-align: center;
    /*padding: 6px 16px;*/
    cursor: pointer;
}

.same {
    border-right: 1px solid #000000;
}
.yzd-sm {
    float: right !important;
}

.sameStart{
    position: absolute;
    border-top: 1px solid #000000;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 50%;
    right: 40%;
    bottom: 0;
}
.sameEnd{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 1px solid #000000;
    width: 10px !important;
    height: 50%;
    right: 40%;
}
.same{
    position: absolute;
    border-top: 0;
    border-right: 1px solid #000000;
    border-left: 0;
    border-bottom: 0;
    width: 10px !important;
    height: 100%;
    right: 40%;
}

.yzd-way{
    text-align: left;
    display: block;
    font-size: 13px
}
.yzd {
    height: 538px;
    overflow: auto;
}
.yzd-table td, .yzd-table th{
    position: relative;
}
.goPrintHide{
    visibility: hidden;
}
@media print {
    .goPrintHide{
        visibility: hidden;
    }
}
.toolMenu_yzd {
    min-height: 32px;
}
.wh30{
    width:30px
}
.wh50Left{
    width: 50%;
    text-align: left;
}
.yz-tables td {
		border: 1px solid #000 ;
		font-weight: 500;
		height: 37px;
		max-height: 37px;
		white-space: nowrap;
		text-align: center;
		font-size: 12px;
		color: #000;
		font-family: '宋体';
	}
	.lsyz-tables td {
		border: 1px solid #000 ;
		font-weight: 500;
		height: 37px;
		max-height: 37px;
		white-space: nowrap;
		text-align: center;
		font-size: 12px;
		color: #000;
		font-family: '宋体';
		min-width: 30px;
	}
	.lsyz-tables th {
		border: 0.1px solid #000;
		font-weight: 500;
		height: 37px;
		max-height: 37px;
		white-space: nowrap;
		text-align: center;
		font-size: 12px;
		color: #000;
		font-family: '宋体';
	}
	.yz-tables th {
		border: 1px solid #000;
		font-weight: 500;
		height: 37px;
		max-height: 37px;
		white-space: nowrap;
		text-align: center;
		font-size: 12px;
		color: #000;
		font-family: '宋体';
	}