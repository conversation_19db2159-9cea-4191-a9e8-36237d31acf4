<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>设备字典</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="sbzd.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="wrapper">
        <div class="panel" id="panel" v-cloak>
            <div class="flex-container padd-t-10  padd-l-10 flex-align-c">
                <div class="flex-container padd-r-10 flex-align-c">
                    <span class="ft-14 padd-r-5 whiteSpace">检索</span>
                    <input class="zui-input wh180" placeholder="请输入关键字" @keyup.enter="goToPage(1)"
                           v-model="param.parm"/>
                </div>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
                <button v-show="showUpload" class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">上传营业执照</button>
                <button v-show="showUpload" class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">上传医疗器械许可证</button>
                <button v-show="showUpload" class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">上传委托书</button>
                <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="del">删除</button>
            </div>
        </div>
        <div class="zui-table-view  " v-cloak>
            <tabs :num="num" :tab-child="[{text:'设备大类'},{text:'设备类别'},{text:'设备单位'},{text:'设备字典'},{text:'供应商单位'}]" @tab-active="tabBg"></tabs>
            <!--设备大类-->
            <div class="fyxm-size" key="0" v-if="num==0">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                <span>
                                <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                </input-checkbox>
                                </span>
                                </div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>大类编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>大类名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>停用标志</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>

                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">

                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @dblclick="edit($index)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <!-- 有复选框的时候传 some  没有复选框的时候传one  -->

                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>

                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.dlbm">大类编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.dlmc">大类名称大类名称大类名称大类名称大类名称
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pydm">拼音代码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <div class=" switch"><input type="checkbox" true-value="0" false-value="1"
                                                                v-model="item.tybz" disabled/>
                                        <label></label>
                                    </div>
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                       @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                            </span>
                                </div>
                            </td>
                            <!--<p v-if="bsdList.length==0" class=" noData  text-center zan-border">暂无数据...</p>-->
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--end-->
            <!--设备类别-->
            <div class="fyxm-size" key="1" v-if="num==1">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                <span>
                                <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </span>
                                </div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>类别编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>类别名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>大类编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>大类名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>停用标志</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>

                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @dblclick="edit($index)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <!-- 有复选框的时候传 some  没有复选框的时候传one  -->

                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>

                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.lbbm">类别编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.lbmc">类别名称类别名称类别名称类别名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.dlbm">大类编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.dlmc">大类名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pydm">拼音代码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <div class=" switch">
                                        <input type="checkbox" true-value="0" false-value="1" v-model="item.tybz"
                                               disabled/>
                                        <label></label>
                                    </div>

                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                       @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                            </span>
                                </div>
                            </td>
                            <!--<p v-if="bsdList.length==0" class=" noData  text-center zan-border">暂无数据...</p>-->
                        </tr>
                        </tbody>
                    </table>


                </div>
            </div>
            <!--end-->
            <!--设备单位-->
            <div class="fyxm-size" key="2" v-if="num==2">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                <span>
                                <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </span>
                                </div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>单位编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>单位名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>停用标志</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>

                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">

                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @dblclick="edit($index)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <!-- 有复选框的时候传 some  没有复选框的时候传one  -->

                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>

                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.dwbm">单位编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.dwmc">类别名称类别名称类别名称类别名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pydm">拼音代码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <div class=" switch">
                                        <input type="checkbox" true-value="0" false-value="1" v-model="item.tybz"
                                               disabled/>
                                        <label></label>
                                    </div>
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                       @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                               </span>
                                </div>
                            </td>
                            <!--<p v-if="bsdList.length==0" class=" noData  text-center zan-border">暂无数据...</p>-->
                        </tr>
                        </tbody>
                    </table>


                </div>
            </div>
            <!--end-->
            <!--设备字典-->
            <div class="fyxm-size" key="3" v-if="num==3">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                <span>
                                <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </span>
                                </div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>设备编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>设备名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>产地</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>设备规格</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>设备单位</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>领用单位</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>备注描述</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>设备类别</span></div>
                            </th>
                            <!--<th>-->
                                <!--<div class="zui-table-cell cell-s"><span>进价</span></div>-->
                            <!--</th>-->
                            <!--<th>-->
                                <!--<div class="zui-table-cell cell-s"><span>单价</span></div>-->
                            <!--</th>-->
                            <th>
                                <div class="zui-table-cell cell-s"><span>停用标导</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>

                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @dblclick="edit($index)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <!-- 有复选框的时候传 some  没有复选框的时候传one  -->

                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.sbbm">设备编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.sbmc">设备名称设备名称设备名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pydm">拼音代码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.cd">产地</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.sbgg">设备规格</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.kfdwmc">设备单位</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.lydwmc">领用单位</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.bzms">备注描述</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.sblbmc">设备类别</div>
                            </td>
                            <!--<td>-->
                                <!--<div class="zui-table-cell cell-s" v-text="item.jj">进价</div>-->
                            <!--</td>-->
                            <!--<td>-->
                                <!--<div class="zui-table-cell cell-s" v-text="item.dj">单价</div>-->
                            <!--</td>-->
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <div class=" switch">
                                        <input type="checkbox" true-value="0" false-value="1" v-model="item.tybz"
                                               disabled/>
                                        <label></label>
                                    </div>
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                       @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                            </span>
                                </div>
                            </td>
                            <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--end-->
            <!--设备供应商-->
            <div class="fyxm-size" key="4" v-if="num==4">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                <span>
                                <input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'" :val="isCheckAll">
                                    </input-checkbox>
                                </span>
                                </div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>供应商编码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>供应商名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>法人</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>公司地址</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>公司电话</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>联系人(紧急)</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>联系人电话(紧急)</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>联系人</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>联系人电话</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>邮箱</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>开户行地址</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>银行卡号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>营业执照</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>经营许可证</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>授权书</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>备注描述</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>停用标导</span></div>
                            </th>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>

                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList"
                            :tabindex="$index"
                            @dblclick="edit($index)"
                            ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','jsonList'],$event)">
                            <!-- 有复选框的时候传 some  没有复选框的时候传one  -->

                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                    :type="'some'" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.gysbm">供应商编码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.gysmc">供应商名称</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.pydm">拼音代码</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.frdb">法人</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.gsdz">公司地址</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.gsdh">公司电话</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.jjlxr">联系人（紧急）</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.jjlxdh">联系人电话（紧急）</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.lxr">联系人</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.lxdh">联系人电话</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yx">邮箱</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.khhdz">开户行</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.yhzh">银行账号</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" > <a :href="'http://172.20.103.63:9081'+item.yyzzfiles"  v-text="item.yyzzname" target="_blank" >营业执照</a></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" > <a  :href="'http://172.20.103.63:9081'+item.jyxkzfiles"  v-text="item.jyxkzname" target="_blank" >经营许可证</a></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" > <a :href="'http://172.20.103.63:9081'+item.sqsfiles"  v-text="item.sqsname" target="_blank" >授权书</a></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.bz">备注描述</div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <div class=" switch">
                                        <input type="checkbox" true-value="0" false-value="1" v-model="item.tybz"
                                               disabled/>
                                        <label></label>
                                    </div>
                                </div>
                            </td>
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                       @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                                </span>
                                </div>
                            </td>
                            <p v-if="jsonList.length==0" class=" noData  text-center zan-border">暂无数据...</p>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <!--左侧固定-->
                <div class="zui-table-fixed table-fixed-l" style="top: 36px;">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th>
                                    <div class="zui-table-cell cell-m">
                                    <span><input-checkbox @result="reCheckBox" :list="'jsonList'" :type="'all'"
                                                          :val="isCheckAll">
                                    </input-checkbox>
                                    </span>
                                    </div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <!-- data-no-change -->
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in jsonList"
                                :tabindex="$index"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                        :type="'some'" :which="$index"
                                                        :val="isChecked[$index]">
                                        </input-checkbox>
                                    </div>
                                </td>
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m">{{$index+1}}</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--右侧固定-->
                <div class="zui-table-fixed table-fixed-r" style="top: 36px; right: -6px;">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-s">
                                    <div class="zui-table-cell cell-s"><span>操作</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(item, $index) in jsonList"
                                :tabindex="$index"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="hoverMouse(true,$index)"
                                @mouseleave="hoverMouse()"
                                @click="checkSelect([$index,'some','jsonList'],$event)">
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                       @click="edit($index)"></i></em>
                                <em class="width30"><i class="icon-sc" data-title="删除" @click="remove($index)"></i></em>
                            </span>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>


            </div>
            <!--end-->
            <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore"
                  :next-more="nextMore"></page>

        </div>
    </div>
    <div class="side-form" :class="{'ng-hide':nums==0}" :data-style="widths" v-cloak id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--设备大类-->
        <div class="ksys-side" v-if="num==0">
        <span class="span0">
            <i>大类编码</i>
            <input type="text" class="zui-input background-h" v-model="popContent.dlbm" disabled="disabled"/>
        </span>
            <span class="span0">
            <i>大类名称</i>
            <input type="text" class="zui-input" v-model="popContent.dlmc"
                   @blur="setPYDM(popContent.dlmc,'popContent','pydm')" @keydown="nextFocus($event)"/>
        </span>
            <span class="span0">
            <i>拼音代码</i>
            <input type="text" class="zui-input " v-model="popContent.pydm" disabled="disabled"/>
        </span>
            <span class="span0">
            <i>停用标志</i>
            <select-input @change-data="resultChange" :not_empty="true" :child="stopSign" :index="popContent.tybz"
                          :val="popContent.tybz" :name="'popContent.tybz'">
			</select-input>
        </span>
        </div>
        <!--设备类别-->
        <div class="ksys-side" v-if="num==1">
        <span class="span0">
            <i>类别编码</i>
            <input type="text" class="zui-input background-h" v-model="popContent.lbbm" disabled="disabled"/>
        </span>
            <span class="span0">
            <i>类别名称</i>
            <input type="text" class="zui-input" v-model="popContent.lbmc"
                   @blur="setPYDM(popContent.lbmc,'popContent','pydm')" @keydown="nextFocus($event)"/>
        </span>
            <span class="span0">
            <i>大类名称</i>
                <!-- <input type="text" class="zui-input "/> -->
            <select-input @change-data="resultChange" :child="dlList" :index="'dlmc'" :index_val="'dlbm'"
                          :val="popContent.dlbm"
                          :name="'popContent.dlbm'" :search="true" :index_mc="'dlmc'" @keydown="nextFocus($event)">
			</select-input>
        </span>
            <!-- <span class="span0">
               <i>大类编码</i>
               <input type="text" class="zui-input background-h" disabled="disabled"/>
            </span> -->
            <span class="span0">
            <i>拼音代码</i>
            <input type="text" class="zui-input background-h" v-model="popContent.pydm" disabled="disabled"/>
        </span>
            <span class="span0">
            <i>停用标志</i>
            <select-input @change-data="resultChange" :not_empty="true" :child="stopSign" :index="popContent.tybz"
                          :val="popContent.tybz" :name="'popContent.tybz'">
			</select-input>
        </span>

        </div>
        <!--设备单位-->
        <div class="ksys-side" v-if="num==2">
        <span class="span0">
            <i>单位编码</i>
            <input type="text" class="zui-input background-h" v-model="popContent.dwbm" disabled="disabled"/>
        </span>
            <span class="span0">
            <i>单位名称</i>
            <input type="text" class="zui-input" v-model="popContent.dwmc"
                   @blur="setPYDM(popContent.dwmc,'popContent','pydm')" @keydown="nextFocus($event)"/>
        </span>
            <span class="span0">
            <i>拼音代码</i>
            <input type="text" class="zui-input background-h" v-model="popContent.pydm" disabled="disabled"/>
        </span>
            <span class="span0">
            <i>停用标志</i>
            <select-input @change-data="resultChange" :not_empty="true" :child="stopSign" :index="popContent.tybz"
                          :val="popContent.tybz" :name="'popContent.tybz'">
			</select-input>
        </span>
        </div>
        <!--设备字典-->
        <div class="ksys-side" v-if="num==3">
            <ul class="tab-edit-list tab-edit2-list">

                <li>
                    <i>设备编码</i>
                    <input type="text" class="label-input background-h" v-model="popContent.sbbm" />
                </li>
                <li>
                    <i>设备名称</i>
                    <input type="text" class="label-input " v-model="popContent.sbmc"
                           @blur="setPYDM(popContent.sbmc,'popContent','pydm')" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>拼音代码</i>
                    <input type="text" class="label-input background-h" v-model="popContent.pydm" disabled>
                </li>

                <li>
                    <i>设备类别</i>
                    <select-input @change-data="resultChange" :child="lbList" :index="'lbmc'"
                                   :index_val="'lbbm'" :val="popContent.sblb" :search="true" :name="'popContent.sblb'">
                    </select-input>
                </li>
                <li>
                    <i>设备规格</i>
                    <input type="text" class="label-input" v-model="popContent.sbgg" @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>库房单位</i>
                    <select-input @change-data="resultChange" :child="dwList" :index="'dwmc'"
                                  :index_val="'dwbm'" :val="popContent.kfdw" :search="true" :name="'popContent.kfdw'">
                    </select-input>
                </li>
                <li>
                    <i>领用单位</i>
                    <select-input @change-data="resultChange" :child="dwList" :index="'dwmc'"
                                  :index_val="'dwbm'" :val="popContent.lydw" :search="true" :name="'popContent.lydw'">
                    </select-input>
                </li>
                <!--<li>-->
                    <!--<i>分装比例</i>-->
                    <!--<input type="text" class="label-input" @input="changeFzbl(false,$event.target.value)" v-model="popContent.fzbl" @keydown="nextFocus($event)">-->
                <!--</li>-->

                <!--<li>-->
                    <!--<i>进价</i>-->
                    <!--<input type="text" class="label-input" v-model="popContent.jj" @keydown="nextFocus($event)">-->
                <!--</li>-->
                <!--<li>-->
                    <!--<i>单价</i>-->
                    <!--<input type="text" class="label-input" v-model="popContent.dj" @keydown="nextFocus($event)">-->
                <!--</li>-->
                <li>
                    <i>产地</i>
                    <input type="text" class="label-input" v-model="popContent.cd" @keydown="nextFocus($event)">
                </li>
                <li>
                    <i>停用标志</i>
                    <select-input @change-data="resultChange" :not_empty="true" :child="stopSign"
                                  :index="popContent.tybz" :val="popContent.tybz" :name="'popContent.tybz'">
                    </select-input>
                </li>
                <li>
                    <i>备注描述</i>
                    <input type="text" class="label-input" v-model="popContent.bzms">
                </li>
            </ul>

        </div>
        <!--设备字典-->
        <div class="ksys-side" v-if="num==4">
            <ul class="tab-edit-list tab-edit2-list">

                <li>
                    <i>供应商编码</i>
                    <input type="text" class="label-input background-h" v-model="popContent.gysbm" disabled/>
                </li>
                <li>
                    <i>供应商名称</i>
                    <input type="text" class="label-input " v-model="popContent.gysmc"
                           @blur="setPYDM(popContent.gysmc,'popContent','pydm')" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>拼音代码</i>
                    <input type="text" class="label-input background-h" v-model="popContent.pydm" disabled>
                </li>
                <li>
                    <i>法人姓名</i>
                    <input type="text" class="label-input background-h" v-model="popContent.frdb" >
                </li>
                <li>
                    <i>公司地址</i>
                    <input type="text" class="label-input background-h" v-model="popContent.gsdz" >
                </li>
                <li>
                    <i>公司电话</i>
                    <input type="text" class="label-input background-h" v-model="popContent.gsdh" >
                </li>
                <li>
                    <i>联系人(紧急)</i>
                    <input type="text" class="label-input background-h" v-model="popContent.jjlxr" >
                </li>
                <li>
                    <i>联系电话(紧急)</i>
                    <input type="text" class="label-input background-h" v-model="popContent.jjlxdh" >
                </li>
                <li>
                    <i>联系人</i>
                    <input type="text" class="label-input background-h" v-model="popContent.lxr" >
                </li>
                <li>
                    <i>联系电话</i>
                    <input type="text" class="label-input background-h" v-model="popContent.lxdh" >
                </li>
                <li>
                    <i>开户行地址</i>
                    <input type="text" class="label-input background-h" v-model="popContent.khhdz" >
                </li>
                <li>
                    <i>银行账号</i>
                    <input type="text" class="label-input background-h" v-model="popContent.yhzh" >
                </li>
                <li>
                    <i>邮箱</i>
                    <input type="text" class="label-input background-h" v-model="popContent.yx" >
                </li>
                <li>
                    <i>停用标志</i>
                    <select-input @change-data="resultChange" :not_empty="true" :child="stopSign"
                                  :index="popContent.tybz" :val="popContent.tybz" :name="'popContent.tybz'">
                    </select-input>
                </li>
                <li>
                    <i>备注描述</i>
                    <input type="text" class="label-input" v-model="popContent.bz">
                </li>
                <li></li>
                <li>
                 <i>营业执照</i>
                    <input type="file" class="label-input"  @change="getFile($event,1)">
                </li>
                <li>
                    <a href=""    id ="yyzzname" target="view_window"></a>
                </li>
                <li>
                    <i>经营许可证</i>
                    <input type="file" class="label-input" @change="getFile($event,2)">
                </li>
                <li>
                    <a href=""   id ="jyxkzname" target="view_window"></a>
                </li>
                <li>
                    <i>授权书</i>
                    <input type="file"  class="label-input"   @change="getFile($event,3)">
                </li>
                <li>
                    <a href=""   id ="sqsname" target="view_window"></a>
                </li>
            </ul>

        </div>

        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="save">保存</button>
        </div>
    </div>

</div>

</body>

<script type="text/javascript" src="sbzd.js"></script>

</html>
