<!DOCTYPE html>
<html lang="en">
<head>
	
    <link href="brPage/user.css" rel="stylesheet">
    <link href="brPage/pr.css" rel="stylesheet" media="print">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <style>
         .zui-table-view .zui-table-cell {
            height: unset;
            vertical-align: top;
        }

        .hzList input {
            height: 28px;
        }

        .hzgl-height {
            height: calc(100% - 132px);
            padding: 8px 10px;
            overflow: hidden;
        }

        .ksys-side i {
            display: initial;
        }

        .color-dshdzcf {
            color: #d25747;
            font-size: 18px;
            font-weight: bolder;

        }

        .zui-table-view .setScroll::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        .bqcydj_model{
            width: auto;
            padding: 14px 18px 14px 18px;
            background: rgba(245, 246, 250, 1);
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        #model{
            z-index: 11111;
        }
        .minWh100{
            min-width: 100px;
        }
        .dzcf input{
            height: 28px;
        }
    </style>

</head>

<body>
<div class=" ynPrintPage"></div>
<div class="wrapper printHide flex-container flex-dir-c">
    <div class="background-box flex-one flex-container flex-dir-c">
        <div class=" flex-one flex-container flex-dir-c" id="yzcl">
            <div class="dzcf-width" id="gmsxx" v-if="sfgm=='1'"><i class="icon-gth"></i><i class="red">过敏史：<span v-text="gms"></span></i></div>
            <div class="tong-search background-f" >
                <div class="zui-form">
                    <div class="zui-inline">
                        <span class="zui-form-label">选择药房</span>
                            <select-input class="wh122" @change-data="resultLxChange" :child="YFJson" :index="'yfmc'"
                                          :index_val="'yfbm'"
                                          :val="serchContent.yfbm" :name="'serchContent.yfbm'">
                            </select-input>
                            <!--<input type="text" class="zui-input background-h" v-if="!InShow || YFJson" v-model="YFJson[0].yfmc" disabled />-->
                            <!--<span class="iconClass" v-if="InShow==false"></span>-->
                    </div>
                    <div class="zui-inline">
                        <span class="zui-form-label">处方类型</span>
                            <select-input class="wh122" @change-data="resultLxChange" :child="CflxJosn" :index="'cflxmc'"
                                          :index_val="'cflxbm'" :val="serchContent.cflxbm"
                                          :name="'serchContent.cflxbm'">
                            </select-input>
                            <!--<input type="text" v-if="!InShow || CflxJosn" class="zui-input background-h" v-model="CflxJosn[0].cflxmc" disabled />-->
                            <!--<span class="iconClass" v-if="InShow==false"></span>-->
                    </div>
                    <div class="zui-inline">
                        <span class="zui-form-label">处方流转</span>
                        <select-input @change-data="resultChange" :not_empty="false"
                                      :child="sflzcf_tran" :index="serchContent.sflzcf" :val="serchContent.sflzcf"
                                      :name="'serchContent.sflzcf'" :search="true">
                        </select-input>
                        <!--<input type="text" v-if="!InShow || CflxJosn" class="zui-input background-h" v-model="CflxJosn[0].cflxmc" disabled />-->
                        <!--<span class="iconClass" v-if="InShow==false"></span>-->
                    </div>

					<template v-if="Brxx_List.fbbm=='40' || Brxx_List.fbbm=='41'">
                        <div class="zui-inline">
                            <span class="zui-form-label">病种编码</span>
                            <div class="zui-input-inline wh45" style="width: 18em;">
                                <input class="zui-input" data-notEmpty="false" v-model="popContent.mtbzmc"
                                       @keydown="mtchangeDown($event,'jbMT','mContent','msearchCon','mtbzbm','mtbzbm','mtbzmc','mtselSearch4')"
                                       @input="mtchange(false,'jbMT',$event.target.value,'mtselSearch4','mtbzmc','mtbzbm',$event)">
                                <jbsearch-table :message="msearchCon" :selected="mtselSearch4" :page="page" :them="mthem"
                                                @click-one="checkedOneOut"
                                                @click-two="mselectOne">
                                </jbsearch-table>
                                <!--<input type="text" class="zui-input background-h" v-model="lczd" disabled v-if="InShow==false"/>-->
                            </div>
                        </div>
<!--						<div class="zui-inline">-->
<!--											    <span class="zui-form-label">处方诊断</span>-->
<!--                                                    <div style="float:left;" v-if='Brxx_List.jbbm'><input-checkbox style="float: left;padding-top: 8px;padding-right: 5px;"  :type="'some'" @result="reCheckBoxZyh" :which="90" :val="iszdchecked['90']"></input-checkbox>{{Brxx_List.jbmc}}</div>-->
<!--                                                    <div style="float:left;" v-if='Brxx_List.qtzdbm'><input-checkbox style="float: left;padding-top: 8px;padding-right: 5px;" class="padd-r-10 padd-b-10 cursor" :type="'some'" @result="reCheckBoxZyh" :which="'91'" :val="iszdchecked['91']"></input-checkbox>{{Brxx_List.qtzdmc}}</div>-->
<!--                                                    <div style="float:left;" v-if='Brxx_List.qtzdbm1'><input-checkbox style="float: left;padding-top: 8px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'92'" :val="iszdchecked['92']"></input-checkbox>{{Brxx_List.qtzdmc1}}</div>-->
<!--                                                    <div style="float:left;" v-if='Brxx_List.qtzdbm2'><input-checkbox style="float: left;padding-top: 8px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'93'" :val="iszdchecked['93']"></input-checkbox>{{Brxx_List.qtzdmc2}}</div>-->
<!--                                                    <div style="float:left;" v-if='Brxx_List.qtzdbm3'><input-checkbox style="float: left;padding-top: 8px;padding-right: 5px;"  :type="'some'" @result="reCheckBoxZyh" :which="'94'" :val="iszdchecked['94']"></input-checkbox>{{Brxx_List.qtzdmc3}}</div>-->
<!--                                                    <div style="float:left;" v-if='Brxx_List.qtzdbm4'><input-checkbox style="float: left;padding-top: 8px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'95'" :val="iszdchecked['95']"></input-checkbox>{{Brxx_List.qtzdmc4}}</div>-->


<!--													<template v-for="(item, $index) in fjzdlist">-->

<!--                                                        <div style="float:left;" ><input-checkbox style="float: left;padding-top: 8px;padding-right: 5px;" style="padd-r-10 padd-b-10 cursor" :type="'some'" @result="reCheckBoxZyh" :which="$index" :val="iszdchecked[$index]"></input-checkbox>{{item.jbmc}}</div>-->
<!--                                                    </template>-->
<!--						</div>-->


					</template>
<!--					<template v-else>-->
<!--						<div class="zui-inline">-->
<!--						    <span class="zui-form-label">处方诊断</span>-->
<!--						    <div class="zui-input-inline wh45" style="width: 70em;">-->
<!--						        <input class="zui-input" v-model="lczd" data-notEmpty="false"-->
<!--						               @input="searching(null,$event.target.value)"-->
<!--						               @keyDown="changeDown($event)">-->
<!--						        <search-table :message="searchCon" :selected="selSearch" :them="them" :page="page"-->
<!--						                      @click-one="checkedOneOut" @click-two="selectOne">-->
<!--						        </search-table>-->
<!--						        &lt;!&ndash;<input type="text" class="zui-input background-h" v-model="lczd" disabled v-if="InShow==false"/>&ndash;&gt;-->
<!--						    </div>-->
<!--						</div>-->
<!--					</template>-->

                    <div class="zui-inline">
                        <span class="zui-form-label">处方诊断</span>
                        <div style="float:left;" v-if='Brxx_List.jbbm'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;"  :type="'some'" @result="reCheckBoxZyh" :which="90" :val="iszdchecked['90']"></input-checkbox>{{Brxx_List.jbmc}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" class="padd-r-10 padd-b-10 cursor" :type="'some'" @result="reCheckBoxZyh" :which="'91'" :val="iszdchecked['91']"></input-checkbox>{{Brxx_List.qtzdmc}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm1'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'92'" :val="iszdchecked['92']"></input-checkbox>{{Brxx_List.qtzdmc1}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm2'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'93'" :val="iszdchecked['93']"></input-checkbox>{{Brxx_List.qtzdmc2}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm3'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;"  :type="'some'" @result="reCheckBoxZyh" :which="'94'" :val="iszdchecked['94']"></input-checkbox>{{Brxx_List.qtzdmc3}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm4'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'95'" :val="iszdchecked['95']"></input-checkbox>{{Brxx_List.qtzdmc4}}</div>
                        <template v-for="(item, $index) in fjzdlist">
                            <div style="float:left;" ><input-checkbox style="float: left;margin-left: 17px; padding-top: 1px;padding-right: 5px;" style="padd-r-10 padd-b-10 cursor" :type="'some'" @result="reCheckBoxZyh" :which="$index" :val="iszdchecked[$index]"></input-checkbox>{{item.jbmc}}</div>
                        </template>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10 padd-b-10" >
                        <button v-waves class="tong-btn btn-parmary height-28  margin-l-10" style="margin-top: -4px;"  @click="openFjzd()">添加诊断</button>
                        <div style="float:left; margin-left: 17px;" v-if="Brxx_List.ghksmc =='急诊科'">
                            <input  type="checkbox" style="float: left;padding-top: 1px;padding-right: 5px; width: 16px; height: 16px; font-size: 12px; margin-right: 2px;"
                                    :checked="isHff" @change="isXzlhff($event.target.checked)"/>
                            先诊疗后付费
                        </div>
                    </div>
                </div>
            </div>
            <div class="dzcf brjz-bottom flex-one flex-container flex-dir-c">
                <div class="flex-container flex-jus-sb position bg-fff">
                    <div class="scrollpic flex-container">
                        <i class="tab-left" @click="move('right')" v-if="cfList.length>=6" id="left">
                            <<</i>
                        <div id="myscroll">
                            <div id="myscrollbox" class="over-auto flex-container">
                                <ul>
                                    <li v-for="(item,$index) in cfList" :class="{'active':$index==num}"
                                        @click="Wf_selectCFMX(item.cfh,$index)">
                                        <a class="flex-container flex-jus-c whiteSpace padd-l-5 padd-r-5 flex-align-c">
                                            {{$index + 1}}{{item.cflxmc}}
                                            <i class="icon-iocn26 iconfont  padd-l-5" v-if="item.kfbz==1 && item.fybz==0" data-title="已收费"></i>
                                            <i v-if="item.fybz==1 && item.zfbz==0" data-title="已发药" class="icon-iocn9 iconfont  padd-l-5"></i>
                                            <i v-if="item.zfbz==1" data-title="已作废" class="icon-iocn15 iconfont  padd-l-5 padd-r-5"></i>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <span class="brjz-add" @click="addCf($event)">+</span>
                        <i class="tab-right" v-if="cfList.length>=6" @click="move('left')" id="right">>></i>
                    </div>
                    <ul class="brjz-right-list">
                        <li @click="showDbrPop" v-if="dmsmxx">实名信息</li>
                        <li @click="addYh" v-if="addShow[num]">
                            <i class="icon-width icon-tops icon-xz-h"></i>
                            <i class="">新增一行</i></li>
                        <li @click="Ypxz" v-if="addShow[num]">
                            <i class="icon-width icon-top icon-fy-h"></i>
                            <i class="">药品选择</i></li>
                        <li @click="delCf">
                            <i class="icon-sc margin-l-8"></i>
                            <i>作废处方</i></li>
                        <li class="cfdyhover">
                            <i class="icon-width icon-top icon-kz"></i>
                            <i class="">处方调用</i>
                            <span class="dzcf-erj">
                                <a @click="Cfdy">处方模板</a>
                                <a @click="Lscf">历史处方</a>
                                <a @click="zdCf">诊断处方</a>
                                <a v-if="Brxx_List.mtfa =='1'" @click="mtcfFun">门特处方</a>
                            </span>

                        </li>
                    </ul>

                </div>
                <div class=" flex-container skin-default  flex-dir-c">
                    <div class="zui-table-view " key="a" v-if="xyShow">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50" :style="{backgroundColor: ishff === '1' ? '#8de4ce' : '' }">
                                <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                                    </th>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>组号</span></div>
                                    </th>
                                    <th style="width: 445px;">
                                        <div class="zui-table-cell text-left" style="width: 343px"><span>药品名</span>
                                        </div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单次剂量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>用法</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>用药频次</span></div>
                                    </th>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>天数</span></div>
                                    </th>
                                    <th class="">
                                        <div style="width: 61px !important;" class="zui-table-cell "><span>总量</span></div>
                                    </th>

                                    <th v-if="sfxxjzyy">
                                        <div class="zui-table-cell cell-l"><span>开嘱时间</span></div>
                                    </th>
									<th v-if="Brxx_List.fbbm=='40'">
									    <div class="zui-table-cell cell-l"><span>用药开始日期</span></div>
									</th>
									<th v-if="Brxx_List.fbbm=='40'">
									    <div class="zui-table-cell cell-l"><span>用药结束日期</span></div>
									</th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>总价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>输液速度</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>输液单位</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>备注说明</span></div>
                                    </th>
                                    <th class="cell-m" v-if="addShow[num]">
                                        <div class="zui-table-cell cell-m"><span>操作</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <!-- data-no-change-->
                        <div class="zui-table-body zuiTableBody setScroll" data-no-change="true"
                             @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <!-- @click="checkSelect([$index,'some','PfxxJson'],$event)"
                            -->
                                <tr v-for="(item, $index) in PfxxJson" class="tableTr2" :tabindex="$index"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="switchIndex('hoverIndex',true,$index)" @mouseleave="hoverMouse()"

                                    @click="switchIndex('activeIndex',true,$index)"
                                    ref="list">
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                    </td>
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m" v-if="!addShow[num]" v-text="item.fzh"></div>
                                        <div class=" cell-m" v-if="addShow[num]">
                                            <!--<select-input style="width: 50px;" @change-data="Wf_YppfChange"-->
                                            <!--:not_empty="false" :child="fzh_tran" :index="item.fzh"-->
                                            <!--:val="item.fzh"-->
                                            <!--:name="'PfxxJson.'+$index+'.fzh'" :search="true">-->
                                                <!--</select-input>-->
                                            <input autocomplete="off" :id="'fzh' + $index" @keydown="nextSelect($event)" @mousewheel.prevent type="number"
                                                   class="zui-input input-border"
                                                   v-model="item.fzh" @input="Wf_fzh($index)" @blur="Wf_fzh($index)"
                                                   data-notEmpty="false" name="text"/>
                                        </div>
                                    </td>
<!--                                    药品名称-->
                                    <td style="width:445px">
                                        <div :class="color[item.gwyp]" class="zui-table-cell flex-container text-left flex-align-c"
                                             style="width: 343px;">
                                            <div class="relative" v-if="!addShow[num]"  @click.left="importInfo(item.ypmc,item.ypbm)"  @contextmenu.prevent="clickOnTest(item.ypmc,item.ypbm)">
                                                {{item.ypmc}}
                                            </div>
                                            <input autocomplete="off" :class="color[item.gwyp]" class="zui-input flex-one" v-model="item.ypmc"
                                                    @dblclick="importInfo(item.ypmc,item.ypbm)"
                                                    @contextmenu.prevent="clickOnTest(item.ypmc,item.ypbm)"
                                                   @keyup="changeDown($index,$event,'xy','searchCon')"
                                                   :id="'ypmc' + $index"
                                                   @input="Wf_change(false,$index,'xy', $event.target.value,$event)"
                                                   v-if="addShow[num]"/>
                                            <search-table :message="searchCon" :selected="selSearch" :page="page"
                                                          :them="them" :them_tran="them_tran" @click-one="checkedOneOut"
                                                          @click-two="selectOne1"></search-table>
                                            <span class="minWh100">
                                                    <i class="title title-width" >{{item.ypgg}}{{(item.psjg =='0' || item.psjg =='1') ? '【'+psjg_tran[item.psjg]+'】':''}}</i>
                                            </span>

                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell dzcf-cell cell-s">
                                            <span v-if="!addShow[num]">{{item.yyjl}}&emsp;</span>
                                            <input autocomplete="off" type="number" class="zui-input input-border" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                                                   v-model="item.yyjl" :id="'yyjl'+$index" name="text"
                                                   @keydown.13="pubJS($index),nextFocusJl($event,$index,item.yyjl)" data-notEmpty="false" min="0"
                                                   @input="pubJS($index)"
                                                   v-if="addShow[num]"/>
                                            <span class="cm" v-text="item.jldwmc"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-if="!addShow[num]"
                                             v-text="item.yyffmc"></div>

                                        <div over-auto class="zui-table-cell cell-l " v-if="addShow[num] && !csqxContent.N050012002701">
                                            <select-input @change-data="Wf_YppfChange" :not_empty="false" :child="YyffData"
                                                :index="'yyffmc'" :index_val="'yyffbm'" :val="item.yyff" :name="'PfxxJson.'+$index+'.yyff'"
                                                :index_mc="'yyffmc'" :search="true">
                                            </select-input>
                                        </div>
                                        <div class="zui-table-cell cell-l " v-if="addShow[num] && csqxContent.N050012002701 =='1'">
                                            <input autocomplete="off" class="zui-input" :id="'yyffmc_' + $index" type="text"
                                                   :value="item.yyffmc" data-notEmpty="false"
                                                   @keydown="Wf_changeDown3($index,$event,'yyffmc')"
                                                   @input="Wf_change3($index,'yyffmc',$event.target.value)"/>
                                            <search-table3 :message="searchCon3" :selected="selSearch3" :them="them3"
                                                           :them_tran="them_tran3" :page="page2"
                                                           @click-one="checkedOneOut" @click-two="selectOne3">
                                            </search-table3>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-if="!addShow[num]"
                                             v-text="item.yypcmc"></div>

                                        <div over-auto class="zui-table-cell cell-l" v-if="addShow[num] && !csqxContent.N050012002701">
                                            <select-input @change-data="Wf_YppfChange" :not_empty="false" :child="PcData"
                                                :index="'pcmc'" :index_val="'pcbm'" :val="item.yypc" :name="'PfxxJson.'+$index+'.yypc'"
                                                :index_mc="'yypcmc'" :search="true">
                                            </select-input>
                                        </div>

                                        <div class="zui-table-cell cell-l " v-if="addShow[num] &&  csqxContent.N050012002701 == '1'">
                                            <input autocomplete="off" class="zui-input" :id="'yypcmc_' + $index" type="text"
                                                   v-model="item.yypcmc" data-notEmpty="false"
                                                   @keydown="Wf_changeDown4($index,$event,'yypcmc')"
                                                   @input="Wf_change4($index,'yypcmc',$event.target.value)"/>
                                            <search-table4 :message="searchCon4" :selected="selSearch4" :them="them4"
                                                           :them_tran="them_tran4" :page="page3"
                                                           @click-one="checkedOneOut" @click-two="selectOne4">
                                            </search-table4>
                                        </div>


                                    </td>
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m" v-if="!addShow[num]" v-text="item.yyts"></div>
                                        <div class="zui-table-cell cell-m" v-if="addShow[num]">

                                            <input autocomplete="off"  type="text" class="zui-input input-border title"
                                                   v-model.number="item.yyts" @input="pubJS($index)" :data-gettitle="'处方最多'+serchContent.cfzdts+'天'"
                                                   @keydown="nextSelect($event,1)" :maxlength="serchContent.cfzdts"
                                                   data-notEmpty="false" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell" style="width: 61px !important;">
                                            <div v-if="!addShow[num]">{{item.fysl}}&emsp;</div>
                                            <input  :disabled="csqxContent.N03001200141=='0'?true:false" @mousewheel.prevent type="number" class="zui-input input-border "
                                                   v-if="addShow[num]" v-model.number="item.fysl"
                                                   @input="cfylChange($index)"
                                                   @keydown="nextFocusYyff($event,$index)" data-notEmpty="false"/>
                                            <span class="cm" v-text="item.fydwmc1"></span>
                                        </div>
                                    </td>
                                    <td  v-if="sfxxjzyy">
                                        <div class="zui-table-cell cell-l">
                                            <input autocomplete="off" style="text-indent: 0;" type="text" class="zui-input" :class="'jzyyrq'+$index" :id="'jzyyrq'+$index"
                                                   v-model="fDate(item.jzyyrq,'AllDate')" data-select="no" @click="showJzyyDate($index,$event)"    />
                                        </div>
                                    </td>
									<td   v-if="Brxx_List.fbbm=='40'">
									    <div class="zui-table-cell cell-l">
									        <input autocomplete="off" style="text-indent: 0;" type="text" class="zui-input" :class="'startdate'+$index" :id="'startdate'+$index"
									               v-model="fDate(item.mtksrq,'date')" data-select="no" @click="showksDate($index,$event,'1')"    />
									    </div>
									</td>
									<td   v-if="Brxx_List.fbbm=='40'">
										<div class="zui-table-cell cell-l">
											<input autocomplete="off" style="text-indent: 0;" type="text" class="zui-input" :class="'enddate'+$index" :id="'enddate'+$index"
											       v-model="fDate(item.mtjsrq,'date')" data-select="no" @click="showksDate($index,$event,'2')"   />
										</div>
									</td>


                                    <td>
<!--                                        fDec(item.yplj * item.fyjl,2)-->
                                        <div class="zui-table-cell cell-s" v-text="fDec((!item.yplj ? '0': item.yplj),2)"></div>
                                    </td>
                                    <td>
                                        <!--   总价    -->
                                        <div  class="zui-table-cell cell-s" v-text="fDec((!item.fysl? 0 :item.fysl*item.yplj),2)"></div>

                                    </td>



                                    <td>
                                        <div class="zui-table-cell cell-s" v-if="!addShow[num]"
                                             v-text="item.sysd"></div>
                                        <div class="zui-table-cell cell-s" v-if="addShow[num]">

                                            <input @keydown="nextSelect($event)" @mousewheel.prevent type="number" class="zui-input input-border"
                                                   v-model="item.sysd" name="text"
                                                   data-notEmpty="false"/>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-if="!addShow[num]"
                                             v-text="sydw_tran[item.sysddw]"></div>
                                        <div over-auto class="zui-table-cell cell-s" v-show="addShow[num]">
                                            <select-input @change-data="Wf_YppfChange" :not_empty="false"
                                                          :child="sydw_tran"
                                                          :index="item.sysddw" :val="item.sysddw"
                                                          :name="'PfxxJson.'+$index+'.sysddw'"
                                                          :search="true">
                                            </select-input>

                                        </div>
                                    </td>
                                    <td>
                                        <div  class="zui-table-cell  cell-s" v-if="!addShow[num]"
                                             v-text="item.yysm"></div>
                                        <div class="zui-table-cell cell-s" v-if="addShow[num]">
                                            <input @keydown="nextSelect($event)" type="text" class="zui-input input-border wh90" v-model="item.yysm"
                                                   name="text" @keyup.13="Wf_YppfChange('yysm',$index)"
                                                   data-notEmpty="false"/>
                                        </div>
                                    </td>
                                    <td class="cell-m" v-if="addShow[num]">
                                        <div class="zui-table-cell cell-m">
                                            <i class="icon-sc icon-font" @click="removeNow($index)"></i>
                                        </div>
                                    </td>
                                    <p class="noData  text-center zan-border" v-if="PfxxJson.length==0"> 暂无数据</p>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="zui-table-fixed table-fixed-l">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body zuiTableBody" @scroll="scrollTableFixed($event)">
                                <table class="zui-table">
                                    <tbody>
                                    <tr v-for="(item, $index) in PfxxJson" :tabindex="$index" class="tableTr2"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'one','PfxxJson'],$event)"
                                        ref="list">
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m">{{$index+1}}</div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="zui-table-fixed table-fixed-r">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>操作</span></div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body zuiTableBody" @scroll="scrollTableFixed($event)">
                                <table class="zui-table">
                                    <tbody>
                                    <tr v-for="(item, $index) in PfxxJson" :tabindex="$index" class="tableTr2"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'one','PfxxJson'],$event)"
                                        ref="list">
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m">
                                                <i class="icon-sc icon-font" @click="removeNow($index)"
                                                   data-title="删除"></i>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="dzcf-rp flex-container over-auto" style="height: 100%" key="b" v-if="zyShow">
                        <span class="dzcf-rps"><img src="/newzui/pub/image/<EMAIL>"/> </span>
                        <div class="tab-card-body " style="width: 100%">
                            <div class="grid-box zui-form zcyItem">
                                <div class="flex-container  flex-align-c flex-wrap-w">
                                    <div style="width: 13%" class=" padd-b-15  margin-r-10"
                                         v-for="(item,$index) in zcyList">
                                        <div
                                             class="padd-b-5  padd-r-5  hzgl-sc ">
											 
											 
                                            <select-input :disable="!addShow[num]" @change-data="resultChange_yyff"
                                                          :not_empty="false" :child="ZyYyffData" :index="'yyffmc'"
                                                          :index_val="'yyffbm'"
                                                          :val="item.yyff" :name="'zcyList.'+$index+'.yyffmc'"
                                                          :index_mc="'yyffmc'" :search="true"
                                                          :phd="'用法'">
                                            </select-input>
                                        </div>
<!--                                        中药名称-->
                                        <div class=" padd-b-5  padd-r-5 position">
                                            <i class="hzgl-shanchu" @click="remove($index)"></i>
                                            <input :disabled="!addShow[num]" class="zui-input " v-model="item.ypmc"
                                                   @contextmenu.prevent="clickOnZy(item.ypmc,item.ypbm)"
                                                   @keyup="changeDown($index,$event,'zy','searchCon2')"
                                                   @input="Wf_change(false,$index,'zy', $event.target.value,$event,$event)"
                                                   :id="'xmsr_' + $index"/>
                                            <search-table2 :message="searchCon2" :selected="selSearch" :page="page"
                                                           :them="them2"
                                                           :them_tran="them_tran2" @click-one="checkedOneOut"
                                                           @click-two="selectOne2"
                                                           v-if="addShow[num]"></search-table2>
                                        </div>
                                        <!--input用于计算用量  keydown用于回车跳转-->
                                        <div  class="padd-b-5   position   padd-r-5">
                                            <input style="text-align: right;padding-right: 20px;"
                                                   :disabled="!addShow[num]" class="zui-input " @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                                                   :tabindex="$index" type="number"
                                                   @input="Wf_zyYppfChange($index,$event)"
                                                   @keydown="Wf_zyYppfChange($index,$event)"
                                                   v-model="item.yyjl"/>
                                            <span class="cm">{{item.jldwmc}}</span>
                                        </div>
                                        <!--<div class=" wh22  ">
                                            <input :disabled="!addShow[num]" class="zui-input " :tabindex="$index"
                                                   @keydown="nextAdd($index,$event)"
                                                   v-model="item.yysm" placeholder="用药说明"/>
                                        </div>-->
                                    </div>
                                    <p class="add-yp-hzgl" @click="add()"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dzcf-js contextInfo" v-if="zyShow" style="padding-bottom: 0">
                    <div class="zui-form padd-t-15 flex-container">
                        <div class="flex-container padd-r-10 flex-align-c">
                            <label class="padd-r-10 whiteSpace">剂数</label>
                            <div class="zui-input-inline wh50">
                                <input type="number" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" placeholder="请输入剂数" v-model="cfcontent.zyfs"
                                       @input="Wf_yyfsChange()" :disabled="!addShow[num]"
                                       @keydown="nextFocus($event)" data-notEmpty="false"/>
                            </div>
                        </div>
                        <div class="flex-container padd-r-10 flex-align-c">
                            <label class="padd-r-10  whiteSpace">用法</label>
                            <div v-if="!addShow[num]" class="zui-input-inline" style="width: 300px;">
                                <input class="zui-input todate" style="width: 300px;" :disabled="!addShow[num]"
                                       placeholder="请输入用法" v-model="cfcontent.yysmcf"
                                       @keydown="nextFocus($event)" data-notEmpty="true"/>
                            </div>
                            <select-input v-if="addShow[num]" style="width: 300px;" class=" " @changevalue="ypyfFun"
                                          :phd="'请输入用法'"
                                          @change-data="resultChangeMb" :not_empty="true" :child="jsonList"
                                          :index="'yysmmc'"
                                          :index_val="'yysmbm'" :val="cfcontent.yysmbm" :name="'cfcontent.yysmbm'"
                                          :index_mc="'yysmcf'"
                                          :search="true" id="zybzsm">
                            </select-input>
                        </div>
                        <div class="flex-container padd-r-10 flex-align-c">
                            <label class="padd-r-10 whiteSpace">主病</label>
                            <div class="zui-input-inline wh150">
                                <input class="zui-input" :disabled="!addShow[num]" data-notEmpty="true" v-model="cfcontent.zyzh"
                                       @keydown="nextFocus($event)"
                                       data-notEmpty="true"/>
                            </div>
                        </div>
                        <div class="flex-container padd-r-10 flex-align-c">
                            <label class="padd-r-10 whiteSpace">症型</label>
                            <div class="zui-input-inline wh150">
                                <input class="zui-input" :disabled="!addShow[num]"  v-model="cfcontent.zyzf"
                                       @keydown="nextFocus($event)"
                                       data-notEmpty="true"/>
                            </div>
                        </div>
                        <div  v-if="csqxContent.N03001200123 == '1'" class="flex-container padd-r-10 flex-align-c">
                            <label class="padd-r-10 whiteSpace">汤头</label>
                            <div class="zui-input-inline wh150">
                                <input class="zui-input" :disabled="!addShow[num]" v-model="cfcontent.zytt"
                                       @keydown="nextFocus($event)"
                                       data-notEmpty="false"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dzcf-add" @click="showExtra = true" style="flex-wrap: wrap; ;">
                    <span style="width: 100%;display: block;float: left;padding-bottom: 15px;" v-if="fjfyIsShow">
                        <i class="icon-xz1 paddr-r5" @click="AddFy"></i>
                        <i @click="AddFy">添加附加费用</i>
                        <!-- <i @click="extraChargesList.push({})">添加附加费用</i> -->
                    </span>
                    <div class="extraCharges over-auto maxHeight" v-if="showFjfyInput">
                        <transition-group class="flex-container flex-wrap-w height100" name="notice-fade">
                            <div :key="$index" class="extraC" v-for="(item,$index) in extraChargesList">
                                <span v-text="item.mxfymc"></span>
                                <input @mousewheel.prevent v-model.number="item.fysl" type="number"
                                       @input="Wf_fjfChange($index)">
                                *<span v-text="item.fydj"></span>
                                =<span v-text="item.fyje"></span>元
                                <span class="fa fa-trash-o" @click="removeExtra($index)"></span>
                            </div>
                        </transition-group>
                    </div>
                </div>
                <model  :model-show="false"  @result-close="kss=false"
                       v-if="kss" :title="'抗生素使用目的'">
                    <div class="bqcydj_model">
                        <div class="flex-container flex-wrap-w">
                            <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                                <span class="padd-r-5">抗生素使用目的</span>
                                <select-input ref="kss" class="wh120" @change-data="resultChangeKss" :not_empty="false" :child="kjYwsymd_tran" :index="kssmd.kjywsymd"
                                              :val="kssmd.kjywsymd"  :search="true" :name="'kssmd.kjywsymd'">
                                </select-input>
                            </div>
                        </div>
                    </div>
                </model>

                <model class="kssModel" :s="'确定'" :c="'取消'" @default-click="saveKssSymd"  @result-clear="kssModelShow=false"  :model-show="true"  @result-close="kssModelShow=false" v-if="kssModelShow" :title="title">
                    <div class="bqcydj_model kssChildModel">
                    </div>
                </model>
            </div>
            <div class="zui-table-tool brjz-foot">
                <span class="flex-container">
                    <!--<button class="tong-btn btn-parmary-d9">作废</button>
                    <button v-waves class="tong-btn btn-parmary btn-parmary-not xmzb-db " @click="getHzJk360Infor()">患者健康档案(360)</button>
                    <button v-waves class="tong-btn btn-parmary btn-parmary-not xmzb-db " @click="getHzJksxzzInfor()">双向转诊</button>-->
                    <button v-waves v-if="csqxContent.N05001200276" class="tong-btn btn-parmary btn-parmary-not xmzb-db " @click="openHyll()">合理用药查询</button>
                    <!-- <button v-if="Brxx_List.sfzh" v-waves class="tong-btn btn-parmary btn-parmary-not xmzb-db " @click="getHzJksjkdaInfor()">健康档案</button>-->
                    <button v-waves class="tong-btn btn-parmary btn-parmary-not xmzb-db " @click="userdzbl()">电子病历</button>
                    <button v-waves class="tong-btn  btn-parmary xmzb-db" @click="printSyk">打印输液卡</button>
                    <button v-waves class="tong-btn  btn-parmary  xmzb-db" @click="printCf">打印</button>
                    <button v-waves class="tong-btn  btn-parmary  xmzb-db" @click="printZlCf">打印治疗处方</button>
                    <button v-waves class="tong-btn  btn-parmary  xmzb-db" v-if="isBshow" @click="saveModel">保存为模板</button>
                    <!--<button class="tong-btn btn-parmary" @click="saveShow?edit():save()" v-text="editTitle"></button>-->
                    <button v-waves v-if="N03001200127 == '1' && isBshow" class="tong-btn btn-parmary xmzb-db" @click="editYpcf()">编辑</button>
					<button v-waves v-if="Brxx_List.fbbm=='8'" class="tong-btn btn-parmary xmzb-db"  @click="xtxx()">血透信息</button>
<!--                    <button v-waves v-if="Brxx_List.fbbm=='40'" class="tong-btn btn-parmary xmzb-db"  @click="historyypxx()">历史药品信息</button>-->
                    <!-- <button  v-waves v-if="N03001200128 == '1'" class="tong-btn btn-parmary xmzb-db" @click="yndrZnsh()">智能审核</button>-->
                    <button v-waves class="tong-btn btn-parmary xmzb-db" v-if="isBshow" @click="save()">保存</button>
                    <button v-waves class="tong-btn btn-parmary xmzb-db" v-if="isBshow" @click="tysq('1')">退药</button>
					<button v-waves class="tong-btn btn-parmary xmzb-db" v-if="isBshow" @click="tysq('0')">取消退药</button>
                    <button v-waves class="tong-btn  btn-parmary  xmzb-db" @click="ybjsd">医保结算单</button>
                    <button v-waves class="tong-btn  btn-parmary  xmzb-db" @click="ybmxcx">医保明细查询</button>
                </span>
                <div class="maginl-10 flex-container" style="height: 72px !important;flex-wrap: wrap;flex: 1;">
                    <div style="flex: 0.3;"><i>处方医生：<em class="color-dshdzcf" v-text="msgContent.cfysxm"></em></i></div>
                    <div style="flex: 0.7;"><i>处方编号：<em class="color-dshdzcf" v-text="msgContent.cfh"></em></i></div>
                    <div style="flex: 0.3;"><i>处方金额:<em class="color-dshdzcf" v-text="fDec(sum,2)"></em>元</i></div>
                    <div style="flex: 0.7;"> <i>处方附加金额:<em class="color-dshdzcf" v-text="msgContent.CF_Fjhj">0</em>元</i></div>
                </span>

            </div>
        </div>
    </div>
    <div class="side-form  pop-width" :class="{'ng-hide':nums==1}" id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--添加附加费用-->
        <div class="ksys-side " v-if="tjShow" style="padding: 10px 10px 10px 16px;">
                <div class="flex-container flex-align-c padd-b-10">
                    <span class="padd-r-5 ft-14 whiteSpace">搜索</span>
                    <input type="text" class="zui-input wh182 " placeholder="请输入关键字" v-model="fjfjs"
                           @keyup.13="Wf_getFjfy"/>
                </div>
            <div class="zui-table-view ">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-m"><span>选项</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xl text-left"><span>名称</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>金额</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <!-- data-no-change -->
                <div class="zui-table-body" @scroll="scrollTable($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in FjfyJson" :tabindex="$index" ref="list"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'some','FjfyJson'],$event,'notIndex')">
                            <td>
                                <div class="zui-table-cell cell-m">
                                    <input-checkbox @result="reCheckBox" :list="'FjfyJson'" :type="'some'"
                                                    :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xl text-left" v-text="item.mxfymc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="item.fydj"></div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!--end-->
        <!--处方调用-->
        <div style="padding:15px 10px 5px" class="flex-container padd-l-10">
            <div class="zui-inline" v-show="zdShow">
                <button v-waves class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addcfZd">新增</button>
            </div>
            <div class="flex-container flex-align-c"  v-show="lsShow && lscfcxqx==0">
                <span class="whiteSpace ft-14 padd-r-5">时间</span>
                <div class="flex-container position  flex-align-c">
                    <i class="icon-position icon-rl"></i>
                    <input class="zui-input todate wh180 text-indent20" placeholder="请选择处方开始时间" id="timeVal"/><span
                        class="padd-l-5 padd-r-5">~</span>
                    <input class="zui-input todate wh150 " placeholder="请选择处方结束时间" id="timeVal1"/>
                </div>
            </div>
            <div class="flex-container flex-align-c padd-l-5" v-if="lsShow">
                <span class="whiteSpace ft-14 padd-r-5">个人</span>
                <select-input style="width: 70px;" @change-data="getCxqxChange" :not_empty="false"
                              :child="istrue_tran" :index="lscfcxqx" :val="lscfcxqx" :name="'lscfcxqx'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-l-5" v-if="cfShow">
                <span class="whiteSpace ft-14 padd-r-5">来源</span>
                <select-input style="width: 70px;" @change-data="resultlyChange" :not_empty="false"
                              :child="cfyyly_tran" :index="cfyylyidx" :val="cfyylyidx" :name="'cfyylyidx'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c " v-if="cfShow || ypShow || lsShow || zdShow">
                <span class=" whiteSpace ft-14 padd-r-5">搜索</span>
                <input class="zui-input wh182" v-if="cfShow" placeholder="请输入关键字" type="text" v-model="cfdyjs"
                       @keyup.13="getTemData($event)"/>
                <input class="zui-input wh182" v-if="ypShow" placeholder="请输入关键字" @keyup="Wf_YpChange($event)"
                       v-model="jsypxx"
                       type="text" id="searchYp"/>
                <input class="zui-input wh120" v-if="lsShow" placeholder="请输入关键字" type="text" v-model="lscfjs"
                       @keyup.13="getLsTemData($event)"/>
                <input class="zui-input wh182" v-if="zdShow" placeholder="请输入关键字" type="text" v-model="lczdValue"
                       @keyup.13="getLczdDate($event)"/>
            </div>
        </div>
        <div v-if="cfShow" class="ksys-side hzgl-height flex-container">
            <div class="col-x-12 flex-one">
                <div class="col-x-5 hzgl-wiwidth-one flex-one flex-container">
                    <div class="zui-table-view over-auto padd-l-10 padd-r-10 zui-item " v-cloak >
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>编码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>类型</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>治法</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>主治</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in cfList" :tabindex="$index" ref="list"
                                    :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                                    @mouseenter="hoverMouse1(true,$index)" @mouseleave="hoverMouse1()"
                                    @click="getTemMxData($index)"
                                    @dblclick="addCfMb($index)">
                                    <td>
                                        <div class="zui-table-cell cell-s " v-text="item.zhyzbm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l text-over-2" v-text="item.zhyzmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s " v-text="item.cflxmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="item.zf"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="item.zz"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <!--<p v-if="yzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </div>
                    </div>

                </div>
                <div class="col-x-7 margin-l-10 hzgl-wiwidth-two">
                    <div class="zui-table-view">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m">
                                            <input-checkbox @result="reCheckBox" :list="'cfMxList'" :type="'all'"
                                                            :val="isCheckAll">
                                            </input-checkbox>
                                        </div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl text-left"><span>名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>库存</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单位</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>数量</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in cfMxList" :tabindex="$index" ref="list"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                    @click="checkSelect([$index,'some','cfMxList'],$event)">
                                    <td>
                                        <div class="zui-table-cell cell-m">
                                            <input-checkbox @result="reCheckBox" :list="'cfMxList'" :type="'some'"
                                                            :which="$index" :val="isChecked[$index]">
                                            </input-checkbox>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-xl text-left" v-text="item.ypmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" v-text="item.ypgg"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.kcsl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yplj"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.cfyl"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end-->
        <!--选择药品-->
        <div v-if="ypShow" class="ksys-side hzgl-height flex-container">
            <div class="col-x-12 flex-one">
                <div class="  flex-one flex-container">
                    <div class="zui-table-view over-auto zui-item padd-r-10 padd-l-10">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m"><span>选项</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>药品名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>药品规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>商品名</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>产地</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>库存</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单位</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>医保统筹</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>是否集采</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in YPJson" :tabindex="$index" ref="list"
                                    :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                                    @mouseenter="hoverMouse1(true,$index)" @mouseleave="hoverMouse1()"
                                    @click="checkSelect([$index,'some','YPJson'],$event)"
                                    @dblclick="addCfMb($index)">
                                    <td>
                                        <div class="zui-table-cell cell-m">
                                            <input-checkbox @result="reCheckBox" :list="'YPJson'" :type="'some'"
                                                            :which="$index" :val="isChecked[$index]">
                                            </input-checkbox>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="$index+1"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l text-over-2" v-text="item.ypmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l " v-text="item.ypgg"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l " v-text="item.yptm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l " v-text="item.cdmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="item.kcsl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="item.yfdwmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="item.yplj"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="item.ybtclbmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s text-over-2" v-text="item.yp47"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <!--<p v-if="yzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--历史处方-->
        <div v-show="lsShow" class="ksys-side hzgl-height flex-container flex-dir-c">
            <div class="col-x-12 flex-one">
                <div class="col-x-5 hzgl-wiwidth-one flex-one flex-container" style="width: 50.3%;">
                    <div class="zui-table-view over-auto zui-item padd-r-10 padd-l-10" >
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>诊断</span></div>
                                    </th>
                                    <!-- <th>
                                         <div class="zui-table-cell cell-l"><span>处方日期</span></div>
                                     </th> -->
									 <th>
									     <div class="zui-table-cell cell-s"><span>类型</span></div>
									 </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>医生</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                                    </th>

                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in lsCfList" :tabindex="$index" ref="list"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                    @click="getLsTemMxData($index)"
                                    @dblclick="addLsCfMb($index)">
                                    <td>
                                        <div class="zui-table-cell text-over-2 cell-s"
                                             :class="item.zfbz==1?'jizheng':''" v-text="item.lczd"></div>
                                    </td>
                                    <!-- <td>
                                        <div class="zui-table-cell cell-l" :class="item.zfbz==1?'jizheng':''" v-text="fDate((item.cfrq,'formatTime'))"> </div>
                                    </td> -->
									<td>
									    <div class="zui-table-cell cell-s" :class="item.zfbz==1?'jizheng':''"
									         v-text="item.mtbzbm && item.cflx =='01'?'门特处方':item.cflxmc"></div>
									</td>
                                    <td>
                                        <div class="zui-table-cell text-over-2 cell-s"
                                             :class="item.zfbz==1?'jizheng':''" v-text="item.brxm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" :class="item.zfbz==1?'jizheng':''"
                                             v-text="item.cfysxm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l" :class="item.zfbz==1?'jizheng':''"
                                             v-text="item.bah"></div>
                                    </td>

                                </tr>
                                </tbody>
                            </table>
                            <!--<p v-if="yzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </div>
                    </div>
                </div>
                <div class="col-x-7 margin-l-10 hzgl-wiwidth-two" style="width: 48%;">
                    <div class="zui-table-view">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m">
                                            <input-checkbox @result="reCheckBox" :list="'lsCfMxList'" :type="'all'"
                                                            :val="isCheckAll">
                                            </input-checkbox>
                                        </div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-l text-left"><span>名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>用药剂量</span></div>
                                    </th>

                                    <th>
                                        <div class="zui-table-cell cell-s"><span>规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>用药开始日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>用药结束日期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>库存</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单价</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in lsCfMxList" :tabindex="$index"
                                    :class="[{'table-hovers':$index === activeIndex1,'table-hover':$index === hoverIndex1}]"
                                    @mouseenter="hoverMouse1(true,$index)" @click="activeMousel($index)"
                                    @mouseleave="hoverMouse1()">
                                    <td>
                                        <div class="zui-table-cell cell-m">
                                            <input-checkbox @result="reCheckBox" :list="'lsCfMxList'" :type="'some'"
                                                            :which="$index" :val="isChecked[$index]">
                                            </input-checkbox>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-l text-left" v-text="item.ypmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yyjl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.ypgg"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="fDate(item.mtksrq,'date')"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="fDate(item.mtjsrq,'date')"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.kcsl+item.yfdwmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yplj"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--诊断处方-->
        <div v-if="zdShow" class="ksys-side hzgl-height flex-container">
            <div class="col-x-12 flex-one">
                <div class="  flex-one flex-container">
                    <div class="zui-table-view over-auto zui-item " v-cloak>
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl text-left"><span>诊断名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>拼音代码</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>类型</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>人员</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>科室</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in lczdJsonList" :tabindex="$index" ref="list"
                                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                    @click="checkSelect([$index,'one','lczdJsonList'],$event)">
                                    <td>
                                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell text-over-2 cell-xl text-left"
                                             v-text="item.zdmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s " v-text="item.pydm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="zhyzlx_tran[item.lx]"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.ryxm"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.ksmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s">
                                                <span class="flex-center padd-t-5">
                                                    <em class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑"
                                                                           @click="bjEdit($index)"></i></em>
                                                    <em class="width30"><i class="icon-sc" data-title="删除"
                                                                           @click="remove($index)"></i></em>
                                                </span>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <!--<p v-if="yzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="mtch" class="ksys-side hzgl-height flex-container">
            <div class="col-x-12 ">
                <div class="   flex-container">
                    <div class="zui-table-view">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th>
                                        <div class="zui-table-cell cell-m"><span>序号</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-m"><span>选项</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-xl text-left"><span>方案名称</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>规格</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>用法</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>单量</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>周期</span></div>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell cell-s"><span>病种</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                <tr v-for="(item, $index) in MtcfJsonList" :tabindex="$index" ref="list" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]" @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'one','MtcfJsonList'],$event)">
                                    <td><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                                    <td><input-checkbox @result="reCheckBox" :list="'MtcfJsonList'" :type="'some'" :which="$index" :val="isChecked[$index]"></input-checkbox></td>
                                    <td>
                                        <div class="zui-table-cell  cell-xl text-left" v-text="item.zlorypmc"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s " v-text="item.gg"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.aka073"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.dl"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yka368"></div>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-s" v-text="item.yka027"></div>
                                    </td>

                                </tr>
                                </tbody>
                            </table>
                            <!--<p v-if="yzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="ksys-btn addList">
            <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button v-waves class="zui-btn btn-primary xmzb-db" @click="save(saveTitle)" v-text="saveTitle"></button>
        </div>

    </div>

    <!--弹窗展示另存模板和处方诊断 -->
    <div id="pop" class="pophide" :class="{'show':popShow}">
        <div class="pop-width460 bcsz-layer"
             style="height: max-content;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative">
            <div class="layui-layer-title padd-l-20" >
                <span class="dzcf-fl" v-text="bcTitle"></span>
                <span class="dzcf-fr closex ti-close" @click="cancel"></span>
            </div>
            <div class="layui-layer-content">
                <div class=" layui-mad layui-height">
                    <div class="flex-container  flex-align-c flex-wrap-w" v-if="bcShow">
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <label class="padd-r-5">医嘱名称</label>
                            <input class="zui-input wh150" placeholder="请输入医嘱名称" type="text"
                                   v-model="mbZhyzContent.zhyzmc"
                                   @keydown="nextFocus($event)" data-notEmpty="false"
                                   @blur="setPYDM(mbZhyzContent.zhyzmc,'mbZhyzContent','pydm')"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                            <label class="padd-r-5">拼音代码</label>
                            <input class="zui-input wh150" placeholder="请输入模板名称" type="text"
                                   v-model="mbZhyzContent.pydm"
                                   @keydown="nextFocus($event)" data-notEmpty="false" disabled="disabled"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <label class="padd-r-5">医嘱类型</label>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="zhyzlx_tran"
                                          :index="mbZhyzContent.zhyzlx"
                                          :val="mbZhyzContent.zhyzlx" :name="'mbZhyzContent.zhyzlx'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                            <label class="padd-r-5">是否草药</label>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="istrue_tran"
                                          :index="mbZhyzContent.sfcy"
                                          :val="mbZhyzContent.sfcy" :name="'mbZhyzContent.sfcy'">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <label class="padd-r-5">中医用法</label>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="yysmList"
                                          :index="'yysmmc'"
                                          :index_val="'yysmbm'" :val="mbZhyzContent.zyyf"
                                          :name="'mbZhyzContent.zyyf'"
                                          :search="true">
                            </select-input>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                            <label class="padd-r-5" style="left: 35px;">治&emsp;&emsp;法</label>
                            <input class="zui-input wh150 " placeholder="请输入治法" type="text"
                                   v-model="mbZhyzContent.zf"
                                   @keydown="nextFocus($event)" data-notEmpty="false"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <label class="padd-r-5" style="left: 35px;">主&emsp;&emsp;治</label>
                            <input class="zui-input wh150 " placeholder="请输入请输入主治" type="text"
                                   v-model="mbZhyzContent.zz"
                                   @keyup.13="saveMb()" data-notEmpty="false"/>
                        </div>
                    </div>
                    <div class="flex-container  flex-align-c flex-wrap-w" v-if="!bcShow">
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                            <label class="padd-r-5">诊断名称</label>
                            <input class="zui-input wh150" placeholder="请输入请输入医嘱名称" type="text"
                                   v-model="lczdPopContent.zdmc"
                                   data-notEmpty="false" @keydown="nextFocus($event)"
                                   @blur="setPYDM(lczdPopContent.zdmc,'lczdPopContent','pydm')"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                            <label class="padd-r-5">拼音代码</label>
                            <input class="zui-input wh150" type="text" v-model="lczdPopContent.pydm"
                                   @keydown="nextFocus($event)"
                                   data-notEmpty="false"/>
                        </div>
                        <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10">
                            <label class="padd-r-5">类&emsp;&emsp;型</label>
                            <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="zhyzlx_tran"
                                          :index="lczdPopContent.lx"
                                          :val="lczdPopContent.lx" :name="'lczdPopContent.lx'">
                            </select-input>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zui-row buttonbox">
                <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="saveMb">确定</button>
            </div>
        </div>

    </div>

    <!--弹窗展示毒麻精神类处方通知书 -->
    <div id="dmPop" class="pophide" :class="{'show':popShow}">
        <div class="pop-width460 bcsz-layer" :class="{'pop-width800':popShow}"
             style="height: max-content;padding-bottom: 20px;margin: 80px auto auto auto;background: #fff; position: relative">
            <div class="layui-layer-title  padd-l-20">
                <span class="dzcf-fl" v-text="popTitle"></span>
                <span class="dzcf-fr closex ti-close" @click="cancel"></span>
            </div>
            <div class="layui-layer-content padd-b-10 padd-l-10 padd-r-10 padd-t-10">
                <p class="font-18 font-weight">医师开具麻醉药品、精神药品处方提示</p>
                <div class="text-left padd-b-10">
                    1、<span>开具麻醉药品、精神药品使用专用处方:麻醉药品和第一类精神药品处方的印刷用纸为 淡红色 ，处方右上角分别标注“ 麻”、“精一”；第二类精神药品处方的印刷用纸为白色 ，处方右上角标注“精二”。</span>
                </div>
                <div class="text-left padd-b-10">
                    2、<span>具有处方权的医师在为门（急）诊癌症疼痛患者和中、重度慢性疼痛患者需长期使用麻醉药品和第一类精神药品的，首诊医师应当亲自诊查患者，为其建立相应的病历，留存患者身份证复印件，要求其签署《知情同意书》病历由医疗机构保管。</span></div>
                <div class="text-left padd-b-10">
                    3、<span>麻醉药品 注射剂仅限于医疗机构内使用 ，或者由医疗机构派医务人员出诊至患者家中使用。</span></div>
                <div class="text-left padd-b-10">
                    4、<span>医疗机构应当要求使用麻醉药品非注射剂型和第一类精神药品的患者每4个月复诊或者随诊一次。</span></div>
                <div class="text-left padd-b-10">
                    5、<span>麻醉药品非注射剂型和第一类精神药品需要带出医疗机构外使用时，具有处方权的医师在患者或者其代办人出示下列材料后方可开具麻醉药品、第一类精神药品处方：</span></div>
                <div class="text-left padd-b-10">
                    <span>（1）二级以上医院开具的诊断证明；</span></div>
                <div class="text-left padd-b-10">
                    <span>（2）患者户籍簿、身份证或者其他相关身份证明；</span></div>
                <div class="text-left padd-b-10">
                    <span>（3）代办人员的身份证明。</span></div>
                <div class="text-left padd-b-10">
                    <span>医疗机构应当在患者门诊病历中留存代办人员身份证明复印件。</span></div>
                <div class="text-left padd-b-10">
                    6、<span>麻醉药品、第一类精神药品注射剂处方为一次用量：其他剂型处方不得超过3日用量：控缓释制剂处方不得超过7日用量。</span></div>
                <div class="text-left padd-b-10">
                    7、<span>第二类精神药品处方一般不得超过7日用量：用于某些特殊情况，处方用量可适当延长，但医师应当注明理由。</span></div>
                <div class="text-left padd-b-10"><span>8、为癌痛、慢性中重度非癌痛患者开具的麻醉药品、第一类精神药品注射剂处方不得超过3日用量；其他剂型处方不得超过7日用量。</span></div>
                <div class="text-left padd-b-10"><span>9、对于需要特别加强管制的麻醉药品，盐酸二氢埃托啡处方为一次用量，药品仅限于二级以上医院使用；盐酸哌替啶处方为一次用量，药品仅限于医疗机构使用。</span></div>
                <div class="text-left padd-b-10"><span>10、为住院患者开具的麻醉药品和第一类精神药品处方应当逐日开具，每张处方为一日用量。</span></div>
            </div>
            <div class="zui-row buttonbox">
                <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="print">打印知情同意书</button>
            </div>
        </div>

    </div>

    <!--毒麻精神类处方代办人信息弹窗-->

    <div id="dbrPop" class="pophide" :class="{'show':popShow}">
        <div class="pop-width520 bcsz-layer"
             style="height: max-content;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative">
            <div class="layui-layer-title " style="padding: 0 0 0 20px;">
                <span class="dzcf-fl" v-text="popTitle"></span>
                <span class="dzcf-fr closex ti-close" @click="cancel"></span>
            </div>
            <div class="layui-layer-content">
                <div class=" layui-mad layui-height">
                    <div class="jbxx margin-top-15">
                        <div class="jbxx-size">
                            <div class="jbxx-position">
                                <span class="jbxx-top"></span>
                                <span class="jbxx-text">本人信息</span>
                                <span class="jbxx-bottom"></span>
                            </div>
                            <div class="zui-form padd-t-15 grid-box">
                                <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10  float-left" style="width: 53%">
                                    <label class="padd-r-5">姓&emsp;&emsp;名</label>
                                    <input class="zui-input wh150" disabled="true" placeholder="请输入姓名" type="text"
                                           v-model="dmbrxx.brxm"
                                           @keydown="nextFocus($event)" data-notEmpty="false"/>
                                </div>
                                <div class="flex-container flex-jus-c flex-align-c padd-b-10 float-left" style="width: 45%">
                                    <label class="padd-r-5">联系电话</label>
                                    <input class="zui-input wh150"  placeholder="请输入联系电话" type="text"
                                           v-model="dmbrxx.sjhm" @keydown="nextFocus($event)"
                                           onkeyup="value=value.replace(/[^\d]/g,'')" data-notEmpty="false"/>
                                </div>
                                <div class="flex-container flex-jus-c flex-align-c padd-b-10 float-left" style="width: 98%">
                                    <label class="padd-r-5" style="width: 13%">身份证号</label>
                                    <input class="zui-input " style="width: 83%" placeholder="请输入身份证号" type="text"
                                           v-model="dmbrxx.sfzjhm"
                                           @keydown="nextFocus($event)" data-notEmpty="false"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="jbxx margin-top-15">
                        <div class="jbxx-size">
                            <div class="jbxx-position">
                                <span class="jbxx-top"></span>
                                <span class="jbxx-text">代办人信息</span>
                                <span class="jbxx-bottom"></span>
                            </div>
                            <div class="zui-form padd-t-15 grid-box">
                                <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10 float-left" style="width: 53%">
                                    <label class="padd-r-5">姓&emsp;&emsp;名</label>
                                    <input class="zui-input wh150" placeholder="请输入姓名" type="text"
                                           v-model="dbrxx.xm"
                                           @keydown="nextFocus($event)" data-notEmpty="false"/>
                                </div>
                                <div class="flex-container flex-jus-c flex-align-c padd-b-10 float-left" style="width: 45%">
                                    <label class="padd-r-5">联系电话</label>
                                    <input class="zui-input wh150" placeholder="请输入联系电话" type="text"
                                           v-model="dbrxx.lxdh" @keydown="nextFocus($event)"
                                           onkeyup="value=value.replace(/[^\d]/g,'')" data-notEmpty="false"/>
                                </div>
                                <div class="flex-container flex-jus-c flex-align-c padd-b-10 float-left" style="width: 98%">
                                    <label class="padd-r-5" style="width: 13%">身份证号</label>
                                    <input class="zui-input " style="width: 83%" placeholder="请输入身份证号" type="text"
                                           v-model="dbrxx.sfzh"
                                           @keydown="nextFocus($event)" data-notEmpty="false"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zui-row buttonbox">
                <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                <button  v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="saveDbrxx">确定</button>
            </div>
        </div>
    </div>
<!--血透信息弹窗-->

    <div id="xtxxPop" class="pophide" :class="{'show':popShow}">
        <div class="pop-width520 bcsz-layer"
             style="height: max-content;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative;bottom: 7rem;">
            <div class="layui-layer-title " style="padding: 0 0 0 20px;">
                <span class="dzcf-fl" v-text="popTitle"></span>
                <span class="dzcf-fr closex ti-close" @click="cancel"></span>
            </div>
            <div class="layui-layer-content">
                <div class=" layui-mad layui-height">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class=" cell-xl text-center">
                                        <div class="zui-table-cell  cell-xl"><span>项目名称</span></div>
                                    </th>
                                    <th class=" cell-xl text-center">
                                        <div class="zui-table-cell  cell-xl"><span>项目值</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body zuiTableBodyHzlist" @scroll="scrollTable($event)">
                            <table class="zui-table">
                                <tbody>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    乙肝HBsAg
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hpttsHbsag"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    乙肝HBs
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hpttsHbs"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    乙肝HBeAg
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hpttsHbeag"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    乙肝HBe
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hpttsHbe"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    乙肝HBc
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hpttsHbc"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   乙肝DNA
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hpttsDna"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    丙肝病毒标志物
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   
											   <input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hcvVsmkr"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   丙肝RNA
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   
											   <input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hcvRna"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    梅毒确证实验
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												 <input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.sphlisCftyEpt"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    梅毒抗体
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.sphlisAb"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    Hiv感染确证试验
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.hivCftyEpt"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    血红蛋白（HB）
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="" type="number" v-model="xytxparam.hb"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    尿素氮溢出率（URR）
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   
											   <input class="zui-input" autocomplete="off" placeholder="" type="number" v-model="xytxparam.urr"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    尿素清除指数（Kt/V）
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="" type="number" v-model="xytxparam.ktv"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    血钙浓度
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   
											   <input class="zui-input" autocomplete="off" placeholder="" type="number" v-model="xytxparam.bcct"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    血磷浓度
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="" type="number" v-model="xytxparam.bpct"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    血清铁蛋白（sFe）
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="" type="number" v-model="xytxparam.sfe"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    血清铁蛋白ug/L
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.ugl"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    血清铁蛋白ng/ml
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    
												<input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.ngml"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    甲状旁腺激素
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   
											   <input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.pth"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    乙肝DNA*100IU/ml
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   
											   <input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.iuml"  @keydown.enter="nextFocus($event)"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<div class=" zui-table-cell  cell-xl">
											    乙肝DNACopies/ml
											</div>
										</td>
										<td>
											<div class=" zui-table-cell  cell-xl">
											   
											   <input class="zui-input" autocomplete="off" placeholder="0 无 1 有" type="number" v-model="xytxparam.csml"  />
											</div>
										</td>
									</tr>
                                </tbody>
                            </table>
                        </div>
                    
                </div>
            </div>
            <div class="zui-row buttonbox">
                <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                <button  v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="saveXtxx">确定</button>
            </div>
        </div>
    </div>

        <div id="historyPop" class="pophide" :class="{'show':popShow}">
            <div class="pop-width520 bcsz-layer"
                 style="height: max-content;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative;bottom: 7rem;width:600px;">
                <div class="layui-layer-title " style="padding: 0 0 0 20px;">
                    <span class="dzcf-fl" v-text="popTitle"></span>
                    <span class="dzcf-fr closex ti-close" @click="cancel"></span>
                </div>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height">


                        <div class="zui-table-view  bqcydj_model_width hzList-border flex-container flex-dir-c">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50">
                                <thead>
                                <tr>
                                    <th class=" cell-xl text-center">
                                        <div class="zui-table-cell  cell-xl"><span>药品名称</span></div>
                                    </th>
                                    <th class=" cell-m text-center">
                                        <div class="zui-table-cell  cell-m"><span>药品数量</span></div>
                                    </th>
                                    <th class=" cell-l text-center">
                                        <div class="zui-table-cell  cell-l"><span>单价</span></div>
                                    </th>
                                    <th class=" cell-l text-center">
                                        <div class="zui-table-cell  cell-l"><span>明细费用总额</span></div>
                                    </th>
                                    <th class=" cell-m text-center">
                                        <div class="zui-table-cell  cell-m"><span>收费项目登记</span></div>
                                    </th>
                                    <th class=" cell-m text-center">
                                        <div class="zui-table-cell  cell-m"><span>自付比例</span></div>
                                    </th>
                                    <th class=" cell-l text-center">
                                        <div class="zui-table-cell  cell-l"><span>全自费金额</span></div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body zuiTableBodyHzlist" @scroll="scrollTable($event)">
                            <table class="zui-table">
                                <tbody>
                                <tr v-for="(item, $index) in fyxx">
                                     <td class=" cell-xl text-center">
                                        <div class="zui-table-cell  cell-xl"><span>{{item.medins_list_name}}</span></div>
                                    </td>
                                    <td class=" cell-m text-center">
                                        <div class="zui-table-cell  cell-m"><span>{{item.cnt}}</span></div>
                                    </td>
                                    <td class=" cell-l text-center">
                                        <div class="zui-table-cell  cell-l"><span>{{item.pric}}</span></div>
                                    </td>
                                    <td class=" cell-l text-center">
                                        <div class="zui-table-cell  cell-l"><span>{{item.det_item_fee_sumamt}}</span></div>
                                    </td>
                                    <td class=" cell-m text-center">
                                        <div class="zui-table-cell  cell-m"><span>{{sfxmdj[item.chrgitm_lv]}}</span></div>
                                    </td>
                                    <td class=" cell-m text-center">
                                        <div class="zui-table-cell  cell-m"><span>{{item.selfpay_prop}}</span></div>
                                    </td>
                                    <td class=" cell-l text-center">
                                        <div class="zui-table-cell  cell-l"><span>{{item.fulamt_ownpay_amt}}</span></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        </div>
                    </div>
                </div>
                <div class="zui-row buttonbox">
                    <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">关闭</button>
                </div>
            </div>
        </div>





        <model :s="'保存'" :c="'退出'" class="fjzdPop" @default-click="saveData" @result-clear="fjzdShow=false"
               :model-show="true" @result-close="fjzdShow=false" v-if="fjzdShow" :title="'附加诊断'">
            <div class="zui-table-view  bqcydj_model_width hzList-border flex-container flex-dir-c">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">序号</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">诊断名称</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">诊断编码</div>
                            </th>
                        </tr>
                        <tr @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()" v-for="(item, $index) in fjzd"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <span v-text="$index+1"></span>
                                </div>
                            </td>
                            <td class="">
                                <div class="zui-table-cell flex-container flex-align-c">
                                    <input class="zui-input height-input wh100MAx" data-notEmpty="false" v-model="item.jbmc"
                                           @keydown="changeDown($event,$index,'jbmc','jbmb','qtzd')"
                                           @input="change(false,$index,'jbmc','jbbm',$event.target.value,'jbbm')">
                                    <search-table :message="searchCon" :selected="selSearch" :page="queryObj" :them="them"
                                                  @click-one="checkedOneOut"
                                                  @click-two="checkedTwoOut">
                                    </search-table>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    {{item.jbmb}}
                                </div>
                            </td>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <button v-waves slot="footer" class="tong-btn btn-parmary  xmzb-db margin-r-10 sizewidth88 font-auto" @click="addFun()">新增一行</button>
        </model>
</div>

<!--添加-->
<div class="cqyzd yzBoxSide" id="cqyzd" >
       <div v-if="syShow && !FRorWindow">
           <div class="yzdTitle">门诊治疗单</div>
           <div class="yzd-brInfo flex-container flex-jus-c padd-b-10">
               <div>
                   <span>姓名:</span>
                   <span>{{printList.brxm}}</span>
               </div>
               <div>
                   <span>性别:</span>
                   <span>{{brxb_tran[printList.brxb]}}</span>
               </div>
               <div>
                   <span>年龄:</span>
                   <span>{{printList.brnl}}{{nldw_tran[printList.nldw]}}</span>
               </div>
               <div>
                   <span>就诊时间:</span>
                   <span>{{fDate(printList.cfrq,'AllDate')}}</span>
               </div>
               <div>
                   <span>地址:</span>
                   <span>{{printList.jzdmc}}</span>
               </div>
           </div>

           <div class="yzd-table">
               <table cellspacing="0" cellpadding="0" style="width: 760px">
                   <tr>
                       <th rowspan="1" colspan="5" style="width: 50%">医嘱起始</th>
                       <th colspan="7" rowspan="2" style="width: 300px">医嘱执行</th>
                   </tr>
                   <tr>
                       <th colspan="5">药名(剂量、用法)</th>
                   </tr>
                   <tr v-for="(item, $index) in printYist">
                       <td v-if="item.ypmc == null" colspan="1" class="yzd-table-blank">
                           <span>医师签名</span><span></span>
                       </td>
                       <td v-if="item.ypmc == null" colspan="1" class="yzd-table-blank">
                           <span>配药签名</span><span></span>
                       </td>
                       <td v-if="item.ypmc == null" colspan="1" class="yzd-table-blank">
                           <span>核对签名</span><span></span>
                       </td>
                       <td v-if="item.ypmc == null" colspan="1" class="yzd-table-blank">
                           <span>执行时间</span><span></span>
                       </td>
                       <td v-if="item.ypmc == null" colspan="1" class="yzd-table-blank">
                           <span>执行者签名</span><span></span>
                       </td>
                       <!--                class="flex-container"-->
                       <td v-if="item.ypmc != null" colspan="5">
                           <span class="yzd-name">{{item.ypmc}}&nbsp;{{item.yyjl}}{{item.jldwmc}} {{item.sysd}} {{item.sysddw}}</span>
                           <span :class="[{'sameStart': sameSE($index) == 'start'},{'sameEnd': sameSE($index) == 'end'},{'same': sameSE($index) == 'all'}]"></span>
                           <p>
                               <span class="yzd-sm" v-text="item.yypcmc"></span>
                               <span v-show="!isShowItem($index)"  class="padd-r-20" v-show="item.yyffmc == '皮试'">结果(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{psjg2_tran[item.psjg]}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span>
                               <span v-show="isShowItem($index)"  class="padd-r-20" v-show="item.yyffmc != '皮试' " v-text="setMC(item)"></span>
                               <span  class="padd-r-20" v-show="item.yyffmc != '皮试' " >{{item.yyts}}天</span>
                           </p>
                       </td>
                       <td class="yzd-table-blank">
                           <span>时间</span>
                           <span>签字</span>
                       </td>
                       <td class="yzd-table-blank"><span></span><span></span></td>
                       <td class="yzd-table-blank"><span></span><span></span></td>
                       <td class="yzd-table-blank"><span></span><span></span></td>
                       <td class="yzd-table-blank"><span></span><span></span></td>
                       <td class="yzd-table-blank"><span></span><span></span></td>
                       <td class="yzd-table-blank"><span></span><span></span></td>
                   </tr>
               </table>
           </div>

           <div class="ysDiv">
               <div class="hzqm  padd-t-10" style="width: 760px">
                   <div class="flex-container flex-jus-sp flex-align-c">
                       <div class="hzjsqm">病情记录：</div>
                       <div class="hzjsqm">医生护士签名：</div>
                   </div>
                   <div class="flex-container  flex-jus-sp flex-align-c">
                       <div class="hzjsqm">患者(家属)签名：</div>
                       <div class="hzage ">年龄：&emsp;&emsp;(<i>岁</i>)</div>
                       <div class="qmryhzgx">签名人与患者关系：</div>
                   </div>
                   <div class="flex-container flex-jus-sp flex-align-c">
                       <div class="hzjsqm">经治医生签名：</div>
                       <div class="qmryhzgx flex-container">时间：<i class="timeAge padd-l-30 padd-r-30">年</i><i class="timeAge  padd-l-30">月</i><i class="timeAge  padd-l-30">日</i></div>
                   </div>
               </div>
           </div>
       </div>
    <div v-if="syShow && FRorWindow=='1'">
        <div class="zhcxx" v-for="(a, b ) in x">
            <div class="row zhcx" id="zhcx" >
                <header>
                    <h3>临时治疗单<div class="qmryhzgx">日期：<i class="timeAge" style="min-width: 42px;">年</i><i class="timeAge" style="min-width: 30px;">月</i><i class="timeAge" style="min-width: 30px;">日</i></div></h3>
                    <div class="header">
                        <p class="left-xb">姓名：<i v-text="printList.brxm"></i></p>
                        <p class="center-xb" >性别：<i v-text="brxb_tran[printList.brxb]"></i></p>
                        <p class="right-nl">年龄：<i v-text="printList.brnl"></i><i v-text="nldw_tran[printList.nldw]"></i>
                        </p>
                    </div>
                    <div class="header">
                        <p class="left-dz">地址：<i v-text="printList.jzdmc"></i></p>
                        <p class="center-jz">
                            就诊时间：<i v-text="fDate(printList.cfrq,'AllDate')"></i></p>
                    </div>
                    <div class="bqzy">
                        <div style="width: 100%;overflow: hidden">病情摘要：</div>
                        <div class="bqzytext" v-text="printList.zyzztz" style="height:70px;width: 100%;overflow: hidden"></div>
                        <!--<i style="float: left;">病情摘要：</i>-->
                        <!--<div class="text bqzytext" contenteditable="true" v-text="printList.zyzztz"></div>-->
                        <!--<p class="ysqm">-->
                        <!--医师签名：<i></i></p>-->
                        <div class="ysqms" style="text-align: right;padding-right: 100px;">医师签名：</div>
                    </div>
                    <div class="zdxx">
                        <div class="left-text">
                            诊断
                        </div>
                        <div class="zdxx_child"  contenteditable="true" v-text="printList.lczd"></div>
                        <div class="zdxx_child" style="border-right:none;" contenteditable="true" v-text="printList.qtzdmc"></div>
                    </div>
                    <div class="psqz">
                        <div class="center-text">
                            <div><span class="qz">皮试（）签字：</span><span style="float: right;">时间：
                                <i class="timeAge">时</i>
                                <i class="timeAge">分</i>
                                <i class="timeAge">秒</i>

                        	</span>
                            </div>

                            <div><span class="qz">皮试（）签字：</span>
                                <span style="float: right;">时间：
                        		<i class="timeAge">时</i>
                                <i class="timeAge">分</i>
                                <i class="timeAge">秒</i>
                        	</span>
                            </div>
                        </div>
                    </div>
                </header>
                <main>
                    <div class="p-box">
                        <div class="header-content">
                            <div class="left-yp">
                                <span class="ri">日</span>
                                <span class="yue">月</span>
                            </div>
                            <div class="left-two-yp">
                                时间
                            </div>
                            <div class="left-two-text">
                                治疗计划
                            </div>
                            <div class="right-two-text">医师签名</div>
                            <div class="right-two-text">配药签名</div>
                            <div class="right-two-text">核对签名</div>
                            <div class="right-two-text">执行时间</div>
                            <div class="right-two-text" style="width:50px;border-right:none;">执行者签名</div>
                        </div>
                        <div class="header-text" v-for="(item,index) in 5" :data-id=" b * 5 +index" :key=" b * 5 +index">
                            <div class="text-rq"></div>
                            <div class="left-two-yp"></div>
                            <div class="left-two-text">
                                <p v-for="(list,index) in printItarr[b * 5 +index]" style="width: 100%; padding: 0;line-height:16px;float: left;margin:0;" :data-text="JSON.stringify(list)" :data-index="JSON.stringify(printItarr[index])">
                                    <i style=" width: 100%;font-size: 12px; text-align: left;
    padding: 0 5px;font-style: normal;display: block;">{{list.ypmc}}&nbsp;{{list.yyjl}}{{list.jldwmc}}</i>
                                </p>
                                <p style="width: 100%; padding: 0;line-height:16px;float: left;margin:0;" v-for="(list,index) in printItarr[b * 5 +index]">
                                    <i style=" width: 100%;font-size: 12px;
    text-align: left;
    padding: 0 5px;font-style: normal;display: block; font-weight: 600;" v-if="index==0">{{list.yyffmc}}&nbsp;{{list.yypcmc}}&nbsp;{{list.yysm}}</i>
                                </p>

                            </div>
                            <div class="right-two-text right-display"></div>
                            <div class="right-two-text right-display"></div>
                            <div class="right-two-text right-display"></div>
                            <div class="right-two-text right-display"></div>
                            <div class="right-two-text right-display" style="width:50px;border-right:none;"></div>
                        </div>

                        <div class="gcjl">
                            <i style="float:left;">观察记录：</i>
                            <div class="gctext" contenteditable="true"></div>
                            <div class="yshsqm" >医师(护士)签名：</div>
                        </div>
                        <div class="hzqm">
                            <div class="hzjsqm">患者(家属)签名：
                            </div>
                            <div class="hzage">年龄：&nbsp;&nbsp;(<i>岁</i>)
                            </div>
                            <div class="qmryhzgx">签名人与患者关系：
                            </div>
                            <div class="hzjsqm" >经治医生签名：
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
</div>
</div>

<script type="text/javascript" src="/newzui/pub/PassJs/McLoader.js"></script>
<script type="text/javascript" src="/newzui/pub/PassJs/McConfig.js"></script>
<script type="text/javascript" src="/newzui/pub/PassJs/McJsAll.js"></script>
<script type="text/javascript" src="/newzui/pub/PassJs/McPassIm.js"></script>
<script type="application/javascript" src="/newzui/pub/js/insuranceGbUtils.js"></script>
<script type="text/javascript" src="brPage/dzcf.js"></script>

</body>

</html>
