var wrapper = new Vue(
		{
			el : '.panel',
			mixins : [ dic_transform, baseFunc, tableBase, mformat ],
			data : {
				isShowpopL : false,
				isTabelShow : false,
				isShowkd : true,
				isShow : false,
                AudShow:true,
                zdyxm:'',
	            zdrq: getTodayDateTime(), //获取制单日期
	            jyinput: false, //禁用输入框
				keyWord : '',
				popContent : {},
				rkContent : {},
				bsdContent : {
					ckfs : "03", // 报损方式
                    "zdrq": getTodayDateTime(),
					lyr : userId
				// 操作人
				},
				// 调拨单
                dbdContent : {
					'zdrq' : getTodayDate(),
					'kfbm' : null,
				},
				zdrq : getTodayDateTime(), // 获取制单日期
				zdyxm: '',
				jyinput: false, //禁用输入框
				TjShow : true,
				qxksbm : '',
				rkd : {}, // 入库单对象
				num : 0,
				param : {
					page : '',
					rows : '',
					total : ''
				},
				json : {},
				csParm : {},

				ShShow : false,
				dg : {
					page : 1,
					rows : 20,
					sort : "",
					order : "asc",
					parm : ""
				},
				isCheck : null,
				dateBegin : null,// getTodayDateBegin(),
				dateEnd : null,// getTodayDateEnd(),
				time : {
					test : 'hello!'
				},
				t : {},
				yfList : [],
				ryList : [],
				flag : false,
				added : false,
				lyks : null,
				tkyf : null,
				tkyfBM : null,
				lyr : null,
				ksbm : '',

				ckd:null,//页面传过来的数据-出库单号
				dbd:null,
				isNew : 0
			},
			watch:{
				'popContent.yfbm':function(){
					if(wrapper.popContent.yfbm == wrapper.popContent.dbyf){
						malert("调入、调出药房不能相同！");
						Vue.set(wrapper.popContent,'yfbm','');
					}
				},
				'popContent.dbyf':function(){
					if(wrapper.popContent.yfbm == wrapper.popContent.dbyf){
						malert("调入、调出药房不能相同！");
						Vue.set(wrapper.popContent,'dbyf','');
					}
				},


			},
			updated:function () {
                kd.getData();
            },
			methods : {

				initial: function(){
		        	//出库单号
                    // this.ckd = JSON.parse(decodeURI(this.getQueryVariable('item')));

                        // <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                        // <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData" v-show="TjShow">作废</button>
                        // <button class="tong-btn btn-parmary-f2a xmzb-db" @click="jujue" v-show="refuseShow">拒绝</button>
                        // <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow">提交</button>
                        // <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="refuseShow">审核</button>
                    this.ckd=JSON.parse( sessionStorage.getItem('dbglitem'));
					if(this.ckd==null){
                        wrapper.AudShow=true;
                        kd.TjShow=true;
                        kd.zfShow=false;
                        kd.mxShShow=true;
                        wrapper.jyinput=false;
                        var reg = /^[\'\"]+|[\'\"]+$/g;
                        wrapper.zdyxm=sessionStorage.getItem("userName"+userId).replace(reg,'');
					}else {
			        	if(this.ckd.ckdh){
			        		wrapper.isNew = 0;
			        		//查询调拨单和明细
				            $.getJSON("/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=query&bean="+JSON.stringify(this.ckd),
									function(json) {
										if (json.a == 0) {
											wrapper.dbd = json.d.list[0];//调拨单详情
											wrapper.getYFData(wrapper.dbd);
											console.log(this.dbd);
											pop.cal();
										}
				            });
			        	}else{
			        		wrapper.isNew = 1;//新开单
			        		wrapper.getYFData();
			        	}
			        	if(this.ckd.shzfbz == '0'){
			        		// kd.TjShow = false;//审核按钮
			        		kd.refuseShow = true;//审核按钮
							kd.TjShow=true;
							kd.zfShow=true;
							kd.mxShShow=true;
	                        wrapper.jyinput=false;
	                        kd.refuseShow=true;
	                        wrapper.zdyxm=this.ckd.zdrxm;
                			wrapper.zdrq=wrapper.fDate(this.ckd.zdrq,'date');
                			wrapper.popContent.yfbm=this.ckd.yfbm;
                			wrapper.popContent.dbyf=this.ckd.dbyf;
                			wrapper.popContent.bzms=this.ckd.bzms;
			        	}
			        	if(this.ckd.shzfbz == '1'||this.ckd.shzfbz == '2'){
                            wrapper.AudShow=false;
                            kd.TjShow=false;
                            kd.zfShow=false;
			        		kd.mxShShow=false;
	                        wrapper.jyinput=true;
	                        kd.refuseShow=false;
	                        kd.dyShow = true;
	                        wrapper.zdyxm=this.ckd.zdrxm;
                			wrapper.zdrq=wrapper.fDate(this.ckd.zdrq,'date');
                			wrapper.popContent.yfbm=this.ckd.yfbm;
                			wrapper.popContent.dbyf=this.ckd.dbyf;
                			wrapper.popContent.bzms=this.ckd.bzms;

			        	}
                    }

				},
				sx : function() {
					this.initial();
				},
				// 新增
				AddMdel : function() {
					kd.isUpdate = 0;
					pop.title = '添加药品'
					pop.open();
					pop.dbdContent = {};
					if(wrapper.isNew == 1){
						//新增
						Vue.set(pop.dbdContent, 'zdrq', getTodayDateTime());// 当前时间
						Vue.set(pop.dbdContent, 'zdr', pop.ryList[0].rybm)// 默认当前操作员-先为第一个
					}else{
						Vue.set(pop.dbdContent, 'zdrq', wrapper.dbdContent.zdrq);// 当前时间
						Vue.set(pop.dbdContent, 'zdr',wrapper.dbdContent.jbr);//经办人
					}
				},

				// 获取药房
				getYFData : function(item) {
					$.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yf",
									function(json) {
										if (json.a == 0) {
											wrapper.yfList = json.d.list;
										}
										$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=rybm',
														function(data) {
															pop.ryList = data.d.list;
																		});
														});
					if(item){
						Vue.set(wrapper.popContent,'yfbm',item.yfbm);//调出
						Vue.set(wrapper.popContent,'dbyf',item.dbyf);//调入
						Vue.set(wrapper.bsdContent,'zdrq',item.zdrq);//制单日期
						Vue.set(wrapper.bsdContent,'lyr',item.jbrmc);//制单人
						kd.getData(item.ckdh);
					}
				},
				resultChanges : function(val) {
					Vue.set(wrapper.popContent, 'yfbm', val[0]);
					Vue.set(wrapper.popContent, 'yfmc', val[4]);

					kd.getks();

				},
				resultChange : function(val) {
					Vue.set(wrapper.popContent, 'dbyf', val[0]);
					Vue.set(wrapper.popContent, 'dbyfmc', val[4]);
				},
			}

		});


	var kd = new Vue(
		{
			el : '.zui-table-view',
			mixins : [ dic_transform, baseFunc, tableBase, mformat ],
			data : {
				isShowpopL : false,
				isTabelShow : false,
				isShowkd : true,
				isShow : false,
				keyWord : '',
                refuseShow:false,
				bsdContent : {
					ckfs : "03", // 报损方式
					lyr : userId
				// 操作人
				},
				// 调拨单
				dbdContent : {
					'zdrq' : getTodayDate(),
					'kfbm' : null,
				},
				TjShow : true,
                zfShow:false,
                mxShShow:true,
				dyShow:false,
				jsonList : [],
				ylbm : 'N040030020022003',
				yfList : [],
				title : '',
				totle : '',
				qxksbm : '',
				num : 0,
				param : {
					page : 1,
					rows : 10,
					total : ''
				},
				ryList : [],
				zdy : userId,
				dbdList : [],

				ShShow : false,
				dg : {
					page : 1,
					rows : 20,
					sort : "",
					order : "asc",
					parm : ""
				},
				isCheck : null,
				thdDetail : [], // 退货单明细集合
				dateBegin : null,// getTodayDateBegin(),
				dateEnd : null,// getTodayDateEnd(),
				yfbm : null,
				ksbm : null,
				zhuangtai : {
					"0" : "待审核",
					"1" : "已审核",
					"2" : "作废",
				},
				queren : {
					"0" : "待确认",
					"1" : "确认"
				},

				added : false,
				json:{
					jjzj:0,
					ljzj:0
				},//价格总计
				cgryList : [], // 退库人员
				KSList : [],
				KFList : [],
				lyr : null,
				ghdwList : [],
				// 药品信息对象
				popContent : {},
				isSubmited : false,
				isUpdate : 0 ,
				modifyIndex : null,
			},
			updated:function () {
                kd.getData();
            },
			methods : {
				// 作废
				invalidData : function() {
					if(confirm("确认作废-"+wrapper.dbd.ckdh+"-调拨单吗?")){
	        			var obj = {
	            				ckdh : wrapper.dbd.ckdh
	            		};
	        			//作废调拨单
	        			this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=zf',
	        					JSON.stringify(obj))
	        				.then(function(data) {
	        					if(data.a == 0 || data.body.a == 0 ) {
	        						kd.topClosePage('page/yfgl/kcgl/dbgl/kaidan.html','page/yfgl/kcgl/dbgl/dbgl.html');
	        					} else {
	        						malert(data.body.c);
	        					}
	        				}, function(error) {
	        					console.log(error);
	        				});
	        		}
                    // this.topNewPage('调拨管理','page/yfgl/kcgl/dbgl/dbgl.html','?isgetData=1');

				},
				printDJ:function(){
					var reportlets ="[{reportlet: 'fpdy%2Fyfgl%2Fyfgl_dbckd.cpt',yljgbm:'"+jgbm+"',yfbm:'"+wrapper.popContent.yfbm+"',ckdh:'"+wrapper.ckd.ckdh+"'}]";
					if (!FrPrint(reportlets,null)){
						window.print();
					}
				},
				// 拒绝
				jujue : function() {
					malert('拒绝')
				},
				//获取明细
				getData: function(ckdh){
                    common.openloading('.zui-table-view')
					var bean = {
							ckdh : ckdh
					}
					$.getJSON("/actionDispatcher.do?reqUrl=YfbKcglDbgl&types=queryMx&bean="+JSON.stringify(bean),
							function(json) {
								if (json.a == 0) {
									kd.jsonList = json.d;
									console.log(kd.jsonList);
									pop.cal();
								}
					});
                    common.closeLoading()
				},
				//获取科室
				getks: function(){
					var yfbm = wrapper.popContent.yfbm;
					for (var i = 0; i < wrapper.yfList.length; i++) {
						if (yfbm == wrapper.yfList[i].yfbm) {
							wrapper.ksbm = wrapper.yfList[i].ksbm;
							break;
						}
					}
				},
				// 提交所有
				submitAll : function() {

					// 是否禁止提交
					if (this.isSubmited) {
						malert('数据提交中，请稍候！');
						return;
					}
					if (this.jsonList.length == 0) {
						malert('没有数据可以提交');
						return;
					}
					// 备注描述
					var bzms = pop.dbdContent.bzms;
					if(bzms == undefined || bzms == null || bzms == "") {
						Vue.set(pop.dbdContent,'bzms',"调拨管理");
					 };
					// 是否禁止提交
					this.isSubmited = true;
					// 准备参数
					kd.getks();
					Vue.set(pop.dbdContent,'lyks', wrapper.ksbm);
					if(wrapper.dbd){
						kd.$set(pop,'dbdContent',wrapper.dbd);
					}
					pop.dbdContent.bzms=wrapper.popContent.bzms;
					var json = {
						"list" : {
							"dbd" : pop.dbdContent,
							"dbdmx" : kd.jsonList
						}
					};
					// 发送请求保存数据
					this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=modify',
									JSON.stringify(json)).then(function(data) {
								if (data.body.a == 0) {
									kd.jsonList = [];
									malert("提交成功");
									// this.topNewPage('调拨管理','page/yfgl/kcgl/dbgl/dbgl.html','?isgetData=1');
                                    kd.topClosePage('page/yfgl/kcgl/dbgl/kaidan.html','page/yfgl/kcgl/dbgl/dbgl.html');

								} else {
									malert(data.c);
								}
								// 是否禁止提交
								kd.isSubmited = false;
							}, function(error) {
								// 是否禁止提交
								kd.isSubmited = false;
							});
				},
				// 审核
				passData : function() {
					if(!wrapper.dbd.ckdh == undefined) {
						malert('请选择调拨单！');
						return;
					}
					//设置审核标志
					wrapper.dbd.shzfbz = 1;
					wrapper.dbd.qrbz = 0;

					//方式请求审核调拨单
					this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglDbgl&types=updateDbd',
							JSON.stringify(wrapper.dbd))
						.then(function(data) {
							if(data.body.a == 0) {
								malert("审核成功");
								// this.topNewPage('调拨管理','page/yfgl/kcgl/dbgl/dbgl.html','?isgetData=1');
                                this.topClosePage('page/yfgl/kcgl/dbgl/kaidan.html','page/yfgl/kcgl/dbgl/dbgl.html');
							} else {
								malert(data.body.c);
							}
						}, function(error) {
							console.log(error);
						});
				},
				// 取消
				cancel : function() {
                    this.topClosePage('page/yfgl/kcgl/dbgl/kaidan.html','page/yfgl/kcgl/dbgl/dbgl.html');
				},
				// 编辑
				edit : function(index) {
					kd.isUpdate = 1;
					kd.modifyIndex = index;
					pop.popContent = JSON.parse(JSON.stringify(kd.jsonList[index]));
					pop.open();
					pop.title = "编辑药品";
					if(wrapper.isNew == 0){
						Vue.set(pop.dbdContent, 'zdrq', wrapper.dbd.zdrq);// 当前时间
						Vue.set(pop.dbdContent, 'zdr',wrapper.dbd.jbr);//经办人
					}

				},
				// 删除2018/07/06二次弹窗删除提示
				remove : function(index) {
                    if (common.openConfirm("确认删除该条信息吗？", function () {
                            kd.jsonList.splice(index, 1);
                        })) {
                        return false;
                    }
					// kd.jsonList.splice(index, 1);
				}
			}
		});

var pop = new Vue(
		{
			el : '#brzcList',
			mixins : [ dic_transform, baseFunc, tableBase, mformat ],
			components : {
				'search-table' : searchTable
			},
			data : {
				title : '',
				dbdContent : {},
				popContent : {},
				ryList : [],

				them_tran : {},
				them : {
					'生产批号' : 'scph',
					'药品编号' : 'ypbm',
					'药品名称' : 'ypmc',
					'库存数量' : 'ykkc',
					'有效期至' : 'yxqz',
					'规格' : 'ypgg',
					'分装比例' : 'fzbl',
					'进价' : 'ykjj',
					'零价' : 'yklj',
					'库房单位' : 'kfdwmc',
					'药房单位' : 'yfdwmc',
					'效期' : 'yxqz',
					'药品剂型' : 'jxmc'
				},

			},
			methods : {
				// 关闭
				closes : function() {
					$(".side-form").removeClass('side-form-bg')
					$(".side-form").addClass('ng-hide');

				},
				open : function() {
					$(".side-form-bg").addClass('side-form-bg')
					$(".side-form").removeClass('ng-hide');
					// 设置库房
				},
				// 药品名称下拉table检索数据
				changeDown : function(event, type) {
					var _searchEvent = $(event.target.nextElementSibling).eq(0);
					var isReq = this.keyCodeFunction(event, 'popContent',
							'searchCon');
					// 选中之后的回调操作
					if (window.event.keyCode == 13) {
						if (type == 'ypmc') {
							if (this.popContent.ypmc == undefined) {
								return;
							}
							this.nextFocus(event);
						} else if (type == 'bzsm') {
							this.add();
						} else if (type == 'cksl') {
							if (this.popContent.cksl > this.popContent.kcsl) {
								malert('库存不足！');
								return;
							} else {
								this.nextFocus(event);
							}
						}

					}

				},
				// 当输入值后才触发
				change : function(event, type) {

					if (wrapper.popContent.yfbm == undefined
							|| wrapper.popContent.yfbm == null
							| wrapper.popContent.yfbm == "") {
						malert("请先择药房!", 'top', 'defeadted');
						return;
					}
					var _searchEvent = $(event.target.nextElementSibling).eq(0);
					if (this.popContent[type] == undefined
							|| this.popContent[type] == null) {
						this.popContent.parm = "";
					} else {
						this.popContent.parm = this.popContent[type];
					}

					var dg = {
						page : 1,
						rows : 5,
						parm : this.popContent.parm
					}

					var json = {
						yfbm : wrapper.popContent.yfbm,
						// "yfbm":'02',
						ksbm : wrapper.ksbm,
						sfpdkcsl : 1
					};
					$.getJSON(
									'/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc'
											+ '&dg=' + JSON.stringify(dg)
											+ '&json=' + JSON.stringify(json),
									function(data) {
										for (var i = 0; i < data.d.list.length; i++) {
											data.d.list[i]['yxqz'] = formatTime(
													data.d.list[i]['yxqz'],
													'date');
											data.d.list[i]['scrq'] = formatTime(
													data.d.list[i]['scrq'],
													'date');
											// 判断可用库存数量
											data.d.list[i]['kykc'] = data.d.list[i]['kcsl']
													- (data.d.list[i]['wshcks'] == undefined ? 0
															: data.d.list[i]['wshcks']);
										}
										pop.searchCon = data.d.list;
										pop.total = data.d.total;
										pop.selSearch = 0;
										if (data.d.list.length != 0) {
											$(".selectGroup").hide();
											_searchEvent.show();
											return false;
										} else {
											$(".selectGroup").hide();
										}
									});
				},

				// 双击选中下拉table
				selectOne : function(item) {
					// 查询下页
					if (item == null) {

						this.dg.page++;
						var json = {
							"yfbm" : wrapper.dbdContent.yfbm,
							'ksbm' : this.qxksbm
						};
						$.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfpckc'
												+ '&dg='
												+ JSON.stringify(this.dg)
												+ '&json='
												+ JSON.stringify(json),
										function(data) {
											for (var i = 0; i < data.d.list.length; i++) {
												data.d.list[i]['yxqz'] = formatTime(
														data.d.list[i]['yxqz'],
														'date');
												data.d.list[i]['scrq'] = formatTime(
														data.d.list[i]['scrq'],
														'date');
												wrapper.searchCon
														.push(data.d.list[i]);
											}
											wrapper.total = data.d.total;
											wrapper.selSearch = 0;
										});
						return;
					}

					this.popContent = item;
					$(".selectGroup").hide();
				},
				// 单击选择
				checkOneMx : function(index) {
					this.index = index;
					this.isCheckMx = [];
					this.isCheckMx[index] = true;
				},
				// 新增
				add : function() {
					if (wrapper.popContent.yfbm == null|| wrapper.popContent.yfbm == undefined) {
						malert('请选择调出药房');
						return;
					}
					Vue.set(pop.dbdContent, 'yfbm', wrapper.popContent.yfbm);
					if (wrapper.popContent.dbyf == null|| wrapper.popContent.dbyf == undefined) {
						malert('请选择调入药房');
						return;
					}
					Vue.set(pop.dbdContent, 'dbyf', wrapper.popContent.dbyf);
					if (wrapper.popContent.yfbm == wrapper.popContent.dbyf) {
						malert('相同药房不能调拨');
						return;
					}
					if (this.dbdContent.zdr == null|| this.dbdContent.zdr == undefined) {
						malert('请选择制单人');
						return;
					}
					if (this.popContent.cksl <= 0|| this.popContent.cksl == null|| this.popContent.cksl == undefined) {
						malert('出库数量不正确');
						return;
					}
					if(this.popContent.cksl > this.popContent.kcsl){
						malert('出库数量大于库存数量！');
						return;
					}
					if(kd.isUpdate == 0 ){
						//新增
						if (kd.jsonList) {
							for (var i = 0; i < kd.jsonList.length; i++) {
								if (kd.jsonList[i].ypbm === this.popContent.ypbm && kd.jsonList[i].xtph === this.popContent.xtph) {
									malert('药品【' + this.popContent.ypmc+ '】已存在，不能重复录入，请修改。');
									return;
								}
							}
						}
						kd.jsonList.push(this.popContent);
						pop.cal();
						pop.popContent = {};
					}else{
						//修改
						kd.$set(kd.jsonList,kd.modifyIndex,pop.popContent);
						pop.cal();
						pop.closes();
						pop.popContent = {};
					}
				},
				cal:function(){
	                var temJj = 0;
	                var temLj = 0;

	                for (var i = 0; i < kd.jsonList.length; i++) {
	                    temJj += parseFloat(kd.jsonList[i].ypjj * kd.jsonList[i].cksl);
	                    temLj += parseFloat(kd.jsonList[i].yplj * kd.jsonList[i].cksl);
	                    kd.json.jjzj = temJj.toFixed(2);
	                    kd.json.ljzj = temLj.toFixed(2);
	                }

	                if (kd.jsonList.length == 0) {
	                    kd.json.jjzj = 0;
	                    kd.json.ljzj = 0;
	                }
				}
			}
		});

wrapper.initial();
wrapper.getYFData();
// 监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function(e) {
	var bol = $(e.target).parents().is(".selectGroup");
	if (!bol) {
		$(".selectGroup").hide();
	}

})
window.addEventListener('storage',function (e) {
    if( e.key == 'dbglitem' && e.oldValue !== e.newValue ){
        wrapper.initial();
    }
});
