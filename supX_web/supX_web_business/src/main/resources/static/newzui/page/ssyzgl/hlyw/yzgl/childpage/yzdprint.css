
/*设置纸张大小和横向*/
@page {
    size: A4 portrait;
    
	margin: 0;
}
.printHide {
    display: none !important;
}

.printShow {
    display: block !important;
}
.t-values {
    border: 0;
}

.tem {
    border: 0;
}

.drag {
    display: none;
}

body{
    height: auto;
}
body,
html{
    height: auto;
    width: auto;
    min-height: auto;
    min-width: auto;
}

.pop{
    position: absolute;
    height: auto;
}

/*隐藏页脚和页眉*/
nav, aside {
    display: none;
}

button {
    display: none !important;
}

/*.cqyzd, .lsyzd {*/
    /*width: 800px;*/
    /*height: auto;*/
/*}*/

    .yz-daying{
        position: absolute;
        right: 100px;
        top: 0;
        z-index: 99999;
    }
.cw_blank{
    background-color: #f84b4b;
}


	.yzdTitle{
		page-break-before: always;
	}

	.goPrintHide{
		visibility: hidden;
	}
	
	.hzxm{
		width: 3.5cm;
		max-width: 3.5cm;
	}
	.hzxb{
		width: 2cm;
		max-width: 2cm;
	}
	.hznl{
		width: 2cm;
		max-width: 2cm;
	}
	.hzbs{
		width: 5.7cm;
		max-width: 5.7cm;
	}
	.hzch{
		width: 2.5cm;
		max-width: 2.5cm;
	}
	.hzzyh{
		width: 4cm;
		max-width: 4cm;
	}
	
	
	.xdyz{
		width: 3.1cm !important;
		max-width: 3.1cm !important;
	}
	.yzxd{
		width: 2.15cm !important;
		max-width: 2.15cm !important;
	}
	.tzyz{
		width: 3.8cm !important;
		max-width: 3.8cm !important;
	}
	
	.yzrq{
		width: 1.5cm !important;
		max-width: 1.5cm !important;
		word-break: break-all;
		white-space: normal;
		word-wrap: break-word;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	
	.lsyzrq{
		width: 1.15cm !important;
		max-width: 1.15cm !important;
		word-break: break-all;
		white-space: normal;
		word-wrap: break-word;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	
	.yzsj{
		width: 1cm !important;
		max-width: 1cm !important;
	}
	.yzys{
		width: 1.2cm !important;
		max-width: 1.2cm !important;
	}
	.cqyzys{
		width: 1.6cm !important;
		max-width: 1.6cm !important;
	}
	.lsyzys{
		width: 1.6cm !important;
		max-width: 1.6cm !important;
	}
	.lsyzhs{
		width: 1.9cm !important;
		max-width: 1.9cm !important;
	}
	.yzhs{
		width: 1.15cm !important;
		max-width: 1.15cm !important;
	}
	.cqyznr{
		width: 7.9cm !important;
		max-width: 7.9cm !important;
	}
	.dybt{
		font-size: 6.8mm;
		color: #000;
		font-weight: 900;
		font-family: '宋体';
	}
	.yzd-brInfo {
	    display: flex;
	    width: 19.6cm;
	    margin: 0;
		margin-left: 1.1cm;
		font-size: 3.7mm;
		color: #000;
		font-weight: 900;
		font-family: '宋体';
	}
	
	.yz-tables{
		width: 19.6cm;
		margin-left: 1.4cm;
		margin-top: 0.2cm;
	}
	.yz-tables td {
		border: 0.1mm solid #000 ;
		font-weight: 500;
		height: 0.9cm !important;
		max-height: 0.9cm !important;
		white-space: nowrap;
		text-align: center;
		font-size: 3mm;
		color: #000;
		font-family: '宋体';
	}
	.lsyz-tables{
		width:18.1cm;
		max-width:18.1cm;
	}
	.lsyz-tables td {
		border: 0.1mm solid #000 ;
		font-weight: 500;
		height: 0.65cm !important;
		max-height: 0.65cm !important;
		white-space: nowrap;
		text-align: center;
		font-size: 3mm;
		color: #000;
		font-family: '宋体';
	}
	.lsyz-tables th {
		border: 0.1mm solid #000;
		font-weight: 500;
		height: 0.6cm !important;
		max-height: 0.6cm !important;
		white-space: nowrap;
		text-align: center;
		font-size: 3.5mm;
		color: #000;
		font-family: '宋体';
	}
	.yz-tables th {
		border: 0.1mm solid #000;
		font-weight: 500;
		height: 0.6cm !important;
		white-space: nowrap;
		text-align: center;
		font-size: 3.5mm;
		color: #000;
		font-family: '宋体';
	}
	.yz-tables td .yzd-name {
		width: 4.9cm !important;
		max-width: 4.9cm !important;
		
		max-height: 1cm !important;
		font-size: 2.5mm;
		text-align: left;
		margin-left: 1mm;
		color: #000;
		word-break: break-all;
		white-space: normal;
		word-wrap: break-word;
		overflow:hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		
		display: block;
	}
	.lsyz-tables td .lsyzd-name {
	    font-size: 2.5mm;
	    text-align: left;
	    margin-left: 1mm;
	    color: #000;
	    word-break: break-all;
	    white-space: normal;
	    word-wrap: break-word;
	    overflow: hidden;
	    text-overflow: ellipsis;
	    -webkit-line-clamp: 2;
	    -webkit-box-orient: vertical;
	    width: 7.4cm !important;
	    max-width: 7.4cm !important;
	    
	    max-height: 0.8cm !important;
		display: block;
	}
	.yzd-table table {
	    border-collapse: collapse;
	    margin: 0 auto;
	    font-size: 14px;
	}
	
	.lsyznrgg{
		width: 2.65cm !important;
		max-width: 2.65cm !important;
		word-break: break-all;
		white-space: normal;
		word-wrap: break-word;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		height: 0.8cm !important;
		max-height: 0.8cm !important;
		display: block;
	}
	.cqyznrgg{
		width: 3cm !important;
		max-width: 3cm !important;
		word-break: break-all;
		white-space: normal;
		word-wrap: break-word;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		
		max-height: 1cm !important;
		display: block;
	}
	.tz-start::before {
	    bottom: -1mm;
	    top: 2mm;
		right:1mm;
	    border-top: 0.1mm solid #000000;
	}
	
	.tz-center::before {
	    bottom: -1mm;
	    top: -2mm;
		right:1mm;
	}
	
	.tz-stop::before {
	    bottom: 2mm;
	    top: 0.5mm;
		right:1mm;
	    border-bottom: 0.1mm solid #000000;
	}
	.tz-start, .tz-center, .tz-stop {
	    width: 5.4cm;
	    position: relative;
	}
	
	
	.printShow{
		font-family: '宋体';
	}
	.hzxm{
		width: 3.5cm;
		max-width: 3.5cm;
	}
	.hzxb{
		width: 2cm;
		max-width: 2cm;
	}
	.hznl{
		width: 2cm;
		max-width: 2cm;
	}
	.hzbs{
		width: 5.7cm;
		max-width: 5.7cm;
	}
	.hzch{
		width: 2.5cm;
		max-width: 2.5cm;
	}
	.hzzyh{
		width: 4cm;
		max-width: 4cm;
	}
	.dybt{
		font-size: 6.8mm;
		color: #000;
		font-weight: 900;
		font-family: '宋体';
		text-align: center;
		margin-top: 0.1cm;
		margin-bottom: 0.2cm;
	}
	.yzd-brInfo {
	    display: flex;
	    width: 19cm;
	    margin: 0;
		margin-left: 1.1cm;
		font-size: 3.7mm;
		color: #000;
		font-weight: 900;
		font-family: '宋体';
	}
	.popCenter{
		
		margin: 0 !important;
		padding: 0 !important;;
	}
	.ysDiv {
	
	    width: 100%;
	    text-align: center;
	    /*position: absolute;*/
	    bottom: 0;
	}
	
	.yzd-ysInfo {
	    display: flex;
	    justify-content: center;
	}
	.yzd-ysqm{
		margin-right: 3cm;
	}
.pagePrintBefore{page-break-before:always;}
.pagePrintAfter{page-break-after:always;}
