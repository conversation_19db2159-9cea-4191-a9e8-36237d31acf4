
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, baseFunc, mformat, printer],
    data: {
//库房列表
        YFList: [],
        popContent: {},
        yfbm: null,
        csN04003002002201201:true,
        ifClick:true,
        ckdmxList: [],
        ckdList: [],
        ckdh: null,
        kfbm: null,
        sldh: null,
        lyyf: null,
        qxksbm: null,
    },
    mounted: function () {
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
        this.getYf();
    },
    methods: {
        //获取数据
        getData: function () {
            this.ckdList = []
            this.ckdmxList = []
            this.param.ckfs = '01'
            this.param.yfshbz = '0'
            this.param.shzfbz = '1'
            this.param.lyyf = this.popContent.yfbm
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=queryCkd' + '&parm=' + JSON.stringify(this.param), function (data) {
                if (data.a == 0 && data.d.list) {
                    for (var i = 0; i < data.d.list.length; i++) {
                        wrapper.ckdList = data.d.list;
                        wrapper.ckdmxList = [];
                    }
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },
        queryMx: function (index) {
            this.ckdh = this.ckdList[index].ckdh;
            this.kfbm = this.ckdList[index].kfbm;
            this.sldh = this.ckdList[index].sldh;
            this.lyyf = this.ckdList[index].lyyf;
            this.qxksbm = this.ckdList[index].ksbm;
            this.getMxData();
        },
        getMxData: function () {
            var parm = {
                "ckdh": this.ckdh,
                "page": this.param.page,
                "rows": this.param.rows,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=queryCkdMxByRkdh&parm=' + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    wrapper.totlePage = Math.ceil(data.d.length / wrapper.param.rows);
                    wrapper.ckdmxList = data.d;
                } else {
                    malert(data.c, 'top', 'defeadted');
                }
            });
        },
        //获取参数权限
        getCsqx: function () {
            var ksbm = this.listGetName(this.YFList, this.popContent.yfbm, 'yfbm', 'ksbm');
            //获取参数权限
            var parm = {
                "ylbm": 'N040030020022012',
                "ksbm": ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N04003002002201201": //药品领用审核显示SPD单号
                                    if (csjson.csz != null && csjson.csz != undefined && csjson.csz != "" && csjson.csz == "1") {
                                        wrapper.csN04003002002201201 = false;
                                    }
                                    break;
                            }
                        }
                }
            })
        },
        getYf: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                function (data) {
                    if (data.a == 0) {
                        wrapper.YFList = data.d.list;
                        wrapper.popContent.yfbm = wrapper.YFList[0].yfbm;
                        wrapper.getCsqx()
                        wrapper.getData();
                    } else {
                        malert('失败', 'top', 'defeadted')
                    }
                });
        },
//审核
        shenHe: function () {
            if (!wrapper.ifClick) return;
            wrapper.ifClick = false;
            var json = {
                ckdh: this.ckdh,
                kfbm: this.kfbm,
                qxksbm: this.qxksbm,
                sldh: this.sldh,
                lyyf: this.lyyf,
            };
            this.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfywCkd&types=yflysh', JSON.stringify(json)).then(function (data) {
                if (data.body.a == "0") {
                    wrapper.ifClick = true;
                    wrapper.isChecked = []
                    malert("审核成功！", 'top', 'success');
                    wrapper.getData();
                } else {
                    wrapper.ifClick = true;
                    malert(data.body.c, 'top', 'defeadted');
                }
            });
        },
//组件选择下拉框之后的回调
        resultChangeYf: function (val) {
            Vue.set(this.popContent, 'yfbm', val[0]);
            this.$forceUpdate()
            this.getData();

        },
    }
});
