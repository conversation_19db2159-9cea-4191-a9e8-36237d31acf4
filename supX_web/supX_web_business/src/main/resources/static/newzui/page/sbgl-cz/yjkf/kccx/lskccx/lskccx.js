var tableInfo = new Vue({
	el: '#crcx',
	//混合js字典庫
	mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
	data: {
		queryType: false, //是否按药品汇总查询
		jsonList: [],
        sbkfList: [],
		sbkf: 0, //设备库房信息
		wzkfbm:'01',
		param: {
			'page': 1,
			'rows': 100,
			'sort': '',
			'order': 'desc',
			'shzfbz': 1,
			'kfbm': '',
			'beginrq': null,
			'endrq': null,
			'parm': ''
		}
	},
	mounted: function() {
        var parm = {
            page: 1,
            rows: 20000,
            sort: 'sbkfbm',
            tybz: '0'
        }
        $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmSbkf&types=queryAll&json=" + JSON.stringify(parm),
            function (data) {
                if (data.a == 0) {
                    tableInfo.sbkfList = data.d;
                } else {
                    malert("获取库房失败！", 'top', 'defeadted');
                }
            });

		//获取单据列表
		//		this.getData();
	},
	methods: {
		//双击修改有效期和批次停用
		//		edit: function() {
		//			mxtzPop.isShow = true;
		//			//			mxtzPop.popContent = this.ypSelected;
		//			Vue.set(mxtzPop.popContent, 'xxq', this.fDate(this.ypSelected.yxqz, 'date'));
		//			Vue.set(mxtzPop.popContent, 'xpcty', '0');
		//			Vue.set(mxtzPop.popContent, 'wzkf', this.ypSelected.wzkf);
		//			Vue.set(mxtzPop.popContent, 'xtph', this.ypSelected.xtph);
		//			Vue.set(mxtzPop.popContent, 'wzbm', this.ypSelected.wzbm);
		//		},
		//单击选中
		checkOne: function(index, item) {
			this.ypSelected = item;
			this.isCheckAll = false;
			this.isChecked = [];
			this.isChecked[index] = true;
		},
		//设备库房改变
        sbkfChange: function() {
			// if(this.yfkf == 0) return;
			//			//重新获取列表
			this.getData();
		},
		//获取数据
		getData: function() {
			if(this.sbkf == 0) {
				malert("请选择库房");
				return;
			}
			if(tableInfo.param.endrq == null) {
				malert("请选择日期");
				return;
			}
			//是否按药品汇总
			this.param.parm = this.queryType ? 'sum' : null
			//设置库房
			this.param.sbkf = this.sbkf == 0 ? null : this.sbkf
			$.getJSON("/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=lskc&parm=" +
				JSON.stringify(this.param),
				function(json) {
					if(json.a == "0") {
						tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
						tableInfo.jsonList = json.d.list;
					} else {
						malert(json.c);
					}
				});
		}
	}
});

//弹出层
var mxtzPop = new Vue({
	el: '#mxtzPop',
	mixins: [dic_transform, baseFunc],
	data: {
		isShow: false,
		popContent: {},
		isKeyDown: null,
		title: '有效期、批次停用修改'
	},
	methods: {
		saveData: function() {
			if(this.popContent.xxq == null) {
				alert("请输入有效期")
				return;
			}
			if(this.popContent.xpcty == null) {
				alert("请输入停用标志")
				return;
			}

			$.getJSON("/actionDispatcher.do?reqUrl=New1SbkfKfywKccx&types=update&parm=" +
				JSON.stringify(this.popContent),
				function(data) {
					if(data.a == 0) {
						//						mxtz.getData();
						mxtzPop.isShow = false;
						mxtzPop.isAdd = false;
						malert("数据保存成功");
						//刷新页面
						tableInfo.getData();
					} else {
						malert("上传数据失败");
					}
				})
		}
	}
});
window.getTime = function(event, type) {
	if(type == 'star') {
		tableInfo.param.beginrq = $(event).val();
	} else if(type == 'end') {
		tableInfo.param.endrq = $(event).val();
	}
};
