@import "../../../../css/baseColor";
.icon-fy:before{
  left:inherit;
}
.icon-dy:before{
  left:17px !important;
}
.icon-ty:before{
left:77px !important;
}
.slgl-by{
  width: 100%;
  height: 40px;
  display: flex;
  line-height: 40px;
  background:@colorRgbf2a;
  justify-content: space-between;
  padding: 0 15px;
  i{
    width: 20%;
    display: block;
    text-align: center;
    color: @color75;
    &:nth-child(5){
      padding-right: 15px;
    }
    em{
      color: @color35;
      padding-left: 5px;
      float: left;
    }
  }
}
.fyxm-tab div{
  border: none !important;
  height: auto;
}
.fyxm-tab div:first-child{
  border: none !important;
}
.actives{
  border-bottom: 2px solid @color1a;
  color: @color1a;
}
.fyty-fr{
  position: absolute;
  right: 10px;
  top:116px;
  width: 120px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0;
  i{
    float: left;
  }
.fyty-select{
  width:50px;
  border: none !important;
  text-align: right;
  -webkit-appearance: none;
  position: relative;
  height: 26px;
  line-height: 26px;

}

  .fyty-dsj{
    width: 10px;
    position: absolute;
    top:29px;
    right: 0;
    height: 10px;
    background: url("../../../../css/@{image}<EMAIL>") center right no-repeat;
    transform: rotate(270deg);
  }
}

.cfhj-top{
  width: 100%;
  height: 36px;
  background:@colored;
  line-height: 36px;
  li{
    width: 100%;
    display: flex;
    justify-content: center;
    i{
      width: calc(~"(100% - 50px)/8");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
    }
  }
}
.cfhj-content{
  width: 100%;
  overflow: auto;
  max-height:500px;
  li{
    width: 100%;
    display: flex;
    border:1px solid @coloree;
    border-top: none;
    justify-content: center;
    cursor: pointer;
    i{
      width: calc(~"(100% - 50px)/8");
      display: block;
      text-align: center;
      &:nth-child(1){
        width: 50px !important;
      }
    }
    &:hover{
      background:rgba(26,188,156,0.08);
    }
  }
}
.title-width{
  width: 70px !important;
}
.wh182 {
  input[type='text']{
    border: none !important;
    height:24px;
  }
  .zui-select-group{
    padding: 0;
  }
}
.zui-form .zui-form-label{
  left: 5px !important;
}
.ksys-side{
  padding: 15px;
}