@import "../../../../css/baseColor";
.icon-bj:before{
  top:4px;
  width: 20px;
  height: 21px;
}
#jyxm_icon .switch{
  left:33px !important;
}
.tab-edit-list li label .label-input{
  text-indent: 10px !important;
}
.ksys-side{
  span{
    i{
    }
  }
}
.border-dotted-t{
  border-top: 1px dashed @color1a;
}
.fl{
  float: left;
}
.zui-select-inline{
  margin-right: 0 !important;
}
.tab-edit-list li label .dz-height{
  height: 70px !important;
  width: 76% !important;
  overflow: auto;

}
.width100{
width: 100% !important;
float: left;
  height: 70px !important;
}
.width150{
  width: 70px !important;
}
.tab-edit-list li label .dz-height{
  height:70px !important;
  width: 76% !important;
  overflow: auto;
  padding: 10px;

}
.width100{
  width: 100% !important;
  float: left;
  height: 70px !important;
}
.width150{
  width: 70px !important;
}
.width76{
  width: 76% !important;
}
.rkgl-position{
  position: fixed;
  bottom:0;
  display: flex;
  justify-content: flex-end;
  left:10px;
  right:10px;
  width: auto;
  z-index: 11;
  height:70px;
  background: @colorff;
  align-items: center;
  color: @color81;
  .rkgl-fl{
    width: auto;
    float: left;
    i{
      float: left;
      padding-left: 20px;
    }
  }
  span{
    display: block;
  }
  .rkgl-fr{
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}