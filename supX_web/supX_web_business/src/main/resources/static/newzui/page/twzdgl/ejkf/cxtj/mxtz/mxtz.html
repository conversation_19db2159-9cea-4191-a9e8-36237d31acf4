
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>明细台账</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b " @click="exportMxtz">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">药房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm"
                                      :name="'param.yfbm'" :search="true" :index_mc="'yfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label">查询方式</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="fsList" :index="'fsmc'" :index_val="'fsbm'" :val="param.fsbm"
                                      :name="'param.fsbm'" :search="true" :index_mc="'fsmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline padd-l-54">
                    <label class="zui-form-label">时间段</label>
                    <div class="zui-input-inline ">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh240 text-indent20" placeholder="请选择申请日期" id="timeVal"/>
                    </div>
                </div>
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline">
                        <input class="zui-input wh180" placeholder="请输入关键字" v-model="param.parm" type="text" id="jsvalue" @keydown="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view padd-r-10 padd-l-10"  z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>药品编码</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-xl text-left"><span>药品名称</span></div></th>
                    <th  v-if="qShow"><div class="zui-table-cell cell-s"><span>药品种类</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>期初数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>入库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>销售数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结余数量</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>损益数量</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  @dblclick="edit($index)"  @click="checkSelect([$index,'some','jsonList'],$event)" :class="[{'table-hovers':isChecked[$index]}]">
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                    <td  v-if="qShow"><div class="zui-table-cell cell-xl text-left">
                        <div class="title title-width text-left" :data-title="item.ypmc"><i v-text="item.ypmc"></i>
                        </div>
                    </div>
                    </td>
                    <td  v-if="qShow">
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" :data-title="item.ypzlmc"><i v-text="item.ypzlmc" style="width: 84px;display: block;overflow: hidden;text-overflow: ellipsis"></i>
                            </div>
                        </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.qcsl||0"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.rksl||0"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.cksl||0"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.xssl||0"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item[slType]||0"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.qcsl+item.rksl-item.cksl-item.xssl-item[slType]"></div></td>
                </tr>
                <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
<!--侧边窗口-->

</div>

<script src="mxtz.js"></script>
</body>

</html>
