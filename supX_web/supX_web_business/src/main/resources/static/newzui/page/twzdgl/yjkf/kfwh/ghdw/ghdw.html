<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>供货单位</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" rel="stylesheet" href="ghdw.css"/>
</head>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>
            <button class="tong-btn btn-parmary-b icon-sc-header paddr-r5 icon-font15" @click="del">删除</button>
            <button class="tong-btn btn-parmary-b icon-width icon-dc ">导出</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline margin-f-l25">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="param.parm" id="jsvalue" @keydown.13="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view "  z-height="full" >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m">
                        <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>单位编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-left"><span>单位名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音代码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>联系方式</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>联系人</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>联系地址</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>邮政编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>电子邮件</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>类型</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>备注描述</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body "  @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList"  :tabindex="$index" @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'some','jsonList'],$event)">
                    <td class="cell-m">
                            <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                            :type="'some'" :which="$index"
                                            :val="isChecked[$index]">
                            </input-checkbox>
                    </td>
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.dwbm"></div></td>
                    <td><div class="zui-table-cell cell-xl text-left" v-text="item.dwmc" :data-title="item.dwmc"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pyjm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.lxfs"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.lxr"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <span class="title title-width" v-text="item.lxdz" ></span>
                         </div>
                    </td>
                    <td><div class="zui-table-cell cell-s" v-text="item.yzbm"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.dzyj"></div></td>
                    <td><div class="zui-table-cell cell-s" v-text="dwlx_tran[item.lx]"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <span class="title title-width" v-text="item.bzms"></span>
                    </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <div class="switch">
                                <input  type="checkbox" true-value="0" false-value="1" v-model="item.tybz" disabled/>
                                <label></label>
                            </div>
                        </div>
                    </td>
                    <td class="cell-s"><div class="zui-table-cell cell-s">
                        <span class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em  class="width30">
                                    <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                        </span>
                    </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <!--左侧固定-->
        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                                                              :type="'all'" :val="isCheckAll">
                        </input-checkbox></div></th>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m">
                                <input-checkbox @result="reCheckBox" :list="'jsonList'"
                                                :type="'some'" :which="$index"
                                                :val="isChecked[$index]">
                                </input-checkbox>
                            </div>
                        </td>
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <!--右侧固定-->
        <div class="zui-table-fixed table-fixed-r">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'some','jsonList'],$event)">
                        <td class="cell-s"><div class="zui-table-cell cell-s" >
                            <span class="flex-center padd-t-5">
                                <em class="width30">
                                    <i class="icon-bj" @click="edit($index)" data-title="编辑"></i>
                                </em>
                                <em  class="width30">
                                    <i class="icon-sc icon-font" @click="remove($index)" data-title="删除"></i>
                                </em>
                        </span>
                        </div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<!--侧边窗口-->
<div class="side-form ng-hide pop-700"    id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side ">
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">常规信息</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="flex-container flex-wrap-w">
                    <div class="span1 flex-container flex-align-c">
                        <i>单位编码</i>
                        <input type="text" class="zui-input border-r4" v-model="popContent.dwbm" @keyup="nextFocus($event)" disabled="disabled"/>
                    </div>
                    <div class="span1 flex-container flex-align-c">
                        <i>单位名称</i>
                        <input type="text" class="zui-input border-r4" v-model="popContent.dwmc" @keyup="nextFocus($event)" @blur="setPYDM(popContent.dwmc, 'popContent', 'pyjm')"/>
                    </div>
                    <div class="span1 flex-container flex-align-c">
                        <i>拼音简码</i>
                        <input type="text" class="zui-input border-r4" v-model="popContent.pyjm" @keydown="nextFocus($event)" disabled="disabled"/>
                    </div>
                    <div class="span1 flex-container flex-align-c">
                        <i>邮政编码</i>
                        <input type="text" class="zui-input border-r4" v-model="popContent.yzbm" @keydown="nextFocus($event)"/>
                    </div>
                    <div class="span1 flex-container flex-align-c">
                        <i>电子邮件</i>
                        <input type="email" class="zui-input border-r4" v-model="popContent.dzyj" @keydown="nextFocus($event)" />
                    </div>
                    <!--<div class="span1 flex-container flex-align-c">
                        <i>单位类型</i>
                        <select-input @change-data="resultChange"  :child="dwlx_tran" :index="popContent.lx" :val="popContent.lx" :name="'popContent.lx'">
                        </select-input>
                    </div>-->
                    <div  class="margin-top-10 span1 flex-container flex-align-c">
                        <i>状态</i>
                        <div class="switch" >
                            <input id="tybz" type="checkbox" true-value="0" false-value="1"  v-model="popContent.tybz"/>
                            <label for="tybz"></label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">附加信息</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div class="flex-container flex-wrap-w">
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>账号</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.zh" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>税务登记号</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.swdjh" @keydown="nextFocus($event)" />
                </div>
                    <div  class="margin-top-10 span1 flex-container flex-align-c">
                        <i>联系人</i>
                        <input type="email" class="zui-input border-r4" v-model="popContent.lxr" @keydown="nextFocus($event)" />
                    </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>电话</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.lxdh" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>许可证号</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.xkzh" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>许可证效期</i>
                    <input @click="showTime('xkzxq','xkzxq')" id="xkzxq" class="zui-input border-r4"  :value="fDate(popContent.xkzxq,'date')" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>执照号</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.zzh" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>执照效期</i>
                    <input @click="showTime('zzxq','zzxq')" id="zzxq" class="zui-input border-r4"  :value="fDate(popContent.zzxq,'date')" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>授权号</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.sqh" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c">
                    <i>授权期</i>
                    <input @click="showTime('sqq','sqq')" id="sqq" class="zui-input border-r4"  :value="fDate(popContent.sqq,'date')" @keydown="nextFocus($event)" />
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c position">
                    <i>信用期</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.xyq" @keydown="nextFocus($event)" />
                    <span class="cm">月</span>
                </div>
                <div  class="margin-top-10 span1 flex-container flex-align-c position">
                    <i>信用额</i>
                    <input type="email" class="zui-input border-r4" v-model="popContent.xye" @keydown="nextFocus($event)" />
                    <span class="cm">元</span>
                </div>
                <div  class="margin-top-10 span1 flex-container wh100MAx flex-align-c">
                    <i>开户银行</i>
                    <input type="email" class="zui-input wh100MAx border-r4" v-model="popContent.khh" @keydown="nextFocus($event)" >
                </div>
                <div  class="margin-top-10 span1 flex-container wh100MAx flex-align-c">
                    <i>地址</i>
                    <input type="email" class="zui-input wh100MAx border-r4" v-model="popContent.dz" @keydown="nextFocus($event)" >
                </div>
                    <div class="span1 flex-container  wh100MAx">
                        <i>联系地址</i>
                        <input  class="zui-input rkgl-position wh100MAx border-r4" v-model="popContent.lxdz" @keydown="nextFocus($event)" >
                    </div>
            </div>
            </div>
        </div>
        <div class="tab-card">
            <div class="tab-card-header">
                <div class="tab-card-header-title font14">其他辅助信息</div>
            </div>
            <div class="tab-card-body padd-t-10">
                <div>
                    <span>销售人员委托信息</span>
                    <div class="flex-container flex-wrap-w">
                        <div  class="margin-top-10 span1 flex-container flex-align-c">
                            <i>姓名</i>
                            <input type="email" class="zui-input border-r4" v-model="popContent.xsrxm" @keydown="nextFocus($event)" />
                        </div>
                        <div  class="margin-top-10 span1 flex-container flex-align-c">
                            <i>日期</i>
                            <input @click="showTime('xswtrq','xswtrq')" id="xswtrq" class="zui-input border-r4" :value="fDate(popContent.xswtrq,'date')"  @keydown="nextFocus($event)" />
                        </div>
                    </div>
                </div>
                <div>
                    <span>质量认证信息</span>
                    <div class="flex-container flex-wrap-w">
                        <div  class="margin-top-10 span1 flex-container flex-align-c">
                            <i>证号</i>
                            <input type="email" class="zui-input border-r4" v-model="popContent.zlrzh" @keydown="nextFocus($event)" />
                        </div>
                        <div  class="margin-top-10 span1 flex-container flex-align-c">
                            <i>日期</i>
                            <input @click="showTime('zlrzrq','zlrzrq')" id="zlrzrq" class="zui-input border-r4" :value="fDate(popContent.zlrzrq,'date')" @keydown="nextFocus($event)" />
                        </div>
                    </div>
                </div>
                <div>
                    <span>药监局备案信息</span>
                    <div class="flex-container flex-wrap-w">
                        <div  class="margin-top-10 span1 flex-container flex-align-c">
                            <i>证号</i>
                            <input type="email" class="zui-input border-r4" v-model="popContent.yjbah" @keydown="nextFocus($event)" />
                        </div>
                        <div  class="margin-top-10 span1 flex-container flex-align-c">
                            <i>日期</i>
                            <input @click="showTime('yjbarq','yjbarq')" id="yjbarq" class="zui-input border-r4" :value="fDate(popContent.yjbarq,'date')" @keydown="nextFocus($event)" />
                        </div>
                    </div>
                </div>
                <div  class="margin-top-10 span1 flex-container  wh100MAx">
                    <i>首营品种</i>
                    <input  class="zui-input rkgl-position wh100MAx border-r4" v-model="popContent.sypz" @keydown="nextFocus($event)" >
                </div>
                <div class="span1 flex-container  wh100MAx">
                    <i>备注描述</i>
                    <input  class="zui-input rkgl-position wh100MAx border-r4" v-model="popContent.bzms" @keydown="nextFocus($event)" >
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>

<script src="ghdw.js"></script>

</body>

</html>
