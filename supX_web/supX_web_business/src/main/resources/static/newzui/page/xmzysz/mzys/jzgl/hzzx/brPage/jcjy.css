.butt-box a{
    display: table-cell;
    padding: 10px;
}
.jybgd{
    width: 210mm;
    margin: 0 auto;
}
/*.table-fixed{*/
/*  table-layout: fixed;*/
/*}*/
.jybgd-table{
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
}
.jybgd-table thead tr{
    border-bottom: 1px solid #000;
}
.jybgd-table thead th{
    font-weight: bold;
    line-height: 1.5;
    text-align: left;
}
.jybgd-table tbody td{
    padding: 3px;
    line-height: 1.1;
}
.visibility{
    visibility:hidden
}
.green{
    color:green;
}
.red{
    color:red;
}



.flex-r{
    display: flex;
    flex-direction: row;
}
.flex-c{
    display: flex;
    flex-direction: column;
}

.flex-jc-c{
    display: flex;
    justify-content: center;
}

.flex-r-sa{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}

.flex1{
    flex:0.333;
}
.absolute-right{
    right: 0;
}
#bgdList{
    width: 30%;
}
@media print {
    @page{
        size: A4;
        margin: 0.5cm 0;
    }

    .yzdTitle{
        page-break-before: always;
    }
    .wrapper {
        border: none;
    }

}

#jydy{
    width:90%;
    margin: 0 auto;
    font-family: '宋体';
    font-size: 10px;
}
