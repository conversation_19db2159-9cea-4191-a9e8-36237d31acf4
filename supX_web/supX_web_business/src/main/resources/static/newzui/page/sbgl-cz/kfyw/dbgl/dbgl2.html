<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>历史库存</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
    <div class="wrapper" id="wrapper">
        <div class="panel ">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-width icon-dc-b">导出</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label ">科室</label>
                        <div class="zui-input-inline wh122">
                            <select-input @change-data="resultRydjChange"
                                          :child="ksList" :index="'ksmc'" :index_val="'ksbm'" :val="param.ksbm"
                                          :name="'param.ksbm'" :search="true" :index_mc="'ksmc'" >
                            </select-input>
                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label   margin-f-l10">时间段</label>
                        <div class="zui-input-inline   margin-f-l20">
                            <input class="zui-input todate wh240 text-indent20" placeholder="请选择申请日期" id="timeVal" />
                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label   margin-f-l10">检索</label>
                        <div class="zui-input-inline margin-f-l35">
                            <input class="zui-input todate wh240 "  v-model="param.parm" @keydown.13="goToPage(1)" placeholder="请输入关键字"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="zui-table-view">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备编码</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>设备名称</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>设备规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>库存数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备进价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>强检日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备供应商</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备在库状态</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>存放地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>科室管理人</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>设备产地</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <table class="zui-table table-width50-1">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  @dblclick="edit($index)" :class="[{'table-hovers':$index===activeIndex}]">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.sbbm">设备编码</div></td>
                        <td>
                            <div class="zui-table-cell cell-xl text-over-2" v-text="item.sbmc"></div>
                        </td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.sbgg">设备规格</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.kcsl">设备数量</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDec(item.jj,2)">设备进价</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.scph">设备批号</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')">设备有效期</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.qjrq,'date')">设备强检日期</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.gysmc">设备供应商</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.zkztmc">设备在库状态</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.cfd">存放地</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ksglrymc">科室管理人</div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.cd">设备产地</div></td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>

            </div>

            <model  :s="'保存'" :c="'退出'"  @default-click="saveData" @result-clear="mbModel=false" :model-show="true" @result-close="mbModel=false"
                    v-if="mbModel" :title="'科室转库'">
                <div class="bqcydj_model">
                    <div class="flex-container flex-wrap-w">
                        <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                            <span class="padd-r-5 whiteSpace">科室名称</span>
                            <select-input @change-data="resultKsdjChange"
                                          :child="zkksList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.zkksbm"
                                          :name="'param.ksbm'" :search="true" :index_mc="'ksmc'" >
                            </select-input>
                        </div>
                    </div>
                </div>
            </model>
            <div class="zui-table-fixed table-fixed-l" style="left: 10px;">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex}]" class="tableTr2">
                            <td><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

        </div>

    </div>
</div>

<script src="dbgl.js"></script>
</body>

</html>
