var yzglContent = new Vue({
    el: '#yzglContent',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ypbz0: 1,
        ypbz1: 1,
        yzlx0: 1,
        yzlx1: 1,
        w: 1,
        jc: 1,
        jy: 1,
        zl: 1,
        ss: 1,
        hz: 1,
        sx: 1,
        hl: 1,
        sss: 1,
        zdy:0,
        ywlx: '0',
        num: 0,
        brPosition: [0],
        brListWinSize: { // 显示区域的边界
            top: null,
            bottom: null
        },
        param: {
            parm: '',
        },
        IsPrint_tran: {
            '0': '未打印',
            '1': '已打印',
        },
        updatedArr:[],
        zxdybz:'0',
        yzbgdybz:'0',
        isGet: true,
        brItemListSumHeight: 0, // 所有病人加起来的高度
        yyffList: [],
        isCheckedS: [],
        searchyyff: [],
        rightJsonList: [],
        beginrq: getTodayDateBegin(),
        endrq: getTodayDateEnd(),
        scrollFn: function () {
        },
    },
    created: function () {
        this.scrollFn = this.brScrollFn();
    },
    updated: function () {
        changeWin()
    },
    watch: {
        'rightJsonList': function (newValue, OldValue) {
            var _list = newValue, _length = newValue.length, _brPosition = [0], _brItemListSumHeight = 0;
            if (newValue.length > 0) {
                common.openloading('#wrapper')
                for (var i = 0; i < newValue.length; i++) {
                    if (this.rightJsonList[i].yzxx.length == 0) {
                        this.rightJsonList.splice(i, 1)
                        continue;
                    }
                    for (var int = 0; int < newValue[i].yzxx.length; int++) {
                        this.rightJsonList[i]['yzxx'][int]['dysj']=this.fDate(new Date(),'AllDate')
                        // 计算当前元素定位
                        var _yzxxListLength = _list[i].yzxx.length;
                        _brPosition[i + 1] = (93 + _yzxxListLength * 40) + (_brPosition[i] == undefined ? _brPosition[i - 1] : _brPosition[i]);
                        // 计算总高度
                        if (i === _length - 1) {
                            _brItemListSumHeight = _brPosition[i + 1];
                        }
                    }
                }
                // 初始化显示边界
                this.$nextTick(function () {
                    var _height = $("#wrapper").height();
                    this.brListWinSize = {
                        top: 0 - _height,
                        bottom: _height * 2
                    }
                });
                this.brItemListSumHeight = _brItemListSumHeight;
                this.brPosition = this.notempty(_brPosition);
                common.closeLoading()
            }
        },
    },
    mounted: function () {
        Mask.newMask(this.MaskOptions('beginrq'));
        Mask.newMask(this.MaskOptions('endrq'));
        laydate.render({
            elem: '#beginrq',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) {
                yzglContent.beginrq = value;
                yzglContent.queryYz()
            }
        });
        laydate.render({
            elem: '#endrq',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) {
                yzglContent.endrq = value;
                yzglContent.queryYz()
            }
        });
        this.getYyffData();
        this.iscf();
    },
    methods: {
        iscf: function () {
            var that=this;
            console.log(111)
            var parm = {
                zyh: this.jlxqContent.zyh
            }
            this.qsxzList = [];
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == '0' && json.d.list != null && json.d.list.length > 0) {
                    that.qsxzList = json.d.list;
                    var qb = {
                        yexm: that.jlxqContent.brxm,//如果有影响请还原上面代码，注释本行代码
                        yebh: "000",
                    };
                    that.qsxzList.unshift(qb);
                    that.jlxqContent.yebh = that.qsxzList[0].yebh;

                }
            });
        },
        resultChangezxjh:function(val){
            this[val[2][0]] = val[0];
            yzglContent.queryYz()
        },
    	// 今天
    	today:function(){
    		this.beginrq = getTodayDateBegin();
            this.endrq = getTodayDateEnd();
            this.queryYz();
    	},
    	// 明天
    	tomorrow:function(){
    		var date = new Date();
    		var next = new Date(date.getTime() + 24*60*60*1000);
    		this.beginrq =this.fDate(next,'date') + " 00:00:00";
            this.endrq = this.fDate(next,'date') + " 23:59:59";
            this.queryYz();
    	},
        printZxjh: function () {
            this.getCheck('print')
            if (this.yzlx0 == 0 && this.yzlx1 == 0) {
                malert('频率类型必须选择一个', 'top', 'defeadted')
                return false
            }
            if (this.ypbz0 == 0 && this.ypbz1 == 0) {
                malert('频率标准必须选择一个', 'top', 'defeadted')
                return false
            }
            var zyhs = JSON.parse(JSON.stringify(yzclLeft.arr)),
                yyff = JSON.parse(JSON.stringify(yzglContent.searchyyff)),
                jcfl = JSON.parse(JSON.stringify(this.searchjcfl));
            var yzlx="";
            if(this.yzlx=="2"){
                yzlx=""
            }else{
                yzlx=this.yzlx;
            }
            this.postPrint()
            var sytPrint = '', sytSize = '';
            strPrint = [{
                reportlet: 'hsz%2Fhsz_ypypzxd_zxxm.cpt',
                yzlx: yzlx,
                yp: this.ypbz,
                ksbm:yzclLeft.jsContent.ksbm,
                ksmc:yzclLeft.jsContent.ksmc,
                dysj:this.updatedArr[0]['dysj'],
                zyh: zyhs.join(',')+',',
                yyff: yyff.join(',')+',',
                endtime: this.endrq,
                starttime: this.beginrq,
                jcfl: jcfl.join(',')+',',
                zxdybz:this.zxdybz
            }];
            window.top.J_tabLeft.csqxparm.csbm = "N010024006";
            console.log(window.top.J_tabLeft.csqxparm);
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" +
                JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0) {
                    console.log(json.d);
                    if (json.d != null && json.d != undefined && json.d.length > 0) {
                        sytPrint = json.d[0].csz;
                        sytSize = json.d[0].cszmc;
                    }
                    if (!FrPrint(strPrint, sytPrint, sytSize)) {
                        window.print();
                    }
                } else {
                    window.print();
                }
            })
        },
        postPrint:function(){
            var saveList=[];
            this.updatedArr=[]
            for (var i = 0; i <this.rightJsonList.length ; i++) {
                saveList.push(this.rightJsonList[i].yzxx)
            }
            this.updatedArr= saveList.reduce(function (a, b) {
                return a.concat(b)
            } );
            var json = '{"list":' + JSON.stringify(this.updatedArr) + '}';
            this.postAjax('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=updZxjhDybz',
                json,function (data) {
                    if (data.a == 0) {
                        malert("更新成功", 'top', 'success');
                        yzglContent.queryYz()
                    } else {
                        malert("更新失败", 'top', 'defeadted')
                    }
                });
        },
        printBgd:function(){
            this.getCheck('print')
            if (this.yzlx0 == 0 && this.yzlx1 == 0) {
                malert('频率类型必须选择一个', 'top', 'defeadted')
                return false
            }
            if (this.ypbz0 == 0 && this.ypbz1 == 0) {
                malert('频率标准必须选择一个', 'top', 'defeadted')
                return false
            }
            var zyhs = JSON.parse(JSON.stringify(yzclLeft.arr)),
                yyff = JSON.parse(JSON.stringify(yzglContent.searchyyff)),
                jcfl = JSON.parse(JSON.stringify(this.searchjcfl));
            this.postBgdPrint();
            strPrint = [{
                reportlet: 'hsz%2Fhsz_ypypzxd_bgd.cpt',
                yzlx: this.yzlx,
                yp: this.ypbz,
                ksbm:yzclLeft.jsContent.ksbm,
                ksmc:yzclLeft.jsContent.ksmc,
                dysj:this.updatedArr[0]['dysj'],
                zyh: zyhs.join(',')+',',
                yyff: yyff.join(',')+',',
                endtime: this.endrq,
                starttime: this.beginrq,
                jcfl: jcfl.join(',')+',',
                zxdybz:this.zxdybz
            }];
            FrPrint(strPrint, null);
        },
        postBgdPrint:function(){
            var saveList=[];
            this.updatedArr=[]
            for (var i = 0; i <this.rightJsonList.length ; i++) {
                saveList.push(this.rightJsonList[i].yzxx)
            }
            this.updatedArr= saveList.reduce(function (a, b) {
                return a.concat(b)
            } );
            var json = '{"list":' + JSON.stringify(this.updatedArr) + '}';
            this.postAjax('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=updYzbgDybz',
                json,function (data) {
                    if (data.a == 0) {
                        malert("更新成功", 'top', 'success');
                        yzglContent.queryYz()
                    } else {
                        malert("更新失败", 'top', 'defeadted')
                    }
                });
        },
        brScrollFn: function (flag) {
            if (flag) {
                common.openloading('#wrapper')
            }
            console.log(12)
            var timer = null;
            return function () {
                clearTimeout(timer)
                timer = setTimeout(function () {
                    var _el = $("#wrapper"),
                        _height = _el.height(),
                        _top = _el.scrollTop();
                    yzglContent.brListWinSize = {
                        top: _top - _height,
                        bottom: _top + _height * 2
                    }
                    common.closeLoading()
                }, 20);
            }
        },
        selectGet: function (val) {
            this[val[2][0]] = val[0];
            this.queryYz()
        },
        setChbox:function(){
            for (let i = 0; i < yzglContent.yyffList.length; i++) {
                Vue.set(yzglContent.isCheckedS, i, true)
            }
            yzglContent.isCheckAll = true;
        },
        getYyffData: function () {
            this.param.rows = 100;
            $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmyyff&types=query&dg=" + JSON.stringify(this.param), function (json) {
                if (json.a == '0' && json.d) {
                    yzglContent.yyffList = json.d.list;
                    yzglContent.setChbox()
                    yzglContent.queryYz()
                }
            });
        },
        getReCheckOne: function (val) {
            Vue.set(this, val[0][0], val[1]);
            if(val[0][0]=='ypbz0' && val[1] =='0'){
                this.isCheckedS=[];
                this.isCheckAll = false;
            }else{
                yzglContent.setChbox()
            }
            this.queryYz();
        },
        tabBg: function (index) {
            this.num = index;
            this.queryYz()
        },
        //医嘱查询接口
        getCheck: function (type) {
            if (this.ypbz0 == 1) {
                this.ypbz = '1';
            }
            if (this.ypbz1 == 1) {
                this.ypbz = '0'
            }
            if (this.ypbz0 == 1 && this.ypbz1 == 1) {
                this.ypbz = type == 'get' ? 2 : '';
            }
            if (this.ypbz0 == 0 && this.ypbz1 == 0) {
                this.ypbz = '';
            }
            if (this.yzlx0 == 1) {
                this.yzlx = '0';
            }
            if (this.yzlx1 == 1) {
                this.yzlx = this.yzlx1
            }
            if (this.yzlx0 == 1 && this.yzlx1 == 1) {
                this.yzlx = type == 'get' ? 2 : '';
            }
            if (this.yzlx0 == 0 && this.yzlx1 == 0) {
                this.yzlx = '';
            }
        },
        reCheckBox2: function (val) {
            var that = this;
            if (val[1] !== 'all') this.activeIndex = val[0];
            if (val[0] == 'some') {
                Vue.set(this.isCheckedS, val[1], val[2]);
                if (that.notempty(this.isCheckedS).length == this[val[3]].length) {
                    this.isCheckedS.every(function (el) {
                        if (el === true) {
                            return that.isCheckAll = true
                        } else {
                            return that.isCheckAll = false
                        }
                    })
                }
                console.log(this.isCheckedS)
            } else {
                this.isCheckAll = val[2];
                if (val[1] == null) val[1] = "jsonList";
                if (this.isCheckAll) {
                    for (var i = 0; i < this[val[1]].length; i++) {
                        Vue.set(this.isCheckedS, i, true);
                    }
                } else {
                    this.isCheckedS = [];
                }
            }
            this.queryYz()
        },
        queryYz: function () {
            console.log(1)
            if (!this.isGet) return;
            this.isGet = false;
            if (yzclLeft.zyhs.length == 0) {
                this.isGet = true;
                malert('请选择病人查询', 'top', 'defeadted');
                return false;
            }
            //对象清空
            this.searchyyff = [];
            this.rightJsonList = [];
            this.searchjcfl = [];
            this.getCheck('get');
            if (this.w == 1) {
                this.searchjcfl.push('0')
            }
            if (this.jc == 1) {
                this.searchjcfl.push('1')
            }
            if (this.jy == 1) {
                this.searchjcfl.push('2')
            }
            if (this.zl == 1) {
                this.searchjcfl.push('3')
            }
            if (this.ss == 1) {
                this.searchjcfl.push('4')
            }
            if (this.hz == 1) {
                this.searchjcfl.push('5')
            }
            if (this.sx == 1) {
                this.searchjcfl.push('6')
            }
            if (this.hl == 1) {
                this.searchjcfl.push('7')
            }
            if (this.sss == 1) {
                this.searchjcfl.push('8')
            }
            //自带药
            if (this.zdy == 1){
                this.yzfl = "6";
            }else{
                this.yzfl = null;
            }
            if (this.num == 0) {
                for (var i = 0; i < this.isCheckedS.length; i++) {
                    if (this.isCheckedS[i]) {
                        this.searchyyff.push(this.yyffList[i].yyffbm)
                    }
                }
                var url = 'quetyYzzxxmb';
            } else {
                var url = 'quetyYzbgd';
            }
            var parm = {
                ypbz: this.ypbz,
                ksbm: yzclLeft.jsContent.ksbm,
                yzlx: this.yzlx,
                ystzbz: (yzclRight.ystzbz ? yzclRight.ystzbz : ''),
                beginrq: this.beginrq,
                endrq: this.endrq,
                xmmc: this.param.parm,
                ywlx: this.ywlx,
                zxdybz:this.zxdybz,
                yzfl:this.yzfl
            };
            if(this.num == '1'){
            	parm.yzbgdybz = this.yzbgdybz;
            }
            common.openBar('#wrapper');
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYzclCx&types=' + url + '&zyh=' + JSON.stringify(yzclLeft.zyhs) + '&searchyyff=' + JSON.stringify(this.searchyyff) + '&yzfl='+this.yzfl+'&searchjcfl=' + JSON.stringify(this.searchjcfl) + '&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == '0' && json.d != null) {
                    yzglContent.isGet = true;
                    yzglContent.rightJsonList = yzglContent.addSameGroupClass(json.d.list);
                    ;
                    common.closeLoading()
                } else {
                    yzglContent.isGet = true;
                    malert(json.c, 'top', 'defeadted')
                    common.closeLoading()
                }

            }, function (error) {
                console.log(error);
                malert(error, 'top', 'defeadted')
                common.closeLoading()
            });
        },
        addSameGroupClass: function (list) {
            list.forEach(function (item) {
                var yzList = item.yzxx,
                    length = yzList.length;
                if (length > 1) {
                    for (var i = 0; i < length; i++) {
                        if (yzList[i].xhid != 0) {
                            if (i == 0) { // 第一个
                                if (yzList[i].xhid == yzList[i + 1].xhid) {
                                    yzList[i]['tzbj'] = 'tz-start';
                                }
                            } else if (i == length - 1) { // 最后一个
                                if (yzList[i].xhid == yzList[i - 1].xhid) {
                                    yzList[i]['tzbj'] = 'tz-stop';
                                }
                            } else {
                                if (yzList[i].xhid != yzList[i - 1].xhid && yzList[i].xhid == yzList[i + 1].xhid ) {
                                    yzList[i]['tzbj'] = 'tz-start';
                                } else if (yzList[i].xhid == yzList[i - 1].xhid && yzList[i].xhid == yzList[i + 1].xhid) {
                                    yzList[i]['tzbj'] = 'tz-center';
                                } else if (yzList[i].xhid == yzList[i - 1].xhid && yzList[i].xhid != yzList[i + 1].xhid) {
                                    yzList[i]['tzbj'] = 'tz-stop';
                                }
                            }
                        }
                    }
                }
            });
            return list;
        }
    },
})
yzclLeft.$watch('zyhs', function (newVal, oldVal) {
    if (this.index == 11) {
        yzglContent.queryYz()
    }
},{deep: true})
