.fyxm-size {
    width: 100%;
}
.fyxm-hide {
    display: none;
}
.fyxm-show {
    display: table;
}
#jyxm_icon .switch {
    top: 15%;
    left: 30%;
}
.icon-bj:before {
    top: 6px;
    width: 20px;
    height: 21px;
}
.pop-width {
    width: 320px;
}
.pop-805 {
    width: 805px !important;
}

.zui-btn.btn-outline.btn-default:hover{
    background-color: transparent;
}
.zui-btn{
    margin-right: 0;
}
.zui-row:after, .zui-row:before{
    display: initial;
}
.rysx_bottom{
    margin: 0
}


.ksys-btn button {
    margin-right: 20px;
}
#jyxm_icon .switch {
    top: 15%;
    left: 35%;
}
.zui-table-view .overflow1{
    overflow: hidden;
   display: block;
}
.confirm-row{
    box-sizing: border-box;
    padding-right: 15px;
}