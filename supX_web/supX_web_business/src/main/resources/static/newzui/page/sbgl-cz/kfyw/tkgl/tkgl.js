function getData() {
    this.getData()
}

var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        kfList: [],
        search: '',
        ylbm: 'N050080012003',
        totlePage: 0,
        zhuangtai: {
            '0': '待审核',
            '1': '已审核',
            '2': '未通过',
            '3': '已作废'
        },
        param: {
            page: 1,
            rows: 10,
            parm: '',
            rkfs: '02',
            beginrq: null,
            endrq: null
        },
        jsonList: [],
    },
    mounted: function () {
        this.getKfmc();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
    },
    methods: {
        //开单
        openNewPage: function () {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.sbkfbm);
            Vue.set(obj, 'kfList', this.kfList);
            this.Verify(obj);
        },
        //库房
        resultRydjChange: function (val) {
            Vue.set(this.param, 'kfbm', val[0]);
            Vue.set(this.param, 'sbkfmc', val[4]);
            this.getData();

        },
        //加载库房名称
        getKfmc: function () {
            // 请求库房的api
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=sbkf', function (data) {
                if (data.a == 0) {
                    wrapper.kfList = data.d.list;
                    Vue.set(wrapper.param, 'kfbm', data.d.list[0].sbkfbm);
                    wrapper.getData();
                } else {
                    malert("获取库房列表失败");
                }
            });
        },
        //参数
        getCsqx: function () {
            var parm = {
                "ylbm": this.ylbm,
                "ksbm": ''
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {}
                        }
                    wrapper.getData();
                } else {
                    malert("获取参数失败！", "top", "defeadted");
                }
            });
        },
        //审核
        sh: function (index) {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.kfbm);
            Vue.set(obj, 'kfList', this.kfList);
            Vue.set(obj, 'tkd', this.jsonList[index]);
            Vue.set(obj, 'ksbm', this.jsonList[index].ksbm);
            Vue.set(obj, 'sh', this.jsonList[index].shzfbz == 0);
            Vue.set(obj, 'dy', this.jsonList[index].shzfbz != 0);
            this.Verify(obj);
        },
        //跳转
        Verify: function (obj) {
            sessionStorage.setItem('obj', JSON.stringify(obj));
            this.topNewPage('退库管理', 'page/sbgl/kfyw/tkgl/tkkd.html');
        },
        //作废
        Refuse: function (index) {
            var obj = this.jsonList[index];
            if (common.openConfirm("<div>确定作废设备退库单号-" + obj.rkdh + "-退库单吗？<div class=\"flex-container flex-align-c\"><span class=\"ft-14 whiteSpace padd-r-5\">作废原因</span>\n" +
                " <textarea rows=\"3\" cols=\"100\" id=\"zfyy\" class=\"padd-t-5 padd-b-5 padd-l-5 padd-r-5 wh100MAx\"></textarea>\n" +
                "</div></div>", function () {
                obj.zfyy = $('#zfyy').val()
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1WzkfKfywRkd&types=invald', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("作废成功！", 'top', 'success');
                        wrapper.getData();
                    } else {
                        malert("作废失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }

        },
        getData: function () {
            //发送请求获取结果
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywTkd&types=query&parm=' + JSON.stringify(this.param),
                function (data) {
                    if (data.a == "0") {
                        wrapper.jsonList = data.d.list;
                        wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
                        malert(data.c);
                    } else {
                        malert(data.c);
                    }

                });
        }

    }
});








