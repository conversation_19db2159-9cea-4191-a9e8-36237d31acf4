var bgd=new Vue({
    el:'.bgd',
    mixins: [dic_transform, baseFunc, tableBase, mformat,printer],
    data:{
    	jsonList:[],
    },
    methods:{
    	getData: function(){
			if (userNameBg.Brxx_List.ghxh==null ||userNameBg.Brxx_List.brid==null) {
                malert("请先选择病人","top","defeadted");
                return false;
            }
			bgd.jsonList = [];   //没找到记录，清空处方列表
			bgd.doPrintRep({}, [{}]);  //先请求一个空模板过来
			//后台查询数据
			var parm={
				bah: userNameBg.Brxx_List.ghxh,
				lx: '0'
			};
            $.getJSON("/actionDispatcher.do?reqUrl=mzysLisJydj&types=query&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                    	bgd.jsonList = data.d.list;
                    }
                } else {
                    malert("检验查询失败" + data.c,"top","defeadted");
                }
                parm={};
            })
		},

		//单击查询
		checkOne: function(index){
			var jyxh=bgd.jsonList[index].jyxh;
			var parm={
				jyxh: jyxh
			};
			$.getJSON("/actionDispatcher.do?reqUrl=mzysLisJydj&types=queryMx&parm=" + JSON.stringify(parm), function (data) {
			    console.log(data);
                if (data.a == 0) {
                	console.log(data.d);
                	data.d['xb']=bgdList.brxb_tran[data.d['xb']];
                	data.d['nldw'] = bgdList.nldw_tran[data.d['nldw']];
                	data.d['lx'] = bgdList.jydjlx_tran[data.d['lx']];
                	data.d['sqrq'] = bgdList.fDate(data.d['sqrq'], "date");
                	data.d['cyrq'] = bgdList.fDate(data.d['cyrq'], "date");
                	data.d['ybhsrq'] = bgdList.fDate(data.d['ybhsrq'], "date");
                	bgd.doPrintRep(data.d,data.d.jydjmxList);
                } else {
                    bgd.doPrintRep({}, [{}]);
                }
            })
		},

        doPrintRep: function (content, list) {
            // 查询打印模板
            var json = {repname: '检验报告单'};
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                if(json.d.length == 0){
                    json.d[0] = printTemplets.getTempletByName('检验报告单');
                }
                // 清除打印区域
                bgd.clearBgd(json.d[0]);
                // 绘制模板的canvas
                bgd.drawList = JSON.parse(json.d[0]['canvas']);
                bgd.creatCanvas();
                bgd.reDraw();
                // 为打印前生成数据
                bgd.printContent(content);
                bgd.printTrend(list);
            });
        },
        clearBgd: function (json) {
            $(".Template").html('');
            $(".Template").append(json['content']);
            $(".Template").append('<div style="page-break-before: avoid"></div>');
            this.isClearArea = true;
        }
    }
});
bgd.getData();