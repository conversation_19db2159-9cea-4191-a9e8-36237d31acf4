<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>入库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="rkgl.css" rel="stylesheet"/>
    <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="background-box">
    <div class="wrapper printHide" id="wrapper" v-cloak>
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kdFun(0)" v-show="isShowkd">开单</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kdFun(1)" v-if="isShowpopL">添加体外诊断试剂
                </button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)" v-show="!ShShow">刷新
                </button>
                <button class="tong-btn btn-parmary icon-sx1 paddr-r5" @click="goToPage(1)" v-show="ShShow">刷新</button>
                <!--<button class="tong-btn btn-parmary-b icon-sc-header paddr-r5" @click="clearAll()" v-show="TjShow">清空</button>-->
                <div class="flex-container flex-align-c">
                    <span class="color-wtg font-18">零价总额： {{totalyplj}}元&ensp;</span>
                </div>
                <div class="flex-container flex-align-c">
                    <span class="color-wtg font-18">进价总额： {{totalypjj}}元&ensp;</span>
                </div>
            </div>
            <div class="tong-search" :class="{'tong-padded':isShow}">
                <div class="flex-container padd-b-10 flex-align-c" v-show="isShowkd">
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">体外诊断试剂库</span>
                        <select-input class="wh122" @change-data="resultRydjChange" :child="KFList"
                                      :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm" :name="'param.kfbm'"
                                      :search="true" :index_mc="'kfmc'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c margin-l-20">
                        <span class="ft-14 padd-r-5 whiteSpace">供货单位</span>
                        <select-input class="wh182" @change-data="resultChange"
                                      :child="ghdwListQuery" :index="'dwmc'"
                                      :index_val="'dwbm'" :val="param.ghdw"
                                      :name="'param.ghdw'" :search="true">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace ">审核标志</span>
                        <select-input @change-data="resultChange"
                                      :child="ckglzt_tran"
                                      class="wh122"
                                      :index="param.zt"
                                      :val="param.zt"
                                      :name="'param.zt'">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-10">
                        <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                        <div class="  flex-container flex-align-c">
                            <input class="zui-input  wh160 " placeholder="请选择申请开始日期" id="timeVal"/>
                            <span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh160 " placeholder="请选择申请结束时间" id="timeVal1"/>
                        </div>
                    </div>
                    <div class="flex-container flex-align-c">
                        <span class="ft-14 padd-r-5 whiteSpace">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text"
                               v-model="param.parm"/>
                    </div>
                </div>
                <div class="jbxx" v-show="!isShowkd">
                    <div class="jbxx-size">
                        <div class="jbxx-position">
                            <span class="jbxx-top"></span>
                            <span class="jbxx-text">基本信息</span>
                            <span class="jbxx-bottom"></span>
                        </div>
                        <div class="flex-container flex-align-c padd-l-10 padd-t-20">
                            <div class="flex-container flex-align-c">
                                <span class="ft-14 padd-r-5 whiteSpace">库房</span>
                                <select-input class="wh122" @change-data="resultRydjChange" :child="KFList"
                                              :index="'kfmc'"
                                              :index_val="'kfbm'" :val="rkd.kfbm" :name="'rkd.kfbm'"
                                              :search="true" :index_mc="'kfmc'" :disable="jyinput">
                                </select-input>
                            </div>

                            <div v-show="false" class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">入库方式</span>
                                <select-input class="wh120" @change-data="resultChange" :not_empty="false"
                                              :child="rkfs_tran"
                                              :index="rkd.rkfs" :val="rkd.rkfs" :name="'rkd.rkfs'">
                                </select-input>
                            </div>
                            <!--<div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">采购方式</span>
                                <select-input class="wh120" @change-data="resultChange" :not_empty="false"
                                              :child="cgfs_tran"
                                              :index="rkd.cgrkfs" :val="rkd.cgrkfs" :name="'rkd.cgrkfs'"
                                              :search="true" :index_mc="'cgrkfs'" :disable="jyinput">
                                </select-input>
                            </div>
                        -->
                            <div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">采购员</span>
                                <select-input class="wh120" @change-data="resultChange" :child="cgryList"
                                              :index="'ryxm'" :not_empty="true"
                                              :index_val="'rybm'" :val="rkd.cgry" :search="true" :name="'rkd.cgry'"
                                              :disable="jyinput">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5 whiteSpace">供货单位</span>

                                <select-input class="wh182" @change-data="resultChangeReset"
                                                  :not_empty="true" :child="ghdwList" :index="'dwmc'"
                                                  :index_val="'dwbm'" :val="rkd.ghdw"
                                                  :name="'rkd.ghdw'" :search="true">
                                </select-input>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20" v-if="!csqxContent.N04003001200111">
                                <span class="ft-14 padd-r-5 whiteSpace ">发票号</span>
                                <input type="text" @mousewheel.prevent class="zui-input wh400" v-model="rkd.fphm"
                                       @keydown="nextFocus($event)" :disabled="jyinput"/>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20" v-if="!csqxContent.N04003001200111">
                                <span class="ft-14 padd-r-5  whiteSpace">企业发票号</span>
                                <input type="text" @mousewheel.prevent class="zui-input wh120" v-model="rkd.qyfphm"
                                       @keydown="nextFocus($event)" :disabled="jyinput"/>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20" v-if="!csqxContent.N04003001200111">
                                <span class="ft-14 padd-r-5  whiteSpace">发票日期</span>
                                <input type="text" @mousewheel.prevent class="zui-input wh120" v-model="rkd.fprq"
                                       @keydown="nextFocus($event)" :disabled="jyinput"/>
                            </div>
                            <div class="flex-container flex-align-c margin-l-20">
                                <span class="ft-14 padd-r-5  whiteSpace">备注说明</span>
                                <input type="text" class="zui-input wh120" v-model="rkd.bzms"
                                       @keydown="nextFocus($event)"
                                       @keydown.enter="kd(1)" :disabled="jyinput"/>
                            </div>
                        </div>
                        <div class="rkgl-kd">
                            <span>开单日期:<i v-text="fDate(zdrq,'datetime')"></i></span>
                            <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view">
            <!--入库列表-->
            <div class="zui-table-header" v-show="isShowkd">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>入库单号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>入库方式</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>供货单位名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>入库时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>发票号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>总零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>总进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>差价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>采购员</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>制单员</span></div>
                        </th>
                        <!--                        <th>-->
                        <!--                            <div class="zui-table-cell cell-xl"><span>SPD单号</span></div>-->
                        <!--                        </th>-->
                        <th>
                            <div class="zui-table-cell cell-s"><span>备注</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>状态</span></div>
                        </th>
                        <th class="cell-l">
                            <div class="zui-table-cell cell-l"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body " v-show="isShowkd" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in rkdList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'background-red':item.totalYpjj != null ?item.totalYpjj.indexOf('-') == 0 : 0}]"
                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','rkdList'],$event)"
                        :tabindex="$index" @dblclick="openDetail($index)">
                        <td class=" cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.rkdh">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="cgfs_tran[item.cgrkfs]">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="item.ghdwmc">供货单位名称</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'datetime')">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fphm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.totalYplj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.totalYpjj"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"
                                 v-text="parseFloat(item.totalYplj) - parseFloat(item.totalYpjj)">序号
                            </div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cgryxm">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zdyxm">序号</div>
                        </td>
                        <!--                        <td>-->
                        <!--                            <div class="zui-table-cell cell-xl" v-text="item.spdno">序号</div>-->
                        <!--                        </td>-->
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bzms">状态</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <span v-text="zhuangtai[item.shzfbz]"
                                      :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : item.shzfbz=='3' ? 'color-wtg':'' "></span>
                            </div>
                        </td>
                        <td class=" cell-l">
                            <div class="zui-table-cell cell-l flex-center padd-t-5">
                                <!--                                0 未审核，1已审核 2 作废 3 未通过-->
                                <span class="width30 title icon-sh" v-show="item.shzfbz == 0" data-gettitle="审核"
                                      @click="showDetail($index)"></span>
                                <span class="width30 title icon-js" v-show="item.shzfbz == 0" data-gettitle="作废"
                                      @click="invalidData($index)"></span>
                                <span class="width30 title icon-bj" v-show="item.shzfbz == '0' || item.shzfbz == '1'"
                                      data-gettitle="编辑" @click="editIndex($index)"></span>
                            </div>
                        </td>
                        <p v-if="rkdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-r" v-show="isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in rkdList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                            @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex1',true,$index)">

                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--end-->
            <!--添加体外诊断试剂-->
            <div class="zui-table-header" v-show="!isShowkd">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
						<th>
						    <div class="zui-table-cell cell-xl"><span>体外诊断试剂编码</span></div>
						</th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>体外诊断试剂名称</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>规格</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>批号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>生产日期</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>效期</span></div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>基药</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-xl"><span>商品名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>产地</span></div>
                        </th>
                        
                        <th>
                            <div class="zui-table-cell cell-s"><span>入库数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>已冲销数量</span></div>
                        </th>
                        <th v-if="cxShow">
                            <div class="zui-table-cell cell-s"><span>冲销数量</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>体外诊断试剂进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>体外诊断试剂总进价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>体外诊断试剂零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>体外诊断试剂总零价</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>体外诊断试剂差价</span></div>
                        </th>
                        
                        <th>
                            <div class="zui-table-cell cell-s"><span>随货单据号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>发票号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>企业发票号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>供货单位</span></div>
                        </th>
                        <th v-show="!csqxContent.N04003001200111">
                            <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                        </th>
                        <th class="cell-s">
                            <div class="zui-table-cell cell-s"><span>操作</span></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body  " v-if="!isShowkd" @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList"
                        :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                        @mouseenter="switchIndex('activeIndex1',true,$index)" @mouseleave="switchIndex()"
                        @click="switchIndex('activeIndex1',true,$index)"
                        :tabindex="$index">
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                        </td>
						<td>
						    <div class="zui-table-cell cell-xl text-over-2 text-left">{{item.ypbm}}</div>
						</td>
                        <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left">{{item.ypmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left">{{item.ypgg}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.scph"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{fDate(item.scrq,'date')}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">{{getYearFun(item.yxqz) ? fDate(item.yxqz,'date') :
                                ''}}
                            </div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="istrue_tran[item.gjjbyw]">基药</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl text-over-2 text-left" v-html="item.ypspm">商品名</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s text-over-2" v-text="item.cdmc">产地</div>
                        </td>
                        
                        <td>
                            <div class="zui-table-cell cell-s">{{item.rksl}}{{item.kfdwmc}}</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s  " v-html="item.ycxsl"></div>
                        </td>
                        <td v-if="cxShow">
                            <div class="zui-table-cell cell-s "><input class="zui-input height-28"
                                                                       @input="getCxsl(item.rksl,item.cxsl,item.ycxsl,$index)"
                                                                       :disabled="item.ycxsl >= item.rksl"
                                                                       v-model="item.cxsl"/></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,4)">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj * item.rksl,2)">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,4)">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="fDec(item.yplj * item.rksl,4)">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s"><span class="colorRed" v-if="item.yplj-item.ypjj<0">{{fDec(item.yplj-item.ypjj,4)}}</span><span
                                    v-else>{{fDec(item.yplj-item.ypjj,4)}}</span></div>
                        </td>
                        
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.shdjh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.fphm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.qyfphm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.ghdwmc"></div>
                        </td>
                        <td v-if="!csqxContent.N04003001200111">
                            <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                        </td>
                        <td class="cell-s">
                            <div class="zui-table-cell cell-s flex-center padd-t-5">
                                <span class="icon-bj width30 title icon-bj-t" v-if="mxShShow" data-gettitle="编辑"
                                      @click="edit($index)"></span>
                                <span class="icon-sc width30  title" v-if="mxShShow && !rkd.shzfbz" data-gettitle="删除"
                                      @click="scmx($index)"></span>
                            </div>
                        </td>
                        <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-fixed table-fixed-l" v-if="!isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                            @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex1',true,$index)">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-fixed table-fixed-r" v-if="!isShowkd">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-s">
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2"
                            :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                            @mouseenter="switchIndex('hoverIndex1',true,$index)" @mouseleave="switchIndex()"
                            @click="switchIndex('activeIndex1',true,$index)">
                            <td class="cell-s">
                                <div class="zui-table-cell cell-s flex-center padd-t-5">
                                    <span class="icon-bj width30 title icon-bj-t" v-if="mxShShow" data-gettitle="编辑"
                                          @click="edit($index)"></span>
                                    <span class="icon-sc width30  title" v-if="mxShShow && (rkd.shzfbz == '0' || !rkd.shzfbz) "
                                          data-gettitle="删除" @click="scmx($index)"></span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <page @go-page="goPage" v-if="isShowkd" :totle-page="totlePage" :page="page" :param="param"
                  :prev-more="prevMore" :next-more="nextMore"></page>
            <div class="zui-table-tool flex-container padd-l-10 padd-r-10 flex-jus-sb" v-if="!isShowkd">
                    <span class="flex-container">
                        <i class="padd-r-10">体外诊断试剂进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></i>
                        <i>体外诊断试剂零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
                    </span>
                <span class="flex-container">
                        <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print" v-if="dyShow">打印</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick"
                                v-if="isCx(rkd) && dyShow">冲销</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick"
                                v-if="!dyShow && cxShow && isCx(rkd)">取消冲销</button>
                        <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-if="zfShow"
                                id="zfbtn">作废</button>
                        <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()"
                                v-if="TjShow && isCx(rkd)">提交</button>
                        <button class="tong-btn btn-parmary xmzb-db" @click="passData()" v-if="ShShow && isShFun(rkd)">审核</button>
                    </span>
            </div>
        </div>

    </div>
</div>
<!--侧边窗口-->
<div class="side-form  pop-805" :class="{'ng-hide':index==0}" v-cloak id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">
            <!--<li class="auto-focus">-->
                <!--<i>供货单位</i>-->
                <!--<select-input  @change-data="resultChange" :not_empty="false"-->
                              <!--:child="ghdwList"-->
                              <!--:index="'dwmc'" :val="popContent.ghdw" :name="'popContent.ghdw'"-->
                              <!--:search="true" :index_val="'dwbm'">-->
                <!--</select-input>-->
            <!--</li>-->
            <li>
                <i>体外诊断试剂名称</i>
                <input autocomplete="off" id="ypmc" class="zui-input" ref="ypmc" v-model="popContent.ypmc"
                       @keydown="changeDown($event,$event.target.value,'text')"
                       @input="change(false, $event.target.value)">
                <search-table :page="queryStr" :message="searchCon" :selected="selSearch" :them="them"
                              :them_tran="them_tran"
                              :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>

            </li>
            <li>
                <i>商品名</i>
                <input type="text" class="zui-input " v-model="popContent.ypspm" disabled @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>体外诊断试剂规格</i>
                <input type="text" class="zui-input background-h" disabled="disabled" v-model="popContent.ypgg"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>体外诊断试剂种类</i>
                <input type="text" class="zui-input background-h" disabled="disabled" v-model="popContent.ypzlmc"
                       @keydown="nextFocus($event)"/>
            </li>
            <li>
                <i>入库数量</i>
                <input :disabled="shDisabled" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent
                       type="number" id="rksl" class="zui-input" v-model="popContent.rksl"
                       @keydown="nextFocus($event,'',true)"
                       @change="watchChange"
                />
                <em v-text="popContent.kfdwmc" class="cm"></em>
            </li>

            <li>
                <i>生产批号</i>
                <input :disabled="shDisabled" type="text" class="zui-input" v-model="popContent.scph"
                       @keydown="nextFocus($event,'',true)"/>
            </li>
			<li>
			    <i>生产日期</i>

			    <input autocomplete="off"
			            type="text" class="zui-input"
			           v-model="fDate(popContent.scrq,'date')"
			           @keydown="nextFocus($event)"
			           @change="watchTimeFormat($event,'scrq')"
			    />
			</li>
			<li>
			    <i>有效期至</i>
			    <input type="text" autocomplete="off"
			           class="zui-input"
			           v-model="fDate(popContent.yxqz,'date')"
			           @blur="validity($event.target.value)"
			           @change="watchTimeFormat($event,'yxqz')"
					   @keydown="nextFocus($event)"
			    />
			</li>
            <li>
                <i>随货单据号</i>
                <input type="text" class="zui-input" @keydown="nextFocus($event)"
                       v-model="popContent.shdjh">
            </li>
            <!--<li>-->
                <!--<i>发票号</i>-->
                <!--<input type="text" class="zui-input" @keydown="nextFocus($event)"-->
                       <!--v-model="popContent.fphm">-->
            <!--</li>-->
            <!--<li>-->
                <!--<i>企业发票号</i>-->
                <!--<input type="text" class="zui-input" @keydown.enter="addData"-->
                       <!--v-model="popContent.qyfphm">-->
            <!--</li>-->

            <li>
                <i>体外诊断试剂进价</i>
                <input :disabled="!0" type="number" class="zui-input "
                       id="ypjj" @input="getXlj($event.target.value)" @change="getXlj($event.target.value)"
                       :value="popContent.ypjj" @keydown="nextFocus($event,'',true)"/>
            </li>
            <li>
                <i>体外诊断试剂零价</i>
                <input :disabled="!0" type="number" class="zui-input"
                       id="yplj" v-model.number="popContent.yplj" @blur="setYplj(popContent.yplj)"
                       @keydown="nextFocus($event,'',true)"
                       @change="watchChange"
                />
            </li>

            <li>
                <i>体外诊断试剂进价<br/>总&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;价</i>
                <input type="number" class="zui-input " disabled :value="popContent.total_ypjj"/>
            </li>
            <li>
                <i>体外诊断试剂零价<br/>总&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;价</i>
                <input type="number" class="zui-input" disabled :value="popContent.total_yplj"/>
            </li>
            <li>
                <i>体外诊断试剂总价<br/>差&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;额</i>
                <input type="number" class="zui-input" disabled :value="popContent.total_diff"/>
            </li>
            <!--<li>
                <i>二级库房进价</i>
                <input type="number" class="zui-input " :disabled="isQTRK" id="yfypjj"
                       @input="getXlj($event.target.value)" @change="getXlj($event.target.value)"
                       :value="popContent.yfypjj" @keydown="nextFocus($event,'',true)"/>
            </li>
            <li>
                <i>二级库房零价</i>
                <input type="number" class="zui-input" :disabled="isQTRK" id="yfyplj" v-model.number="popContent.yfyplj"
                       @blur="setYplj(popContent.yfyplj)" @keydown="nextFocus($event,'',true)"/>
            </li>-->
           <li>
                <i>产地</i>
                <input disabled id="ypcd" ref="cdmc" class="zui-input" v-model="popContent.cdmc"
                       @keydown="changeDown1($event,$event.target.value)"
                       @input="change1(false, $event.target.value)">
                <search-table :page="queryStr" :message="searchCon1" :selected="selSearch1" :them="them1"
                              :them_tran="them_tran1" :current="dg.page" :rows="dg.rows" @click-one="checkedOneOut"
                              @click-two="selectOne1" @keydown="nextFocus($event)">
                </search-table>
            </li>
            <li>
                <i>是否基药</i>
                <!-- <select-input @change-data="resultChange" :not_empty="true"
                          :child="istrue_tran" :index="popContent.jzgh" :val="popContent.jzgh"
                          :name="'popContent.jzgh'" :search="true" :disable="false">
            </select-input> -->
                <input type="text" class="zui-input" disabled="disabled" @keydown="nextFocus($event)"
                       v-model="istrue_tran[popContent.gjjbyw]">
            </li>
            <!--<li>-->
            <!--<i>二级库房单位</i>-->
            <!--<input type="text" class="zui-input" disabled="disabled" @keydown="nextFocus($event)" v-model="popContent.yfdwmc">-->
            <!--</li>-->
            <li>
                <i>分装比例</i>
                <input type="text" class="zui-input" disabled="disabled" @keydown="nextFocus($event)"
                       v-model="popContent.fzbl">
            </li>

            <li>
                <i>产&ensp;品&ensp;标准&ensp;&ensp;&ensp;&ensp;号</i>
                <input type="text" class="zui-input" :disabled="isQTRK2" @keydown="nextFocus($event)"
                       v-model="popContent.cpbzh">
            </li>
            <li>
                <i>批准文号</i>
                <input type="text" class="zui-input" disabled @keydown="nextFocus($event)" v-model="popContent.pzwh">
            </li>
            <li>
                <i>合格证号</i>
                <input type="text" class="zui-input" :disabled="isQTRK2" @keydown="nextFocus($event)"
                       v-model="popContent.hgzh">
            </li>
            <li>
                <i>外观质量</i>
                <select-input @change-data="resultChange" :disable="isQTRK2" :child="wgzlList"
                              :index="'popContent.wgzl'" :val="popContent.wgzl"
                              :name="'popContent.wgzl'">
                </select-input>
            </li>
            <li>
                <i>验收结论</i>
                <select-input @change-data="resultChange" :disable="isQTRK2" :child="ysjlList"
                              :index="'popContent.ysjl'" :val="popContent.ysjl"
                              :name="'popContent.ysjl'">
                </select-input>
            </li>
            <li v-show="popContent.cgrkfs == '00'">
                <i>招标方式</i>
                <select-input @change-data="resultZbfsChange" :disable="isQTRK2" :child="zbfsList" :index="'zbfsmc'"
                              :index_val="'zbfsbm'"
                              :val="popContent.zbfsbm" :name="'popContent.zbfsbm'">
                </select-input>
            </li>
            <li v-show="popContent.cgrkfs == '00'">
                <i>加成比例</i>
                <input type="text" class="zui-input" :disabled="isQTRK2" @keydown="nextFocus($event)"
                       v-model="popContent.jcbl">
            </li>

        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="addData">保存</button>
    </div>
</div>
<script src="rkgl.js?v=2"></script>
</body>

</html>
