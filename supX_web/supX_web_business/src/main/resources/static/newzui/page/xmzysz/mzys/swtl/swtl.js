var hzList=new Vue({
    el:'#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat,scrollOps],
    data:{
        popContent:{},
        defaltObj:{
            title:'死亡讨论邀请',
            cs:'background-f4b26b color-8e9694',
            cb:'拒绝',
            sb:'接受'
        },
        refuseObj:{
            title:'拒绝原因',
        },
    },
    updated:function(){
        changeWin()
    },
    methods:{
        fqtl:function () {
            brzcList1.type=false
        },
        newOpenPage(){
            this.topNewPage('死亡讨论-认不到','page/zyysz/bqgl/swtl/swtlUser/hzgl.html')//为了方便维护都在同一个目录
            // this.topNewPage('死亡讨论-认不到','page/xmzysz/mzys/swtl/swtlUser/hzgl.html')
        },
        accept(){
            common.openConfirm('确定接受患者：<span class="color-green">'+"李浩然"+'</span>的死亡讨论吗？',function () {
            },function () {
                    common.openConfirm('<textarea placeholder="请输入拒绝原因" class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="refuseText"></textarea>',function () {

                    },function () {

                    },hzList.refuseObj)
            },this.defaltObj)
        },
        refuse(){
            common.openConfirm('确定接受患者：<span class="color-green">'+"李浩然"+'</span>的死亡讨论吗？',function () {

            },function () {
                   common.openConfirm('<textarea placeholder="请输入拒绝原因" class="zui-textarea" style="width: 100%;height: 92px;text-indent:0;" id="refuseText"></textarea>',function () {

                   },function () {

                   },hzList.refuseObj)
            },this.defaltObj)
        },
    },
})
var brzcList=new Vue({
    el:'#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        type:true,
        popContent:{}
    },
    create(){

    },
    methods:{
        fqtl:function () {

        },
        getData:function(){

        },
        submit:function () {
            this.type=true
        },
        close:function () {
            this.type=true
        }
    },
})
var brzcList1=new Vue({
    el:'#brzcList1',
    mixins: [dic_transform, baseFunc, tableBase, mConfirm, mformat],
    data:{
        type:true,
        objAvatar:5,
        shsj:'',
        popContent:{}
    },
    create(){

    },
    mounted(){
        this.popContent.shsj =this.fDate(new Date(),'date')+' '+this.fDate(new Date(),'times');
        console.log(this.shsj);
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    brzcList1.popContent.shsj = value;
                } else {
                    brzcList1.popContent.shsj = '';
                }
            }
        });
    },
    methods:{
        tjys:function(){
            brzcList.type=false
        },
        fqtl:function () {
        },
        close:function(){
            this.type=true
        },
        submit:function(){
            this.type=true
        },
        getData:function(){

        },
    },
})
