//
var brxx = new Vue({
        el: '#jbxx',
        mixins: [dic_transform, baseFunc, tableBase, mformat, checkData],
        components: {
            'search-table': searchTable,
        },
        data: {
            zysearchCon: [],
            gjList: [],
            saveShow: true,
            editTitle: '编辑',
            lxrgxList: [], //联系人关系
            zyList: [], //职业下拉框
            zdxx: {sfcrb: '0'}, //诊断信息
            fzxx: {},//分诊信息
            tsdw: '天',
            zcxx: userNameBg.Brxx_List, //注册信息
            selSearch: -1,
            selSearch1: -1,
            selSearch2: -1,
            selSearch3: -1,
            selSearch4: -1,
            selSearch5: -1,
            selSearch6: -1,
            selSearch7: -1,
            selSearch8: -1,
            selSearch9: -1,
            selSearch10: -1,
            selSearch11: -1,
            changeType: false,
            page: {
                page: 1,
                rows: 30,
                total: null
            },
            zythem: {'中医疾病编码': 'jbbm', '中医疾病名称': 'jbmc', '拼音代码': 'pydm'},
            //中医
            zyContent: {},
            allBmContent: {},
            page1: {page: 1, rows: 10, total: null},
            //下拉table初步诊断信息检索选中对象
            jbbmContent: {},
            searchCon: [],
            them: {
                '疾病编码': 'jbmb',
                '疾病名称': 'jbmc',
                '慢病': 'mbbz',
                '重大疾病': 'zdjb',
                '拼音代码': 'pydm'
            },
            trem_tran: {
                'mbbz': dic_transform.data.istrue_tran,
                'zdjb': dic_transform.data.istrue_tran
            },
            sfQz_tran: {
                "1": "疑诊",
                "0": "确诊"
            },
            //疾病编码下拉table
            qtzdbmContent: {},
            popContent: {},
            jbsearchCon: [],
            jbthem: {'疾病编码': 'jbmb', '疾病名称': 'jbmc', '拼音代码': 'pydm'},
            ybjbbmContent: {},
            ybsearchCon: [],
            ybthem: {
                '疾病编码': 'bm',
                '疾病名称': 'mc',
                '代码': 'dm'
            },
            clContent: {},
            clsearchCon: [],
            clthem: {
                '对应处理': 'clmc',
            },
            tzContent: {},
            tzsearchCon: [],
            tzthem: {
                '症状体征': 'zzmc',
            },
            dgparm: {
                rows: 20000
            },
            clxzbz: true,
            tzxzbz: true,
            bxurl: null,
            bxlbbm: null,
            billCode: null,
            mzList: [],
            gsjbpxBm: window.top.J_tabLeft.obj.gsjbpxBm,
            hyList: [],
            provinceList: [],
            cityList: [],
            countyList: [],
            lsghxxList: [], //历史挂号信息
            ghxh: null,
            mc: '',
            bm: '',
            csqxContent: fyxmTab.csqx,
            isBshow: true,
            fbbm: userNameBg.Brxx_List.fbbm,
            mtbzlist: [],
            jyrqlx_tran: {
                "1": "新冠肺炎确诊患者",
                "2": "其他国家突发公共事件",
                "3": "新冠肺炎疑似患者",
                "4": "无症状感染者",
            },
            mjzzzbz_tran: {
                "1": "急诊",
                "2": "转诊",
                "3": "转诊合并急诊"
            },
            fzjb: {
                '1': 'Ⅰ',
                '2': 'Ⅱ',
                '3': 'Ⅲ',
                '4': 'Ⅳ'
            },
        },

        mounted: function () {
            this.getBrbz();
            if (this.csqxContent.N03001200146 && this.csqxContent.N03001200146 == '1') {
                this.getbxlb();
            }
            this.csqxContent.N01006400112 = window.top.J_tabLeft.obj.ygjc;
            this.csqxContent.N01006400113 = window.top.J_tabLeft.obj.ygyycx;
            this.csqxContent.N01006400135 = window.top.J_tabLeft.obj.crbsbId;
            this.csqxContent.N01006400202 = window.top.J_tabLeft.obj.scburl;

            this.getDate(); //初始化页面加载病人信息
            this.GetZybmData();//病人职业下拉框
            this.GetlxrgxData(); //联系人关系下拉框
            this.readyData("gjbm", "gjList");
            this.readyData("mzbm", "mzList");
            this.readyData("hyzk", "hyList");
            // this.readyData("hyzk", "hyList");
            this.readyData1({"xzqhlx": "1"}, "xzqh", "provinceList");
            this.getFzByGhxh();
        },
        created: function () {
            this.zcxx = JSON.parse(JSON.stringify(userNameBg.Brxx_List))
            this.$forceUpdate()
        },
        methods: {
            //通过挂号序号获取分诊信息
            getFzByGhxh: function () {
                let parm = {"ghxh": userNameBg.Brxx_List.ghxh}
                this.$http.post('http://localhost:6001/getFzByGhxh', JSON.stringify(parm)).then(function (res) {
                    if (res.body.success) {
                        brxx.fzxx = res.body.data;
                        if (!this.zdxx.xtong && brxx.fzxx.xtong)
                            this.zdxx.xtong = brxx.fzxx.xtong
                        if (!this.zdxx.xtms && brxx.fzxx.xtms)
                            this.zdxx.xtms = brxx.fzxx.xtms
                        if (!this.zdxx.xt && brxx.fzxx.xt)
                            this.zdxx.xt = brxx.fzxx.xt
                        if (!this.zdxx.tw && brxx.fzxx.tw)
                            this.zdxx.tw = brxx.fzxx.tw
                        if (!this.zdxx.hx && brxx.fzxx.hx)
                            this.zdxx.hx = brxx.fzxx.hx
                        if (!this.zdxx.mb && brxx.fzxx.mb)
                            this.zdxx.mb = brxx.fzxx.mb
                        if (!this.zdxx.xl && brxx.fzxx.xl)
                            this.zdxx.xl = brxx.fzxx.xl
                        if (brxx.fzxx.xy) {
                            let split = brxx.fzxx.xy.split("/");
                            if (!this.zdxx.xySsy && split[0]) {
                                this.zdxx.xySsy = split[0]
                            }
                            if (!this.zdxx.xySzy && split[1]) {
                                this.zdxx.xySzy = split[1]
                            }
                        }

                        this.$forceUpdate()
                    }
                });
            },
            mtbzhq: function () {
                console.log(1111)
            },
            openFjzd: function () {
                fjzdPop.fjzdShow = true;
                if (Array.isArray(this.zdxx.fjzd)) {
                    fjzdPop.fjzd = this.zdxx.fjzd || [{}];
                } else {
                    fjzdPop.fjzd = this.zdxx.fjzd && JSON.parse(this.zdxx.fjzd) || [{}];
                }
            },
            fbts: function () {
                if (this.zdxx.fbts == '') {
                    this.zdxx.fbrq = this.zdxx.fbrq1;
                    return
                }
                ;
                if (this.zdxx.fbrq) {
                    var myDate = new Date(this.zdxx.fbrq);
                } else {
                    var myDate = new Date();
                }
                this.zdxx.fbrq = this.fDate(myDate.setDate(myDate.getDate() - parseInt(this.zdxx.fbts)), 'date');
            },
            //鼠标双击（中医）
            zyselectOne: function (item) {
                if (item == null) {
                    this.page.page++;
                    this.change1(true, allBm);
                } else {
                    var str = item.jbmb;
                    //当诊断编码有S00-T98时，损伤的外部原因编码不能为空
                    if (str >= "S00" && str <= "T98") {
                        brxx.pdSsyyIfNull = brxx.pdSsyyIfNull + 1;
                    }
                    Vue.set(this.zdxx, allBm, item['jbbm']);
                    Vue.set(this.zdxx, mc, item['jbmc']);
                    $(".selectGroup").hide();
                }
            },
            change1: function (add, type, val, event, selSearch) {
                if (!add) this.page.page = 1;
                this.changeType = true;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.zdxx[type] = val;
                this.page.parm = val;
                var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
                // 1-证；2-病
                if (type == 'zyzf') {
                    str_param.lx = '1';
                } else if (type == 'zyzh') {
                    str_param.lx = '2';
                }
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbzy' + '&json=' + JSON.stringify(str_param),
                    function (data) {
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                brxx.zysearchCon.push(data.d.list[i]);
                            }
                        } else {
                            brxx.zysearchCon = data.d.list;
                        }
                        brxx.page.total = data.d.total;
                        brxx[selSearch] = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        }
                    });
            },

            //检索
            changeDown1: function (event, type, content, searchCon, modelBm, showBm, modelMc, selSearch, text) {
                //全局变量
                bm = modelBm;
                allBm = showBm;
                mc = modelMc;
                console.log(111)
                // this.keyCodeFunction(event,content,searchCon);
                this.inputUpDown(event, this[searchCon], selSearch);
                this[content] = this[searchCon][this[selSearch]]
                var types = {
                    // 'zy1':'主病',
                    // 'zyBz1':'症型'
                }
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (this.changeType) {
                        // if(types[type] && this[content] == undefined){
                        //     return malert('请严格按照国家政策填写'+types[type]+'信息','top','defeadted')
                        // };
                        if (brxx.zyContent) {
                            var str = brxx.zyContent['jbbm'];
                            if (str >= "S00" && str <= "T98") {
                                brxx.pdSsyyIfNull = brxx.pdSsyyIfNull + 1;
                            }
                            Vue.set(this.zdxx, showBm, this.zyContent['jbbm']);
                            Vue.set(this.zdxx, modelMc, this.zyContent['jbmc']);
                        }
                    }
                    //当诊断编码有S00-T98时，损伤的外部原因编码不能为空
                    this.$forceUpdate()
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                    // this[content]={}
                    this[selSearch] = -1
                }
            },
            //公用查询
            readyData: function (types, listName) {
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: types,
                    }
                }).then(function (json) {
                    if (json.body.a == 0) {
                        brxx[listName] = json.body.d.list;
                    } else {
                        malert(types + "查询失败", 'top', 'defeadted');
                        return false;
                    }
                });
            },
            readyData1: function (req, types, listName) {
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: types,
                        json: JSON.stringify(req)
                    }
                }).then(function (json) {
                    if (json.body.a == 0) {
                        brxx[listName] = json.body.d.list;
                    } else {
                        malert(types + "查询失败", 'top', 'defeadted');
                        return false;
                    }
                })
            },
            resulwhChange: function (val) {
                if (val[2].length > 1) {
                    if (Array.isArray(this[val[2][0]])) {
                        Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    } else {
                        Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                        if (val[3] != null) {
                            Vue.set(this[val[2][0]], val[3], val[4]);
                        }
                    }
                } else {
                    this[val[2][0]] = val[0];
                }
                this.$forceUpdate()
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case 'whsfhbfh':
                        if (brxx.zdxx.whsfhbfh == '0') {
                            Vue.set(brxx.zdxx, "whhbfhsj", '');
                        }
                        break;
                    case 'wh14jzfy':
                        if (brxx.zdxx.wh14jzfy == '0') {
                            Vue.set(brxx.zdxx, "wh14jzfysj", '');
                        }
                        break;
                    case 'wh14jzhbr':
                        if (brxx.zdxx.wh14jzhbr == '0') {
                            Vue.set(brxx.zdxx, "wh14jzhbrsj", '');
                        }
                        break;
                    case 'wh14sqfrjz':
                        if (brxx.zdxx.wh14sqfrjz == '0') {
                            Vue.set(brxx.zdxx, "wh14sqfrjzsj", '');
                        }
                        break;
                }
            },
            resultzcChange: function (val) {
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case 'jzdsheng':
                        Vue.set(this.zcxx, "jzdsheng", val[0]);
                        Vue.set(this.zcxx, "jzdshengmc", val[4]);
                        this.shi(val[1])
                        break;
                    case 'jzdshi':
                        Vue.set(this.zcxx, "jzdshi", val[0]);
                        Vue.set(this.zcxx, "jzdshimc", val[4]);
                        this.qu(val[1]);
                        break;
                    case 'jzdxian':
                        Vue.set(this.zcxx, "jzdxian", val[0]);
                        Vue.set(this.zcxx, "jzdxianmc", val[4]);
                        this.nextFocus(val[1])
                        break;
                    case 'hyzk':
                        Vue.set(this.zcxx, "hyzkbm", val[0]);
                        Vue.set(this.zcxx, "hyzk", val[0]);
                        Vue.set(this.zcxx, "hyzkmc", val[4]);
                        this.nextFocus(val[1])
                        break;
                    case 'zybm':
                        Vue.set(this.zcxx, "zybm", val[0]);
                        Vue.set(this.zcxx, "zybmmc", val[4]);
                        this.nextFocus(val[1])
                        break;
                    case 'lxrgx':
                        Vue.set(this.zcxx, "lxrgx", val[0]);
                        Vue.set(this.zcxx, "lxrgxbm", val[0]);
                        Vue.set(this.zcxx, "lxrgxmc", val[4]);
                        this.nextFocus(val[1])
                        break;

                }
            },
            shi: function (event) {
                brxx.cityList = [];
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"2","sjbm":"' + this.zcxx.jzdsheng + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                    if (json.a == 0) {
                        brxx.cityList = json.d.list;
                        if (event) {
                            brxx.$nextTick(function () {
                                brxx.nextFocus(event)
                            })
                        }
                    } else {
                        malert("市编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                        return false;
                    }
                });
            },
            qu: function (event) {
                brxx.countyList = [];
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=xzqh&json={"xzqhlx":"3","sjbm":"' + this.zcxx.jzdshi + '"}&dg=' + JSON.stringify(this.dgparm), function (json) {
                    if (json.a == 0) {
                        brxx.countyList = json.d.list;
                        if (event) {
                            brxx.$nextTick(function () {
                                brxx.nextFocus(event)
                            })
                        }
                    } else {
                        malert("县编码下拉列表查询失败：" + json.c, 'top', 'defeadted');
                        return false;
                    }
                });
            },
            //点击编辑按钮
            edit: function () {
                this.editTitle = '确定';
                this.saveShow = false;
                setTimeout(function () {
                    $('#timeVal').focus()
                }, 0)
            },
            getBrbz: function () {
                console.log(111111111111)
                this.mtbzlist = [];
                if (this.fbbm != '40' && this.fbbm != '41') {
                    return false;
                }
                let param = {
                    parm: '',
                    ghxh: userNameBg.Brxx_List.ghxh,
                    page: 1,
                    rows: 9999,
                }
                let that = this;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbMT'
                    + '&json=' + JSON.stringify(param),
                    function (data) {
                        console.log(data)
                        if (data.a == '0' && data.d.list.length > 0) {
                            that.mtbzlist = data.d.list
                            that.$forceUpdate()
                        }
                    });
            },
            printBl: function () {
                var reportlets = "[{reportlet: 'fpdy%2Fmzys%2Fmzys_mzbl.cpt',ghxh:'" + userNameBg.Brxx_List.ghxh + "'}]";
                if (FrPrint(reportlets)) {
                    return;
                }
            },
            //点击接诊模板编辑
            editModel: function () {
                jzMbPop.popShow = true;
                jzMbPop.popContent = Object.assign({}, brxx.zdxx);
                jzMbPop.popContent.mc = '';
                jzMbPop.popContent.pydm = '';
                jzMbPop.popContent.id = '';
                jzMbPop.popContent.lx = jzMbPop.popContent.lx == undefined || jzMbPop.popContent.lx == null ? '1' : jzMbPop.popContent.lx;
            },
            QuoteModel: function () {
                Quote.num = 0;
                Quote.getData();
            },
            historyJzxx: function () {
                jzxx.num = 0;
                jzxx.getData();
            },
            openPacs: function () {
                window.open(window.top.J_tabLeft.obj.dybgurl + "/xds/index.php?appuser=1&type=hiscode1&value=" + userNameBg.Brxx_List.ghxh);
            },
            //院感上报
            ygsbUrl: function () {
                var zyh = userNameBg.Brxx_List.ghxh;
                if (window.top.J_tabLeft.obj.ygjc == '1') {
                    var password = '123'//JSON.parse(this.getcookie("user_info"+userId)).password
                    location.href = "SSOAPP" + window.top.J_tabLeft.obj.ygyycx + ":" + userId + "|" + password + "|" + zyh + "|0";
                }
            },
            //传染病上报
            crbsbUrl: function () {
                var zyh = userNameBg.Brxx_List.ghxh;
                if (window.top.J_tabLeft.obj.ygjc == '1' && window.top.J_tabLeft.obj.crbsbId) {
                    var password = '123'//JSON.pygjcarse(this.getcookie("user_info"+userId)).password
                    location.href = "SSOAPP" + window.top.J_tabLeft.obj.crbsbId + ":" + userId + "|" + password + "|" + zyh + "|0|0";
                }
            },
            crbOpen: function () {
                // var str='&BRID='+userNameBg.Brxx_List.brid+'&PatientName='+userNameBg.Brxx_List.brxm+'&ZD='+userNameBg.Brxx_List.jbmc+'&IDCard='+userNameBg.Brxx_List.sfzh+'&HospitalCode='
                // var req = brxx.csqxContent.N01006400202+str;
                // var parm={
                //     req:req
                // };
                // this.$http.post("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=yocrb",JSON.stringify(parm)).then(function (json) {
                // });

                var crbParm = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                    "  <soap:Body>\n" +
                    "    <CheckCRB xmlns=\"http://tempuri.org/\">\n" +
                    "      <BRID>" + userNameBg.Brxx_List.ghxh + "</BRID>\n" +
                    "      <PatientName>" + userNameBg.Brxx_List.brxm + "</PatientName>\n" +
                    "      <ZD>" + userNameBg.Brxx_List.jbmc + "</ZD>\n" +
                    "      <IDCard>" + userNameBg.Brxx_List.sfzh + "</IDCard>\n" +
                    "      <HospitalCode></HospitalCode>\n" +
                    "    </CheckCRB>\n" +
                    "  </soap:Body>\n" +
                    "</soap:Envelope>";

                $.ajax({
                    type: 'post',
                    url: brxx.csqxContent.N01006400202, // http://192.168.0.115:8081/WebService.asmx
                    data: crbParm,
                    contentType: 'text/xml; charset=utf-8',
                    beforeSend: function (xhr) {
                    },
                    // dataType:"xml",
                    success: function (data) {
                        var parser = new DOMParser();
                        var checkCRBResult = parser.parseFromString(data.children[0].textContent, "text/xml");
                        var isBk = checkCRBResult.children[0].children[0].textContent;
                        var remark = checkCRBResult.children[0].children[1].textContent;
                        console.log("isBk:" + isBk + " remark:" + remark);
                        if (isBk == 'Y') {
                            // 中华人民共和国传染病报告卡
                            common.openConfirm("诊断【" + userNameBg.Brxx_List.jbmc + "】属于传染病，是否上报?", function () {
                                brxx.crbsbUrl();
                            }, function () {
                            });
                        }
                    }
                });
            },
            //保存
            save: function () {
                $.ajaxSettings.async = false;
                /*if (brxx.zdxx.brnl < 14) {
                    if (brxx.zcxx.lxrxm == null) {
                        malert("病人年龄小于14，家长姓名必填",'top','defeadted');
                        return false;
                    }
                }*/
                /*
                 if(brxx.zdxx.sfgm==1){
                     if(!brxx.zdxx.gms){
                         malert("判定为过敏时过敏史为必填",'top','defeadted');
                         return false;
                     }
                 }
                 */
                //根据病人年龄判断是否填写血压信息
                if (this.csqxContent.N05001200253 == '1') {
                    if (this.zdxx.nldw == '1' && this.zdxx.brnl >= '35'
                        && (this.zdxx.xySsy == null || this.zdxx.xySzy == null)) {
                        if (mconfirm('35岁以上病人请填写血压信息')) {
                            return false
                        }
                    }
                } else if (this.csqxContent.N05001200253 == '2') {
                    if (this.zdxx.nldw == '1' && this.zdxx.brnl >= '35'
                        && (this.zdxx.xySsy == null || this.zdxx.xySzy == null)) {
                        malert("35岁以上病人请填写血压信息!", 'top', 'defeadted');
                        return;
                    }
                }
                if (brxx.zdxx.jbbm == '' || brxx.zdxx.jbbm == undefined) {
                    malert("请输入诊断疾病", 'top', 'defeadted');
                    return false;
                }
                // 提交前验证数据
                if (!this.empty_sub('jbxxcontent')) {
                    return false;
                }
                // 病人是否发热为是时，发热情况必填
                if (this.zdxx.sffrbr == '1' && !this.zdxx.frqk) {
                    malert("请输入发热情况", 'top', 'defeadted');
                    return false;
                }
                if (this.zdxx.sffrbr == '0' && this.zdxx.frqk) {
                    this.zdxx.frqk = '';
                }

                if (!this.zdxx.gms) {
                    this.zdxx.sfgm == 0;
                } else {
                    this.zdxx.sfgm == 1;
                }
                if (this.tzxzbz) {
                    // brxx.zdxx.zyzztz = brxx.tzContent.zyzztz;
                    this.setPYDM(this.zdxx.zyzztz, 'zdxx', 'pydm');
                    var zztz = {
                        zzmc: this.zdxx.zyzztz,
                        pyjm: this.zdxx.pydm,
                        yyz: userId,
                        yyks: userNameBg.Brxx_List.ghks,
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=saveZztz&parm="
                        + JSON.stringify(zztz), function (json) {
                        if (json.a == 0) {

                        } else {

                        }
                    });
                }
                if (this.clxzbz) {
                    this.setPYDM(this.zdxx.dycl, 'zdxx', 'pydm');
                    var dycl = {
                        clmc: this.zdxx.dycl,
                        pyjm: this.zdxx.pydm,
                        yyz: userId,
                        yyks: userNameBg.Brxx_List.ghks,
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=saveDycl&parm="
                        + JSON.stringify(dycl), function (json) {
                        if (json.a == 0) {

                        } else {

                        }
                    });
                }
                //判断未接诊情况下，第一次保存接诊，为当前科室
                if (this.zdxx.jzbz == "0") {
                    this.zdxx.ghks = userNameBg.Brxx_List.ksbm;
                    this.zdxx.ghksmc = userNameBg.Brxx_List.ksmc;
                }
                var parm = {
                    Ghxx: this.zdxx,
                    Zcxx: this.zcxx
                };
                console.log(this.zdxx);
                var json = JSON.stringify(parm);

                //填写血压信息
                this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=updateBrjbxxAndGhxx', json).then(function (data) {
                    if (data.body.a == 0) {
                        malert("上传数据成功", 'top', 'success');
                        // tabBg('brPage/dzcf',1,this)
                        userNameBg.getBrxx(1);//调用查询进行重新赋值操作
                        if (brxx.csqxContent.N01006400202) {
                            brxx.crbOpen()//TODO URL必须判断参数存在在调用
                        }
                        // @yqq 向叫号系统推送最新数据
                        if (userNameBg.csqxContent.N03001200131) {
                            $.ajax({
                                type: 'get',
                                url: userNameBg.csqxContent.N03001200131 + '/pushData/' + userNameBg.Brxx_List.ghks,
                                async: true,
                                success: function (json) {
                                    console.log("数据发送成功");
                                },
                                error: function (response) {
                                }
                            });
                        }
                        //电子健康卡上传门诊项（宣汉）
                        if (brxx.csqxContent.N03001200171) {
                            if (!brxx.saveShow) {//已经接诊后不再上传
                                //查询是否本地有健康卡信息
                                brxx.getAjax(brxx.csqxContent.N03001200171 + "/jsgapi/healthCard/queryCardByBrid?brid=" + brxx.zcxx.brid + "&yljgbm=" + jgbm,
                                    function (data) {
                                        if (data.returnCode == "0") {
                                            var outResult = JSON.parse(data.outResult);
                                            brxx.outpatientAdd(outResult.ehealthCardId);
                                        } else {
                                            if (brxx.zcxx.sfzjhm) {
                                                brxx.outpatientAdd();
                                            }
                                        }
                                    }, function (error) {
                                        console.log("查询是否本地有健康卡信息错误！" + error)
                                    });
                            }
                        }
                    } else {
                        malert("上传数据失败" + data.body.c, 'top', 'defeadted');
                        return false;
                    }
                }, function (error) {
                    console.log(error);
                });
                this.editTitle = '编辑';
                this.saveShow = true;
                $("input[name='text']").each(function () {
                    $(this).attr('disabled', true)
                    $(this).addClass('background-h');
                });
                //小丑鱼食源性疾病集成调用
                try {
                    let diagnosis = brxx.zdxx.jbmc;
                    if (brxx.zdxx.qtzdmc) {
                        diagnosis += "|" + brxx.zdxx.qtzdmc;
                    }
                    if (brxx.zdxx.qtzdmc1) {
                        diagnosis += "|" + brxx.zdxx.qtzdmc1;
                    }
                    if (brxx.zdxx.qtzdmc2) {
                        diagnosis += "|" + brxx.zdxx.qtzdmc2;
                    }
                    if (brxx.zdxx.qtzdmc3) {
                        diagnosis += "|" + brxx.zdxx.qtzdmc3;
                    }
                    if (brxx.zdxx.qtzdmc4) {
                        diagnosis += "|" + brxx.zdxx.qtzdmc4;
                    }
                    //nis_url= "openNISExe://InfectiousBoot 0 " + userNameBg.Brxx_List.ghxh + " " + brxx.lsghxxList.length + " " + brxx.zdxx.jzys + " " + diagnosis + " " + brxx.zdxx.sffz;
                    const params = ['InfectiousBoot','0',userNameBg.Brxx_List.ghxh,brxx.lsghxxList.length, brxx.zdxx.jzys,diagnosis,brxx.zdxx.sffz].join('&');
                    const nisUrl = `universalLinks:///?${params}`;
                    console.log("********:"+params);
                    console.log("********:"+nisUrl);
                    window.location.href = nisUrl;
                } catch (err) {
                    malert("食源性疾病集成调用失败！", 'top', 'defeadted');
                    return false;
                }
            },
            //取消
            cancel: function () {
                this.topNewPage('接诊管理', 'page/xmzysz/mzys/jzgl/jzgl.html', '?isgetData=1');
            },
            //获取保险类别
            getbxlb: function () {
                var param = {bxjk: "001"};
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'New1XtwhKsryBxlb',
                        types: 'query',
                        json: JSON.stringify(param)
                    }
                }).then(function (json) {
                    if (json.body.a == 0 && json.body.d) {
                        brxx.bxlbbm = json.body.d.list[0].bxlbbm;
                        brxx.bxurl = json.body.d.list[0].url;
                    } else {
                        malert("保险类别查询失败!" + json.c, 'top', 'defeadted');
                    }
                });
            },
            getS02: function () {
                var head = {
                    operCode: "S02",
                    rsa: ""
                };
                var body = {
                    userName: "",
                    passWord: ""
                };
                var param = {
                    head: head,
                    body: body
                };
                var str_param = JSON.stringify(param);

                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1BxInterface&url=" + brxx.bxurl + "&bxlbbm=" + brxx.bxlbbm + "&types=S&parm=" + str_param, function (json) {
                        if (json.a == 0) {
                            brxx.billCode = json.d;
                        } else {
                            malert("认证鉴权失败，请从新操作", 'top', 'defeadted');
                        }
                    });
            },
            // 页面加载时自动获取联系人关系Dddw数据
            GetlxrgxData: function () {
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: 'lxrgx'
                    }
                }).then(function (json) {
                    if (json.body.a == 0) {
                        brxx.lxrgxList = json.body.d.list;
                    } else {
                        malert("联系人下拉列表查询失败" + json.body.c, 'top', 'defeadted');
                        return false;
                    }
                })
            },
            //页面加载时自动获职业编码Dddw数据
            GetZybmData: function () {
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'GetDropDown',
                        types: 'zybm'
                    }
                }).then(function (json) {
                    if (json.body.a == 0) {
                        brxx.zyList = json.body.d.list;
                    } else {
                        malert("职业列表查询失败" + json.body.c, 'top', 'defeadted');
                        return false;
                    }
                })
            },
            //根据病人id查询该病人所有挂号信息
            getghxx: function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=quertBrAllGhxx&parm={ghxh:" + userNameBg.Brxx_List.ghxh + ",brid:" + userNameBg.Brxx_List.brid + "}", function (json) {
                    if (json.a == 0) {
                        brxx.lsghxxList = json.d.list;
                        for (var i = 0; i < brxx.lsghxxList.length; i++) {
                            brxx.lsghxxList[i]['jzsj1'] = brxx.fDate(brxx.lsghxxList[i]['jzsj'], 'date')
                        }
                    } else {
                        malert("获取病人历史挂号信息失败" + json.c, 'top', 'defeadted');
                    }
                });
            },

            //历史挂号信息改变时
            lsghxxChange: function (val) {
                var isTwo = false;
                brxx.ghxh = val[0];
                var wzxx = {};
                for (var i = 0; i < brxx.lsghxxList.length; i++) {
                    if (brxx.ghxh == brxx.lsghxxList[i].ghxh) {
                        wzxx = brxx.lsghxxList[i];
                        Vue.set(brxx.jbbmContent, 'jbmc', wzxx.jbmc);
                        Vue.set(brxx.qtzdbmContent, 'jbmc', wzxx.qtzdmc);
                        Vue.set(brxx.ybjbbmContent, 'mc', wzxx.ybjbmc);
                        //Vue.set(brxx.zyjbbmContent, 'jbmc', wzxx.zyjbmc);
                        Vue.set(brxx.zdxx, 'xtms', wzxx.xtms);
                        Vue.set(brxx.zdxx, 'sffz', '1');
                        Vue.set(brxx.zdxx, 'xt', wzxx.xt);
                        Vue.set(brxx.zdxx, 'xySsy', wzxx.xySsy);
                        Vue.set(brxx.zdxx, 'xySzy', wzxx.xySzy);
                        Vue.set(brxx.zdxx, 'tw', wzxx.tw);

                        Vue.set(brxx.zdxx, 'zs', wzxx.zs);
                        Vue.set(brxx.zdxx, 'zyzh', wzxx.zyzh);
                        Vue.set(brxx.zdxx, 'zyzf', wzxx.zyzf);
                        Vue.set(brxx.zdxx, 'jzs', wzxx.jzs);
                        Vue.set(brxx.zdxx, 'jws', wzxx.jws);

                        Vue.set(brxx.zdxx, 'lxrzjh', wzxx.lxrzjh);
                        Vue.set(brxx.zdxx, 'zyzztz', wzxx.zyzztz);
                        Vue.set(brxx.zdxx, 'jbbm', wzxx.jbbm);
                        Vue.set(brxx.zdxx, 'jbmc', wzxx.jbmc);
                        Vue.set(brxx.zdxx, 'zyjbbm', wzxx.zyjbbm);
                        Vue.set(brxx.zdxx, 'zyjbmc', wzxx.zyjbmc);
                        Vue.set(brxx.zdxx, 'qtzdbm', wzxx.qtzdbm);
                        Vue.set(brxx.zdxx, 'qtzdmc', wzxx.qtzdmc);
                        Vue.set(brxx.zdxx, 'ybjbbm', wzxx.ybjbbm);
                        Vue.set(brxx.zdxx, 'ybjbmc', wzxx.ybjbmc);
                        Vue.set(brxx.zdxx, 'dycl', wzxx.dycl);
                        Vue.set(brxx.zdxx, 'sfcrb', wzxx.sfcrb);
                        Vue.set(brxx.zdxx, 'crbdj', wzxx.crbdj);
                        Vue.set(brxx.zdxx, 'sfgm', wzxx.sfgm);
                        Vue.set(brxx.zdxx, 'gms', wzxx.gms);
                        Vue.set(brxx.zdxx, 'xbs', wzxx.xbs);
                        Vue.set(brxx.zdxx, 'mb', wzxx.mb);
                        Vue.set(brxx.zdxx, 'hx', wzxx.hx);
                        Vue.set(brxx.zdxx, 'xl', wzxx.xl);
                        Vue.set(brxx.zdxx, 'bzsm', wzxx.bzsm);
                        Vue.set(brxx.zdxx, 'fbdd', wzxx.fbdd);
                        //用于下拉复制
                        Vue.set(brxx.clContent, 'dycl', wzxx.dycl);
                        Vue.set(brxx.tzContent, 'zyzztz', wzxx.zyzztz);
                    }
                }
                //回车跳转
                if (val[1] != null && !isTwo) {
                    this.nextFocus(event);
                }
            },

            //宣汉妇保健康卡，门诊项上传
            outpatientAdd: function (ehealthCardId) {
                //上传门诊项
                var req = {
                    ehealthCardId: ehealthCardId ? ehealthCardId : "", //string	false	电子健康卡id
                    morbidt: new Date(brxx.zdxx.fbrq).getTime() + "",   //number	true	发病日期时间（单位：毫秒）
                    hospname: '宣汉县妇幼保健院',  //string	true	就诊机构名称
                    hosporgcode: '125114224523722007',   //string	true	就诊机构组织机构代码
                    examoffice: brxx.zdxx.ghksmc,    //string	true	就诊科室名称
                    visitdt: new Date().getTime() + "",   //number	true	就诊日期时间（单位：毫秒）
                    examno: brxx.zdxx.ghxh,    //string	true	门诊号
                    diseasecode: brxx.zdxx.jbbm,   //string	true	门诊诊断代码
                    paytype: brxx.zdxx.fbmc,   //string	true	医疗付款方式
                    confirmdt: new Date().getTime() + "", //number	true	诊断日期（单位：毫秒）
                    symptomdt: new Date().getTime() + "", //number	true	症状持续时间（单位：毫秒）
                    symptomcode: brxx.zdxx.jbbm,   //string	true	症状代码
                    symptomname: brxx.zdxx.jbmc,   //string	true	症状名称
                    idcard: brxx.zcxx.sfzjhm,    //string	false	身份证号
                };
                var inParm = {
                    czybm: userId,
                    yljgbm: jgbm,
                    inJson: JSON.stringify(req)
                };
                brxx.postAjax(brxx.csqxContent.N03001200171 + "/jsgapi/healthCard/outpatientAdd",
                    JSON.stringify(inParm), function (data) {
                        if (data.returnCode == "0") {
                            console.log(data.msgInfo);
                            console.log(data.outResult);
                        } else {
                            console.log(data.msgInfo);
                        }
                    }, function (error) {
                        console.log("【健康卡注册错误】：" + error);
                    });
            },
            //下拉检索
            changeDown: function (event, type, selSearch, searchCon, bm, mc) {
                // if (this[searchCon][this.selSearch] == undefined) return;
                this.bm = bm;
                this.mc = mc;
                this.inputUpDown(event, searchCon, selSearch);
                this.jbbmContent = this[searchCon][this[selSearch]]
                var types = {
                    // 'qtzd':'其他诊断',
                    'jbbm': '诊断疾病'
                }
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (types[type] && !this.zdxx[type] && this.jbbmContent == undefined) {
                        return malert('请严格按照国家政策填写' + types[type] + '信息', 'top', 'defeadted')
                    } else {
                        this.nextFocus(event);
                    }
                    if (this.jbbmContent) {
                        if (type == "jbbm") {
                            this.zdxx['jbbm'] = this.jbbmContent['jbmb'];
                            this.zdxx['jbmc'] = this.jbbmContent['jbmc'];
                        }
                        if (type == "qtzd") {
                            this.zdxx[bm] = this.jbbmContent['jbmb'];
                            this.zdxx[mc] = this.jbbmContent['jbmc'];
                        }
                        if (type == "ybjb") {
                            this.zdxx['ybjbbm'] = this.jbbmContent['bm'];
                            this.zdxx['ybjbmc'] = this.jbbmContent['mc'];
                        }
                    }
                    this.nextFocus(event);
                    this.$forceUpdate()
                    this.jbbmContent = {};
                    this.selSearch = -1;
                    $(".selectGroup").hide();
                }
            },
            BlurData: function (key, text, value) {
                setTimeout(function () {
                    if (!brxx[key.split('.')[0]][key.split('.')[1]]) {
                        malert('请严格按照国家政策填写' + text + '信息', 'top', 'defeadted')
                        brxx[value.split('.')[0]][value.split('.')[1]] = ''
                        return
                    }
                }, 0)
            },
            //处理下拉检索
            clchangeDown: function (event, type, selSearch, searchCon) {
                console.log(111)
                //if (this[searchCon][this.selSearch] == undefined) return;
                this.inputUpDown(event, searchCon, selSearch);
                this.clContent = this[searchCon][this[selSearch]]
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    // if(this.clContent == undefined){
                    //     return malert('请严格按照国家政策填写对应处理信息','top','defeadted')
                    // };
                    if (this.changeType) {
                        this.zdxx['dycl'] = this.clContent && this.clContent['clmc'] || this.zdxx['dycl'];
                        // this.clContent['dycl'] = this.clContent && this.clContent['clmc'] || this.zdxx['dycl'];
                    }
                    this.changeType = false;
                    brxx.clxzbz = false;
                    this.nextFocus(event);
                    this.$forceUpdate()
                    $(".selectGroup").hide();
                }
            },
            //症状下拉检索
            tzchangeDown: function (event, type, selSearch, searchCon) {
                this.inputUpDown(event, searchCon, selSearch);
                this.tzContent = this[searchCon][this[selSearch]]
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (this.changeType) {
                        this.zdxx['zyzztz'] = this.tzContent['zzmc'];
                        brxx.tzxzbz = false;
                    }
                    this.nextFocus(event);
                    this.changeType = false;
                    // if(this.tzContent == undefined){
                    //     return malert('请严格按照国家政策填写症状体征信息','top','defeadted')
                    // };
                    this.$forceUpdate()
                    // }
                    $(".selectGroup").hide();
                }
            },
            //当输入值后才触发
            change: function (events, add, type, val, bm, mc, selSearch) {
                this.changeType = true;
                if (!add) this.page.page = 1; // 设置当前页号为第一页
                var evt = window.event.target || events.target;
                var _searchEvent = $(evt.nextElementSibling).eq(0);
                if (type == "jbbm") {
                    console.log(type);
                    this.zdxx.jbmc = val;
                    this.zdxx.jbbm = '';
                    var str_param = {
                        parm: val,
                        page: this.page.page,
                        rows: this.page.rows
                    };
                    if (!this.csqxContent.N03001200146 || this.csqxContent.N03001200146 == '0') {
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm' +
                            '&json=' + JSON.stringify(str_param),
                            function (data) {
                                if (data.d) {
                                    if (add) { //不是第一页则需要追加
                                        for (var i = 0; i < data.d.list.length; i++) {
                                            brxx.searchCon.push(data.d.list[i]);
                                        }
                                    } else {
                                        brxx.searchCon = data.d.list;
                                    }
                                    brxx.page.total = data.d.total;
                                    brxx.selSearch = 0;
                                    if (data.d.list.length > 0 && !add) {
                                        console.log('追加');
                                        $(".selectGroup").hide();
                                        _searchEvent.show();
                                    }
                                } else {
                                    malert(data.c, 'top', 'defeadted')
                                }
                            });
                    } else {
                        // yqq 使用贵州农合诊断库
                        $.getJSON("/actionDispatcher.do?reqUrl=New1=New1BxInterface&url=" + brxx.bxurl + "&bxlbbm=" + brxx.bxlbbm + "&types=jbbm&method=query&parm="
                            + JSON.stringify(str_param),
                            function (json) {
                                if (json.a == 0) {
                                    var date = null;
                                    var res = eval('(' + json.d + ')');
                                    if (add) { //不是第一页则需要追加
                                        for (var i = 0; i < res.list.length; i++) {
                                            res.list[i].jbmb = res.list[i].bm;
                                            res.list[i].jbmc = res.list[i].mc;
                                            brxx.searchCon.push(res.list[i]);
                                        }
                                    } else {
                                        for (var i = 0; i < res.list.length; i++) {
                                            res.list[i].jbmb = res.list[i].bm;
                                            res.list[i].jbmc = res.list[i].mc;
                                        }
                                        brxx.searchCon = res.list;
                                    }
                                    brxx.page.total = res.total;
                                    brxx.selSearch = 0;
                                    if (res.list.length > 0 && !add) {
                                        console.log('追加');
                                        $(".selectGroup").hide();
                                        _searchEvent.show();
                                    }
                                } else {
                                    malert("查询失败  " + json.c, 'top', 'defeadted');
                                }
                            });
                    }
                } else if (type == "qtzd") {
                    this.zdxx[mc] = val
                    this.zdxx[bm] = '';
                    var _searchEvent = $(events.target.nextElementSibling).eq(0);
                    this.page.parm = val;
                    var str_param = {
                        parm: this.page.parm,
                        page: this.page.page,
                        rows: this.page.rows
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbbm' +
                        '&json=' + JSON.stringify(str_param),
                        function (data) {
                            if (data.d) {
                                if (add) { //不是第一页则需要追加
                                    for (var i = 0; i < data.d.list.length; i++) {
                                        brxx.jbsearchCon.push(data.d.list[i]);
                                    }
                                } else {
                                    brxx.jbsearchCon = data.d.list;
                                }
                                brxx.page.total = data.d.total;
                                brxx[selSearch] = 0;
                                if (data.d.list.length > 0 && !add) {
                                    $(".selectGroup").hide();
                                    _searchEvent.show();
                                }
                            } else {
                                malert(data.c, 'top', 'defeadted')
                            }

                        });
                }
                //症状体征
                else if (type == "zyzztz") {
                    brxx.tzxzbz = true;
                    this.zdxx.zyzztz = val;
                    var _searchEvent = $(events.target.nextElementSibling).eq(0);
                    this.page.parm = val;
                    var str_param = {
                        parm: this.page.parm,
                        page: this.page.page,
                        rows: this.page.rows,
                        yyz: userId,
                        yyks: userNameBg.Brxx_List.ghks
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=queryZztz' +
                        '&parm=' + JSON.stringify(str_param),
                        function (data) {
                            if (data.d) {
                                if (add) { //不是第一页则需要追加
                                    for (var i = 0; i < data.d.list.length; i++) {
                                        brxx.tzsearchCon.push(data.d.list[i]);
                                    }
                                } else {
                                    brxx.tzsearchCon = data.d.list;
                                }
                                brxx.page.total = data.d.total;
                                brxx.selSearch3 = 0;
                                if (data.d.list.length > 0 && !add) {
                                    $(".selectGroup").hide();
                                    _searchEvent.show();
                                }
                            } else {
                                malert(data.c, 'top', 'defeadted')
                            }
                        })
                }
                //对应处理
                else if (type == "dycl") {
                    brxx.clxzbz = true;
                    this.zdxx.dycl = val
                    var _searchEvent = $(events.target.nextElementSibling).eq(0);
                    this.page.parm = val;
                    var str_param = {
                        parm: this.page.parm,
                        page: this.page.page,
                        rows: this.page.rows,
                        yyz: userId,
                        yyks: userNameBg.Brxx_List.ghks
                    };
                    $.getJSON('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=queryDycl' +
                        '&parm=' + JSON.stringify(str_param),
                        function (data) {
                            if (data.d) {
                                if (add) { //不是第一页则需要追加
                                    for (var i = 0; i < data.d.list.length; i++) {
                                        brxx.clsearchCon.push(data.d.list[i]);
                                    }
                                } else {
                                    brxx.clsearchCon = data.d.list;
                                }
                                brxx.page.total = data.d.total;
                                brxx.selSearch4 = 0;
                                if (data.d.list.length > 0 && !add) {
                                    $(".selectGroup").hide();
                                    _searchEvent.show();
                                }
                            } else {
                                malert(data.c, 'top', 'defeadted')
                            }

                        });
                } else if (type == "ybjb") {
                    var _searchEvent = $(events.target.nextElementSibling).eq(0);
                    this.page.parm = val;
                    this.zdxx.ybjbmc = val
                    this.zdxx.ybjb = '';
                    var str_param = {
                        parm: this.page.parm,
                        page: this.page.page,
                        rows: this.page.rows
                    };
                    $.getJSON("/actionDispatcher.do?reqUrl=New1BxInterface&url=" + brxx.bxurl + "&bxlbbm=" + brxx.bxlbbm + "&types=jbbm&method=query&parm="
                        + JSON.stringify(str_param),
                        function (data) {
                            if (data.d) {
                                var res = eval('(' + data.d + ')');
                                if (add) { //不是第一页则需要追加
                                    for (var i = 0; i < res.list.length; i++) {
                                        brxx.ybsearchCon.push(res.list[i]);
                                    }
                                } else {
                                    brxx.ybsearchCon = res.list;
                                }
                                brxx.page.total = res.total;
                                brxx.selSearch2 = 0;
                                if (res.list.length > 0 && !add) {
                                    $(".selectGroup").hide();
                                    _searchEvent.show();
                                }
                            } else {
                                malert(data.c, 'top', 'defeadted')
                            }
                        });
                }
            },
            //鼠标双击（医保）
            selectYbjbbm: function (item) {
                if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++; // 设置当前页号
                    this.change(true, 'ybjb', this.zdxx['ybjbmc']); // 传参表示请求下一页,不传就表示请求第一页
                } else { // 否则就是选中事件,为json赋值
                    this.ybjbbmContent = item;
                    this.zdxx['ybjbbm'] = item.bm;
                    this.zdxx['ybjbmc'] = item.mc;
                    $(".selectGroup").hide();
                }
            },
            //鼠标双击（疾病）
            selectJbbm: function (item) {
                console.log('asxsa');
                if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++; // 设置当前页号
                    this.change(true, 'jbbm', this.zdxx['jbmc']); // 传参表示请求下一页,不传就表示请求第一页
                } else { // 否则就是选中事件,为json赋值
                    this.zdxx['jbbm'] = item.jbmb;
                    this.zdxx['jbmc'] = item.jbmc;
                    $(".selectGroup").hide();
                    $('#dycl').focus()
                }
            },
            //鼠标双击（症状体征）
            selecttz: function (item) {
                if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++; // 设置当前页号
                    this.change(true, 'zyzztz', this.zdxx['zyzztz']); // 传参表示请求下一页,不传就表示请求第一页
                } else { // 否则就是选中事件,为json赋值
                    this.zdxx['zyzztz'] = item.zzmc;
                    brxx.tzxzbz = false;
                    $(".selectGroup").hide();
                    $('#gms').focus()
                }
            },
            //鼠标双击（对应处理）
            selectcl: function (item) {
                if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++; // 设置当前页号
                    this.change(true, 'dycl', this.zdxx['dycl']); // 传参表示请求下一页,不传就表示请求第一页
                } else { // 否则就是选中事件,为json赋值
                    this.zdxx['dycl'] = item.clmc;
                    brxx.clxzbz = false;
                    $(".selectGroup").hide();
                    brxx.save();
                }
            },
            //鼠标双击（其他诊断）
            selectQtzd: function (item) {
                if (item == null) { // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++; // 设置当前页号
                    this.change(true, 'qtzd', this.zdxx['jbmc']); // 传参表示请求下一页,不传就表示请求第一页
                } else { // 否则就是选中事件,为json赋值
                    this.zdxx[this.bm] = item.jbmb;
                    this.zdxx[this.mc] = item.jbmc;
                    $(".selectGroup").hide();
                }
            },
            //查询诊断信息
            getDate: function () {
                if (!userNameBg.Brxx_List.ghxh || !userNameBg.Brxx_List.brid) {
                    malert("请先选择病人", 'top', 'defeadted');
                    return false;
                }
                // 根据挂号序号再从服务器上查询当前挂号序号的信息
                var str_param = {
                    ghxh: userNameBg.Brxx_List.ghxh,
                    brid: userNameBg.Brxx_List.brid
                };
                common.openloading('#jbxx')
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'New1GhglGhywBrgh',
                        types: 'queryOne',
                        parm: JSON.stringify(str_param)
                    }
                }).then(function (json) {
                    if (json.body.a == '0' && json.body.d.list) {
                        brxx.zdxx = json.body.d.list[0].ghxx;
                        if (brxx.zdxx.sfcrb == undefined || brxx.zdxx.sfcrb == null) {
                            Vue.set(brxx.zdxx, 'sfcrb', "0");
                        }
                        if (brxx.zdxx.sffrbr == undefined || brxx.zdxx.sffrbr == null) {
                            Vue.set(brxx.zdxx, 'sffrbr', "0");
                        }

                        // if(brxx.zdxx.wcbz =='1'){
                        // 	brxx.isBshow = false;
                        // }else if(brxx.zdxx.ghrq && brxx.zdxx.ghxq){
                        // 	var date=new Date(brxx.zdxx.ghrq);
                        // 	var nowDate = new Date();
                        // 	 date=new Date(date.setDate(date.getDate()+brxx.zdxx.ghxq));
                        // 	 if(date>nowDate){
                        // 		 if(brxx.zdxx.jzys == userId){
                        // 			 brxx.isBshow = true;
                        // 			 if (brxx.zdxx.jzbz == 0) {
                        // 			     brxx.edit();
                        // 			 } else {
                        // 			     brxx.editTitle = '编辑';
                        // 			     brxx.saveShow = true;
                        // 			 }
                        // 		 }else{
                        // 			brxx.isBshow = false;
                        // 		 }
                        // 	 }else{
                        // 		 brxx.isBshow = false;
                        // 	 }
                        // }else{
                        // 	if (brxx.zdxx.jzbz == 0) {
                        // 	    brxx.edit();
                        // 	} else {
                        // 	    brxx.editTitle = '编辑';
                        // 	    brxx.saveShow = true;
                        // 	}
                        // }
                        if (brxx.zdxx.jzbz == 0) {
                            brxx.edit();
                        } else {
                            brxx.editTitle = '编辑';
                            brxx.saveShow = true;
                        }

                        brxx.clContent.dycl = brxx.zdxx.dycl;
                        brxx.tzContent.zyzztz = brxx.zdxx.zyzztz;
                        brxx.zcxx = json.body.d.list[0].zcxx;

                        brxx.shi()
                        brxx.qu()
                        Vue.set(brxx.zdxx, 'fbrq', formatTime(brxx.zdxx.fbrq, "date"));
                        Vue.set(brxx.zdxx, 'qjsj', formatTime(brxx.zdxx.qjsj, "datetime"));
                        Vue.set(brxx.zdxx, 'fbrq1', formatTime(brxx.zdxx.fbrq, "date"));
                        Vue.set(brxx.jbbmContent, 'jbmc', brxx.zdxx.jbmc);
                        Vue.set(brxx.qtzdbmContent, 'jbmc', brxx.zdxx.qtzdmc);
                        Vue.set(brxx.ybjbbmContent, 'mc', brxx.zdxx.ybjbmc);
                        Vue.set(brxx.zdxx, 'brmzmc', json.body.d.list[0].zcxx.brmzmc);
                        Vue.set(brxx.zdxx, 'brmz', json.body.d.list[0].zcxx.brmz);

                        //肿瘤默认值
                        if (brxx.zdxx.sfgm == undefined || brxx.zdxx.sfgm == null) {
                            Vue.set(brxx.zdxx, 'sfgm', '0');
                        }
                        //国籍默认值
                        if (brxx.zcxx.brgj == undefined || brxx.zcxx.brgj == null) {
                            Vue.set(brxx.zcxx, 'brgj', '156');
                        }

                        brxx.getghxx(); //根据病人id查询病人基本信息
                        brxx.$forceUpdate()
                        var cfz = '0';
                        //判断初复诊
                        var parm2 = {
                            brid: brxx.zcxx.brid,
                            jbbm: brxx.zdxx.jbbm,
                        }
                        this.$http.get('/actionDispatcher.do', {
                            params: {
                                reqUrl: 'New1MzysZlglBrjz',
                                types: 'sffzpd',
                                parm: JSON.stringify(parm2)
                            }
                        }).then(function (json) {
                            if (json.body.a == "0") {
                                if (json.body.d.list[0].sffz == '1') {
                                    cfz = '1';
                                }
                            }
                        })
                        if (!brxx.zdxx.sffz) {
                            brxx.zdxx.sffz = cfz;
                        }
                        common.closeLoading()

                    } else {
                        malert(json.body.c, 'top', 'defeadted');
                    }
                });
            },
        }
    })
;


var jzMbPop = new Vue({
    el: '.jzMbPop',
    mixins: [baseFunc, tableBase, checkData],
    data: {
        popShow: false,
        jzMblx_tran: {
            "0": "全院",
            "1": "个人",
        },
        ifClick: true,
        popContent: {} //
    },
    updated: function () {
        this.$nextTick(function () {
            this.setNotEmpty()
        })
    },
    methods: {
        saveData: function () {
            if (!this.ifClick) return;
            this.ifClick = false;
            // 提交前验证数据
            if (!this.empty_sub('contextInfo')) {
                this.ifClick = true;
                return false;
            }
            var parmBlbm = {
                yljgbm: this.popContent.yljgbm,
                mc: this.popContent.mc,
                pydm: this.popContent.pydm,
                lx: this.popContent.lx,
                zs: this.popContent.zs,
                xbs: this.popContent.xbs,
                jws: this.popContent.jws,
                zyzztz: this.popContent.zyzztz,
                gms: this.popContent.gms,
                jzs: this.popContent.jzs,
                czybm: userId,
                dycl: this.popContent.dycl,
            }
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1MzysZlglZhypyz',
                    types: 'saveBlmb',
                    parm: JSON.stringify(parmBlbm)
                }
            }).then(function (json) {
                if (json.body.a == '0') {
                    malert(json.body.c, 'top', 'success');
                    jzMbPop.popShow = false;
                    jzMbPop.ifClick = true;
                    jzMbPop.popContent = {}
                } else {
                    jzMbPop.ifClick = true;
                    malert(json.body.c, 'top', 'defeadted');
                }
            });
        }
    }
})
var crBPop = new Vue({
    el: '.crBPop',
    mixins: [baseFunc, tableBase, checkData],
    data: {
        popShow: false,
        popContent: userNameBg.Brxx_List //
    },

    methods: {
        saveData: function () {
            brxx.crbsbUrl()
        }
    }
})
var fjzdPop = new Vue({
    el: '.fjzdPop',
    components: {
        'search-table': searchTable
    },
    mixins: [baseFunc, tableBase, checkData],
    data: {
        fjzdShow: false,
        fjzd: [],
        popContent: {},
        them: {'疾病编码': 'jbmb', '疾病名称': 'jbmc'},
        queryObj: {
            page: 1,
            rows: 50,
            sort: '',
            order: 'asc'
        },
        searchCon: [],
        selSearch: -1,
    },
    methods: {
        scmx: function (index) {
            if (common.openConfirm("确认删除该条信息吗？", function () {
                fjzdPop.fjzd.splice(index, 1);
            })) {
                return false;
            }
        },
        addFun: function () {
            if (this.fjzd.length == 0) {
                this.fjzd.push({});
            } else if (this.fjzd[this.fjzd.length - 1]['jbmb']) {
                this.fjzd.push({});
            }
        },
        saveData: function () {
            var fjzdmx = '';
            for (var i = this.fjzd.length - 1; i >= 0; i--) {
                if (!this.fjzd[i].jbmb) {
                    this.fjzd.splice(i, 1);
                } else {
                    fjzdmx += this.fjzd[i].jbmb + this.fjzd[i].jbmc + ','
                }
            }
            brxx.zdxx.fjzd = this.fjzd;
            brxx.zdxx.fjzdxm = brxx.zdxx.jbbm + brxx.zdxx.jbmc + ',' + fjzdmx;
            this.fjzdShow = false
        },
        change: function (add, index, mc, bm, val, types, events) {
            if (!add) this.queryObj.page = 1;
            var _searchEvent = $(events.target.nextElementSibling).eq(0);
            this.fjzd[this.allIndex][mc] = val;
            this.queryObj.parm = val;
            this.fjzd[this.allIndex][bm] = '';
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=' + types + '&json=' + JSON.stringify(this.queryObj), function (data) {
                if (add) {
                    fjzdPop.searchCon = fjzdPop.selSearch.concat(data.d.list)
                } else {
                    fjzdPop.searchCon = data.d.list;
                }
                fjzdPop.page.total = data.d.total;
                fjzdPop.selSearch = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                    return false;
                }
            });
        },
        changeDown: function (event, index, zdms, zdbm, qtzd) {
            this.allIndex = index;
            if (this['searchCon'][this['selSearch']] == undefined) return;
            this.inputUpDown(event, this['searchCon'], 'selSearch')
            this['popContent'] = this['searchCon'][this['selSearch']];
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                this.fjzd[index] = this.popContent;
                $(".selectGroup").hide();
                this.selSearch = -1;
                this.addFun()
                this.$nextTick(function () {
                    this.nextFocus(event);
                })
            }
        },
        checkedTwoOut: function (item) {
            if (!item) {
                ++this.queryObj.page;
                this.change(true, this.allIndex, 'jbmc', 'jbbm', this.fjzd[this.allIndex]['jbmc'])
            } else {
                this.fjzd[this.allIndex] = item;
                $(".selectGroup").hide();
                this.selSearch = -1;
                this.$forceUpdate()
            }
        },
    }
})
var Quote = new Vue({
    el: '#Quote',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 1,
        jsonList: [],
        childList: [],
        jzMblx_tran: {
            "0": "全院",
            "1": "个人",
        },
        czybm: userId,
        popContent: {
            parm: '',
            lx: '1',
            czybm: ''
        }, //
        checkEd: {},
        saveDataObj: {},
        childText: {
            'zs': '主诉',
            'xbs': '现病史',
            'jws': '既往史',
            'zyzztz': '主要症状体征',
            'gms': '过敏史',
            'jzs': '家族史',
            'dycl': '对应处理',
        },
    },
    watch: {
        'num': function (newValue, oldValue) {
            if (newValue == 0) {
                this.checkEd = {
                    zs: '1',
                    xbs: '1',
                    jws: '1',
                    zyzztz: '1',
                    gms: '1',
                    jzs: '1',
                    dycl: '1',
                }
            }
        }
    },
    updated: function () {
        changeWin()
    },
    created: function () {
    },
    methods: {
        clickSetData: function (item) {
            this.childList = [];
            this.childList.push(item);
        },
        reCheckChild: function (val) {
            Vue.set(this.checkEd, val[0], val[1])
        },
        saveData: function () {
            for (let key in this.checkEd) {
                if (this.checkEd[key] == '1') {
                    this.saveDataObj[key] = this.childList[0][key]
                }
            }
            brxx.zdxx = Object.assign(brxx.zdxx, this.saveDataObj);
            this.num = 1;
            this.activeIndex = undefined;
            this.hoverIndex = undefined;
            this.childList = [];
        },
        close: function () {
            this.num = 1;
            this.activeIndex = undefined;
            this.hoverIndex = undefined;
            this.childList = [];
        },
        getCxqxChange: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.getData()
        },
        remove: function (index) {
            var json = '{"list":' + JSON.stringify([this.jsonList[index]]) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhypyz&types=deleteBlmb',
                json).then(function (data) {
                if (data.body.a == 0) {
                    malert(data.body.c, 'top', 'success');
                    Quote.getData()
                } else {
                    malert(data.body.c, 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        getData: function () {
            this.popContent.czybm = this.popContent.lx == '0' ? '' : userId
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1MzysZlglZhypyz',
                    types: 'queryBlmb',
                    parm: JSON.stringify(this.popContent)
                }
            }).then(function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    Quote.jsonList = json.body.d.list;
                } else {
                    malert(json.body.c, 'top', 'defeadted');
                }
            });
        }
    }
})

var jzxx = new Vue({
    el: '#jzxx',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 1,
        jsonList: [],
        childList: [],
        czybm: userId,
        popContent: {
            parm: '',
            lx: '1',
            czybm: ''
        }, //
        checkEd: {},
        saveDataObj: {},
        childText: {
            'zs': '主诉',
            'xbs': '现病史',
            'jws': '既往史',
            'zyzztz': '主要症状体征',
            'gms': '过敏史',
            'jzs': '家族史',
            'jbmc': '疾病名称',
            'zyzh': '主病',
            'zyzf': '症型'
        },
    },
    watch: {
        'num': function (newValue, oldValue) {
            if (newValue == 0) {
                this.checkEd = {
                    zs: '1',
                    xbs: '1',
                    jws: '1',
                    zyzztz: '1',
                    gms: '1',
                    jzs: '1',
                    jbmc: '1',
                    zyzh: '1',
                    zyzf: '1'
                }
            }
        }
    },
    updated: function () {
        changeWin()
    },
    created: function () {
        this.popContent.parm = userNameBg.Brxx_List.brxm
    },
    methods: {
        clickSetData: function (item) {
            this.childList = [];
            this.childList.push(item);
        },
        reCheckChild: function (val) {
            Vue.set(this.checkEd, val[0], val[1])
        },
        saveData: function () {
            for (let key in this.checkEd) {
                if (this.checkEd[key] == '1') {
                    this.saveDataObj[key] = this.childList[0][key]
                }
            }
            brxx.zdxx = Object.assign(brxx.zdxx, this.saveDataObj);
            this.num = 1;
            this.activeIndex = undefined;
            this.hoverIndex = undefined;
            this.childList = [];
        },
        close: function () {
            this.num = 1;
            this.activeIndex = undefined;
            this.hoverIndex = undefined;
            this.childList = [];
        },
        getData: function () {
            this.popContent.czybm = this.popContent.lx == '0' ? '' : userId

            var parm = {
                'ghxh': userNameBg.Brxx_List.ghxh,
                'brxm': this.popContent.parm,
                'page': 1,
                'rows': 20
            };

            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1GhglGhywBrgh',
                    types: 'quertBrAllGhxx',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    jzxx.jsonList = json.body.d.list;
                } else {
                    malert(json.body.c, 'top', 'defeadted');
                }
            });
        }
    }
})

//时间控件
laydate.render({
    elem: '#timeVal',
    theme: '#1ab394',
    done: function (value, data) {
        brxx.zdxx.fbrq = value;
        var days = dateDiff(brxx.fDate(brxx.zdxx.fbrq, 'date'), brxx.fDate(new Date(), 'date'));
        brxx.zdxx.fbts = days;

        $("#zs").focus();
    }
});
//时间控件
laydate.render({
    elem: '#timeValqj',
    theme: '#1ab394',
    type: 'datetime',
    done: function (value, data) {
        brxx.zdxx.qjsj = value;
        $("#zs").focus();
    }
});
//返回时间
laydate.render({
    elem: '#timeValfh',
    theme: '#1ab394',
    done: function (value, data) {
        brxx.zdxx.whhbfhsj = value;
    }
});
//接触时间
laydate.render({
    elem: '#timeValfyfy',
    theme: '#1ab394',
    done: function (value, data) {
        brxx.zdxx.wh14jzfysj = value;
    }
});
//接触时间
laydate.render({
    elem: '#timeValjzs',
    theme: '#1ab394',
    done: function (value, data) {
        brxx.zdxx.wh14jzhbrsj = value;
    }
});
laydate.render({
    elem: '#timeValxqjz',
    theme: '#1ab394',
    done: function (value, data) {
        brxx.zdxx.wh14sqfrjzsj = value;
    }
});


var a = 0;

function xyz() {
    if (a == 0) {
        a = 1
    }
}

var pageList = '';


//用于判断非空的
$('input[data-notEmpty=true],select[data-notEmpty=true]').on('blur', function () {
    if ($(this).val() == '' || $(this).val() == null) {
        $(this).addClass("emptyError");
    } else {
        $(this).removeClass("emptyError");
    }
});

//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    e.stopPropagation();
});

