
.flex{
    display: flex;
    align-items: center;
}
.userNameImg{
    /*width: 100px;*/
    /*height: 100px;*/
}
.header-item{
    height: 100%;
    padding-top: 125.58px;
}
.userNameImg img{
    width: 100%;
    height: 100%;
}
.text-color{
    width: 100%;
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}

.userCwh .text,.userFooter .text{
    display: inline-block;
    white-space: nowrap;
    margin-right: 20px;
}
.userCwh{
    margin-bottom: 4px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #B0BFBB;
    text-decoration: underline;
}
.content{
    /*-webkit-user-select: none;*/
    /*-moz-user-select: none;*/
    /*user-select: none;*/
    background: #fff;
}
.blRight {
    position: absolute;
    right: 7px;
    bottom: -40px;
}
.blImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    height:80px;
    background-image: url("/newzui/pub/image/yuanquan.png");
}
.blImg:after{
    content: '';
    background-image: url("/newzui/pub/image/bl.png");
    width: 60%;
    height: 60%;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
}
.swtl{
    font-size:14px;
    color:#757c83;
    padding-left: 12px;
    position: relative;
}
.swtl-hr{
    opacity:0.3;
    background:#757c83;
    width:100%;
    display: block;
    margin-bottom: 17px;
    margin-top: 5px;
    height:2px;
}
.swtl-icon{
    width:26px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    height:26px;
    display: inline-block;
    border-radius:100%;
}
.width973{
    width: 96.5%;
}
.icon-img-date{
    background-image: url("/newzui/pub/image/2018072105.png");
}
.icon-img-map{
    background-image: url("/newzui/pub/image/2018082110.png");
}
.icon-img-ch{
    background-image: url("/newzui/pub/image/2018072106.png");
}
.icon-img-lc{
    background-image: url("/newzui/pub/image/20180721115.png");
}
.icon-img-bl{
    background-image: url("/newzui/pub/image/2018072107.png");
}
.icon-img-nr{
    background-image: url("/newzui/pub/image/2018072108.png");
}
.icon-img-zj{
    background-image: url("/newzui/pub/image/413787740397312856.png");
}
.icon-img-qz{
    background-image: url("/newzui/pub/image/2018072109.png");
}
.icon-img-jl{
    background-image: url("/newzui/pub/image/151130123720481256.png");
}
.HeadImg:after{
    width:14px;
    height:14px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    display: inline-block;
    border-radius:100%;
    content: '';
    right: -2px;
    top: -2px;
    position: absolute;
    background-image: url("/newzui/pub/image/20180721114.png");
}
.icon-upload{
    display: inline-block;
    width: 38px;
    height: 38px;
    cursor: pointer;
    position: relative;
    margin-right: 7px;
    border-radius: 50px;
    -webkit-border-radius: 50px;
    border: 1px solid #cccfd4;
}
.icon-upload:hover{
    background:rgba(26,188,156,0.10);
}
.icon-upload:before,  .icon-upload:after {
    content: '';
    height: 1px;
    width: 24px;
    display: block;
    background:#cccfd4;
    border-radius: 10px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    position: absolute;
    top: 18px;
    margin-right: 7px;
    left: 6px;
}
.icon-upload:after {
    height: 24px;
    width: 1px;

    top: 7px;
    left: 17px;
}
.swtl-item .list{
    min-height: 36px;
    position: relative;
}
.swtl-item .VerticalLine:after{
    content: '';
    left: 13px;
    border-left: 2px solid rgba(194, 202, 212, .2);
    position: absolute;
    height: calc(100% - 26px);
    top: 26px;
}
.swtl-item .list .icon-text{
    padding-left: 8px;
}
.swtl-item .list .swtl-text-content{
    margin-left: 34px;
}
.swtl-item{
    padding-left: 7px;
}
.color-333{
    color: #333333;
}
.color-d66b3f{
    color: #d66b3f;
}
.color-f4b26b{
    color: #f4b26b;
}
.color-7f5cff{
    color: #7f5cff;
}
.color-ff5c63{
    color: #ff5c63;
}
#content .icon-iocn1:before{
    color: #ff5c63;
    margin-right: 3px;
}
#content .icon-iocn1{
    font-size: 14px;
    vertical-align: initial;
}
.color-3f64d6{
    color: #3f64d6;
}
.color-72bc1a{
    color: #72bc1a;
}
.color-00a7ff{
    color: #00a7ff;
}
.color-757c83{
    color: #757c83;
}
.Headimg{
    width:36px;
    cursor: pointer;
    position: relative;
    margin-right: 7px;
    height:36px;
    border-radius:100%;
}
.Headimg .headImg:hover{
    box-shadow: -1px 1px 10px rgba(26,188,156,0.10);
}
.color-f2a654{
    color: #f2a654;
}
.HeadPortrait .headImg,.Headimg .headImg{
    width: 36px;
    border-radius:100%;
    height: 36px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;

}
.startFail:after,.HeadStart:after{
    position: absolute;
    right: 2px;
    top: 5px;
    z-index: 11;
    content: '';
    border-radius:2px;
    width:8px;
    cursor: pointer;
    height:2px;
    background-color: #ffffff;
}
.startFail:before,.HeadStart:before{
    opacity:0.8;
    content: '';
    cursor: pointer;
    right: 0;
    position: absolute;
    background:#ff5c63;
    width:12px;
    z-index: 11;
    height:12px;
    border-radius:100%;
}
.tips{
    background:rgba(0, 0, 0, .6);
    border-radius:4px;
    width:76px;
    position: absolute;
    height:30px;
    font-size:12px;
    line-height: 30px;
    z-index: 1111;
    bottom: -34px;
    color:#ffffff;
    text-align:center;
}
.tips:before{
    width: 0;
    height: 0;
    content: '';
    position: absolute;
    top: -5px;
    left: 12px;
    border-width:0 5px 5px;
    border-style:solid;
    border-color:transparent transparent rgba(0, 0, 0, .6);/*透明 透明  灰*/
}
.hoverAvter:before{
    width: 0;
    height: 0;
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    border-width:0 5px 5px;
    border-style:solid;
    border-color:transparent transparent black;
}
.hoverAvter{
    background:#ffffff;
    box-shadow:0 2px 6px 0 rgba(2,41,33,0.30);
    border-radius:4px;
    width:140px;
    height:130px;
    top: 0;
    overflow: hidden;
    position: fixed;
    z-index: 11111;
}
.djzt {
    position: absolute;
    right: -44px;
    top: 1px;
    box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.20);
    transform: rotate(46deg);
    color: #ffffff;
    text-align: center;
    overflow: hidden;
    width: 130px;
    font-size: 12px;
    background-color: #f4b26b;
    height: 35px;
    line-height: 35px;
}
.hoverAvter .headImg{
    background:#3dc9ad;
    width:60px;
    height:60px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    margin-left: auto;
    margin-right:auto;
    border-radius:100%;
}
.username{
    color:#333333;
    margin: 0 auto;
    font-weight: bold;
}
.Photo {

}
.Photo .PhotoImg{
    width:80px;
    height:38px;
    margin-right: 11px;
    position: relative;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}
.icon-Photo{
    position: relative;
}
.icon-Photo:hover:before{
    position: absolute;
    content: '\e939';
    left: 50%;
    top: 50%;
    font-size: 25px;
    cursor: pointer;
    z-index: 1111;
    transform: translate(-50%,-50%);

}
.icon-Photo:hover:after{
    width: 100%;
    height: 100%;
    cursor: pointer;
    background-color: rgba(58, 58, 58, .3);
    position: absolute;
    z-index: 111;
    content: '';
}
.previewPhoto .preview{
    vertical-align: middle;
}
.wtg{
    width: 80px;
    height: 80px;
    position: absolute;
    right: 0;
    top: -10px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url("/newzui/pub/image/69150B30F38E6C3457C5BFE54E7CD57F.png");
}
#content .text-indent-0{
    text-indent: 0;
    min-height: 36px;
    height: auto;
    line-height: unset;
    padding-top: 7px;
}
.bg-center{
    width: 100px;
    margin: 0 auto;
    cursor: pointer;
    height: 100px;
    position: relative;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
}
.start{

    background-image: url("/newzui/pub/image/2018072104.png");
}
.Success{
    background-image: url("/newzui/pub/image/2018072103.png");
}
.fail{
    background-image: url("/newzui/pub/image/2018072102.png");
}
.fqtlbg{
    position: relative;
    width: 196px;
    height: 196px;
    margin: 0 auto;
    padding-top: 35.5px;
}
.point{
    position: absolute;
    border-radius: 50%;
    animation:border 2s linear infinite;
}
.point2{
    -webkit-animation-delay:.5s;
}
.point3{
    -webkit-animation-delay:1s;
}

@keyframes border{
    from {
        width:0;
        height:0;
        top:44%;
        left:50%;
        border: 1px solid #1abc9c;
    }
    to {
        width:100%;
        height:100%;
        top:-6%;
        left:0;
        border: 1px solid white;
    }
}
.color-1abc9c{
color: #1abc9c;
}
.HeadPortrait{
    width:36px;
    cursor: pointer;
    position: relative;
    margin-right: 7px;
    height:auto;
    border-radius:100%;
}
.color-f2a654{
    color: #f2a654;
}
