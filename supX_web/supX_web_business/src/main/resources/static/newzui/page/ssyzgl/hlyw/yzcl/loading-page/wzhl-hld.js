var wzhlHld = new Vue({
	el: "#wzhlHld",
	mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat, scrollOps],
    data: {
    	brxx:{},
    	hljl: [],
    },
    mounted: function(){
    	this.moun();
    	window.addEventListener("storage",function(e){
    		if(e.key == 'wzhlHld' && e.oldValue !== e.newValue){
    			wzhlHld.moun();
    		}
    	});
    },
    methods: {
    	moun: function(){
    		var param = JSON.parse(sessionStorage.getItem("wzhlHld"));
    		this.brxx = param.brxx;
    		this.hljl = param.hljl;
    		for(var i=0;i<this.hljl.length;i++){
    			var hours = new Date(this.hljl[i].jlsj).getHours() ;
    			this.hljl[i].hours=hours;
    		}
    	},
    	closePage: function(){
    		this.topClosePage("page/hsz/hlyw/yzcl/loading-page/wzhl-hld.html");
    	},
    	print: function(){
    		var printBox = $(".printArea"),
    			printInfo = $(".hld-box");
    		printBox.html( printInfo.html() );
    		window.print();
    	}
    }
});