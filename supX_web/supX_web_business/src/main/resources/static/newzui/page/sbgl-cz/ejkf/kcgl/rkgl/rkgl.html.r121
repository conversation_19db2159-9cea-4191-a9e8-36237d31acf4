<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>入库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="rkgl.css" rel="stylesheet" />
    <link href="rydj.css" rel="stylesheet" />
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
    <div class="printArea printShow"></div>
    <div class="background-box" >
        <div class="wrapper printHide" id="wrapper">
            <div class="panel">
                <div class="tong-top">
                    <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-if="isShowkd">开单</button>
                    <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="isShowpopL">添加材料</button>
                    <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>

                </div>
                <div class="tong-search" :class="{'tong-padded':isShow}">
                    <div class="flex-container flex-align-c padd-b-10" v-show="isShowkd">
                        <div class="flex-container flex-align-c padd-r-20">
                            <span class="ft-14 padd-r-5 whiteSpace ">二级库房</span>
                                <select-input @change-data="resultChangeFun" class="wh122" :not_empty="false" :child="YFList" :index="'yfmc'"
                                    :index_val="'yfbm'" :val="param.yfbm" :search="true" :name="'param.yfbm'" id="yfbm" :index_mc="'yfbm'">
                                </select-input>
                        </div>
                        <div class="flex-container flex-align-c padd-r-20">
                            <span class="ft-14 padd-r-5 whiteSpace ">审核标志</span>
                            <select-input @change-data="resultChange"
                                          :child="ckglzt_tran"
                                          class="wh122"
                                          :index="param.zt"
                                          :val="param.zt"
                                          :name="'param.zt'"  >
                            </select-input>
                        </div>
                        <div class="flex-container flex-align-c padd-r-20">
                            <span class="ft-14 padd-r-5 whiteSpace">时间段</span>
                            <div class=" flex-container flex-align-c ">
                                <input class="zui-input todate wh200 " placeholder="请选择申请开始日期" id="timeVal" /><span
                                    class="padd-l-5 padd-r-5">~</span>
                                <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                            </div>
                        </div>
                        <div class="flex-container flex-align-c">
                            <span class="ft-14 padd-r-5 whiteSpace ">检索</span>
                                <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="goToPage(1)" type="text" v-model="param.parm" />
                        </div>
						<div class="flex-container flex-align-c">
						    <span class="color-wtg font-18">零价总额： {{totalyplj}}元&ensp;</span>
						</div>
						<div class="flex-container flex-align-c">
						    <span class="color-wtg font-18">进价总额： {{totalypjj}}元&ensp;</span>
						</div>
                    </div>
                    <div class="jbxx fyxm-hide" :class="{'btn-show':isShow}">
                        <div class="jbxx-size">
                            <div class="jbxx-position">
                                <span class="jbxx-top"></span>
                                <span class="jbxx-text">基本信息</span>
                                <span class="jbxx-bottom"></span>
                            </div>
                            <div class="zui-form padd-l24 padd-t-20 grid-box">
                                <div class="zui-inline">
                                    <label class="zui-form-label">二级库房</label>
                                    <div class="zui-input-inline wh122 margin-f-l20" style="margin-left: 20px">
                                        <select-input @change-data="resultChange" :not_empty="false" :child="YFList"
                                            :index="'yfmc'" :index_val="'yfbm'" :val="param.yfbm" :search="true" :name="'param.yfbm'"
                                            id="yfbm" :index_mc="'yfbm'" :disable="jyinput">
                                        </select-input>

                                    </div>
                                </div>
                                <div class="zui-inline" style="width:50%;">
                                    <label class="zui-form-label ">备注</label>
                                    <div class="zui-input-inline margin-f-l20">
                                        <input class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="bzms"
                                            :disabled="jyinput" />
                                    </div>
                                </div>
                            </div>
                            <div class="rkgl-kd">
                                <span>开单日期:<i class="color-wtg" v-text="zdrq"></i></span>
                                <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="zui-table-view  padd-r-10 padd-l-10">
                <!--入库列表-->
                <div class="zui-table-header" v-if="isShowkd">
                    <table class="zui-table table-width50">
                        <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-l"><span>入库单号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>制单时间</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>制单员</span></div>
                                </th>
								<th>
								    <div class="zui-table-cell cell-s"><span>总零价</span></div>
								</th>
								<th>
								    <div class="zui-table-cell cell-s"><span>总进价</span></div>
								</th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>备注</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                                </th>
                                <th class="cell-l">
                                    <div class="zui-table-cell cell-l"><span>操作</span></div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" key="b" v-if="isShowkd" @scroll="scrollTable($event)">
                    <table class="zui-table ">
                        <tbody>
                            <tr v-for="(item,$index) in rkdList" @click="checkSelect([$index,'one','rkdList'],$event)"
                                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'background-red':item.totalypjj< 0}]" :tabindex="$index" @dblclick="showDetail($index,item)">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l" v-text="item.rkdh">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.zdrxm">序号</div>
                                </td>
								<td>
								    <div class="zui-table-cell cell-s text-right" v-text="fDec(item.totalyplj,2)"></div>
								</td>
								<td>
								    <div class="zui-table-cell cell-s text-right" v-text="fDec(item.totalypjj,2)"></div>
								</td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.bzms">序号</div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <i v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : item.shzfbz=='3' ? 'color-wtg':'' "></i>
                                    </div>
                                </td>
                                <td class="cell-l">
                                    <div class="zui-table-cell cell-l">
                                        <span class="flex-center padd-t-5">
                                            <em class="width30" v-if="item.shzfbz== 0">
                                                <i class="icon-sh" @click="showDetail($index,item)" data-title="审核"></i></em>
                                            <em class="width30" v-if="item.shzfbz== 0">
                                                <i class="icon-js" @click="invalidData($index)" data-title="作废"></i>
                                            </em>
                                            <em class="width30" v-if=" item.shzfbz !='2'">
                                                <i class="icon-bj" @click="editIndex($index)" data-title="编辑"></i>
                                            </em>
                                        </span>
                                    </div>
                                </td>
                                <p v-show="rkdList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--添加材料-->
                <div class="zui-table-header " v-if="isShow" >
                    <table class="zui-table table-width50">
                        <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
								<th>
								    <div class="zui-table-cell cell-xl text-left"><span>材料编码</span></div>
								</th>
                                <th>
                                    <div class="zui-table-cell cell-xl text-left"><span>材料名称</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料规格</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>入库数量</span></div>
                                </th>
								<th>
								    <div class="zui-table-cell cell-s"><span>已冲销数量</span></div>
								</th>
								<th v-if="cxShow">
								    <div class="zui-table-cell cell-s"><span>冲销数量</span></div>
								</th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料进价</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料零价</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>材料批号</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>有效期至</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>产地</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>库房单位</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>二级库房单位</span></div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s"><span>分装比例</span></div>
                                </th>
                                <th class="cell-s">
                                    <div class="zui-table-cell cell-s"><span>操作</span></div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body   " key="a" v-if="isShow"  @scroll="scrollTable($event)">
                    <table class="zui-table ">
                        <tbody>
                            <tr v-for="(item,$index) in jsonList" @click="checkSelect([$index,'some','jsonList'],$event)"
                                :class="[{'table-hovers':$index===activeIndex}]">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                </td>
								<td>
								    <div class="zui-table-cell cell-xl text-over-2 text-left " v-text="item.ypbm"></div>
								</td>
                                <td>
                                    <div class="zui-table-cell cell-xl text-over-2 text-left " v-text="item.ypmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s">
                                        <div v-text="item.ypgg"></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.rksl"></div>
                                </td>
								<td>
								    <div class="zui-table-cell cell-s  " v-html="item.ycxsl"></div>
								</td>
								<td v-if="cxShow">
								    <div class="zui-table-cell cell-s "><input class="zui-input height-28"
								                                               @input="getCxsl(item.rksl,item.cxsl,item.ycxsl,$index)"
								                                               :disabled="item.ycxsl >= item.rksl"
								                                               v-model="item.cxsl"/></div>
								</td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDec(item.ypjj,4)"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDec(item.yplj,4)"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.scph"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                                </td>
                                <td class="cell-s">
                                    <div class="zui-table-cell cell-s">
                                        <span class="flex-center padd-t-5">
                                            <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t"
                                                    data-title="编辑" @click="edit($index)"></i></em>
                                            <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除"
                                                    @click="scmx($index)"></i></em>
                                        </span>
                                    </div>
                                </td>
                                <p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="zui-table-fixed table-fixed-l" v-if="isShow">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                                <tr>
                                    <th class="cell-m">
                                        <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                                <tr v-for="(item, $index) in jsonList" :tabindex="$index" class="tableTr2">
                                    <td class="cell-m">
                                        <div class="zui-table-cell cell-m">{{$index+1}}</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="zui-table-fixed table-fixed-r" v-if="isShow">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                                <tr>
                                    <th class="cell-s">
                                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                        <table class="zui-table">
                            <tbody>
                                <tr v-for="(item, $index) in jsonList" :tabindex="$index" o class="tableTr2">
                                    <td class="cell-s">
                                        <div class="zui-table-cell cell-s">
                                            <span class="flex-center padd-t-5">
                                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t"
                                                        data-title="编辑" @click="edit($index)"></i></em>
                                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除"
                                                        @click="scmx($index)"></i></em>
                                            </span>

                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <page @go-page="goPage" v-if="isShowkd" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
                <div class="rkgl-position addList" v-if="isShow">
                    <span class="rkgl-fl" :id="money">
                        <i>材料进价总价: <em class="color-wtg">{{fDec(json.jjzj,2)}}元</em></i>
                        <i>材料零价总价: <em class="color-wtg">{{fDec(json.ljzj,2)}}元</em></i>
                    </span>
                    <span class="rkgl-fr">
						<button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
						<button class="tong-btn btn-parmary-f2a xmzb-db" @click="print" v-if="dyShow">打印</button>
						<button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick" v-if="cxshowAll && isCx() && !cxShow">冲销</button>
						<button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick" v-if="cxshowAll && cxShow && isCx()">取消冲销</button>
						<button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-show="zfShow">作废</button>
						<button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow && isCx()">提交</button>
						<button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow && isShFun()">审核</button>
						
                    </span>
                </div>
            </div>

        </div>
    </div>
    <!--侧边窗口-->
    <div class="side-form ng-hide pop-548" v-cloak  id="brzcList" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <!--诊疗类别-->
        <div class="ksys-side">
            <ul class="tab-edit-list tab-edit2-list">
                <li class="auto-focus">
                    <i>供货单位</i>
                    <select-input @change-data="resultChange" :child="ghdwList" :index="'dwmc'" :index_val="'dwbm'"
                        :val="popContents.ghdw" :search="true" :name="'popContents.ghdw'" ref="autofocus" :search="true">
                    </select-input>
                    <input type="hidden" v-model="popContent.ghdw" data-skip />
                </li>
                <li>
                    <i>材料名称</i>
                    <input id="ypmc" class="zui-input" @input="change(false,$event.target.value)" v-model="popContent.ypmc" @keydown="changeDown($event,'ypmc','searchCon')">
                    <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                        @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </li>
                <li>
                    <i>材料规格</i>
                    <input type="text" class="zui-input" v-model="popContent.ypgg" @keydown="nextFocus($event)"
                        disabled />
                </li>
                <li class="position">
                    <i>入库数量</i>
                    <input type="number" id="rksl" class="zui-input" v-model="popContent.rksl" @keydown="nextFocus($event)" />
                    <span class="cm" v-text="popContent.yfdwmc"></span>
                </li>
                <li>
                    <i>材料进价</i>
                    <input type="number"  class="zui-input" v-model="popContent.ypjj" @keydown="nextFocus($event,'','')"
                        :disabled="!csqxContent.cs00200100101" />
                </li>
                <li>
                    <i>材料零价</i>
                    <input type="number" class="zui-input" v-model="popContent.yplj" @keydown="nextFocus($event)"
                        :disabled="!csqxContent.cs00200100102" />
                </li>
                <li>
                    <i>材料批号</i>
                    <input type="text" id="scph" class="zui-input" v-model="popContent.scph" @keydown="nextFocus($event)" />
                </li>
                <li>
                    <i>产地</i>
                    <select-input ref="cdbm" @change-data="resultChange" :remote="true"  :child="ypcdList" :index="'cdmc'" :index_val="'cdbm'"
                        :val="popContent.cdbm" :search="true" :name="'popContent.cdbm'" :index_mc="'cdmc'">
                    </select-input>
                </li>
                <li>
                    <i>生产日期</i>
                    <input v-model="popContent.scrq" class="zui-input  times1" id="_scrq"  @click="showTime('_scrq','scrq')" @keyup="setTime($event,'popContent.yxqz')"
                    @keydown="nextFocus($event)"  >
                </li>
                <li>
                    <i>有效期至</i>
                    <input v-model="popContent.yxqz" class="zui-input  times2" id="_yxqz"  @click="showTime('_yxqz','yxqz')" @keyup="setTime($event,'popContent.yxqz')"
                        @keydown.13="addData($event)" >
                </li>
                <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input" disabled="disabled" v-model="popContent.fzbl" @keydown="nextFocus($event)"
                         />
                </li>
                <!-- <li>
                    <i>供货单位</i>
                    <select-input @change-data="resultChange" :not_empty="true" :child="ghdwList" :index="'dwmc'"
                                  :index_val="'dwbm'" :val="popContent.ghdw" :search="true" :name="'popContent.ghdw'">
                    </select-input>
            </li> 
                <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input type="text" class="zui-input" v-model="popContent.cpbzh" @keydown="nextFocus($event)" />
                </li>-->
                <li>
                    <i>批准文号</i>
                    <input type="text" class="zui-input" v-model="popContent.pzwh" @keydown="nextFocus($event)" />
                </li>

                <!--<li>
                    <i>备注描述</i>
                    <input type="text" class="zui-input" v-model="popContent.bzms"  @keydown="changeDown($event,'bzms','')" />
            </li>-->

            </ul>

        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="addData">保存</button>
        </div>
    </div>

    <script src="rkgl.js"></script>

</body>

</html>
