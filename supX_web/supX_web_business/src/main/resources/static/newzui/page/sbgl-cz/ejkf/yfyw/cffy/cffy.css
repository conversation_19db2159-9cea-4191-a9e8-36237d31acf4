.tong-btn {
  width: auto;
  min-width: 72px;
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
}
.font12 {
  font-size: 12px !important;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}
.wh240 {
  width: 240px!important;
}
.wh182 {
  width: 182px !important;
}
.wh100 {
  width: 100px !important;
}
.wh66 {
  width: 66px !important;
}
.wh112 {
  width: 112px !important;
}
.wh120 {
  width: 120px !important;
}
.wh122 {
  width: 122px !important;
}
.wh138 {
  width: 138px !important;
}
.wh200 {
  width: 200px !important;
}
.wh220 {
  width: 220px !important;
}
.wh150 {
  width: 150px !important;
}
.wh1000 {
  width: 80% !important;
}
.wh50 {
  width: 50px !important;
}
.wh70 {
  width: 70px !important;
}
.width162 {
  width: 162px !important;
}
.wh160 {
  width: 160px !important;
}
.wh453 {
  width: 453px !important;
}
.wh247 {
  width: 243px !important;
  display: flex;
  justify-content: start;
  align-items: center;
}
.wh179 {
  width: 179px !important;
}
.wh59 {
  width: 59px !important;
}
.padd {
  padding: 0 !important;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-ed {
  background: #edf2f1 !important;
}
.color-green {
  color: #1abc9c !important;
  font-style: normal;
}
.color-dsh {
  color: #f3b169;
}
.color-ysh {
  color: #45e135;
}
.color-wtg {
  color: #ff4735;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.border-d {
  border-bottom: 1px dashed #1abc9c;
}
.icon-bj {
  margin: 0 !important;
}
.icon-bj:before {
  top: 0px;
  left: 4px;
}
.icon-bj-t:before {
  top: -7px;
  left: 4px;
}
.tong-padded {
  padding: 20px 10px 10px;
}
.tong-search .zui-form .padd-l-40 {
  padding-left: 40px !important;
}
.rkgl-kd {
  width: 100%;
  padding: 5px 0 0;
  box-sizing: border-box;
  color: #333333;
  font-size: 14px;
}
.rkgl-kd span {
  padding-left: 22px;
}
.tab-edit-list .inner li {
  width: 100% !important;
  margin-bottom: 0 !important;
}
.ksys-side .zui-select-inline {
  margin-right: 0px !important;
}
.ksys-side .tab-edit-list li label .label-input {
  width: 148px;
}
.rkgl-position {
  position: fixed;
  bottom: 0px;
  display: flex;
  justify-content: flex-end;
  left: 10px;
  right: 10px;
  width: auto;
  z-index: 11;
  height: 70px;
  background: #fff;
  align-items: center;
  color: #81878e;
}
.rkgl-position .rkgl-fl {
  width: auto;
  float: left;
}
.rkgl-position .rkgl-fl i {
  float: left;
  padding-left: 20px;
}
.rkgl-position span {
  display: block;
}
.rkgl-position .rkgl-fr {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.mr-t108 {
  margin-top: 108px !important;
}
.mr-t168 {
  margin-top: 168px !important;
}
.tab-edit-list li label .label-input {
  text-indent: 10px;
}
.cffy-list {
  width: 100%;
}
.cffy-list li {
  width: 100%;
  font-size: 14px;
  display: flex;
  line-height: 30px;
  justify-content: flex-start;
}
.cffy-list li i {
  display: flex;
  justify-content: flex-start;
  /*width: calc((100% - 30px)/4);*/
  text-align: left;
  width: auto;
  color: #7f8fa4;
  margin-right: 10px;
}
.cffy-list li em {
  color: #354052;
  padding-left: 5px;
}
.cffy-cy {
  width: 100%;
  background: #edf2f1;
  height: 36px;
  line-height: 36px;
  margin-top: 10px;
}
.cffy-cy li {
  width: 100%;
}
.cffy-cy li i {
  display: block;
  float: left;
  text-align: center;
  width: calc(100% /11);
}
.cffy-cy li i:first-child {
  width: 50px;
}

.cffy-scr {
  line-height: 40px;
  overflow: auto;
}
.cffy-scr li {
  width: 100%;
  border: 1px solid #eee;
  border-top: none;
  overflow: hidden;
}
.cffy-scr li i {
  display: flex;
  justify-content: center;
  align-items: center;
  float: left;
  text-align: center;
  width: calc(100% /11);
}
.cffy-scr li i:first-child {
  width: 50px;
}
.cffy-scr li:nth-child(2n) {
  background: #fdfdfd;
}
.cffy-scr li:hover {
  background: rgba(26, 188, 156, 0.08) !important;
}
.cffy-cy1 li i,.cffy-scr1 li i{
  width: calc((100% - 350px)/5);
}
.zui-form .zui-form-label {
  left: 5px;
}
.pop-850 .ksys-side {
  padding: 15px 10px;
}
.width50{
  width: 50%;
}
.overflowHide{
  overflow: hidden;
}
.ksys-btn{
  height: 50px;
}
body .ksys-btn{
  position:absolute;
}
.zui-table-view .setScroll::-webkit-scrollbar{
  width: 10px;
  height: 10px;
}
.zui-table-view .zui-table-fixed.table-fixed-r {
  right: 10px !important;
}
.zui-table-view .zui-table-fixed.table-fixed-l{
  left: 10px !important;
}
.tem {
  position: relative;
  float: left;
  /*width: 300px;*/
  /*height: 450px;*/
  width: 800px;
  height: 500px;
  border: 1px solid green;
  /*margin-left: 20px;*/
  margin-top: 20px;
}
.bgbs{
  background-color: #ffffff;
}
.item {
  position: absolute;
  display: inline-block;
  margin-bottom: 20px;
  font-size: 14px;
  cursor: default;
  z-index: 100;
}

.item span {
  float: left;
  /*height: 38px;*/
  z-index: 1;
  /*line-height: 38px;*/
}
.ksys-btn{
  justify-content: space-between;
}
