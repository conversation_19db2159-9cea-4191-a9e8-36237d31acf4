<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>病员管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="xsexx.css">
</head>
<body class="skin-default">
<div class="wrapper" id="loadingPage">
    <div id="xselb" v-cloak>
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addData">登记</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">状态</label>
                    <div class="zui-input-inline wh120">
                        <select-input @change-data="resultChange"
                                      :data-notEmpty="true"
                                      :child="rydjZT_tran2"
                                      :index="ztType"
                                      :val="ztType"
                                      :name="'ztType'"
                                      :search="true"></select-input>
                    </div>
                </div>
                <div class="zui-inline padd-l-54">
                    <label class="zui-form-label ">时间段</label>
                    <div class="flex-container flex-align-c position">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh200 text-indent20" placeholder="不限定时间范围" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择处方结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">排序</label>
                    <div class="zui-input-inline wh120">
                        <select-input @change-data="resultPxChange"
                                      :data-notEmpty="true"
                                      :child="px_tran"
                                      :index="sort"
                                      :val="sort"
                                      :name="'sort'"
                                      :search="true"></select-input>
                    </div>
                    <div class="zui-input-inline wh100">
                        <select-input @change-data="resultPxfsChange"
                                      :data-notEmpty="true"
                                      :child="pxfs_tran"
                                      :index="order"
                                      :val="order"
                                      :name="'order'"
                                      :search="true"></select-input>
                    </div>
                </div>
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">检索</label>
                    <div class="zui-input-inline wh182">
                        <input class="zui-input" placeholder="请输入关键字" type="text" v-model="jsvalue" @keydown.enter="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
        <!--检索字段end-->

        <!--循环列表begin-->
        <div class="zui-table-view">
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>住院号</span></div>
                        </th>
                        <th class="cell-m">
                            <div class="zui-table-cell cell-m"><span>床位号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>产妇/婴儿姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>产妇/婴儿年龄</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>婴儿编号</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>婴儿姓名</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>婴儿性别</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-l"><span>出生时间</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>生产方式</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>身高</span></div>
                        </th>
                        <th>
                            <div class="zui-table-cell cell-s"><span>体重</span></div>
                        </th>
                        <th><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)">
                <p v-if="!xseList.length" class=" noData  text-center zan-border">暂无数据...</p>
                <table v-if="xseList.length" class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item, $index) in xseList"
                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','xseList'],$event)"
                        @dblclick="edit($index)"
                        ref="list">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zyh"></div>
                        </td>
                        <td class="cell-m">
                            <div class="zui-table-cell cell-m" v-text="item.rycwbh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.cfxm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.nl"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yebh"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.yexm"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="brxb_tran[item.xb]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-l"  v-text="fDate(item.csrq,'datetimes')"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="scfs_tran[item.scfs]"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.sg"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.tz"></div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-m">
                                <span @click="deleteData(item)" class="icon-sc" data-title="删除"></span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!--左侧固定-->
            <div class="zui-table-fixed table-fixed-l">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in xseList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()"
                            @click="checkSelect([$index,'one','xseList'],$event)">
                            <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="zui-table-fixed table-fixed-r">
                <div class="zui-table-header">
                    <table class="zui-table">
                        <thead>
                        <tr>
                            <th><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in xseList"
                            :tabindex="$index"
                            :class="[{'table-hovers':isChecked[$index]}]"
                            @click="checkSelect([$index,'one','xseList'],$event)">
                            <td>
                                <div class="zui-table-cell cell-m">
                                	<span @click="deleteData(item)" class="icon-sc" data-title="删除"></span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
        <!--循环列表end-->
    </div>

    <div class="side-form pop-548"  id="bjxq" v-cloak :class="{'ng-hide': !isShow}">
        <div class="fyxm-side-top">
            <span>新生儿登记</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side grid-box">
            <div class="col-xxl-6 padd">
                <div class="input-box">
                    <i class="input-box-title">妇/婴住院号</i>
                    <div class="pop-input-box position margin-b-20">
                    <input  class="zui-input" data-notEmpty="true" v-model="brxxContent.text" @keydown="changeDown($event,'text')"
                           @input="change(false,'text',$event.target.value)" id="zyh">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :current="dg.page" :rows="dg.rows" :total="total"
                                  :them="them" :them_tran="them_tran"
                                  @click-one="checkedOneOut" @click-two="selectOne" >
                    </search-table>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">妇/婴姓名</i>
                    <div class="pop-input-box position margin-b-20">
                        <input class="zui-input" v-model="popContent.cfxm" disabled="disabled">
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">婴儿编号</i>
                    <div class="pop-input-box position margin-b-20">
                        <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" type="number" v-model="popContent.yebh" @keydown="nextFocus($event)"
                           data-notEmpty="true" id="yebh">
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">婴儿姓名</i>
                    <div class="pop-input-box position margin-b-20">
                        <input class="zui-input" type="text" v-model="popContent.yexm" @keydown="nextFocus($event)"
                           data-notEmpty="true">
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">性别</i>
                    <div class="pop-input-box position margin-b-20" id="kdys">
                     	<select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="brxb_tran" :index="popContent.xb" :val="popContent.xb"
                                  :name="'popContent.xb'">
                    	</select-input>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">年龄</i>
                    <div class="zui-input-inline pop-input-box position margin-b-20">
                        <div class="col-xxl-6" style="padding-right: 5px;">
                            <input id="brnl" placeholder="" data-notEmpty="false" class="zui-input"
                                   :title="popContent.nl"
                                   v-model.trim="popContent.yenl" @blur="setCsrq"
                                   @keydown.enter="setCsrq($event)":disabled="readonly">
                        </div>
                        <div class="col-xxl-6">
                            <select-input
                                    :search="true"
                                    @change-data="resultNldwChange"
                                    :not_empty="false"
                                    :child="yenldw_tran"
                                    :index="popContent.yenldw"
                                    :val="popContent.yenldw"
                                    :name="'popContent.yenldw'" id="yenldw">
                            </select-input>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">出生时间</i>
                    <div class="pop-input-box position margin-b-20">
                         <input class="zui-input" data-notEmpty="true" id="csrq" :value="fDate(popContent.csrq,'datetime')" >
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">生产方式</i>
                    <div class="pop-input-box position margin-b-20">
                         <select-input @change-data="resultChange" :data-notEmpty="true"
                                  :child="scfs_tran" :index="popContent.scfs" :val="popContent.scfs"
                                  :name="'popContent.scfs'">
                    	</select-input>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">身高</i>
                    <div class="pop-input-box danwei-box position margin-b-20">
                        <input @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent class="zui-input" type="number" v-model="popContent.sg" @keydown="nextFocus($event)"
                           data-notEmpty="false">
                        <span class="cm">cm</span>
                    </div>
                </div>
            </div>
            <div class="col-xxl-6">
                <div class="input-box">
                    <i class="input-box-title">体重</i>
                    <div class="pop-input-box danwei-box position margin-b-20">
                        <input class="zui-input" @mousewheel.prevent @keydown.up.prevent @keydown.down.prevent type="number" v-model="popContent.tz" @keydown="nextFocus($event)"
                           data-notEmpty="false">
                        <span class="cm">kg</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="saveYe()">确定</button>
        </div>
    </div>
</div>
<script src="xsexx.js"></script>
</body>
</html>
