<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>调价单据查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b ">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>

        </div>
        <div class="tong-search" >
            <div class="zui-form" >
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">库房</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label margin-f-l10">时间段</label>
                    <div class="zui-input-inline margin-f-l20">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh240 text-indent20" placeholder="请选择申请日期" id="timeVal"/>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label margin-f-l10">检索</label>
                    <div class="zui-input-inline margin-f-l35">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model="param.parm" @keydown.13="goToPage(1)"/>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view ybglTable ">
        <!--入库列表-->
        <div class="zui-table-header" >
            <table class="zui-table table-width50-1">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>调价单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>审核标志</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>调价人</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单日期</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table table-width50-1">
                <tbody>
                <tr v-for="(item,$index) in djList" :class="[{'table-hovers':$index===activeIndex}]">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.tjdjh">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="isshzh_tran[item.qrzfbz]">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.tjrxm"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')"></div>
                    </td>
                    <p v-if="djList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
    </div>
</div>
</div>
<script src="tjdjcx.js"></script>
</body>

</html>
