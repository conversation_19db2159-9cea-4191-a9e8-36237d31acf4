.userNameBg{
    background:#708f89;
    /*height:120px;*/
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    /*position: fixed;*/
    top: 0;
    left: 0;
    right: 0;
    background-image: url("/newzui/pub/image/userImg.png");
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg{
    margin:10px 6px 0 8px;
    width: 100px;
    height: 100px;
}
.userNameImg img{
    width: 100px;
    height: 100px;
}
.text-color{
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}
.zyh,.bq,.ys,.brzt,.bz,.cwh{
    margin-right: 60px;
}
.userCwh{
    margin-bottom: 4px;
}
.fyhj {
    margin-right: 39px;
}
.yjhj {
    margin-right: 104px;
}
.zyts {
    margin-right: 32px;
}
.phone {
    margin-right: 53px;
}
.hl {
    margin-right: 52px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #B0BFBB;
    text-decoration: underline;
}
.content{
    background: #fff;
    overflow: hidden;
    height: calc(100% - 100px);
}
.blImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/<EMAIL>");
}
.xyzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/<EMAIL>");
}
.jcImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/<EMAIL>");
}
.wcjzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/<EMAIL>");
}
.qxwcjzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/Group <EMAIL>");
}
.blRight{
    position: absolute;
    right: 7px;
    display: flex;
    bottom: -40px;
    z-index:88;
}
.loadPage{
    margin: 7px 0;
    background: #fff;
    height: calc(100% - 46px);
}
.jchides{
    display: none;
}
/*.hzzx-top{*/
/*background:#f3f3f3;position: fixed;right: 10px;left: 10px;z-index: 999; padding-top: 10px;top: 0;*/
/*}*/
.dyblImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    height:80px;
    background-image: url("/newzui/pub/image/yuanquan.png");
}
.dyblImg:after{
    content: '';
    background-image: url("/newzui/pub/image/bl.png");
    width: 60%;
    height: 60%;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
}
.background-box1{
    height: 100%;
}
