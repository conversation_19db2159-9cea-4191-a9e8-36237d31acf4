<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>药品种类</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>
<style>

</style>
<body class="skin-default padd-b-10 padd-l-10 padd-r-10 padd-t-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="AddMdel">新增</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5 icon-font14" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search padd-b-10">
                <div class="flex-container flex-align-c">
                    <span class="ft-14 padd-r-10 ">检索</span>
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" v-model.trim="param.parm"/>
                </div>

        </div>
    </div>
    <div class="zui-table-view padd-l-10 padd-r-10"  z-height="full">
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>种类编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>种类名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>拼音简码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>作废标志</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    :tabindex="$index" @dblclick="edit($index)" @click="checkSelect([$index,'some']),clickOne($index)" >
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>

                    <td><div class="zui-table-cell cell-s" v-text="item.ypflbm"></div></td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            <div class="title title-width" >{{item.ypflmc}}</div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s relative">
                            {{item.pyjm}}
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <div class="switch">
                                <input  type="checkbox" true-value="0" false-value="1" v-model="item.zfbz" disabled/>
                                <label></label>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell  cell-s">
                            <span class="flex-center padd-t-5">
                                <em class="width30"><i class="icon-bj icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<div class="side-form ng-hide pop-width"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <div class="ksys-side">
            <span class="span0">
                <i>种类编码</i>
                <input type="text" class="zui-input border-r4" placeholder="自动生成" v-model="popContent.ypflbm"
                       @keydown="nextFocus($event)" disabled="disabled"/>
            </span>
        <span class="span0">
                <i>种类名称</i>
                <input type="text" @blur="setPYDM(popContent.ypflmc,'popContent','pyjm')" class="zui-input border-r4"  v-model="popContent.ypflmc" @keydown="nextFocus($event)" />
            </span>
        <span class="span0">
                <i>拼音代码</i>
                <input type="text" class="zui-input border-r4"  v-model="popContent.pyjm" @keydown="nextFocus($event)" />
            </span>
        <span class="span0">
                <i>作废标志</i>
            <div class="switch">
                 <input id="zt1"  type="checkbox"  true-value="0" false-value="1"   v-model="popContent.zfbz"/>
                <label for="zt1"></label>
                            </div>
            </span>
    </div>

    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>
<script src="yply.js"></script>
</body>

</html>
