<div class="cd_014" id="cd_014" style="height: 90%;">
    <div class="ksys-side">
        <ul class="tab-edit-list flex-start">
            <li>
                <i>个人编号</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac001" disabled="disabled"/>
            </li>
            <li>
                <i>姓&emsp;&emsp;名</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac003" disabled="disabled"/>
            </li>
            <li>
                <i>性&emsp;&emsp;别</i>
                <select-input @change-data="resultChange" id="xb" disable
                              :child="brxb_tran" :index="grxxJson.aac004" :val="grxxJson.aac004"
                              :name="'grxxJson.aac004'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>年&emsp;&emsp;龄</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.akc023" disabled="disabled"/>
            </li>
            <li>
                <i>身&nbsp;&nbsp;份&nbsp;证号&emsp;&emsp;码</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac002" disabled="disabled"/>
            </li>
            <li>
                <i>出生日期</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aac006" disabled="disabled"/>
            </li>
            <li>
                <i>单位名称</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.aab004" disabled="disabled"/>
            </li>
            <li>
                <i>个人帐户<br>种&emsp;&emsp;类</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc303" disabled="disabled"/>
            </li>
            <li>
                <i>个人账户余&emsp;&emsp;额</i>
                <input type="text" class="zui-input  background-h" v-model="grxxJson.ykc194" disabled="disabled"/>
            </li>
            <li class="zflb" v-if="gsdataset_show">
                <i class="color-wtg">工伤诊断</i>
                <select-input @change-data="commonResultChange" :not_empty="true"
                              :child="gsdataset_tran" :index="'alc022'" :index_val="'yke109'" :val="grxxJson.yke109"
                              :name="'grxxJson.yke109'" :search="true">
                </select-input>
            </li>
            <li class="zflb" v-if="datasetyka026_show">
                <i class="color-wtg">特慢病</i>
                <select-input @change-data="commonResultChange" :not_empty="true"
                              :child="datasetyka026_tran" :index="'bkc117'" :index_val="'bkc014'" :val="grxxJson.bkc014"
                              :name="'grxxJson.bkc014'" :search="true">
                </select-input>
            </li>
            <li class="zflb">
                <i class="color-wtg">支付类别</i>
                <select-input @change-data="resultChange" id="aka130"
                              :child="cd_aka130_tran" :index="grxxJson.aka130" :val="grxxJson.aka130"
                              :search="true" :name="'grxxJson.aka130'" :not_empty="true">
                </select-input>
            </li>
            <li class="zflb">
                <i class="color-wtg">公务员<br>类&emsp;别</i>
                <select-input disable @change-data="resultChange" id="ykc117"
                              :child="cd_ykc117_tran" :index="grxxJson.ykc117" :val="grxxJson.ykc117"
                              :search="true" :name="'grxxJson.ykc117'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>异地标志</i>
                <select-input @change-data="resultChange" id="ydbz" disable
                              :child="cdydbz_tran" :index="grxxJson.ydbz" :val="grxxJson.ydbz"
                              :name="'grxxJson.ydbz'" :not_empty="true">
                </select-input>
            </li>
            <li>
                <i>就诊人<br/>关&emsp;系</i>
                <select-input @change-data="resultChange" id="ydbz"
                              :child="skr_tran" :index="grxxJson.skrgx" :val="grxxJson.skrgx"
                              :name="'grxxJson.skrgx'" :not_empty="true">
                </select-input>
            </li>
        </ul>

    </div>

    <!--    <div class="zui-row buttonbox">-->
    <!--        <button class="tong-btn btn-parmary-f2a xmzb-db paddr-r5" @click="load()">读卡</button>-->
    <!--        <button class="tong-btn btn-parmary xmzb-db paddr-r5" @click="enter()">引入</button>-->
    <!--    </div>-->
</div>
<script type="application/javascript" src="/newzui/pub/js/uuid.js"></script>
<!--<script type="application/javascript" src="../../../CommonPage/insurancePort/014cdyhyb/014cdyhyb.js"></script>-->
<script type="application/javascript" src="/newzui/page/CommonPage/insurancePort/014cdyhyb/014cdyhyb.js"></script>

