<<<<<<< .mine
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ckgl.css" rel="stylesheet"/>
     <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="background-box">
<div class="wrapper printHide" id="jyxm_icon">
    <div class="panel" v-cloak>
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-show="isShowkd">开单</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="isShowpopL">添加耗材</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData(1)">刷新</button>
        </div>
        <div class="tong-search" :class="{'tong-padded':isShow}">
            <div class="zui-form" v-show="isShowkd">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">二级库房</label>
                    <div class="zui-input-inline wh122" style="margin-left: 20px">
                        <select-input @change-data="resultChange"  :child="yfList" :index="'yfmc'" :index_val="'yfbm'"
                                      :val="ckdContent.yfbm" :search="true" :name="'ckdContent.yfbm'" id="yfbm">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">状态</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultChange"
                                      :child="ckglzt_tran"
                                      :index="popContent.zt"
                                      :val="popContent.zt"
                                      :name="'popContent.zt'" @keydown="nextFocus($event)" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline margin-f-l5 flex-container flex-align-c">
                        <input class="zui-input todate wh200 " placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="searchHc()" type="text" v-model="search"/>
                    </div>
                </div>
				<div class="zui-inline" style="padding: 0 0 8px 0px;">
				    <span class="color-wtg font-18">零价总额：{{totalyplj}}元&ensp;</span>
				</div>
				<div class="zui-inline" style="padding: 0 0 8px 0px;">
				    <span class="color-wtg font-18">进价总额：{{totalypjj}}元&ensp;</span>
				</div>
            </div>
            <div class="jbxx fyxm-hide" :class="{'btn-show':isShow}">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">二级库房</label>
                            <div class="zui-input-inline  wh122" style="margin-left: 20px">
                                <select-input @change-data="resultChange"  :child="yfList" :index="'yfmc'" :index_val="'yfbm'"
                                              :val="ckdContent.yfbm" :search="true" :name="'ckdContent.yfbm'" id="yfbm" :disable="jyinput">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label ">出库方式 </label>
                            <div class="zui-input-inline wh122 margin-l-25">
                                <select-input :cs="true"
                                                              @change-data="resultChange"
                                                              :not_empty="false"
                                                              :child="ckfs_tran"
                                                              :search="true"
                                                              :index="ckdContent.ckfs"
                                                              :val="ckdContent.ckfs"
                                                              :name="'ckdContent.ckfs'" @keydown="nextFocus($event)" >
                               </select-input>

                            </div>
                        </div>
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">备注描述</label>
                            <div class="zui-input-inline  wh150 margin-l-30">
                               <input class="zui-input" placeholder="请输入备注" type="text" id="bzms" v-model="ckdContent.bzms" :disabled="jyinput"/>
                            </div>
                        </div>
                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i class="color-wtg" v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view   "  :money="money"  v-cloak>
        <!--出库列表-->
        <div class="zui-table-header" key="a" v-if="isShowkd">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>出库单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单时间</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>总零价</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>总进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" key="a" v-if="isShowkd"  @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item,$index) in ckdList" :tabindex="$index"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'background-red':item.totalypjj< 0}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','ckdList'],$event)" @dblclick="showDetail($index,item)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ckdh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')"></div>
                    </td>
					<td>
					    <div class="zui-table-cell cell-s text-right" v-text="fDec(item.totalyplj,2)"></div>
					</td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(item.totalypjj,2)"></div>
                    </td>
					<td>
					    <div class="zui-table-cell cell-s" v-text="item.zdrxm"></div>
					</td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.bzms"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <!--v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.sfzfbz=='1' ? 'color-ysh' : item.sfzfbz=='2' ? 'color-yzf' : '' "-->
                        <i v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : '' "></i></div>

                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                            <span  class="flex-center padd-t-5">
                                <em class="width30"  v-if="item.shzfbz== 0">
                                    <i class="icon-sh" @click="showDetail($index,item)" data-title="审核"></i></em>
                                <em class="width30" v-if="item.shzfbz== 0">
                                	<i class="icon-js" @click="invalidData($index)" data-title="作废" ></i>
                                </em>
                                <em class="width30"  v-if="item.shzfbz == '0' || item.shzfbz == '1'">
                                	<i class="icon-bj" @click="editIndex($index)" data-title="编辑"></i>
                                </em>
                               </span>
                        </div>
                    </td>
                    <p v-show="ckdList.length==0" class=" noData text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <!--添加材料-->
        <div class="zui-table-header " key="b" v-if="isShow" >
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-over-2"><span>供货单位</span></div></th>
					<th><div class="zui-table-cell cell-xl text-over-2"><span>材料编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-over-2"><span>材料名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
					<th>
					    <div class="zui-table-cell cell-s"><span>已冲销数量</span></div>
					</th>
                    <th><div class="zui-table-cell cell-s"><span>材料进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>进价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>材料批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>二级库房单位</span></div></th>
					
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
					<th v-if="cxShow">
					    <div class="zui-table-cell cell-s"><span>冲销数量</span></div>
					</th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " key="b" v-if="isShow" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item,$index) in jsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','jsonList'],$event)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                            <div class="zui-table-cell cell-xl text-over-2" v-text="item.ghdwmc"></div>
                        </td>
						<td>
						    <div class="zui-table-cell cell-xl text-over-2" v-text="item.ypbm"></div>
						</td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2" v-text="item.ypmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypgg">
                            <div v-text="item.ypgg"></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cksl">序号</div>
                    </td>
					<td>
					    <div class="zui-table-cell cell-s">{{item.ycxsl}}</div>
					</td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(item.ypjj,4)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(Mul(item.ypjj,item.cksl),2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(item.yplj,4)">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(Mul(item.yplj,item.cksl),2)">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.scph">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                    </td>
					
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
					<td v-if="cxShow">
					    <div class="zui-table-cell cell-s ">
					        <input class="zui-input height-28"  @input="getCxsl(item.cksl,item.cxsl,item.ycxsl,$index)"  :disabled="item.ycxsl >= item.cksl "  v-model="item.cxsl" />
					    </div>
					</td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" @click="edit($index)" data-title="编辑"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" @click="scmx($index)" data-title="删除"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <!-- 左侧固定-->
        <div class="zui-table-fixed table-fixed-l" key="b" v-if="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','jsonList'],$event)">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
<!--end-->
        <div class="zui-table-fixed table-fixed-r" key="b" v-if="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','jsonList'],$event)">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage"  key="a" v-if="isShowkd" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="zui-table-tool rkgl-position" key="b" style="bottom:10px !important" v-if="isShow">
           <span class="rkgl-fl">
               <i>材料进价总价: <em class="color-wtg">{{json.jjzj}}元</em></i>
               <i>材料零价总价: <em class="color-wtg">{{json.ljzj}}元</em></i>
           </span>
            <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print" v-if="dyShow">打印</button>
				<button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick" v-if="isCx() && dyShow">冲销</button>
				<button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick" v-if="!dyShow && cxShow && isCx()">取消冲销</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-show="zfShow">作废</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow && isCx()">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow && isShFun()">审核</button>
           </span>
        </div>
    </div>






</div>
</div>
    <!--侧边窗口-->
<div class="side-form ng-hide pop-548"  style="padding-top: 0;"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">

            <li>
                <i>经办人</i>
                <select-input ref="autofocus"  @change-data="resultChange" :not_empty="true" :child="ryList" :index="'ryxm'"
                              :index_val="'rybm'" :val="ckdContent.jbr" :search="true" :name="'ckdContent.jbr'" id="jbr">
                </select-input>
        </li>
            
            <li>
                    <i>制单日期</i>
                    <input class="zui-input" disabled="disabled" v-model="ckdContent.zdrq">
            </li>
        </ul>
        <ul class="tab-edit-list tab-edit2-list">


            <li>
                    <i>材料名称</i>
                    <input class="zui-input text-indent10"  id="ypmc" v-model="popContent.ypmc" @keyup.up.down.enter="changeDown($event,'ypmc','searchCon')"
                           @input="change(false,'ypmc',$event.target.value)" data-notEmpty="true">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :total="total" :them="them" :them_tran="them_tran"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
            </li>
            <li class="auto-focus">
                <i>供货单位</i>
                <input type="text" disabled="disabled" class="zui-input" v-model="popContent.ghdwmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>出库数量</i>
                    <input type="number" class="zui-input" id="cksl" v-model="popContent.cksl" @keyup="changeDown($event,'cksl','')"/>
                    <em class="cm" v-text="popContent.yfdwmc"></em>
            </li>

             <li>
                    <i>材料规格</i>
                    <input type="text" disabled="disabled" class="zui-input" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>库存数量</i>
                    <input type="number" class="zui-input" v-model="popContent.kcsl" @keydown="nextFocus($event)" disabled="disabled"/>
            </li>
            <li>
                    <i>材料进价</i>
                    <input type="number" class="zui-input background-h" disabled="disabled"  v-model="popContent.ypjj" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>材料零价</i>
                    <input type="number" class="zui-input background-h" disabled="disabled" v-model="popContent.yplj" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>材料批号</i>
                    <input type="text" class="zui-input" v-model="popContent.scph" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                    <i>生产日期</i>
                    <input type="text" class="zui-input  " id="_scrq" v-model="fDate(popContent.scrq,'date')"  disabled/>
            </li>
            <li>
                    <i>有效期至</i>
                    <input type="text" class="zui-input  " id="_yxqz" v-model="fDate(popContent.yxqz,'date')" disabled/>
            </li>
            <li>
                    <i>产地</i>
                    <input class="zui-input" v-model="popContent.cdmc" disabled="disabled">
            </li>
            <!-- <li>
                    <i>二级库房单位</i>
                    <input type="text" class="zui-input background-h" disabled="disabled"  v-model="popContent.yfdwmc">
            </li> -->
            <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input " disabled="disabled"  v-model="popContent.fzbl">
            </li>
            <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input class="zui-input background-h" v-model="popContent.cpbzh" disabled="disabled" />
            </li>
            <li>
                    <i>批准文号</i>
                    <input v-model="popContent.pzwh" class="zui-input background-h" disabled="disabled"/>
            </li>
            <!--<li>
                    <i>备注说明</i>
                    <input type="text" class="zui-input" @keydown="changeDown($event,'bzsm')" v-model="popContent.bzms" id="bzms">
            </li>-->
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>

<script src="ckgl.js"></script>
</body>

</html>
||||||| .r0
=======
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>出库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="ckgl.css" rel="stylesheet"/>
     <link href="rydj.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="printArea printShow"></div>
<div class="background-box">
<div class="wrapper printHide" id="jyxm_icon">
    <div class="panel" v-cloak>
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="kd(0)" v-show="isShowkd">开单</button>
            <button class="tong-btn btn-parmary icon-xz1 paddr-r5 " @click="kd(1)" v-if="isShowpopL">添加耗材</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="getData(1)">刷新</button>
        </div>
        <div class="tong-search" :class="{'tong-padded':isShow}">
            <div class="zui-form" v-show="isShowkd">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">二级库房</label>
                    <div class="zui-input-inline wh122" style="margin-left: 20px">
                        <select-input @change-data="resultChange"  :child="yfList" :index="'yfmc'" :index_val="'yfbm'"
                                      :val="ckdContent.yfbm" :search="true" :name="'ckdContent.yfbm'" id="yfbm">
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label ">状态</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultChange"
                                      :child="ckglzt_tran"
                                      :index="popContent.zt"
                                      :val="popContent.zt"
                                      :name="'popContent.zt'" @keydown="nextFocus($event)" >
                        </select-input>

                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">时间段</label>
                    <div class="zui-input-inline margin-f-l5 flex-container flex-align-c">
                        <input class="zui-input todate wh200 " placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh200 " placeholder="请选择申请结束时间" id="timeVal1" />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label ">检索</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input wh180" placeholder="请输入关键字" @keydown.enter="searchHc()" type="text" v-model="search"/>
                    </div>
                </div>
				<div class="zui-inline" style="padding: 0 0 8px 0px;">
				    <span class="color-wtg font-18">零价总额：{{totalyplj}}元&ensp;</span>
				</div>
				<div class="zui-inline" style="padding: 0 0 8px 0px;">
				    <span class="color-wtg font-18">进价总额：{{totalypjj}}元&ensp;</span>
				</div>
            </div>
            <div class="jbxx fyxm-hide" :class="{'btn-show':isShow}">
                <div class="jbxx-size">
                    <div class="jbxx-position">
                        <span class="jbxx-top"></span>
                        <span class="jbxx-text">基本信息</span>
                        <span class="jbxx-bottom"></span>
                    </div>
                    <div class="zui-form padd-l24 padd-t-20">
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">二级库房</label>
                            <div class="zui-input-inline  wh122" style="margin-left: 20px">
                                <select-input @change-data="resultChange"  :child="yfList" :index="'yfmc'" :index_val="'yfbm'"
                                              :val="ckdContent.yfbm" :search="true" :name="'ckdContent.yfbm'" id="yfbm" :disable="jyinput">
                                </select-input>
                            </div>
                        </div>
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label ">出库方式 </label>
                            <div class="zui-input-inline wh122 margin-l-25">
                                <select-input :cs="true"
                                                              @change-data="resultChange"
                                                              :not_empty="false"
                                                              :child="ckfs_tran"
                                                              :search="true"
                                                              :index="ckdContent.ckfs"
                                                              :val="ckdContent.ckfs"
                                                              :name="'ckdContent.ckfs'" @keydown="nextFocus($event)" >
                               </select-input>

                            </div>
                        </div>
                        <div class="zui-inline padd-l-40">
                            <label class="zui-form-label">备注描述</label>
                            <div class="zui-input-inline  wh150 margin-l-30">
                                <input class="zui-input" placeholder="请输入备注" type="text"  val="门诊出库" id="bzms" v-model="ckdContent.bzms" disabled/>
                            </div>
                        </div>
                    </div>
                    <div class="rkgl-kd">
                        <span>开单日期:<i class="color-wtg" v-text="zdrq"></i></span>
                        <span>开单人：<i class="color-wtg" v-text="zdyxm"></i></span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="zui-table-view"  :money="money"  v-cloak>
        <!--出库列表-->
        <div class="zui-table-header" key="a" v-if="isShowkd">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-l"><span>出库单号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单时间</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>总零价</span></div></th>
					<th><div class="zui-table-cell cell-s"><span>总进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>制单员</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                    <th class="cell-l"><div class="zui-table-cell cell-l"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" key="a" v-if="isShowkd"  @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item,$index) in ckdList" :tabindex="$index"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex,'background-red':item.totalypjj< 0}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','ckdList'],$event)" @dblclick="showDetail($index,item)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.ckdh"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.zdrq,'date')"></div>
                    </td>
					<td>
					    <div class="zui-table-cell cell-s text-right" v-text="fDec(item.totalyplj,2)"></div>
					</td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(item.totalypjj,2)"></div>
                    </td>
					<td>
					    <div class="zui-table-cell cell-s" v-text="item.zdrxm"></div>
					</td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.bzms"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s">
                            <!--v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.sfzfbz=='1' ? 'color-ysh' : item.sfzfbz=='2' ? 'color-yzf' : '' "-->
                        <i v-text="zhuangtai[item.shzfbz]" :class="item.shzfbz=='0' ? 'color-dsh':item.shzfbz=='1' ? 'color-ysh' : item.shzfbz=='2' ? 'color-yzf' : '' "></i></div>

                    </td>
                    <td class="cell-l">
                        <div class="zui-table-cell cell-l">
                            <span  class="flex-center padd-t-5">
                                <em class="width30"  v-if="item.shzfbz== 0">
                                    <i class="icon-sh" @click="showDetail($index,item)" data-title="审核"></i></em>
                                <em class="width30" v-if="item.shzfbz== 0">
                                	<i class="icon-js" @click="invalidData($index)" data-title="作废" ></i>
                                </em>
                                <em class="width30"  v-if="item.shzfbz == '0' || item.shzfbz == '1'">
                                	<i class="icon-bj" @click="editIndex($index)" data-title="编辑"></i>
                                </em>
                               </span>
                        </div>
                    </td>
                    <p v-show="ckdList.length==0" class=" noData text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <!--添加设备-->
        <div class="zui-table-header " key="b" v-if="isShow" >
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-over-2"><span>供货单位</span></div></th>
					<th><div class="zui-table-cell cell-xl text-over-2"><span>设备编码</span></div></th>
                    <th><div class="zui-table-cell cell-xl text-over-2"><span>设备名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>设备规格</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>出库数量</span></div></th>
					<th>
					    <div class="zui-table-cell cell-s"><span>已冲销数量</span></div>
					</th>
                    <th><div class="zui-table-cell cell-s"><span>设备进价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>进价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>设备零价</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>零价金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>设备批号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>产地</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>库房单位</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>二级库房单位</span></div></th>
					
                    <th><div class="zui-table-cell cell-s"><span>分装比例</span></div></th>
					<th v-if="cxShow">
					    <div class="zui-table-cell cell-s"><span>冲销数量</span></div>
					</th>
                    <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " key="b" v-if="isShow" id="zui-table" @scroll="scrollTable($event)">
            <table class="zui-table">
                <tbody>
                <tr v-for="(item,$index) in jsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)"
                    @mouseleave="hoverMouse()"
                    @click="checkSelect([$index,'one','jsonList'],$event)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1">序号</div>
                    </td>
                    <td>
                            <div class="zui-table-cell cell-xl text-over-2" v-text="item.ghdwmc"></div>
                        </td>
						<td>
						    <div class="zui-table-cell cell-xl text-over-2" v-text="item.ypbm"></div>
						</td>
                    <td>
                        <div class="zui-table-cell cell-xl text-over-2" v-text="item.ypmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.ypgg">
                            <div v-text="item.ypgg"></div>
                        </div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cksl">序号</div>
                    </td>
					<td>
					    <div class="zui-table-cell cell-s">{{item.ycxsl}}</div>
					</td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(item.ypjj,4)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(Mul(item.ypjj,item.cksl),2)">序号</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(item.yplj,4)">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s text-right" v-text="fDec(Mul(item.yplj,item.cksl),2)">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.scph">状态</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.cdmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.kfdwmc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div>
                    </td>
					
                    <td>
                        <div class="zui-table-cell cell-s" v-text="item.fzbl"></div>
                    </td>
					<td v-if="cxShow">
					    <div class="zui-table-cell cell-s ">
					        <input class="zui-input height-28"  @input="getCxsl(item.cksl,item.cxsl,item.ycxsl,$index)"  :disabled="item.ycxsl >= item.cksl "  v-model="item.cxsl" />
					    </div>
					</td>
                    <td  class="cell-s">
                        <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" @click="edit($index)" data-title="编辑"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" @click="scmx($index)" data-title="删除"></i></em>
                            </span>
                        </div>
                    </td>
                    <p v-show="jsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>
        </div>
        <!-- 左侧固定-->
        <div class="zui-table-fixed table-fixed-l" key="b" v-if="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','jsonList'],$event)">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
<!--end-->
        <div class="zui-table-fixed table-fixed-r" key="b" v-if="isShow">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-s"><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index"  class="tableTr2" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','jsonList'],$event)">
                        <td  class="cell-s">
                            <div class="zui-table-cell cell-s">
                                <span class="flex-center padd-t-5">
                                <em v-if="mxShShow" class="width30"><i class="icon-bj  icon-bj-t" data-title="编辑" @click="edit($index)"></i></em>
                                <em v-if="mxShShow" class="width30"><i class="icon-sc" data-title="删除" @click="scmx($index)"></i></em>
                            </span>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage"  key="a" v-if="isShowkd" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        <div class="zui-table-tool rkgl-position" key="b" style="bottom:10px !important" v-if="isShow">
           <span class="rkgl-fl">
               <i>设备进价总价: <em class="color-wtg">{{json.jjzj}}元</em></i>
               <i>设备零价总价: <em class="color-wtg">{{json.ljzj}}元</em></i>
           </span>
            <span class="rkgl-fr">
                <button class="tong-btn btn-parmary-d9 xmzb-db" @click="cancel">取消</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="print" v-if="dyShow">打印</button>
				<button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick" v-if="isCx() && dyShow">冲销</button>
				<button class="tong-btn btn-parmary-f2a xmzb-db" @click="cxClick" v-if="!dyShow && cxShow && isCx()">取消冲销</button>
                <button class="tong-btn btn-parmary-f2a xmzb-db" @click="invalidData()" v-show="zfShow">作废</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="submitAll()" v-show="TjShow && isCx()">提交</button>
                <button class="tong-btn btn-parmary xmzb-db" @click="passData" v-show="ShShow && isShFun()">审核</button>
           </span>
        </div>
    </div>






</div>
</div>
    <!--侧边窗口-->
<div class="side-form ng-hide pop-548"  style="padding-top: 0;"  id="brzcList" role="form">
    <div class="fyxm-side-top">
        <span v-text="title"></span>
        <span class="fr closex ti-close" @click="closes"></span>
    </div>
    <!--诊疗类别-->
    <div class="ksys-side">
        <ul class="tab-edit-list tab-edit2-list">

            <li>
                <i>经办人</i>
                <select-input ref="autofocus"  @change-data="resultChange" :not_empty="true" :child="ryList" :index="'ryxm'"
                              :index_val="'rybm'" :val="ckdContent.jbr" :search="true" :name="'ckdContent.jbr'" id="jbr">
                </select-input>
        </li>
            
            <li>
                    <i>制单日期</i>
                    <input class="zui-input" disabled="disabled" v-model="ckdContent.zdrq">
            </li>
        </ul>
        <ul class="tab-edit-list tab-edit2-list">


            <li>
                    <i>设备名称</i>
                    <input class="zui-input text-indent10"  id="ypmc" v-model="popContent.ypmc" @keyup.up.down.enter="changeDown($event,'ypmc','searchCon')"
                           @input="change(false,'ypmc',$event.target.value)" data-notEmpty="true">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :total="total" :them="them" :them_tran="them_tran"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
            </li>
            <li class="auto-focus">
                <i>供货单位</i>
                <input type="text" disabled="disabled" class="zui-input" v-model="popContent.ghdwmc" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>出库数量</i>
                    <input type="number" class="zui-input" id="cksl" v-model="popContent.cksl" @keyup="changeDown($event,'cksl','')"/>
                    <em class="cm" v-text="popContent.yfdwmc"></em>
            </li>

             <li>
                    <i>设备规格</i>
                    <input type="text" disabled="disabled" class="zui-input" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>库存数量</i>
                    <input type="number" class="zui-input" v-model="popContent.kcsl" @keydown="nextFocus($event)" disabled="disabled"/>
            </li>
            <li>
                    <i>设备进价</i>
                    <input type="number" class="zui-input background-h" disabled="disabled"  v-model="popContent.ypjj" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>设备零价</i>
                    <input type="number" class="zui-input background-h" disabled="disabled" v-model="popContent.yplj" @keydown="nextFocus($event)"/>
            </li>
            <li>
                    <i>设备批号</i>
                    <input type="text" class="zui-input" v-model="popContent.scph" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                    <i>生产日期</i>
                    <input type="text" class="zui-input  " id="_scrq" v-model="fDate(popContent.scrq,'date')"  disabled/>
            </li>
            <li>
                    <i>有效期至</i>
                    <input type="text" class="zui-input  " id="_yxqz" v-model="fDate(popContent.yxqz,'date')" disabled/>
            </li>
            <li>
                    <i>产地</i>
                    <input class="zui-input" v-model="popContent.cdmc" disabled="disabled">
            </li>
            <!-- <li>
                    <i>二级库房单位</i>
                    <input type="text" class="zui-input background-h" disabled="disabled"  v-model="popContent.yfdwmc">
            </li> -->
            <li>
                    <i>分装比例</i>
                    <input type="text" class="zui-input " disabled="disabled"  v-model="popContent.fzbl">
            </li>
            <li>
                    <i>产品标准&ensp;&ensp;号</i>
                    <input class="zui-input background-h" v-model="popContent.cpbzh" disabled="disabled" />
            </li>
            <li>
                    <i>批准文号</i>
                    <input v-model="popContent.pzwh" class="zui-input background-h" disabled="disabled"/>
            </li>
            <!--<li>
                    <i>备注说明</i>
                    <input type="text" class="zui-input" @keydown="changeDown($event,'bzsm')" v-model="popContent.bzms" id="bzms">
            </li>-->
        </ul>
    </div>
    <div class="ksys-btn">
        <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
        <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
    </div>
</div>

<script src="ckgl.js"></script>
</body>

</html>
>>>>>>> .r121
