    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
        data:{
            jsonList: [],
            sum:false,
            yfkfList: [],
            ypzlList: [],
            yfkf: 0, //药房库房信息
            param: {
                'page': 1,
                'rows': 10,
                'sort': '',
                'ywbm': '0',
                'fsbm': '0',
                'order': '',
                'shzfbz': 1,
                'beginrq': null,
                'endrq': null,
                'parm': '',
            },
            barContent:{},
            fsList:{
                "0":'全部',
                "1":'明细'
            },
            ywxzList:{
                "0":'门诊查询',
                "1":'住院查询',
                "2":'业务查询'
            }
        },
        created:function(){
            this.getYP();
        },
        watch: {
            sum: function() {
                this.param.order = this.sum ? 'sum' : '';
                this.getData();
            },
        },
        computed:{
            money:function(){
                var sum = this.jsonList.reduce(function (total, num) {
                    return  total + num.yplj*num.cfyl
                }, 0)
                return sum.toFixed(2);
            },
        },
        methods:{
            //获取数据
            getData: function(num) {
                this.param.page =num || this.param.page
                $.getJSON("/actionDispatcher.do?reqUrl=YfbCxtjAll&types=xs&parm=" + JSON.stringify(this.param), function(json) {
                    if(json.a == "0") {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.jsonList = json.d.list;

                    }
                });
            },
            getYP: function() {
                //药品种类
                var that=this;
                this.updatedAjax('/actionDispatcher.do?reqUrl=New1YkglKfwhYpsx&types=query&dg='+ JSON.stringify(this.param), function(data) {
                        if(data.a == 0) {
                            that.ypzlList = data.d.list;
                            Vue.set(that.param,'ypzlbm',that.ypzlList[0].ypzlbm);
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
                //库房列表
                this.updatedAjax('/actionDispatcher.do?reqUrl=GetDropDown&types=yf', function(data) {
                        if(data.a == 0) {
                            that.yfkfList = data.d.list;
                            Vue.set(that.param,'yfbm',that.yfkfList[0].yfbm);
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });
                this.getData();

            },
            resultRydjChange: function (val) {
                //先获取到操作的哪一个
                var types = val[2][val[2].length - 1];
                switch (types) {
                    case "yfbm":
                        Vue.set(this.param, 'yfbm', val[0]);
                        Vue.set(this.param, 'yfmc', val[4]);
                        this.getData();
                        break;
                    case "fsbm":
                        Vue.set(this.param, 'fsbm', val[0]);
                        Vue.set(this.param, 'fsmc', val[4]);
                        if(val[0]==0){
                            this.sum=true;
                        }else{
                            this.sum=false;
                        }
                        this.getData();
                        break;
                    case "ywbm":
                        Vue.set(this.param, 'ywbm', val[0]);
                        Vue.set(this.param, 'ywmc', val[4]);
                        this.getData();
                        break;
                    case "ypzlbm":
                        Vue.set(this.param, 'ypzlbm', val[0]);
                        Vue.set(this.param, 'ypzlmc', val[4]);
                        this.getData();
                        break;
                    default:
                        break;
                }
                this.$forceUpdate()
            },
        }
    });
    laydate.render({
        elem: '.todate',
        trigger: 'click',
        theme: '#1ab394',
        range: true,
        done: function (value, data) {
            // wrapper.param.time = value
            wrapper.param.beginrq = value.slice(0,10);
            wrapper.param.endrq =value.slice(13,23);
        }
    });


