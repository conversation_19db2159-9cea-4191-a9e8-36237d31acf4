.height-300{
    height: 2.8656579550664834rem;
    overflow: auto;
}
.height-300 input{
    height: 0.24216216216216216rem;
}
#model{
    width: 60%;
    max-width: 8.64864864864865rem;
}
.bqcydj_model{
    padding: 0.08648648648648649rem;
    height: 3.1135135135135137rem;
    overflow: auto;
    background: rgba(255, 255, 255, 1);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.minWh70{
    min-width: 0.6054054054054054rem;
}

.wh30{
    width: calc(100% / 3);
}
.cgjhzxqk,.kcxq{
    height: 1.2972972972972974rem;
}
.hzList{
    height: auto;
}
.background-box{
    height: 100%;
}

.zui-table-view .zui-table-cell {
    
    min-width: 0.43243243243243246rem;
   
}
.zui-table-view .zui-table-cell:not([over-auto]) {
    text-align: center;
    margin: 0 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}
.zui-table-view .zui-table-cell {
    font-size: 0.08648648648648649rem;
   
}
.zui-table-view .cell-xl {
    width: 4rem !important;
}
.zui-table-view .cell-s {
    width: 0.8648648648648649rem !important;
}
.zui-table-view .zui-table-cell {
     line-height: unset;
}
.zui-table-view .zui-table-header .zui-table-cell {
    height: unset;
    line-height: unset;
    padding: unset; 
}
.zui-table-body {
   transform: translateZ(0);
   overflow: scroll !important;
}