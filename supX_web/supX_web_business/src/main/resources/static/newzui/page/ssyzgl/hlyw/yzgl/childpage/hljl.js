/**
 * Created by mash on 2017/11/24.
 */
var toolMenu = new Vue({
    el: '.toolMenu',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        index2: 0,
        hllx:'1',
        save: false,
        qsxzList:[],
        N03004200254: yzclRight.caqxContent.N03004200254,
        jlxqContent: yzclLeft.HszbrItem,
    },
    mounted:function(){
        this.iscf()
    },
    methods: {
        commonResultChange:function(val){
            this.jlxqContent.yebh=val[0];
            if(val[5] != 0){
                this.jlxqContent.rl=dateDiff(toolMenu.fDate(this.qsxzList[val[5]].csrq,'date'),toolMenu.fDate(new Date(),'date'))
            }
            this.$forceUpdate();
            jlxq.$forceUpdate();
            jlxq.getJlData();
        },
        iscf: function () {
            var that=this;
            console.log(111)
            var parm = {
                zyh: this.jlxqContent.zyh
            }
            this.qsxzList = [];
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == '0' && json.d.list != null && json.d.list.length > 0) {
                    that.qsxzList = json.d.list;
                        var qb = {
                            yexm: that.jlxqContent.brxm,//如果有影响请还原上面代码，注释本行代码
                            yebh: "000",
                        };
                    that.qsxzList.unshift(qb);
                    that.jlxqContent.yebh = that.qsxzList[0].yebh;

                }
            });
        },
        // 每日小结
        mrxj: function () {
            Vue.set(jlxq.jlContent, 'bqgcjcs', '');
            // 筛选当前日期 24小时内的数据
            console.log(jlxq.jlxx_list)
            console.log(jlxq.jlContent.jlsj)
            var date = new Date(jlxq.jlContent.jlsj);
            var time1 = date.getTime();

            var clList = [];
            var rlList = [];
            for (var j = 0; j < jlxq.jlxx_list.length; j++) {
                var obj = jlxq.jlxx_list[j];
                var sjc = time1 - obj.jlsj;

                if (obj.clMc && obj.clSl && sjc < 24 * 60 * 60 * 1000 && sjc > 0) {
                    var clMcArr = obj.clMc.split(',');
                    var clSlArr = obj.clSl.split(',');
                    for (var i = 0; i < clMcArr.length; i++) {
                        clList.push({
                            clMcItem: clMcArr[i] != 'undefined' ? clMcArr[i] : '',
                            clSlItem: clSlArr[i] != 'undefined' ? clSlArr[i] : '',
                        });
                    }
                }

                if (obj.rlMc && obj.rlSl && sjc < 24 * 60 * 60 * 1000 && sjc > 0) {
                    var rlMcArr = obj.rlMc.split(',');
                    var rlSlArr = obj.rlSl.split(',');
                    for (var i = 0; i < rlMcArr.length; i++) {
                        rlList.push({
                            rlMcItem: rlMcArr[i],
                            rlSlItem: rlSlArr[i]
                        });
                    }
                }
            }
            var msg = '计24小时：';
            var zcl = 0, zrl = 0;
            var clMsg = '', clMsg1 = '';
            var cl = clList.reduce((r, x) => ((r[x.clMcItem] || (r[x.clMcItem] = [])).push(x), r), {});
            var rl = rlList.reduce((r, x) => ((r[x.rlMcItem] || (r[x.rlMcItem] = [])).push(x), r), {});
            for (var arr in cl) {
                var jg = cl[arr].reduce((p, e) => p + (parseInt(e.clSlItem)), 0);
                if (!isNaN(jg)) {
                    clMsg += (arr + jg + 'ml,');
                    zcl += jg;
                }
            }
            for (var arr1 in rl) {
                var jg1 = rl[arr1].reduce((p, e) => p + (parseInt(e.rlSlItem)), 0);
                if (!isNaN(jg1)) {
                    clMsg1 += (arr1 + jg1 + 'ml,');
                    zrl += jg1;
                }
            }
            msg += (';总出量：' + zcl + 'ml,' + clMsg + ';总入量:' + zrl + 'ml,' + clMsg1);


            Vue.set(jlxq.jlContent, 'bqgcjcs', msg);

            malert("计算成功！", 'right', 'success');

        },

        getJyData: function () {
            jyjgPop.jyjgShow = true;
            jyjgPop.$nextTick(function () {
                jyjgPop.getJyjg()
            })
        },
        getTwData: function () {
            jlxq.getTwData()
        },
        Wf_Click: function (index) {
            // if(jlxq.jlxx_list[index].czybm == userId){
            // 	this.index2 = index;
            // 	jlxq.jlContent = jlxq.jlxx_list[index];
            // }else {
            // 	malert("不是本人不允许编辑！", 'top', 'defeadted');
            // }
        },
        hlPrint: function () {
            var val = '';
            if (jlxq.hllx == '1') {
                val = 'fpdy%2Fhsz%2Fhsz_hljl.cpt';
            }
            if (jlxq.hllx == '2') {
                val = 'fpdy%2Fhsz%2Fhsz_ckhljl.cpt';
            }
            if (jlxq.hllx == '3') {
                val = 'fpdy%2Fhsz%2Fhsz_ckfmjl.cpt';
            }
            if (jlxq.hllx == '4') {
                val = 'fpdy%2Fhsz%2Fhsz_wzhljl.cpt';
            }
            if (jlxq.hllx == '5') {
                val = 'fpdy%2Fhsz%2Fhsz_xsrhljl.cpt';
            }
            toolMenu.print(val);
        },
        print: function (val) {
            var sytPrint = '', sytSize = '';
            str = "[{reportlet: '" + val + "',zyh:'" + jlxq.IsZyh + "'}]";
            window.top.J_tabLeft.csqxparm.csbm = "N010024010";
            console.log(window.top.J_tabLeft.csqxparm);
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=sysiniconfig&parm=" +
                JSON.stringify(window.top.J_tabLeft.csqxparm), function (json) {
                if (json.a == 0) {
                    console.log(json.d);
                    if (json.d != null && json.d != undefined && json.d.length > 0) {
                        sytPrint = json.d[0].csz;
                        sytSize = json.d[0].cszmc;
                    }
                    if (!FrPrint(str, sytPrint, sytSize)) {
                        window.print();
                    }
                } else {
                    window.print();
                }
            })
        },
        getData: function () {
            jlxq.getJlData();
        },
        openCpt: function () {
            if (jlxq.hllx == '1') {
                toolMenu.topNewPage('护理记录预览', window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=fpdy%2Fhsz%2Fhsz_hljl.cpt&yljgbm=' + toolMenu.getQueryVariable('yljgbm') + '&czybm=' + toolMenu.getQueryVariable('czybm') + '&ksbm=' + ksbm + '&zyh=' + jlxq.IsZyh)
            }
            if (jlxq.hllx == '2') {
                toolMenu.topNewPage('产科护理记录', window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=fpdy%2Fhsz%2Fhsz_ckhljl.cpt&yljgbm=' + toolMenu.getQueryVariable('yljgbm') + '&czybm=' + toolMenu.getQueryVariable('czybm') + '&ksbm=' + ksbm + '&zyh=' + jlxq.IsZyh)
            }
            if (jlxq.hllx == '3') {
                toolMenu.topNewPage('产科分娩经过记录', window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=fpdy%2Fhsz%2Fhsz_ckfmjl.cpt&yljgbm=' + toolMenu.getQueryVariable('yljgbm') + '&czybm=' + toolMenu.getQueryVariable('czybm') + '&ksbm=' + ksbm + '&zyh=' + jlxq.IsZyh)
            }
            if (jlxq.hllx == '4') {
                toolMenu.topNewPage('危重护理记录预览', window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=fpdy%2Fhsz%2Fhsz_wzhljl.cpt&yljgbm=' + toolMenu.getQueryVariable('yljgbm') + '&czybm=' + toolMenu.getQueryVariable('czybm') + '&ksbm=' + ksbm + '&zyh=' + jlxq.IsZyh)
            }
            if (jlxq.hllx == '5') {
                toolMenu.topNewPage('新生儿护理记录预览', window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=fpdy%2Fhsz%2Fhsz_xsrhljl.cpt&yljgbm=' + toolMenu.getQueryVariable('yljgbm') + '&czybm=' + toolMenu.getQueryVariable('czybm') + '&ksbm=' + ksbm + '&zyh=' + jlxq.IsZyh + '&yebh='+toolMenu.jlxqContent.yebh)
            }
        },
        addData: function () {

            jlxq.clList = [{
                clMcItem: '',
                clSlItem: '',
                clYsxzItem: ''
            }];
            jlxq.rlList = [{
                rlMcItem: '',
                rlSlItem: ''
            }];
            jlxq.jlContent = {
                zdmc: jlxq.jlContent.zdmc,
                rl:this.jlxqContent.rl,
                rybm: userId,
                jlsj: '',
                tw: '',
                bqgcjcs: '',
                clsd: '',
                ys: '',
                hllx: jlxq.hllx,
            };
            jlxq.activeIndex = undefined;
            jlxq.jlContent.jlsj = getTodayDateTime();
            jlxq.jlContent.zrhs = jlxq.hsList[0]['rybm'];
            // 查询护理评估记录
            //jlxq.getHljl();
        },
        editSuccess: function (val) {
            // 保存时将多个入量出量进行组装
            var reg = /,$/gi;
            if (jlxq.clList.length > 0) {
                jlxq.jlContent.clMc = '';
                jlxq.jlContent.clSl = '';
                jlxq.jlContent.clYsxz = '';
                for (var i = 0; i < jlxq.clList.length; i++) {
                    var obj = jlxq.clList[i];
                    jlxq.jlContent.clMc += (obj.clMcItem + ',');
                    jlxq.jlContent.clSl += (obj.clSlItem + ',');
                    jlxq.jlContent.clYsxz += (obj.clYsxzItem + ',');
                }
                jlxq.jlContent.clMc = jlxq.jlContent.clMc.replace(reg, "");
                jlxq.jlContent.clSl = jlxq.jlContent.clSl.replace(reg, "");
                jlxq.jlContent.clYsxz = jlxq.jlContent.clYsxz.replace(reg, "");
            }

            jlxq.jlContent.rlMc = '';
            jlxq.jlContent.rlSl = '';
            if (jlxq.rlList.length > 0) {
                jlxq.jlContent.rlMc = '';
                jlxq.jlContent.rlSl = '';
                for (var i = 0; i < jlxq.rlList.length; i++) {
                    var obj = jlxq.rlList[i];
                    jlxq.jlContent.rlMc += (obj.rlMcItem + ',');
                    jlxq.jlContent.rlSl += (obj.rlSlItem + ',');
                }
                jlxq.jlContent.rlMc = jlxq.jlContent.rlMc.replace(reg, "");
                jlxq.jlContent.rlSl = jlxq.jlContent.rlSl.replace(reg, "");
            }
            //jlxq.jlContent.hllx = '1';
            console.log(jlxq.jlContent);
            jlxq.jlContent.zyh = jlxq.IsZyh;
            jlxq.jlContent.gxbz=val;
            jlxq.jlContent.yebh = toolMenu.jlxqContent.yebh;
            this.save = true;
            $.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=insert&parm=' + encodeURIComponent(JSON.stringify(jlxq.jlContent)), function (json) {
                if (json.a == '0') {
                    toolMenu.save = false;
                    malert("保存成功！", 'right', 'success');
                    //jlxq.jlContent = {};
                    jlxq.changeClsdData();
                    jlxq.getJlData();
                    jlxq.clearData();
                } else {
                    malert("保存失败！", 'right', 'defeadted');
                }
            });
        },
        edit: function (val) {
                        if (this.N03004200254 == 0 || this.N03004200254 == undefined) {
                if (jlxq.activeIndex && jlxq.hlxxArr[jlxq.activeIndex].czybm != userId) {
                    malert('当前记录是' + jlxq.hlxxArr[jlxq.activeIndex].czyxm + "记录，不允许保存其他人记录！", 'right', 'defeadted');
                    return false;
                }
            }
            if (jlxq.jlContent.id=='1') { // 修改
                var msg = '是否批量修改诊断？';
                common.openConfirm(msg, function () {
                    toolMenu.editSuccess(val);
                }, function () {
                    return false;
                })
            } else {
                if (jlxq.hlxxArr.length > 0 && new Date(jlxq.jlContent.jlsj).getTime() <= jlxq.hlxxArr[0].jlsj) {
                    var msg = '当前记录时间小于最新记录时间，是否继续保存？';
                    common.openConfirm(msg, function () {
                        toolMenu.editSuccess(val);
                    }, function () {
                        return false;
                    })
                } else {
                    toolMenu.editSuccess(val);
                }
            }
        },

        remove: function () {
            if (this.N03004200254 == 0 || this.N03004200254 == undefined) {
                if (jlxq.hlxxArr[jlxq.activeIndex].czybm != userId) {
                    malert('当前记录是' + jlxq.hlxxArr[jlxq.activeIndex].czyxm + "记录，不允许删除其他人记录！", 'right', 'defeadted');
                    return
                }
            }
            var msg = '是否删除【' + this.fDate(jlxq.jlContent.jlsj) + '】该记录？';
            common.openConfirm(msg, function () {
                $.getJSON('/actionDispatcher.do?reqUrl=HszHlywWzhljl&types=delete&parm=' + encodeURIComponent(JSON.stringify(jlxq.jlContent)), function (json) {
                    if (json.a == '0') {
                        malert("删除成功！", 'right', 'success');
                        jlxq.jlContent = {};
                        jlxq.getJlData();
                        jlxq.clearData();
                    } else {
                        malert("删除失败！", 'right', 'defeadted');
                    }
                });
            }, function () {

            }, this.defaltObj)
        },
    }
});


var jlxq = new Vue({
    el: '#jlxq',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        jlxx_list: [],
        hsList: [],
        total: 0,
        json: {},
        IsZyh: yzclLeft.HszbrItem.zyh,
        jlContent: {
            rybm: userId,
            jlsj: '',
            tw: '',
            bqgcjcs: '',
            clsd: '',
            ys: '',
            hllx: '1',
        },
        sd_tran: {
            '1': '2',
            '2': '6',
            '3': '10',
            '4': '14',
            '5': '18',
            '6': '22',
        },
        hllx_tran: {
            '1': '护理记录',
            '2': '产科护理记录',
            '3': '产科分娩经过记录',
            '4': '危重护理记录',
            '5': '新生儿护理记录',
        },
        twList: [],
        twqtObj: {},
        rlList: [{}],
        clList: [{}],
        rlxxList: [],
        clxxList: [],

        defaltObj: {
            title: '拒绝申请',
            cs: 'btn-parmary fr-right',
            cm: 'btn-parmary-f2a1 fr-right',
            bm: 'flex-none',
            cb: '取消',
            sb: '拒绝'
        },
        today: '',
        hlxxArr: [], // 页面显示记录列表
        hllx: '1',

        tfw_tran: {
            '枕左前(LOA)': '枕左前(LOA)',
            '枕右前(ROA)': '枕右前(ROA)',
            '枕左后(LOP)': '枕左后(LOP)',
            '枕右后(ROP)': '枕右后(ROP)',
            '枕左横(LOT)': '枕左横(LOT)',
            '枕右横(ROT)': '枕右横(ROT)',
            '颏左前(LMA)': '颏左前(LMA)',
            '颏右前(RMA)': '颏右前(RMA)',
            '颏左后(LMP)': '颏左后(LMP)',
            '颏右后(RMP)': '颏右后(RMP)',
            '颏左横(LMT)': '颏左横(LMT)',
            '颏右横(RMT)': '颏右横(RMT)',
            '骶左前(LSA)': '骶左前(LSA)',
            '骶右前(RSA)': '骶右前(RSA)',
            '骶左后(LSP)': '骶左后(LSP)',
            '骶右后(RSP)': '骶右后(RSP)',
            '骶左横(LST)': '骶左横(LST)',
            '骶右横(RST)': '骶右横(RST)',
            '肩左前(LScA)': '肩左前(LScA)',
            '肩右前(RScA)': '肩右前(RScA)',
            '肩左后(LScP)': '肩左后(LScP)',
            '肩右后(RScP)': '肩右后(RScP)',
            '臀位': '臀位',
            '双胎': '双胎',
            '不详': '不详'
        },
        txqk_tran: {
            '快': '快', '慢': '慢', '节律不齐': '节律不齐', '正常': '正常',
        },
        txwz_tran: {
            '左上': '左上', '左下': '左下', '右上': '右上', '右下': '右下',
            '左上/左下': '左上/左下', '左上/右上': '左上/右上', '左上/右下': '左上/右下',
            '左下/右上': '左下/右上', '左下/右下': '左下/右下', '右上/右下': '右上/右下'
        },
        xlgd_tran: {
            '0': '0',
            '+1': '+1',
            '+2': '+2',
            '+3': '+3',
            '+4': '+4',
            '+5': '+5',
            '+6': '+6',
            '-1': '-1',
            '-2': '-2',
            '-3': '-3',
            '-4': '-4',
            '-5': '-5',
            '-6': '-6'
        },
        gkdx_tran: {
            '未查': '未查',
            '未开': '未开',
            '1': '1',
            '2': '2',
            '3': '3',
            '4': '4',
            '5': '5',
            '6': '6',
            '7': '7',
            '8': '8',
            '9': '9',
            '10': '10'
        },
        gkhb_tran: {
            '厚': '厚', '薄': '薄'
        },
        gksj_tran: {
            '松': '松', '紧': '紧'
        },
        tm_tran: {
            '已破': '已破', '未破': '未破', '人工破膜': '人工破膜', '自动破膜': '自动破膜'
        },
        jcfs_tran: {
            '肛': '肛', '阴': '阴'
        },

        hljb_tran: {
            'I': 'I', 'II': 'II', 'III': 'III', '特': '特'
        },
        cktw_tran: {
            '半卧位': '半卧位',
            '平卧位': '平卧位',
            '仰卧位': '仰卧位',
            '侧卧位': '侧卧位',
            '被动体位': '被动体位',
            '主动体位': '主动体位',
            '自动体位': '自动体位',
            '抬高臀部': '抬高臀部'
        }
    },
    created: function () {
        this.getJlData();
        // this.readyData({"hsbz": "1"}, "rybm", "hsList"); //护士
        var user = JSON.parse(sessionStorage.getItem('yljgOrUser' + userId));
        if (user) {
            this.hsList.push({
                rybm: userId,
                ryxm: user.czyxm
            });
        }
        if (yzclRight.caqxContent.N03004200270 && yzclRight.caqxContent.N03004200270.length > 1) {
            var rlxxArr = yzclRight.caqxContent.N03004200270.split(',');
            if (rlxxArr) {
                for (var i = 0; i < rlxxArr.length; i++) {
                    this.rlxxList.push({rlVal: rlxxArr[i]});
                }
            }
        }
        if (yzclRight.caqxContent.N03004200271 && yzclRight.caqxContent.N03004200271.length > 1) {
            var clxxArr = yzclRight.caqxContent.N03004200271.split(',');
            if (clxxArr) {
                for (var i = 0; i < clxxArr.length; i++) {
                    this.clxxList.push({clVal: clxxArr[i]});
                }
            }
        }
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        Mask.newMask(this.MaskOptions('today'));
        Mask.newMask(this.MaskOptions('jlsj'));
        this.jlContent.jlsj = getTodayDateTime();
        this.today = this.fDate(getTodayDateTime(), 'date');
        this.changeClsdData();
        laydate.render({
            elem: '#jlsj',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                jlxq.jlContent.jlsj = value;
            }
        });
        laydate.render({
            elem: '#today',
            rigger: 'click',
            type: 'date',
            theme: '#1ab395',
            done: function (value, data) { //回调方法
                jlxq.today = value;
                jlxq.getJlData();
            }
        });
    },
    watch: {
        "jlContent.jlsj": function (newVal) {
            this.changeClsdData();
            if (!this.jlContent.id) {
                // this.getTwData();
                //this.getHljl();
            }
        }
    },
    methods: {
        clearData: function () {
            jlxq.clList = [{
                clMcItem: '',
                clSlItem: '',
                clYsxzItem: ''
            }];
            jlxq.rlList = [{
                rlMcItem: '',
                rlSlItem: ''
            }];
            jlxq.jlContent = {
                rybm: userId,
                jlsj: '',
                tw: '',
                bqgcjcs: '',
                //clsd:'',
                ys: '',
                hllx: jlxq.hllx,
            };
            jlxq.jlContent.jlsj = getTodayDateTime();
            jlxq.jlContent.zrhs = jlxq.hsList[0]['rybm'];
        },
        changeHllx: function (val) {
            this.hllx = val[0];
            toolMenu.hllx = val[0];
            // 查询列表
            this.getJlData();
            jlxq.jlContent.hllx = val[0];
            this.$forceUpdate();
            this.clearData();
        },
        delCl: function (index) {
            this.clList.splice(index, 1);
        },
        addCl: function () {
            this.clList.push({
                clMcItem: '',
                clSlItem: '',
                clYsxzItem: ''
            });
        },
        delRl: function (index) {
            this.rlList.splice(index, 1);
        },
        addRl: function () {
            this.rlList.push({
                rlMcItem: '',
                rlSlItem: ''
            });
        },
        resultChangeRlxx: function (index, val) {
            this.rlList[index].rlMcItem = val[4];
        },
        resultChangeClxx: function (index, val) {
            this.clList[index].clMcItem = val[4];
        },
        resultChangeSd: function (val) {
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            // this.getTwData();
        },
        resultChangeHllx: function (val) {
            this.jlContent.hllx = val[0];
        },
        // 根据记录时间默认拉取最近时段的体温信息
        changeClsdData: function () {
            // 获取当前时间
            var time = this.fDate(this.jlContent.jlsj, 'time').split(':')[0];
            console.log(time)
            var clsd = '';
            var type = null;
            for (var key in this.sd_tran) {
                var value = this.sd_tran[key];
                var i = parseInt(time) - parseInt(value);
                if (type == null) {
                    clsd = key;
                    type = i;
                } else {
                    if (i >= 0 && Math.abs(i) < Math.abs(type)) {
                        clsd = key;
                        type = i;
                    }
                }
            }
            this.jlContent.clsd = clsd;
        },
        // 根据时段取对应的体温数据
        getTwObj: function () {
            if (!this.jlContent.id) {
                for (var i = 0; i < jlxq.twList.length; i++) {
                    if (jlxq.twList[i].clsd == jlxq.jlContent.clsd) {
                        jlxq.jlContent.tw = jlxq.twList[i].tw;
                        jlxq.jlContent.mb = jlxq.twList[i].mb;
                        jlxq.jlContent.hx = jlxq.twList[i].fx;
                        jlxq.jlContent.xl = jlxq.twList[i].xt;
                        jlxq.jlContent.xybhd = jlxq.twList[i].ybhd;
                    }
                }
                if (parseInt(this.sd_tran[this.jlContent.clsd]) >= 2 && parseInt(this.sd_tran[this.jlContent.clsd]) <= 10) {// 上午
                    jlxq.jlContent.xyL = jlxq.twqtObj.swSsy;
                    jlxq.jlContent.xyR = jlxq.twqtObj.swSzy;
                } else {
                    jlxq.jlContent.xyL = jlxq.twqtObj.xwSsy;
                    jlxq.jlContent.xyR = jlxq.twqtObj.xwSzy;
                }
                if (jlxq.twqtObj.dbl || jlxq.twqtObj.dbs || jlxq.twqtObj.xbl || jlxq.twqtObj.xbs) {
                    jlxq.clList = [];
                }
                if (jlxq.twqtObj.dbl || jlxq.twqtObj.dbs) { // 大便
                    jlxq.clList.push({
                        clMcItem: '大便',
                        clSlItem: jlxq.twqtObj.dbl,
                        clYsxzItem: jlxq.twqtObj.dbs
                    });
                }

                if (jlxq.twqtObj.xbl || jlxq.twqtObj.xbs) { // 小便
                    jlxq.clList.push({
                        clMcItem: '小便',
                        clSlItem: jlxq.twqtObj.xbl,
                        clYsxzItem: jlxq.twqtObj.xbs
                    });
                }

                for (var i = 0; i < jlxq.jlxx_list.length; i++) {

                    // 当天如果在某次评估中已经录入大便小便出量 则本天不再拉取数据
                    if (this.jlContent.jlsj.substr(0, 10) == this.fDate(jlxq.jlxx_list[i].jlsj, 'date') &&
                        jlxq.jlxx_list[i].clMc != null) {
                        if (jlxq.jlxx_list[i].clMc.indexOf("大便") != -1
                            || jlxq.jlxx_list[i].clMc.indexOf("小便") != -1) {
                            jlxq.clList = [{
                                clMcItem: '',
                                clSlItem: '',
                                clYsxzItem: ''
                            }];
                        }
                    }


                    // 判断护理记录是否重复
                    if (jlxq.jlxx_list[i].bqgcjcs == jlxq.jlContent.bqgcjcs) {
                        jlxq.jlContent.bqgcjcs = '';
                    }

                }
            }
        },
        // 获取体温数据
        getTwData: function () {
            var parm = {
                zyh: toolMenu.jlxqContent.zyh,
                ryksbm: toolMenu.jlxqContent.ryks,
                clrq: this.fDate(this.jlContent.jlsj, 'date'),
                yebh:toolMenu.jlxqContent['yebh']=='000'?undefined:toolMenu.jlxqContent['yebh'],
                //yebh: yebh,
            };
            $.ajaxSettings.async = false;
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=queryByOneBr&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        jlxq.twList = json.d.list;
                    } else {
                        malert(json.c, 'right', 'defeadted');
                    }
                });
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywTwd&types=select&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        //给体温单其他记录对象赋值
                        if (json.d) {
                            jlxq.twqtObj = json.d;
                        } else {
                            jlxq.twqtObj = {};
                        }
                    } else {
                        malert(json.c, 'right', 'defeadted');
                    }
                });
            setTimeout(function () {
                jlxq.getTwObj();
            }, 150)

        },

        //共用下拉框请求后台
        readyData: function (req, types, listName) {
            this.$http.get("/actionDispatcher.do", {
                params: {
                    reqUrl: 'GetDropDown',
                    types: types,
                    json: JSON.stringify(req)
                }
            }).then(function (json, status, xhr) {
                if (json.body.a == 0) {
                    jlxq[listName] = json.body.d.list;
                    jlxq.$forceUpdate()
                } else {
                    malert(types + "查询失败", 'right', 'defeadted');
                }
            }, function (json) {
            })
        },
        yjbz: function () {
            this.jlContent.yjbz = !this.jlContent.yjbz ? 1 : 0;
            this.$forceUpdate()
        },
        Wf_Click: function (index) {
            $('#hllxShow').addClass("disable");
            this.index2 = index;
            this.jlContent = this.hlxxArr[index];
            this.jlContent.jlsj = this.fDate(this.jlContent.jlsj, 'datetime');
            console.log(this.jlContent)

            this.clList = [{
                clMcItem: '',
                clSlItem: '',
                clYsxzItem: ''
            }];
            if (this.jlContent.clMc || this.jlContent.clSl || this.jlContent.clYsxz) {
                this.clList = [];
                var clMcArr = this.jlContent.clMc.split(',');
                var clSlArr = this.jlContent.clSl.split(',');
                var clYsxzArr = this.jlContent.clYsxz.split(',');
                for (var i = 0; i < clMcArr.length; i++) {
                    this.clList.push({
                        clMcItem: clMcArr[i] != 'undefined' ? clMcArr[i] : '',
                        clSlItem: clSlArr[i] != 'undefined' ? clSlArr[i] : '',
                        clYsxzItem: clYsxzArr[i] != 'undefined' ? clYsxzArr[i] : ''
                    });
                }
            }
            this.rlList = [{
                rlMcItem: '',
                rlSlItem: ''
            }];
            if (this.jlContent.rlMc != 'undefined' || this.jlContent.rlSl != 'undefined') {
                this.rlList = [];
                var rlMcArr = this.jlContent.rlMc && this.jlContent.rlMc.split(',');
                var rlSlArr = this.jlContent.rlSl && this.jlContent.rlSl.split(',');
                if (rlMcArr) {
                    for (var i = 0; i < rlMcArr.length; i++) {
                        this.rlList.push({
                            rlMcItem: rlMcArr[i],
                            rlSlItem: rlSlArr[i]
                        });
                    }
                }
            }

        },
        getJlData: function () {
            var parm = {
                // ksrq: this.json.dateBegin,
                // jsrq: this.json.dateEnd,
                zyh: this.IsZyh,
                yebh:toolMenu.jlxqContent['yebh']=='000'?undefined:toolMenu.jlxqContent['yebh'],
                hllx: '1,2,3,4,5'
            };
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'HszHlywWzhljl',
                    types: 'queryAll',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    jlxq.jlxx_list = json.body.d.list;
                    jlxq.hlxxArr = [];
                    for (var i = 0; i < jlxq.jlxx_list.length; i++) {
                        if (jlxq.jlxx_list[i].hllx == jlxq.hllx) {
                            // if (jlxq.fDate(jlxq.today, 'date') == jlxq.fDate(jlxq.jlxx_list[i].jlsj, 'date')) {
                            //     jlxq.hlxxArr.push(jlxq.jlxx_list[i]);
                            // }
                            jlxq.hlxxArr.push(jlxq.jlxx_list[i]);
                        }
                    }
                    jlxq.clearData();
                } else {
                    malert(json.body.c, 'right', 'defeadted');
                }
            });
        },

        getHljl: function () {
            var parm = {
                zyh: this.IsZyh,
                //zyh: '2019005056',
                xhcs: 120
            };
            $.ajaxSettings.async = false;
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'HszHlywWzhljl',
                    types: 'queryHljl',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json) {
                console.log(json.body)
                if (json.body.a == '0' && json.body.d) {
                    // 判断当前护理记录是否在当天已经拉取
                    var str = json.body.d;
                    Vue.set(jlxq.jlContent, 'bqgcjcs', str);
                } else {
                    //malert('未查询到护理评估信息！', 'top', 'defeadted');
                }
            });
        },

        yrjl: function () {
            var parm = {
                zyh: this.IsZyh,
                //zyh: '2019005056',
                xhcs: 120
            };
            $.ajaxSettings.async = false;
            this.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'HszHlywWzhljl',
                    types: 'queryHljl',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json) {
                console.log(json.body)
                if (json.body.a == '0' && json.body.d) {
                    var str = json.body.d;
                    for (var i = 0; i < jlxq.jlxx_list.length; i++) {
                        // 判断护理记录是否重复
                        if (jlxq.jlxx_list[i].bqgcjcs == str) {

                            var msg = '该记录与【' + this.fDate(jlxq.jlxx_list[i].jlsj, 'datetime') +
                                '】录入的数据相同，是否继续引入？';

                            common.openConfirm(msg, function () {
                                Vue.set(jlxq.jlContent, 'bqgcjcs', str);
                            }, function () {

                            }, this.defaltObj)
                            return;
                        }
                    }

                    Vue.set(jlxq.jlContent, 'bqgcjcs', str);
                } else {
                    malert('未查询到护理评估信息！', 'right', 'defeadted');
                }
            });
        },
    }
})
var jyjgPop = new Vue({
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    el: '.jyjgPop',
    data: {
        jyjgShow: false,
        djList: [],
        ifShow: false,
        jybgd: {},
        jybgdmx: [],
        isChecked: [],
        messageStr: '',
        yljgmc: yzclLeft.HszbrItem.yljgmc,
        Brxx_List: yzclLeft.HszbrItem,
    },
    mounted: function () {

    },
    methods: {
        saveData: function () {
            this.messageStr = '';
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    var value = this.jybgdmx[i].sjlx == '1' || this.jybgdmx[i].sjlx == '4' ? this.jybgdmx[i].valueN : this.jybgdmx[i].sjlx == '3' ? this.jybgdmx[i].xzjgmc : this.jybgdmx[i].valueT
                    this.messageStr += this.jybgdmx[i]['zwmc'] + ', ' + value + ' ' + $.trim(this.jybgdmx[i].dw) + ';'
                }
            }
            jlxq.jlContent.bqgcjcs += this.messageStr
        },
        getJyjg: function () {
            //后台查询数据
            this.djList = []
            var parm = {
                bah: this.Brxx_List.zyh,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_bgcx&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        jyjgPop.djList = data.d.list;
                    }
                } else {
                    malert("检验报告单列表查询失败" + data.c, "right", "defeadted");
                }
            });
        },

        getJymx: function (index) {
            common.openloading('.jybgd');
            this.isChecked = [];
            this.jybgd = this.djList[index];
            var parm = {
                jyxh: this.jybgd.jyxh,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_bgmxcx&parm=" + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    common.closeLoading()
                    jyjgPop.jybgdmx = data.d.list;
                } else {
                    common.closeLoading()
                    malert("检验明细查询失败！" + data.c, "right", "defeadted");
                }
            });
        },
    },
})
yzclLeft.$watch('HszbrItem', function (newVal, oldVal) {
    console.log(12)
    jyjgPop.jybgdmx = [];
    jyjgPop.jybgd = {};
    jyjgPop.djList = [];
    jlxq.hlxxArr = [];
    if (newVal.zyh && newVal.zyh != oldVal.zyh && this.index == 5) {
        toolMenu.jlxqContent = newVal;
        jlxq.jlContent = newVal;
        jyjgPop.Brxx_List = newVal;
        jlxq.IsZyh = newVal.zyh;
        jlxq.getJlData();
        jyjgPop.getJyjg();
    }
})
