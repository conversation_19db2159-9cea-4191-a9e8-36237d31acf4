function getData() {
    wrapper.getData()
}
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        popContent: {},
        kfList: [],
        search: '',
        ylbm: 'N050080012005',
        totlePage: 0,
        zhuangtai: {
            '0': '待审核',
            '1': '已审核',
            '2': '未通过',
            '3': '已作废'
        },
        ckfs: {
            '01': '出库',
            '02': '退货',
            '03': '报损',
            '04': '盘亏出库'
        },
        param: {
            page: 1,
            rows: 10,
            parm: '',
            ckfs: '03',
            beginrq: null,
            endrq: null
        },
        jsonList: [],
    },
    mounted: function () {
        this.getKfData();
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate() - 7), 'date') + ' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            eventElem: '.zui-date',
            value: this.param.beginrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.beginrq = value;
                wrapper.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            eventElem: '.zui-date',
            value: this.param.endrq,
            type: 'datetime',
            trigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                wrapper.param.endrq = value;
                wrapper.getData();
            }
        });
    },
    methods: {
        //开单
        topNewPageFun: function () {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.wzkf);
            Vue.set(obj, 'kfList', this.kfList);
            this.Verify(obj);
        },
        //库房查询
        getKfData: function () {
            // 请求库房的api
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=sbkf', function (data) {
                if (data.a == 0) {
                    wrapper.kfList = data.d.list;
                    Vue.set(wrapper.param, 'sbkf', data.d.list[0].sbkfbm);
                    wrapper.getData();
                } else {
                    malert("获取库房列表失败");
                }

            });
        },
        resultRydjChange: function (val) {
            Vue.set(this.param, 'sbkf', val[0]);
            Vue.set(this.param, 'sbkfmc', val[4]);
            this.getData();

        },
        //审核
        sh: function (index) {
            var obj = {};
            Vue.set(obj, 'kfbm', this.param.wzkf);
            Vue.set(obj, 'kfList', this.kfList);
            Vue.set(obj, 'ckd', this.jsonList[index]);
            Vue.set(obj, 'sh', this.jsonList[index].shzfbz == 0);
            Vue.set(obj, 'dy', (this.jsonList[index].shzfbz != 0));
            this.Verify(obj);
        },
        //跳转
        Verify: function (obj) {
            console.log(JSON.stringify(obj))
            sessionStorage.setItem('obj', JSON.stringify(obj));
            this.topNewPage('报损管理', 'page/sbgl/yjkf/kfyw/bsgl/bskd.html');
        },
        //作废2018/07/09二次弹窗作废提示
        Refuse: function (index) {
            var obj = this.jsonList[index];
            if (common.openConfirm("<div>确定作废物资入库单号-" + obj.ckdh + "-入库单吗？<div class=\"flex-container flex-align-c\">\n" +
                "<span class=\"ft-14 whiteSpace padd-r-5\">作废原因</span><textarea rows=\"3\" cols=\"100\"id=\"zfyy\" class=\"padd-t-5 padd-b-5 padd-l-5 padd-r-5 wh100MAx\"></textarea>\n" +
                "</div></div>", function () {
                obj.zfyy = $('#zfyy').val()
                wrapper.$http.post('/actionDispatcher.do?reqUrl=New1SbglKfywBsd&types=invald', JSON.stringify(obj)).then(function (data) {
                    if (data.body.a == "0" || data.a == "0") {
                        malert("作废成功！", 'top', 'success');
                        wrapper.getData();
                    } else {
                        malert("作废失败", 'top', 'defeadted');
                    }
                }, function (error) {
                    console.log(error);
                });
            })) {
                return false;
            }

        },
        getData: function () {
            $.getJSON('/actionDispatcher.do?reqUrl=New1SbglKfywBsd&types=query&parm=' + JSON.stringify(this.param), function (data) {
                if (data.a == "0") {
                    wrapper.jsonList = data.d.list;
                    wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows)
                } else {
                    malert(data.c);
                }
            });
        }
    }
});




