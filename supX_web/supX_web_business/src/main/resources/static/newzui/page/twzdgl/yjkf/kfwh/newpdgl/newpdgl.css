.wh1000{
    width: 100%!important;
  }
  .color-f2a654{
    color:#f2a654;
  }
  .tong-search{
    padding-bottom: 12px;
  }
  .zui-table-view .zui-table-fixed.table-fixed-l{
    left: 10px;
  }
  .background-box{
    height:100%;
  }
  .w30{
      width: 30%;
  }
  .w70{
      width: 70%;
  }
  .wh49{
      width: 49%;
  }
  
  .redClass {
      background-color: red !important;
  }
  
  
  .h1title{
      font-weight: bold;
      font-size:22px;
      color:#3a3a3a;
      text-align:center;
      line-height:19px;
  }
  .pdgl-height-30{
      height:28px;
  }
  .pdgl-height-30:focus{
      color: #1abc9c;
  }
  .iconfont.font-14{
      font-size: 14px;
  }
  .icon-iocn44:before,.icon-iocn51:before,.icon-iocn45:before,.icon-iocn42:before{
      color: #ffffff;
      font-size: 18px;
      vertical-align: sub;
  }
  .icon-iocn56:before,.icon-iocn4:before,.icon-Artboard-1:before{
      color: #1abc9c;
      font-size: 16px;
      vertical-align: text-top;
  }
  .icon-iocn4:before,.icon-iocn56:before{
      font-size: 18px;
  }
  .tong-btn.padd-r-10{
      padding-right: 10px !important;
  }
  .zui-input{
  text-indent: 11px;
  }
  .icon-icon61{
      position: absolute;
      top: 48%;
      cursor: pointer;
      transform: translate(-48%,0);
      left: 20px;
  }
  .icon-icon61:before{
      color: #c5d0de;
  }
  #timeVal{
      text-indent: 20px;
  }