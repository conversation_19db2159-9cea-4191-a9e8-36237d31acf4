
var xselb = new Vue({
    el: '#xselb',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
    data: {
        ztType: '0',
        xseList:[],
        jsvalue: '',
        sort:'yecsrq',
        px_tran:{
        	'rycwbh':'床位号',
        	'yecsrq':'出生日期'
        },
        order:'0',
        
    },
    watch: {
        'ztType': function (val,oldval) {
            if( val != oldval ){
                this.getData();
            }
        }
    },
    updated: function () {
      changeWin();
    },
    mounted: function(){
        //初始化检索日期！为今天0点到今天24点
        var myDate=new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
        this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
        //入院登记查询列表 时间段选择器
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.param.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    xselb.param.beginrq = value;
                } else {
                    xselb.param.beginrq = '';
                }
                //获取一次列表
                xselb.getData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.param.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) {
                if (value != '') {
                    xselb.param.endrq = value;
                } else {
                    xselb.xselb.endrq = '';
                }
                //获取一次列表
                xselb.getData();
            }
        });
    },
    methods:{
    	deleteData:function(val){
    		console.log(val)
    		var msg = '是否删除【'+val.yexm+'】记录？';
    		common.openConfirm(msg, function () {
    			// 删除婴儿信息
        		$.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=deleteYexx&parm="+JSON.stringify(val),function (json) {
        			if(json.a == 0){
                    	if(json.d == 9999){
                    		malert('该记录存在医嘱信息或体温信息，不能进行删除！','top','defeadted');
                    	}else{
                    		malert('删除成功','top','success');
                            xselb.getData();
                    	}
    	            } else {
                        malert(json.c,'top','defeadted');
    	            }
                });            
            }, function () {
            })
    	},
    	resultPxChange:function(val){
    		this.sort = val[0];
    		this.getData();
    	},
    	resultPxfsChange:function(val){
    		this.order = val[0];
    		this.getData();
    	},
    	edit:function(index){
    		bjxq.add=false;
    		$("#yebh").attr("disabled", true);
            $("#zyh").attr("disabled", true);
            bjxq.popContent = JSON.parse(JSON.stringify(xselb.xseList[index]));
            var csrq = bjxq.fDate(bjxq.popContent.csrq, "datetimes");
   			Vue.set(bjxq.popContent, 'csrq', csrq);
   			Vue.set(bjxq.brxxContent, 'text', bjxq.popContent['zyh']);
   			bjxq.open();
    	},
        addData: function () {
        	bjxq.add=true;
        	bjxq.brxxContent={};
        	bjxq.popContent={};
        	bjxq.popContent.yenldw = '4';
        	$("#yebh").attr("disabled", false);
            $("#zyh").attr("disabled", false);
            bjxq.open();
        },
        getData: function () {
            this.param.parm=this.jsvalue;
            if(this.param.parm!=null&&this.param.parm!=''&&this.param.parm!=undefined){
            	this.param.page=1;
            }
            if(this.ztType=='0'){//在院
                this.param.zyzt='0';
                this.param.cxrq='ryrq';
            }else if(this.ztType=='1'){//出院
                this.param.zyzt='1';
                this.param.cxrq='cyrq';
            }
            
            this.param.sort = this.sort;
            if(this.order == '0'){
            	this.param.order = 'desc';
            }else{
            	this.param.order = 'asc';
            }
            
            
            $.getJSON("/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm="+JSON.stringify(this.param),function (json) {
               //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if(json.a == 0 ){
                        xselb.totlePage = Math.ceil(json.d.total / xselb.param.rows);
                        xselb.xseList=json.d.list;
                }else{
                    malert(json.c,'top','defeadted')
                }
           });
        },
    }
});

var bjxq = new Vue({
    el: '#bjxq',
    mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
    components: {
        'search-table': searchTable
    },
    data: {
    	add:true,
        isShow: false,
        brxb: '',
        brxxContent:{},
        popContent: {}, //新增新生儿信息
        searchCon: [],
        selSearch: -1,
        dg: {page:1,rows:5,sort:"",order:"asc",parm:""},
        them_tran: {
        },
        yenldw_tran:{
            "3": "天",
            "4": "小时",
            '5': '分钟'
        },
        them: {
        	'住院号' : 'zyh',
        	'病人姓名':'brxm',
        	'病人性别':'brxbmc',
        	'年龄':'nl',
        	'入院日期':'ryrq',
        	'入院科室':'ryksmc',
        	'住院医生':'zyysxm',
        	'床位编号':'rycwbh'
        }
    },
    mounted: function () {
        laydate.render({
            elem: '#csrq',
            theme: '#1ab394',
            type: 'datetime',
            done: function (value, data) {
                if((new Date(value).getTime()) > new Date().getTime()){
                    malert("出生日期不能再当前时间之后！","top","defeadted");
                    bjxq.popContent.csrq = "";
                    bjxq.$forceUpdate();
                    return;
                }
                bjxq.popContent.csrq = value;
                var nlContent = bjxq.getBabyAge(new Date(value));
                bjxq.popContent.yenl = nlContent.nl;
                bjxq.popContent.yenldw = nlContent.nldw;
                bjxq.$forceUpdate();
            }
        });
    },
    methods: {
           //记账项目检索
       	changeDown: function (event,type) {
       		if (this['searchCon'][this.selSearch] == undefined) return;
              this.keyCodeFunction(event,'brxxContent','searchCon');
               //选中之后的回调操作
              if(event.code == 'Enter' || event.code == 13){
              		if(type=='text'){
	                   Vue.set(bjxq.brxxContent, 'text', this.brxxContent['zyh']);
	                   this.popContent['zyh']=this.brxxContent['zyh'];
	                   Vue.set(bjxq.popContent, 'cfxm', this.brxxContent['brxm']);
	                   this.selSearch = -1;
	                   this.nextFocus(event,1)
              		}

               }
           },

            //当输入值后才触发
           change:function(add,type,val){
        	this.brxxContent['text'] = val;
        	if (!add) this.page.page = 1;
           	var _searchEvent = $(event.target.nextElementSibling).eq(0);
               	this.dg.parm = val;
               // var json={//当婴儿从其他医院接过来时,没有产妇信息
           	//         brxb: '2'
               // };
               $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=rydj'
                       +'&dg='+JSON.stringify(this.dg),//+'&json='+JSON.stringify(json)
                       function (data) {
                       	if(data.d.list.length>0){
                       		for(var i=0;i<data.d.list.length;i++){
                       			var ryrq = bjxq.fDate(data.d.list[i].ryrq, "date");
   				   				Vue.set(data.d.list[i], 'ryrq', ryrq);
                       		}
                       	}
                           bjxq.searchCon = data.d.list;
                           bjxq.dg.total = data.d.total;
                           bjxq.selSearch = 0;
                           if (data.d.list.length > 0 && !add) {
                               $(".selectGroup").hide();
                               _searchEvent.show();
                           }
                       });
           },
	        //鼠标双击（记账项目）
	        selectOne: function (item) {
   		     if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                 this.page.page++;               // 设置当前页号
                 this.change(true, 'text', this.brxxContent['text']);           // 传参表示请求下一页,不传就表示请求第一页
             } else {   // 否则就是选中事件,为json赋值
            	this.brxxContent = item;
            	Vue.set(bjxq.brxxContent, 'text', this.brxxContent['zyh']);
  	            this.popContent['zyh']=this.brxxContent['zyh'];
  	            Vue.set(bjxq.popContent, 'cfxm', this.brxxContent['brxm']);
  	            $("#yebh").focus();
     		    $(".selectGroup").hide();
             }
	        },
        //关闭
        closes: function () {
            this.isShow = false;
        },
        open: function () {
            this.isShow = true;
        },
        submit: function () {
            // 确定按钮事件
        },
        saveYe:function(){
        	if(bjxq.popContent['zyh']==undefined||bjxq.popContent['zyh']==null||bjxq.popContent['zyh']==""){
                malert('请先选择产妇','top','defeadted');
            	return;
    		}
    		if(bjxq.popContent['yebh']==undefined||bjxq.popContent['yebh']==null||bjxq.popContent['yebh']==""){
                malert('婴儿编号不能为空','top','defeadted');
            	return;
    		}
    		if(bjxq.popContent['yexm']==undefined||bjxq.popContent['yexm']==null||bjxq.popContent['yexm']==""){
                malert('婴儿姓名不能为空','top','defeadted');
            	return;
    		}
    		if(bjxq.popContent['xb']==undefined||bjxq.popContent['xb']==null||bjxq.popContent['xb']==""){
                malert('婴儿性别不能为空','top','defeadted');
            	return;
    		}
    		if(bjxq.popContent['csrq']==undefined||bjxq.popContent['csrq']==null||bjxq.popContent['csrq']==""){
                malert('出生日期不能为空','top','defeadted');
            	return;
    		}
    		if(bjxq.popContent['scfs']==undefined||bjxq.popContent['scfs']==null||bjxq.popContent['scfs']==""){
                malert('生产方式不能为空','top','defeadted');
            	return;
    		}
    		var json = JSON.stringify(bjxq.popContent);
            this.$http.post('/actionDispatcher.do?reqUrl=HszHlywYexx&types=save',json).then(function (data) {
                if(data.body.a == 0){
                    malert('保存成功','top','success');
                    bjxq.popContent={};
                    bjxq.brxxContent={};
                    xselb.getData();
                    bjxq.isShow = false;
	            } else {
                    malert(data.body.c,'top','defeadted')
	            }
            }, function (error) {
                console.log(error);
            });

        },
        setCsrq: function (event) {
            if (this.popContent.yenldw && this.popContent.yenl > 0) {
                this.popContent.csrq = this.getBabyBirthday(this.popContent.yenl, this.popContent.yenldw);
            }
            this.nextFocus(event,1)
        },
        resultNldwChange:function (val) {
            Vue.set(this.popContent, 'yenldw', val[0]);
            this.setCsrq(val[1]);
            this.$forceUpdate()
        }
    }
});
xselb.getData();//初始化查询出院的婴儿信息
$(document).mouseup(function(e){
    var bol = $(e.target).parents().is(".selectGroup");
    if(!bol){
	    $(".selectGroup").hide();
    }

});
