var tableInfo = new Vue({
	el: '#crcx',
	//混合js字典庫
	mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
	data: {
		searchWord: null,
		queryType: false, //是否按药品汇总查询
		jsonList: [],
		yfkfList: [],
		yfkf: 0, //药房库房信息
		param: {
			'page': 1,
			'rows': 100,
			'sort': '',
			'order': 'desc',
			'shzfbz': 1,
			'kfbm': '',
			'beginrq': null,
			'endrq': null,
			'parm': ''
		}
	},
	mounted: function() {
		//初始化页面记载库房
		$.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm',
			function(data) {
				if(data.a == 0 && data.d.list.length > 0) {
						tableInfo.yfkfList = data.d.list;
				} else {
					malert("科室获取失败");
				}
			});

		//获取单据列表
		//		this.getData();
	},
	watch: {
		searchWord: function(newVal){
			//设置参数
			this.param.parm = newVal;
			//获取列表
			this.getData();
		}
	},
	methods: {
		//科室库存报表导出
		exportKsKc: function() {
			if(this.yfkf == 0) {
				malert('请选择库房');
				return;
			}
			//数据查询参数
			var param = {
				'page': 1,
				'rows': 20000,
				'yljgbm': jgbm,
				'ksbm': this.yfkf,
				'wzkfmc': '物资'
			};
			//准备地址
			var url = "/actionDispatcher.do?reqUrl=WzkfKfywKccx&types=exportKsKc&parm=" +
				JSON.stringify(param);
			//组合地址
			this.url = (window.location.protocol + '//' + window.location.host) + url;
			//打开下载页
			window.location = this.url;
		},
		//双击修改有效期和批次停用
		edit: function() {
			mxtzPop.isShow = true;
			//			mxtzPop.popContent = this.ypSelected;
			Vue.set(mxtzPop.popContent, 'xxq', this.fDate(this.ypSelected.yxqz, 'date'));
			Vue.set(mxtzPop.popContent, 'xpcty', '0');
			Vue.set(mxtzPop.popContent, 'ksbm', this.ypSelected.ksbm);
			Vue.set(mxtzPop.popContent, 'xtph', this.ypSelected.xtph);
			Vue.set(mxtzPop.popContent, 'wzbm', this.ypSelected.wzbm);
		},
		//单击选中
		checkOne: function(index, item) {
			this.ypSelected = item;
			this.isCheckAll = false;
			this.isChecked = [];
			this.isChecked[index] = true;
		},
		//药房库房改变
		yfkfChange: function() {
			if(this.yfkf == 0) return;
			//			//重新获取列表
			this.getData();
		},
		//获取数据
		getData: function() {
			//是否按药品汇总
			this.param.sort = this.queryType ? 'sum' : null
			//设置库房
			this.param.ksbm = this.yfkf == 0 ? null : this.yfkf
			$.getJSON("/actionDispatcher.do?reqUrl=WzkfKfywKccx&types=kskc&parm=" +
				JSON.stringify(this.param),
				function(json) {
					if(json.a == "0") {
						tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
						tableInfo.jsonList = json.d.list;
					} else {
						malert(json.c);
						tableInfo.jsonList = [];
					}
				});
		}
	}
});

//弹出层
var mxtzPop = new Vue({
	el: '#mxtzPop',
	mixins: [dic_transform, baseFunc],
	data: {
		isShow: false,
		popContent: {},
		isKeyDown: null,
		title: '有效期、批次停用修改'
	},
	methods: {
		saveData: function() {
			if(this.popContent.xxq == null) {
				alert("请输入有效期")
				return;
			}
			if(this.popContent.xpcty == null) {
				alert("请输入停用标志")
				return;
			}

			$.getJSON("/actionDispatcher.do?reqUrl=WzkfKfywKccx&types=updateKs&parm=" +
				JSON.stringify(this.popContent),
				function(data) {
					if(data.a == 0) {
						mxtzPop.isShow = false;
						mxtzPop.isAdd = false;
						malert("数据保存成功");
						//刷新页面
						tableInfo.getData();
					} else {
						malert("上传数据失败");
					}
				})
		}
	}
});
window.getTime = function(event, type) {
	if(type == 'star') {
		tableInfo.param.beginrq = $(event).val();
	} else if(type == 'end') {
		tableInfo.param.endrq = $(event).val();
	}
};
