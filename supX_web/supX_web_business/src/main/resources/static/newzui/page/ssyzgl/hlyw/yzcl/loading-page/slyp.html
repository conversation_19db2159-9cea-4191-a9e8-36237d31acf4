<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <title>住院管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="../../../../../css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../../../../../css/icon.css">
    <link rel="stylesheet" href="slyp.css">
</head>
<body class="skin-default">
<div  id="loadingPage" v-cloak="" style="height: 100%;">
    <div class="wrapper no-scrollbar">
        <div v-for="(brListItem, brListIndex) in ypslInfoList" class="bryz-list" :class="{'margin-b-10': ypslInfoList.length > 1 }">
            <header class="userNameBg">
                <div class="flex">
                    <div class="userNameImg">
                        <span v-show="brListItem.nljd==1">
                                <img src="/newzui/pub/image/maleBaby.png">
                            </span>
                            <span v-show="brListItem.nljd==2">
                                <img src="/newzui/pub/image/femalebaby.png">
                            </span>
                            <span v-show="brListItem.nljd==3">
                                <img src="/newzui/pub/image/Group <EMAIL>">
                            </span>
                            <span v-show="brListItem.nljd==4">
                                <img src="/newzui/pub/image/Group <EMAIL>">
                            </span>
                            <span v-show="brListItem.nljd==5">
                                <img src="/newzui/pub/image/juvenile.png">
                            </span>
                            <span v-show="brListItem.nljd==6">
                                <img src="/newzui/pub/image/maid.png">
                            </span>
                            <span v-show="brListItem.nljd==7">
                                <img src="/newzui/pub/image/youth.png">
                            </span>
                            <span v-show="brListItem.nljd==8">
                                <img src="/newzui/pub/image/woman.png">
                            </span>
                            <span v-show="brListItem.nljd==9">
                                <img src="/newzui/pub/image/grandpa.png">
                            </span>
                            <span v-show="brListItem.nljd==10">
                                <img src="/newzui/pub/image/grandma.png">
                            </span>
                            <span v-show="brListItem.nljd==11">
                                <img src="/newzui/pub/image/<EMAIL>">
                            </span>
                    </div>
                    <div class="text-color">
                       <p class="userHeader">
                            <span class="userName" v-text="brListItem.brxm"></span>
                            <span class="sex text" v-text="brxb_tran[brListItem.brxb]"></span>
                            <span class="nl text" v-text="brListItem.nl"></span>
                        </p>
                        <div class="userCwh">
                            <span class="cwh text" v-text="'床位号：'+ brListItem.rycwbh +'号'"></span>
                            <span class="zyh text" v-text="'住院号：'+ brListItem.brzyh"></span>
                            <span class="bq text" v-text="'科室：'+ brListItem.ryksmc"></span>
                            <span class="ys text" v-text="'医师：'+ brListItem.zyysxm"></span>
                            <span class="brzt text" v-text="'病人状态：'+zyzt_tran[brListItem.zyzt]"></span>
                            <span class="bz text" v-text="'病种：'+ brListItem.ryzdmc"></span>
                        </div>
                        <!--<div class="userCwh">-->
                            <!--<span class="fyhj text" v-text="'费用合计：'+ brListItem.fyhj"></span>-->
                            <!--<span class="yjhj text" v-text="'预交合计：'+ brListItem.yjhj"></span>-->
                            <!--<span class="zyts text" v-text="'住院天数：'+ brListItem.zyts"></span>-->
                            <!--<span class="phone text" v-text="'联系电话：'+ brListItem.lxdh"></span>-->
                            <!--<span class="sfz text"  v-text="'身份证：'+ brListItem.sfzh"></span>-->
                        <!--</div>-->
                        <!--<div class="userFooter">-->
                            <!--<span class="hl text" v-text="'护理等级：'"></span>-->
                            <!--<span class="wz text">危重：危重</span>-->
                        <!--</div>-->
                        <div>
                            <p class="heaf text">更多详细信息>></p>
                        </div>
                    </div>
                </div>
            </header>
            <!--
            <div class="tong-search">
                <div class="zui-form">
                    <div class="zui-inline padding-left40">
                        <label class="zui-form-label">类型</label>
                        <div class="zui-input-inline wh150">
                            <select-input @change-data="resultChange"
                                          :data-notEmpty="true"
                                          :child="lxList"
                                          :index="lx"
                                          :val="lx"
                                          :name="'lx'"
                                          :search="true"></select-input>
                        </div>
                    </div>
                </div>
            </div>
             -->
            <div class="zui-table-view">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">
                                   <input class="green" type="checkbox" v-model="brListItem.isCheckAll">
                                    <label @click="reCheckBoxSl(brListIndex)" ></label>
                                </div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>序号</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>类型</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xxl text-left"><span>医嘱内容</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>用量</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>单位</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s"><span>频次</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l"><span>用药方法</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l"><span>医生签名</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-l"><span>护士签名</span></div>
                            </th>
                             <th>
                                <div class="zui-table-cell cell-l"><span>执行到</span></div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-xxl text-left"><span>说明</span></div>
                            </th>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" :data-no-change="ypslInfoList.length>1" @scroll="scrollTable($event)">
                    <table class="zui-table table-width50">
                        <tbody>
                        <tr v-for="(yzListItem,$index) in brListItem.yzxx"
                            :tabindex="$index"
                            @click="reCheckBoxSl(brListIndex,$index)"
                            :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index===hoverIndex&&brListIndex===hoverBrListIndex}]"
                            @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                            @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')"
                            class="tableTr2" ref="list">
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <input class="green" type="checkbox" v-model="yzListItem.isChecked">
                                    <label @click="checkSelectSl(brListIndex,$index)" ></label>
                                </div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'shortY')"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xxl text-left title" v-text="yzListItem.ryypmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="yzListItem.sl"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l" v-text="yzListItem.ysxm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l" v-text="yzListItem.zxhsxm"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.zxEnd,'datetime')"></div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-xxl text-left title" v-text="yzListItem.yssm"></div>
                            </td>
                            <td class="cell-m">
                                <div class="zui-table-cell cell-m">
                                    <span class="flex-center padd-t-5">
                                        <em class="width30">
                                            <i data-title="领药" class="icon-width butt-hover icon-lingyao"></i>
                                        </em>
                                    </span>
                                    <!--<span data-title="领药" class="icon-width butt-hover icon-lingyao"></span>-->
                                    <!--
                                    <span data-title="取消执行" class="icon-width icon-zhixing"></span>
                                    -->
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <!--左侧固定-->
                <div class="zui-table-fixed table-fixed-l">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                      <input class="green" type="checkbox" v-model="brListItem.isCheckAll">
                                       <label @click.stop="reCheckBoxSl(brListIndex)" ></label>
                                    </div>
                                </th>
                                <th class="cell-m">
                                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                                </th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)" :data-no-change="ypslInfoList.length>1">
                        <table class="zui-table">
                            <tbody>
                            <tr v-for="(yzListItem,$index) in brListItem.yzxx" :tabindex="$index"
                                @click="reCheckBoxSl(brListIndex,$index)"
                                :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index===hoverIndex&&brListIndex===hoverBrListIndex}]"
                                @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')"
                                class="tableTr2">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                        <input class="green" type="checkbox" v-model="yzListItem.isChecked">
                                        <label @click.stop="checkSelectSl(brListIndex,$index)" ></label>
                                    </div>
                                </td>
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m" v-text="$index+1"><!--序号--></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!--右侧固定-->
                <div class="zui-table-fixed table-fixed-r">
                    <div class="zui-table-header">
                        <table class="zui-table">
                            <thead>
                            <tr class="cell-m">
                                <th><div class="zui-table-cell cell-m"><span>操作</span></div></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body" @scroll="scrollTableFixed($event)" :data-no-change="ypslInfoList.length>1">
                        <table class="zui-table">
                            <tbody>
                            <tr  v-for="(yzListItem,$index) in brListItem.yzxx" :tabindex="$index"
                                 @click="reCheckBoxSl(brListIndex,$index)"
                                 :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index===hoverIndex&&brListIndex===hoverBrListIndex}]"
                                 @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                 @mouseleave="hoverMouse(),switchIndex('hoverBrListIndex')"
                                 class="tableTr2">
                                <td class="cell-m">
                                    <div class="zui-table-cell cell-m">
                                        <!--<span data-title="领药" class="icon-width butt-hover icon-lingyao"></span>-->
                                        <span class="flex-center padd-t-5">
                                        <em class="width30">
                                            <i data-title="领药" class="icon-width butt-hover icon-lingyao"></i>
                                        </em>
                                        </span>
                                        <!--
                                        <span data-title="取消执行" class="icon-width icon-zhixing"></span>
                                        -->
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="ksys-btn action-bar fixed">
        <span v-if="ypslInfoList.length>1" style="flex: 1;">
            <input class="green" type="checkbox" v-model="isOver">
            <label @click.stop="isOverClick(!isOver)">全选</label>
        </span>
        <button v-waves class="zui-btn btn-primary xmzb-db qxsh" @click="closePage" style="background: #d9dddc;color: #8e9694;">关闭</button>
        <button v-waves v-show="brList.length = 1" class="zui-btn btn-primary xmzb-db qxsh" @click="qxzx()">取消执行</button>
        <button v-waves class="zui-btn btn-primary xmzb-db zx" @click="ypsl()">申领</button>
        <button v-waves class="zui-btn btn-primary xmzb-db dy" @click="">打印</button>
    </div>
</div>
<div class="pop openConfirm printHide" id="tspop" ref="tspop" style="display: none" v-cloak="">
    <div class="pophide printHide show"></div>
    <div class="zui-form confirm-height podrag show openConfirm pop-548">
        <div class="confirm-title">申领药品</div>
        <div class="confirm-content">
            <div class="confirm-mad confirm-height">
                <div class="zui-form">
                    <div class="zui-inline padding-left60">
                        <label class="zui-form-label" style="line-height: 24px;">申领时间</label>
                        <div class="zui-input-inline">
                            <i class="icon-position icon-rl"></i>
                            <input class="zui-input todate text-indent20" placeholder="请选择时间" id="timeVal"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="confirm-row ">
                <button v-waves class="confirm-btn confirm-primary-b cancel" @click="closes">取消</button>
                <button v-waves class="confirm-btn confirm-primary  submit" @click="oksl()">确定申领</button>
            </div>
        </div>
    </div>
</div>

<script src="slyp.js"></script>
</body>
</html>
