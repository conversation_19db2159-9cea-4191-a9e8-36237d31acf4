<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="pdwc">
    <div class="toolMenu toolMenu_4" style="display: block;">
        <button @click="shPdb"><span class="fa fa-check-square-o"></span>审核</button>
        <span style="margin-left: 10px">凭证号</span>
        <select v-model="pzNum">
            <option value="0">-请选择-</option>
            <option v-for="item in pzhList" :value="item">{{item.pdpzh}}</option>
        </select>
        <span style="margin-left: 10px">物资检索</span>
        <input type="text" v-model="ypjs">
    </div>

    <div class="enter_tem1 enter_pdbmx">
        <div class="enter_tem1_title">盘点表明细</div>

        <div class="table_tem2" style="height: calc(100% - -4px)">
            <table>
                <tr>
                    <th>物资编码</th>
                    <th>物资名称</th>
                    <th>规格</th>
                    <th>物资批号</th>
                    <th>库存数量</th>
                    <th>实存数量</th>
                    <th>单位</th>
                    <th>生产批号</th>
                    <th>有效期至</th>
                    <th>库房单位</th>
                    <th>分装比例</th>
                    <th>库位</th>
                    <th>产地</th>
                    <th>供货单位</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <td v-text="item.wzbm"></td>
                    <td v-text="item.wzmc"></td>
                    <td v-text="item.wzgg"></td>
                    <td v-text="item.xtph"></td>
                    <td v-text="item.kcsl"></td>
                    <td v-text="item.scsl"></td>
                    <td v-text="item.yfdw"></td>
                    <td v-text="item.scph"></td>
                    <td v-text="item.yxqz"></td>
                    <td v-text="item.kfdw"></td>
                    <td v-text="item.fzbl"></td>
                    <td v-text=""></td>
                    <td v-text="item.cdbm"></td>
                    <td v-text="item.ghdw"></td>
                </tr>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript" src="pdwc.js"></script>