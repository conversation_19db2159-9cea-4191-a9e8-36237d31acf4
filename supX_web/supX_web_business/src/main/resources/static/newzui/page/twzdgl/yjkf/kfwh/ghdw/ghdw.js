    var wrapper=new Vue({
        el:'#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc],
        data:{
            jsonList: [],
            isShowpopL:false,
            isTabelShow:false,
            isShow:false,
            keyWord:'',
            popContent:{},
            title:'',
            totle:'',
            num:0,
            param: {
                'sort': 'dwbm',
                page: '',
                parm: '',
                rows: 100,
                total: '',
                lx:'3'
            },
        },
        mounted:function(){
            this.getData();
        },
        methods:{
            //新增
            AddMdel:function () {
                wap.title='新增供货单位';
                wap.open();
                wap.popContent={};
            },
            //进入页面加载列表信息
            getData: function () {
                common.openloading('.zui-table-view')
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json="+JSON.stringify(this.param),function (json) {
                    wrapper.totlePage = Math.ceil(json.d.total/wrapper.param.rows);
                    wrapper.jsonList = json.d.list;
                });
                common.closeLoading()
            },
            //编辑修改根据num判断
            edit: function(num) {
                wap.title='编辑供货单位'
                wap.open();
                wap.popContent = JSON.parse(JSON.stringify(this.jsonList[num]));

            },
            //2018/07/04添加二次弹窗删除提示
            remove: function(num) {
                var dwbmList = [];
                if(num == -1){
                    for(var i = 0; i < this.isChecked.length; i++) {
                        if(this.isChecked[i] == true) {
                            var dwbm ={};
                            dwbm.dwbm = this.jsonList[i].dwbm;
                            dwbmList.push(dwbm);
                        }
                    }
                }else{
                    //单个删除
                    var obj = {
                        dwbm: this.jsonList[num].dwbm
                    }
                    dwbmList.push(obj);
                }

                if(dwbmList.length == 0) {
                    malert("请选中您要删除的数据",'top','defeadted');
                    return false;
                }
                if (common.openConfirm("确认删除该条信息吗？",function () {
                    var json = '{"json":' + JSON.stringify(dwbmList) + '}';
                    wrapper.$http.post('/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=delete&',
                        json).then(function (data) {
                        if(data.body.a == 0) {
                            malert("删除成功",'top','success')
                            this.getData();
                        } else {
                            malert("删除失败",'top','defeadted')
                        }
                    }, function(error) {
                        console.log(error);
                    });
                })) {
                    return false;
                }

            }
        },
    });


    var wap=new Vue({
        el:'#brzcList',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc,mformat],
        data:{
            isShow: false,
            popContent: {},
            isKeyDown: null,
            title: '',
            searchCon: [],
            dg: {page:1,rows:20,sort:"dwbm",order:"asc",parm:""},
            selSearch: -1
        },
        methods:{
            //关闭
            closes: function () {
                $(".side-form").removeClass('side-form-bg')
                $(".side-form").addClass('ng-hide');

            },
            open: function () {
                $(".side-form-bg").addClass('side-form-bg')
                $(".side-form").removeClass('ng-hide');
            },
            showTime:function(el,code){
                laydate.render({
                    elem: '#'+el,
                    type: 'date',
                    show: true ,//直接显示
                    rigger: 'click',
                    theme: '#1ab394',
                    done: function (value, data) { //回调方法
                        wap.popContent[code] = value;
                    }
                });
            },
            //确定
            confirms:function () {
                    if(!this.popContent.tybz){
                        Vue.set(wap.popContent,'tybz','0');
                    }
                    //耗材
                    this.popContent.lx = "3";
                    if(this.popContent.lx == null) {
                        malert("请输入单位类型",'top','defeadted')
                        return;
                    }
                    if(this.popContent.dwmc == null) {
                        malert("请输入单位名称",'top','defeadted')
                        return;
                    }
                    // 卫生材料供货商

                    this.$http.post('/actionDispatcher.do?reqUrl=YkglKfwhGhdw&types=save', JSON.stringify(this.popContent))
                        .then(function (data) {
                            if(data.body.a == 0){
                                wrapper.getData();
                                wap.closes();
                                malert("数据更新成功",'top','success');
                            } else {
                                malert("数据失败",'top','defeadted');
                            }
                        }, function (error) {
                            console.log(error);
                        });;
            },
            changeDown: function(index, type) {
                var _input = $(".popInfo input");
                var _ul = $(".popInfo ul");
                if(window.event.keyCode == 13) {
                    if(this.selSearch != -1) {
                        this.popContent[type] = this.searchCon[this.selSearch][type];
                        this.selSearch = -1
                    } else {
                        _input.eq(index + 1).focus();
                    }
                    _ul.hide();
                } else if(window.event.keyCode == 40) {
                    if((this.searchCon.length - 1) == this.selSearch) {
                        this.selSearch = 0;
                    } else {
                        this.selSearch++;
                    }
                } else if(window.event.keyCode == 38) {
                    if(this.selSearch == 0) {
                        this.selSearch = this.searchCon.length - 1;
                    } else {
                        this.selSearch--;
                    }
                } else {
                    if(this.popContent[type] != null && this.popContent[type] != "") {
                        this.dg.parm = this.popContent[type];
                        $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=cdbm' +
                            '&dg=' + JSON.stringify(wap.dg),
                            function(data) {
                                wap.searchCon = data.d.list;
                                wap.selSearch = -1;
                                if(data.d.list.length != 0) {
                                    var el = _input.eq(index);
                                    _ul.hide();
                                    el.next().show();
                                } else {
                                    _ul.hide();
                                }
                            });
                    }
                }
            },
        }
    });
