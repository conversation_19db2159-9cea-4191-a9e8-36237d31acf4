<html>

	<head>
		<title>物资库存月报表</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<script src="/pub/top.js"></script>
		<link href="kcybb.css" rel="stylesheet" type="text/css" />
	</head>

	<body>

		<div id="crcx">
			<div class="toolMenu" style="display: block;">
				<input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd 00:00:00' })" onchange="getTime(this, 'star')" />至
				<input onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd 23:59:59' })" onchange="getTime(this, 'end')" />
				<select class="YFSelect" v-model="yfkf" @change="yfkfChange">
					<option :value="0">-全部库房-</option>
					<option v-for="item in yfkfList" :value="item.kfbm" v-text="item.kfmc"></option>
				</select>
				<button @click="getData"><span class="fa fa-refresh"></span>查询</button>
				<button @click=""><span class="fa fa-check-square-o"></span>报表</button>
			</div>
			<div class="tableDiv">
				<table class="patientTable" cellspacing="0" cellpadding="0">
					<thead style="position: absolute;">
						<tr>
							<th class="tableNo"></th>
							<th><input type="checkbox" v-model="isCheckAll" @click="checkAll"></th>
							<th>种类编码</th>
							<th>种类名称</th>
							<!--<th>种类数量</th>-->
							<th>期初金额</th>
							<th style="background-color: #94AAB6;">开单入库</th>
							<th style="background-color: #94AAB6;">退库入库</th>
							<th style="background-color: #94AAB6;">盘点入库</th>
							<th style="background-color: #9EB6CE;">领用出库</th>
							<th style="background-color: #9EB6CE;">退货出库</th>
							<th style="background-color: #9EB6CE;">报损出库</th>
							<th style="background-color: #9EB6CE;">盘点出库</th>
							<th>损溢金额</th>
							<th>结余金额</th>
							<!--<th>分装比例</th>-->
						</tr>
					</thead>
					<tr>
						<th v-for="item in 14"></th>
					</tr>
					<tr v-for="(item, $index) in jsonList" @click="checkOne($index)" :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]" @dblclick="edit($index)">
						<th class="tableNo" v-text="$index+1"></th>
						<th><input type="checkbox" name="checkNo" v-model="isChecked[$index]" @click.stop="checkSome($index)" />
						</th>
						<td v-text="item.zlbm"></td>
						<td v-text="item.zlmc"></td>
						<!--<td v-text="item.kcsl"></td>-->
						<td v-text="item.qcje"></td>
						<td style="background-color: #94AAB6;" v-text="item.kdrk"></td>
						<td style="background-color: #94AAB6;" v-text="item.tkrk"></td>
						<td style="background-color: #94AAB6;" v-text="item.pdrk"></td>
						<td style="background-color: #9EB6CE;" v-text="item.lyck"></td>
						<td style="background-color: #9EB6CE;" v-text="item.thck"></td>
						<td style="background-color: #9EB6CE;" v-text="item.bsck"></td>
						<td style="background-color: #9EB6CE;" v-text="item.pdck"></td>
						<td v-text="item.syje"></td>
						<td v-text="item.jyje"></td>
						<!--<td v-text="item.fzbl"></td>-->
					</tr>
				</table>
			</div>

			<div class="pageDiv">
				<div class="page">
					<div @click="goPage(page, 'prev')" class="fa fa-angle-left num"></div>
					<div class="num" v-for="(item, $index) in totlePage" v-text="item" :class="{currentPage: param.page == $index + 1}" @click="goPage($index + 1)"></div>
					<div @click="goPage(page, 'next')" class="fa fa-angle-right num next"></div>
					<div>
						第<input type="number" v-model="page" />页
						<div class="divBtu" @click="goPage(page)">跳转</div>
					</div>
					<div>
						共<span v-text="totlePage"></span>页
						<select v-model="param.rows" @change="getData()">
							<option value="10">10</option>
							<option value="20">20</option>
							<option value="30">30</option>
						</select>条/页
					</div>
				</div>
			</div>
		</div>
	</body>
	<script type="text/javascript" src="kcybb.js"></script>

</html>