var dqindex = null;
//药房处方类型选择
var jbM = {'门特病种编码': 'jbbm', '门特病种名称': 'jbmc', '拼音代码': 'pydm'};
var chose = new Vue({
    el: '.tong-search',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable,
        'jbsearch-table': searchTable,
    },
    data: {
        lczd: null,     //获取临床诊断
        searchCon: [],
        json: [],
        page: {
            page: 1,
            rows: 30,
            total: null
        },
        them: { // 检索的标题字段
            '诊断名称': 'zdmc'
        },
        myzdlist : {
            zd1:null,
            zd2:null,
            zd3:null,
            zd4:null,
            zd5:null,
            zd6:null,
        },
        mContent: {},
        msearchCon: [],
        mthem: jbM,
        pagemt: {
            page: 1,
            rows: 10,
            total: null
        },
        mtselSearch4: -1,
        popContent:{
            mtbzmc:'',
            mtbzbm:'',
        },
        Brxx_List:userNameBg.Brxx_List,
        fjzdlist:[],
        iszdchecked:[],
        userInfo:[],
        isHff: false,
    },
    mounted: function () {
        let that = this;
        this.$nextTick(function(){
            that.setfjzdlist();
        })
        this.getUserInfo();
    },
    methods: {
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    let userInfo = json.body.d;
                    chose.userInfo = userInfo;

                });
        },
        openFjzd:function (){
            console.log(1111)
            fjzdPop.fjzdShow=true;
            fjzdPop.fjzd = [{}];

        },
        getZdgxjg:function(){
            if(this.iszdchecked.length>0){
                let zd = '';
                let zd2 = '';
                let zd1 = '';
                for(let key of Object.keys(this.iszdchecked)){
                    console.log(key,this.iszdchecked[key]);
                    if(this.iszdchecked[key]){
                        if(key<90){
                            zd2+='('+this.fjzdlist[key].jbmb+')'+( this.fjzdlist[key].jbmc || '')+","
                        }else{
                            if(key == 90){
                                zd1+='('+this.Brxx_List.jbbm+')'+( this.Brxx_List.jbmc || '')+","
                            }else if(key == 91){
                                zd1+='('+this.Brxx_List.qtzdbm+')'+( this.Brxx_List.qtzdmc || '')+","
                            }else if(key == 92){
                                zd1+='('+this.Brxx_List.qtzdbm1+')'+( this.Brxx_List.qtzdmc1 || '')+","
                            }else if(key == 93){
                                zd1+='('+this.Brxx_List.qtzdbm2+')'+( this.Brxx_List.qtzdmc2 || '')+","
                            }else if(key == 94){
                                zd1+='('+this.Brxx_List.qtzdbm3+')'+( this.Brxx_List.qtzdmc3 || '')+","
                            }else if(key == 95){
                                zd1+='('+this.Brxx_List.qtzdbm4+')'+( this.Brxx_List.qtzdmc4 || '')+","
                            }
                        }
                    }

                }
                zd = zd1+zd2
                if(zd){
                    zd = zd.substring(0,zd.length-1)
                }
                return zd;
            }else{
                return '';
            }
        },
        reCheckBoxZyh: function (val) {
            console.log(val)
            if (val[0] == 'some') {
                Vue.set(this.iszdchecked, val[1], val[2]);
            }
            this.getZdgxjg();
        },
        isXzlhff(val) {
            // 更新先诊疗后付费复选框的状态
            Vue.set(this,'isHff',val); // 直接更新布尔值
        },
        setfjzdlist:function(){  this.fjzdlist = JSON.parse(userNameBg.Brxx_List.fjzd) },
        mtchangeDown: function (event, type, content, searchCon, modelBm, showBm, modelMc,selSearch) {
            //全局变量
            bm = modelBm;
            allBm = showBm;
            selSearch = selSearch;
            mc = modelMc;
            this.selectType = type;
            if (this[searchCon][this[selSearch]] == undefined) return;
            this.inputUpDown(event,this[searchCon],selSearch)
            this[content]=this[searchCon][this[selSearch]]
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {

                //*************************************疾病形态学
                if (type == "jbMT") {
                    Vue.set(this.popContent, 'mtbzbm', this.mContent['jbbm']);
                    Vue.set(this.popContent, 'mtbzmc', this.mContent['jbmc']);
                }
                this.nextFocus(event);
                $(".selectGroup").hide();
                this[selSearch]=-1
            }
        },
        mselectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.mtchange(true, allBm, this.popContent[allBm]);
            } else {
                                this.mContent = item;
                Vue.set(this.popContent, 'mtbzbm', this.mContent['jbbm']);
                Vue.set(this.popContent, 'mtbzmc', this.mContent['jbmc']);
                $(".selectGroup").hide();
            }
        },
        //当输入值后才触发
        mtchange: function (add, type, val,selSearch,mc,bm,tpevent) {
            this.selectType = type;
            if (!add) this.page.page = 1;
            var _searchEvent = $(tpevent.target.nextElementSibling).eq(0);
            this.popContent[mc] = val;
            this.page.parm = val;
            this.popContent[bm]='';
            this.page.ghxh = userNameBg.Brxx_List.ghxh;
            //病理诊断
            if (type == "jbMT") {
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=jbMT'
                    + '&json=' + JSON.stringify(this.page),
                    function (data) {
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                chose.msearchCon.push(data.d.list[i]);
                            }
                        } else {
                            chose.msearchCon = data.d.list;
                        }
                        chose.pagemt.total = data.d.total;
                        chose.mtselSearch4 = 0;
                        if (data.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                            return false;
                        }
                    });
            }

        },
        getReCheckOne:function(zd){
            if(this.myzdlist[zd]){
                Vue.set(this.myzdlist,zd,'')
            }else {
                Vue.set(this.myzdlist,zd,'1')
            }
        },
        // 这里是下拉框查询的
        searching: function (add, event) {
            this.lczd = event.target.value;
            if (!add) this.page.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var str_param = {
                parm: this.lczd,
                page: this.page.page,
                rows: this.page.rows,
                rybm: userId,
                ksbm: userNameBg.Brxx_List.ghks
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglLczd&types=query&parm=" + JSON.stringify(str_param),
                function (json) {
                    if (json.a == 0) {
                        if (json.d.list == null || json.d.list == "") {
                            $(".selectGroup").hide();
                        }
                        if (add) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                chose.searchCon.push(json.d.list[i]);
                            }
                        } else {
                            chose.searchCon = json.d.list;
                        }
                        chose.page.total = json.d.total;
                        chose.selSearch = 0;
                        if (json.d.list.length > 0 && !add) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert(json.c, 'top', 'defeadted')
                    }
                });
        },
        //鼠标双击（病人挂号信息）
        selectOne: function (item) {
            if (item == null) {
                this.page.page++;
                this.searching(true, this.lczd);
            } else {
                $(".selectGroup").hide();
                this.lczd = item.zdmc;
            }
        },
        //回车
        changeDown: function (event) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'json', 'searchCon');
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                this.lczd = this.json.zdmc;
            }
        },

    }

});
//底部工具栏
var brjzFoot = new Vue({
    el: '.brjz-foot',
    mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
    data: {
        saveShow: true,
        mbZhyzContent:{},
        // editTitle:'编辑',
        msgContent: {},  //底部需要的信息
        ifClick: true,
        //检查检验治疗打印需要的
        printList: [],
        jcList: [],
        jyList: [],
        zlList: [],
        editYzhm: '',
        N03001200143: '',
        N03001200164:"",
        showECG:false, //查看心电报告
		isBshow:true,
        hasHrxxData: false,
        hrxxUrl: null,
    },
    created: function () {
        if (window.top.J_tabLeft.obj.flpxdjkurl) {
            this.showECG = true;
        }
    },
	mounted:function(){
		//this.qxcl();
        this.getHrxxInfo();
	},
    methods: {
		qxcl:function(){
			
			if(userNameBg.Brxx_List.wcbz =='1'){
				this.isBshow = false;
			}else if(userNameBg.Brxx_List.ghrq && userNameBg.Brxx_List.ghxq){
				var date=new Date(userNameBg.Brxx_List.ghrq);
				var nowDate = new Date();
				 date=new Date(date.setDate(date.getDate()+userNameBg.Brxx_List.ghxq));
				 if(date>nowDate){
					 if(userNameBg.Brxx_List.jzys == userId){
						 this.isBshow = true;
					 }else{
						this.isBshow = false; 
					 }
				 }else{
					 this.isBshow = false;
				 }
				 
			}
		},
        getHrxxInfo: function () {
            if (!chose.Brxx_List.sfzh) {
                console.log("No ID number available");
                return;
            }
            // 保存 Vue 实例的引用
            var str_param = {
                zjhm: chose.Brxx_List.sfzh
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglLczd&types=hrxx&parm=" + JSON.stringify(str_param)).done((json) => {  // 使用箭头函数，this 仍然指向 Vue 实例
                if (json.a == "0" && json.d != null) {
                    this.hrxxUrl = json.d.url;
                    this.hasHrxxData = true;
                    console.log("URL set:", this.hrxxUrl);
                    console.log("Data flag set:", this.hasHrxxData);
                } else {
                    this.hasHrxxData = false;
                    console.log("No data returned from API");
                }
            }).fail(function() {
                this.hasHrxxData = false;
                console.log("API request failed");
            });
        },
        openHrxx: function() {
            if (this.hasHrxxData) {
                window.open(this.hrxxUrl);
                console.log("Opening mutual recognition information", this.hrxxInfo);
            }
        },
		openLisBG:function(){
			console.log(userNameBg.Brxx_List.ghxh)
			var url = 'http://220.220.220.9/ZhiFang.ReportFormQueryPrint/ui_new/doctor.html?PATNO='+userNameBg.Brxx_List.ghxh;
			window.open(url);
		},
        openUrl: function (sfyx) {
            if(sfyx){
                if(zcy.N03001200164){//翼展pacs系统
                    if(zcy.activeIndex != '0' &&  !zcy.activeIndex){
                        malert("请先选择要查看的项目！", 'top', 'defeadted');
                        return false;
                    }
                    if(!zcy.jcxmMxJson[zcy.activeIndex].fyjlid){
                        malert("无可查看的影像！", 'top', 'defeadted');
                        return false;
                    }
                    var url = zcy.N03001200164 + '/poly2/app/viewer.html?HISExamRequestID=' + (zcy.jcxmMxJson[zcy.activeIndex].fyjlid) + '&SysType=101&tmp=' + new Date().getTime();
                    window.open(url);
                }else{
                    malert("无可查看的影像！", 'top', 'defeadted');
                    return false;
                }
            }else{
                if(zcy.N03001200164){//翼展pacs系统
                    if(zcy.activeIndex != '0' &&  !zcy.activeIndex){
                        malert("请先选择要查看的报告！", 'top', 'defeadted');
                        return false;
                    }
                    if(!zcy.jcxmMxJson[zcy.activeIndex].fyjlid){
                        malert("无可查看的报告！", 'top', 'defeadted');
                        return false;
                    }
                    var url = zcy.N03001200164 + '/Report.html?HISExamRequestID=' + (zcy.jcxmMxJson[zcy.activeIndex].fyjlid) + '&SysType=101&tmp=' + new Date().getTime();
                    window.open(url);
                }else{
                    if(zcy.csqxContent.N05001200259 == '1'){
                        window.open(window.top.J_tabLeft.obj.xwpacsdz+"/ClinicList.aspx?colid0=220&colvalue0="+userNameBg.Brxx_List.ghxh+"");
                    }else{
                        var yzhm = [];
                        for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
                            yzhm.push(zcy.jcxmMxJson[i].yzhm+zcy.jcxmMxJson[i].fylb)
                        }
                        window.open(window.top.J_tabLeft.obj.xwpacsdz + "ClinicList.aspx?colid0=3078&colvalue0=~in~" + yzhm.join(',') + "");
                    }
                   }
            }
        },
        //PACS报告链接
        openPacsBG: function () {
            window.open("http://172.20.103.12:8802/?hisid=" + userNameBg.Brxx_List.brid);
        },
        openUrlECG: function () {
            window.open(window.top.J_tabLeft.obj.flpxdjkurl + "/Web/ECG/index_EMR.html?patientID=" + userNameBg.Brxx_List.ghxh);
        },
        editFypcf: function () {
            //扣费标志
            if (zcy.CfxxJson.kfbz == "1") {
                malert("该处方已经结算，不能编辑！", 'top', 'defeadted');
                return;
            }
            if (zcy.jcxmMxJson.length > 0) {
                if (zcy.jcxmMxJson[zcy.jcxmMxJson.length - 1].mxfyxmmc == undefined || zcy.jcxmMxJson[zcy.jcxmMxJson.length - 1].mxfyxmmc == '') {

                } else {
                    zcy.jcxmMxJson.push({});
                }
            }
            zcy.addShow[zcy.num] = true;
            brjzFoot.editYzhm = brjzFoot.msgContent.cfh;
            brjzFoot.msgContent.cfh = null;
            zcy.CfxxJson.yzhm = "";
        },
        // 打印检查，检验，治疗申请单
        printJc: function (type) {
            // brjzFoot.jcList = [];
            // brjzFoot.jyList = [];
            // brjzFoot.zlList = [];
            // if (zcy.CfxxJson.yzhm == null || zcy.CfxxJson.yzhm == "") {
            //     malert("当前没有检查，检验，治疗申请单可以打印", 'top', 'defeadted');
            //     return false;
            // }
            var parm = {
                yzhm: zcy.CfxxJson.yzhm,
            };
            if(zcy.CfxxJson.yzhm==null ||  zcy.CfxxJson.yzhm ==undefined ){
                malert("无项目打印！", 'top', 'defeadted');
                return
            };
            brjzFoot.jcList = []
            brjzFoot.jyList = []
            brjzFoot.zlList = []
            //帆软打印
            // var reportFileName = "fpdy%2Fyfgl%2Fyfgl_fcfj.cpt";

            // if (type == '1') {
            //     reportFileName = "fpdy%2Fyfgl%2Fyfgl_fcfj.cpt";
            // } else if (type == '2') {
            //     reportFileName = "fpdy%2Fyfgl%2Fyfgl_fcfj2.cpt";
            // } else if (type == '3') {
            //     reportFileName = "fpdy%2Fyfgl%2Fyfgl_fcfj3.cpt";
            // } else {
            //     malert("只能打印检查/检验/治疗单,请重新选择打印模板！", 'top', 'defeadted');
            //     return;
            // }
			
			// let jyList = zcy.jcxmMxJson;
			// let jyxhid = '';
			// let str = '';
			// let map = new Map();
			// 		            for (var i = 0; i < jyList.length; i++) {
			// 						if(map.size>0){
			// 							if(map.get(jyList[i].jyfdbh)){
			// 								let tpxhids =map.get(jyList[i].jyfdbh)+(jyList[i].mxxmbm + ",")
			// 								map.delete(jyList[i].jyfdbh)
			// 								map.set(jyList[i].jyfdbh,tpxhids)
			// 							}else{
			// 								if(jyList[i].jyfdbh){
			// 									map.set(jyList[i].jyfdbh,jyList[i].mxxmbm + ",")
			// 								}else{
			// 									jyxhid += (jyList[i].mxxmbm + ",")
			// 								}
			// 							}
			// 						}else{
			// 							if(jyList[i].jyfdbh){
			// 								map.set(jyList[i].jyfdbh,jyList[i].mxxmbm + ",")
			// 							}else{
			// 								jyxhid += (jyList[i].mxxmbm + ",")
			// 							}
			// 						}
			// 		            }
			// 					if(jyxhid){
			// 						str += "{reportlet: 'fpdy%2Fyfgl%2Fyfgl_fcfj_x.cpt',jyfdbh:'',ghxh:'" + zcy.CfxxJson.ryghxh + "',yzhm:'" + zcy.jcxm_List[zcy.num].yzhm + "'},"
			// 					}
								
			// 					map.forEach(function(value, key, self){
			// 						str += "{reportlet: 'fpdy%2Fyfgl%2Fyfgl_fcfj_x.cpt',jyfdbh:'" + key + "',ghxh:'" + zcy.CfxxJson.ryghxh + "',yzhm:'" + zcy.jcxm_List[zcy.num].yzhm + "'},"
			// 					})
			// 					console.log(str)
			
			
			

				// str = str.substr(0, str.length - 1);
		  //       var reportlets = '[' + str + ']';
		  //       console.log(reportlets);
		  
var reportFileName = "fpdy%2Fyfgl%2Fyfgl_fcfj_x.cpt";
            var reportlets = "[{reportlet:'" + reportFileName + "',yljgbm:'" + jgbm + "',ghxh:'" + zcy.CfxxJson.ryghxh + "',yzhm:'" + zcy.jcxm_List[zcy.num].yzhm + "'}]";

            var isPopUp = null;
            if (zcy.csqxContent.N03001200142 == '1') {//打印预览
                isPopUp = true;
            }
            if(!window.top.J_tabLeft.obj.FRorWindow){
                if (!FrPrint(reportlets, null, null, isPopUp)) {
                    this.printWy(parm)
                }
            }else {
                this.printWy(parm)
            }
        },
        printWy:function(parm){
            if(brjzFoot.printList){
                for (var i = 0; i < brjzFoot.printList.length; i++) {
                    // 性别转换
                    brjzFoot.printList[i]['brxb'] = brjzFoot.brxb_tran[brjzFoot.printList[i]['brxb']];
                    // 年龄转换
                    brjzFoot.printList[i]['nldw'] = brjzFoot.nldw_tran[brjzFoot.printList[i]['nldw']];
                    if (brjzFoot.printList[i]['ffylb'] && brjzFoot.printList[i]['ffylb'].indexOf("30") >= 0) {
                        brjzFoot.jcList.push(brjzFoot.printList[i]);
                    } else if (brjzFoot.printList[i]['ffylb'] == '4') {
                        brjzFoot.jyList.push(brjzFoot.printList[i]);
                    } else if (brjzFoot.printList[i]['ffylb'] == '6') {
                        brjzFoot.zlList.push(brjzFoot.printList[i]);
                    }
                }
                if (brjzFoot.jcList.length < 1 && brjzFoot.jyList.length < 1 && brjzFoot.zlList.length < 1) {
                    malert("无项目打印！", 'top', 'defeadted');
                    return
                } else {
                    brjzFoot.doAginPrint();
                }
            }else{
                $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=printJcjySqd&parm=" + JSON.stringify(parm), function (json) {
                    if (json.d != null) {
                        var zlxmList = [];
                        var zlxmmxList = [];

                        brjzFoot.printList = json.d.list;
                        for (var i = 0; i < brjzFoot.printList.length; i++) {
                            // 性别转换
                            brjzFoot.printList[i]['brxb'] = brjzFoot.brxb_tran[brjzFoot.printList[i]['brxb']];
                            // 年龄转换
                            brjzFoot.printList[i]['nldw'] = brjzFoot.nldw_tran[brjzFoot.printList[i]['nldw']];
                            if (brjzFoot.printList[i]['ffylb'] && brjzFoot.printList[i]['ffylb'].indexOf("30") >= 0) {
                                brjzFoot.jcList.push(brjzFoot.printList[i]);
                            } else if (brjzFoot.printList[i]['ffylb'] == '4') {
                                brjzFoot.jyList.push(brjzFoot.printList[i]);
                            } else if (brjzFoot.printList[i]['ffylb'] == '6') {
                                brjzFoot.zlList.push(brjzFoot.printList[i]);
                            }
                        }
                        if (brjzFoot.jcList.length < 1 && brjzFoot.jyList.length < 1 && brjzFoot.zlList.length < 1) {
                            malert("无项目打印！", 'top', 'defeadted');
                            return
                        } else {
                            brjzFoot.doAginPrint();
                        }
                    }
                });
            }



        },
        doAginPrint: function () {
            if (brjzFoot.jcList.length > 0) {
                if (brjzFoot.jcList.length <= 5) {
                    var dyjclist = {};
                    var xmlist = [];
                    var fyze = 0.00;
                    for (var i = 0; i < brjzFoot.jcList.length; i++) {
                        xmlist.push(brjzFoot.jcList[i]['brfys'][0]);
                    }

                    dyjclist = brjzFoot.jcList[0];
                    dyjclist.brfys = xmlist;
                    for (var k = 0; k < xmlist.length; k++) {
                        fyze += xmlist[k].fyje;
                    }
                    dyjclist.fyje = fyze;
                    brjzFoot.doPrintJc(dyjclist, xmlist);
                } else {
                    for (var i = 0; i < brjzFoot.jcList.length; i += 5) {
                        var xjsonlist = brjzFoot.jcList.slice(i, i + 5);
                        var dyjclist = {};
                        var xmlist = [];
                        var fyze = 0.00;
                        for (var j = 0; j < xjsonlist.length; j++) {
                            xmlist.push(xjsonlist[j]['brfys'][0]);
                        }
                        dyjclist = xjsonlist[0];
                        dyjclist.brfys = xmlist;
                        for (var k = 0; k < xmlist.length; k++) {
                            fyze += xmlist[k].fyje;
                        }
                        dyjclist.fyje = fyze;
                        brjzFoot.doPrintJc(dyjclist, xmlist);
                    }
                }
            }
            if (brjzFoot.jyList.length > 0) {
                if (brjzFoot.jyList.length <= 5) {
                    var dyjylist = {};
                    var xmlist = [];
                    var fyze = 0.00;
                    for (var i = 0; i < brjzFoot.jyList.length; i++) {
                        xmlist.push(brjzFoot.jyList[i]['brfys'][0]);
                    }
                    dyjylist = brjzFoot.jyList[0];
                    dyjylist.brfys = xmlist;
                    for (var k = 0; k < xmlist.length; k++) {
                        fyze += xmlist[k].fyje;
                    }
                    dyjylist.fyje = fyze;
                    brjzFoot.doPrintJy(dyjylist, xmlist);
                } else {
                    for (var i = 0; i < brjzFoot.jyList.length; i += 5) {
                        var xjsonlist = brjzFoot.jyList.slice(i, i + 5);
                        var dyjylist = {};
                        var xmlist = [];
                        var fyze = 0.00;
                        for (var j = 0; j < xjsonlist.length; j++) {
                            xmlist.push(xjsonlist[j]['brfys'][0]);
                        }
                        dyjylist = xjsonlist[0];
                        dyjylist.brfys = xmlist;
                        for (var k = 0; k < xmlist.length; k++) {
                            fyze += xmlist[k].fyje;
                        }
                        dyjylist.fyje = fyze;
                        brjzFoot.doPrintJy(dyjylist, xmlist);
                    }
                }
            }
            if (brjzFoot.zlList.length > 0) {
                if (brjzFoot.zlList.length <= 5) {
                    var dyzllist = {};
                    var xmlist = [];
                    var fyze = 0.00;
                    for (var i = 0; i < brjzFoot.zlList.length; i++) {
                        xmlist.push(brjzFoot.zlList[i]['brfys'][0]);
                    }
                    dyzllist = brjzFoot.zlList[0];
                    dyzllist.brfys = xmlist;
                    for (var k = 0; k < xmlist.length; k++) {
                        fyze += xmlist[k].fyje;
                    }
                    dyzllist.fyje = fyze;
                    brjzFoot.doPrintZl(dyzllist, xmlist);
                } else {
                    for (var i = 0; i < brjzFoot.zlList.length; i += 5) {
                        var xjsonlist = brjzFoot.zlList.slice(i, i + 5);
                        var dyzllist = {};
                        var xmlist = [];
                        var fyze = 0.00;
                        for (var j = 0; j < xjsonlist.length; j++) {
                            xmlist.push(xjsonlist[j]['brfys'][0]);
                        }
                        dyzllist = xjsonlist[0];
                        dyzllist.brfys = xmlist;
                        for (var k = 0; k < xmlist.length; k++) {
                            fyze += xmlist[k].fyje;
                        }
                        dyzllist.fyje = fyze;
                        brjzFoot.doPrintZl(dyzllist, xmlist);
                    }
                }
            }
        },
        //门诊检验申请单调用打印
        doPrintJy: function (jyContent, jyList) {
            // 查询打印模板
            var json = {repname: '检验申请单'};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                brjzFoot.clearArea(json.d[0]);
                // 绘制模板的canvas
                brjzFoot.drawList = JSON.parse(json.d[0]['canvas']);
                brjzFoot.creatCanvas();
                brjzFoot.reDraw();
                // 为打印前生成数据
                brjzFoot.printContent(jyContent);
                brjzFoot.printTrend(jyList);
                // 开始打印
                window.print();
            });
        },
        //门诊检查申请单调用打印
        doPrintJc: function (jcContent, jcList) {
            // 查询打印模板
            var json = {repname: '检查申请单'};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                brjzFoot.clearArea(json.d[0]);
                // 绘制模板的canvas
                brjzFoot.drawList = JSON.parse(json.d[0]['canvas']);
                brjzFoot.creatCanvas();
                brjzFoot.reDraw();
                // 为打印前生成数据
                brjzFoot.printContent(jcContent);
                brjzFoot.printTrend(jcList);
                // 开始打印
                window.print();
            });
        },
        //门诊治疗单调用打印
        doPrintZl: function (zlContent, zlList) {
            if (zlList.length <= 0) {
                return false;
            }
            // 查询打印模板
            var json = {repname: '门诊治疗单'};
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                // 清除打印区域
                brjzFoot.clearArea(json.d[0]);
                // 绘制模板的canvas
                brjzFoot.drawList = JSON.parse(json.d[0]['canvas']);
                brjzFoot.creatCanvas();
                brjzFoot.reDraw();
                // 为打印前生成数据
                brjzFoot.printContent(zlContent);
                brjzFoot.printTrend(zlList);
                // 开始打印
                window.print();
            });
        },
        //新加代码-贵州医药监管-门诊检查检验审核
        gz_yyjgJcJy: function () {
        },
        //保存处方信息
        save: function () {
            //门诊诊断
            if(!brjzFoot.ifClick){
                malert("请勿重复点击！","top","defeadted");
                return;
            }
            brjzFoot.ifClick = false;
            //门诊诊断
            var lczd = chose.getZdgxjg();
            var mtbzbm = '';
            var mtbzmc = '';
            if(chose.Brxx_List.fbbm=='40' || chose.Brxx_List.fbbm=='41'){
                if(chose.popContent.mtbzbm){
                    mtbzbm = chose.popContent.mtbzbm;
                    mtbzmc = chose.popContent.mtbzmc;
                }else{
                    malert("特殊病人病种不能为空！", 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
            }
            if (lczd == null || lczd == undefined || lczd == "") {
                malert("临床诊断不能为空！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return;
            }
            if (brjzFoot.msgContent.cfh && brjzFoot.msgContent.cfh != '空') {
                malert("该处方已经保存,请勿重复点击！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                return;
            }
            var tempZxks = '';
            for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
                //确认第一条mx的执行科室
                tempZxks = zcy.jcxmMxJson[0].zxks;
                zcy.jcxmMxJson[i].fyjlid = null;
                zcy.jcxmMxJson[i].yzhm = null;
                if(fyxmTab.csqx.N03001200173 == '1'){
                    zcy.jcxmMxJson[i].mzks = fyxmTab.dqks;
                    zcy.jcxmMxJson[i].mzksmc = fyxmTab.dqksmc;
                }
                var mxfyxmbm = zcy.jcxmMxJson[i].mxfyxmbm;    //费用编码
                var mxfyxmmc = zcy.jcxmMxJson[i].mxfyxmmc;    //费用名称
                var fysl = zcy.jcxmMxJson[i].fysl;    //费用数量
                var fydj = zcy.jcxmMxJson[i].fydj;    //费用单价
                var zxks = zcy.jcxmMxJson[i].zxks;    //执行科室
                zcy.jcxmMxJson[i].zxksmc = zcy.listGetName(zcy.zxksList, zcy.jcxmMxJson[i].zxks, 'ksbm', 'ksmc');
                var row = i + 1;
                //费用编码判断
                if (mxfyxmbm == null || mxfyxmbm == undefined || mxfyxmbm == "") {
                    zcy.jcxmMxJson.splice(i, 1);  //费用编码为空时移出当前行
                    continue;  //跳出本次循环
                }
                //费用数量判断
                if (fysl == null || fysl == undefined ) {
                    malert("第【" + row + "】行费用名称【" + mxfyxmmc + "】的【费用数量】不能等于0或为空", 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
                //费用单价判断
                // if (fydj == null || fydj == undefined || fydj <= 0) {
                //     malert("第【" + row + "】行费用名称【" + mxfyxmmc + "】的【费用单价】不能等于0或为空", 'top', 'defeadted');
                //     brjzFoot.ifClick = true;
                //     return;
                // }
                //执行科室判断：1.执行科室不能为空
                if (zxks == null || zxks == undefined || zxks == '') {
                    malert("第【" + row + "】行费用名称【" + mxfyxmmc + "】的【执行科室】不能为空", 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
                //执行科室判断：2.执行科室不相同，不能在一张单子上
                if (zxks != tempZxks) {
                    malert("第【" + row + "】行费用名称【" + mxfyxmmc + "】的【执行科室】不相同，请另开处方", 'top', 'defeadted');
                    brjzFoot.ifClick = true;
                    return;
                }
                zcy.jcxmMxJson[i].fyje = brjzFoot.MathFun(brjzFoot.accMul(zcy.jcxmMxJson[i].fysl,zcy.jcxmMxJson[i].fydj)); 
                zcy.jcxmMxJson[i].rymzzd = lczd;

                zcy.jcxmMxJson[i].mtbzmc = chose.popContent.mtbzmc;
                zcy.jcxmMxJson[i].mtbzbm = chose.popContent.mtbzbm;

                if (zcy.CfxxJson.yzhm != undefined && zcy.CfxxJson.yzhm != null && zcy.CfxxJson.yzhm != "") {//处方号存在则进行修改操作
                    zcy.jcxmMxJson[i].yzhm = zcy.CfxxJson.yzhm;
                }
            }
            if(zcy.csqxContent.N05001200246){
                brjzFoot.yyjgpt();
                return;
            }
            if (window.top.J_tabLeft.obj.cdssUrl){
                brjzFoot.cdssCheck();
                return;
            }
            brjzFoot.finalSaveFypcf();

            //小丑鱼食源性疾病集成调用
            try {
                let diagnosis = brxx.zdxx.jbmc;
                if (chose.getZdgxjg()) {
                    // 匹配所有 ()后面紧跟的中文疾病名
                    const matches = chose.getZdgxjg().match(/\([^)]+\)([^,]+)/g);
                    if (!matches) return '';
                    diagnosis = matches.map(m => m.replace(/\([^)]+\)/, '').trim()).join('|');
                }
                console.log("****chose.getZdgxjg()****:"+chose.getZdgxjg());
                console.log("****diagnosis****:"+diagnosis);
                const params = ['InfectiousBoot','0',userNameBg.Brxx_List.ghxh,brxx.lsghxxList.length, brxx.zdxx.jzys,diagnosis,brxx.zdxx.sffz].join('&');
                const nisUrl = `universalLinks:///?${params}`;
                console.log("********:"+params);
                console.log("********:"+nisUrl);
                window.location.href = nisUrl;
            } catch (err) {
                malert("食源性疾病集成调用失败！", 'top', 'defeadted');
                return false;
            }
        },
        finalSaveFypcf:function(){
            //入参
            var parm = {
                list: [{
                    Mzfy: zcy.jcxmMxJson,
                    Ghxx: userNameBg.Brxx_List,
                    Zcxx: userNameBg.Brxx_List,
                    ishff: chose.isHff
                }]
            };
            if(null != chose.Brxx_List.sfzh){
                brjzFoot.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=query_hrxx', JSON.stringify(parm)).then(function(data) {
                    if (data.body.a == 0) {
                        var hrxxData = data.body.d;
                        if (null != hrxxData && hrxxData.url && hrxxData.url.trim() !== '') {
                            // 如果有数据，弹出弹窗展示数据
                            popHrxx.popShow = true;
                            popHrxx.hrxxContent = hrxxData;
                        } else {
                            brjzFoot.savePrescription();
                        }
                    }else{
                        brjzFoot.savePrescription();
                    }
                });
            }else{
                brjzFoot.savePrescription();
            }
        },
        savePrescription: function () {
            console.log("进入保存方法了.....")
            var parm = {
                list: [{
                    Mzfy: zcy.jcxmMxJson,
                    Ghxx: userNameBg.Brxx_List,
                    Zcxx: userNameBg.Brxx_List,
                    ishff: chose.isHff
                }]
            };
            brjzFoot.popShow = false;
            brjzFoot.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Save_jcxm', JSON.stringify(parm)).then(function (data) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (data.body.a == 0) {
                    console.log('Checkbox value:', chose.isHff);
                    // 让复选框恢复到未选中状态
                    chose.isHff = false;
                    if(fyxmTab.csqx.N05001200270 && userNameBg.Brxx_List.sfzjhm!=''){
                        let tpfyhj = 0;
                        for (let i = 0; i < zcy.jcxmMxJson.length; i++) {
                            tpfyhj = brjzFoot.MathAdd(tpfyhj, zcy.jcxmMxJson[i].fyje);
                        }
                        userNameBg.payWx('notifyOutpatientFee',tpfyhj)
                    }
                    malert("处方保存成功！", 'top', 'success');
                    brjzFoot.ifClick = true;
                    //根据挂号序号再次查询检查项目处方信息
                    zcy.num = 0;
                    zcy.Wf_selectCFJcxm();
                    if(zcy.csqxContent.N03001200157 == '1'){
                        brjzFoot.printJc(2)
                    }
                    this.saveShow = true;
                    // this.editTitle='编辑';
                    zcy.addShow[zcy.num] = false;
                    zcy.tabShow = false;
                    zcy.addZj = false;
                    $("input[name='text']").each(function () {
                        $(this).attr('disabled', true)
                        $(this).addClass('input-border');
                    })
                    //修改保存后作废原处方
                    if (zcy.csqxContent.N03001200143 == '1') {
                        //删除处方
                        if (!brjzFoot.editYzhm) {
                            return
                        }
                        var parm = {
                            yzhm: brjzFoot.editYzhm,
                            ryghxh: userNameBg.Brxx_List.ghxh,
                            ksbm:userNameBg.Brxx_List.mzks
                        };
                        brzcList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Delete_jcxm', JSON.stringify(parm)).then(function (data) {
                            if (data.body.a == 0) {
                                //malert("处方作废成功！", 'top', 'success');
                                brjzFoot.editYzhm = '';
                                // zcy.jcxm_List.splice(zcy.CfxxJson.index, 1);
                                zcy.Wf_selectCFJcxm();   //刷新一下病人处方 信息
                            } else {
                                malert("处方作废失败：" + data.body.c, 'top', 'defeadted');
                            }
                        });
                    }
                    // 绿色通道推送检查检验数据
                    if (userNameBg.Brxx_List.lstdbz == '1') {
                        var parm = {
                            beginrq: getTodayDateBegin(),
                            endrq: getTodayDateEnd(),
                            ksbm: userNameBg.Brxx_List.ghks
                        };
                        $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqsqByWebService&ksbm=" + userNameBg.Brxx_List.ghks + "&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0) {
                            } else {
                                malert("上传检验项目失败！" + json.c, 'top', 'defeadted');
                            }
                        });
                        $.getJSON("/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_hqjcByYzpacs&parm=" + JSON.stringify(parm), function (json) {
                            if (json.a == 0) {

                            } else {
                                malert("上传检查项目失败！" + json.c, 'top', 'defeadted');
                            }
                        });
                    }
                } else {
                    brjzFoot.ifClick = true;
                    malert(data.body.c, 'top', 'defeadted')
                }
                zcy.fybzShow = []
            });
        },
        //CDSS调用验证
        cdssCheck: function () {
            $.ajaxSettings.async = false;
            var flag = true;
            for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
                var xmType ;
                var itemType;
                if (zcy.jcxmMxJson[i].jclx == "2" || zcy.jcxmMxJson[i].ffylb == '4'){ //检验
                    xmType = 'JY';
                    itemType = '6';
                } else if (zcy.jcxmMxJson[i].jclx == "1" || zcy.jcxmMxJson[i].ffylb == "301" || zcy.jcxmMxJson[i].ffylb == "302"
                    || zcy.jcxmMxJson[i].ffylb == "303" || zcy.jcxmMxJson[i].ffylb == "304" || zcy.jcxmMxJson[i].ffylb == "305"){    //检查
                    xmType = 'JC';
                    itemType = '5';
                }else {
                    continue;
                }
                var hqxmUrl = window.top.J_tabLeft.obj.cdssUrl + "/klgbase-api/knowledge/his-items";
                var hqxmParm = {
                    pageNumber:1,
                    pageSize:1,
                    type: xmType,
                    queryKey:zcy.jcxmMxJson[i].mxfyxmbm
                }
                this.postNewAjax(hqxmUrl, hqxmParm,function(json){
                    if (json.status == 200 && json.data.list != 0) {
                        var res = json.data.list[0];
                        var inspectObj = null;
                        var examineObj = null;
                        if (zcy.jcxmMxJson[i].jclx == "2" || zcy.jcxmMxJson[i].ffylb == '4'){ //检验
                            examineObj = {
                                catalogId: 0,
                                xmName:res.xm_name,
                                xmCode:res.xm_code,
                                jyffCode:res.jyff,
                                yblxCode:'',
                                jyTypeCode:res.jy_tpye_code,
                                cjbw:'',
                                jgjldwCode:res.jgjldw_code,
                                jgzcbsCode:'',
                                startCkz:'',
                                endCkz:'',
                                syz:'',
                                jjz:''
                            }
                        } else if (zcy.jcxmMxJson[i].jclx == "1" || zcy.jcxmMxJson[i].ffylb == "301" || zcy.jcxmMxJson[i].ffylb == "302"
                            || zcy.jcxmMxJson[i].ffylb == "303" || zcy.jcxmMxJson[i].ffylb == "304" || zcy.jcxmMxJson[i].ffylb == "305"){ //检查
                            inspectObj = {
                                xmName:res.xmName,
                                xmCode:res.xmCode,
                                bwName:'',
                                bwCode:'',
                                jcTypeCode:res.jcTypeCode,
                                yxFlagCode:'',
                                syz:'',
                                jjz:'',
                                status:1
                            }
                        }

                        //判断患者是否可以做该项目
                        var operJson = {
                            "ysCode": userId,
                            itemType: itemType, //1-人资 2-患者 3-药品 4-诊断 5-检查 6-检验 7-过敏史
                            itemCode: res.xmCode, //项目Code
                            hisPatients:{
                                age:userNameBg.Brxx_List.nl
                            },
                            inspect:inspectObj,
                            diagnosis:{//诊断信息
                                catalogId:0,
                                name:userNameBg.Brxx_List.ryzdmc,
                                icd10Code:userNameBg.Brxx_List.jbbm,
                                type:'2',
                                zzdFlag:'1',
                                crbFlag:'0',
                                cffmFlag:'0',
                                dbzFlag:'0'
                            },
                            examine:examineObj,
                            historyallergy:null
                        };
                        this.postAjax(window.top.J_tabLeft.obj.cdssUrl + '/rule/operation-1', JSON.stringify(operJson), function (data) {
                            if (data.status == 200 && data.data.flag) {
                                //可以执行
                            } else {
                                malert("【" + data.data.name + "】" +data.data.feedback, 'top', 'defeadted');
                                flag = false;
                                return;
                            }
                        }, function (error) {
                            malert("判断患者是否可以做该项目发生异常", 'top', 'defeadted');
                            flag = false;
                            return;
                        });
                    } else {
                        malert('CDSS获取项目失败！', 'top', 'defeadted');
                        flag = false;
                        return;
                    }
                },function (error) {
                    malert('CDSS接口异常', 'top', 'defeadted');
                    flag = false;
                    return;
                });

                //cdss接口异常或不允许执行都不往下走了
                if (!flag){
                    brjzFoot.ifClick = true;
                    return;
                }
            }

            if (flag){
                brjzFoot.finalSaveFypcf();
            }
        },

        /**
         * @zh 药监平台接口
         * @param parm
         */
        yyjgpt: function () {
            var parm = {
                yljgbm:jgbm,
                Mzfy: zcy.jcxmMxJson,
                Ghxx: userNameBg.Brxx_List,
                Zcxx: userNameBg.Brxx_List,
            };
            //this.postAjax(zcy.csqxContent.N05001200246, JSON.stringify(parm), function (json) {

            brjzFoot.$http.post(zcy.csqxContent.N05001200246, JSON.stringify(parm)).then(function (json) {
                brjzFoot.ifClick = true;
                if (json.success == "T") {
                    malert(json.body.error_msg, 'top', 'success');
                    brjzFoot.finalSaveFypcf();
                }else{
                    common.openConfirm("药监品台提示：<br><br>" + json.body.error_msg + "<br><br>是否继续保存处方？", function () {
                        brjzFoot.finalSaveFypcf();
                    }, function () {
                    });
                }
            },function(e){
                malert("药监平台接口已断开，请联系管理员！", 'top', 'defeadted');
                brjzFoot.ifClick = true;
                brjzFoot.finalSaveFypcf();
            });
        },
        //作废处方
        removeCf: function () {
            //如果处方号为空，直接移出当前元素
            if (!zcy.CfxxJson.yzhm) {
                zcy.jcxm_List.splice(zcy.CfxxJson.index, 1);
                zcy.jcxmMxJson = [];
                setTimeout(function () {
                    zcy.num = zcy.jcxm_List.length - 1
                    zcy.tabShowActive()
                }, 100)
                return;
            }

            //扣费标志
            if (zcy.CfxxJson.kfbz == "1") {
                malert("该处方已经结算，不能删除！", 'top', 'defeadted');
                return;
            }

            //给用户确认
            common.openConfirm("确定要删除当前处方吗？", sccf);

            function sccf() {
                //删除处方（
                var parm = {
                    yzhm: zcy.CfxxJson.yzhm,
                    ryghxh: userNameBg.Brxx_List.ghxh,
                    ksbm:userNameBg.Brxx_List.ksbm
                };
                brzcList.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=Delete_jcxm', JSON.stringify(parm)).then(function (data) {
                    if (data.body.a == 0) {
                        malert("处方作废成功！", 'top', 'success');
                        zcy.jcxm_List.splice(zcy.CfxxJson.index, 1);
                        zcy.Wf_selectCFJcxm();   //刷新一下病人处方 信息
                        setTimeout(function () {
                            zcy.num = zcy.jcxm_List.length - 1
                            zcy.tabShowActive()
                        }, 100)
                    } else {
                        malert("处方作废失败：" + data.body.c, 'top', 'defeadted');
                    }
                });
            }

        },
        // 检查检验
        jcjyOpen: function () {
            jcjyfy.show();
            jcjyfy.getJcjyd();
            jcjyfy.zxksbm = null;
        },

        //保存为模板
        saveModel: function () {
            pop.mbZhyzContent = {
                zhyzlx: '1',
                ypbz: '0'
            };
            pop.popShow = true;
            pop.bcTitle = '保存为模板';
            pop.bcShow = true;
            $(".blRight").css({
                'z-index': '0'
            })
//            if(zcy.cfcontent.yysmbm!=null){
//            	 pop.mbZhyzContent.zyyf=zcy.cfcontent.yysmbm;
//            }
            if (userNameBg.Brxx_List.zyzh != null) {
                pop.mbZhyzContent.zz = userNameBg.Brxx_List.zyzh;
            }
            if (userNameBg.Brxx_List.zyzf != null) {
                pop.mbZhyzContent.zf = userNameBg.Brxx_List.zyzf;
            }
        }
    }
});

var popHrxx = new Vue({
    el: '#popHrxx',
    data: {
        popShow: false,
        hrxxContent:{},
    },
    methods: {
        closePopup: function () {
            this.popShow = false;
            if (brjzFoot && typeof brjzFoot.savePrescription === 'function') {
                brjzFoot.savePrescription(); // 调用 brjzFoot 的方法
            } else {
                console.error("brjzFoot.savePrescription 方法不存在！");
            }
        },
    },
})

var fjzdPop = new Vue({
    el: '.fjzdPop',
    components: {
        'search-table': searchTable
    },
    mixins: [baseFunc, tableBase, checkData],
    data: {
        fjzdShow: false,
        fjzd:[],
        popContent:{},
        them:{'疾病编码': 'jbmb', '疾病名称': 'jbmc'},
        queryObj:{
            page:1,
            rows:50,
            sort:'',
            order:'asc'
        },
        searchCon:[],
        selSearch:-1,
    },
    methods: {
        addFun:function (){
            if(this.fjzd.length == 0 ){
                this.fjzd.push({});
            }else if(this.fjzd[this.fjzd.length-1]['jbmb']){
                this.fjzd.push({});
            }
        },
        saveData:function () {
            if(!chose.fjzdlist){
                chose.fjzdlist = [];
            }
            for (var i = this.fjzd.length - 1; i >= 0; i--) {

                if (!this.fjzd[i].jbmb) {
                    this.fjzd.splice(i, 1);
                }else {
                    chose.fjzdlist.push(this.fjzd[i]);

                }
            }

            var fjzdmx='';
            for (var i = chose.fjzdlist.length - 1; i >= 0; i--) {

                fjzdmx+=chose.fjzdlist[i].jbmb+chose.fjzdlist[i].jbmc+','
            }


            var str_param = {
                ghxh: userNameBg.Brxx_List.ghxh,
                brid: userNameBg.Brxx_List.brid
            };

            let that = this;

            that.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1GhglGhywBrgh',
                    types: 'queryOne',
                    parm: JSON.stringify(str_param)
                }
            }).then(function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    let zdxx = json.body.d.list[0].ghxx;
                    zdxx.fjzd=chose.fjzdlist;

                    userNameBg.Brxx_List.fjzd = JSON.stringify(chose.fjzdlist);

                    zdxx.fjzdxm=zdxx.jbbm+zdxx.jbmc+','+fjzdmx;

                    var parm = {
                        Ghxx: zdxx,
                        Zcxx: null
                    };
                    var json = JSON.stringify(parm);
                    that.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=updateBrjbxxAndGhxx', json).then(function (data) {

                    })


                }
            })





            chose.$forceUpdate();




            this.fjzdShow=false
        },
        change:function (add,index,mc,bm,val,types){
            if (!add) this.queryObj.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.fjzd[this.allIndex][mc] = val;
            this.queryObj.parm = val;
            this.fjzd[this.allIndex][bm]='';
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types='+types+'&json=' + JSON.stringify(this.queryObj), function (data) {
                if (add) {
                    fjzdPop.searchCon=fjzdPop.selSearch.concat(data.d.list)
                } else {
                    fjzdPop.searchCon = data.d.list;
                }
                fjzdPop.page.total = data.d.total;
                fjzdPop.selSearch= 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                    return false;
                }
            });
        },
        changeDown:function (event,index,zdms,zdbm,qtzd){
            this.allIndex=index;
            if (this['searchCon'][this['selSearch']] == undefined) return;
            this.inputUpDown(event,this['searchCon'],'selSearch')
            this['popContent']=this['searchCon'][this['selSearch']];
            if(event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter'){
                this.fjzd[index]=this.popContent;
                $(".selectGroup").hide();
                this.selSearch= -1;
                this.addFun()
                this.$nextTick(function (){
                    this.nextFocus(event);
                })
            }
        },
        checkedTwoOut:function (item){
            if(!item){
                ++this.queryObj.page;
                this.change(true,this.allIndex,'jbmc','jbbm',this.fjzd[this.allIndex]['jbmc'])
            }else {
                this.fjzd[this.allIndex]=item;
                $(".selectGroup").hide();
                this.selSearch= -1;
                this.$forceUpdate()
            }
        },
    }
})
//处方主体部分
var zcy = new Vue({
    el: '.dzcf',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        num: 0,
        tabShow: false,
        addShow: [],
        cfShow: false,
        addZj: false,
        isChecked: [],
        checkAll: false,
        isCheck: [],
        fybzShow: [],
        isShow: false,
        editIndex: null,
        Yzxx_List: [],    //西药处方列表
        zcyList: [],
        objData: [
            {type: '0', text: '临床诊断', t: ''},//input
            {type: '0', text: '检查部位', t: ''},//select
            {type: '0', text: '临床症状', t: ''},//input
            {type: '2', text: '检查描述', t: ''},//textarea

        ],
        /* jsonList: [
             {yfbm:'0',yfmc:'临床诊断',ksbm:'01'},
             {yfbm:'1',yfmc:'检查部位',ksbm:'02'},
             {yfbm:'2',yfmc:'检查目的',ksbm:'03'},
         ],*/
        dqIndex: "",//获取当前选中的
        jcxm_List: [], //查询检查处方
        jcxmMxJson: [], //检查检验集合
        CfxxJson: {}, //当前处方信息
        zxksList: [], //执行科室
        ffylb: null, //用于判断是否属于检查检验
        leftNum: 0,
        page: {
            page: 1,
            rows: 30,
            total: null
        },
        popContent: {}, //暂存下拉table选中对象
        selSearch: 0,
        searchCon: [],
        them_tran: {'zhfy': dic_transform.data.sfzhfy_tran, 'nbtclb': dic_transform.data.nh_tran},
        them: {
            '类型': 'zhfy',
            '费用名称': 'mxfymc',
            '拼音代码': 'pydm',
            '费用单价': 'fydj',
            '优惠比例': 'yhbl',
            '规格': 'fygg',
            '统筹类别': 'tclbmc',
            '农合报销': 'nbtclb',
            '医保国家码': 'ybgjm',
        },
        csqxContent: {
            N03001200155:'0',
            N05001200272:{},
        },
        yqbm: null,
        sfxxjzyy:false,
        ishff: '0',
    },
    //切换
    updated: function () {
        changeWin();
    },
    created: function () {
        this.sfjz();
    },
    computed: {
        //公用实时显示处方总金额
        // pubgetCfzje: function () {
        //     var sum = this.jcxmMxJson.reduce(function (total, num) {
        //         return  total + num.fyje
        //     }, 0)
        //     return sum.toFixed(2);
        //
        //     // var cfje = 0;
        //     // for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
        //     //     cfje += zcy.jcxmMxJson[i].fyje;
        //     // }
        //     // Vue.set(brjzFoot.msgContent, 'fyhj', cfje);     //给总量赋值
        // },
        money: function () {
            var reducers = {
                totalInEuros: function (state, item) {
                    return state.fyhj += item.fyje == undefined ? 0 : item.fyje;
                },
            };
            var manageReducers = function (reducers) {
                return function (state, item) {
                    return Object.keys(reducers).reduce(function (nextState, key) {
                        reducers[key](state, item);
                        return state;
                    }, {})
                }
            }
            var bigTotalPriceReducer = manageReducers(reducers);
            this.jcxmMxJson.reduce(bigTotalPriceReducer, brjzFoot.msgContent = {
                fyhj: 0,
                cfh: brjzFoot.msgContent.cfh,
                cfysxm: brjzFoot.msgContent.cfysxm,
            });
        }
    },
    filters: {
        zhfybm: function (index, list) {
            var className = ['tz-start', 'tz-center', 'tz-stop', 'tz-single']
            zhfybm = list[index].zhfybm,
                length = list.length;
            if (zhfybm && length > 1) {
                if (index === 0) { // 处理第一个
                    if (zhfybm === list[index + 1].zhfybm) {
                        return className[0];
                    }else {
                        return className[3];
                    }
                } else if (index === length - 1) {// 处理最后一个
                    if (zhfybm === list[index - 1].zhfybm) {
                        return className[2];
                    }else {
                        return className[3];
                    }
                } else { // 处理中间的
                    if (zhfybm !== list[index - 1].zhfybm && zhfybm === list[index + 1].zhfybm) {
                        return className[0];
                    } else if (zhfybm === list[index - 1].zhfybm && zhfybm === list[index + 1].zhfybm) {
                        return className[1];
                    } else if (zhfybm === list[index - 1].zhfybm && zhfybm !== list[index + 1].zhfybm) {
                        return className[2]
                    } else if (zhfybm !== list[index - 1].zhfybm && zhfybm !== list[index + 1].zhfybm) {
                        return className[3];
                    }
                }
            }
        }
    },
    methods: {
        sfjz:function(){
            if(userNameBg.Brxx_List.ghksmc.indexOf('急诊') != -1){
                this.sfxxjzyy = true;
            }else{
                this.sfxxjzyy = false;
            }
        },
        showJzyyDate: function (index, event) {
            var elm = elm = 'jzyyrq' + index;

            Mask.newMask(this.MaskOptions(elm));

            this._laydate = laydate.render({
                elem: '.'+elm
                , show: true //直接显示
                , type: 'datetime'

                , theme: '#1ab394',
                done: function (value, data) {
                    zcy.jcxmMxJson[index].jzyyrq = value;
                    zcy.$forceUpdate();



                }
            })
        },
        setLczd: function () {
            var str_param = {
                ghxh: userNameBg.Brxx_List.ghxh,
                brid: userNameBg.Brxx_List.brid
            };

            let that = this;

            that.$http.get('/actionDispatcher.do', {
                params: {
                    reqUrl: 'New1GhglGhywBrgh',
                    types: 'queryOne',
                    parm: JSON.stringify(str_param)
                }
            }).then(function (json) {
                if (json.body.a == '0' && json.body.d.list) {
                    let zdxx = json.body.d.list[0].ghxx;
                    zdxx.fjzd=chose.fjzdlist;
                    userNameBg.Brxx_List.qtzdbm = zdxx.qtzdbm;
                    userNameBg.Brxx_List.qtzdbm1 = zdxx.qtzdbm1;
                    userNameBg.Brxx_List.qtzdbm2 = zdxx.qtzdbm2;
                    userNameBg.Brxx_List.qtzdbm3 = zdxx.qtzdbm3;
                    userNameBg.Brxx_List.qtzdbm4 = zdxx.qtzdbm4;
                    userNameBg.Brxx_List.qtzdmc = zdxx.qtzdmc;
                    userNameBg.Brxx_List.qtzdmc1 = zdxx.qtzdmc1;
                    userNameBg.Brxx_List.qtzdmc2 = zdxx.qtzdmc2;
                    userNameBg.Brxx_List.qtzdmc3 = zdxx.qtzdmc3;
                    userNameBg.Brxx_List.qtzdmc4 = zdxx.qtzdmc4;
                                        var zd ='';
                    (userNameBg.Brxx_List.zdsm1 || '')
                    if(userNameBg.Brxx_List.zdsm1){
                        zd+=userNameBg.Brxx_List.zdsm1
                    }
                    if(userNameBg.Brxx_List.jbbm){
                        zd+='('+userNameBg.Brxx_List.jbbm+')'+userNameBg.Brxx_List.jbmc
                    }
                    if(userNameBg.Brxx_List.zdsm2){
                        zd+= userNameBg.Brxx_List.zdsm2
                    }
                    if(userNameBg.Brxx_List.qtzdbm){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm+')'+( userNameBg.Brxx_List.qtzdmc || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm1){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm1+')'+( userNameBg.Brxx_List.qtzdmc1 || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm2){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm2+')'+( userNameBg.Brxx_List.qtzdmc2 || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm3){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm3+')'+( userNameBg.Brxx_List.qtzdmc3 || '')
                    }
                    if(userNameBg.Brxx_List.qtzdbm4){
                        zd+=',('+userNameBg.Brxx_List.qtzdbm4+')'+( userNameBg.Brxx_List.qtzdmc4 || '')
                    }
                    if(userNameBg.Brxx_List.fjzd){
                        let fjzd = JSON.parse(userNameBg.Brxx_List.fjzd)
                        for (let i = 0; i < fjzd.length; i++) {
                            zd+=',('+fjzd[i].jbmb+')'+( fjzd[i].jbmc || '')
                        }

                    }
                    chose.lczd = zd.replace(/,null/g,"");

                }
            })


        },
        getIfShow:function(sftf){
            if(sftf == '1'){
                return false;
            }
            return true;
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N030012001',
                "ksbm": userNameBg.Brxx_List.ghks
            };

            this.$http.get("/actionDispatcher.do", {
                params: {
                    reqUrl: 'CsqxAction',
                    types: 'csqx',
                    parm: JSON.stringify(parm)
                }
            }).then(function (json, status, xhr) {
                if (json.body.a == 0 && json.body.d) {
                    var csList = json.body.d;
                    for (var i = 0; i < csList.length; i++) {
                        var csjson = csList[i];
                        switch (csjson.csqxbm) {
                            case "N03001200122":
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200122 = csjson.csz;
                                }
                                break;
                            case "N03001200130":
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200130 = csjson.csz;
                                }
                                break;
                            case "N03001200139":
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200139 = csjson.csz;
                                }
                                break;
                            case "N03001200140":
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200140 = csjson.csz;
                                    jcjyfy.N03001200140 = csjson.csz;
                                }
                                break;
                            case "N03001200142": //门诊处方打印是否预览 0-否 1-是
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200142 = csjson.csz;
                                }
                                break;
                            case "N03001200143":
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200143 = csjson.csz;
                                    brjzFoot.N03001200143 = csjson.csz;
                                }
                                break;
                            case "N03001200145"://贵州医药监管路径(门诊检验监管)
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200145 = csjson.csz;

                                }
                                break;
                            case "N05001200246"://贵州医药监管路径(门诊检查监管)
                                if (csjson.csz) {
                                    zcy.csqxContent.N05001200246 = csjson.csz;
                                }
                                break;
                            case "N03001200149":// 检验是否附加抽血费
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200149 = csjson.csz;
                                }
                                break;
                            case "N03001200155":// 默认科室 1 当前科室 0默认科室
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200155 = csjson.csz;
                                }
                                break;
                            case "N03001200157":// 是否默认保存打印
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200157 = csjson.csz;
                                }
                                break;
                             case "N03001200160":// '是否保存采血选择
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200160 = csjson.csz;
                                }
                                break;
                             case "N03001200162":// '门诊急诊模板默认选中急诊标志
                            	 if (csjson.csz) {
                            		 zcy.csqxContent.N03001200162 = csjson.csz;
                            	 }
                            	 break;
                            case "N03001200164":// '翼展PACS地址
                                if (csjson.csz) {
                                    zcy.csqxContent.N03001200164 = csjson.csz;
                                    brjzFoot.N03001200164 = csjson.csz;
                                }
                                break;
                            case "N05001200254":// 非药品处方描述保存是否验证 '0-否，1-是'
                                if (csjson.csz) {
                                    zcy.csqxContent.N05001200254 = csjson.csz;
                                    brjzFoot.N05001200254 = csjson.csz;
                                }
                                break;
                            case "N05001200256":// 非药品处方模板是否区分门诊和住院
                                if (csjson.csz) {
                                    zcy.csqxContent.N05001200256 = csjson.csz;
                                    brjzFoot.N05001200256 = csjson.csz;
                                }
                                break;
                            case "N05001200257":// 急诊下拉框显示条件：维护不显示的模板编码
                            	if (csjson.csz) {
                            		zcy.csqxContent.N05001200257 = csjson.csz;
                                    jcjyfy.N05001200257 = csjson.csz;
                            	}
                            	break;
                            case "N05001200258":
                            	if (csjson.csz) {
                            		zcy.csqxContent.N05001200258 = csjson.csz;
                            	}
                            	break;
                            case "N05001200259": //检验报告查看方式 0-批量查看 1-单个查看
                                if (csjson.csz) {
                                    zcy.csqxContent.N05001200259 = csjson.csz;
                                }
                                break;
                            case "N05001200262": // 费用项目按科室院区区分院区编码 0-不区分 1-区分
                                if (csjson.csz && csjson.csz == '1') {
                                    zcy.yqbm = zcy.listGetName(treatment.mzksList, userNameBg.Brxx_List.ghks, 'ksbm', 'yqbm');
                                    console.log("院区编码：" + zcy.yqbm);
                                }
                                break;
                            case "N05001200272": //检查检验费别职工编码+优惠比例
                                if (csjson.csz) {
                                    zcy.csqxContent.N05001200272 =eval("("+csjson.csz+")");
                                }
                                break;
                        }
                    }
                } else {
                    malert('参数权限获取失败' + json.body.c, 'top', 'defeadted')
                }
            });
        },
        tabShowActive: function () {
            if (this.jcxm_List.length >= 6) {
                zcy.tabShow = true;
            } else {
                zcy.tabShow = false;
            }
        },
        move: function (direction) {
            if (direction == 'left') {
                if ($('#myscrollbox  ul')[0].scrollLeft >= this.leftNum) {
                    this.leftNum += 100
                    $('#myscrollbox  ul').scrollLeft(+this.leftNum);
                }
            } else if (direction == 'right') {
                if ($('#myscrollbox  ul')[0].scrollLeft <= this.leftNum) {
                    this.leftNum = this.leftNum - 100
                    $('#myscrollbox  ul').scrollLeft(this.leftNum);
                }
            }
        },
        tabBg: function (index) {
            this.num = index;
        },

        hover: function (index, event) {
            if (event.shiftKey) {
                this.numClass = null;
                zcy.checkAll = false;
                Vue.set(zcy.isCheck, index, true)
            } else {
                zcy.checkAll = false
                zcy.isCheck = [];
                this.numClass = index;
            }
        },

        //弹框检查检验目的
        fybzClick: function ($index) {
            zcy.ffylb = zcy.jcxmMxJson[$index].yzfl;
            zcy.dqIndex = $index;
            // this.addShow = true;
            //检查或检验
            if (zcy.ffylb != null) {
                if (zcy.ffylb.indexOf("30") >= 0 || zcy.ffylb == '4') {
                    zcy.fybzShow[$index] = true;
                    var jcxmmx = zcy.jcxmMxJson[$index];
                    if (jcxmmx.lczd == null || jcxmmx.lczd == undefined || jcxmmx.lczd == "") {
                        jcxmmx.lczd = chose.lczd;
                    }
                    if (zcy.ffylb.indexOf("30") >= 0) { //检查
                        //获取当前对象的值
                        //if( zcy.CfxxJson.yzhm !=undefined && zcy.CfxxJson.yzhm!=null && zcy.CfxxJson.yzhm!=''){
                        zcy.objData = [
                            {type: '0', text: '临床诊断', t: jcxmmx.lczd},//input
                            {type: '0', text: '检查部位', t: jcxmmx.jcbw},//select
                            {type: '0', text: '临床症状', t: jcxmmx.lczz},//input
                            {type: '2', text: '检查描述', t: jcxmmx.jcms},//textarea
                        ]
                        /* }else{
                             zcy.objData= [
                                 {type:'0',text:'临床诊断',t:chose.lczd},//input
                                 {type:'0',text:'检查部位',t: ''},//select
                                 {type:'0',text:'临床症状',t:''},//input
                                 {type:'2',text:'检查描述',t:''},//textarea
                             ]
                         }*/
                    } else { //检验
                        //获取当前对象的值
                        //if( zcy.CfxxJson.yzhm !=undefined && zcy.CfxxJson.yzhm!=null && zcy.CfxxJson.yzhm!=''){
                        zcy.objData = [
                            {type: '0', text: '临床诊断', t: jcxmmx.lczd},//input
                            {type: '0', text: '检验目的', t: jcxmmx.jymd},//select
                            {type: '0', text: '检验标本', t: jcxmmx.jybb},//input
                            {type: '2', text: '标本说明', t: jcxmmx.bbsm},//textarea
                        ]
                        /*}else{
                            zcy.objData= [
                                {type:'0',text:'临床诊断',t:chose.lczd},//input
                                {type:'0',text:'检验目的',t: ''},//select
                                {type:'0',text:'检验标本',t:''},//input
                                {type:'2',text:'标本说明',t:''},//textarea
                            ]
                        }*/
                    }
                } else {
                    // zcy.fybzShow[$index] = false;
                }
            } else {
                // zcy.fybzShow[$index] = false;
            }
            this.$forceUpdate()
        },

        //检查检验输入之后的回调
        savego: function (list) {
            // $(".fytyp").hide();
            console.log(list);
            //保存
            if (zcy.ffylb.indexOf("30") >= 0 ) { //检查
                if (zcy.csqxContent.N05001200254 !='1'){
                    if (list[0].t == null || list[0].t == undefined || list[0].t == "") {
                        malert("临床诊断不能为空哟!", 'top', 'defeadted');
                        return;
                    }

                    if (list[3].t == null || list[3].t == undefined || list[3].t == "") {
                        malert("检查描述不能为空哟!", 'top', 'defeadted');
                        return;
                    }

                    if (list[2].t == null || list[2].t == undefined || list[2].t == "") {
                        malert("临床症状不能为空哟!", 'top', 'defeadted');
                        return;
                    }

                    if (list[1].t == null || list[1].t == undefined || list[1].t == "") {
                        malert("检查部位不能为空哟!", 'top', 'defeadted');
                        return;
                    }
                }

                zcy.jcxmMxJson[zcy.dqIndex].lczd = list[0].t; //临床诊断
                zcy.jcxmMxJson[zcy.dqIndex].jcms = list[3].t; //检查描述
                zcy.jcxmMxJson[zcy.dqIndex].lczz = list[2].t; //临床症状
                zcy.jcxmMxJson[zcy.dqIndex].jcbw = list[1].t; //检查部位
                if (zcy.jcxmMxJson[pop.jcxmLength - 1] !=undefined && zcy.jcxmMxJson[pop.jcxmLength - 1].zhfybh != undefined && zcy.jcxmMxJson[pop.jcxmLength - 1].zhfybh != null) {//组合费用
                    for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
                        if (zcy.jcxmMxJson[pop.jcxmLength - 1].zhfybh == zcy.jcxmMxJson[i].zhfybh) {
                            zcy.jcxmMxJson[i].lczd = list[0].t; //临床诊断
                            zcy.jcxmMxJson[i].jcms = list[3].t; //检查描述
                            zcy.jcxmMxJson[i].lczz = list[2].t; //临床症状
                            zcy.jcxmMxJson[i].jcbw = list[1].t; //检查部位
                        }
                    }
                }

            } else if (zcy.ffylb == '4') { //检验
                if(zcy.csqxContent.N05001200254 !='1'){
                    if (list[0].t == null || list[0].t == undefined || list[0].t == "" ) {
                        malert("临床诊断不能为空哟!", 'top', 'defeadted');
                        return;
                    }

                    if (list[2].t == null || list[2].t == undefined || list[2].t == "") {
                        malert("检验标本不能为空哟!", 'top', 'defeadted');
                        return;
                    }

                    if (list[3].t == null || list[3].t == undefined || list[3].t == "") {
                        malert("标本说明不能为空哟!", 'top', 'defeadted');
                        return;
                    }

                    if (list[1].t == null || list[1].t == undefined || list[1].t == "") {
                        malert("检验目的不能为空哟!", 'top', 'defeadted');
                        return;
                    }
                }

                Vue.set(zcy.jcxmMxJson[zcy.dqIndex], 'lczd', list[0].t);
                //zcy.jcxmMxJson[zcy.dqIndex].lczd = list[0].t; //临床诊断
                zcy.jcxmMxJson[zcy.dqIndex].jybb = list[2].t; //检验标本
                zcy.jcxmMxJson[zcy.dqIndex].bbsm = list[3].t; //标本说明
                zcy.jcxmMxJson[zcy.dqIndex].jymd = list[1].t; //检验目的
                if (zcy.jcxmMxJson[zcy.dqIndex].zhfybh != undefined && zcy.jcxmMxJson[zcy.dqIndex].zhfybh != null) {//组合费用
                    for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
                        if (zcy.jcxmMxJson[zcy.dqIndex].zhfybh == zcy.jcxmMxJson[i].zhfybh) {
                            zcy.jcxmMxJson[i].lczd = list[0].t; //临床诊断
                            zcy.jcxmMxJson[i].jybb = list[2].t; //检验标本
                            zcy.jcxmMxJson[i].bbsm = list[3].t; //标本说明
                            zcy.jcxmMxJson[i].jymd = list[1].t; //检验目的
                        }
                    }
                }
            }
            zcy.fybzShow[zcy.dqIndex] = false
            malert("保存成功!")
            // $('.fytyp').hide();
            $("#bzsm" + zcy.dqIndex).focus();
        },

        //用药方法改变事件
        resultChange_zxks: function (val) {
            console.log(11)
            var index = val[2][1];
            zcy.ffylb = zcy.jcxmMxJson[index].yzfl;
            Vue.set(zcy.jcxmMxJson[index], 'zxks', val[0]);
            Vue.set(zcy.jcxmMxJson[index], 'zxksmc', val[4]);
            if (val[1] != null) {
                if (zcy.ffylb != null) {
                    if (zcy.ffylb.indexOf("30") >= 0 || zcy.ffylb == '4') {
                        this.nextFocus(val[1]);
                        this.fybzClick(index);
                    } else {
                        this.nextFocus(val[1]);
                        // $("#bzsm" + index).focus();
                    }
                } else {
                    this.nextFocus(val[1]);
                    // $("#bzsm" + index).focus();
                }
            }
        },

        commonResultChange: function (val) {
            var type = val[2][2];
            switch (type) {
                case "sfjz"://是否急诊
                    var index = val[2][1];
                    Vue.set(zcy.jcxmMxJson[index], 'sfjz', val[0]);
                    $("#mxfyxmmc" + index).focus();
                    for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
                        if (zcy.jcxmMxJson[index].zhfybh == zcy.jcxmMxJson[i].zhfybh) {
                            Vue.set(zcy.jcxmMxJson[i], 'sfjz', val[0]);
                        }
                    }
                    break;
            }
        },


        //值改变事件
        //药品
        Wf_change: function (add, index, type, val) {
            dqindex = index;
            this.changeType=true;
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            //            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var _searchEvent = $("#mxfyxmmc" + index + " + div");
            this.jcxmMxJson[index]['mxfyxmmc'] = val;
            this.jcxmMxJson[index]['mxfyxmbm'] = '';
            this.page.parm = trimStr(val);
            var json = {
                fylx: '1',
                ypfy: '0',
                yqbm: zcy.yqbm
            };
            // @yqq 根据权限参数进行条件筛选
            if (zcy.csqxContent.N03001200122 == '1') {
                json.zhfy = '1';
            }
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryJzxm&dg=' + JSON.stringify(this.page) + '&json=' + JSON.stringify(json), function (data) {
                if (add) {//不是第一页则需要追加
                    for (var i = 0; i < data.d.list.length; i++) {
                        zcy.searchCon.push(data.d.list[i]);
                    }
                } else {
                    zcy.searchCon = data.d.list;
                }
                if (zcy.csqxContent.N05001200258 == 1) {
                    for (var j = zcy.searchCon.length - 1; j >= 0; j--) {
                        if (zcy.searchCon[j].ffylb == '4') {
                        	zcy.searchCon.splice(j, 1);
                        }
                    }
                }

                zcy.page.total = data.d.total;
                zcy.selSearch = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                }
            });
        },


        //键盘事件
        changeDown: function (index, event, type, searchCon) {
            this.keyCodeFunction(event, 'popContent', searchCon);
            //赋值
            if (event.code == 'Enter' || event.keyCode == 13 || event.code == 'NumpadEnter') {
                if (!this.jcxmMxJson[index]['mxfyxmbm'] && zcy.popContent) {
                    Vue.set(zcy.popContent, 'sfjz', zcy.jcxmMxJson[index].sfjz);
                    zcy.addXm(zcy.popContent,index);
                    this.nextFocus(event);
                    $(".selectGroup").hide();
                }else if (this.jcxmMxJson[index]['mxfyxmbm']){
                    this.nextFocus(event);
                }
            }


        },
        //鼠标西藥双击选中事件
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.Wf_change(true, dqindex, 'mxfyxmmc', this.jcxmMxJson[dqindex]['mxfyxmmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                zcy.addXm(item,dqindex);
                this.selSearch = -1;
                this.nextFocus(event);
                $(".selectGroup").hide();
            }
        },
        setFbbm:function (mzfyModel){
			            if(JSON.stringify(zcy.csqxContent.N05001200272 ) != '{}'){
                var fylbmc=mzfyModel.ffylb;
                var fbbm=zcy.csqxContent.N05001200272[userNameBg.Brxx_List.fbbm];
                if(fbbm !='-37'){
                    if(fbbm && fylbmc && (fylbmc.indexOf('3') != -1 || fylbmc.indexOf('4')) != -1 ){
						if(mzfyModel.fylb != '228'){
							mzfyModel.yhje=parseFloat(zcy.fDec(parseFloat(mzfyModel.fydj) *(parseFloat(fbbm)/100),2));
							mzfyModel.fydj=parseFloat(zcy.fDec(parseFloat(mzfyModel.fydj1)-parseFloat(mzfyModel.yhje),2));
							mzfyModel.fyje=parseFloat(zcy.fDec(parseFloat(mzfyModel.fydj1)-parseFloat(mzfyModel.yhje),2));
						}
                    }
                }else {
                        mzfyModel.yhje=parseFloat(zcy.fDec(parseFloat(mzfyModel.fydj) *(parseFloat(mzfyModel.yhbl)/100),2));
                        mzfyModel.fydj=parseFloat(zcy.fDec(parseFloat(mzfyModel.fydj1)-parseFloat(mzfyModel.yhje),2));
                        mzfyModel.fyje=parseFloat(zcy.fDec(parseFloat(mzfyModel.fydj1)-parseFloat(mzfyModel.yhje),2));
                }

                return  mzfyModel
            }
        },
        //处理费用项目问题
        addXm: function (jcjyContent,index) {

            zcy.ffylb = null;
            var mxfybm = jcjyContent.mxfybm;
            var mxfymc = jcjyContent.mxfymc;
            var ffylb = jcjyContent.ffylb;
            if (jcjyContent.zhfy == '1') {//组合费用需要请求后台查询组合费用明细
                var jsons = {
                    mxfybm: mxfybm
                };
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryZhfy' +
                    '&json=' + JSON.stringify(jsons),
                    function (data) {
                        if (data.a == 0 && data.d.list.length > 0) {
                            zcy.jcxmMxJson.splice(zcy.jcxmMxJson.length - 1, 1);
                            for (var j = 0; j < data.d.list.length; j++) {
                                //进行赋值操作
                                var mzfyModel = {};
                                mzfyModel.fysl = data.d.list[j].sl;
                                mzfyModel.fyje = data.d.list[j].fydj * data.d.list[j].sl;
                                mzfyModel.zhfybh = mxfybm;
                                mzfyModel.zhfymc = mxfymc;
                                mzfyModel.disabled = true;
                                mzfyModel.mxfyxmbm = data.d.list[j].mxfybm;
                                mzfyModel.mxfyxmmc = data.d.list[j].mxfymc;
                                mzfyModel.fylb = data.d.list[j].lbbm;
                                mzfyModel.ffylb = data.d.list[j].ffylb;
                                mzfyModel.fylbmc = data.d.list[j].fylbmc;
                                mzfyModel.fydj = data.d.list[j].fydj;
                                mzfyModel.fydj1 = data.d.list[j].fydj;
                                mzfyModel.yhje = 0.00;
                                mzfyModel.yhbl = data.d.list[j].yhbl;
                            // && (data.d.list[j].ffylb != '4' && data.d.list[j].ffylb.indexOf())
                                if(zcy.csqxContent.N03001200155 =='1' && (jcjyContent.ffylb && jcjyContent.ffylb != '4' && jcjyContent.ffylb.indexOf('3')==-1)){
                                    mzfyModel.zxks = userNameBg.Brxx_List.ghks;
                                    mzfyModel.zxksmc = userNameBg.Brxx_List.ghksmc;
                                }else{
                                    mzfyModel.zxks = data.d.list[j].hsks;
                                    mzfyModel.zxksmc = data.d.list[j].zxksmc;
                                }
                                mzfyModel=Object.assign(mzfyModel,zcy.setFbbm(mzfyModel))
                                mzfyModel.zhfybz = '1';
                                mzfyModel.zhfybm = data.d.list[j].zhfybm;
                                mzfyModel.fygg = data.d.list[j].fygg;
                                mzfyModel.yzfl = data.d.list[j].ffylb;
                                mzfyModel.sfgd = data.d.list[j].sfgd; //此字段针对是否允许改变费用单价
                                mzfyModel.sfjz = jcjyContent.sfjz ? jcjyContent.sfjz : "0"; //是否急诊
                                if (zcy.jcxmMxJson != null && zcy.jcxmMxJson != undefined && zcy.jcxmMxJson.length > 0) {
                                    for (var k = 0; k < zcy.jcxmMxJson.length; k++) {
                                        if (mzfyModel.mxfyxmbm == zcy.jcxmMxJson[k].mxfyxmbm) {
                                            var r = mconfirm("第【" + (k + 1) + "】行费用【" + zcy.jcxmMxJson[k].mxfyxmmc + "】已经存在，确定需要添加吗？");
                                            if (r == false) {
                                                return;
                                            }
                                        }
                                    }
                                }
                                if (mzfyModel.zxks != null && mzfyModel.zxks != '') {
                                    mzfyModel.zxksSfgd = '1';
                                } else {
                                    mzfyModel.zxksSfgd = '0';
                                    mzfyModel.zxks = ksbm?ksbm:userNameBg.Brxx_List.ghks;
                                    mzfyModel.zxksmc = zcy.listGetName(zcy.zxksList, ksbm?ksbm:userNameBg.Brxx_List.ghks, 'ksbm', 'ksmc');
                                }
                                zcy.jcxmMxJson.push(mzfyModel);
                            }
                            // zcy.pubgetCfzje(); //计算费用总额
                            //处理当前添加费用关于于检查检验的操作
                            if (ffylb != null) {
                                if (ffylb.indexOf("30") >= 0 || ffylb == '4') {
                                    zcy.ffylb = ffylb;
                                }
                            }
                            zcy.jcxmMxJson.push({});
                            zcy.$nextTick(function () {
                                $("#mxfyxmmc" + (zcy.jcxmMxJson.length - 1)).focus()
                            })
                        } else {
                            malert("查询失败：" + data.c, 'top', 'defeadted');
                            return;
                        }
                    });
            } else {//不是组合费用
                // zcy.jcxmMxJson.splice(zcy.jcxmMxJson.length - 1, 1);
                var mzfyModel = {};
                mzfyModel.fysl = 1;
                mzfyModel.fyje = jcjyContent.fydj;
                mzfyModel.fygg = jcjyContent.fygg;
                mzfyModel.mxfyxmbm = jcjyContent.mxfybm;
                mzfyModel.mxfyxmmc = jcjyContent.mxfymc;
                mzfyModel.fylb = jcjyContent.lbbm;
                mzfyModel.fylbmc = jcjyContent.fylbmc;
                mzfyModel.fydj = jcjyContent.fydj;
                mzfyModel.fydj1 = jcjyContent.fydj;
                mzfyModel.ffylb = jcjyContent.ffylb;
                mzfyModel.yhje = 0.00;
                mzfyModel.yhbl = jcjyContent.yhbl;
                mzfyModel=Object.assign(mzfyModel,zcy.setFbbm(mzfyModel))
                if(zcy.csqxContent.N03001200155 =='1' && (jcjyContent.ffylb && jcjyContent.ffylb != '4' && jcjyContent.ffylb.indexOf('3')==-1)){
                    mzfyModel.zxks = userNameBg.Brxx_List.ghks;
                    mzfyModel.zxksmc = userNameBg.Brxx_List.ghksmc;
                }else{
                    mzfyModel.zxks = jcjyContent.hsks;
                    mzfyModel.zxksmc = jcjyContent.zxksmc;
                }
                mzfyModel.yzfl = jcjyContent.ffylb;
                mzfyModel.sfgd = jcjyContent.sfgd;
                mzfyModel.sfjz = jcjyContent.sfjz ? jcjyContent.sfjz : "0"; //是否急诊
                if (zcy.jcxmMxJson != null && zcy.jcxmMxJson != undefined && zcy.jcxmMxJson.length > 0) {
                    for (var k = 0; k < zcy.jcxmMxJson.length; k++) {
                        if (mzfyModel.mxfyxmbm == zcy.jcxmMxJson[k].mxfyxmbm) {
                            var r = mconfirm("第【" + (k + 1) + "】行费用【" + zcy.jcxmMxJson[k].mxfyxmmc + "】已经存在，确定需要添加吗？");
                            if (r == false) {
                                return;
                            }
                        }
                    }
                }
                if (mzfyModel.zxks != null && mzfyModel.zxks != '') {
                    mzfyModel.zxksSfgd = '1';
                } else {
                        mzfyModel.zxksSfgd = '0';
                        mzfyModel.zxks = ksbm?ksbm:userNameBg.Brxx_List.ghks;
                        mzfyModel.zxksmc = zcy.listGetName(zcy.zxksList, ksbm?ksbm:userNameBg.Brxx_List.ghks, 'ksbm', 'ksmc');
                }
                Vue.set(zcy.jcxmMxJson,index,mzfyModel)
                // zcy.jcxmMxJson.push({});
                // zcy.jcxmMxJson.push(mzfyModel);
                // zcy.pubgetCfzje(); //计算费用总额
                //处理当前添加费用关于于检查检验的操作
                if (ffylb != null) {
                    if (ffylb.indexOf("30") >= 0 || ffylb == '4') {
                        zcy.ffylb = ffylb;
                    }
                }
                //zcy.jcxmMxJson.push({});
                /* var index = zcy.jcxmMxJson.length - 1;
                var id = "mxfyxmmc" + index;
                setTimeout(function () {
                    document.getElementById(id).focus();
                }, 500);*/
            }
            zcy.selSearch = -1;
            zcy.popContent=undefined;
            $(".selectGroup").hide();
        },
        //备注说明回车事件
        hcpd: function (event, index) {
            if (index == zcy.jcxmMxJson.length - 1) {
                this.addYh();
            } else {
                $("#mxfyxmmc" + (index + 1)).focus();
            }
        },

        //添加处方
        addCf: function () {
            //先将编辑处方号清空
            if (brjzFoot.editYzhm != '') {
                brjzFoot.editYzhm = ''
            }

            zcy.addShow[this.num] = true;
            brjzFoot.editTitle = '保存';
            $("input[name='text']").each(function () {
                $(this).removeClass('input-border');
                $(this).attr('disabled', false)
            })
            //判断是否存在没有保存的处方信息
            if (zcy.jcxm_List.length > 0) {
                for (var i = 0; i < zcy.jcxm_List.length; i++) {
                    if (zcy.jcxm_List[i].yzhm == undefined || zcy.jcxm_List[i].yzhm == null || zcy.jcxm_List[i].yzhm == "") {
                        malert('请先保存新处方', 'top', 'defeadted');
                        return false;
                    }
                }
            }
            //进行一系列清空
            zcy.CfxxJson = {};  //清空当前处方信息
            zcy.jcxmMxJson = [];    //清空配方列表内容
            brjzFoot.msgContent = {}; //清空底部金额
            chose.myzdlist = {
                zd1:null,
                zd2:null,
                zd3:null,
                zd4:null,
                zd5:null,
                zd6:null,
            }
            chose.popContent = {
                mtbzmc:'',
                mtbzbm:'',

            }
            chose.iszdchecked =[]
            //获取疾病信息
            if (userNameBg.Brxx_List.jbmc) {
                // 将诊断描述和其他诊断全部显示在临床诊断中
                this.setLczd()
            } else {
                //去传输过来的疾病信息
                if (zcy.cfList.length > 0) {
                    chose.lczd = zcy.cfList[zcy.cfList.length - 1].lczd;
                }
            }
            zcy.CfxxJson = {cflxbm: '', cflxmc: '检查项目', cfh: ''};
            zcy.CfxxJson.index = zcy.jcxm_List.length;  //当前处方行数
            zcy.jcxm_List.push({cflxbm: '', cflxmc: '检查项目', cfh: ''});  //增加一个处方
            zcy.addYh();//调用新增一行药品
            event = $('#myscrollbox > ul');

            // $('#myscrollbox > ul >li').click(function () {
            //     $(this).addClass('active');
            //     $(this).parent().siblings().children().removeClass('active');
            // })
            setTimeout(function () {
                $("input[name='text']").each(function () {
                    $(this).removeClass('input-border');
                    $(this).attr('disabled', false)
                })
                zcy.num = zcy.jcxm_List.length - 1
                zcy.addShow[zcy.num] = true;
                zcy.tabShowActive()

            }, 50);

        },

        //新增一行
        addYh: function () {
            if (zcy.jcxmMxJson.length > 0) {
                if (zcy.jcxmMxJson[zcy.jcxmMxJson.length - 1].mxfyxmmc == undefined || zcy.jcxmMxJson[zcy.jcxmMxJson.length - 1].mxfyxmmc == '') {
                    malert("数据格式有问题，请正确输入医嘱名称", 'top', 'defeadted');
                    return false
                }
            }

            if (zcy.CfxxJson.yzhm == undefined || zcy.CfxxJson.yzhm == null || zcy.CfxxJson.yzhm == "") {
                //初始化配方
                zcy.jcxmMxJson.push({});
                var cou = zcy.jcxmMxJson.length - 1;
                setTimeout(function () {   //延时0.1秒执行
                    $("#mxfyxmmc" + cou).focus();
                    // var str=$('#myscrollbox > ul:last-child');
                    //     $(str).addClass('active');
                    //     $(str).siblings().removeClass('active');
                    zcy.$nextTick(function () {
                        zcy.num = zcy.jcxm_List.length - 1
                        zcy.tabShowActive()
                    })
                }, 100);
            } else {
                malert("该处方已保存不能进行配方的添加操作", 'top', 'defeadted');
            }

        },

        //弹框检查检验调用
        Cfdy: function () {
            brzcList.open();
            $('.side-form').css({'width': '805px'});
            brzcList.saveTitle = '确定'
            brzcList.title = '处方调用';
            brzcList.cfShow = true;
            brzcList.wShow = true;
            brzcList.ypShow = false;
            brzcList.hzShow = false;
            brzcList.lsShow = false;
            // brzcList.Wf_getJcxm();
            brzcList.cfList = []
            brzcList.getTemData();
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'hidden'})
            //此处判断是否有未保存的处方信息
            for (var i = 0; i < zcy.jcxm_List.length; i++) {
                if (zcy.jcxm_List[i].yzhm == null || zcy.jcxm_List[i].yzhm == "") {
                    zcy.jcxm_List.splice(i, 1);
                    zcy.jcxmMxJson = [];
                    // zcy.Wf_selectCFMX(zcy.cfList[zcy.cfList.length - 1].cfh, zcy.cfList.length - 1);
                }
            }
        },

        //历史处方
        Lscf: function () {
            brzcList.open();
            $('.side-form').css({'width': '805px'});
            console.log("历史处方");
            brzcList.saveTitle = '引用'
            brzcList.title = '历史处方 红色为作废处方';
            brzcList.cfShow = false;
            brzcList.wShow = true;

            brzcList.hzShow = false;
            brzcList.zdShow = false;
            brzcList.lsShow = true;

            brzcList.getLsTemData();
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'hidden'})
            // $('.blRight').hide();
            //此处判断是否有未保存的处方信息
            for (var i = 0; i < zcy.jcxm_List.length; i++) {
                if (zcy.jcxm_List[i].yzhm == null || zcy.jcxm_List[i].yzhm == "") {
                    zcy.jcxm_List.splice(i, 1);
                    zcy.jcxmMxJson = [];
                }
            }
        },

        nextAdd: function (event) {
            if (event.keyCode == 13) {
                var _input = $(".zcyItem input").not(":disabled,input[type=checkbox],input[type=date]");
                for (var i = 0; i < _input.length; i++) {
                    if (_input.eq(i)[0] == event.currentTarget) {
                        _input.eq(i + 1).focus();
                    }
                    if (this.zcyList.length - 1 == event.currentTarget.tabIndex) {
                        this.add()
                    }
                }
            }
        },

        //删除当前
        removeNow: function (num, fzh) {
            if (brjzFoot.msgContent.cfh) {
                malert("该处方已保存不能进行配方的删除操作", 'top', 'defeadted');
                return;
            }
            var msg = fzh ? "确定要删除多行药品吗？" : "确定要删除第【" + (num) + "】行，药品：【" + zcy.jcxmMxJson[num].mxfyxmmc + "】吗？"
            common.openConfirm(msg, sccf);

            function sccf() {
                if (fzh != '' && fzh != undefined) {
                    for (var i = zcy.jcxmMxJson.length - 1; i >= 0; i--) {
                        if (fzh == zcy.jcxmMxJson[i].zhfybm) {
                            zcy.jcxmMxJson.splice(i, 1);
                        }
                    }
                } else {
                    zcy.jcxmMxJson.splice(num, 1);
                    // zcy.pubgetCfzje();
                }
            }
        },


        //获取执行科室
        Wf_getHsksData: function () {
            var ks_dg = {page: 1, rows: 10000, sort: "", order: "asc", parm: ""};
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=ksbm&dg=" + JSON.stringify(ks_dg), function (json) {
                if (json.a == 0) {
                    jcjyfy.ksList = json.d.list;
                    zcy.zxksList = json.d.list;  //执行科室下拉窗口绑定数据
                } else {
                    malert("执行科室列表查询失败：" + json.c, 'top', 'defeadted');
                    return;
                }
            });
        },

        //检查项目：数量单价改变时
        Wf_fyjeChange: function (index) {
            var fydj = zcy.jcxmMxJson[index].fydj;  //单价
            var fysl = zcy.jcxmMxJson[index].fysl;  //数量
            if (fydj == null || fydj == undefined || fydj <= 0) {
                fydj = 0;
            }
            if (fysl == null || fysl == undefined || fysl <= 0) {
                fysl = 0;
            }
            var fyje = fydj * fysl;  //自动算费用金额
            Vue.set(zcy.jcxmMxJson[index], 'fyje', fyje);     //给费用金额赋值
            // zcy.pubgetCfzje();
        },
        //数量单价回车
        nextYhFocus: function (event, index) {
            console.log(12)
            if (event.keyCode == 13) {
                if (index == zcy.jcxmMxJson.length - 1) {
                    this.addYh();
                } else {
                    $("#mxfyxmmc" + (index + 1)).focus();
                }
            }
        },


        //根据挂号序号查询检查项目处方
        Wf_selectCFJcxm: function () {
            zcy.CfxxJson = {};
            zcy.jcxm_List = [];
            zcy.jcxmMxJson = [];
            brjzFoot.msgContent = {};
            var json = {
                ryghxh: userNameBg.Brxx_List.ghxh,
                sqys: userId
            };
            //后台查询数据
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectJcxmCf&parm=" + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        zcy.jcxm_List = data.d.list;  //检查项目处方目录
                        //给第一张处方赋值
                        var cfh = data.d.list[0].yzhm;
                        zcy.Wf_selectCFJcxmMx(cfh, zcy.num);             //查询处方明细
                    } else {
                        zcy.jcxmMxJson = []
                        zcy.addCf()
                    }
                } else {
                    malert("检查检验项目查询失败" + data.c, 'top', 'defeadted');
                }
            });
        },
uniq :function(arr,field) {
        var map = {};
            var res = [];
            for (var i = 0; i < arr.length; i++) {
                if (!map[arr[i][field]]) {
                    map[arr[i][field]]=1;
                    res.push(arr[i]);
                }
            }
             return res;
      },
        // 根据处方号查询检查项目明细列表（index代表查询第几个处方）
        Wf_selectCFJcxmMx: function (cfh, index) {
            this.num = index;
            if (cfh != undefined) {
                this.addShow[index] = false;
            }
            this.CfxxJson = {cflxmc: '检查项目'};
            brjzFoot.msgContent = {};
            this.CfxxJson.index = index;  // 设置当前处方行号
            if (cfh == null || cfh == "") {
                zcy.jcxmMxJson = [];                     // 如果处方号为空，就清空配方编辑区内容
                this.addYh();
                return false;
            }
            var json = {
                ryghxh: userNameBg.Brxx_List.ghxh,
                yzhm: cfh
            };

            // 请求后台
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectJcxmMx&parm=" + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
						let tplist = zcy.uniq(data.d.list,'fyjlid');
                        zcy.jcxmMxJson = tplist;   // 配方编辑区内容列表赋值
                        zcy.CfxxJson = JSON.parse(JSON.stringify(zcy.jcxm_List[index]));       // 当前处方明细信息
                        zcy.CfxxJson.lczd = zcy.jcxm_List[index].rymzzd;   // 当前处方明细信息
                        chose.lczd = zcy.jcxm_List[index].rymzzd;
                        zcy.ishff = data.d.list[0].ishff;
                        chose.myzdlist = {
                            zd1:null,
                            zd2:null,
                            zd3:null,
                            zd4:null,
                            zd5:null,
                            zd6:null,
                        }
                        chose.popContent = {
                            mtbzmc:'',
                            mtbzbm:'',

                        }
                        chose.iszdchecked =[]



                        if(zcy.jcxm_List[index].rymzzd && zcy.jcxm_List[index].rymzzd.indexOf(chose.Brxx_List.jbbm) != -1){
                            Vue.set(chose.iszdchecked,'90','true')
                        }
                        if(chose.Brxx_List.qtzdbm && zcy.jcxm_List[index].rymzzd.indexOf(chose.Brxx_List.qtzdbm) != -1){
                            Vue.set(chose.iszdchecked,'91','true')
                        }
                        if(chose.Brxx_List.qtzdbm1 && zcy.jcxm_List[index].rymzzd.indexOf(chose.Brxx_List.qtzdbm1) != -1){
                            Vue.set(chose.iszdchecked,'92','true')
                        }
                        if(chose.Brxx_List.qtzdbm2 && zcy.jcxm_List[index].rymzzd.indexOf(chose.Brxx_List.qtzdbm2) != -1){
                            Vue.set(chose.iszdchecked,'93','true')
                        }
                        if(chose.Brxx_List.qtzdbm3 &&zcy.jcxm_List[index].rymzzd.indexOf(chose.Brxx_List.qtzdbm3) != -1){
                            Vue.set(chose.iszdchecked,'94','true')
                        }
                        if(chose.Brxx_List.qtzdbm4 && zcy.jcxm_List[index].rymzzd.indexOf(chose.Brxx_List.qtzdbm4) != -1){
                            Vue.set(chose.iszdchecked,'95','true')
                        }

                        chose.popContent.mtbzbm = zcy.jcxm_List[index].mtbzbm;
                        chose.popContent.mtbzmc = zcy.jcxm_List[index].mtbzmc;

                        chose.fjzdlist = JSON.parse(userNameBg.Brxx_List.fjzd)
                        console.log(chose.fjzdlist)
                                                if(chose.fjzdlist){
                            for (let i = 0; i < chose.fjzdlist.length; i++) {
                                if(chose.fjzdlist[i].jbmb && zcy.jcxm_List[index].rymzzd.indexOf(chose.fjzdlist[i].jbmb) != -1){
                                    Vue.set(chose.iszdchecked,i+'','true')
                                }
                            }
                        }




                            chose.popContent.mtbzbm = zcy.jcxm_List[index].mtbzbm;
                            chose.popContent.mtbzmc = zcy.jcxm_List[index].mtbzmc;




                        zcy.CfxxJson.kfbz = zcy.jcxm_List[index].sfjs;     // 是否结算
                        zcy.CfxxJson.cflxmc = '检查项目';
                        zcy.CfxxJson.index = index;  // 设置当前处方行号
                        //对底部信息进行赋值

                        brjzFoot.$set(brjzFoot.msgContent, 'cfh', cfh);
                                                brjzFoot.$set(brjzFoot.msgContent, 'cfysxm', zcy.jcxmMxJson[0].sqysxm);
                        // brjzFoot.$set(brjzFoot.msgContent, 'fyhj', fyhj);

                        // 处理左上角的收费标志
                        if (zcy.CfxxJson.kfbz == "0") {

                        } else if (zcy.CfxxJson.kfbz == "1") {

                        }
                    }
                } else {
                    malert("检查项目明细查询失败", 'top', 'defeadted');
                }
            });
        },

    }
});
zcy.getCsqx();
//模板信息
var pop = new Vue({
    el: '#pop',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        popShow: false,
        bcShow: false,
        bcTitle: '',
        lczdPopContent: {}, //临床诊断对象
        ifClick: true,
        mbZhyzContent: {},  //另存模板对象
    },
    methods: {
        //保存
        saveMb: function () {
            if (!pop.mbZhyzContent.zhyzmc) {
                malert("请输入医嘱名称", 'top', 'defeadted');
                return;
            }

            pop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})

            if (!pop.ifClick) return;
            pop.ifClick = false;

            // zcy.jcxm_List zcy.num
            if (!zcy.CfxxJson.yzhm) {
                malert("当前无处方模板可以保存", 'top', 'defeadted');
                return;
            }
            pop.mbZhyzContent.yyks = userNameBg.Brxx_List.ghks;
            pop.mbZhyzContent.yyz = userId;
            pop.mbZhyzContent.insertbz = "1";

            // jcxm_List zcy.jcxm_List

            var pfxxs = [];
            for (var i = 0; i < zcy.jcxmMxJson.length; i++) {
                var jcxm = zcy.jcxmMxJson[i];
                console.log("配方信息： " + JSON.stringify(jcxm));
                var pfxx = {};
                // pfxx.yljgbm = '';
                // pfxx.zhyzbm = '';
                pfxx.mxzlxmbm = jcxm.mxfyxmbm;
                pfxx.sl = jcxm.fysl;
                pfxx.dj = jcxm.fydj;
                pfxx.zxks = jcxm.zxks;
                pfxx.zhfybm = jcxm.zhfybh;
                pfxxs.push(pfxx);
            }

            var parm = {
                list: [
                    {
                        zhyz: pop.mbZhyzContent,
                        yppfs: pfxxs
                    }
                ]
            };

            console.log("保存诊疗医嘱模板：" + JSON.stringify(parm));
            // pop.ifClick = true;
            this.$http.post('/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=insertCmb', JSON.stringify(parm)).then(function (data) {
                //注意此处如果有jq的代码必须要用tableInfo.jsonList而不是this.jsonList
                if (data.body.a == 0) {
                    malert("处方保存成功", 'top', 'success');
                    pop.ifClick = true;
                    // brzcList.getTemData();         //处方模板查询处方
                } else {
                    malert("处方保存失败：" + data.body.c, 'top', 'defeadted');
                    pop.ifClick = true;
                }
            });
        },

        //取消
        cancel: function () {
            pop.popShow = false;
            $('.hzzx-top').css({'z-index': '0'})
            $('body').css({'overflow': 'auto'})
            $(".blRight").css({'z-index': '88'})
        },
    },
})

//附加费用，费用项目
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 0,
        scrollLeft: 0,
        scollType: true,
        nums: 1,
        title: '',
        cfShow: false,//处方调用
        saveTitle: '',
        isChecked: [],
        checkAll: false,
        activeIndex1: undefined,
        hoverIndex1: undefined,
        ypShow: false,//药品选择
        tjShow: false,//添加附加费用
        wShow: false,//侧滑宽度
        hzShow: false,
        lsCfList: [], //历史处方处方集合
        lsCfMxList: [],  //历史处方处方明细集合
        lscfjs: "", //历史处方检索
        cfList: [], //处方模板处方集合
        cfMxList: [],//处方模板处方明细集合
        cfdyjs: "",  //处方调用检索
        jcxmJson: [], //检查项目列表展示
        cfmbIndex: '',//选中的处方模板index
        lscfIndex: '',//选中的历史处方index
        dg: {page: 1, rows: 20, sort: "", order: "asc", parm: ""},//分页信息
        selSearch: -1,
        ffylb: '', //处方类型
        beginrq: '',
        endrq: '',
        lscfcxqx: '1', //历史处方判断查询当前病人处方还是当前医生所有处方
        lscfTmp: {},
        cfyyly_tran:{
            '1':'科室',
            '2':'个人'
        },
        cfyylyidx:'2',
        cssfbh:false,
    },
    mounted: function () {
        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()), 'date') + ' 00:00:00';
        this.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    brzcList.beginrq = value ;
                } else {
                    panel.beginrq = '';
                }
                //获取一次列表
                brzcList.getLsTemData();
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                if (value != '') {
                    brzcList.endrq = value;
                } else {
                    panel.endrq = '';
                }
                //获取一次列表
                brzcList.getLsTemData();
            }
        });
    },

    methods: {
        hoverMouse1: function (type, index) {
            this.hoverIndex1 = type ? index : undefined;
        },
        activeMousel(index) {
            this.activeIndex1 = index
        },
        //关闭
        closes: function () {
            brzcList.nums = 1;
            this.isChecked = [];
            this.isCheckAll = false;
            $('.blRight').show();
            // $(".side-form").removeClass('side-form-bg')
            // $(".side-form").addClass('ng-hide');

        },
        open: function () {
            brzcList.nums = 0;
            // $(".side-form-bg").addClass('side-form-bg')
            // $(".side-form").removeClass('ng-hide');
        },

        getCxqxChange: function (val) {
            if (val) {
                brzcList.lscfcxqx = val[0];
            }
            this.getLsTemData();
        },

        //调用历史非药品处方接口
        getLsTemData: function () {
            brzcList.lsCfList = []; //历史处方处方集合
            brzcList.lsCfMxList = [];  //历史处方处方明细集合

            var ghxx = {
                cfbz: '0',
                parm: this.lscfjs,
                rows: '10',
                page: '1'
            }

            if (brzcList.lscfcxqx == '1') {
                ghxx.rows = 100;
                ghxx.rybrid = userNameBg.Brxx_List.brid;
            } else {
                ghxx.beginrq = this.beginrq;
                ghxx.endrq = this.endrq;
            }

            //查询非药品处方历史数据
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectJcxmCf&parm=" + JSON.stringify(ghxx), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        brzcList.lsCfList = data.d.list;  //检查项目处方目录
                    }
                } else {
                    malert("检查检验项目查询失败" + data.c, 'top', 'defeadted');
                }
            });
        },

        // 根据历史处方参数调用处方明细（单击）
        getLsTemMxData: function (index) {
            this.lscfIndex = index;
            this.activeIndex = index
            var cfh = this.lsCfList[index].yzhm;
            var ghxh = this.lsCfList[index].ryghxh;
            var json = {
                yzhm: cfh,
                ryghxh: ghxh
            };

            // 请求后台
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglBrjz&types=selectLsJcxmMx&parm=" + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (data.d.list != null && data.d.list.length > 0) {
                        // zcy.jcxmMxJson = data.d.list;   // 配方编辑区内容列表赋值
                        brzcList.lsCfMxList = data.d.list;   // 配方编辑区内容列表赋值
                    }
                } else {
                    malert("检查项目明细查询失败", 'top', 'defeadted');
                }
            });
        },

        // 双击历史处方添加药品
        addLsCfMb: function (index) {
            //全选
            this.isCheckAll = true;
            for (var i = 0; i < brzcList.lsCfMxList.length; i++) {
                Vue.set(brzcList.isChecked, i, true);
            }
            this.save('引用', index);
        },
        resultlyChange: function (val) {
            console.log(val[0])
            this.cfyylyidx = val[0]

            this.cssfbh = true;
            this.$forceUpdate()
            this.getTemData();
        },
        // 调用处方模板的API
        getTemData: function (event) {
            if (event) {
                brzcList.cfList = []
            }
            var parm = {
                rows: 20,
                sort: 'zhyzbm',
                order: 'desc',
                lx: '1',
                page: 1,
                yyz: '',
                yyks: userNameBg.Brxx_List.ghks,
                parm: this.cfdyjs,
                ypbz: '0'
            };

            if(brzcList.cfyylyidx =='1'){
                parm.yyz='';
            }else{
                parm.yyz=chose.userInfo.czybm;
            }

            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=queryCfmb&parm=" + JSON.stringify(parm), function (json) {
                if(json.a=='0'){
                    if (json.d.list.length != 0) {
                        if(brzcList.cssfbh){
                            brzcList.cfList = json.d.list;
                        }else{
                            brzcList.cfList = brzcList.cfList.concat(json.d.list);
                        }

                        brzcList.total = json.d.total;
                        brzcList.scollType = true;
                    }else{
                        brzcList.cfList = [];
                        brzcList.total = 0;
                        brzcList.scollType = true;
                    }

                }
            });
        },
        scrollGata(event) {
            if (event.srcElement.scrollHeight - event.srcElement.scrollTop === event.srcElement.clientHeight) {
                if (event.target.scrollLeft < 0 || this.scrollLeft == event.target.scrollLeft) {
                    if (event.target.scrollTop > this.scrollTop) {
                        if (this.scollType) {
                            this.scollType = false
                            if (this.cfList.length < this.total) {
                                if (this.uilPageBottom() == true) {
                                    this.param.page = this.param.page + 1;
                                    this.getTemData();
                                }
                            } else {
                                // this.loadData='暂无更多数据...'
                            }
                        }
                    }
                }
            }
            this.scrollLeft = event.target.scrollLeft;
            this.scrollTop = event.target.scrollTop
        },
        //根据处方模板参数查询明细
        getTemMxData: function (index) {
            this.cfmbIndex = index;
            brzcList.isChecked = []
            brzcList.isCheckAll = false
            this.activeIndex1 = index
            var zhyzbm = this.cfList[index].zhyzbm;
            var json = {
                zhyzbm: zhyzbm,
                lx:'0'
            };

            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=zhylyz&dg&json=' + JSON.stringify(json), function (data) {
                brzcList.cfMxList = [];
                if (data.d != null) {
                    brzcList.cfMxList = data.d.list;
                }
            });
        },
        // 双击处方模板添加药品
        addCfMb: function (index) {
            //全选
            this.sfcy = this.cfList[index].sfcy;
            this.isCheckAll = true;
            for (var i = 0; i < brzcList.cfMxList.length; i++) {
                Vue.set(brzcList.isChecked, i, true);
            }
            this.save('确定', index);
        },

        //保存
        save: function (saveTitle, index) {
            if (brzcList.title == '处方调用' && saveTitle == '确定') {
                zcy.addCf();
                zcy.jcxmMxJson = [];
                //进行数据的判断
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        var cfMx = brzcList.cfMxList[i];
                        var tmpMx = {};

                        tmpMx.fydj = cfMx.dj;
                        tmpMx.fysl = cfMx.sl;
                        tmpMx.fyje = (cfMx.dj * cfMx.sl);
                        tmpMx.fylb = cfMx.fylb;
                        tmpMx.fylbmc = cfMx.fylbmc;
                        tmpMx.mxfyxmbm = cfMx.mxzlxmbm;
                        tmpMx.mxfyxmmc = cfMx.mxzlxmmc;
                        tmpMx.rymzzd = userNameBg.Brxx_List.jbmc;
                        // tmpMx.sfgd = '';
                        tmpMx.yhbl = cfMx.yhbl;
                        tmpMx.yzfl = cfMx.ffylb;
                        tmpMx.zhfybh = cfMx.zhfybm;
                        tmpMx.zhfybm = cfMx.zhfybm;
                        if (cfMx.zhfybm) {
                            tmpMx.zhfybz = '1';
                        } else {
                            tmpMx.zhfybz = '0';
                        }
                        tmpMx.zhfymc = cfMx.zhfymc;
                        tmpMx.zxks = cfMx.zxks;
                        tmpMx.zxksmc = cfMx.zxksmc;

                        zcy.jcxmMxJson.push(tmpMx); //赋值给处方
                    }
                }
                // zcy.pubgetCfzje(); //计算费用总额
                zcy.jcxmMxJson.push({});

                this.closes();
                return;
            }

            if (brzcList.title == '历史处方 红色为作废处方' && saveTitle == '引用') {
                zcy.addCf();
                zcy.jcxmMxJson = [];
                //进行数据的判断
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        var lscfMx = brzcList.lsCfMxList[i];
                        var tmpMx = {};
                        tmpMx.fydj = lscfMx.fydj;
                        tmpMx.fylb = lscfMx.fylb;
                        tmpMx.fylbmc = lscfMx.fylbmc;
                        tmpMx.fysl = lscfMx.fysl;
                        tmpMx.fyje = lscfMx.fydj * lscfMx.fysl;
                        tmpMx.mxfyxmbm = lscfMx.mxfyxmbm;
                        tmpMx.mxfyxmmc = lscfMx.mxfyxmmc;
                        tmpMx.rymzzd = lscfMx.rymzzd;
                        // tmpMx.sfgd = '';
                        tmpMx.yhbl = lscfMx.yhbl;
                        tmpMx.yzfl = lscfMx.yzfl;
                        tmpMx.zhfybh = lscfMx.zhfybh;
                        tmpMx.zhfybm = lscfMx.zhfybh;
                        tmpMx.zhfybz = lscfMx.zhfybz;
                        tmpMx.zhfymc = lscfMx.zhfymc;
                        tmpMx.zxks = lscfMx.zxks;
                        tmpMx.zxksmc = lscfMx.zxksmc;

                        zcy.jcxmMxJson.push(tmpMx); //赋值给处方
                    }
                }
                // zcy.pubgetCfzje(); //计算费用总额
                zcy.jcxmMxJson.push({});

                this.closes();
                return;
            }

        },

        //获取明细费用项目（检查项目）这里暂时不要后面再来处理
        Wf_getJcxm: function () {
            var json = {
                ffylb: brzcList.ffylb,
                fylx: '1',
                ypfy: '0'
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryJzxm&dg=' + JSON.stringify(brzcList.dg) + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    brzcList.jcxmJson = [];  //先清空
                    if (data.d.list.length > 0) {
                        brzcList.jcxmJson = data.d.list;
                    }
                } else {
                    malert('获取费用项目列表失败', 'top', 'defeadted');
                }
            });
        },
    }
});
var change = false;

function remove(obj) {
    $(obj).parent().remove();
}

var check = '', copy = [];
document.onkeydown = function (event) {
    if (event.ctrlKey && event.keyCode === 65) {
        console.log('ctrl + a')
        zcy.checkAll = true
        zcy.numClass = null
    }
    if (event.ctrlKey && event.keyCode === 67) {
        console.log('ctrl + c')
        if (zcy.checkAll) {
            check = zcy.Yzxx_List
        } else if (zcy.numClass != null) {
            check = zcy.Yzxx_List[zcy.numClass]
        } else if (zcy.isCheck.length > 0) {
            for (var i = 0; i < zcy.isCheck.length; i++) {
                if (zcy.isCheck[i] == true) copy.push(JSON.parse(JSON.stringify(zcy.Yzxx_List[i])))
            }
            check = copy
            copy = []
        }
    }
    if (event.ctrlKey && event.keyCode === 86) {
        console.log('ctrl + v');
        if (check != '' || zcy.numClass) {
            zcy.Yzxx_List = zcy.Yzxx_List.concat(JSON.parse(JSON.stringify(check)))
        }
    }
}

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).is('.fytyp') || $(e.target).parents().is(".fytyp");
    if (!bol) {
        zcy.fybzShow = []
    }

})

//判断是否显示
function Djck(index) {
    if (zcy.ffylb != null) {
        if (zcy.ffylb.indexOf("30") >= 0 || zcy.ffylb == '4') {
            $(index).next().show();
        }
    }
}

//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

// 检查检验
var jcjyfy = new Vue({
    el: '#jcjyfy',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifShow: false,
        title: '检查/检验费用',
        jcjydList: {},
        zhList: [],
        ybList:[],
        zxksbm: null,
        fymxList: [],
        cxSqd: '',
        jybb:'',
        jybbMC:'',
        djxxContent: {},
        userName: {}, //登录的人得名字
        sfjz: "0",//是否急诊，默认 否
        filterArr: [],
        filterContent: {},
        mbbm: '', // 检查检验单切换时状态
    },
    computed: {
        brInfo: function () {
            return userNameBg.Brxx_List;
        },
    },
    watch: {
        "jcjyd": function (newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getZhList(newVal);
            }
        },
        'cxSqd': function (newVal, oldVal) {
            // if (newVal != '' || newVal != undefined) {
            this.filter(newVal)
            // }else{
            // this.djxxContent=this.filterContent;
            // }
        }
    },
    mounted: function () {
        this.getJybb()
        this.userName = sessionStorage.getItem("userName" + userId);
    },
    updated: function () {
        changeWin();

    },
    methods: {
        resultChangeJybb: function (val) {
            this.jybb = val[0];
            this.jybbMC = this.listGetName(this.ybList, val[0], 'ybbm', 'ybmc');
             this.$forceUpdate();
        },
          //获取检验标本
          getJybb: function () {
            var param = {
                parm: '',
                rows: 1000,
                page: 1,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmYbSelect&param=" + JSON.stringify(param), function (json) {
                console.log(json);
                if (json.a == "0") {
                    jcjyfy.ybList = json.d.list;
                }
            });
        },
        filter: function (val) {
            var _list_a = ['pydm', 'mxxmmc'], _this = this;
            for (var i = 0; i < this.djxxContent.zhList.length; i++) {
                for (var j = 0; j < this.djxxContent.zhList[i].mxList.length; j++) {
                    var py = -1;
                    _list_a.forEach(function (item) {
                        if (_this.djxxContent.zhList[i].mxList[j][item] && _this.djxxContent.zhList[i].mxList[j][item].toLowerCase().indexOf(val.toLowerCase()) != -1) {
                            py = 0;
                        }
                    })
                    if (py == -1) {
                        _this.djxxContent.zhList[i].mxList[j].indexShow = false;
                    } else {
                        _this.djxxContent.zhList[i].mxList[j].indexShow = true;
                    }
                }
            }
        },
        resultChangeMb: function (val) {
            jcjyfy.mbbm = val[0];

            // 判断是否显示 是否急诊下拉框
            if(jcjyfy.N05001200257 && jcjyfy.N05001200257.indexOf(jcjyfy.mbbm) != -1){
            	jcjyfy.N03001200140 = '0';
            }else{
            	jcjyfy.N03001200140 = '1';
            }

            jcjyfy.zxksbm = jcjyfy.jcjydList[val[5]].zxks;
            if (val[2].length > 1) {
                if (Array.isArray(this[val[2][0]])) {
                    Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    jcjyfy.getDjContent(val[0]);
                } else {
                    Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                    if (val[3] != null) {
                        Vue.set(this[val[2][0]], val[3], val[4]);
                    }
                    jcjyfy.getDjContent(val[0]);
                }
            } else {
                this[val[2][0]] = val[0];
                jcjyfy.getDjContent(val[0]);
            }
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
        },
        show: function () {
            this.ifShow = true;
        },
        closes: function () {
            this.ifShow = false;
        },
        //获取单据模板下拉框
        getJcjyd: function () {
            var parm = {
                page: 1,
                rows: 20000,
                mzorzy: '0',
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymb&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0 && json.d && json.d.list.length > 0) {
                            jcjyfy.jcjydList = json.d.list;
                            for (var i = 0; i < jcjyfy.jcjydList.length; i++) {
                                    if( jcjyfy.jcjydList[i].zxks== userNameBg.Brxx_List.ghks){
                                        jcjyfy.mbbm = jcjyfy.jcjydList[i].mbbm;
                                        jcjyfy.djxxContent = jcjyfy.jcjydList[i];
                                        jcjyfy.zxksbm = jcjyfy.jcjydList[i].zxks;
                                        break;
                                    }
                            }
                            if(!jcjyfy.mbbm){
                                jcjyfy.mbbm = jcjyfy.jcjydList[0].mbbm;
                                jcjyfy.djxxContent = jcjyfy.jcjydList[0];
                                jcjyfy.zxksbm = jcjyfy.jcjydList[0].zxks;
                            }
                            jcjyfy.getDjMx(jcjyfy.mbbm);
                    } else {
                        malert('获取列表失败', 'top', 'defeadted');
                    }
                });
        },


        getDjContent: function (mbbm) {
            var parm = {
                page: 1,
                rows: 20000,
                mzorzy: '0',
                mbbm: mbbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymb&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            jcjyfy.mbbm = json.d.list[0].mbbm;
                            jcjyfy.djxxContent = json.d.list[0];
                            jcjyfy.getDjMx(jcjyfy.mbbm);
                        }
                    } else {
                        malert('获取列表失败', 'top', 'defeadted');
                    }
                });
        },

        //获取模板内容
        getDjMx: function (mbbm) {
            var parm = {
                page: 1,
                rows: 20000,
                mbbm: mbbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymbxm&parm=' + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list.length > 0) {
                        jcjyfy.djxxContent.zhList = data.d.list;
                        var parm = {
                            page: 1,
                            rows: 20000,
                            mbbm: jcjyfy.mbbm,
                            mzorzy: '0',
                        };
                        $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymbmxxm&parm=' + JSON.stringify(parm), function (data) {
                            if (data.a == 0) {
                                if (data.d.list.length > 0) {
                                    for (var i = 0; i < jcjyfy.djxxContent.zhList.length; i++) {
                                        var mxlist = [];
                                        for (var j = 0; j < data.d.list.length; j++) {
                                            if (data.d.list[j].mbxmbm == jcjyfy.djxxContent.zhList[i].mbxmbm) {
                                                // @yqq
                                                // if (zcy.csqxContent.N03001200149 &&
                                                //     data.d.list[j].mxxmbm == zcy.csqxContent.N03001200149) {
                                                //     data.d.list[j].fymx = true;
                                                // }
                                                data.d.list[j].indexShow = true;
                                                mxlist.push(data.d.list[j]);
                                            }
                                        }
                                        jcjyfy.djxxContent.zhList[i].mxList = mxlist;
                                    }
                                    jcjyfy.djxxContent = Object.assign({}, jcjyfy.djxxContent);
                                    jcjyfy.filterContent = Object.assign({}, jcjyfy.djxxContent);
                                    console.log("这个是你最后得到的，帅得一批的东西！");
                                    console.log(jcjyfy.djxxContent);
                                }
                            } else {
                                malert('获取列表失败', 'top', 'defeadted');
                            }
                        });
                    }
                } else {
                    malert('获取列表失败', 'top', 'defeadted');
                }
            });
        },

        dbladd: function () {
            for (var i = 0; i < jcjyfy.fymxList.length; i++) {
                this.isChecked.push(true);
            }
            jcjyfy.add();
        },
        //添加
        add: function () {
                        /***************by zh***************/
                //判断如果没有新开页，就先新开页，避免替换第一个处方
            var ifadd = true;
            loop:
                for (var i = 0; i < zcy.jcxm_List.length; i++) {
                    if (!zcy.jcxm_List[i].yzhm) {
                        ifadd = false;
                        break loop;
                    }
                }
            if (ifadd) {
                zcy.addCf();
            }
            /***************by zh***************/
            var realList = [];
            for (var i = 0; i < jcjyfy.djxxContent.zhList.length; i++) {
                for (var j = 0; j < jcjyfy.djxxContent.zhList[i].mxList.length; j++) {
                    if (jcjyfy.djxxContent.zhList[i].mxList[j].czbw == '0') {
                        if (jcjyfy.djxxContent.zhList[i].mxList[j].fymx) {
                            realList.push(jcjyfy.djxxContent.zhList[i].mxList[j]);
                        }
                    } else {
                        if (jcjyfy.djxxContent.zhList[i].mxList[j].bwLeft) {
                            realList.push(jcjyfy.djxxContent.zhList[i].mxList[j]);
                        }
                        if (jcjyfy.djxxContent.zhList[i].mxList[j].bwRight) {
                            realList.push(jcjyfy.djxxContent.zhList[i].mxList[j]);
                        }
                    }
                }
            }
            var flag=false;
            for (var i = 0; i < realList.length; i++) {
                if(zcy.csqxContent.N03001200160 =='1' && !flag && realList[i].sfcx == '0'){
                    flag=true;
                    for (var j = 0; j <jcjyfy.djxxContent.zhList[0].mxList.length ; j++) {
                        if (zcy.csqxContent.N03001200149 && jcjyfy.djxxContent.zhList[0].mxList[j].mxxmbm == zcy.csqxContent.N03001200149) {
                            jcjyfy.djxxContent.zhList[0].mxList[j].fymx = true;
                            realList.push(jcjyfy.djxxContent.zhList[0].mxList[j])
                        }
                    }
                }
                zcy.ffylb = null;
                var mxfybm = realList[i].mxxmbm;
                var mxfymc = realList[i].mxxmmc;
                var ffylb = realList[i].ffylb;
                if (realList[i].zhfy == '1') {//组合费用需要请求后台查询组合费用明细
                    var jsons = {
                        mxfybm: mxfybm
                    };
                    $.ajaxSettings.async = false;
                    $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=queryZhfy' + '&json=' + JSON.stringify(jsons), function (data) {
                            if (data.a == 0) {
                                if (data.d.list.length > 0) {
                                    zcy.jcxmMxJson.splice(zcy.jcxmMxJson.length - 1, 1);
                                    for (var j = 0; j < data.d.list.length; j++) {
                                        //进行赋值操作
                                        var mzfyModel = {};
                                        mzfyModel.fysl = data.d.list[j].sl;
                                        mzfyModel.zhfy = realList[i].zhfy;
                                        mzfyModel.fyje = data.d.list[j].fydj * data.d.list[j].sl;
                                        mzfyModel.zhfybh = mxfybm;
                                        mzfyModel.zhfymc = mxfymc;
                                        mzfyModel.bbsm=jcjyfy.jybb
                                        mzfyModel.mxfyxmbm = data.d.list[j].mxfybm;
                                        var jybb = '';
                                        if (jcjyfy.jybbMC) {
                                            jybb = '[' + jcjyfy.jybbMC + ']';
                                        }
                                        //准备新增限制
                                        mzfyModel.mxfyxmmc = data.d.list[j].mxfymc+ jybb;
                                        mzfyModel.fylb = data.d.list[j].lbbm;
                                        mzfyModel.disabled = true;
                                        mzfyModel.zltsDisabled = true;
                                        mzfyModel.fylbmc = data.d.list[j].fylbmc;
                                        mzfyModel.fydj = data.d.list[j].fydj;
                                        mzfyModel.fydj1 = data.d.list[j].fydj;
                                        mzfyModel.yhje = 0.00;
                                        mzfyModel.yhbl = data.d.list[j].yhbl;
                                        mzfyModel.zxks = data.d.list[j].hsks;
                                        mzfyModel.zxksmc = data.d.list[j].zxksmc;
                                        mzfyModel.zhfybz = '1';
                                        mzfyModel.zhfyzh = '1';
                                        mzfyModel.zhfybm = data.d.list[j].zhfybm;
                                        mzfyModel.yzfl = data.d.list[j].ffylb;
                                        mzfyModel.ffylb = data.d.list[j].ffylb;
                                        mzfyModel.sfgd = data.d.list[j].sfgd; //此字段针对是否允许改变费用单价
                                        mzfyModel.jclx = data.d.list[j].jclx;
                                        mzfyModel.sfjz = jcjyfy.sfjz ? jcjyfy.sfjz : "0"; //是否急诊

                                        if(zcy.csqxContent.N03001200162 == realList[i].mbxmbm){
                                        	mzfyModel.sfjz = '1';
                                        }
                                        mzfyModel=Object.assign(mzfyModel,zcy.setFbbm(mzfyModel))
                                        if (zcy.jcxmMxJson != null && zcy.jcxmMxJson != undefined && zcy.jcxmMxJson.length > 0) {
                                            for (var k = 0; k < zcy.jcxmMxJson.length; k++) {
                                                if (mzfyModel.mxfyxmbm == zcy.jcxmMxJson[k].mxfyxmbm && zcy.csqxContent.N03001200130 != '1') {
                                                    var r = mconfirm("第【" + (k + 1) + "】行费用【" + zcy.jcxmMxJson[k].mxfyxmmc + "】已经存在，确定需要添加吗？");
                                                    if (r == false) {
                                                        zcy.jcxmMxJson.push({});
                                                        return;
                                                    }
                                                }
                                            }
                                        }
                                        if (mzfyModel.zxks != null && mzfyModel.zxks != '') {
                                            mzfyModel.zxksSfgd = '1';
                                        } else {
                                            //取选择的执行科室
                                            // if (jcjyfy.zxksbm) {
                                            //     mzfyModel.zxksSfgd = '0';
                                            //     mzfyModel.zxks = jcjyfy.zxksbm;
                                            //     mzfyModel.zxksmc = zcy.listGetName(zcy.zxksList, jcjyfy.zxksbm, 'ksbm', 'ksmc');
                                            // } else {
                                                mzfyModel.zxksSfgd = '0';
                                                mzfyModel.zxks = ksbm?ksbm:userNameBg.Brxx_List.ghks;
                                                mzfyModel.zxksmc = zcy.listGetName(zcy.zxksList, ksbm?ksbm:userNameBg.Brxx_List.ghks, 'ksbm', 'ksmc');
                                            // }
                                        }
                                        zcy.jcxmMxJson.push(mzfyModel);
                                        jcjyfy.ifShow=false;
                                    }
                                    // zcy.pubgetCfzje(); //计算费用总额
                                    //处理当前添加费用关于于检查检验的操作
                                    if (ffylb != null) {
                                        if (ffylb.indexOf("30") >= 0 || ffylb == '4') {
                                            zcy.ffylb = ffylb;
                                        }
                                    }
                                    zcy.jcxmMxJson.push({});
                                    var index = zcy.jcxmMxJson.length - 1;
                                    var id = "mxfyxmmc" + index;
                                    setTimeout(function () {
                                        document.getElementById(id).focus();
                                    }, 500);
                                } else {
                                    malert("未查到相关记录", 'top', 'defeadted');
                                }
                            } else {
                                malert("查询失败：" + data.c, 'top', 'defeadted');
                                return;
                            }
                        });
                }
                else {//不是组合费用
                    zcy.jcxmMxJson.splice(zcy.jcxmMxJson.length - 1, 1);
                    var mzfyModel = {};
                    mzfyModel.fysl =realList[i].xmsl ;
                    mzfyModel.mxfyxmbm = realList[i].mxxmbm;
                    var jybb = '';
                    if (this.jybbMC) {
                        jybb = '[' + this.jybbMC + ']';
                    }
                    mzfyModel.bbsm=this.jybb
                    mzfyModel.mxfyxmmc = realList[i].mxxmmc+ jybb;
                    mzfyModel.fylb = realList[i].lbbm;
                    mzfyModel.fylbmc = realList[i].fylbmc;
                    if (realList[i].xmdj > 0) {
                        mzfyModel.fydj = realList[i].xmdj;
                        mzfyModel.fydj1 = realList[i].fydj;
                    } else {
                        mzfyModel.fydj = realList[i].fydj;
                        mzfyModel.fydj1 = realList[i].fydj;
                    }
                    mzfyModel.yhje = 0.00;
                    mzfyModel.fyje = mzfyModel.fydj * mzfyModel.fysl;
                    mzfyModel.yhbl = realList[i].yhbl;
                    mzfyModel.zxks = realList[i].zxks;
                    mzfyModel.zxksmc = realList[i].zxksmc;
                    mzfyModel.yzfl = realList[i].ffylb;
                    mzfyModel.ffylb = realList[i].ffylb;
                    mzfyModel.sfgd = realList[i].sfgd;
                    mzfyModel.jclx = realList[i].jclx;
                    mzfyModel.sfjz = jcjyfy.sfjz ? jcjyfy.sfjz : "0"; //是否急诊

                    if(zcy.csqxContent.N03001200162 == realList[i].mbxmbm){
                    	mzfyModel.sfjz = '1';
                    }
                    mzfyModel=Object.assign(mzfyModel,zcy.setFbbm(mzfyModel))
                    if (zcy.jcxmMxJson != null && zcy.jcxmMxJson != undefined && zcy.jcxmMxJson.length > 0) {
                        for (var k = 0; k < zcy.jcxmMxJson.length; k++) {
                            if (mzfyModel.mxfyxmbm == zcy.jcxmMxJson[k].mxfyxmbm && zcy.csqxContent.N03001200130 != '1') {
                                var r = mconfirm("第【" + (k + 1) + "】行费用【" + zcy.jcxmMxJson[k].mxfyxmmc + "】已经存在，确定需要添加吗？");
                                if (r == false) {
                                    zcy.jcxmMxJson.push({});
                                    return;
                                }
                            }
                        }
                    }
                    if (mzfyModel.zxks != null && mzfyModel.zxks != '') {
                        mzfyModel.zxksSfgd = '1';
                    } else {
                        //取选择的执行科室
                        if (jcjyfy.zxksbm) {
                            mzfyModel.zxksSfgd = '0';
                            mzfyModel.zxks = jcjyfy.zxksbm;
                            mzfyModel.zxksmc = zcy.listGetName(zcy.zxksList, jcjyfy.zxksbm, 'ksbm', 'ksmc');
                        } else {
                            mzfyModel.zxksSfgd = '0';
                            mzfyModel.zxks = ksbm?ksbm:userNameBg.Brxx_List.ghks;
                            mzfyModel.zxksmc = zcy.listGetName(zcy.zxksList, ksbm?ksbm:userNameBg.Brxx_List.ghks, 'ksbm', 'ksmc');
                        }
                    }
                    zcy.jcxmMxJson.push(mzfyModel);
                    jcjyfy.ifShow=false;
                    // zcy.pubgetCfzje(); //计算费用总额
                    //处理当前添加费用关于于检查检验的操作
                    if (ffylb != null) {
                        if (ffylb.indexOf("30") >= 0 || ffylb == '4') {
                            zcy.ffylb = ffylb;
                        }
                    }
                    zcy.jcxmMxJson.push({});
                    var index = zcy.jcxmMxJson.length - 1;
                    var id = "mxfyxmmc" + index;
                    setTimeout(function () {
                        document.getElementById(id).focus();
                    }, 500);
                }
            }

            jcjyfy.getDjContent(jcjyfy.mbbm);

        },
    },
});
zcy.Wf_selectCFJcxm();
zcy.Wf_getHsksData();
