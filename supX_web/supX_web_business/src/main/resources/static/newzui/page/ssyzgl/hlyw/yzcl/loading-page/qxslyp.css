.wrapper{
    background-color: #fff;
    min-height: 100%;
}
.userNameBg{
    background:#708f89;
    /*height:180px;*/
    position: relative;
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    background-image: url("/newzui/pub/image/userImg.png");
    padding: 10px;
}
.flex{
    display: flex;
    align-items: center;
}
.userNameImg img{
    width: 100px;
}
.text-color{
    color: #ffffff;
}
.userName{
    font-size:22px;
    color:#ffffff;
    text-align:left;
    margin-right: 31px;
}
.sex{
    margin-right: 27px;
}
.userHeader{
    margin-bottom: 10px;
}
.text{
    font-size:14px;
    color:#E0E6E4;
    text-align:left;
}
.zyh,.bq,.ys,.brzt,.bz,.cwh{
    margin-right: 60px;
}
.userCwh{
    margin-bottom: 4px;
}
.fyhj {
    margin-right: 39px;
}
.yjhj {
    margin-right: 104px;
}
.zyts {
    margin-right: 32px;
}
.phone {
    margin-right: 53px;
}
.hl {
    margin-right: 52px;
}
.userFooter{
    margin-bottom: 13px;
}
.heaf{
    color: #B0BFBB;
    text-decoration: underline;
}
.bryz-list{
    margin-bottom: 10px;
}
.ksys-btn{
    background-color: #fff;
}
.zui-form-label{
    width: 70px;
}
.pop .zui-form .zui-inline {
    padding: 0 0px 0 70px;
    display: block;
    float: none!important;
}
.zui-inline, .zui-input-inline, .zui-select-inline{
    margin-right: 0px;
}
.todate{
    width: 448px;
}
.tisi{
    color:#f2a654;
    margin-top: 10px;
}
.icon-width{
    width: 24px;
    display: inline-block;
    height: 24px;
    position: relative;
}
.icon-width::before {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 0px;
    top: 0px;
}
.pop .block-box{
    padding-left: 26px!important;
    text-align: left!important;
}
.pop .block-box .zui-table-cell{
    display: inline-block;
    text-align: center;
}
.pop .block-box span{
    line-height: 20px;
}
.qxsh{
    background: #d9dddc !important;
    color: #8e9694 !important;
}
.syt{
    background: #9353bb !important;
    color: #ffffff !important;
}
.zx{
    background: #f2a654 !important;
}
.dy{
    background: #1abc9c !important;
}
.blRight{
    position: absolute;
    right: 7px;
    bottom: -40px;
}
.blImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/xbl.png");
}
.xyzImg{
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    width:80px;
    cursor: pointer;
    height:80px;
    display: inline-block;
    background-image: url("/newzui/pub/image/xyz.png");
}

.danwei-box .zui-input{
    padding-right: 34px;
}
.danwei-box .danwei{
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -9.5px;
    color:#1abc9c;
}
.input-box{
    padding-left: 80px;
    position: relative;
}
.input-box .input-box-title{
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 75px;
    text-align: right;
    line-height: 36px;
}
.pop-input-box input{
    padding-right: 36px;
}
.pop-input-box .danwei{
    color:#1abc9c;
    line-height:36px;
    position: absolute;
    right: 0;
    top: 0;
    padding: 0 11px;
}
.ksys-side span {
    /* display: block; */
    width: 20px;
    position: absolute;
}
.zui-select-group {
    width: 436px;
}
.cfh .zui-select-group {
    left: -258px;
}
.zui-table-view{
    margin: 10px;
}
.action-bar.fixed{
    bottom: 10px;
    right: 10px;
    left: 10px;
    padding-top: 16px;
    padding-bottom: 16px;
    width: auto;
    height: 68px;
}