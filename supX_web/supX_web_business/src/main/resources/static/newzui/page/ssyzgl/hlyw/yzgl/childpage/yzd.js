/**
 * Created by mash on 2017/10/10.
 */
var printGd = 20;
var yljgmc=sessionStorage.getItem('yljgOrUser' + userId)?JSON.parse(sessionStorage.getItem('yljgOrUser' + userId)).yljgmc:'';
var dateend = getTodayDateEnd();
var datestart = getTodayDateBegin();
var toolMenu_yzd = new Vue({
    el: '.toolMenu_yzd',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        which: 0,
        bk:'1',
        qy:'1',
        yfList: {
            'a': "用法1"
        },
        yf: "a",
        pcList: {
            "a": "20~80"
        },
        pc: "a",
        ypbz: '2',
        ystzbz: '9',
        ypfy_tran: {
            "0": "非药品",
            "1": "药品",
            "2": "全部"
        },
        ystzbz_tran: {
            '0': '正常',
            '1': '停嘱',
            '9': '全部',
        },
        yyffList: [],
        yypcList: [],
        qsxzList: [],
        searchContent: {},
        popContent: {},
        caqxContent: {},
        oldbrxxContent: yzclLeft.HszbrItem,
		zx_begin:null,
		zx_end:null,
    },
    mounted:function(){
        this.getYf();
        this.getPc();
		console.log(this.oldbrxxContent)
		Mask.newMask(this.MaskOptions('dbegin'));
        Mask.newMask(this.MaskOptions('dEnd'));
        //默认加载当前时间
        this.zx_begin = this.fDate(this.oldbrxxContent.ryrq,'AllDate');
        this.zx_end = dateend;
        laydate.render({
            elem: '#dbegin',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
			min:this.fDate(this.oldbrxxContent.ryrq,'AllDate'),
            done: function (value, data) { //回调方法
                toolMenu_yzd.zx_begin = value;
                if (toolMenu_yzd.which == 0) {
                    toolMenu_yzd.long(toolMenu_yzd.which)
                } else {
                    toolMenu_yzd.short(toolMenu_yzd.which)
                }
            }
        });
        laydate.render({
            elem: '#dEnd',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                toolMenu_yzd.zx_end = value;
                if (toolMenu_yzd.which == 0) {
                    toolMenu_yzd.long(toolMenu_yzd.which)
                } else {
                    toolMenu_yzd.short(toolMenu_yzd.which)
                }
            }
        });
    },
    methods: {
        reCheckOne: function (val) {
            Vue.set(this, val[0], val[1])
            if (toolMenu_yzd.which == 0) {
                cqyzd.getData();
            } else {
                lsyzd.getData();
            }
        },
        commonResultChange2: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "ypbz":
                    this.ypbz = val[0]
                    if (toolMenu_yzd.which == 0) {
                        cqyzd.getData();
                    } else {
                        lsyzd.getData();
                    }
                    break;
                case "ystzbz":
                    this.ystzbz = val[0];
                    if (toolMenu_yzd.which == 0) {
                        cqyzd.getData();
                    } else {
                        lsyzd.getData();
                    }
                    break;
            }
        },
        iscf: function (zyh) {
            var parm = {
                zyh: zyh?zyh:cqyzd.BrxxJson.zyh
            };
            toolMenu_yzd.qsxzList = [];
            toolMenu_yzd.popContent.yebh = '000';
            $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' + JSON.stringify(parm), function (json) {
                if (json.a == '0') {
                    if (json.d.list != null && json.d.list.length > 0) {
                        var qb = {
                            yexm: cqyzd.BrxxJson.brxm,//如果有影响请还原上面代码，注释本行代码
                            yebh: "000",
                        };
                        json.d.list.push(qb);
                        toolMenu_yzd.qsxzList = json.d.list;//亲属选择
                    } else {
                        var qb = {
                            yexm: cqyzd.BrxxJson.brxm,//如果有影响请还原上面代码，注释本行代码
                            yebh: "000",
                        };
                        toolMenu_yzd.qsxzList.push(qb);
                    }
                }
            });
        },
        initData: function () {
            cqyzd.BrxxJson = JSON.parse(sessionStorage.getItem("HszbrItem"));
            lsyzd.BrxxJson = JSON.parse(sessionStorage.getItem("HszbrItem"));
            lsPrint.BrxxJson = JSON.parse(sessionStorage.getItem("HszbrItem"));
            cqPrint.BrxxJson = JSON.parse(sessionStorage.getItem("HszbrItem"));
            this.oldbrxxContent = JSON.parse(sessionStorage.getItem("HszbrItem"));

        },
        commonResultChange: function (val) {
                        Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            toolMenu_yzd.oldbrxxContent['yebh'] = this.qsxzList[val[5]].yebh;
            this.$forceUpdate();
            var ageContent = {};
            if (this.qsxzList[val[5]]['yebh'] == '000') {
                cqyzd.BrxxJson = this.oldbrxxContent;
                lsyzd.BrxxJson = this.oldbrxxContent;
                lsPrint.BrxxJson = this.oldbrxxContent;
                cqPrint.BrxxJson = this.oldbrxxContent;
            } else {
                var obj = JSON.parse(JSON.stringify(this.qsxzList[val[5]]))
                obj.brxm = obj.yexm
                obj.brxb = obj.xb
                obj.ryksmc=this.oldbrxxContent.ryksmc;
                ageContent = this.toAge(obj.csrq,obj.bqcyrq);
                obj.nl = ageContent.age;
                obj.nldw = ageContent.unitNum;
                obj.ryks=yzclLeft.HszbrItem.ryks;
                obj.ryksmc=yzclLeft.HszbrItem.ryksmc;
                cqyzd.BrxxJson = obj
                lsyzd.BrxxJson = obj
                lsPrint.BrxxJson = obj
                cqPrint.BrxxJson = obj
            }
            if (this.which == 0) {
                this.long(this.which)
            } else {
                this.short(this.which)
            }
        },
        long: function (num) {
            this.which = num;
            cqyzd.which = num;
            lsyzd.isShow = false;
            cqyzd.isShow = true;
            cqyzd.getData();
        },
        short: function (num) {
            this.which = num;
            cqyzd.which = num;
            cqyzd.isShow = false;
            lsyzd.isShow = true;
            lsyzd.getData();
        },
        // 加上假数据填充格子
        setPrintData: function () {
            var sa = 20 - cqyzd.jsonList.length;
            for (var i = 0; i < sa; i++) {
                // cqyzd.jsonList.push({});
            }
        },
        doPrint: function (istpye) {
            // cqyzd.isGoPrint = false;
            // lsyzd.isGoPrint = false;
            // setTimeout(function () {
            //     window.print();
            // }, 100);
            cqyzd.doPrint(istpye);
        },
        resultChangePc: function (val) {
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            Vue.set(this[val[2][0]], 'pcbm', val[0]);
            if (toolMenu_yzd.which == 0) {
                cqyzd.getData();
            } else {
                lsyzd.getData();
            }
        },
        resultChangeYf: function (val) {
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            Vue.set(this[val[2][0]], 'yyffbm', val[0]);
            if (toolMenu_yzd.which == 0) {
                cqyzd.getData();
            } else {
                lsyzd.getData();
            }
        },
        getYf: function () {
            var json = {
                sfcy: 0
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yyff" +
                '&json=' + JSON.stringify(json),
                function (json) {
                    if (json.a == 0) {
                        toolMenu_yzd.yyffList = json.d.list;
                        var nlYf = {
                            yyffbm: '',
                            yyffmc: '全部'
                        };
                        toolMenu_yzd.yyffList.push(nlYf);
                        toolMenu_yzd.searchContent.yfbm = '';
                    } else {
                        malert('用药方法列表查询失败' + json.c, 'top', 'defeadted')
                        return;
                    }
                });
        },
        getPc: function () {
            var pc_dg = {page: 1, rows: 100, sort: "sor", order: "asc", parm: ""};
            $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmpc&types=query&dg=" + JSON.stringify(pc_dg), function (json) {
                if (json.a == 0) {
                    toolMenu_yzd.yypcList = json.d.list;  //频次下拉窗口绑定数据
                    var nlPc = {
                        pcbm: '',
                        pcmc: '全部'
                    };
                    toolMenu_yzd.yypcList.push(nlPc);
                    toolMenu_yzd.searchContent.yypc = '';
                } else {
                    malert('频次列表查询失败' + json.c, 'top', 'defeadted')
                    return;
                }
            });
        },
        goOnPrint: function () {
            cqyzd.isGoPrint = true;
            lsyzd.isGoPrint = true;
            $('.yzd-table td').css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                $('.yzd-table td').css('border', '1px solid #999');
            }, 100);
        },
    }
});

var cqyzd = new Vue({
    el: '.cqyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        yljgmc:yljgmc,
        jsonList: [],
        pageList: [],
        isShow: true,
        isGoOn: false,
        param: {},
        is_csqx: {
            N03003200146: yzclLeft.caqxContent.N03003200146,
            N03003200125: yzclLeft.caqxContent.N03003200125,
            N03003200139: yzclLeft.caqxContent.N03003200139,
            N03003200141: yzclLeft.caqxContent.N03003200141,
            N03004200265: yzclLeft.caqxContent.N03004200265,
            N03003200189: yzclLeft.caqxContent.N03003200189,
        },
        which: 0,
        BrxxJson: yzclLeft.HszbrItem,
        pageH: 830,
        isGoPrint: false,            // 是否续打
        caqxContent:yzclLeft.caqxContent
    },
    watch: {
        jsonList: function () {
            toolMenu_yzd.setPrintData();
        }
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        doPrint: function (isGoOn) {
			
            $('.no-print').html('')
            var cqTr;
            cqyzd.isGoOn = isGoOn
            cqPrint.list = [];
            lsPrint.list = [];
            if (this.which == 0) {
                cqTr = $(".cqyzd tr");
            } else {
                cqTr = $(".lsyzd tr");
            }
            var _height = 0;
            var a = 0, b = -1;
            for (var i = 0; i < cqTr.length-2 ; i++) {
                
                if (i >1 && (i-1)%22 == 0 && this.which == 0) {
                    b++;
                    var as = [];
                    for (var f = a; f < i; f++) {
                        if (this.which == 0) {
                            as.push(cqyzd.jsonList[f]);
                        } else {
                            as.push(lsyzd.jsonList[f]);
                        }
                    }
                    if (this.which == 0){
						cqPrint.list[b] = as;
					} else {
						lsPrint.list[b] = as;
					}
                    a = i;
                    this.pageList.push(as.length);
                }else if(i >1 && (i-1)%27 == 0 && this.which == 1){
					b++;
					var as = [];
					for (var f = a; f < i; f++) {
					    if (this.which == 0) {
					        as.push(cqyzd.jsonList[f]);
					    } else {
					        as.push(lsyzd.jsonList[f]);
					    }
					}
					if (this.which == 0){
						cqPrint.list[b] = as;
					} else {
						lsPrint.list[b] = as;
					}
					a = i;
					this.pageList.push(as.length);
				}
            }
            var pp = [];
            if (this.which == 0) {
                for (var p = a; p < cqyzd.jsonList.length; p++) {
                    pp.push(cqyzd.jsonList[p]);
                }
            } else {
                for (var ls = a; ls < lsyzd.jsonList.length; ls++){
					pp.push(lsyzd.jsonList[ls]);
				} 
            }
			if(pp.length>0 ){
				let tplength = 28;
				if(this.which == 0){
					tplength = 23;
				}
				
				for (var l = pp.length; l < tplength; l++) {
				    pp.push({'psjg': '无'});
				}
				console.log(pp.length)
			}
            
            if (this.which == 0) {
				if(pp && pp.length>0){
					cqPrint.list[b + 1] = pp;
				}
                
                cqPrint.isShow = true;
                lsPrint.isShow = false;
            } else {
				if(pp && pp.length>0){
					lsPrint.list[b + 1] = pp;
				}else{
					if(lsPrint.list[lsPrint.list.length-1].length!= 28){
						for (let i = lsPrint.list[lsPrint.list.length-1].length; i < 28; i++) {
							lsPrint.list[lsPrint.list.length-1][i]={'psjg': '无'};
						}
					}
				}
                
                lsPrint.isShow = true;
                cqPrint.isShow = false;
            }
            cqyzd.showTable(cqyzd.which);
            cqPrint.isPrint = isGoOn;
            lsPrint.isPrint = isGoOn;
			            if (isGoOn) {
                cqPrint.isGoPrint = true;
                lsPrint.isGoPrint = true;
                setTimeout(function () {
                    cqyzd.hideTable(cqyzd.which);
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '0px solid transparent');
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '0px solid transparent');
					$('.cqPrint table').eq(lsPrint.pagePrint).find("th").css('border', '0px solid transparent');
					$('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '0px solid transparent');
					
					$('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '0px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '0px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '0px solid transparent');
					$('.lsPrint table').eq(lsPrint.pagePrint).find("th").css('border', '0px solid transparent');
					
					$('.ysDiv').attr('style','visibility: hidden;')
					$('.yzdTitle').attr('style','visibility: hidden;')
					$('.yzd-brInfo').attr('style','visibility: hidden;')
                }, 50);
                setTimeout(function () {
                    window.print();
                    cqPrint.isGoPrint = false;
                    lsPrint.isGoPrint = false;
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
					
					$('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '0px solid #999');
					$('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '0px solid #999');
					$('.cqPrint table').eq(lsPrint.pagePrint).find("th").css('border', '0px solid #999');
					$('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '0px solid #999');
					
					$('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '0px solid #999');
					$('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '0px solid #999');
					$('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '0px solid #999');
					$('.lsPrint table').eq(lsPrint.pagePrint).find("th").css('border', '0px solid #999');
					
					$('.ysDiv').attr('style','')
					$('.yzdTitle').attr('style','')
					$('.yzd-brInfo').attr('style','')
					
                }, 100);
            } else {
								
                setTimeout(function () {
                    window.print();
					
                    printGd = 20
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                }, 100);
            }
        },
        hideTable: function (type) {
			
            var num = 0;
            if (type == 0 && cqyzd.isChecked == cqPrint.isChecked) {
                for (var i = 0; i < cqPrint.pagePrint; i++) {
                    $('.cqPrint .popCenter').eq(i).hide();
                    num += this.pageList[i];
                }
                cqPrint.isChecked = cqPrint.isChecked - num;
                cqPrint.$forceUpdate()
            } else if (type == 0) {
                cqPrint.isChecked = cqyzd.isChecked
                this.hideTable(cqyzd.which)
            }
            if (type == 1 && lsyzd.isChecked == lsPrint.isChecked) {
                for (var j = 0; j < lsPrint.pagePrint; j++) {
                    $('.lsPrint .popCenter').eq(j).hide();
                    num += this.pageList[j];
                }
                lsPrint.isChecked = lsPrint.isChecked - num;
                lsPrint.$forceUpdate()
            } else if (type == 1) {
                lsPrint.isChecked = lsyzd.isChecked
                this.hideTable(cqyzd.which)

            }
        },
        showTable: function (type) {
            if (type == 0) {
                for (var i = 0; i < $('.cqPrint .popCenter').length; i++) {
                    $('.cqPrint .popCenter').eq(i).show();
                }
                cqPrint.$forceUpdate()
            } else {
                for (var j = 0; j < $('.lsPrint .popCenter').length; j++) {
                    $('.lsPrint .popCenter').eq(j).show();
                }
            }
        },
        goPrint: function (index) {
            cqyzd.isChecked = index;
            cqPrint.isChecked = index;
            var cqTr = $(".cqyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > 680) {
                    b++;
                    _height = 0;
                }
            }
            cqPrint.pagePrint = b;
        },
        getData: function () {
            if (!this.BrxxJson.zyh) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                yebh: toolMenu_yzd.oldbrxxContent['yebh'] == '000' || !toolMenu_yzd.oldbrxxContent['yebh'] ? undefined : toolMenu_yzd.oldbrxxContent['yebh'],
                zyh: this.BrxxJson.zyh,
                yzlx: '1',
                cfss:(toolMenu_yzd.oldbrxxContent['yebh'] == '000' || !toolMenu_yzd.oldbrxxContent['yebh'])?'0':'1',
                ypbz: (toolMenu_yzd.ypbz ? toolMenu_yzd.ypbz : ''),
                ystzbz: (toolMenu_yzd.ystzbz ? toolMenu_yzd.ystzbz : ''),
				zx_begin:toolMenu_yzd.zx_begin,
				zx_end:toolMenu_yzd.zx_end,
				zxks:yzclRight.zxks,
				ssks:yzclRight.zxks,
            };
            if (toolMenu_yzd.searchContent.yfbm != '') {
                this.param.yyffbm = toolMenu_yzd.searchContent.yfbm;
            }
            if (toolMenu_yzd.searchContent.yypc != '') {
                this.param.pcbm = toolMenu_yzd.searchContent.yypc;
            }
            // if (toolMenu_yzd.bk == '1') {
            //     this.param.ksbm = this.BrxxJson.ryks;
            // }else{
            //     this.param.ksbm='';
            // }
            if(cqyzd && cqyzd.is_csqx.N03003200189){
                this.param.zlyzsffhxs=cqyzd.is_csqx.N03003200189;
            }else {
                this.param.zlyzsffhxs='0';
            }
			
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&hsbz=1&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    cqyzd.jsonList = json.d.list;
					                    var list = [];
                    for (var i = 0; i < cqyzd.jsonList.length; i++) {
						if (cqyzd.jsonList[i].fzh != 0) {
							if (i == 0) { // 第一个
								if (cqyzd.jsonList[i + 1] != undefined) {
									if (cqyzd.jsonList[i].fzh == cqyzd.jsonList[i + 1].fzh && cqyzd.jsonList[i].yzxh == cqyzd.jsonList[i + 1].yzxh) {
										cqyzd.jsonList[i]['tzbj'] = 'tz-start';
									}
								}
							} else if (i == cqyzd.jsonList.length - 1) { // 最后一个
								if (cqyzd.jsonList[i].fzh == cqyzd.jsonList[i - 1].fzh && cqyzd.jsonList[i].yzxh == cqyzd.jsonList[i - 1].yzxh) {
									cqyzd.jsonList[i]['tzbj'] = 'tz-stop';
								}
							} else {
								if ((cqyzd.jsonList[i].fzh != cqyzd.jsonList[i - 1].fzh || cqyzd.jsonList[i].yzxh != cqyzd.jsonList[i - 1].yzxh) && (cqyzd.jsonList[i].fzh == cqyzd.jsonList[i + 1].fzh && cqyzd.jsonList[i].yzxh == cqyzd.jsonList[i + 1].yzxh)) {
									cqyzd.jsonList[i]['tzbj'] = 'tz-start';
								} else if (cqyzd.jsonList[i].fzh == cqyzd.jsonList[i - 1].fzh && cqyzd.jsonList[i].yzxh == cqyzd.jsonList[i - 1].yzxh && cqyzd.jsonList[i].fzh == cqyzd.jsonList[i + 1].fzh && cqyzd.jsonList[i].yzxh == cqyzd.jsonList[i + 1].yzxh) {
									cqyzd.jsonList[i]['tzbj'] = 'tz-center';
								} else if ((cqyzd.jsonList[i].fzh == cqyzd.jsonList[i - 1].fzh && cqyzd.jsonList[i].yzxh == cqyzd.jsonList[i - 1].yzxh) && (cqyzd.jsonList[i].fzh != cqyzd.jsonList[i + 1].fzh || cqyzd.jsonList[i].yzxh != cqyzd.jsonList[i + 1].yzxh)) {
									cqyzd.jsonList[i]['tzbj'] = 'tz-stop';
								}
							}
						}
						
						
                        cqyzd.jsonList[i]['xmmc'] = cqyzd.jsonList[i]['xmmc']?cqyzd.jsonList[i]['xmmc'].replace('null', ''):"";
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc']?cqyzd.jsonList[i]['yyffmc'].replace('null', ''):"";
                        cqyzd.jsonList[i]['yyffmc'] = cqyzd.jsonList[i]['yyffmc']?cqyzd.jsonList[i]['yyffmc'].replace('null', ''):"";

                        if(cqyzd.sameSE(i) == 'start' || cqyzd.sameSE(i) == 'end' || cqyzd.sameSE(i) == 'all'){//输液显示单词计量，其他显示用法
                            cqyzd.jsonList[i].plusYyff = 'dcjl';
                        }else{
                            cqyzd.jsonList[i].plusXsyl = 'xsyl';
                        }

                        if(cqyzd.is_csqx.N03003200146 == '1'){
                        	//yysm
                            var yysm = cqyzd.jsonList[i]['yysm'];
                            // @yqq 根据用药方法 或者xmmc来将医嘱单拆分为多个医嘱显示
                            if(yysm && yysm.length > 15 && yysm.indexOf('、') > -1){
                            	var yysmArr = yysm.split('、');
                            	var newArr = [];
                            	var temp = '';
                            	var c = 20;
                            	for (var j = 0; j < yysmArr.length; j++) {
                            		var t = temp;
                            		temp += (yysmArr[j] + '、');
                            		if(temp.length > c){
                            			newArr.push(t);
                            			temp = (yysmArr[j] + '、');
                            		}
                            		if(temp.length == c || j == (yysmArr.length - 1)){
                            			newArr.push(temp);
                            			temp = '';
                            		}
                            	}

                            	for (var j = 0; j < newArr.length; j++) {
                            		var obj = JSON.parse(JSON.stringify(cqyzd.jsonList[i]));
                            		obj.yysm = newArr[j].substring(0, newArr[j].lastIndexOf('、'));
                            		list.push(obj);
                            	}
                            }else{
                                list.push(cqyzd.jsonList[i]);
                            }
                        }else{
                        	list.push(cqyzd.jsonList[i]);
                        }


                        if(yzclLeft.caqxContent.N03004200243 == '1'){
                            if(cqyzd.sameSE(i) == 'end'){
                                var obj = {
                                    yyffmc:cqyzd.jsonList[i]['yyffmc'],
                                    sysd:cqyzd.jsonList[i]['sysd'],
                                    sysddw:cqyzd.jsonList[i]['sysddw'],
                                    pcmc:cqyzd.jsonList[i]['pcmc'],
                                    ksrq:cqyzd.jsonList[i]['ksrq'],
                                    zxsj:cqyzd.jsonList[i]['zxsj'],
                                    ystzsj:cqyzd.jsonList[i]['ystzsj'],
                                    hstzsj:cqyzd.jsonList[i]['hstzsj'],
                                    fzh:0,
                                    end:'plus',
                                };
                                list.push(obj);
                            }
                        }
						
                    }
                    cqyzd.jsonList = [];
                    cqyzd.jsonList = list;
					console.log(list)
                } else {
                    malert("病人医嘱单信息查询失败！", 'top', 'defeadted');
                }
            });
        },
        sameDate: function (name, index, type) {
			
            var val = this.jsonList[index][name];
            if(!val){
				return;
			}
            var reDate = new Date(val);
            if (type == 'ry') {
                return reDate.getFullYear().toString()+'-'+this.Appendzero((reDate.getMonth() + 1)) + '-' + this.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return this.Appendzero(reDate.getHours()) + ':' + this.Appendzero(reDate.getMinutes());
            }else if(type=='name'){
                return this.jsonList[index][name]
            }
        },

        sameDate_arrow_head: function (name, index) {
            var val = cqyzd.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if(index <= 0 ) return;
            if (index != cqyzd.jsonList.length - 1) {
                nextVal = cqyzd.jsonList[index + 1][name]
            }
            if (index != 0) {
                prvVal = cqyzd.jsonList[index - 1][name];
            }
            if(val != nextVal){
                return 'head';
            }
            if (!val && nextVal){
                return 'head';
            }
            if(index == (this.jsonList.length - 1)){
                return 'head';
            }

        },
        sameDate_qm: function (name, index) {
            var val = cqyzd.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if(index == 0){
                return '';
            }
            if(index != 0){
                prvVal = cqyzd.jsonList[index - 1][name]
            }
            if(index != cqyzd.jsonList.length - 1 ){
                nextVal = cqyzd.jsonList[index + 1][name]
            }else{
                return;
            }
            if (!val && !nextVal) {
                return 'line';
            }
            if(val != prvVal && val == nextVal){
                return '';
            }
            if(val == nextVal){
                return 'line';
            }
        },
        sameDate_arrow_head_qm: function (name, index) {
            var val = cqyzd.jsonList[index][name];
            var afterNextVal = null;
            if(index <= 0 ) return;
            if (index < cqyzd.jsonList.length - 2) {
                afterNextVal = cqyzd.jsonList[index + 2][name];
            }
            if(index == cqyzd.jsonList.length - 1){
                return 'head';
            }
            if(val != afterNextVal){
                return 'head';
            }

        },

        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            var ksrq = this.jsonList[index]['ksrq'];
            if (index != 0) {
                var prvFzh = this.jsonList[index - 1]['fzh'];
                var prvKsrq = this.jsonList[index - 1]['ksrq'];
            }
            if (index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var nextKsrq = this.jsonList[index + 1]['ksrq'];
            }
            if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                return 'null';
            }
            if (index == this.jsonList.length - 1) { // 最后一个
                if (this.jsonList[index].fzh == this.jsonList[index].fzh && this.jsonList[index].yzxh == this.jsonList[index - 1].yzxh) {
                    return 'end';
                }
                return 'null'
            }
            if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                return 'null';
            }
            if ((fzh != prvFzh || this.jsonList[index].yzxh != this.jsonList[index-1].yzxh) && (fzh == nextFzh && this.jsonList[index].yzxh == this.jsonList[index + 1].yzxh)) {
                return 'start';
            }

            if ((fzh == prvFzh && this.jsonList[index].yzxh == this.jsonList[index - 1].yzxh) && (fzh != nextFzh || this.jsonList[index].yzxh != this.jsonList[index + 1].yzxh)) {
                return 'end';
            }
            if (fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq == nextKsrq)) {
                return 'all';
            }
            return 'null'
        },
        isShowItem: function (index) {
            
            if (this.jsonList[index + 1] == null) {
                return true;
            }
            if (this.jsonList[index]['fzh'] == this.jsonList[index + 1]['fzh'] && this.jsonList[index]['yzxh'] == this.jsonList[index+1]['yzxh'] && this.jsonList[index]['fzh'] != 0) {
                if (this.jsonList[index]['yyffmc'] == this.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});
var lsyzd = new Vue({
    el: '.lsyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        yljgmc:yljgmc,
        jsonList: [],
        isShow: false,
        param: {},
        is_csqx: {
            N03003200125: yzclLeft.caqxContent.N03003200125,
            N03003200133: yzclLeft.caqxContent.N03003200133,
            N03003200139: yzclLeft.caqxContent.N03003200139,
            N03003200141: yzclLeft.caqxContent.N03003200141,
            N03004200265: yzclLeft.caqxContent.N03004200265,
            N03003200189: yzclLeft.caqxContent.N03003200189,
        },
        BrxxJson: yzclLeft.HszbrItem,
        isGoPrint: false,            // 是否续打
        caqxContent:yzclLeft.caqxContent
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        goPrint: function (index) {
            lsyzd.isChecked = index;
            lsPrint.isChecked = index;
            var cqTr = $(".lsyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > cqyzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            lsPrint.pagePrint = b;
        },
        getData: function () {
                        if (!this.BrxxJson.zyh) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }
            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                yebh: toolMenu_yzd.oldbrxxContent['yebh'] == '000' || !toolMenu_yzd.oldbrxxContent['yebh'] ? undefined : toolMenu_yzd.oldbrxxContent['yebh'],
                order: 'asc',
                zyh: this.BrxxJson.zyh,
                yzlx: '0',
                cfss:(toolMenu_yzd.oldbrxxContent['yebh'] == '000' || !toolMenu_yzd.oldbrxxContent['yebh'])?'0':'1',
                ypbz: (toolMenu_yzd.ypbz ? toolMenu_yzd.ypbz : ''),
                ystzbz: (toolMenu_yzd.ystzbz ? toolMenu_yzd.ystzbz : ''),
				zx_begin:toolMenu_yzd.zx_begin,
				zx_end:toolMenu_yzd.zx_end,
				zxks:yzclRight.zxks,
				ssks:yzclRight.zxks,
            };
            if (toolMenu_yzd.searchContent.yfbm != '') {
                this.param.yyffbm = toolMenu_yzd.searchContent.yfbm;
            }
            if (toolMenu_yzd.searchContent.yypc != '') {
                this.param.pcbm = toolMenu_yzd.searchContent.yypc;
            }
            // if (toolMenu_yzd.bk == '1') {
            //     this.param.ksbm = this.BrxxJson.ryks;
            // }else{
            //     this.param.ksbm='';
            // }
            if(lsyzd.is_csqx.N03003200189){
                this.param.zlyzsffhxs=lsyzd.is_csqx.N03003200189;
            }else {
                this.param.zlyzsffhxs='0';
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&hsbz=1&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
                    lsyzd.jsonList = json.d.list;
                    var list = [];
					                    for (var i = 0; i < lsyzd.jsonList.length; i++) {
						
						if(lsyzd.jsonList[i].psff && lsyzd.jsonList[i].psjg !='无'){
							let tppsff = lsyzd.jsonList[i].psff.substring(0,lsyzd.jsonList[i].psff.length-1)
							tppsff = tppsff+toolMenu_yzd.psjg_tran[lsyzd.jsonList[i].psjg]+")"
							lsyzd.jsonList[i].psff = tppsff
						}
						
						
						if (lsyzd.jsonList[i].fzh != 0) {
							if (i == 0) { // 第一个
								if (lsyzd.jsonList[i + 1] != undefined) {
									if (lsyzd.jsonList[i].fzh == lsyzd.jsonList[i + 1].fzh && lsyzd.jsonList[i].yzxh == lsyzd.jsonList[i + 1].yzxh) {
										lsyzd.jsonList[i]['tzbj'] = 'tz-start';
									}
								}
							} else if (i == lsyzd.jsonList.length - 1) { // 最后一个
								if (lsyzd.jsonList[i].fzh == lsyzd.jsonList[i - 1].fzh && lsyzd.jsonList[i].yzxh == lsyzd.jsonList[i - 1].yzxh) {
									lsyzd.jsonList[i]['tzbj'] = 'tz-stop';
								}
							} else {
								if ((lsyzd.jsonList[i].fzh != lsyzd.jsonList[i - 1].fzh || lsyzd.jsonList[i].yzxh != lsyzd.jsonList[i - 1].yzxh) && (lsyzd.jsonList[i].fzh == lsyzd.jsonList[i + 1].fzh && lsyzd.jsonList[i].yzxh == lsyzd.jsonList[i + 1].yzxh)) {
									lsyzd.jsonList[i]['tzbj'] = 'tz-start';
								} else if (lsyzd.jsonList[i].fzh == lsyzd.jsonList[i - 1].fzh && lsyzd.jsonList[i].yzxh == lsyzd.jsonList[i - 1].yzxh && lsyzd.jsonList[i].fzh == lsyzd.jsonList[i + 1].fzh && lsyzd.jsonList[i].yzxh == lsyzd.jsonList[i + 1].yzxh) {
									lsyzd.jsonList[i]['tzbj'] = 'tz-center';
								} else if ((lsyzd.jsonList[i].fzh == lsyzd.jsonList[i - 1].fzh && lsyzd.jsonList[i].yzxh == lsyzd.jsonList[i - 1].yzxh) && (lsyzd.jsonList[i].fzh != lsyzd.jsonList[i + 1].fzh || lsyzd.jsonList[i].yzxh != lsyzd.jsonList[i + 1].yzxh)) {
									lsyzd.jsonList[i]['tzbj'] = 'tz-stop';
								}
							}
						}
						
						
                        lsyzd.jsonList[i]['xmmc'] = lsyzd.jsonList[i]['xmmc']?lsyzd.jsonList[i]['xmmc'].replace('null', ''):"";
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc']?lsyzd.jsonList[i]['yyffmc'].replace('null', ''):"";
                        lsyzd.jsonList[i]['yyffmc'] = lsyzd.jsonList[i]['yyffmc']?lsyzd.jsonList[i]['yyffmc'].replace('null', ''):"";

                        if(lsyzd.sameSE(i) == 'start' || lsyzd.sameSE(i) == 'end' || lsyzd.sameSE(i) == 'all'){//输液显示单词计量，其他显示用法
                            lsyzd.jsonList[i].plusYyff = 'dcjl';
                        }
                        //list.push(lsyzd.jsonList[i]);
                        if(cqPrint.is_csqx.N03003200146 == '1'){
                        	//yysm
                            var yysm = lsyzd.jsonList[i]['yysm'];
                            // @yqq 根据用药方法 或者xmmc来将医嘱单拆分为多个医嘱显示
                            if(yysm && yysm.length > 20){
                            	var yysmArr = yysm.split('、');
                            	var newArr = [];
                            	var temp = '';
                            	var c = 20;
                            	for (var j = 0; j < yysmArr.length; j++) {
                            		var t = temp;
                            		temp += (yysmArr[j] + '、');
                            		if(temp.length > c){
                            			newArr.push(t);
                            			temp = (yysmArr[j] + '、');
                            		}
                            		if(temp.length == c || j == (yysmArr.length - 1)){
                            			newArr.push(temp);
                            			temp = '';
                            		}
                            	}

                            	for (var j = 0; j < newArr.length; j++) {
                            		var obj = JSON.parse(JSON.stringify(lsyzd.jsonList[i]));
                            		obj.yysm = newArr[j].substring(0, newArr[j].lastIndexOf('、'));
                            		list.push(obj);
                            	}

                            }else{
                            	list.push(lsyzd.jsonList[i]);
                            }
                        }else{
                        	list.push(lsyzd.jsonList[i]);
                        }

                        if(lsyzd.caqxContent.N03004200243 == '1'){
                            if(lsyzd.sameSE(i) == 'end'){
                                var obj = {
                                    yyffmc:lsyzd.jsonList[i]['yyffmc'],
                                    sysd:lsyzd.jsonList[i]['sysd'],
                                    sysddw:lsyzd.jsonList[i]['sysddw'],
                                    pcmc:lsyzd.jsonList[i]['pcmc'],
                                    ksrq:lsyzd.jsonList[i]['ksrq'],
                                    zxsj:lsyzd.jsonList[i]['zxsj'],
                                    fzh:0,
                                    end:'plus',
                                    plusYyff:'dcjl'
                                };
                                list.push(obj);
                            }
                        }
                    }


                    // 将中药处方合并显示为 汤头+剂数
                    if (lsyzd.is_csqx.N03003200133 == '1') {
                        for (var i = lsyzd.jsonList.length - 1; i > 0; i--) {
                            if (lsyzd.jsonList[i].psff == '中药' || lsyzd.jsonList[i].zytt || lsyzd.jsonList[i].sfcy=='1') {
                                if (lsyzd.jsonList[i].yzxh == lsyzd.jsonList[i - 1].yzxh) {
                                    lsyzd.jsonList.splice(i, 1);
                                } else {
                                    var xmmc = lsyzd.jsonList[i].zytt==null ? '中药一剂' : lsyzd.jsonList[i].zytt;
                                    lsyzd.jsonList[i].xmmc = xmmc + ' x ' + lsyzd.jsonList[i].zyjs;
                                    lsyzd.jsonList[i].sl = '';
                                    lsyzd.jsonList[i].sfcy = '1';
                                    lsyzd.jsonList[i].yfdwmc = '';
                                }
                            }
                        }
                    }

                    if(lsyzd.caqxContent.N03004200243 == '1'){
                        var resultList = [];
                        for (var i = 0; i < list.length; i++) {
                            resultList.push(list[i]);
                            if(list[i].xmmc){
                                if (resultList[i].xmmc.indexOf(",") != -1){
                                    countTheLine(resultList,i,resultList[i].xmmc.split(","));
                                } else {
                                    //处理嘱托医嘱以、分隔的情况
                                    countTheLine(resultList,i,resultList[i].xmmc.split("、"));
                                }
                            }
                        }
                        lsyzd.jsonList = [];
                        lsyzd.jsonList = resultList;
                    }
                } else {
                    malert("查询临时医嘱失败！");
                }
            });
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            if(!val){
				return;
			}
            var reDate = new Date(val);
            if (type == 'ry') {
                return reDate.getFullYear()+'-'+cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }else if( type =='name'){
                return  lsyzd.jsonList[index][name]
            }else if(type =='dTime'){
                return this.fDate(reDate,'short')
            }
        },
        sameDate_arrow_head: function (name, index) {
            var val = lsyzd.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if(index <= 0) return ;
            if (index != this.jsonList.length - 1 && index != 0) {
                prvVal = lsyzd.jsonList[index - 1][name];
                nextVal = lsyzd.jsonList[index + 1][name]
            }
            if(val != nextVal){
                return 'head';
            }
            if (!val && nextVal){
                return 'head';
            }
            if(index == (lsyzd.jsonList.length - 1)){
                return 'head';
            }
        },
        sameDate_arrow_head_qm: function (name, index) {
            var val = lsyzd.jsonList[index][name];
            var afterNextVal = null;
            if(index < 0 ) return;
            if (index < lsyzd.jsonList.length - 2) {
                afterNextVal = lsyzd.jsonList[index + 2][name];
            }
            if(index == lsyzd.jsonList.length - 1){
                return 'head';
            }
            if(val != afterNextVal){
                return 'head';
            }

        },
        sameDate_qm: function (name, index) {
            var val = lsyzd.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if(index == 0){
                return '';
            }
            prvVal = lsyzd.jsonList[index - 1][name]
            if(index != lsyzd.jsonList.length - 1 ){
                nextVal = lsyzd.jsonList[index + 1][name]
            }else{
                return;
            }
            if (!val && !nextVal) {
                return 'line';
            }
            if(val != prvVal && val == nextVal){
                return '';
            }
            if(val == nextVal){
                return 'line';
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                var nextKsrq = this.jsonList[index + 1]['ksrq'];
                var prvKsrq = this.jsonList[index - 1]['ksrq'];
                var ksrq = this.jsonList[index]['ksrq'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq == nextKsrq)) {
                    return 'all';
                }
                if(fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq != nextKsrq)){
                    return 'end';
                }
                if(fzh == nextFzh && fzh == prvFzh && (ksrq != prvKsrq && ksrq == nextKsrq)){
                    return 'start';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = cqPrint.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['yzxh'] && cqyzd.jsonList[index+1]['yzxh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var cqPrint = new Vue({
    el: '.cqPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        yljgmc:yljgmc,
        isShow: false,
        isPrint: false,
        list: [],
        pagePrint: 0,
        BrxxJson: yzclLeft.HszbrItem,
        isGoPrint: false,
        printGd: 20,
        is_csqx: {
            N03003200125: yzclLeft.caqxContent.N03003200125,
            N03003200133: yzclLeft.caqxContent.N03003200133,
            N03003200139: yzclLeft.caqxContent.N03003200139,
            N03003200141: yzclLeft.caqxContent.N03003200141,
            N03003200146: yzclLeft.caqxContent.N03003200146,
            N03004200265: yzclLeft.caqxContent.N03004200265,
        },
        caqxContent:yzclLeft.caqxContent
    },
    filters: {
        compuGd: function (index) {
            if (!cqyzd.isGoOn) {
                if (index >= 1) {
                    return 'paddingTop:20px'
                }
            } else {
                if (index >= 1) {
                    return 'paddingTop:20px'
                }
            }

        },
    },
    methods: {
        cssFun: function (index) {
            if (this.isPrint) {
                if (index < cqPrint.pagePrint + 1) {
                    return true
                }
                {
                    return false
                }
            }
        },
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                cqPrint.isGoPrint = false;
                printGd = 20
                $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate_arrow_head: function (name, index,num) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= cqyzd.jsonList.length) return null;
            if (indexAll <= 0) return;
            var val = cqyzd.jsonList[indexAll][name];
            var prvVal = cqyzd, nextVal = null;
            if (indexAll != cqyzd.jsonList.length - 1 && indexAll != 0) {
                prvVal = cqyzd.jsonList[indexAll - 1][name];
                nextVal = cqyzd.jsonList[indexAll + 1][name]
            }

            if(indexAll == ((num + 1) * 20) - 2){//每一页倒数第二个必须箭头，但不一定是line
                return 'head';
            }

            if(val != nextVal){
                return 'head';
            }
            if (!val && nextVal){
                return 'head';
            }
            if(indexAll == (cqyzd.jsonList.length - 1)){
                return 'head';
            }
        },
        sameDate: function (name, index, num, type) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[indexAll][name];
            var ksrq=cqyzd.jsonList[indexAll]['ksrq'];

            var prvVal = cqyzd, nextVal = null,prvKsrq=null,nextKsrq=null;
            if(indexAll != 0){
                prvKsrq=cqyzd.jsonList[indexAll-1]['ksrq'];
                prvVal = cqyzd.jsonList[indexAll - 1][name];
            }else {
                prvVal = cqyzd.jsonList[indexAll][name];
            }
            if (indexAll != cqyzd.jsonList.length - 1) {
                nextKsrq=cqyzd.jsonList[indexAll+1]['ksrq'];
                nextVal = cqyzd.jsonList[indexAll + 1][name]
            }

            if((indexAll == ((num + 1) * 20) - 1) || (indexAll == (num * 20))){//每一页倒数第一个必须展示时间
                if(val){
                    return this.setDate(val, type,indexAll,name);
                }else{
                    return '';
                }
            }

            if (!val) {
                if(cqPrint.is_csqx.N03003200142 == '1'){
                    return 'line';
                }else{
                    if (val == prvVal && val == nextVal){
                        return '';
                    } else {
                        return  ''
                    }
                }
            }
            if (num >= 1 && indexAll == (num * 20) && cqyzd.jsonList[num * 20][name]) {
                return this.setDate(val, type,indexAll,name)
            }

            if(cqPrint.is_csqx.N03003200142 == '1'){
                if (val == prvVal && indexAll != 0) {
                    return 'line';
                }
            }else{
                if(index != 0) {
                    if (ksrq ==prvKsrq && val == prvVal   && val == nextVal) {
                        if(cqPrint.caqxContent.N03004200269 != '1'){
                            return '""';
                        }
                    }
                }
            }
            return this.setDate(val, type,indexAll,name)
        },
        sameDate_arrow_head_qm: function (name, index,num) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[indexAll][name];
            var afterNextVal = null;
            if(indexAll < 0 ) return;
            if (indexAll < cqyzd.jsonList.length - 2) {
                afterNextVal = cqyzd.jsonList[indexAll + 2][name];
            }
            if(indexAll == cqyzd.jsonList.length - 1){
                return 'head';
            }
            if(indexAll == ((num + 1) * 20) - 1){//每页最后一行留空格
                return '';
            }
            if(indexAll == ((num + 1) * 20) - 2){//每页倒数第二行箭头
                return 'head';
            }

            if(val != afterNextVal){
                return 'head';
            }
        },

        sameDate_qm: function (name, index, num) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[indexAll][name];
            var prvVal = null, nextVal = null;
            if(index == 0){
                return '';
            }
            prvVal = cqyzd.jsonList[indexAll - 1][name]
            if(indexAll != cqyzd.jsonList.length - 1 ){
                nextVal = cqyzd.jsonList[indexAll + 1][name]
            }else{
                return;
            }
            if(indexAll == ((num + 1) * 20) - 1){//每一页最后一行空着签字
                return '';
            }
            if (!val && !nextVal) {
                return 'line';
            }
            if(val != prvVal && val == nextVal){
                return '';
            }
            if(val == nextVal){
                return 'line';
            }
        },
        setDate: function (val, type,index,name) {
			
			if(!val){
				return;
			}
            var reDate = new Date(val);
            if (type == 'ry') {
                return reDate.getFullYear().toString().substring(2,4)+'-'+cqPrint.Appendzero((reDate.getMonth() + 1)) + '-' + cqPrint.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqPrint.Appendzero(reDate.getHours()) + ':' + cqPrint.Appendzero(reDate.getMinutes());
            }else if(type =='name'){
                return cqyzd.jsonList[index][name]
            }
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        sameSE: function (index, num) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= cqyzd.jsonList.length) return null;
            var fzh = cqyzd.jsonList[indexAll]['fzh'];
            if (fzh == 0) return false;
            var ksrq = cqyzd.jsonList[indexAll]['ksrq'];
            if(indexAll != 0){
                var prvFzh = cqyzd.jsonList[indexAll - 1]['fzh'];
                var prvKsrq = cqyzd.jsonList[indexAll - 1]['ksrq'];
            }
            if(indexAll != cqyzd.jsonList.length - 1){
                var nextFzh = cqyzd.jsonList[indexAll + 1]['fzh'];
                var nextKsrq = cqyzd.jsonList[indexAll + 1]['ksrq'];
            }
            if (indexAll == 0 && (fzh == nextFzh && ksrq == nextKsrq)) {
                return 'start';
            }
            if(indexAll != 0 && indexAll != cqyzd.jsonList.length - 1){
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if ((fzh == nextFzh && ksrq == nextKsrq) && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && (fzh == prvFzh && ksrq == prvKsrq)) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq == nextKsrq)) {
                    return 'all';
                }
                if(fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq != nextKsrq)){
                    return 'end';
                }
                if(fzh == nextFzh && fzh == prvFzh && (ksrq != prvKsrq && ksrq == nextKsrq)){
                    return 'start';
                }
                if (indexAll == cqyzd.jsonList.length - 1 && (fzh == prvFzh && ksrq == prvKsrq)) {
                    return 'end'
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['yzxh'] == cqyzd.jsonList[index+1]['yzxh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});
var lsPrint = new Vue({
    el: '.lsPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        yljgmc:yljgmc,
        isShow: false,
        isPrint: false,
        list: [],
        is_csqx: {
            N03003200125: yzclLeft.caqxContent.N03003200125,
            N03003200133: yzclLeft.caqxContent.N03003200133,
            N03003200139: yzclLeft.caqxContent.N03003200139,
            N03003200141: yzclLeft.caqxContent.N03003200141,
            N03004200265: yzclLeft.caqxContent.N03004200265,
        },
        pagePrint: 0,
        BrxxJson: yzclLeft.HszbrItem,
        isGoPrint: false,
        caqxContent:yzclLeft.caqxContent
    },
    filters: {
        compuGd: function (index) {
            if (!cqyzd.isGoOn) {
                if (index >= 1) {
                    return 'paddingTop:19px'
                }
            } else {
                if (index >= 1) {
                    return 'paddingTop:20px'
                }
            }

        },
    },
    methods: {
        cssFun: function (index) {
            if (this.isPrint) {
                if (index == 0) {
                    return true
                }
                {
                    return false
                }
            }
        },
        print: function () {
            window.print();
        },
        goOnPrint: function () {
            this.isGoPrint = true;
            $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid transparent');
            setTimeout(function () {
                window.print();
                lsPrint.isGoPrint = false;
                $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border', '1px solid #999');
            }, 100);
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate_arrow_head: function (name, index,num) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[indexAll][name];
            var prvVal;
            var nextVal;
            if (indexAll != 0 && indexAll != (lsyzd.jsonList.length - 1)) {
                prvVal = lsyzd.jsonList[indexAll - 1][name];
                nextVal = lsyzd.jsonList[indexAll + 1][name];
            }
            if (num >= 1 && indexAll == (num * 20) && lsyzd.jsonList[num * 20][name]) {
                return cqPrint.setDate(val, type,indexAll,name)
            }
            if(val != nextVal){
                return 'head';
            }
            if (!val && nextVal){
                return 'head';
            }
            if(indexAll == (lsyzd.jsonList.length - 1)){
                return 'head';
            }
            if(indexAll == ((num + 1) * 20) - 2){//每页倒数第二行箭头
                return 'head';
            }
        },
        sameDate: function (name, index, num, type) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[indexAll][name];
            
            return this.setDate(val, type,indexAll,name)
        },
        setDate: function (val, type,index,name) {
			if(!val){
				return;
			}
            var reDate = new Date(val);
            if (type == 'ry') {
                return reDate.getFullYear()+'-'+cqPrint.Appendzero((reDate.getMonth() + 1)) + '-' + cqPrint.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqPrint.Appendzero(reDate.getHours()) + ':' + cqPrint.Appendzero(reDate.getMinutes());
            }else if(type =='name'){
                return lsyzd.jsonList[index][name]
            }else if(type =='dTime'){
                return this.fDate(reDate,'short')
            }
        },
        sameDate_arrow_head_qm: function (name, index, num) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[indexAll][name];
            var afterNextVal = null;
            if(indexAll < 0 ) return;
            if (indexAll < lsyzd.jsonList.length - 2) {
                afterNextVal = lsyzd.jsonList[indexAll + 2][name];
            }
            if(indexAll == ((num + 1) * 20) - 1){//每页最后一行留空格
                return '';
            }
            if(indexAll == ((num + 1) * 20) - 2){//每页倒数第二行箭头
                return 'head';
            }
            if(indexAll == lsyzd.jsonList.length - 1){
                return 'head';
            }
            if(val != afterNextVal){
                return 'head';
            }

        },
        sameDate_qm: function (name, index,num) {
            var indexAll = this.toIndex(index, num);
            if (indexAll >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[indexAll][name];
            var prvVal = null, nextVal = null;
            if(index == 0){
                return '';
            }
            if(indexAll == ((num + 1) * 20) - 1){
                return '';
            }
            prvVal = lsyzd.jsonList[indexAll - 1][name];
            if(indexAll != lsyzd.jsonList.length - 1 ){
                nextVal = lsyzd.jsonList[indexAll + 1][name]
            }else{
                return;
            }
            if (!val && !nextVal) {
                return 'line';
            }
            if(val != prvVal && val == nextVal){
                return '';
            }
            if(val == nextVal){
                return 'line';
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) {
                return null;
            }
            var fzh = lsyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == lsyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != lsyzd.jsonList.length - 1) {
                var nextFzh = lsyzd.jsonList[index + 1]['fzh'];
                var prvFzh = lsyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            if (lsyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (lsyzd.jsonList[index]['fzh'] == lsyzd.jsonList[index + 1]['fzh'] && lsyzd.jsonList[index]['yzxh'] == lsyzd.jsonList[index+1]['yzxh']) {
                if (lsyzd.jsonList[index]['yyffmc'] == lsyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

if(yzclLeft.caqxContent.N03003200141 == '1'){
    cqyzd.is_csqx.show='1';
    cqPrint.is_csqx.show='1';
    lsyzd.is_csqx.show='1';
    lsPrint.is_csqx.show='1';
}else if (yzclLeft.caqxContent.N03003200141 == '2'){
    cqyzd.is_csqx.show='1';
    cqPrint.is_csqx.show='1';
    lsyzd.is_csqx.show='0';
    lsPrint.is_csqx.show='0';
}else if(yzclLeft.caqxContent.N03003200141 == '3'){
    cqyzd.is_csqx.show='0';
    cqPrint.is_csqx.show='0';
    lsyzd.is_csqx.show='1';
    lsPrint.is_csqx.show='1';
}
toolMenu_yzd.iscf();
// toolMenu_yzd.initData();
yzclLeft.$watch('HszbrItem', function (newVal, oldVal) {
    console.log('yzd')
    if (newVal.zyh != oldVal.zyh && this.index==10) {
        cqyzd.BrxxJson = JSON.parse(JSON.stringify(newVal))
        lsyzd.BrxxJson = JSON.parse(JSON.stringify(newVal))
        lsPrint.BrxxJson = JSON.parse(JSON.stringify(newVal))
        cqPrint.BrxxJson = JSON.parse(JSON.stringify(newVal))
        toolMenu_yzd.oldbrxxContent = JSON.parse(JSON.stringify(newVal));
        cqyzd.getData();
        lsyzd.getData();
    }

})

//递归计算行数
function countTheLine(list,index,xmmcList){
    list[index].xmmc = list[index].xmmc.replace(/,/g,"、");
    if(list[index].ypbz == '0' && getCharCodeLen(list[index].xmmc) > 59){//
        var xmmc = "";//当前合并项目名称
        var restXmmc = "";//剩余名称
        var otherLineList = [];//剩余的另起行
        list[index].doubleLine = '1';//合并一个div标志
        if(getCharCodeLen(list[index].xmmc) > 118){//按打印显示118个字符
            var elseIf = false;//数组顺序拼接，一旦超长，后面不做判断，全部放入下一次循环
            for (var i = 0; i < xmmcList.length; i++) {
                if(!elseIf){
                    if(getCharCodeLen(xmmc + xmmcList[i]) <= 118){//每行只显示118个字符，不然影响打印格式
                        xmmc += xmmcList[i] + '、';//拼接，按中文 '、'隔开
                    }else{//第一次超过118个字符部分，进行下一次循环
                        elseIf = true;
                        restXmmc += xmmcList[i] + '、';
                        otherLineList.push(xmmcList[i]);
                    }
                }else{//剩余超过118部分
                    restXmmc += xmmcList[i] + '、';
                    otherLineList.push(xmmcList[i]);
                }
            }
            list[index].xmmc = xmmc;//将本次拼接部分赋值
            if(otherLineList.length > 0){//如果有剩余部分，进行下一次循环
                list.push(JSON.parse(JSON.stringify(list[index])));//序列化，产生新的对象，不然指向同一内存地址，是一个对象
                list[index+1].xmmc = restXmmc;//剩余名字赋值，准备递归
                countTheLine(list,(index + 1),otherLineList);//递归调用
            }
        }
        return list;
    }
}
//计算中文字符
function getCharCodeLen(str){
    var   i,len,code;
    if(!str)   return   0;
    len = str.length;
    for   (i=0;i<str.length;i++) {
        code = str.charCodeAt(i);
        if   (code > 255) {
            len ++;
        }
    }
    return len;
}
