var panel = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        index: 0,
        menuTitle: true,
        fa: 0,
        userList: [],
        newLocalValue: '', //监听是否需要刷新
        csqxContent: {cs03001200132: "0"}, //参数对象
        ksList: [], //科室下拉
        param: { //查询条件
            page: 1,
            rows: 9999,
            sort: "",
            mtfa: 0,
            order: 'desc',
            parm: "",
            beginrq: "",
            endrq: "",
            jzbz: "9",
            ghks: "",
            jzys: "",
            brgl: '0'
        },
        page: {
            page: 1,
            rows: 30,
            sort: '',
            order: 'asc'
        },
        total: 0,
        mzbrgl_tran: {
            '0': '全科病人',
            '1': '接诊病人'
        },
        parm: {},
        jhksObj: {
            ghksip: ''
        },
        jhksbm: '',
        beginrq: '',
        endrq: '',
        userInfo: {}
    },
    created: function () {
        // this.$nextTick(function () {
        this.getKsbm();
        // })
    },
    watch: {
        'newLocalValue': function (o, n) {
            // panel.Wf_GetBrList();
        }
    },
    mounted: function () {

        //初始化检索日期！为今天0点到今天24点
        var myDate = new Date();
        this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()), 'date') + ' 00:00:00';
        this.beginrq = this.fDate(myDate.setDate(myDate.getDate()), 'date') + ' 00:00:00';
        this.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';
        this.param.endrq = this.fDate(new Date(), 'date') + ' 23:59:59';

        laydate.render({
            elem: '#timeVal',
            type: 'datetime',
            value: this.param.beginrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                panel.param.beginrq = value;
                panel.beginrq = value;
                panel.Wf_GetBrList(String);
            }
        });
        laydate.render({
            elem: '#timeVal1',
            type: 'datetime',
            value: this.param.endrq,
            rigger: 'click',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                panel.param.endrq = value;
                panel.endrq = value;
                //获取一次列表
                panel.Wf_GetBrList(String);
            }
        });
    },
    methods: {
        faFun: function (val) {
            this.param.mtfa = val[1];
            this.$forceUpdate();
            this.getData()
        },
        initModel: function () {
            if (this.menuTitle) {
                this.initPopData()
            } else {
                clearInterval(this.timeID)
            }
        },
        showDate: function (elm, code) {
            laydate.render({
                elem: '#' + elm
                , show: true //直接显示
                , type: 'date'
                , theme: '#1ab394',
                done: function (value, data) {
                    navli[code] = value

                }
            })
        },
        initPopData: function () {
            if (!this.timeID) {
                this.getPopData()
            }
            this.timeID = setInterval(function () {
                J_tabLeft.getPopData()
            }, 1800000)
        },
        getOne: function (index, item) {
            var urlToke = 'http://' + location.hostname + ':8888/sxzz-api/TransferServer/EncString';
            var pra = {encstrencryption: jgbm + new Date().getTime().toString()};
            this.$http.post(urlToke, JSON.stringify(pra)).then(function (data) {
                if (data.status == 200) {
                    var param = {
                        timestamp: new Date().getTime().toString(),
                        token: data.data.data,
                        accesscode: jgbm,
                        trans_date: getTodayDate(),
                        orgcode: jgbm,
                        serialnumber: item.serialnumber,
                        deptcode: this.userInfo.ksbm,
                        deptname: this.userInfo.ksmc,
                        doctorcode: this.userInfo.czybm,
                        doctorname: this.userInfo.czyxm,
                        appointmenttype: '0'
                    };
                    url = 'http://' + location.hostname + ':8888/sxzz-api/TransferServer/saveVerifyAndTriageAndDirect';
                    this.$http.post(url, JSON.stringify(param)).then(function (data) {
                        if (data.status == 200) {
                            malert("接诊成功");
                            J_tabLeft.getPopData();
                        }
                        ;
                    });

                }
            });
        },
        getPopData: function () {
            var url = 'http://' + location.hostname + ':8888/sxzz-api/TransferServer/selectQueryVerify';

            var urlToke = 'http://' + location.hostname + ':8888/sxzz-api/TransferServer/EncString';
            var tokenn = '';
            var pra = {encstrencryption: jgbm + new Date().getTime().toString()};
            this.$http.post(urlToke, JSON.stringify(pra)).then(function (data) {
                if (data.status == 200) {

                    var param = {
                        timestamp: new Date().getTime().toString(),
                        token: data.data.data,
                        accesscode: jgbm,
                        trans_date: getTodayDate(),
                        starttime: this.ksrq,
                        endtime: this.jsrq,
                        orgcode: jgbm
                    };
                    var json = JSON.stringify(param);
                    panel.$http.post(url, json).then(function (data) {
                        if (data.body.status == 200) {
                            J_tabLeft.userList = data.body.data;
                        } else {
                            // malert("上传数据失败" + data.body.c);
                        }
                    })


                } else {
                    // malert("上传数据失败" + data.body.c);
                }
            })
        },
        getQuery: function (name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
            var r = window.top.location.search.substr(1).match(reg);
            if (r != null) {
                return unescape(r[2]);
            }
            return null;
        },
        // @yqq 排队叫号>>> 加载叫号人员信息
        loadUserInfo: function () {
            $.ajax({
                type: 'get',
                url: panel.csqxContent.N03001200131 + '/findByRybm/' + userId,
                async: true,
                success: function (json) {
                    if (json.code == 0) {
                        panel.userInfo = json.data;
                    }
                },
                error: function (response) {
                }
            });
        },
        tab: function (index) {
            this.index = index;
            zuiItem.index = index;
            kp.index = index;
            changeWin();
            panel.Wf_GetBrList(String);
        },
        getData: function () {
            panel.Wf_GetBrList(String)
        },
        getList: function () {
            var url = window.location.href;
        },
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            var parm = {
                "ylbm": 'N030012001',
                "ksbm": this.param.ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0 && json.d && json.d.length > 0) {
                    for (var i = 0; i < json.d.length; i++) {
                        var csjson = json.d[i];
                        switch (csjson.csqxbm) {
                            case "N03001200101": //是否强行接诊  0=不允许,1=允许
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100101 = csjson.csz;
                                }
                                break;
                            case "N03001200102": //自动获取就诊病人刷新时间0=手动刷新,大于零时为刷新间隔秒数
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100102 = csjson.csz;
                                }
                                break;
                            case "N03001200103": //门诊医生站默认药房 （选择默认药房）
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100103 = csjson.csz;
                                }
                                break;
                            case "N03001200104": //是否必须录入门诊诊断0=否，1＝是
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100104 = csjson.csz;
                                }
                                break;
                            case "N03001200105": //门诊医生接诊范围0=本人，1=全院，2=全科
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100105 = csjson.csz;
                                    if (csjson.csz == "1") {
                                        panel.mzbrgl_tran["2"] = "全院病人";
                                    }
                                }
                                break;
                            case "N03001200106": //电子处方默认处方类型（输入药品处方类型编码）
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100106 = csjson.csz;
                                }
                                break;
                            case "N03001200107": //是否强行限定药品处方位数0=否,1=是
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100107 = csjson.csz;
                                }
                                break;
                            case "N03001200108": //病人信息修改权限0=否,1=是
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100108 = csjson.csz;
                                }
                                break;
                            case "N03001200109": //甘肃省疾病普排序接口0=否 1=是(接诊按接口规范录入病人信息和门诊诊断)  2=是(接诊时不强制输入，以后再补)
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100109 = csjson.csz;
                                }
                                break;
                            case "N03001200110": //保存电子处方时是否打印0=无，1=有
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100110 = csjson.csz;
                                }
                                break;
                            case "N03001200111": //是否限制一个挂号序号只能开一张处方0-不限定,1限定
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100111 = csjson.csz;
                                }
                                break;
                            case "N03001200112": //申请单的打印方式0-不打印，1-提示打印，2-直接打印
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100112 = csjson.csz;
                                }
                                break;
                            case "N03001200113": //保存时是否签名0=否，1=是
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100113 = csjson.csz;
                                }
                                break;
                            case "N03001200114": //开电子申请单方式1-医嘱,2-项目格式
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100114 = csjson.csz;
                                }
                                break;
                            case "N03001200115": //中医处方是否必须填写主症、治法	0-否，1-是
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100115 = csjson.csz;
                                }
                                break;
                            case "N03001200116": //处方金额是否允许为零0-不允许，1-允许
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100116 = csjson.csz;
                                }
                                break;
                            case "N03001200117": //急诊挂号对应急诊处方（急诊挂号对应急诊处方）
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100117 = csjson.csz;
                                }
                                break;
                            case "N03001200118": //中药处方数量允许输入小数0=不允许 1=允许
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100118 = csjson.csz;
                                }
                                break;
                            case "N03001200119": //是否允许开非药品处方0=否 1=是
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100119 = csjson.csz;
                                }
                                break;
                            case "N03001200120": //中药煎药袋参数
                                if (csjson.csz) {
                                    panel.csqxContent.cs00600100120 = csjson.csz;
                                }
                                break;
                            case "N01006400108": //合理用药接口地址参数
                                if (csjson.csz) {
                                    panel.csqxContent.cs01006400108 = csjson.csz;
                                }
                                break;
                            case "N03003200118": //是否对抗生素药品使用限制
                                if (csjson.csz) {
                                    panel.csqxContent.cs003003200118 = csjson.csz;
                                }
                                break;
                            case "N03003200126": //是否对抗肿瘤药品使用限制0-否，1-是
                                if (csjson.csz) {
                                    panel.csqxContent.cs003003200126 = csjson.csz;
                                }
                                break;
                            case "N03003200127": //是否对药品种类中成药使用限制0-否，1-是
                                if (csjson.csz) {
                                    panel.csqxContent.cs003003200127 = csjson.csz;
                                }
                                break;
                            case "N03001200125": //甘肃双向转诊门诊转出参数

                                if (csjson.csz && csjson.csz == '1') {
                                    kp.isShow = true;
                                }
                                break;
                            case "N03001200131": //排队叫号请求地址
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200131 = csjson.csz;
                                }
                                break;
                            case "N03001200132": //门诊用药使用服药剂量
                                if (csjson.csz) {
                                    panel.csqxContent.cs03001200132 = csjson.csz;
                                }
                                break;
                            case "N03001200133": //排队叫号方式
                                if (csjson.csz) {
                                    // @yqq 排队叫号>>> 初始化人员数据
                                    panel.csqxContent.N03001200133 = csjson.csz;
                                    panel.loadUserInfo();


                                    if (panel.jhksObj) { // 如果没有设置默认叫号科室和叫号ip
                                        if (sessionStorage.getItem('jhksObj')) {
                                            panel.jhksObj = JSON.parse(sessionStorage.getItem('jhksObj'));
                                            if (panel.jhksObj && panel.jhksObj.ghksip) {
                                                panel.jhksbm = panel.jhksObj.ksbm;
                                            }
                                        }
                                    }

                                }
                                break;
                            case "N03001200138": //门诊医生跳转补挂号
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200138 = csjson.csz;
                                }
                                break;
                            case "N03001200151": //接诊列表是否过滤留观病人
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200151 = csjson.csz;
                                }
                                break;
                            case "N03001200126": // 门诊入院打印
                                panel.csqxContent.cs03001200126 = csjson.csz;
                                break;
                            case "N03001200146": //病人接诊是否使用贵州农合诊断代码库
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200146 = csjson.csz;
                                }
                                break;
                            case "N03001200147": //病人接诊是否使用贵州农合诊断代码库
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200147 = csjson.csz;
                                }
                                break;
                            case "N03001200150": //是否显示其他诊断 0 不显示 1 显示
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200150 = csjson.csz;
                                }
                                break;
                            case "N03001200134": //急诊接诊病人信息是否必填
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200134 = csjson.csz;
                                }
                                break;
                            case "N03001200159": //是否可以同时开几个病人接诊界面 1是 0否
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200159 = csjson.csz;
                                }
                                break;
                            case "N03001200161": //使用健康卡（宣汉）0-否 1-是
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200161 = csjson.csz;
                                }
                                break;
                            case "N03001200163": //翼展Pacs服务地址（门诊）
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200163 = csjson.csz;
                                }
                                break;
                            case "N05001200253": //35岁以上血压是否必填 0-否 1-提醒 2-必填
                                if (csjson.csz) {
                                    panel.csqxContent.N05001200253 = csjson.csz;
                                }
                                break;
                            case "N05001200255": //是否显示民族
                                if (csjson.csz) {
                                    panel.csqxContent.N05001200255 = csjson.csz;
                                }
                                break;
                            case "N03001200171": //电子健康卡程序地址（宣汉）
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200171 = csjson.csz;
                                }
                                break;
                            case "N03001200175": //武汉患者情况  接诊登记表科室编码
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200175 = csjson.csz;
                                }
                                break;
                            case "N05001200264": //诊是否可以跳过接诊直接开药
                                if (csjson.csz) {
                                    panel.csqxContent.N05001200264 = csjson.csz;
                                }
                                break;
                            case "N05001200269": //调阅报告是否显示  1 显示 0不显示
                                if (csjson.csz) {
                                    panel.csqxContent.N05001200269 = csjson.csz;
                                }
                                break;
                            case "N05001200270": //建筑医院处方保存后推送消息请求地址和参数|分割
                                if (csjson.csz) {
                                    panel.csqxContent.N05001200270 = csjson.csz;
                                }
                                break;
                            case "N05001200271": //35岁不必填信息
                                if (csjson.csz) {
                                    panel.csqxContent.N05001200271 = csjson.csz;
                                }
                                break;
                            case "N03001200176": //强制刷新诊断地址
                                if (csjson.csz) {
                                    panel.csqxContent.N03001200176 = csjson.csz;
                                }
                                break;
                        }
                    }
                } else {
                    malert('参数权限获取失败' + json.c, 'top', 'defeadted')
                }
            });
        },

        //获取当前操作员的拥有科室权限
        getKsbm: function () {
            common.openloading('.wrapper');
            var str_param = {
                ylbm: "N030012001"
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxks&parm=" + JSON.stringify(str_param), function (json) {
                if (json.a == 0 && json.d != null) {
                    if (json.d.length > 0) {
                        panel.ksList = json.d;
                        panel.param.ksbm = panel.ksList[0].ksbm;

                        if (panel.ksList[0].ksmc.indexOf('急诊') != -1) {
                            panel.mzbrgl_tran = {
                                '0': '全科病人',
                                '1': '接诊病人',
                                '2': '全院病人'
                            }
                        } else {
                            panel.mzbrgl_tran = {
                                '0': '全科病人',
                                '1': '接诊病人'
                            }
                        }

                        panel.$forceUpdate();

                        setTimeout(function () {
                            panel.getCsqx();//科室记载完成之后再加载参数权限
                            setTimeout(function () {
                                if (panel.getQuery('IsNextPage')) {
                                    panel.Wf_GetBrList(String, true); //加载完成后自动获取就诊病人列表
                                } else {
                                    panel.Wf_GetBrList(undefined); //加载完成后自动获取就诊病人列表
                                }
                            }, 100);
                        }, 100);

                    }
                } else {
                    malert('获取科室失败', 'top', 'defeadted')
                }
            });
        },

        //科室改变时
        ksChange: function (val) {
            //先获取住院医生的值
            this.Wf_GetBrList(val);
            panel.getCsqx();//科室改变之后再加载参数权限
            kp.brk_list();
            if (val[4].indexOf('急诊') != -1) {
                panel.mzbrgl_tran = {
                    '0': '全科病人',
                    '1': '接诊病人',
                    '2': '全院病人'
                }
            } else {
                panel.mzbrgl_tran = {
                    '0': '全科病人',
                    '1': '接诊病人'
                }
                if (panel.param.brgl == '2') {
                    panel.param.brgl = '1';
                }
            }

            panel.$forceUpdate();
        },

        // @yqq 排队叫号 >>> 科室改变时查询出叫号科室的ip
        queryKsip: function (val) {
            $.ajax({
                type: 'get',
                url: panel.csqxContent.N03001200131 + '/queryKsxx/' + val[0],
                async: true,
                success: function (json) {
                    if (json.data.ghksip) {
                        var r = confirm("是否设置" + json.data.ksmc + "为当前叫号科室？")
                        if (r == true) {
                            malert('设置成功');
                            panel.jhksbm = val[0];
                            panel.jhksObj = json.data;
                            sessionStorage.setItem('jhksObj', JSON.stringify(json.data));

                            if (panel.userInfo) {
                                obj.jzys = panel.userInfo.jzys,
                                    obj.jzysxm = panel.userInfo.jzysxm,
                                    obj.ghks = panel.jhksObj.ksbm,
                                    obj.ghksmc = panel.jhksObj.ksmc,
                                    obj.ghksip = panel.jhksObj.ghksip;
                                obj.jzystx = panel.csqxContent.N03001200131.replace('12222', '12223') + '/images/' + panel.userInfo.jzystx;
                                obj.yszc = panel.userInfo.yszc;
                                obj.ysjj = panel.userInfo.ysjj;
                                obj.kslc = panel.jhksObj.kslc;
                            }

                            $.ajax({
                                type: 'post',
                                url: panel.csqxContent.N03001200131 + '/showData',
                                data: JSON.stringify(obj),
                                async: true,
                                dataType: 'json',
                                contentType: "application/json;charset=utf-8",
                                success: function (json) {
                                    if (json.code == 0) {
                                        malert('屏幕初始化成功...')
                                    } else {
                                        malert(json.msg, 'top', 'defeadted')
                                    }
                                }
                            });
                        } else {
                            malert('取消设置！', 'top', 'defeadted')
                        }
                    } else {
                        panel.jhksbm = null;
                        sessionStorage.setItem('jhksObj', JSON.stringify('{}'));
                        malert('未设置叫号科室ip，请联系管理员！', 'top', 'defeadted')
                    }
                },
                error: function (response) {
                }
            });
        },
        nextJh: function () {
            if (!kp.kpSelect) {
                --kp.kpIndex
            }
            kp.kpIndex = kp.kpIndex == -1 ? kp.Brxx_List.length - 1 : kp.kpIndex
            kp.jhcz(kp.Brxx_List[kp.kpIndex])
            kp.kpSelect = false;
        },
        restJh: function () {
            kp.jhcz(kp.Brxx_List[kp.kpIndex])
        },
        toGh: function () {
            var bgh = {
                bgh: '0',
                ghks: this.param.ksbm,
            }
            sessionStorage.setItem('bgh', JSON.stringify(bgh));
            this.topNewPage('挂号退号', 'page/ghgl/ghyw/brgh/brgh.html');
        },
        //获取病人信息
        Wf_GetBrList: function (val, event) {
            if (this.csqxContent.N03001200161 == '1' && this.param.parm && this.param.parm.length == 66) {
                if (this.param.parm.indexOf(":") != -1) {
                    this.param.parm = panel.param.parm.substr(0, this.param.parm.lastIndexOf(":"));
                }
            }

            this.param.endrq = this.endrq;
            this.param.beginrq = this.beginrq;
            zuiItem.Brxx_List = [];
            if (val) kp.Brxx_List = [];
            kp.brk_listD = 0;
            if (typeof val == 'object') {
                this.param.page = 1
                if (val[2][1] == 'ksbm') {
                    Vue.set(this.param, 'ksbm', val[0]);
                } else if (val[2][1] == 'brgl') {
                    Vue.set(this.param, 'brgl', val[0]);
                } else if (val[2][1] == 'jzbz') {
                    Vue.set(this.param, 'jzbz', val[0]);
                    if (val[0] == 2) {
                        Vue.set(this.param, 'wcbz', '1');
                    } else if (val[0] == 9) {
                        Vue.set(this.param, 'jzbz', '9');
                        Vue.set(this.param, 'wcbz', null);
                    } else {
                        Vue.set(this.param, 'jzbz', val[0]);
                        Vue.set(this.param, 'wcbz', '0');
                    }
                } else if (val[2][1] == 'brgl') {
                    Vue.set(this.param, 'jzbz', val[0]);
                    if (val[0] == 2) {
                        Vue.set(this.param, 'wcbz', '1');
                    } else if (val[0] == 9) {
                        Vue.set(this.param, 'jzbz', null);
                        Vue.set(this.param, 'wcbz', null);
                    } else {
                        Vue.set(this.param, 'jzbz', val[0]);
                        Vue.set(this.param, 'wcbz', '0');
                    }
                }
            }
            if (this.param.brgl == "2") {
                Vue.set(this.csqxContent, "cs00600100105", "1");
            } else if (this.param.brgl == "0") {
                Vue.set(this.csqxContent, "cs00600100105", "2");
            } else {
                Vue.set(this.csqxContent, "cs00600100105", "0");
            }
            this.$forceUpdate();
            //门诊医生接诊范围0=本人，1=全院，2=全科
            if (this.csqxContent.cs00600100105 == '0') {
                this.param.jzys = userId;
                this.param.ghks = this.param.ksbm;
            } else if (this.csqxContent.cs00600100105 == '1') {
                this.param.jzys = null;
                this.param.ghks = null;
            } else if (this.csqxContent.cs00600100105 == '2') {
                this.param.jzys = null;
                this.param.ghks = this.param.ksbm;
                if (this.param.brgl == '1') {
                    this.param.jzys = userId;
                }
            }

            if (this.csqxContent.N03001200151 && this.csqxContent.N03001200151 == '1') {
                this.param.lgflag = 2;
            }

            this.param.rows = this.index ? zuiItem.param.rows : kp.param.rows;
            this.param.sort = 'ghxh';
            this.param.order = 'desc';
            $.ajax({
                type: 'get',
                url: '/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=queryJzlb&parm=' + JSON.stringify(this.param),
                async: true,
                dataType: 'json',
                success: function (json) {
                    if (json.a == 0 && json.d != null) {
                        kp.scollType = true
                        kp.isDoneCb = false;
                        panel.total = json.d.total;
                        if (json.d.list.length > 0) {
                            for (var i = 0; i < json.d.list.length; i++) {
                                //把时间戳改改时间
                                json.d.list[i].ghrq = formatTime(json.d.list[i].ghrq, "datetime");
                                json.d.list[i].qhrq = formatTime(json.d.list[i].qhrq, "datetime");
                                //判断年龄阶段的1、男儿童，2、女儿童(0-6);3、男少年，4、女少年(7-17);5、男青年，6、女青年（18-40）；7、男中年，8女中年（41-65）；9、男老年，10、女老年（66以后）
                                if (json.d.list[i].brnl < 7 && json.d.list[i].brxb == '1') {
                                    json.d.list[i].nljd = '1';
                                } else if (json.d.list[i].brnl < 7 && json.d.list[i].brxb == '2') {
                                    json.d.list[i].nljd = '2';
                                } else if (json.d.list[i].brnl < 18 && json.d.list[i].brnl > 6 && json.d.list[i].brxb == '1') {
                                    json.d.list[i].nljd = '3';
                                } else if (json.d.list[i].brnl < 18 && json.d.list[i].brnl > 6 && json.d.list[i].brxb == '2') {
                                    json.d.list[i].nljd = '4';
                                } else if (json.d.list[i].brnl < 41 && json.d.list[i].brnl > 17 && json.d.list[i].brxb == '1') {
                                    json.d.list[i].nljd = '5';
                                } else if (json.d.list[i].brnl < 41 && json.d.list[i].brnl > 17 && json.d.list[i].brxb == '2') {
                                    json.d.list[i].nljd = '6';
                                } else if (json.d.list[i].brnl < 66 && json.d.list[i].brnl > 40 && json.d.list[i].brxb == '1') {
                                    json.d.list[i].nljd = '7';
                                } else if (json.d.list[i].brnl < 66 && json.d.list[i].brnl > 40 && json.d.list[i].brxb == '2') {
                                    json.d.list[i].nljd = '8';
                                } else if (json.d.list[i].brnl > 65 && json.d.list[i].brxb == '1') {
                                    json.d.list[i].nljd = '9';
                                } else if (json.d.list[i].brnl > 65 && json.d.list[i].brxb == '2') {
                                    json.d.list[i].nljd = '10';
                                } else {
                                    json.d.list[i].nljd = '11';
                                }
                                //状态
                                if (json.d.list[i].jzbz == '0' && json.d.list[i].wcbz == '0') {
                                    json.d.list[i].zt = '0';
                                } else if (json.d.list[i].jzbz == '1' && json.d.list[i].wcbz == '0') {
                                    json.d.list[i].zt = '1';
                                } else if (json.d.list[i].wcbz == '1') {
                                    json.d.list[i].zt = '2';
                                }
                                //获取时间
                                var times = getxcsfm(json.d.list[i].ghrq);
                                json.d.list[i].hh = times.hh;
                                json.d.list[i].mm = times.mm;
                                json.d.list[i].ss = times.ss;
                            }
                        }
                        if (panel.param.parm == 64) {
                            zuiItem.userGet(json.d.list[0], ['brPage/brjz', 0, json.d.list[0]])
                        }
                        zuiItem.Brxx_List = json.d.list;
                        zuiItem.totlePage = Math.ceil(json.d.total / zuiItem.param.rows);
                        kp.brk_list(json.d.list.length);
                        kp.Brxx_List = kp.Brxx_List.concat(json.d.list);
                        kp.kpIndex = kp.Brxx_List.length
                        common.closeLoading()
                    } else {
                        common.closeLoading()
                        kp.isDoneCb = false;
                        malert('获取挂号列表失败' + json.c, 'top', 'defeadted')
                    }
                },
                error: function (response) {
                    malert('获取挂号列表失败', 'top', 'defeadted')
                }
            });
        },
    },
});
var orignalSetItem = sessionStorage.setItem;
sessionStorage.setItem = function (key, newValue) {
    var setItemEvent = new Event('setItemEvent');
    setItemEvent.newValue = newValue;
    window.dispatchEvent(setItemEvent);
    orignalSetItem.apply(this, arguments);
};
window.addEventListener('setItemEvent', function (e) {
    if (e.key == 'wcjzAndqcwc') {
        panel.Wf_GetBrList(String)
    }
});
window.addEventListener('storage', function (e) {
    if (e.key == 'wcjzAndqcwc') {
        panel.Wf_GetBrList(String)
    }
});


window.onload = function () {
    panel.getList();
};

//列表信息
var zuiItem = new Vue({
    el: '.zui-item',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        index: 0,
        Brxx_List: [],
        isActive: [],
        page: 1,
        totlePage: 0,
        objStyle: {},
        param: {
            page: 1,
            rows: 100,
            sort: '',
            order: 'asc'
        },
        czList: [{
            name: "叫号",
            clickBc: function (brIndex, item) {
                kp.jhcz(item);
            }
        }, {
            name: "接诊",
            clickBc: function (brIndex, item) {
                kp.userGet(item, ['brPage/brjz', 0, item]);
            }
        }, {
            name: "检查检验",
            clickBc: function (brIndex, item) {
                kp.userGet(item, ['brPage/jcjy', 5, item]);
            }
        }, {
            name: "电子处方",
            clickBc: function (brIndex, item) {
                kp.userGet(item, ['brPage/dzcf', 1, item]);
            }
        }, {
            name: "抢救",
            clickBc: function (brIndex, item) {
                kp.userGetQj(item, ['brPage/qjdj', 0, item]);
            }
        }, {
            name: "留观",
            clickBc: function (brIndex, item) {
                kp.userGetLg(item, ['brPage/lgdj', 0, item]);
            }
        }, {
            name: "写病历",
            clickBc: function (brIndex, item) {
                kp.userdzbl(item);
            }
        }, {
            name: "转出",
            clickBc: function (brIndex, item) {
                kp.sxzz(item);
            }
        }, {
            name: "补挂号",
            clickBc: function (brIndex, item) {
                kp.bgh(item);
            }
        }
        ]
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
        $(window).click(function () {
            zuiItem.isActive = [];
        });
    },
    methods: {
        setShow: function (index, item) {
            if (index == 0) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 1) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 2) {
                if (item.zt == 0) {
                    return false
                }
            } else if (index == 3) {
                if (item.zt == 0) {
                    return false
                }
            } else if (index == 4) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 5) {
                if (item.zt == 0) {
                    return false
                }
            }else if (index == 6) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 7) {
                return this.isShow
            } else if (index == 8) {
                return panel.csqxContent.N03001200138 == '1' && item.mtfa == '1' ? true : false
            }
            return true
        },
        setName: function (name, item, index) {
            if (index == 1) {
                return item.wcbz == 0 ? '接诊' : '完成'
            }
            return name
        },
        czClick: function (index, $event) {
            this.activeIndex = index;
            this.objStyle.left = $event.x - 230 + 'px';
            this.objStyle.top = $event.y + 'px';
            this.objStyle.position = 'fixed';
            this.removeActiveCzButt(index);
        },
        removeActiveCzButt: function (index) {
            this.isActive = [];
            this.isActive[index] = !this.isActive[index]
            this.$forceUpdate()
        },
        goBre: function (list, val) {
            kp.userGet(list, val);
        },
        getData: function () {
            panel.totlePage = this.totlePage;
            panel.page = this.page;
            panel.param.page = this.param.page;
            panel.param.rows = this.param.rows;
            panel.param.parm = this.param.parm;
            panel.Wf_GetBrList(String)
        },
    },
});

//卡片信息
var kp = new Vue({
    el: '.kp',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        sz1_tran: {
            '0': '天',
            '1': '周',
            '2': '月',
            '3': '年',
            '4': '终身',
        },
        index: 0,
        kpIndex: undefined,
        isActive: [],
        loadData: '',
        isShow: false,
        cyzd: false,
        isDoneCb: false,
        scollType: true,
        brk_listD: 0,
        allIndex: 0,
        Brxx_List: [],
        searchCon: [],
        MtcfJsonList: [],
        jsonList: [],
        selSearch: -1,
        csContent: {},
        userObj: {
            sz: 0,
            qtzd: [{}],
            zyqtzd: [{}],
        },
        popContent: {},
        param: {
            page: 1,
            rows: 30,
            sort: '',
            order: 'asc'
        },
        queryObj: {
            page: 1,
            rows: 50,
            sort: '',
            order: 'asc'
        },
        them: {'疾病编码': 'jbmb', '疾病名称': 'jbmc', '拼音代码': 'pydm'},
        czList: [{
            name: "叫号",
            clickBc: function (brIndex, item) {
                kp.jhcz(item);
            }
        }, {
            name: "接诊",
            clickBc: function (brIndex, item) {
                kp.userGet(item, ['brPage/brjz', 0, item]);
            }
        }, {
            name: "检查检验",
            clickBc: function (brIndex, item) {
                kp.userGet(item, ['brPage/jcjy', 5, item]);
            }
        }, {
            name: "电子处方",
            clickBc: function (brIndex, item) {
                kp.userGet(item, ['brPage/dzcf', 1, item]);
            }
        }, {
            name: "抢救",
            clickBc: function (brIndex, item) {
                kp.userGetQj(item, ['brPage/qjdj', 0, item]);
            }
        }, {
            name: "留观",
            clickBc: function (brIndex, item) {
                kp.userGetLg(item, ['brPage/lgdj', 0, item]);
            }
        }, {
            name: "写病历",
            clickBc: function (brIndex, item) {
                kp.userdzbl(item);
            }
        }, {
            name: "转出",
            clickBc: function (brIndex, item) {
                kp.sxzz(item);
            }
        }, {
            name: "院感",
            clickBc: function (brIndex, item) {
                kp.openYgPage(item);
            }
        }, {
            name: "补挂号",
            clickBc: function (brIndex, item) {
                kp.toGh(item);
            }
        }, {
            name: "写诊断",
            clickBc: function (brIndex, item) {
                kp.xzd(item);
            }
        }, {
            name: "补传信息",
            clickBc: function (brIndex, item) {
                kp.bcxx(item);
            }
        },{
            name: "护理记录",
            clickBc: function (brIndex,item) {
                kp.hljl(item);
            }
         }//,{
        //     name: "会诊申请",
        //     clickBc: function (brIndex,item) {
        //         kp.hzsq(item);
        //     }
        // }
        ],
        nljd: {
            '1': '/newzui/pub/image/maleBaby.png',
            '2': '/newzui/pub/image/femalebaby.png',
            '3': '/newzui/pub/image/Group <EMAIL>',
            '4': '/newzui/pub/image/Group <EMAIL>',
            '5': '/newzui/pub/image/juvenile.png',
            '6': '/newzui/pub/image/maid.png',
            '7': '/newzui/pub/image/youth.png',
            '8': '/newzui/pub/image/woman.png',
            '9': '/newzui/pub/image/grandpa.png',
            '10': '/newzui/pub/image/grandma.png',
            '11': '/newzui/pub/image/<EMAIL>',
        },
    },
    computed: {
        disabled: function () {
            return this.userObj.sz ? this.userObj.sz == '0' ? true : false : true
        },
    },
    mounted: function () {
        $(window).click(function () {
            kp.isActive = [];
        });
        this.getNextGhxh();
    },
    methods: {
        bcxx: function (item) {
            if (panel.csqxContent.N03001200176) {
                let zdparam = {
                    ghxh: item.ghxh
                }

                $.ajax({
                    type: "POST",
                    url: panel.csqxContent.N03001200176,
                    contentType: "application/json", //
                    dataType: "json",
                    data: JSON.stringify(zdparam),//
                    async: false,
                    success: function (json) {


                    }
                })

            }
        },
        hljl:function(list){
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction').then(function (json) {
                if (json.body.d != null) {
                    let zd = ['czybm','czykl','czyxm']
                    let params = {}
                    zd.forEach(element => {
                        if(Object.prototype.hasOwnProperty.call(json.body.d,element))
                            params[element] = json.body.d[element]
                    });
                    var url = "http://localhost:9527/hlpg_index?zyh="+base64encode(utf16to8(list.ghxh))+"&userInfo="+window.encodeURIComponent(base64encode(utf16to8(JSON.stringify(params))))+"&ryzd="+window.encodeURIComponent(base64encode(utf16to8(list.jbbm)))+"&ryzdmc="+window.encodeURIComponent(base64encode(utf16to8(list.jbmc)))+"&ksbm="+base64encode(utf16to8(list.ghks));
                    window.open(url);
                }
            });
        },
        // hzsq:function (list) {
        //     alert(list.brxm)
        //     console.log(JSON.stringify(list))
        //     this.topNewPage('会诊申请','page/hzxt/hzxt/hzshf/Subdirectory/apzj.html');
        //     //sessionStorage.setItem('hzsqglitem',JSON.stringify(userNameBg.Brxx_List))
        //     sessionStorage.setItem("hzsqglitem", JSON.stringify({
        //         zyh: list.brxm
        //     }));
        // },
        getFaList: function (item) {
            this.MtcfJsonList = [];
            var param = {ghxh: item.ghxh, page: 1, rows: 99999, id: item.id};
            this.updatedAjax("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=queryZlfa&parm=" + JSON.stringify(param), function (json) {
                if (json.a == '0' && json.d && json.d.list.length != 0) {
                    this.MtcfJsonList = json.d.list;
                }
            });
        },
        faFun: function (val) {
            Vue.set(this[val[0][0]], val[0][1], val[1])
            this.$forceUpdate();
        },
        addFun: function () {
            if (this.userObj.qtzd.length == 0) {
                this.userObj.qtzd.push({})
            } else {
                if (this.userObj.qtzd[this.userObj.qtzd.length - 1]['jbmb']) {
                    this.userObj.qtzd.push({})
                }
            }

        },
        saveZd: function () {

            common.openloading('', '正在保存门特诊断')
            this.userObj.qtzd = this.spliceFun(this.userObj.qtzd, 'jbmb')
            var json = '{"list":' + JSON.stringify(this.userObj.qtzd) + '}';
            this.$http.post('/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=saveMzGhxhZd', json)
                .then(function (data) {
                    if (data.body.a == 0) {
                        this.cyzd = false
                        malert(data.body.c, 'top')
                    } else {
                        malert(data.body.c, 'top', 'defeadted')
                        popWin.ifClick = true;
                    }
                }, function (error) {
                    console.log(error);
                });

            common.closeLoading();
        },

        showTime: function (el, code) {
            laydate.render({
                elem: '#' + el
                , show: true, //直接显示
                type: 'datetime',
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) { //回调方法
                    kp.userObj[code] = value;
                }
            });
        },
        resultChange1: function (val) {
            Vue.set(this.userObj.qtzd[val[2][0]], 'cyqk', val[0]);
            this.$forceUpdate();
            this.addFun();
            this.$nextTick(function () {
                this.nextFocus(val[1])
            })
        },
        change: function (add, index, mc, bm, val, types) {
            if (!add) this.queryObj.page = 1;
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.userObj['qtzd'][this.allIndex][mc] = val;
            this.queryObj.parm = val;
            this.userObj['qtzd'][this.allIndex][bm] = '';
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=' + types + '&json=' + JSON.stringify(this.queryObj), function (data) {
                if (add) {
                    kp.searchCon = kp.selSearch.concat(data.d.list)
                } else {
                    kp.searchCon = data.d.list;
                }
                kp.page.total = data.d.total;
                kp.selSearch = 0;
                if (data.d.list.length > 0 && !add) {
                    $(".selectGroup").hide();
                    _searchEvent.show();
                    return false;
                }
            });
        },
        changeDown: function (event, index, zdms, zdbm, qtzd) {
            this.allIndex = index;
            if (this['searchCon'][this['selSearch']] == undefined) return;
            this.inputUpDown(event, this['searchCon'], this.selSearch)
            this['popContent'] = this['searchCon'][this['selSearch']];
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                this.userObj[qtzd][index] = Object.assign(this.userObj[qtzd][index], this.popContent);
                $(".selectGroup").hide();
                this.selSearch = -1;
                this.nextFocus(event)
            }
        },
        checkedTwoOut: function (item) {
            if (!item) {
                ++this.queryObj.page;
                this.change(true, this.allIndex, 'jbmc', 'jbbm', this.userObj['qtzd'][this.allIndex]['jbmc'])
            } else {
                this.userObj['qtzd'][this.allIndex] = Object.assign(this.userObj['qtzd'][this.allIndex], item);
                $(".selectGroup").hide();
                this.selSearch = -1;
            }
        },
        resultChange2: function (val) {
            Vue.set(this.userObj.zyqtzd[val[2][0]], 'zycyqk', val[0]);
            this.$forceUpdate();
            this.nextFocus(val[1])
        },
        xzd: function (item) {
            this.cyzd = true;
            console.log(item)

            this.userObj.cyzdmc = item.jbmc;
            this.userObj.zdbm = item.jbbm;
            this.userObj.sz = this.userObj.sz || 0;
            this.userObj.qtzd = [{}];
            this.userObj.zyqtzd = [{}];
            this.userObj.brxm = item.brxm;
            this.userObj.brxb = item.brxb;
            this.userObj.brnl = item.brnl;
            this.userObj.nldw = item.nldw;
            this.userObj.ghxh = item.ghxh;
            var obj = this.getMtZd(item.ghxh);
            if (obj) {
                Vue.set(this.userObj, 'qtzd', obj)
            } else {
                Vue.set(this.userObj, 'qtzd', item)
            }
        },
        getMtZd: function (ghxh) {
            var obj;
            this.updatedAjax('/actionDispatcher.do?reqUrl=New1GhglGhywBrgh&types=getMzGhxhZd&parm=' + JSON.stringify({ghxh: ghxh}), function (json) {
                obj = json
            });
            if (obj.a == '0') {
                return obj.d;
            } else {
                malert(obj.c, 'top', 'defeadted')
            }
        },
        toGh: function (item) {
            var bgh = {
                bgh: '0',
                item: item,
                ghks: panel.param.ksbm,
            }
            sessionStorage.setItem('bgh', JSON.stringify(bgh));
            this.topNewPage('挂号退号', 'page/ghgl/ghyw/brgh/brgh.html');
        },
        check: function (index) {
            this.kpIndex = index;
            this.kpSelect = true;
        },
        getNextGhxh: function () {
            if (panel.getQuery('ghxh')) {
                panel.param.parm = panel.getQuery('ghxh');
                var arr = window.top.location.search.split('&').slice(2);
                arr.push("IsNextPage=1");
                window.top.history.pushState("", "", 'index.html?' + arr.join('&'));
            }
        },
        setShow: function (index, item) {
            if (index == 0) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 1) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 2) {
                if (item.zt == 0) {
                    return false
                }
            } else if (index == 3) {
                if (item.zt == 0) {
                    return false
                }
            } else if (index == 4) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 5) {
                if (item.zt == 0) {
                    return false
                }
            }else if (index == 6) {
                if (item.zt == 0) {
                    return true
                }
            } else if (index == 7) {
                return this.isShow
            } else if (index == 8) {
                return panel.csqxContent.N03001200138 == '1' && item.mtfa == '1' ? true : false
            }
            return true
        },
        setName: function (name, item, index) {
            if (index == 1) {
                return item.wcbz == 0 ? '接诊' : '完成'
            }
            return name
        },
        czClick: function (index, $event) {
            this.activeIndex = index
            this.removeActiveCzButt(index);
        },
        removeActiveCzButt: function (index) {
            kp.isActive = [];
            kp.isActive[index] = !kp.isActive[index]
            kp.$forceUpdate()
        },
        openUrl: function (list) {
            window.open(window.top.J_tabLeft.obj.ygyycx + "/ClinicList.aspx?colid0=3078&colvalue0=" + list.ghxh + "");
        },
        openYgPage: function (list) {
            window.open(window.top.J_tabLeft.obj.ygyycx + "?employeeId=" + userId + "&ticket=nis&deptId=" + panel.param.ksbm + "");
        },
        loadingData: function (event) {
            if (event.wheelDelta > 0) return
            if (this.scollType) {
                this.scrollTop = this.$refs.kp.scrollTop
                this.scollType = false
                if (this.Brxx_List.length < panel.total) {
                    // if(this.uilPageBottom()){
                    panel.isflag = false;
                    panel.param.page = panel.param.page + 1;
                    panel.Wf_GetBrList(undefined);
                    // }
                } else {
                    this.loadData = '暂无更多数据...'
                }
            }
        },
        sxzz: function (list) {

            var param = {
                brid: list.brid,
                xh: list.ghxh,
                bz: '1',
                yljgbm: list.yljgbm
            }

            $.getJSON("/actionDispatcher.do?reqUrl=New1ZyglCryglRydj&types=queryZcHzxx&&parm=" + JSON.stringify(param), function (json) {

                if (json.a == 0) {
                    kp.askJson = JSON.parse(json.d);
                    if (!kp.askJson.YWSJ.JBXX.ZJHM) {
                        malert('身份证不能为空', 'top', 'defeadted')
                        return;
                    }
                    kp.callByRestful();
                }


            });
        },
        callByRestful() {
            var jsonPara = {
                'YWJSON': kp.askJson
            }
            $.ajax({
                type: 'post',
                url: 'http://127.0.0.1:8081/open',
                data: JSON.stringify(jsonPara),
                contentType: 'application/x-www-form-urlencoded',
                beforeSend: function (xhr) {
                },
                dataType: "json",
                success: function (data) {
                    malert(data.YWXT.MSG)
                }
            });
        },
        jhcz: function (list) {
            console.log(list)
            var obj = {
                ghxh: list.ghxh,
                brxm: list.brxm,
                //ghks:list.ghks,
                //ghksmc:list.ghksmc,
                jzys: panel.userInfo.jzys,
                jzysxm: panel.userInfo.jzysxm,

            };

            if (!panel.jhksbm) {
                malert('请选择叫号科室！', 'top', 'defeadted');
                return;
            }
            if (!panel.jhksObj && !panel.jhksObj.ghksip) {
                malert('当前科室未设置叫号屏幕ip，请联系管理员设置！', 'top', 'defeadted');
                return;
            }
            ;
            if (panel.userInfo) {
                obj.ghks = panel.jhksObj.ksbm,
                    obj.ryxh = list.ryxh,
                    obj.ksxh = list.ksxh,
                    obj.ghksmc = panel.jhksObj.ksmc,
                    obj.ghksip = panel.jhksObj.ghksip;
                obj.jzystx = panel.csqxContent.N03001200131.replace('12222', '12223') + '/images/' + panel.userInfo.jzystx;
                obj.yszc = panel.userInfo.yszc;
                obj.ysjj = panel.userInfo.ysjj;
                obj.kslc = panel.jhksObj.kslc;
            }

            $.ajax({
                type: 'post',
                url: panel.csqxContent.N03001200131 + '/callNumber',
                data: JSON.stringify(obj),
                async: true,
                dataType: 'json',
                contentType: "application/json;charset=utf-8",
                success: function (json) {
                    if (json.code == 0) {
                        malert('叫号成功，请稍后...')
                    } else {
                        malert(json.msg, 'top', 'defeadted')
                    }
                },
                error: function (response) {
                    malert('叫号失败', 'top', 'defeadted')
                }
            });
        },
        userGet: function (list, val) {
            var time = panel.csqxContent.N03001200159 == '1' ? list.ghxh : '1';
            this.topNewPage(list.brxm + '-接诊', 'page/xmzysz/mzys/jzgl/hzzx/hzzx.html?ghxh=' + time);
            val[2].fypcfShow = panel.csqxContent.cs00600100119; //非药品处方是否显示
            val[2].ksbm = panel.param.ksbm;
            val[2].ksmc = this.listGetName(panel.ksList, panel.param.ksbm, 'ksbm', 'ksmc');
            val[3] = panel.csqxContent;
            sessionStorage.setItem('brPage' + time, JSON.stringify(val))
        },
        userGetLg: function (list, val) {
            var time = panel.csqxContent.N03001200159 == '1' ? list.ghxh : '1';
            if (list.zyh) return malert('门诊已入院，不能在留观')
            this.topNewPage(list.brxm + '-留观登记', 'page/xmzysz/mzys/jzgl/hzzx/lgPage/lgdj.html?ghxh='+ time);
            val[2].ghxh = list.ghxh
            val[2].brid = list.brid
            sessionStorage.setItem('brPage1' + time, JSON.stringify(val))
        },
        userGetQj: function (list, val) {
            var time = panel.csqxContent.N03001200159 == '1' ? list.ghxh : '1';
            if (list.zyh) return malert('门诊已入院，不能在留观')
            this.topNewPage(list.brxm + '-抢救登记', 'page/xmzysz/mzys/jzgl/hzzx/qjPage/qjdj.html?ghxh='+ time);
            val[2].ghxh = list.ghxh
            val[2].brid = list.brid
            sessionStorage.setItem('brPage1' + time, JSON.stringify(val))
        },
        //电子病历
        userdzbl: function (list) {
            console.log(list);
            if (!list.brid || !list.ghxh) {
                malert("请选择患者!", 'top', 'defeadted');
                return;
            }
            ;
            //写注册信息
            $.getJSON(
                "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + list.brid + "&json=" + JSON.stringify(kp.param), function (json) {
                    if (json.a == "0") {
                        //写就诊信息
                        $.getJSON(
                            "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=M" + list.ghxh + "&json=" + JSON.stringify(kp.param), function (json) {
                                if (json.a == "0") {
                                    $.ajaxSettings.async = false;
                                    var sxdz = "";
                                    var user = "";
                                    var password = "";
                                    //取病历参数
                                    $.getJSON("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(kp.param), function (json) {
                                        if (json.a == "0") {
                                            kp.csContent = JSON.parse(JSON.stringify(json.d.list[0]));
                                            sxdz = kp.csContent.blSxdz;
                                            user = userId;
                                            //取操作员信息
                                            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                                                if (json.a == "0") {
                                                    password = json.d.password;
                                                }
                                            });
                                            if (!sxdz) {
                                                malert("书写地址为空，打开病历失败！", 'top', 'defeadted');
                                                return
                                            }
                                            if (!user) {
                                                malert("用户名为空，打开病历失败！！", 'top', 'defeadted');
                                                return
                                            }
                                            if (!password) {
                                                malert("用户密码为空，打开病历失败！", 'top', 'defeadted');
                                                return
                                            }
                                            var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=M" + list.ghxh + ",userid=" + user + ",password=" + password + ",lyzyhmz=1,blhhl=0"; //0医生 1 护理
                                            window.open(url);
                                        }
                                    });

                                } else {
                                    malert("患者信息上传失败失败：" + json.c, 'top', 'defeadted')
                                }
                            });


                    } else {
                        malert("患者信息上传失败失败：" + json.c, 'top', 'defeadted')
                    }
                });
        },
        brk_list: function (list) {
            if (this.index == 0) {
                this.brk_listD = parseInt(this.$refs.kp.offsetWidth / 307) - (this.Brxx_List.length % parseInt(this.$refs.kp.offsetWidth / 307))
            }
            // if(list>5){
            // }
        },
    },
});
$(window).resize(function () {
    kp.brk_list();
});
kp.brk_list();

//当前时间与过去时间相差的时分秒
function getxcsfm(enddate) {
    var times1 = new Date();
    var times2 = new Date(Date.parse(enddate));
    var date = times1 - times2;
    var hoursRound = Math.floor(date / 1000 / 60 / 60);
    var minutesRound = Math.floor(date / 1000 / 60 - (60 * hoursRound));
    var secondRound = Math.floor(date / 1000 - (60 * 60 * hoursRound) - (60 * minutesRound));
    if (hoursRound <= 0) {
        hoursRound = 0;
    }
    if (minutesRound <= 0) {
        minutesRound = 0;
    }
    if (secondRound <= 0) {
        secondRound = 0;
    }
    var resulttime = {
        hh: hoursRound,
        mm: minutesRound,
        ss: secondRound
    };
    return resulttime;
}

$(document).keydown(function (e) {
    // F1顺脚
    if (e.keyCode == 112) {
        panel.nextJh()
        return false
    }
    // F2 重叫
    if (e.keyCode == 113) {
        panel.restJh()

    }
});
