<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>危急值管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="wjzgl.css" rel="stylesheet"/>
</head>

<body class="skin-default padd-r-10 padd-b-10 padd-t-10 padd-l-10">
<div class="wrapper background-f percent100" id="wrapper" v-cloak>
    <div class="panel">
        <div class="tong-top">
            <button v-waves class="tong-btn btn-parmary-b  icon-iocn56 icon-c1" @click="goToPage(1)">刷新</button>
            <button v-waves class="tong-btn btn-parmary-f2a" @click="printWjz">打印</button>
        </div>
        <div class="flex-container padd-b-10 padd-t-10 padd-l-10">
            <div class="flex-container flex-align-c padd-r-10">
                <span class="ft-14 padd-r-5">状态</span>
                <select-input class="wh122" @change-data="resultChange"
                              :child="bxzt_tran" :index="'zt'" :val="zt"
                              :name="'zt'">
                </select-input>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="ft-14 padd-r-5">检索</span>
                <input class="zui-input  wh182 " placeholder="请输入关键字" id="timeVal"/>
            </div>
            <div class="flex-container flex-align-c padd-r-10">
                <span class="ft-14 padd-r-5">时间段</span>
                <input class="zui-input todate wh182 text-indent-10" v-model="beginrq" placeholder="结束时间" id="beginrq"/>
                <span class="padd-r-5 padd-l-5">~</span>
                <input class="zui-input todate wh182 text-indent-10" v-model="endrq" placeholder="结束时间" id="endrq"/>
            </div>
        </div>
    </div>
<div class="zui-table-view padd-r-10 padd-r-10">
    <div class="zui-table-header">
        <table class="zui-table">
            <thead>
            <tr>
                <th class="cell-m">
                    <div class="zui-table-cell cell-m"><span>序号</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-l"><span>检验序号</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-s"><span>患者姓名</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-s"><span>年龄</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-s"><span>性别</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>来源</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-xl"><span>身份证号</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-l"><span>住院号/门诊号</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-xl text-left text-indent-9"><span>检验项目</span></div>
                </th>
                <th>
                    <div class="zui-table-cell cell-xl text-left"><span>临床诊断</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>状态</span></div>
                </th>
                <th >
                    <div class="zui-table-cell cell-s"><span>操作</span></div>
                </th>
            </tr>
            </thead>
        </table>
    </div>
    <div class="zui-table-body  zuiTableBody" @scroll="scrollTable($event)">
        <table class="zui-table ">
            <tbody>
            <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
            <tr v-for="(item,$index) in wjzxxList" :tabindex="$index"
                :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                @mouseenter="switchIndex('activeIndex',true,$index)"
                @mouseleave="switchIndex()"
                @dblclick="editWjz($index)"
                @click="switchIndex('hoverIndex',true,$index)">
                <td class="cell-m">
                    <div class="zui-table-cell cell-m"><span v-text="$index+1"></span></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-l" v-text="item.jyxh"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-s text-line color-dsh" @click="Patient" v-text="item.brxm">
                    </div>
                </td>
                <td>
                    <div class="zui-table-cell cell-s" v-text="item.nl + ' ' + nldw_tran[item.nldw]"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-s" v-text="brxb_tran[item.xb]"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-s" v-text="item.ksmc">
                        急诊 <!--门诊：颜色状态值color-393f，急诊：color-cff5，住院：color-008-->
                    </div>
                </td>
                <td>
                    <div class="zui-table-cell cell-xl" v-text="item.sfzh"></div>
                </td>
                <td>
                    <div class="zui-table-cell cell-l" v-text="item.bah">住院号/门诊号</div>
                </td>
                <td>
                    <div class="zui-table-cell cell-xl text-left">
                        <i class="crisis-danger">
                            <small>危</small>
                        </i>
                        <em v-text="item.zwmc"></em>
                    </div>
                </td>
                <td>
                    <div class="zui-table-cell cell-xl text-left" v-text="item.lczd">临床诊断</div>
                </td>
                <td >
                    <div class="zui-table-cell cell-s" v-text="item.ysqr == '1' ? '已接收' :'未接收'" :class="item.ysqr != '1'  ? 'color-cff5':'' ">
                        <!--未接收：颜色状态值color-cff5，未发起：color-c04，待接收：color-ca3,报告成功：color-008,返回:color-f2-->
                    </div>
                </td>
                <td >
                    <div class="zui-table-cell cell-s flex-center padd-t-2">
                        <em v-if="item.ysqr != '1'" class="width30"><i class="iconfont icon-iocn12 icon-font20 icon-hover" data-title="未接收" @click="jsShow(item)"></i></em>
                        <em v-if="item.ysqr == '1'" class="width30"><i class="iconfont icon-iocn32 icon-font20 icon-transform icon-hover" data-title="已接收" @click="phone"></i></em>
                      </div>
                </td>
            </tr>
            </tbody>
        </table>
        <!--暂无数据提示,绑数据放开-->
        <!--<p v-show="jsonList.length==0" class="noData  text-center zan-border">暂无数据...</p>-->
    </div>

    <!--左侧固定-->
    <div class="zui-table-fixed table-fixed-l background-f">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <!-- data-no-change -->
        <div class="zui-table-body" @scroll="scrollTableFixed($event)">
            <table class="zui-table zui-collapse">
                <tbody>
                <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                <tr v-for="(item, $index) in wjzxxList"
                    :tabindex="$index"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('activeIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('hoverIndex',true,$index)">
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!--右侧固定-->
    <div class="zui-table-fixed table-fixed-r background-f">
        <div class="zui-table-header">
            <table class="zui-table">
                <thead>
                <tr>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s"><span>状态</span></div>
                    </th>
                    <th class="cell-s">
                        <div class="zui-table-cell cell-s"><span>操作</span></div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body" @scroll="scrollTableFixed($event)">
            <table class="zui-table zui-collapse">
                <tbody>
                <!--带危标识颜色状态样式为table-active 当前以$index==2为例-->
                <tr v-for="(item, $index) in wjzxxList"
                    :tabindex="$index"
                    :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="switchIndex('activeIndex',true,$index)"
                    @mouseleave="switchIndex()"
                    @click="switchIndex('hoverIndex',true,$index)">
                    <td >
                        <div class="zui-table-cell cell-s"  v-text="item.ysqr == '1' ? '已接收' :'未接收'" :class="item.ysqr != '1'  ? 'color-cff5':'' ">
                            <!--未接收：颜色状态值color-cff5，返回：color-f2，已接收：color-008-->
                        </div>
                    </td>
                    <td >
                        <div class="zui-table-cell cell-s flex-center padd-t-2">
                            <em v-if="item.ysqr != '1'" class="width30"><i class="iconfont icon-iocn12 icon-font20 icon-hover"  @click="jsShow(item)" data-title="未接收"></i></em>
                            <em v-if="item.ysqr == '1'" class="width30"><i class="iconfont icon-iocn32 icon-font20 icon-transform icon-hover" data-title="已接收" @click="phone"></i></em>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
</div>

<!--危急值接收复述-->
<model :s="'保存'" :c="'退出'" class="wjzDjpop" @default-click="saveData" @result-clear="wjzShow=false"
       :model-show="true" @result-close="wjzShow=false" v-show="wjzShow" :title="'危机值处理意见登记'">
    <div class="bqcydj_model flex-container flex-align-c flex-wrap-w">
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">病员姓名&emsp;</span>
            <input class="zui-input wh122" type="text" readonly="readonly" v-model="popContent.brxm"/>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">性&emsp;&emsp;别&emsp;</span>
            <select-input class="wh122"
                          @change-data="resultChange"
                          :not_empty="false"
                          :child="brxb_tran"
                          :val="popContent.xb"
                          :name="'popContent.xb'"
                          :disable="true">
            </select-input>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">年&emsp;&emsp;龄&emsp;</span>
            <input class="zui-input wh50" type="text" readonly="readonly" v-model="popContent.nl"/>
            <select-input :disable="true" class="wh70" @change-data="resultChange" :not_empty="false" :child="nldw_tran"
                          :index="popContent.nldw" :val="popContent.nldw" :name="'popContent.nldw'"></select-input>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">样&ensp;本&ensp;号&emsp;</span>
            <input class="zui-input wh122" type="text" readonly="readonly" v-model="popContent.ybbm"/>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">检验项目&emsp;</span>
            <input class="zui-input wh122" type="text" readonly="readonly" v-model="popContent.zwmc"/>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">结&emsp;&emsp;果&emsp;</span>
            <input class="zui-input wh122" type="text" readonly="readonly" v-model="popContent.jg"/>
        </div>
        <div class="flex-container fyxm-size padd-r-10 padd-b-10 ">
            <span class="wjz-jsr">判断标准&emsp;</span>
            <input class="zui-input" style="width: 85%" type="text" readonly="readonly" v-model="popContent.pdbz"/>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c" style="width: 43%">
            <span class="wjz-jsr">检验日期&emsp;</span>
            <input class="zui-input" style="width: 55%" type="text" readonly="readonly"
                   v-model="fDate(popContent.jlrq,'datetime')"/>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">接报告医师</span>
            <input class="zui-input wh122" :value="searchjson.text"
                   @keydown="changeDown($event,'text','jbgys')" @input="change('jbgys',null,$event.target.value,$event)"
                   id="jzys">
            <search-table :message="searchCon" :selected="selSearch" :page="page" :them="them"
                          :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne">
            </search-table>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
            <span class="wjz-jsr">处置医师&emsp;</span>
            <input class="zui-input wh122" :value="searchjson2.text"
                   @keydown="changeDown($event,'text','ysqrr')" @input="change('ysqrr',null,$event.target.value,$event)"
                   id="jzys2">
            <search-table2 :message="searchCon2" :selected="selSearch2" :page="page" :them="them"
                           :them_tran="them_tran" @click-one="checkedOneOut" @click-two="selectOne2">
            </search-table2>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c" style="width: 43%">
            <span class="wjz-jsr">接报告时间</span>
            <input class="zui-input todate wh182 " type="text" v-model="popContent.jbgrq" readonly="readonly"
                   placeholder="接报告时间" id="jbgrq"/>
        </div>
        <div class="flex-container padd-r-10 padd-b-10 flex-align-c" style="width: 50%">
            <span class="wjz-jsr">处置时间&emsp;</span>
            <input class="zui-input todate wh182" id="ysqrrq" readonly="readonly" placeholder="处置时间"
                   v-model="popContent.ysqrrq"/>
        </div>

        <div class="flex-container fyxm-size padd-r-10 padd-b-10 ">
            <span class="wjz-jsr whiteSpace">处理意见&emsp;</span>
            <textarea style="height: 120px;width: 85%" v-model="popContent.ysqrnr"></textarea>
        </div>
    </div>
</model>
</div>

<div class="side-form  pop-width" :class="{'ng-hide':num==0}" id="brzcList" role="form" v-cloak>
    <div class="fyxm-side-top flex-between">
        <span v-text="title"></span>
        <span class="fr iconfont icon-iocn55 icon-cf056 icon-font20" @click="close"></span>
    </div>
    <div class="ksys-side">
        <ul class="tab-edit-list1 flex-start">
            <li>
                <i>报告时间</i>
                <em class="iconfont icon-icon61 icon-c5 icon-position icon-font20"></em>
                <input type="text" class="zui-input times text-indent-25" name="disable"/>
            </li>
            <li class="patient">
                <i>患者姓名</i>
                <!--组件-->
                <select-div-input @change-data="resultChange" :styles="'nz'" :not_empty="false" :child="ryxmList"
                                  :rymc="'jszgmc'" :position="'pydm'" :index_val="'jszgbm'" :val="popContent.jszgmc"
                                  :name="'popContent.jszgmc'" :search="true">
                </select-div-input>
            </li>
            <li v-show="!launchShow">
                <i>报告科室</i>

                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="BgList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                              :name="'popContent.wxks'" :search="true">
                </select-input>
            </li>
            <li v-show="!launchShow">
                <i>报告人</i>
                <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                                  :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'" :index_val="'jszgbm'"
                                  :val="popContent.jszgmc"
                                  :name="'popContent.jszgmc'" :search="true">
                </select-div-input>
            </li>
            <li v-show="!launchShow">
                <i>危急值项目</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="BgList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                              :name="'popContent.wxks'" :search="true">
                </select-input>
            </li>
            <li v-show="!launchShow">
                <i>执行人</i>
                <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                                  :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'" :index_val="'jszgbm'"
                                  :val="popContent.jszgmc"
                                  :name="'popContent.jszgmc'" :search="true">
                </select-div-input>
            </li>
            <li v-show="launchShow">
                <i>报告形式</i>
                <select-input @change-data="resultChanges"
                              :child="XsList" :index="'bgxs'" :index_val="'ksbm'" :val="popContent.ksbm"
                              :name="'popContent.ksbm'" :search="true" :index_mc="'bgxs'">
                </select-input>

            </li>
            <li v-show="launchShow">
                <i>接收科室</i>
                <select-input @change-data="resultChange" :data-notEmpty="false"
                              :child="BgList" :index="'ksmc'" :index_val="'ksbm'" :val="popContent.wxks"
                              :name="'popContent.wxks'" :search="true">
                </select-input>
            </li>
            <li v-show="launchShow">
                <i>接收人</i>
                <!--组件-->
                <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                                  :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'" :index_val="'jszgbm'"
                                  :val="popContent.jszgmc"
                                  :name="'popContent.jszgmc'" :search="true">
                </select-div-input>
            </li>
            <li v-show="launchShow" id="phone">
                <i>电话接收人</i>
                <!--组件-->
                <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                                  :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'" :index_val="'jszgbm'"
                                  :val="popContent.jszgmc"
                                  :name="'popContent.jszgmc'" :search="true">
                </select-div-input>
            </li>
        </ul>
        <div class="crunch" v-show="launchShow">
            <div class="crunch-name">危机值数据</div>
            <div class="crisis-title">
                <span class="crisis-span text-left text-indent-28">检查项目</span>
                <span class="crisis-span">检查结果</span>
                <span class="crisis-span">状态</span>
            </div>
            <vue-scroll :ops="pageScrollOps">
                <ul class="crisis-list">
                    <li v-for="(item,$index) in datas">
                    <span class="crisis-span text-left">
                        <em class="crisis-danger crisis-dangers"><small>危</small></em>
                        <em v-text="item.name" data-title="阿斯兰的空间"></em>
                    </span>
                        <span class="crisis-span"
                              :class=" item.result=='0' ? 'color-c04':item.result=='1' ? 'color-cff5' : '' "
                              v-text="is_result[item.result]">
                    </span>
                        <span class="crisis-span" :class=" item.state=='0' ? 'wj_x':item.state=='1' ? 'wj_s': '' "
                              v-text="is_state[item.state]"></span>
                    </li>

                </ul>
            </vue-scroll>
        </div>

    </div>
    <div class="ksys-btn padd-r-10">
        <button v-waves class="root-btn btn-parmary-d9" @click="close">取消</button>
        <button v-waves class="root-btn btn-parmary" @click="register" v-text="btnTitle"></button>
    </div>
</div>
<!--危急值接收复述-->
<model :s="'确定'" :c="'取消'" v-cloak class="wjzFspop" @default-click="wjzShow=false" @result-clear="wjzShow=false"
       :model-show="true" @result-close="wjzShow=false" v-if="wjzShow" :title="'危机值接收复述'">
    <div class="bqcydj_model">
        <div class="wjz-content">
            <span class="wjz-jsr" :id="windowMsgListener">接受人</span>
            <!--组件components.js调用说明方法-->
            <select-div-input @change-data="resultChange" :styles="'nzp'" :not_empty="false" :child="ryxmList"
                              :rymc="'jszgmc'" :position="'pydm'" :phone="'jszgbm'" :index_val="'jszgbm'"
                              :val="popContent.jszgmc"
                              :name="'popContent.jszgmc'" :search="true">
            </select-div-input>
        </div>
        <div class="wjz-radio">
            <div class="c_radio">
                <input type="radio" id="1" name="radio11" checked >
                <label for="1"></label>
                <label for="1" class="lb_text">信息无误</label>
            </div>
            <div class="c_radio">
                <input type="radio" id="2" name="radio11" >
                <label for="2"></label>
                <label for="2" class="lb_text">转科</label>
            </div>
            <div class="c_radio">
                <input type="radio" id="3" name="radio11" >
                <label for="3"></label>
                <label for="3" class="lb_text ">出院</label>
            </div>
            <div class="c_radio">
                <input type="radio" id="4" name="radio11" >
                <label for="4"></label>
                <label for="4" class="lb_text ">死亡</label>
            </div>
        </div>
        <div class="wjz-bz">
            <span class="wjz-jsr">备注</span>
            <textarea class="wjz-textarea"></textarea>
        </div>
    </div>
</model>
<script src="wjzgl.js" type="text/javascript"></script>
</body>
</html>
