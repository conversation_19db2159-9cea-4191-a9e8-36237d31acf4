<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<div id="lrsh">
    <div class="toolMenu toolMenu_3" style="display: block;">
        <button @click="shPdlr"><span class="fa fa-check-square-o"></span>审核</button>
        <button @click="zfPdlr"><span class="fa fa-scissors"></span>作废</button>
        <span style="margin-left: 10px">盘点凭证号</span>
        <select v-model="pdlrpz" @change="getPdlrList()">
            <option :value="0">-请选择-</option>
            <option v-for="item in pdblrList" v-text="item.pdpzh" :value="item"></option>
        </select>
        <span style="margin-left: 10px">物资检索</span>
        <input type="text" v-model="ypjs">
    </div>

    <div class="enter_tem1 enter_djList_sh">
        <div class="enter_tem1_title">单据列表</div>
        <div class="table_tem2" style="height: calc(100% - 130px);">
            <table>
                <tr>
                    <th>盘点录入单号</th>
                    <th>盘点日期</th>
                    <th>制单人</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index),getPdblrmx(item)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <td>{{item.pdlrpzh}}</td>
                    <td>{{item.zdrq}}</td>
                    <td>{{item.zdy}}</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="enter_tem1 enter_djDetail_sh">
        <div class="enter_tem1_title">单据明细</div>
        <div class="table_tem2" style="height: calc(100% - 130px)">
            <table>
                <tr>
                    <th>物资编码</th>
                    <th>物资名称</th>
                    <th>规格</th>
                    <th>产品标准号</th>
                    <th>生成标准</th>
                    <th>录入数量</th>
                    <th>单位</th>
                    <th>生产批号</th>
                    <th>有效期至</th>
                    <th>库房单位</th>
                    <th>分装比例</th>
                    <th>产地</th>
                    <th>供货单位</th>
                </tr>
                <tr v-for="(item, $index) in jsonList" @click="checkOne($index)"
                    :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                    <td v-text="item.wzbm"></td>
                    <td v-text="item.wzmc"></td>
                    <td v-text="item.wzgg"></td>
                    <td v-text="item.xtph"></td>
                    <td v-text="item.kcsl"></td>
                    <td v-text="item.lrsl"></td>
                    <td v-text=" item.yfdw "></td>
                    <td v-text="item.scph "></td>
                    <td v-text="item.yxqz "></td>
                    <td v-text="item.kfdwmc "></td>
                    <td v-text="item.fzbl "></td>
                    <td v-text="item.cd "></td>
                    <td v-text="item.ghdwmc "></td>
                </tr>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript" src="lrsh.js"></script>