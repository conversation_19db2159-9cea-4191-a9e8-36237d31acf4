<!DOCTYPE html>
<html>
<head>
    <style type="text/css">
        #printPage {
            width: 200mm;
            /*height: 195mm;*/
            height: 1038px;
            padding: 0 3mm;
            position: relative;
        }

        .logo {
            width: 80px;
            height: 80px;
            border-radius: 100%;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: contain;
        }

        .font-weight {
            font-weight: bolder;
        }

        .lineHeight40 {
            line-height: 40px;
            font-size: 20px;
        }

        .border-bottom {
            border-bottom: 3px solid #333333;
        }

        .text-info {
            font-size: 20px;
        }

        .xmmcWidth {
            width: calc(100% / 4);
            height: 50px;
            margin-bottom: 10px;
        }

        .xmmcWidth div {
            display: flex;
            height: 100%;
            align-items: center;
            /*justify-content: center;*/
        }

        .xmmc {
            font-weight: bold;
            font-size: 20px;
        }

        .yf {
            position: relative;
            right: 28px;
            bottom: 13px;
            font-weight: bold;
            font-size: 16px;
        }

        .jl {
            position: relative;
            right: 0;
            top: 13px;
            font-weight: bold;
            font-size: 16px;
        }

        .contentPrint {
            position: relative;
            height: 160mm;
            top: 20px;
            bottom: 0;
            left: 0;
        }

        .contentPrint:after {
            position: absolute;
            content: 'Rp';
            left: 0;
            top: -20px;
            font-weight: bolder;
            font-size: 20px;
        }

        .yfBottom {
            position: absolute;
            bottom: 0;
            width: 100%;
        }

        .line {
            transform: rotate(149deg);
            /*background: #333333;*/
            /*height: 2px;*/
            border: 2px solid #333333;
            position: absolute;
        }


        .footer {
            position: absolute;
            bottom: 0;
            left: 0;
            padding: 0 4mm;
            width: calc(100% - 200px);
        }


        /*@media print{*/
        /*    @page{*/
        /*        size:140mm 195mm;*/
        /*        margin:0 0.5mm 0;*/
        /*    }*/
        /*}*/
    </style>
</head>
<div id="printPage">
    <div class="flex-container flex-jus-sb margin-b-10 headerTitle">
        <div class="lineHeight40 padd-r-10">
            <div>宁蒗县中医医院</div>
            <div class="print-barcode-img marg-b-1mm">
                <!-- <svg class="print-barcode-img marg-b-1mm" jsbarcode-format="CODE128" :jsbarcode-value="cfxxJson.cfh" jsbarcode-margin=10 jsbarcode-displayValue="false" jsbarcode-height=34 alt="病人条码"></svg> -->
            </div>
        </div>
        <div class="flex-container flex-align-c padd-r-10">
            <div class="font-weight" style="font-size: 26px">门诊处方签</div>
        </div>
        <div class="lineHeight40 padd-r-10">
            <div>{{cfxxJson.cflxmc}}</div>
            <div class="font-weight">No.{{cfxxJson.cfh}}</div>
        </div>
        <img class="logo" src="/newzui/pub/image/yylogo.png">
    </div>
    <div class="border-bottom text-info">

        <div class="flex-container  flex-jus-sb flex-wrap-w">

            <div class="flex-container flex-align-c margin-b-10 padd-r-10">
                <p>姓名：</p>
                <p>{{brxxJson.brxm}}</p>
            </div>
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>性别：</p>
                <p>{{brxb_tran[brxxJson.brxb]}}</p>
            </div>
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>年龄：</p>
                <p>{{brxxJson.brnl}}{{nldw_tran[brxxJson.nldw]}}</p>
            </div>
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>科别：</p>
                <p>{{cfxxJson.brksmc}}</p>
            </div>
        </div>

        <div class="flex-container  flex-jus-sb flex-wrap-w">
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>患者ID：</p>
                <p>{{brxxJson.brid}}</p>
            </div>
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>电话：</p>
                <p>{{brxxJson.sjhm ? brxxJson.sjhm : brxxJson.lxrdh}}</p>
            </div>
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>门诊号：</p>
                <p>{{brxxJson.ghxh}}</p>
            </div>
        </div>

        <div class="flex-container">
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>处方日期：</p>
                <p>{{fDate(cfxxJson.cfrq,'datetime')}}</p>
            </div>
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p style="padding-left: 50px">身份证号：</p>
                <p>{{brxxJson.sfzh}}</p>
            </div>
        </div>

        <div class="flex-container  flex-wrap-w">
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>临床诊断：</p>
                <p>{{cfxxJson.lczd}}({{cfxxJson.zyzf}})</p>
            </div>

        </div>

        <div class="flex-container  flex-jus-sb flex-wrap-w">
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>住址：</p>
                <p>{{brxxJson.jzdmc}}</p>
            </div>
        </div>

        <div class="flex-container  flex-jus-sb flex-wrap-w">
            <div class="flex-container padd-r-10 margin-b-10 flex-align-c">
                <p>备注：</p>
                <p></p>
            </div>
        </div>

    </div>
    <div class="contentPrint">
        <ul ref="xmmc" class="flex-container xmmc  flex-wrap-w" style="margin-left: 20px">
            <li class=" xmmcWidth" v-for="item in pfxxList">
                <div class="position">

                    <p class="xmmc">{{item.ypmc}}</p>
                    <span class="jl">{{item.yyjl}}{{item.yfdwmc}}</span>
                    <span class="yf">{{item.yyffmc}}</span>

                </div>
            </li>
        </ul>
        <div class="line" :style="style"></div>

        <div class="flex-container yfBottom flex-jus-sb flex-wrap-w padd-b-10 padd-l-10 border-bottom text-info">
            <div class="flex-container padd-r-10 flex-align-c">
                <p>用法：</p>
                <p>{{cfxxJson.yysm}}</p>
            </div>
            <div class="flex-container padd-r-10 flex-align-c">
                <p>处方金额：</p>
                <p>{{cfxxJson.cfje}}元</p>
            </div>
        </div>

        <div class="flex-container yfBottom flex-jus-c flex-align-c padd-b-10 border-bottom text-info" v-if="false">
            用法：{{cfxxJson.yysm}}
        </div>
    </div>
    <div class="footer flex-container  flex-jus-sb flex-wrap-w text-info" v-if="false">
        <div class="flex-container flex-align-c margin-b-10 padd-r-10">
            <p>医师签名：</p>
            <p>{{cfxxJson.cfysxm}}</p>
        </div>
        <div class="flex-container flex-align-c margin-b-10 padd-r-10">
            <p>付数：</p>
            <p>{{cfxxJson.zyfs}}付</p>
        </div>
        <div class="flex-container flex-align-c margin-b-10 padd-r-10">
            <p>处方金额：</p>
            <p>{{cfxxJson.cfje}}元</p>
        </div>
        <div class="flex-container flex-align-c margin-b-10 padd-r-10">
            <p>对应药房：</p>
            <p>中药房</p>
        </div>
        <div class="flex-container flex-align-c margin-b-10 padd-r-10">
            <p>调配：</p>
            <p></p>
        </div>
        <div class="flex-container flex-align-c margin-b-10 padd-r-10">
            <p>核发：</p>
            <p></p>
        </div>
    </div>

    <div class="flex-container  flex-jus-sb flex-wrap-w" style="float: right">
        <div class="footer text-info">
            <div class="flex-container  flex-jus-sb flex-wrap-w">
                <div class="flex-container flex-align-c margin-b-10 padd-r-10">
                    <p>医师签名：</p>
                    <p>{{cfxxJson.cfysxm}}</p>
                </div>
                <div class="flex-container flex-align-c margin-b-10 padd-r-10">
                    <p>付数：</p>
                    <p>{{cfxxJson.zyfs}}付</p>
                </div>
            </div>
            <div class="flex-container  flex-jus-sb flex-wrap-w">
                <div class="flex-container flex-align-c margin-b-10 padd-r-10">
                    <p>对应药房：</p>
                    <p>中药房</p>
                </div>
                <div class="flex-container flex-align-c margin-b-10 padd-r-10">
                    <p>核发：</p>
                </div>
                <div class="flex-container flex-align-c margin-b-10 padd-r-10">
                    <p>调配：</p>
                </div>
            </div>
        </div>

        <div style="position: relative; margin-top: 25px; text-align: center">
            <p id="qrcode"  ></p>
            <p style="font-size: 8px">微信扫码支付</p>
        </div>

    </div>


</div>
<script src="/newzui/js/plugins/jquery-barcode.js"></script>
<script src="/newzui/js/plugins/jquery.qrcode.min.js"></script>
<script type="text/javascript">
    var printPage = new Vue({
        el: '#printPage',
        mixins: [dic_transform,mformat],
        data: {
            brxxJson: {},
            cfxxJson: {},
            pfxxList: [],
            style: {},
        },
        mounted: function () {
            this.pfxxList = [
                {ypmc: '王不留行子', yyjl: '20g', yyffmc: '碾碎'}, {ypmc: '豆蔻(免煎)', yyjl: '20g'}, {
                    ypmc: '白花蛇舌草',
                    yyjl: '20g'
                }, {ypmc: '王不留行子', jl: '20g'},
                {ypmc: '苦参', yyjl: '120g'}, {ypmc: '白芍', yyjl: '2g', yf: '另外包'}, {ypmc: '白果', yyjl: '20g'}, {
                    ypmc: '艾叶',
                    yyjl: '20g'
                },
                {ypmc: '扁蓄', yyjl: '20g'}, {ypmc: '石韦', yyjl: '20g'}, {ypmc: '大黄', yyjl: '2g'}, {ypmc: '乌梅', yyjl: '20g'},
                {ypmc: '白豆蔻', yyjl: '20g', yf: '研粉口服'}, {ypmc: '冬葵子', yyjl: '120g', yyffmc: '碾碎'}, {
                    ypmc: '槐米',
                    yyjl: '20g'
                }, {ypmc: '杏仁', jl: '20g'},
                {ypmc: '丁香', yyjl: '20g'}, {ypmc: '苍术', yyjl: '20g', yyffmc: '先煎'}, {ypmc: '黄连', yyjl: '20g', yyffmc: '后下'},
                {ypmc: '丁香', yyjl: '20g'}, {ypmc: '苍术', yyjl: '20g', yyffmc: '先煎'}, {ypmc: '黄连', yyjl: '20g', yyffmc: '后下'},
                {ypmc: '丁香', yyjl: '20g'}, {ypmc: '苍术', yyjl: '20g', yyffmc: '先煎'}, {ypmc: '黄连', yyjl: '20g', yyffmc: '后下'},
                {ypmc: '丁香', yyjl: '20g'}, {ypmc: '苍术', yyjl: '20g', yyffmc: '先煎'}, {ypmc: '黄连', yyjl: '20g', yyffmc: '后下'}
            ];


            this.brxxJson = userNameBg.Brxx_List;
            this.cfxxJson = zcy.cfcontent;
            this.pfxxList = zcy.zcyList;

            console.log(1111)
            this.$nextTick(function () {

                <!-- JsBarcode(".print-barcode-img").init(); -->
                $(".print-barcode-img").barcode(this.cfxxJson.cfh, "code128", {
                    output: 'svg',       //渲染方式 css/bmp/svg/canvas
                    //bgColor: '#ff0000', //条码背景颜色
                    //color: '#00ff00',   //条码颜色
                    barWidth: 1,        //单条条码宽度
                    barHeight: 34,     //单体条码高度
                    // moduleSize: 10,   //条码大小
                    // posX: 10,        //条码坐标X
                    // posY: 5,         //条码坐标Y
                    showHRI: false,    //是否在条码下方显示内容
                    addQuietZone: false  //是否添加空白区（内边距）
                });
                if(printPage.cfxxJson.wxPrePayUrl){
                    jQuery('#qrcode').qrcode({
                        render: "canvas",
                        width: 60,
                        height: 60,
                        correctLevel: 0,
                        text: printPage.cfxxJson.wxPrePayUrl
                    });
                }
                this.dragStyle();
            });
        },
        methods: {
            dragStyle: function () {
                var width = document.querySelector('.xmmc').clientWidth;
                var height = document.querySelector('.contentPrint').clientHeight - document.querySelector('.xmmc').clientHeight - document.querySelector('.yfBottom').clientHeight;
                var calcHeight = height / 2;
                var topHeight = document.querySelector('.xmmc').clientHeight + calcHeight;
                var left = width - height;
                this.style = {
                    width: height + 'px',
                    top: topHeight + 'px',
                    left: left / 2 + 'px',
                }
                setTimeout(function () {
                    $('.userNameBg').hide()
                    window.print()
                    $('.userNameBg').show()
                    $('.ynPrintPage').html('')
                },500)
                // $('.ynPrintPage').html('')
                return this.style
            }
        },
    })
</script>
</html>
