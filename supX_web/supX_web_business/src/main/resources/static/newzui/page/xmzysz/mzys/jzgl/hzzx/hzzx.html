<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>患者中心</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link href="hzxx.css" rel="stylesheet">
    <link rel="stylesheet" href="/pub/css/print.css" media="print"/>
    <link rel="stylesheet" href="/FR/ReportServer?op=emb&resource=finereport.css">
	<style>
		.btn-parmary-f2a {
		    background: #9bd8cc;
		 		color: black;
		    position: relative;
		}
		.btn-parmary {
		    background: #9bd8cc;
		    color: black;
		    position: relative;
		}
.btn-parmary-not {
    border: none;
    color: black !important;
}
	</style>
</head>
<body class="skin-default padd-b-10 padd-r-10 padd-l-10 padd-t-10">
<div class="background-box1 background-f">
<div class="hzzx-top">
 <header class="userNameBg padd-b-5 padd-t-5" v-cloak >
     <div class="printArea printShow "></div>
     <div class="flex printHide">
         <div class="text-color padd-l-10">
                 <span class="userName" v-text="Brxx_List.brxm"></span>
                 <span class="sex text" v-text="brxb_tran[Brxx_List.brxb]"></span>
                 <span class="nl text" v-text="Brxx_List.brnl+nldw_tran[Brxx_List.nldw]"></span>
                 <span class="nl text" v-if="Brxx_List.nl2" v-text="Brxx_List.nl2+nldw_tran[Brxx_List.nldw2]"></span>
                 <span class="cwh text">挂号序号：<span v-text="Brxx_List.ghxh"></span><span v-if="Brxx_List.ghxh==null">无</span></span>
                 <span  v-if="Brxx_List.gms" class="ys color-wtg text-left ft-14">过敏史：<span v-text="Brxx_List.gms"></span></span>
                 <span class="zyh text">科室：<span v-text="Brxx_List.ghksmc"></span><span v-if="Brxx_List.ghksmc==null">无</span></span>
                 <span class="zyh text">保险：<span v-text="Brxx_List.bxlbmc"></span><span v-if="Brxx_List.bxlbmc==null">无</span></span>
                 <span class="ys text">医师：<span v-text="Brxx_List.jzysxm"></span><span v-if="Brxx_List.jzysxm==null">无</span></span>
                 <span class="bz text">病种：
                 	<span>{{ (Brxx_List.zdsm1 == null ? '' :  Brxx_List.zdsm1 )
                 	+ Brxx_List.jbmc ? '' :  Brxx_List.jbmc
                 	+ (Brxx_List.zdsm2 == null ? '' :  Brxx_List.zdsm2)}}</span>
                 	<span v-if="Brxx_List.jbmc==null">无</span>
                 </span>
                 <span class="phone text">联系电话：<span v-text="Brxx_List.sjhm?Brxx_List.sjhm:Brxx_List.lxrdh"></span><span v-if="!(Brxx_List.sjhm?Brxx_List.sjhm:Brxx_List.lxrdh)">无</span></span>
                 <span class="sfz text">身份证：<span v-text="Brxx_List.sfzh"></span><span v-if="Brxx_List.sfzh==null">无</span></span>
                     <span class="sfz text padd-r-20">详细地址：<span v-text="Brxx_List.jzdmc"></span><span v-if="Brxx_List.jzdmc==null">无</span></span>
                     <span class="sfz text padd-r-20">挂号时间：<span v-text="fDate(Brxx_List.ghrq,'datetime')"></span></span>
         </div>
     </div>
     <!--
     	作者：offline
     	时间：2018-04-19
     	描述：取消接诊，完成接诊，，门诊入院按钮
     -->
     <div class="blRight">
<!--         <span class="dyblImg" v-if="Brxx_List.wcbz!=1|| Brxx_List.wcbz==3"  onclick="tabBg('brPage/wzbrqj',3,this,3)"></span>-->
         <span class="wcjzImg" v-if="Brxx_List.wcbz!=1" @click="wcJz"></span>
         <span class="qxwcjzImg" v-if="Brxx_List.wcbz==1" @click="qxwc"></span>
         <!--Brxx_List.sfzt == '0' && Brxx_List.wcbz!=1     台江需求显示取消接诊 -->
         <span class="blImg" @click="cancelJz"></span>
<!--         <span class="xyzImg"  @click="addSide"></span>-->
     </div>
 </header>
</div>
<div class="content  background-f">
    <div class="fyxm-tab printHide" id="tabs">
        <div><span :class="{'active':num==0}" onclick="tabBg('brPage/brjz',0,this)">病人接诊</span></div>
        <div><span :class="{'active':num==1}" onclick="tabBg('brPage/dzcf',1,this)">药品处方</span></div>
        <div v-if="fypcfShow==1"><span :class="{'active':num==2}" onclick="tabBg('brPage/fypcf',2,this)">非药品处方</span></div>
<!--        <div><span :class="{'active':num==3}" onclick="tabBg('brPage/ynPrintPage',3,this)">打印</span></div>-->
        <!--
        <div><span :class="{'active':num==4}" onclick="tabBg('brPage/jybgd',4,this)">检验报告</span></div>
        <div><span :class="{'active':num==5}" onclick="tabBg('brPage/jcbgd',5,this)">检查报告</span></div>
         -->
         <div><span :class="{'active':num==5}" onclick="tabBg('brPage/jcjy',5,this)">检查检验报告</span></div>
         <div><span :class="{'active':num==6}" onclick="tabBg('brPage/fyxx',6,this)">费用信息</span></div>
    </div>
    <div class="loadPage over-auto col-x-12">

    </div>
</div>

    <div class="side-form  pop-width printHide" :class="{'ng-hide':nums==1}" v-cloak  id="brzcList"
         role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side ksys-side4">
		<span class="span0">
			<i>姓名</i>
			<input type="text" class="zui-input border-r4 background-h" disabled v-model="json.brxm"/>
		</span>
            <span class="span0">
			<i>初步诊断</i>
			<input class="zui-input border-r4" placeholder="请输入初步诊断" :value="jbbmContent.jbmc"
                   @keydown="changeDown($event)"
                   @input="change(false,$event.target.value)">
	        <search-table :message="searchCon" :selected="selSearch"
                          :them="them" :page="page" @click-one="checkedOneOut" @click-two="selectOne">
	        </search-table>
		</span>
            <span class="span0">
			<i>门诊科室</i>
			<select-input @change-data="resultMzryChange" :not_empty="true" :child="mzksList"
                          :index="'ksmc'" :index_val="'ksbm'" :val="json.mzks" :name="'json.mzks'"
                          :search="true">
            </select-input>
		</span>
            <span class="span0">
			<i>门诊医生</i>
			<select-input @change-data="resultChange" :not_empty="true" :child="mzysList" :index="'ryxm'"
                          :index_val="'rybm'" :val="json.mzys" :name="'json.mzys'" :search="true">
            </select-input>
		</span>
            <span class="span0">
			<i>入院预交</i>
			<input class="zui-input border-r4" @mousewheel.prevent placeholder="请输入入院预交" type="number"
                   v-model="json.yjje" @keydown="nextFocus($event)"/>
			<em class="hzzx-money">元</em>
		</span>
            <span class="span0">
			<i>住院科室</i>
			<select-input @change-data="resultChange" :not_empty="true" :child="zyksList" :index="'ksmc'"
                          :index_val="'ksbm'" :val="json.zyks" :name="'json.zyks'" :search="true">
            </select-input>
		</span>
            <span class="span0">
			<i>特别注意事项</i>
			<input type="text" class="zui-input border-r4" placeholder="请输入特别注意事项" v-model="json.tbzysx"
                   data-notEmpty="false" @keydown="nextFocus($event)"/>
			</span>
            <span>
			<i>备注说明</i>
			<input type="text" class="zui-input border-r4" placeholder="请输入备注说明" v-model="json.bzsm"
                   data-notEmpty="false" @keyup.13="saveData"/>
		</span>
        </div>

        <div class="ksys-btn">
            <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button v-waves class="zui-btn btn-primary xmzb-db" @click="saveData" v-if="!rybz">确定</button>
            <!-- <button v-waves class="zui-btn btn-primary xmzb-db" @click="print" v-if="csqxContent.cs03001200126==1">补打入院证</button>-->
        </div>
    </div>
</div>
 <script type="text/javascript" src="hzxx.js"></script>
</body>
</html>
