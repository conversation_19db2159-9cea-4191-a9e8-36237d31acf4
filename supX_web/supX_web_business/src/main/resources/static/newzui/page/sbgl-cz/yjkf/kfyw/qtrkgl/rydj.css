.icon-width:before{
	top: 4px;
}
.paddig-left{
	padding-left: 30px;
}

.tong-search .zui-form .zui-inline.padding-left40{
	padding-left: 40px;
}

.tong-search .zui-form .zui-inline.padding-left60{
	padding-left: 60px;
}

.table-width50 .cell-1-1{ /*由于当前tablist列表没有复选框 故不能让main.css中的cell-1-1覆盖掉默认宽度100px!*/
	width: 100px !important;
}

.icon-icon{
	margin: 0 5px;
}
.icon-icon,.icon-icon:before{
	width: 24px;
	height: 24px;
	display: inline-block;
}
.icon-icon:before{
	position: static;
}
.rydj-info-box,
.syjj-info-box{
	padding-top: 10px;
	background-color: #fff;
	margin-bottom: 52px;
}

.zui-inline, .zui-input-inline, .zui-select-inline{
	margin-right: 0;
}

.zui-form-label{
	width: 70px;
	padding: 8px 10px 8px 0;
}
.zui-form .zui-inline{
	padding-left: 70px;
}

.action-bar.fixed{
	right: 10px;
    left: 10px;
    width: auto;
}

.danwei-box .zui-input{
	padding-right: 34px;
}
.danwei-box .danwei{
	position: absolute;
	right: 10px;
	top: 50%;
	margin-top: -9.5px;
	color:#1abc9c;
}
.icon-dysqb:before{
	color: #757c83;
}
.flex{
	display: flex;
}
.hsColor{
	font-size:14px;
	color:#f2a654;
	position: relative;
	display: inline-block;
	float: right;
	cursor: pointer;
	margin-bottom: 9px;
}
.hsColor:after{
	content: '+';
	position: absolute;
	left: -17px;
	top: -8px;
	font-size: 24px;
}
.yjjlTable{
	background-color: transparent;
}
.ksys-side .span0 {
	display: inline-flex;
	position: relative;
	width: 28%;
	float: left;
	margin-right: 5%;
	margin-bottom: 19px;
	align-self: flex-end;
	align-items: center;
	justify-content: space-between;
}
.ksys-side .span0 i {
	/* display: block; */
	/* width: 100%; */
	width: 62px;
	text-indent: initial;
	display: inline-block;
}
.ksys-side .span0 input, .ksys-side .span0 .zui-select-inline {
	width: 70%;
	float: right;
}
.ksys-side .span0 .zui-select-inline input{
	width: 100%;
}
.tem{
	position: relative;
	float: left;
/* width: 300px; */
/* height: 450px; */
	width: 800px;
	height: 500px;
	border: 1px solid green;
	margin-left: 20px;
	margin-top: 20px;
}
.item {
	position: absolute;
	display: inline-block;
	top: 10px;
	margin-bottom: 20px;
	font-size: 14px;
	cursor: default;
	z-index: 100;
}
.hqjtcy{
	background-image:linear-gradient(-180deg, #ffffff 0%, #e6e8e9 100%);
	border:1px solid #dfe3e9;
	border-radius:4px;
	width:140px !important;
	text-align: center;
	line-height: 34px;
	height:34px;
	font-size:14px;
	color:#f3ab5e;
	cursor: pointer;
}
.pad-top-bot{
	padding: 8px 10px 8px 0;
}
.zui-form-label{
	width: 75px;
}
.zdjb{
	position: relative;
	width: 100%;
}
.zdjb span{

}
.zdjb:after{
	width: 96%;
	content: '';
	right: 0;
	top: 11px;
	opacity:0.2;
	border-bottom: 1px dashed #354052;
	position: absolute;
}
.file{
	background-image: linear-gradient(-180deg, #ffffff 0%, #e6e8e9 100%);
	border: 1px solid #dfe3e9;
	border-radius: 4px;
	width: 80px;
	height: 34px;
	position: relative;
	display: inline-block;
}
.file:after{
	content: '选择文件';
	color:#1abc9c;
	position: absolute;
	left: 50%;
	top: 50%;
	width: 100%;
	text-align: center;
	transform: translate(-50%,-50%);
}
.file input{
	display: none;
}
.ckbxqd{
	cursor: pointer;
	font-size:14px;
	color:#f3ab5e;
	text-decoration: underline;
}
.zdjbtj-bg{
	background:rgba(26,188,156,0.06);
}
.zdjbtj:after{
	position: absolute;
	left: 22%;
	margin-top: -1px;
	color:#1abc9c;
	content: '+';
}