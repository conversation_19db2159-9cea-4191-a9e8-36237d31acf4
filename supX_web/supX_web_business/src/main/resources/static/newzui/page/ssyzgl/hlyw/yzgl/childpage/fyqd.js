var yljgmc=sessionStorage.getItem('yljgOrUser' + userId)?JSON.parse(sessionStorage.getItem('yljgOrUser' + userId)).yljgmc:'';
//检索显示区
var brSearch = new Vue({
    el: '.brSearch',
    components: {
        'search-table': searchTable
    },
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ksbm: "",
        ksmc: "",
        allKs: [],
        brContent: {},
        popContent: {},
        searchCon: [],
        fylbsList: [],
        qdPxfs_tran:{
            'djrq':'时间',
            'xmmc':'项目',
        },
        selSearch: -1,
        json: yzclLeft.HszbrItem,
        page: {
            page: 1,
            rows: 10,
            brxm: '',
            ryks: '',
            total:null
        },
        bk:'1',
        title: '病人信息',
    },
    mounted:function(){
        this.getfylb();

    },
    methods: {
        openJZ:function (){
            sessionStorage.setItem("hszToZygl"+userId, JSON.stringify({
                zyh: yzclLeft.HszbrItem.zyh,
                brxx: yzclLeft.HszbrItem,
                ksbm: yzclLeft.HszbrItem.ryks,
                ksmc: yzclLeft.HszbrItem.ryksmc,
            }));
            this.topNewPage('费用记账', 'page/zygl/rcygl/fyjz/fyjz.html','N050022011');
        },
        openTy:function (){
            var brJson = {
                brlist: [yzclLeft.HszbrItem],
                ksid: yzclLeft.jsContent.ksbm,
                csqx: yzclLeft.caqxContent,
                zyType:yzclLeft.jsContent.zyType
            };
            sessionStorage.setItem('tysq', JSON.stringify(brJson));
            this.topNewPage('退药申请', 'page/hsz/hlyw/yzcl/loading-page/tysq.html');
        },
        openTf:function(){
            sessionStorage.setItem('jztf', JSON.stringify({
                type: "jztf",
                zyh: yzclLeft.HszbrItem.zyh,
                brxx: yzclLeft.HszbrItem,
                ksbm: yzclLeft.HszbrItem.ryks,
            }));
            this.topNewPage('记账退费', 'page/zygl/rcygl/fyjz/jztf.html');
        },
        setJson: function (item) {
            console.log(item);
            this.json = item;
        },
        getfylb: function () {
            var param = {
                page: 1,
                rows: 100,
                sort: '',
                order: 'asc'
            };
            let sjson={
                tybz:'0'
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhYlfwxmFylb&types=query&dg=" + JSON.stringify(param)+"&json="+JSON.stringify(sjson), function (json) {
                if(json.a == '0' && json.d && json.d.list.length){
                    brSearch.fylbsList = json.d.list;
                    brSearch.fylbsList.unshift({lbbm:'%','lbmc':'全部'});
                    brSearch.popContent.lbbm=json.d.list[0].lbbm
                    tableInfo.getBrData();
                }

            });
        },
        resultChangeData:function(val){
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            this.$forceUpdate()
            tableInfo.getBrData()
        },
        pxfsFun:function(val){
            Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
            if(tableInfo.popContent.fyqd == '1'){
                tableInfo.jsonMxList=this.filterSort(tableInfo.jsonMxList,this.popContent.pxfs)
            }
        },
        filterSort: function (arr, field) { //排序
            if(field =='xmmc'){
                var list = arr.sort(function (a, b) {
                    return  a[field].localeCompare(b[field])
                })
            }else {
                var list = arr.sort(function (a, b) {
                    return  new Date(a[field]).getTime() - new Date(b[field]).getTime()
                })
            }

            return list
        },
        reCheckOne: function (val) {
            Vue.set(this, val[0], val[1])
            tableInfo.getBrData()
        },
    }
});
	//汇总明细内容显示
    var tableInfo = new Vue({
        el: '#context',
        //混合js字典庫
        mixins: [dic_transform, baseFunc, tableBase, mformat],
        data: {
            caqxContent:yzclLeft.caqxContent,
            is_csqx: {
                N03003200146: yzclLeft.caqxContent.N03003200146,
                N03003200125: yzclLeft.caqxContent.N03003200125,
                N03003200139: yzclLeft.caqxContent.N03003200139,
                N03003200141: yzclLeft.caqxContent.N03003200141,
                N03004200265: yzclLeft.caqxContent.N03004200265,
                N03003200189: yzclLeft.caqxContent.N03003200189,
            },
            isGoPrint: false,
            yzShow: false,
            yljgmc:yljgmc,
            ksbm: "",
            ksmc: "",
            yzlx: '1',
            allKs: [],
            yzJsonList: [],
            jsonList: [],
            jsonMxList: [],
            fylbsList: [],
            ksVal: null,
            type: null,
            ksrq: null,
            jsrq: null,
            popContent:{
                fyqd:'0',
            },
            brxxContent: {},
            fyqdContent: {},
            //detailFyqd: {},         // 明细清单
            fyhj: 0,
            jzrqfyhj:0,//截止日期费用合计，用于每日清单计算余额
            csqxContent:yzclLeft.caqxContent,
            yeList:[],
            haveYe:false,
        },
        mounted:function(){
            Mask.newMask(this.MaskOptions('timeVal'));
            Mask.newMask(this.MaskOptions('timeVal1'));
            //初始化检索日期！为今天0点到今天24点
            laydate.render({
                elem: '#timeVal',
                format: 'yyyy-MM-dd',
                rigger: 'click',
                type: 'date',
                theme: '#1ab394',
                done: function (value, data) { //回调方法
                    tableInfo.fyqdContent.ksrq1 = value;
                }
            });
            laydate.render({
                elem: '#timeVal1',
                format: 'yyyy-MM-dd',
                rigger: 'click',
                type: 'date',
                theme: '#1ab394',
                done: function (value, data) { //回调方法
                        tableInfo.fyqdContent.jsrq1 = value;
                }
            });
        },
        methods: {
            getBrData:function(){
                brSearch.json=yzclLeft.HszbrItem;
                this.brxxContent=brSearch.json;
                this.fyqdContent=brSearch.json;
                if(tableInfo.csqxContent.N03004200267){
                    this.fyqdContent.ksrq1 = this.getDayBeforeOrAfterDate(-1) + " " + tableInfo.csqxContent.N03004200267;
                }else{
                    this.fyqdContent.ksrq1 = this.fDate(this.fyqdContent.ryrq, 'date');
                }
                if(tableInfo.csqxContent.N03004200268){
                    this.fyqdContent.jsrq1 = getTodayDate() + " " + tableInfo.csqxContent.N03004200268;
                }else{
                    this.fyqdContent.jsrq1 = this.fyqdContent.bqcyrq == null ? this.fDate(new Date(), 'date') : this.fDate(new Date(), 'date');
                }
                this.getYexx(brSearch.json.zyh);
                // this.fyqdContent.jsrq = this.fDate(new Date(), 'date')+' 23:59:59';
                tableInfo.param.yebh = '';
                this.getDataList();
            },
            commonResultChange: function (val) {
                                Vue.set(tableInfo.popContent, 'yebh', val[0]);
                Vue.set(tableInfo.popContent, 'yexm', val[4]);
                if (val[0] == '000') {
                    tableInfo.param.yebh = '0';
                }else if(val[0] == 'all'){
                    tableInfo.param.yebh = '';
                } else if(val[0] == 'allye'){//所有婴儿
                    tableInfo.param.yebh = '1';
                }else{//其中一名新生儿
                    tableInfo.param.yebh = val[0];
                }
                this.getDataList();
            },
            //获取婴儿信息
            getYexx: function (zyh) {
                var parm = {
                    zyh: zyh
                }
                $.getJSON('/actionDispatcher.do?reqUrl=New1HszHlywYexx&types=query&parm=' +
                    JSON.stringify(parm)
                    , function (json) {
                        tableInfo.yeList = [];
                        if (json.a == '0') {
                            if (json.d.list != null && json.d.list.length > 0) {
                                tableInfo.yeList.push({yexm: '全部', yebh: "all"});
                                tableInfo.yeList.push({yexm: '成人', yebh: "000"});
                                if(json.d.list.length > 1){
                                    tableInfo.yeList.push({yexm: '全部婴儿', yebh: "allye"});
                                }
                                json.d.list.forEach(item=>{
                                    tableInfo.yeList.push(item) ;
                                })
                                tableInfo.popContent.yebh = 'all';
                                tableInfo.haveYe = true;
                            } else {
                                tableInfo.popContent.yebh = '';
                                tableInfo.haveYe = false;
                            }
                        }
                    });
            },
            //对帐清单
            openCpt:function(){
                
                var frurl = window.top.J_tabLeft.obj.FrUrl + '/FR/ReportServer?reportlet=zyb%2Fzyb_brfyqd.cpt&zyh=' + this.brxxContent.zyh;
                console.log(frurl);
                this.topNewPage('对帐清单预览', frurl);
            },
            // 获取汇总清单
            getDataList: function () {
                if(brSearch.bk =='1'){
                    this.param.ksbm = brSearch.json.ryks;
                }else {
                    this.param.ksbm=''
                }
                this.param.ksrq = this.fyqdContent.ksrq1+' 00:00:00';
                this.param.jsrq = this.fyqdContent.jsrq1+' 23:59:59';
                this.param.hldj = 6;
                this.param.fylb = brSearch.popContent.lbbm =='%' ?null :brSearch.popContent.lbbm;
                this.param.zyh = this.brxxContent.zyh;
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszCxtjFyqd&types=queryMx&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == "0") {
                        tableInfo.totlePage = Math.ceil(json.d.total / tableInfo.param.rows);
                        //tableInfo.jsonList = json.d.list;
                        var arr = json.d.list;
                        var total = 0;
                        for (var i = 0; i < arr.length; i++) {
                            var fylistbak=arr[i].fymx;
                            for(var j = fylistbak.length - 1; j >= 0; j--){
                                if (fylistbak[j].fysl==0||fylistbak[j].fysl=='0') {
                                    arr[i].fymx.splice(j, 1);
                                }else {
                                    total += arr[i].fymx[j].fyje;

                                }
                            }
                        }
                        tableInfo.ksVal = '0005';
                        if(tableInfo.csqxContent.N03004200261 == '1'){
                            tableInfo.fyqdContent.fyhj = total;
                        }else{
                            tableInfo.fyqdContent.fyhj = json.d.jzrqfyhj;
                        }
						tableInfo.fyqdContent.lsfyhj = total;
                        tableInfo.fyqdContent.jzrqfyhj = json.d.jzrqfyhj;
                        tableInfo.jsonList = arr;

                        if(tableInfo.csqxContent.N03004200261 == '1'){
                            for (var i = 0; i < tableInfo.jsonList.length; i++) {
                                var doubleList = [];
                                var fymx = tableInfo.jsonList[i].fymx;
                                var index = 0;
                                for (var k = 0; k < fymx.length; k+=2) {
                                    var fymx2 = [];
                                    if(fymx[k]){
                                        fymx[k].xmmc = fymx[k].xmmc.substr(0,16);//显示不完就隐藏
                                        fymx2[0] = fymx[k];
                                    }
                                    if(fymx[k+1]){
                                        fymx[k+1].xmmc = fymx[k+1].xmmc.substr(0,16);//显示不完就隐藏
                                        fymx2[1] = fymx[k+1];
                                    }
                                    doubleList[index] = fymx2;
                                    index++;
                                }
                                tableInfo.jsonList[i].fymx = doubleList;
                            }
                        }
                        tableInfo.$forceUpdate();
                    }
                });
                this.getMxData();
            },
            // 获取明细清单
            getMxData: function () {
                //请求后台查询
                $.getJSON("/actionDispatcher.do?reqUrl=New1HszCxtjFyqd&types=queryFyqdMxxm&parm=" + JSON.stringify(this.param), function (json) {
                    if (json.a == "0") {
                    	var length=0;
                    	var fylistbak=[];
                        var total = 0;
                        tableInfo.jsonMxList = json.d.list;
                        fylistbak=json.d.list;
                        for (var i = fylistbak.length - 1; i >= 0; i--) {
                            total += fylistbak[i].fyje;
                            if (fylistbak[i].fysl==0||fylistbak[i].fysl=='0') {
                            	tableInfo.jsonMxList.splice(i, 1);
                            }
                        }
                        if(tableInfo.csqxContent.N03004200261 == '1'){
                            tableInfo.fyhj = total;
                        }else{
                            tableInfo.fyqdContent.fyhj = json.d.jzrqfyhj;
                        }
                        tableInfo.fyqdContent.jzrqfyhj = json.d.jzrqfyhj;
                        console.log(tableInfo.jsonMxList);
                        tableInfo.$forceUpdate();
                    }
                });
            },
            print: function () {
                $('.fyqdTable').css('zoom','0.7')
				
              setTimeout(function () {
                  window.print();
                  $('.fyqdTable').css('zoom','1')
              },50)
            },

            getYzData: function (index) {
                if (!this.fyqdContent.zyh) {
                    malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                    return
                }
                this.yzlx=index == undefined ? 1 : index;
                this.param = {
                    page: 1,
                    rows: 10,
                    sort: '',
                    order: 'asc',
                    zyh: this.fyqdContent.zyh,
                    yzlx: this.yzlx,
                };
                common.openloading('.yz_model');
                $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzd&hsbz=1&ksbm='+ yzclLeft.jsContent.ksbm +'&parm=' + JSON.stringify(this.param), function (json) {
                    if (json.a == '0') {
                        common.closeLoading()
                        tableInfo.yzJsonList = json.d.list;
                        var list = [];
                        for (var i = 0; i < tableInfo.yzJsonList.length; i++) {
                            tableInfo.yzJsonList[i]['xmmc'] = tableInfo.yzJsonList[i]['xmmc']?tableInfo.yzJsonList[i]['xmmc'].replace('null', ''):"";
                            tableInfo.yzJsonList[i]['yyffmc'] = tableInfo.yzJsonList[i]['yyffmc']?tableInfo.yzJsonList[i]['yyffmc'].replace('null', ''):"";
                            tableInfo.yzJsonList[i]['yyffmc'] = tableInfo.yzJsonList[i]['yyffmc']?tableInfo.yzJsonList[i]['yyffmc'].replace('null', ''):"";

                            if(tableInfo.sameSE(i) == 'start' || tableInfo.sameSE(i) == 'end' || tableInfo.sameSE(i) == 'all'){//输液显示单词计量，其他显示用法
                                tableInfo.yzJsonList[i].plusYyff = 'dcjl';
                            }else{
                                tableInfo.yzJsonList[i].plusXsyl = 'xsyl';
                            }

                            if(tableInfo.is_csqx.N03003200146 == '1'){
                                //yysm
                                var yysm = tableInfo.yzJsonList[i]['yysm'];
                                // @yqq 根据用药方法 或者xmmc来将医嘱单拆分为多个医嘱显示
                                if(yysm && yysm.length > 15 && yysm.indexOf('、') > -1){
                                    var yysmArr = yysm.split('、');
                                    var newArr = [];
                                    var temp = '';
                                    var c = 20;
                                    for (var j = 0; j < yysmArr.length; j++) {
                                        var t = temp;
                                        temp += (yysmArr[j] + '、');
                                        if(temp.length > c){
                                            newArr.push(t);
                                            temp = (yysmArr[j] + '、');
                                        }
                                        if(temp.length == c || j == (yysmArr.length - 1)){
                                            newArr.push(temp);
                                            temp = '';
                                        }
                                    }

                                    for (var j = 0; j < newArr.length; j++) {
                                        var obj = JSON.parse(JSON.stringify(tableInfo.yzJsonList[i]));
                                        obj.yysm = newArr[j].substring(0, newArr[j].lastIndexOf('、'));
                                        list.push(obj);
                                    }
                                }else{
                                    list.push(tableInfo.yzJsonList[i]);
                                }
                            }else{
                                list.push(tableInfo.yzJsonList[i]);
                            }


                            if(yzclLeft.caqxContent.N03004200243 == '1'){
                                if(tableInfo.sameSE(i) == 'end'){
                                    var obj = {
                                        yyffmc:tableInfo.yzJsonList[i]['yyffmc'],
                                        sysd:tableInfo.yzJsonList[i]['sysd'],
                                        sysddw:tableInfo.yzJsonList[i]['sysddw'],
                                        pcmc:tableInfo.yzJsonList[i]['pcmc'],
                                        ksrq:tableInfo.yzJsonList[i]['ksrq'],
                                        zxsj:tableInfo.yzJsonList[i]['zxsj'],
                                        ystzsj:tableInfo.yzJsonList[i]['ystzsj'],
                                        hstzsj:tableInfo.yzJsonList[i]['hstzsj'],
                                        fzh:0,
                                        end:'plus',
                                    };
                                    list.push(obj);
                                }
                            }
                        }
                        tableInfo.yzJsonList = [];
                        tableInfo.yzJsonList = list;
                    } else {
                        common.closeLoading()
                        malert("病人医嘱单信息查询失败！", 'top', 'defeadted');
                    }
                });
            },
            sameDate: function (name, index, type) {
                var val = this.yzJsonList[index][name];
                var ksrq=this.yzJsonList[index]['ksrq'];
                var prvVal = null, nextVal = null,prvKsrq=null,nextKsrq=null;
                if(index != this.yzJsonList.length - 1 ){
                    nextKsrq = this.yzJsonList[index + 1]['ksrq'];
                    nextVal = this.yzJsonList[index + 1][name]
                }
                if (index != 0) {
                    prvKsrq = this.yzJsonList[index - 1]['ksrq'];
                    prvVal = this.yzJsonList[index - 1][name];
                }else {
                    prvKsrq = this.yzJsonList[index]['ksrq'];
                    prvVal = this.yzJsonList[index][name];
                }
                if (!val) {
                    if(this.is_csqx.N03003200142 == '1'){
                        return 'line';
                    }else{
                        if (val == prvVal && val == nextVal){
                            return '';
                        }
                        else {
                            return  ''
                        }
                    }
                }
                if(this.is_csqx.N03003200142 == '1'){
                    if(val == prvVal && index != 0){
                        return 'line';
                    }
                }else{
                    if(index != 0){
                        if (ksrq ==prvKsrq &&  val == prvVal && val == nextVal){
                            if(this.caqxContent.N03004200269 != '1'){
                                return '""';
                            }
                        }
                    }

                }
                var reDate = new Date(val);
                if (type == 'ry') {
                    return reDate.getFullYear().toString().substring(2,4)+'-'+this.Appendzero((reDate.getMonth() + 1)) + '-' + this.Appendzero(reDate.getDate());
                } else if (type == 'sj') {
                    return this.Appendzero(reDate.getHours()) + ':' + this.Appendzero(reDate.getMinutes());
                }else if(type=='name'){
                    return this.yzJsonList[index][name]
                }
            },

            sameDate_arrow_head: function (name, index) {
                var val = this.yzJsonList[index][name];
                var prvVal = null, nextVal = null;
                if(index <= 0 ) return;
                if (index != this.yzJsonList.length - 1) {
                    nextVal = this.yzJsonList[index + 1][name]
                }
                if (index != 0) {
                    prvVal = this.yzJsonList[index - 1][name];
                }
                if(val != nextVal){
                    return 'head';
                }
                if (!val && nextVal){
                    return 'head';
                }
                if(index == (this.yzJsonList.length - 1)){
                    return 'head';
                }

            },
            sameDate_qm: function (name, index) {
                var val = this.yzJsonList[index][name];
                var prvVal = null, nextVal = null;
                if(index == 0){
                    return '';
                }
                if(index != 0){
                    prvVal = this.yzJsonList[index - 1][name]
                }
                if(index != this.yzJsonList.length - 1 ){
                    nextVal = this.yzJsonList[index + 1][name]
                }else{
                    return;
                }
                if (!val && !nextVal) {
                    return 'line';
                }
                if(val != prvVal && val == nextVal){
                    return '';
                }
                if(val == nextVal){
                    return 'line';
                }
            },
            sameDate_arrow_head_qm: function (name, index) {
                var val = this.yzJsonList[index][name];
                var afterNextVal = null;
                if(index <= 0 ) return;
                if (index < this.yzJsonList.length - 2) {
                    afterNextVal = this.yzJsonList[index + 2][name];
                }
                if(index == this.yzJsonList.length - 1){
                    return 'head';
                }
                if(val != afterNextVal){
                    return 'head';
                }

            },

            Appendzero: function (obj) {
                if (obj < 10) return "0" + "" + obj;
                else return obj;
            },
            sameSE: function (index) {
                var fzh = this.yzJsonList[index]['fzh'];
                if (fzh == 0) return false;
                if (index == 0 && fzh == this.yzJsonList[index + 1]['fzh']) {
                    return 'start';
                }
                var ksrq = this.yzJsonList[index]['ksrq'];
                if (index != 0) {
                    var prvFzh = this.yzJsonList[index - 1]['fzh'];
                    var prvKsrq = this.yzJsonList[index - 1]['ksrq'];
                }
                if (index != this.yzJsonList.length - 1) {
                    var nextFzh = this.yzJsonList[index + 1]['fzh'];
                    var nextKsrq = this.yzJsonList[index + 1]['ksrq'];
                }
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (index == this.yzJsonList.length - 1) { // 最后一个
                    if (this.yzJsonList[index].fzh == this.yzJsonList[index].fzh && this.yzJsonList[index].yzxh == this.yzJsonList[index - 1].yzxh) {
                        return 'end';
                    }
                    return 'null'
                }
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if ((fzh != prvFzh || this.yzJsonList[index].yzxh != this.yzJsonList[index-1].yzxh) && (fzh == nextFzh && this.yzJsonList[index].yzxh == this.yzJsonList[index + 1].yzxh)) {
                    return 'start';
                }

                if ((fzh == prvFzh && this.yzJsonList[index].yzxh == this.yzJsonList[index - 1].yzxh) && (fzh != nextFzh || this.yzJsonList[index].yzxh != this.yzJsonList[index + 1].yzxh)) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh && (ksrq == prvKsrq && ksrq == nextKsrq)) {
                    return 'all';
                }
                return 'null'
            },
            isShowItem: function (index) {
                console.log(111)
                if (this.yzJsonList[index + 1] == null) {
                    return true;
                }
                if (this.yzJsonList[index]['fzh'] == this.yzJsonList[index + 1]['fzh'] && this.yzJsonList[index]['yzxh'] == this.yzJsonList[index+1]['yzxh'] && this.yzJsonList[index]['fzh'] != 0) {
                    if (this.yzJsonList[index]['yyffmc'] == this.yzJsonList[index + 1]['yyffmc']) {
                        return false;
                    }
                }
                return true;
            }
        }
    });
    yzclLeft.$watch('HszbrItem', function (newVal,oldVal) {
        if(newVal.zyh != oldVal.zyh && this.index==7){
            tableInfo.getBrData()
        }
    })
