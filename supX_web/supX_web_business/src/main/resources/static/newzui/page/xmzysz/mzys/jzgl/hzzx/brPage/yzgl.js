//医嘱处理
// $("#zuiTable").uitable();
var qjIndex = 0;
var printGd = 20
var yyffcz = false;
var pccz = false;
var sfzt = false;
var index2 = "";
var zdtj = false;//指定添加
var tjIndex = 0;//添加位置
var change = false;
//顶部工具栏
var panel = new Vue({
    el: '.panel',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        qhCode: '0',
        ifShow: true,
        haveYe: false,
        timeTabArr: '',
        /*isShow: true,*/
        popContent: {
            yzxx: '2', //医嘱类型
            /*yzzt: '1',*/
            yzgl: '0',  //医嘱过滤
            yfbm: '2',  //药房
            yfmc: '',
            yess: '0',
            yebh: '999999'
        },
        /*YzdpopContent: {
            yzxx: '0'
        },*/
        Yf_List: [],
        CflxJosn: [],
        yfyycflx: [],        //药房拥有处方类型
        cf_List: [],
        is_csqx: {},
        qsxzList: [],//亲属选择
        yeContent: {},
    },
    mounted: function () {
        this.timeTabArr = getTodayDateTime()
        laydate.render({
            elem: '#timeTabArr',
            format: 'yyyy-MM-dd HH:mm:ss',
            rigger: 'click',
            type: 'datetime',
            theme: '#1ab394',
            done: function (value, data) { //回调方法
                hzList.timeTabArr = value;
                panel.timeTabArr = value;
            }
        });
        this.Wf_init();
        this.getCsqx();
    },
    computed: {
        yfbmListen: function () {
            if (this.is_csqx.yfbm) {
                for (var i = 0; i < this.Yf_List.length; i++) {
                    if (this.is_csqx.yfbm == this.Yf_List[i].yfbm) {
                        this.popContent.yfbm = this.Yf_List[i].yfbm
                        this.popContent.yfmc = this.Yf_List[i].yfmc
                    }
                }
            }else{
                if(this.Yf_List.length!=0){
                    this.popContent.yfbm = this.Yf_List[0].yfbm
                    this.popContent.yfmc = this.Yf_List[0].yfmc
                }

            }
        },
    },
    methods: {
        //获取参数权限
        getCsqx: function () {
            //获取参数权限
            parm = {
                "ylbm": 'N030032001',
                "ksbm": userNameBg.qxks
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N03003200101": //医嘱默认西药房  --下医嘱和申领药品时默认，为空则取本地配置文件药房编码
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200101 = csjson.csz;
                                    }
                                    break;
                                case "N03003200102": //中药医嘱默认药房 --输入药房编码
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200102 = csjson.csz;
                                    }
                                    break;
                                case "N03003200103": //医生作废医嘱权限	 0-提示是否打印;1-直接打印;2-不打印
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200103 = csjson.csz;
                                    }
                                    break;
                                case "N03003200104": //中药医嘱默认用药方法   --中药医嘱默认用药方法
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200104 = csjson.csz;
                                    }
                                    break;
                                case "N03003200105": //临时医嘱默认频次编码 --请录入频次编码
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200105 = csjson.csz;
                                    }
                                    break;
                                case "N03003200106": //录入医嘱时指定药品统筹类别  --0、不指定，1、指定
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200106 = csjson.csz;
                                    }
                                    break;
                                case "N03003200107": //保存出院医嘱时是否停嘱 --0、否，1、是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200107 = csjson.csz;
                                    }
                                    break;
                                case "N03003200108": //下转科医嘱是否自动停嘱参数 0、否，1、是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200108 = csjson.csz;
                                    }
                                    break;
                                case "N03003200109": //医嘱单打印行数 --医嘱单打印行数
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200109 = csjson.csz;
                                    }
                                    break;
                                case "N03003200110": //自动失效长期医嘱是否签停嘱医生和时间  --0=否，1=是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200110 = csjson.csz;
                                    }
                                    break;
                                case "N03003200111": //输液速度单位  --0、滴/分 1、gtt/min 2、毫升/小时
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200111 = csjson.csz;
                                    }
                                    break;
                                case "N03003200112": //检查医嘱填写诊断、申请信息 --0-否，1-有
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200112 = csjson.csz;
                                    }
                                    break;
                                case "N03003200113": //检验医嘱填写诊断、申请信息 0-否，1-有
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200113 = csjson.csz;
                                    }
                                    break;
                                case "N03003200114": //是否允许修改医嘱排序功能 0-无 1-有
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200114 = csjson.csz;
                                    }
                                    break;
                                case "N03003200115": //医嘱保存是否需要密码0-不需要,1-需要
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200115 = csjson.csz;
                                    }
                                    break;
                                case "N03003200116": //医嘱保存是否需要密码0-不需要,1-需要
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200116 = csjson.csz;
                                    }
                                    break;
                                case "N03003200117": //审核之后是否允许作废医嘱0-允许，1-不允许
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200117 = csjson.csz;
                                    }
                                    break;
                                case "N03003200118": //是否对抗生素药品使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200118 = csjson.csz;
                                    }
                                    break;
                                case "N03003200126": //是否对抗肿瘤药品使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200126 = csjson.csz;
                                    }
                                    break;
                                case "N03003200127": //是否对药品种类中成药使用限制0-否，1-是
                                    if (csjson.csz) {
                                        panel.is_csqx.cs003003200127 = csjson.csz;
                                    }
                                    break;
                                case "N03003200119": //新开医嘱是否清空前面医嘱','0-不清空,1=清空
                                    if (csjson.csz) {
                                        panel.is_csqx.N03003200119 = csjson.csz;
                                    }
                                    break;
                                case "N03003200119":
                                    if (csjson.csz) {
                                        brjzFoot.hlyyCheckUrl = csjson.csz + "check";
                                    }
                                    break;
                                case "N03003200123":
                                    if (csjson.csz) {
                                        brzcList00.is_csqx.N03003200123 = csjson.csz;
                                    }
                                    break;

                            }
                        }

                    }
                } else {
                    malert("参数权限获取失败：" + json.c, 'top', 'defeadted');
                }
            });


            //获取参数权限
            var parm = {
                "ylbm": 'N010064001',
                "ksbm": ksbm
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=csqx&parm=" + JSON.stringify(parm), function (json) {
                var s_address = ""
                if (json.a == 0) {
                    if (json.d.length > 0) {
                        for (var i = 0; i < json.d.length; i++) {
                            var csjson = json.d[i];
                            switch (csjson.csqxbm) {
                                case "N01006400108": //合理用药接口地址参数
                                    if (csjson.csz) {
                                        s_address = csjson.csz;//默认加载第一个

                                        // tableInfo.getData();

                                    }
                                    break;
                            }
                        }
                    }

                } //else {
                //     malert('参数权限获取失败'+json.c,'top','defeadted')

                // }

                if (s_address.length > 0) {
                    hzList.hlyyCheckUrl = s_address + "/check";//默认加载第一个


                } else {
                    hzList.hlyyCheckUrl = "http://127.0.0.1:8082/hlyy/check";//默认加载第一个
                }
                // left.getData();

            });


        },
        //获取药房拥有处方
        getYfyycf: function () {
            var yfcf_dg = {page: 1, rows: 100, sort: "cflxbm", order: "asc", parm: ""};
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhYlfwxmYfyycflx&types=query&dg=' + JSON.stringify(yfcf_dg), function (data) {
                if (data.a == 0) {
                    panel.yfyycflx = data.d.list;
                    brzcList00.yfyycflx = data.d.list;
                    //药房检索处方类型
                    panel.cfglData();
                } else {
                    malert('获取药房拥有处方' + data.c, 'top', 'defeadted')
                }
            });
        },
        //调用公共方法处理处方过滤问题
        cfglData: function () {
            this.popContent.cflxbm = ''
            //处方类型默认为西药（根据药房过滤出处方集合）
            this.CflxJosn = jsonFilter(this.yfyycflx, "yfbm", this.popContent.yfbm);
            //显示药房关联的处方
            if (this.CflxJosn.length != 0) {
                for (var i = 0; i < this.Yf_List.length; i++) {
                    if (this.popContent.yfbm == this.Yf_List[i].yfbm) {
                        this.popContent.cflxbm = this.Yf_List[i]['qscflx'];
                    }
                }
            }
        },
        //组件选择下拉框之后的回调
        resultLxChange: function (val) {
            //先获取到操作的哪一个
            Vue.set(this.popContent, 'cflxbm', val[0]);
            Vue.set(this.popContent, 'cflxmc', val[4]);
            Vue.set(this.popContent, 'mbmc', val[0]);
            this.$forceUpdate()
            hzList.Wf_selectYZ()
        },
        //医嘱类型改变
        resultChange_yzxx: function (val) {
            Vue.set(this[val[2][0]], val[2][1], val[0]);
            cqyzd.which = val[0];
            if (val[0] === '1') {
                cqyzd.isShow = false;
                lsyzd.isShow = true;
                lsyzd.getData();
            } else {
                lsyzd.isShow = false;
                cqyzd.isShow = true;
                cqyzd.getData();
            }
        },

        commonResultChange: function (val) {
            var yebhVal = val[0];
            Vue.set(this.popContent, 'yebh', yebhVal);
            // Vue.set(this[val[2][0]], val[3], val[4]);
            /********************************这里给医嘱单查询添加条件（解决母亲婴儿分开打印问题）*******************************/
            if (yebhVal == '000') {//母亲本人
                fyxmTab.yzdcs.cfss = '0';
                fyxmTab.yzdcs.yebh = "";
            } else if (yebhVal == '999999') {//全部
                fyxmTab.yzdcs.cfss = '';
                fyxmTab.yzdcs.yebh = "";
            } else {//其中一名新生儿
                fyxmTab.yzdcs.cfss = '1';
                fyxmTab.yzdcs.yebh = yebhVal;
            }
            //值改变后请求一次长期医嘱单
            toolMenu_yzd.long(0);
            /********************************上面给医嘱单查询添加条件（解决母亲婴儿分开打印问题）*******************************/
            hzList.Wf_selectYZ()
        },
        //医嘱过滤改变
        resultChange_type: function (val) {
            Vue.set(this[val[2][0]], val[2][1], val[0]);
            hzList.Wf_selectYZ()
        },
        //药房改变
        resultChange_text: function (val) {
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            Vue.set(this[val[2][0]], 'yfmc', val[0]);
            panel.cfglData();
            hzList.Wf_selectYZ()
        },
        //初始化数据
        Wf_init: function () {
            var yf_dg = {page: 1, rows: 1000, sort: "yfbm", order: "asc", parm: ""};
            this.$http.get("/actionDispatcher.do",{params:{reqUrl:'New1XtwhYlfwxmYf',types:'query',dg:JSON.stringify(yf_dg)}}).then(function (json) {
                if (json.body.a == 0) {
                    panel.Yf_List = json.body.d.list;  //绑定药房
                    brzcList00.Yf_List = json.body.d.list;  //绑定药房
                    panel.getYfyycf();
                } else {
                    malert('获取药房失败', 'top', 'defeadted');
                }
            }, function (json) {
            })
        },
        tabCode: function () {
            panel.qhCode = panel.qhCode == '0' ? '1' : panel.qhCode == '1' ? '2' : panel.qhCode == '2' ? '0' : ''
        }
    },
});

//医嘱列表
var hzList = new Vue({
        el: '.hzList',
        mixins: [dic_transform, baseFunc, tableBase, mformat, printer],
        components: {
            'search-table': searchTable,
            'search-table2': searchTable,
            'search-table3': searchTable
        },
        data: {
            zfbz:{
                '药品':'1',
                '诊疗':'2'
            },
            kssmd: {},  //抗生素使用目的
            haveYe: false,
            kss: false,
            yeList: [],
            numClass: null,
            zcyYzList: [],
            isShow: true,
            Init: false,     //初始化标志
            TableInit: false,  //锁表头初始化状态
            Yf_List: [],     //药房列表
            Yzxx_List: [],    //医嘱列表
            Yyff_List: [],    //用药方法
            Pc_List: [],     //频次
            Yzxm_List: [],   //医嘱项目列表
            BrxxJson: {},    //当前住院病人信息
            csContent: {},
            is_ksbm: '',   //当前科室编码
            is_ksmc: '',   //当前科室名称
            is_yfbm: '',   //当前药房编码
            is_yfmc: '',   //当前药房名称
            is_yzlx: '%',   //医嘱类型   %-全部  1-长期,0-临时
            is_yzgl: '0',   //医嘱过滤   %-未停  1-已停,0-作废

            popContent: {},  //选中的值
            selSearch: 0,
            selSearch2: 0,
            selSearch3: 0,
            searchCon3: [],
            searchCon2: [],
            searchCon: [],
            color: {
                '0': '',
                '1': 'red',
                '2': 'red',
                '3': 'blue',
                '4': 'zgs',
            },
            title: '医嘱项目',
            them_tran: {
                'zdksbz': dic_transform.data.zdksbz_tran,
                'nh': dic_transform.data.nh_tran,
                'jclx': dic_transform.data.yzjcfl_tran,
            },
            them: {
                '类型': 'lx', '项目编码': 'xmbm', '项目名称': 'xmmc', '规格': 'xmgg', '库存': 'kc', '单位': 'yfdwmc',
                '价格': 'cklj', '分装比例': 'fzbl', '医保': 'ybtclbmc', '农合': 'nh', '药品种类': 'zlmc', '基本剂量': 'jbjl',
                '检查类型': 'jclx', '剂量单位': 'jldwbm', '剂量单位': 'jldwmc', '用药方法': 'yyff',
                '最大剂量': 'zdjl', '抗生素级别': 'kssjb', '用药方法': 'yyffmc', '可拆分': 'kcfbz'
            },
            // them_tran: {'zdksbz': dic_transform.data.zdksbz_tran, 'gwyp': dic_transform.data.gwyp_tran},
            // them: {
            //     '药品名称': 'ypmc', '药品规格': 'ypgg', '库存': 'kcsl', '药房单位': 'yfdwmc',
            //     '价格': 'yplj', '医保统筹': 'ybtclbmc', '农保统筹': 'nbtclbmc', '高危药品':'gwyp'
            // },
            //
            title2: '用药方法',
            them_tran2: {'zxdlx': dic_transform.data.zxdlx_tran},
            them2: {
                '方法编码': 'yyffbm',
                '方法名称': 'yyffmc',
                '拼音简码': 'pydm',
                '执行单类型': 'zxdlx'
            },
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            pageStr: {
                page: 1,
                rows: 20,
                total: null
            },
            queryStr: {
                page: 1,
                rows: 20,
                total: null
            },
            them_tran3: {'tybz': dic_transform.data.stopSign},
            them3: {
                '频次编码': 'pcbm',
                '频次名称': 'pcmc',
                '拉丁名称': 'ldmc',
                '次数': 'cs',
                '停用标志': 'tybz'
            },
            ifClick: true,
            param: {},
            csContent1: {},
            isShowYzTem: false,
            falg: false,
            _laydate: '',
            isShowLsYz: false,
            printList: [],
            jcList: [],
            jyList: [],
            isChecked: [],
            toolIsSHow: true,
            checkAll: false,
            isCheck: [],
            kssContent: null, //查询到的抗生素
            sfqk: 0, //是否清空
            yebhIndex: 0,
            hlyyCheckUrl: '',
            hlyyYpmx: [],
            hlyynewYpmx: {
                dcjl: 0,
                pcbm: '',
                rcs: 0,
                ypbm: ''
            },
        },

        watch: {
            'Yzxx_List': function (ov, nv) {
                // this.fiexd();
                this.height();
                changeWin();
            },
            'isChecked': function () {
                if (this.notempty(this.isChecked).length == this.Yzxx_List.length) {
                    this.isChecked.every(function (el) {
                        if (el === true) {
                            return hzList.isCheckAll = true
                        } else {
                            return hzList.isCheckAll = false
                        }
                    })
                }
            }
        },
        created: function () {
            this.Wf_selectYZ(xyz);
        },
        computed: {
            yxxSum: function () {
                var total = 0;
                var sum = this.Yzxx_List.reduce(function (total, num) {
                    if (num.zt == '4' && num != null && num.sl != undefined && num.dj != undefined) {
                        total = total + num.sl * num.dj
                    }
                    return total;
                }, 0)
                return sum.toFixed(2);
            },
        },
        methods: {
            userdzbl:function(){
                if(!userNameBg.Brxx_List.brid || !userNameBg.Brxx_List.ghxh){
                    malert("请选择患者!",'top','defeadted');
                    return;
                };
                //写注册信息
                $.getJSON(
                    "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=HZXX&method=DSEMR_HZXX_ADD&id=" + userNameBg.Brxx_List.brid+"&json=" + JSON.stringify(this.param), function (json) {
                        if (json.a == "0") {
                            //写就诊信息
                            $.getJSON(
                                "/actionDispatcher.do?reqUrl=New1InterfaceDzbl&types=JZXX&method=DSEMR_JZXX_ADD&id=M" + userNameBg.Brxx_List.ghxh+"&json=" + JSON.stringify(hzList.param), function (json) {
                                    if (json.a == "0") {
                                        $.ajaxSettings.async = false;
                                        var sxdz = "";
                                        var user = "";
                                        var password = "";
                                        //取病历参数
                                        $.getJSON("/actionDispatcher.do?reqUrl=New1DzblCs&types=query&json=" + JSON.stringify(hzList.param), function (json) {
                                            if (json.a == "0") {
                                                hzList.csContent1 = JSON.parse(JSON.stringify(json.d.list[0]));
                                                sxdz = hzList.csContent1.blSxdz;
                                                user = userId;
                                                //取操作员信息
                                                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhKsryRybm&types=queryOne&rybm=" + userId, function (json) {
                                                    if (json.a == "0") {
                                                        password=json.d.password;
                                                    }
                                                });
                                                if (!sxdz) {
                                                    malert("书写地址为空，打开病历失败！",'top','defeadted');
                                                    return
                                                }
                                                if (!user) {
                                                    malert("用户名为空，打开病历失败！！",'top','defeadted');
                                                    return
                                                }
                                                if (!password) {
                                                    malert("用户密码为空，打开病历失败！",'top','defeadted');
                                                    return
                                                }
                                                var url = sxdz + "/BLCX/HISWriteDSEMR?sn=zyh=M" + userNameBg.Brxx_List.ghxh + ",userid=" + user + ",password=" + password + ",lyzyhmz=1,blhhl=0"; //0医生 1 护理
                                                window.open(url);
                                            }
                                        });

                                    } else {
                                        malert("患者信息上传失败失败："+json.c,'top','defeadted')
                                    }
                                });


                        } else {
                            malert("患者信息上传失败失败："+json.c,'top','defeadted')
                        }
                    });
            },
            reCheckChange: function (val) {
                var fzh = this.Yzxx_List[val[1]].fzh, yzxh = this.Yzxx_List[val[1]].yzxh;//临时分组号
                for (var i = 0; i < this.Yzxx_List.length; i++) {
                    if (parseInt(fzh) != 0 || parseInt(fzh) != '') {
                        if (fzh == this.Yzxx_List[i].fzh && yzxh == this.Yzxx_List[i].yzxh) {
                            Vue.set(this.isChecked, i, !this.isChecked[i]);
                        }
                    } else if (fzh == 0 || fzh == '') {
                        // Vue.set(this.isChecked, i, false);
                        Vue.set(this.isChecked, val[1], val[2]);
                    }
                }
                if (!val[2]) this.isCheckAll = false;
            }

            ,
            height: function () {
                var that = this;
                setTimeout(function () {
                    var nextTickHeight = $('.nextTickHeight');
                    for (var i = 0; i < nextTickHeight.length; i++) {
                        nextTickHeight[i].style.height = parseInt(that.$refs.body.style.height.split('px')[0]) - 5 + 'px'
                    }
                }, 1100)
            },
            yzShow: function () {
                this.isShow = true;
                panel.isShow = true;
                cqyzd.isShow = false;
                $('#yzd').addClass('hide');
                // this.fiexd();
                panel.ifShow = true;
                toolMenu_yzd.long(0);
            },
            pubFzhsame: function (index) {
                if (this.Yzxx_List[index].fzh > 0) {
                    //同组药品用药方法，频次，输液速度，输液速度单位同样改变
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (this.Yzxx_List[index].fzh == this.Yzxx_List[i].fzh) {
                            this.Yzxx_List[i].yyff = this.Yzxx_List[index].yyff;
                            this.Yzxx_List[i].yyffmc = this.Yzxx_List[index].yyffmc;
                            this.Yzxx_List[i].pcbm = this.Yzxx_List[index].pcbm;
                            this.Yzxx_List[i].pcmc = this.Yzxx_List[index].pcmc;
                            this.Yzxx_List[i].sysd = this.Yzxx_List[index].sysd;
                            this.Yzxx_List[i].sysddw = this.Yzxx_List[index].sysddw;
                        }
                    }
                }
            }
            ,
            //查询抗生素申请记录
            getKssSq: function (item) {
                if (item.kssjb && item.kssjb != '1' && panel.is_csqx.cs003003200118 != '0') {
                    var ypbm = null;
                    if (item.xmbm) {
                        ypbm = item.xmbm;
                    } else {
                        ypbm = item.ypbm;
                    }
                    var json = {
                        rows: 50000,
                        //spbz: '1',
                        zyh: userNameBg.Brxx_List.zyh,
                        ypbm: ypbm
                    };
                    var result;
                    $.ajax({
                        type: "get",
                        url: '/actionDispatcher.do?reqUrl=New1ZyysYsywKsssyba&types=query&parm=' + JSON.stringify(json),
                        cache: false,
                        async: false,
                        success: function (data) {
                            var json = JSON.parse(data)
                            if (json.a == 0) {
                                if (json.d.list.length > 0) {
                                    hzList.kssContent = json.d.list[0];
                                }
                                result = hzList.pubKss(item)
                            } else {
                                malert("获取项目列表失败！", data.c, 'top', 'defeadted');
                                return true
                            }
                        }

                    });
                    return result
                } else {
                    return true
                }

            },

            //针对抗生素药物的判定（住院医生（04）一级（2），主治医生（03）一（2）、二（3）级，副主任医师（02）和主任医师（01）一（2）、二（3）、三（4）级）
            pubKss: function (json) {
                hzList.sfqk = 0;
                var ypmc = '';
                if (!json['ypmc']) {
                    ypmc = json['xmmc'];
                } else {
                    ypmc = json['ypmc'];
                }
                if (window.top.J_tabLeft.jszgdm == '04' || window.top.J_tabLeft.jszgdm == null) {
                    if (json.kssjb == '3' || json.kssjb == '4') {
                        if (hzList.kssContent == null || hzList.kssContent == '') {
                            malert(ypmc + "属于" + hzList.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                            hzList.sfqk = 1;
                            return false;
                        } else if (hzList.kssContent.spbz != 1) {
                            malert(ypmc + "属于" + hzList.kssjb_tran[json['kssjb']] + "," + hzList.sfsp_tran[isthzL.kssContent.spbz] + "不允许使用", 'top', 'defeadted');
                            hzList.sfqk = 1;
                            return false
                        }
                    }
                }
                if (window.top.J_tabLeft.jszgdm == '03') {
                    if (json.kssjb == '4') {
                        if (hzList.kssContent == null || hzList.kssContent == '') {
                            malert(ypmc + "属于" + hzList.kssjb_tran[json['kssjb']] + "不允许使用", 'top', 'defeadted');
                            hzList.sfqk = 1;
                            return false;
                        }
                    }
                }
                if (json.kssjb && json.kssjb > '1') {
                    hzList.kss = true
                    console.log(123)
                    setTimeout(function () {
                        hzList.$refs.kss.setLiShow(hzList.$refs.kss.$refs.inputFu)
                        hzList.$refs.kss.setLiFirst(hzList.$refs.kss.$refs.inputFu)
                    }, 1000)
                    return true
                }
            },
            zyyz() {
                brzcList00.sfxz = true;
                brzcList00.getYyff();
                brzcList00.numOne = 0;
                brzcList00.add();
            }
            ,
            //医嘱单显示
            printJcShow: function () {
                this.isShow = false;
                panel.isShow = false;
                cqyzd.isShow = true;
                $('#yzd').removeClass('hide')
            }
            ,
            //打印医嘱单
            doPrint: function (istpye) {
                cqyzd.doPrint(istpye);
            }
            ,
            openJcjy: function () {
                jcjyfy.show();
                jcjyfy.getJcjyd();
            }
            ,

            //************************打印检查检验申请单start***************************
            printJcJy: function () {
                userNameBg.Brxx_List.brxb = hzList.brxb_tran[userNameBg.Brxx_List.brxb];
                userNameBg.Brxx_List.nldw = hzList.nldw_tran[userNameBg.Brxx_List.nldw];
                // 获取选中检查检验的信息
                var jcList = [], jyList = [];
                if (this.isChecked.length > 0) {
                    for (var i = 0; i < this.isChecked.length; i++) {
                        if (this.isChecked[i] == true) {
                            // @yqq打印出现重复 换成以下代码
                            if (this.Yzxx_List[i]['jcfl'] == '1') {
                                jcList.push(this.Yzxx_List[i]);
                            } else if (this.Yzxx_List[i]['jcfl'] == '2') {
                                jyList.push(this.Yzxx_List[i]);
                            }
//                         if (this.Yzxx_List[i]['jcfl'] == '1') {
//                            for(var j=0;j<this.Yzxx_List.length;j++){
//                            	if(this.Yzxx_List[i].sqdh!=null && this.Yzxx_List[i].sqdh==this.Yzxx_List[j].sqdh){
//                            		jcList.push(this.Yzxx_List[j]);
//                            	}
//                            }
//                        } else if (this.Yzxx_List[i]['jcfl'] == '2') {
//                             ddd:
//                            for(var j=0;j<this.Yzxx_List.length;j++){
//                                if(!this.Yzxx_List[j].mbbm && this.isChecked[j] == true && this.Yzxx_List[j].sqdh  && this.Yzxx_List[i].sqdh==this.Yzxx_List[j].sqdh){
//                                    this.doPrintJy([this.Yzxx_List[j]]);
//                                    this.isChecked[j]=false
//                                    break;
//                                } else if(this.Yzxx_List[j].mbbm && this.isChecked[j] == true && this.Yzxx_List[i].sqdh && this.Yzxx_List[i].sqdh==this.Yzxx_List[j].sqdh){
//                            		jyList.push(this.Yzxx_List[j]);
//                                    this.isChecked[j]=false
//                            	}
//                            }
//                        }
                        }
                    }
                } else {
                    malert('请选择需要打印的申请单', 'top', 'defeadted');
                    return
                }
                if (jcList.length == 0 && jyList.length == 0) {
                    malert('没有检查检验的项目', 'top', 'defeadted');
                    return
                }
                if (jcList.length != 0) {
                    userNameBg.Brxx_List.jymd = jcList[0].jymd;
                    userNameBg.Brxx_List.jcms = jcList[0].jcms;
                    userNameBg.Brxx_List.wxts = jcList[0].wxts;
                    userNameBg.Brxx_List.lczd = jcList[0].lczd;
                    userNameBg.Brxx_List.lczz = jcList[0].lczz;
                    userNameBg.Brxx_List.bzsm = jcList[0].yysm;
                    userNameBg.Brxx_List.zxksmc = jcList[0].zxksmc;
                    userNameBg.Brxx_List.yzlx = hzList.yzlx_tran[jcList[0].yzlx];
                    var date = new Date();
                    var dyrq_n = hzList.fDate(date, 'year');
                    var dyrq_y = hzList.fDate(date, 'month');
                    var dyrq_r = hzList.fDate(date, 'day');
                    var dyrq_sf = hzList.fDate(date, 'times');
                    userNameBg.Brxx_List.n = dyrq_n;
                    userNameBg.Brxx_List.y = dyrq_y;
                    userNameBg.Brxx_List.r = dyrq_r;
                    userNameBg.Brxx_List.sf = dyrq_sf;
                    console.log(userNameBg.Brxx_List);
                    if (jcList.length <= 3) {
                        this.doPrintJc(jcList);
                    } else {
                        for (var i = 0; i < jcList.length; i += 3) {
                            var xjsonlist = jcList.slice(i, i + 3);
                            this.doPrintJc(xjsonlist);
                        }
                    }
                }
                if (jyList.length != 0) {
                    userNameBg.Brxx_List.jymd = jyList[0].jymd;
                    userNameBg.Brxx_List.jcms = jyList[0].jcms;
                    userNameBg.Brxx_List.wxts = jyList[0].wxts;
                    userNameBg.Brxx_List.lczd = jyList[0].lczd;
                    userNameBg.Brxx_List.bbsm = jyList[0].bbsm;
                    userNameBg.Brxx_List.bzsm = jyList[0].yysm;
                    userNameBg.Brxx_List.zxksmc = jyList[0].zxksmc;
                    userNameBg.Brxx_List.yzlx = hzList.yzlx_tran[jyList[0].yzlx];
                    var date = new Date();
                    var dyrq_n = hzList.fDate(date, 'year');
                    var dyrq_y = hzList.fDate(date, 'month');
                    var dyrq_r = hzList.fDate(date, 'day');
                    var dyrq_sf = hzList.fDate(date, 'times');
                    userNameBg.Brxx_List.n = dyrq_n;
                    userNameBg.Brxx_List.y = dyrq_y;
                    userNameBg.Brxx_List.r = dyrq_r;
                    userNameBg.Brxx_List.sf = dyrq_sf;
                    if (jyList.length <= 3) {
                        this.doPrintJy(jyList);
                    } else {
                        for (var i = 0; i < jyList.length; i += 3) {
                            var xjsonlist = jyList.slice(i, i + 3);
                            this.doPrintJy(xjsonlist);
                        }
                    }
                }

            }
            ,
            // 打印住院检验申请单
            doPrintJy: function (jyList) {
                // 查询打印模板
                var json = {repname: '住院检验申请单'};
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 清除打印区域
                    hzList.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    hzList.drawList = JSON.parse(json.d[0]['canvas']);
                    hzList.creatCanvas();
                    hzList.reDraw();
                    // 为打印前生成数据
                    hzList.printContent(userNameBg.Brxx_List);
                    hzList.printTrend(jyList);
                    // 开始打印
                    window.print();
                });
            }
            ,
            // 打印住院检查申请单
            doPrintJc: function (jcList) {
                // 查询打印模板
                var json = {repname: '住院检查申请单'};
                $.getJSON("/actionDispatcher.do?reqUrl=New1XtwhXtpzPjgs&type=query&json=" + JSON.stringify(json), function (json) {
                    // 清除打印区域
                    hzList.clearArea(json.d[0]);
                    // 绘制模板的canvas
                    hzList.drawList = JSON.parse(json.d[0]['canvas']);
                    hzList.creatCanvas();
                    hzList.reDraw();
                    // 为打印前生成数据
                    var reg = /^[\'\"]+|[\'\"]+$/g;
                    userNameBg.Brxx_List.czyxm = sessionStorage.getItem("userName" + userId).replace(reg, '');
                    hzList.printContent(userNameBg.Brxx_List);
                    hzList.printTrend(jcList);
                    // 开始打印
                    window.print();
                });
            }
            ,
            //************************打印检查检验申请单end***************************

            //修改医属
            Wf_update: function (index) {
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                if (this.Yzxx_List[index].shbz == '0') {
                    if (this.Yzxx_List[index].lx == "诊疗") { // 检查检验医嘱点击修改是弹出检查检验模板来选择的
                        this.openJcjy();
                    } else {
                        this.Yzxx_List[index].readonly = false;   //只读标志
                        this.Yzxx_List[index].updateBz = true;   //修改标志
                    }
                } else {
                    malert("第" + index + "条医嘱已审核，不能修改！", 'top', 'defeadted');
                }
            }
            ,
            //删除医嘱
            Wf_delete: function (index) {
                this.falg = false
                $.ajaxSettings.async = false;
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return;
                }
                if (this.isChecked.length > 0) {

                } else {
                    this.isChecked[index] = true;
                }

                if (this.isChecked.length > 0) {
                    common.openConfirm("你确定要删除所选医嘱信息吗？", this.deleteyz, function () {
                        hzList.isChecked = []
                    })
                } else {
                    malert("请选中需要删除的医嘱信息！", 'top', 'defeadted');

                }
            }
            ,
            deleteyz: function () {
                var deleteYzList = [], removeObj = [];
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        hzList.Yzxx_List[i].ypbz= hzList.zfbz[hzList.Yzxx_List[i].lx]
                        if (hzList.Yzxx_List[i].sfcy == '0' || hzList.Yzxx_List[i].sfcy == null || hzList.Yzxx_List[i].sfcy == undefined) {
                            if (hzList.Yzxx_List[i].insertBz == true) {
                                hzList.Yzxx_List[i].remove = true;  //新增加的直接删除
                                continue;
                            }
                            if (hzList.Yzxx_List[i].zt != '2') {
                                //根据参数判断是否允许作废医嘱
                                if(hzList.Yzxx_List[i].zxbz=='1'){
                                    malert("第【" + (i + 1) + "】行的医嘱信息已执行不允许作废","top","defeadted");
                                    continue
                                }
                                if(panel.is_csqx.cs003003200117 == '1'){//审核之后是否允许作废医嘱0-允许，1-不允许
                                    if(hzList.Yzxx_List[i].shbz == '1'){
                                        malert("根据参数第【" + (i + 1) + "】行的医嘱信息已审核不允许作废","top","defeadted");
                                        continue;
                                    }
                                }
                                this.falg = true;
                                deleteYzList.push(hzList.Yzxx_List[i]);
                            }
                        } else {
                            if (hzList.Yzxx_List[i].zt != '2') {
                                this.falg = true
                                deleteYzList.push(hzList.Yzxx_List[i]);
                            }
                        }
                    }
                }
                for (var j = hzList.Yzxx_List.length - 1; j >= 0; j--) {
                    if (hzList.Yzxx_List[j].remove) {
                        hzList.Yzxx_List.splice(j, 1)
                    }
                }

                this.isChecked = [];
                this.isCheckAll = false;


                //没有记录就直接返回
                if (this.falg == true) {
                    if (deleteYzList.length <= 0) {
                        malert("未找到允许作废的医嘱信息", 'top', 'defeadted');
                        return false;
                    }
                    for(var i=0;i<deleteYzList.length;i++){
                        deleteYzList[i].ypbz=deleteYzList[i].zloryp=='yp'?'1':'2';
                        deleteYzList[i].tsyz=deleteYzList[i].tsyz?'1':'0';
                    }
                    var json = {
                        list: [
                            {
                                yzxx: deleteYzList,          //医嘱信息
                                lczd: []              //诊断信息（检查，检验的诊断）
                            }
                        ]
                    };
                    this.$http.post('/actionDispatcher.do?reqUrl=New1MjzglYzd&types=yzzf', JSON.stringify(json)).then(function (data) {
                        if (data.body.a == 0) {
                            hzList.Wf_selectYZ();  //查询医嘱
                            malert("医嘱作废成功！", 'top', 'success');
                            this.isChecked = [];
                        } else {
                            malert("医嘱作废失败：" + data.body.c, 'top', 'defeadted');
                        }
                    });
                }
            }
            ,
            //停医嘱
            Wf_stop: function (index) {
                var deleteYzList = [];
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                if (index != null) {
                    this.isChecked = [];
                    this.isChecked[index] = true;
                }
                var sfts = true;
                if (this.isChecked.length > 0) {
                    for (var list in this.isChecked) {
                        if (this.isChecked[list] == true) {
                            hzList.Yzxx_List[list].ypbz= hzList.zfbz[hzList.Yzxx_List[list].lx]
                            // if (this.Yzxx_List[list].rqxgbz == null) {
                            //     this.Yzxx_List[list].rqxgbz = '0';
                            // }
                            this.Yzxx_List[list].rqxgbz = '1';
                            this.Yzxx_List[list].ystzsj = new Date(panel.timeTabArr).getTime();
                            if (this.Yzxx_List[list].shbz == '1') {
                                if (this.Yzxx_List[list].zt == '3') {
                                    deleteYzList.push(JSON.parse(JSON.stringify(this.Yzxx_List[list])))
                                }
                            } else {
                                malert("第" + list + "条医嘱未审核，请直接作废！","top","defeadted");
                            }

                        }
                    }
                } else {
                    malert("请选择需要停的医嘱！", 'top', 'defeadted');
                    return false;
                }
                //没有记录就直接返回
                if (deleteYzList.length <= 0) {
                    malert("未找到允许停嘱的信息", 'top', 'defeadted');
                    return false;
                }
                for (var j = 0; j < deleteYzList.length; j++) {
                    if (deleteYzList[j].rqxgbz == '1') {
                        sfts = false;
                    }
                    if (deleteYzList[j].rqxgbz == '0') {
                        sfts = true;
                        break;
                    }
                }

                //没有记录就直接返回
                var json = {
                    list: [
                        {
                            yzxx: deleteYzList,          //医嘱信息
                            lczd: []              //诊断信息（检查，检验的诊断）
                        }
                    ]
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1MjzglYzd&types=ystz', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        hzList.Wf_selectYZ();  //查询医嘱
                        malert('医嘱停止申请成功', 'top', 'success');
                        this.isChecked = [];
                        //发送通知
                        if (sfts) {
                            var sendmsg = {
                                msgtype: '1',
                                ksbm: userNameBg.Brxx_List.ryks,
                                yljgbm: jgbm,
                                yqbm: yqbm,
                                msg: '有停医嘱待审核，病人：' + userNameBg.Brxx_List.brxm + ",挂号序号:" + userNameBguserNameBg.Brxx_List.ghxh,
                                toaddress: 'page/hsz/hlyw/yzgl/yzgl.html',
                                pagename: '护士站医嘱管理',
                                sbid: userNameBg.Brxx_List.ghxh + "_" + hzList.fDate(new Date(), 'YY'),
                                ylbm: 'N030042002',
                            };
                            hzList.ifClick = true;
                            window.top.J_tabLeft.socket.send(JSON.stringify(sendmsg));
                        }
                    } else {
                        malert("医嘱停止申请失败:" + data.body.c, 'top', 'defeadted');
                    }
                });

            }
            ,

            //取消停医嘱
            cancelStopYz: function (index) {
                var deleteYzList = [];
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                if (index != null) {
                    this.isChecked = [];
                    this.isChecked[index] = true;
                }
                if (this.isChecked.length > 0) {
                    for (var list in this.isChecked) {
                        hzList.Yzxx_List[list].ypbz= hzList.zfbz[hzList.Yzxx_List[list].lx]
                        if (this.isChecked[list] == true) {
                            if (this.Yzxx_List[list].ystzbz == '1') {
                                deleteYzList.push(JSON.parse(JSON.stringify(this.Yzxx_List[list])))
                            }
                        }
                    }
                } else {
                    malert("请选择需要取消停嘱的信息！", 'top', 'defeadted');
                    return false;
                }
                //没有记录就直接返回
                var json = {
                    list: [
                        {
                            yzxx: deleteYzList,          //医嘱信息
                            lczd: []              //诊断信息（检查，检验的诊断）
                        }
                    ]
                };
                this.$http.post('/actionDispatcher.do?reqUrl=New1MjzglYzd&types=qxystz', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        hzList.Wf_selectYZ();  //查询医嘱
                        malert('医嘱取消停嘱成功', 'top', 'success');
                    } else {
                        malert("医嘱取消停嘱失败:" + data.body.c, 'top', 'defeadted');
                    }
                });

            }
            ,

            //合理用药接口调用
            Hlyy_checkYz: function (yzList) {
                for (var i = 0; i < yzList.length; i++) {
                    hzList.hlyynewYpmx.dcjl = yzList[i].yyjl
                    // var cs = zcy.listGetName(zcy.PcData, zcy.PfxxJson[i].yypc, 'pcbm', 'cs');//获取频次次数
                    hzList.hlyynewYpmx.rcs = yzList[i].pccs;
                    hzList.hlyynewYpmx.pcbm = yzList[i].pcbm;
                    hzList.hlyynewYpmx.ypbm = yzList[i].ypbm;

                    hzList.hlyyYpmx.push(JSON.parse(JSON.stringify(hzList.hlyynewYpmx)));
                }

                var checkParm = {
                    ly: "住院",
                    csid: 6,
                    brid: userNameBg.Brxx_List.brid,
                    cfh: userNameBg.Brxx_List.cfh,
                    zdmc: userNameBg.Brxx_List.ryzdmc,
                    czybm: userId,
                    ksbm: userNameBg.Brxx_List.ryks,
                    nl: userNameBg.Brxx_List.brnl,
                    ypmx: hzList.hlyyYpmx
                }
                this.$http.post(hzList.hlyyCheckUrl, JSON.stringify(checkParm)).then(function (data) {
                    if (data.body.code == 1 && data.body.printName != 'ok') {
                        malert("亲！" + data.body.printName, 'top', 'defeadted');
                    }
                });

            },

            //保存医嘱
            Wf_saveYZ: function () {
                $.ajaxSettings.async = false;
                if ((Object.keys(lgdj.lgdjxx).length === 0) || lgdj.lgdjxx.zfbz == '1') {
                    malert("病人无留观信息，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }

                if (!this.ifClick) return; //如果为false表示已经点击了不能再点
                this.ifClick = false;
                var sfts = true;
                //var tzyz = false;//针对医嘱类型是否停嘱
                try {
                    if (this.Yzxx_List.length <= 0) return;   //没有数据就直接返回
                    var yzList = [];   //用于提交的医嘱信息List
                    //循环判断每条医嘱是否正常
                    console.log(this.Yzxx_List);
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        var insertBz = this.Yzxx_List[i].insertBz;  //新增加标志
                        var updateBz = this.Yzxx_List[i].updateBz;  //修改标志
                        this.Yzxx_List[i].mbbm = jcjyfy.mbbm;//检查检验模板
                        if (this.Yzxx_List[i].rqxgbz == null) {
                            this.Yzxx_List[i].rqxgbz = '0';
                        }
                        if (this.Yzxx_List[i].yyzl == null || this.Yzxx_List[i].yyzl == '' || this.Yzxx_List[i].yyzl == undefined) {
                            this.Yzxx_List[i].yyzl = this.Yzxx_List[i].sl;
                        }
                        //关联类别为空的设置未0
                        if (this.Yzxx_List[i].gllb == null) {
                            this.Yzxx_List[i].gllb = '0';
                        }
                        if ((insertBz == null || insertBz == false) && (updateBz == null || updateBz == false)) continue;  //如果不是修改也不是新增加就跳出本次循环进入下一次
                        //医嘱编码为空的直接删除
                        if (this.Yzxx_List[i].tsyz == true) {
                            this.Yzxx_List[i].ryypmc = this.Yzxx_List[i].ryypmc;
                        }
                        if (!this.Yzxx_List[i].ryypbm ) {
							if(this.Yzxx_List[i].ryypmc){
								this.Yzxx_List[i].ryypbm = this.Yzxx_List[i].ryypmc
							}else{
								continue;
							}
                            //this.Yzxx_List.splice(i, 1);  //直接删除
                            
                        }
                        //抗生素只能是临时医嘱需要进行判断
                        // if (this.Yzxx_List[i].kssjb) {
                        //     if (this.Yzxx_List[i].kssjb != '1' && this.Yzxx_List[i].kssjb != '0') {
                        //         if (this.Yzxx_List[i].yzlx == '1') {
                        //             malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的属于抗生素【医嘱类型】不能长期！", 'top', 'defeadted');
                        //             this.ifClick = true;
                        //             return;
                        //         }
                        //     }
                        // }

                        //医嘱类型判断
                        if (!this.Yzxx_List[i].yzlx) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【医嘱类型】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }
                        //医嘱分类
                        if (!this.Yzxx_List[i].yzfl) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【医嘱分类】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }
                        //医嘱日期
                        if (!this.Yzxx_List[i].ksrq) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【医嘱日期】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }
                        //医嘱名称
                        if (!this.Yzxx_List[i].ryypmc) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【医嘱名称】不能为空！", 'top', 'defeadted');
                            return;
                        }
                        //药品还是诊疗
                        if (!this.Yzxx_List[i].lx) {
                            malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【医嘱分类(药品，诊断)】不能为空！", 'top', 'defeadted');
                            this.ifClick = true;
                            return;
                        }

                        //药品
                        if (this.Yzxx_List[i].lx == '药品') {
                            //单次剂量
                            if (!this.Yzxx_List[i].dcjl || this.Yzxx_List[i].dcjl <= 0) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【单次剂量】必需大于0！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }
                            //用药方法
                            if (!this.Yzxx_List[i].yyffbm) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【用药方法】不能为空！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }
                            //用药方法名称
                            if (!this.Yzxx_List[i].yyffmc) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【用药方法名称】不能为空！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }
                            //频次
                            if (!this.Yzxx_List[i].pcbm) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【频次】不能为空！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }

                            //用药天数
                            if (!this.Yzxx_List[i].yyts || this.Yzxx_List[i].yyts <= 0) {
                                malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].ryypmc + "】的【用药天数】必需大于0！", 'top', 'defeadted');
                                this.ifClick = true;
                                return;
                            }
                        }
                        //医嘱总量
                        // if (this.Yzxx_List[i].sl == null || this.Yzxx_List[i].sl == undefined || this.Yzxx_List[i].sl <= 0) {
                        //     malert("第【" + (i + 1) + "】行【" + this.Yzxx_List[i].xmmc + "】的【医嘱总量】必需大于0！", 'top', 'defeadted');
                        //     this.ifClick = true;
                        //     return;
                        // }
                        //处理各种标志
                        if (this.Yzxx_List[i].lx == '药品') {
                            this.Yzxx_List[i].zloryp = 'yp';
                        } else {
                            this.Yzxx_List[i].zloryp = 'zl';
                        }
						this.Yzxx_List[i].shbz='1';
                        yzList.push(this.Yzxx_List[i]);  //增加到保存医嘱json中
                    }  //循环完
                    if (yzList.length <= 0) {
                        malert("没有可保存的医嘱项目 ！", 'top', 'defeadted');
                        this.ifClick = true;
                        return;
                    }
                    for (var i = 0; i < yzList.length; i++) {
                        yzList[i].cflxbm = panel.popContent.cflxbm
                        if (yzList[i].tsyz) {
                            yzList[i].tsyz = '1';
                        } else {
                            yzList[i].tsyz = '0';
                        }

                    }
                    for (var j = 0; j < yzList.length; j++) {
                        if (yzList[j].rqxgbz == '1') {
                            sfts = false;
                        }
                        if (yzList[j].rqxgbz == '0') {
                            sfts = true;
                            break;
                        }
                    }

                    /* //判断是否需要停掉之前所有未停医嘱
                     for (var i = 0; i < yzList.length; i++) {
                         if (yzList[i].yzfl == '1' ||yzList[i].yzfl == '2' ||yzList[i].yzfl == '3' || yzList[i].yzfl == '4' || yzList[i].yzfl == '5') {
                             tzyz = true;
                             break;
                         }
                     }

                     //执行停止所有未停医嘱
                     if (tzyz) {
                         var wtyzList = [];
                         var ksbm = userNameBg.Brxx_List.ryks;   //科室
                         var zyh = userNameBg.Brxx_List.ghxh;  //住院号
                         var yzlx = '';
                         var ystzbz = '0';
                         var parm_ksbm = {
                             ksbm: ksbm,
                             yzlx: yzlx,
                             ystzbz: ystzbz,
                         };
                         var parm_zyh = [{
                             zyh: zyh
                         }];
                         $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=hzyzxx&parm=' + JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh)
                             , function (json) {
                                 if (json.a == '0') {
                                     wtyzList = json.d.list;
                                 }
                             });

                         if (wtyzList.length < 1) {
            //                    	break;
                         } else {
                             var tzjson = {
                                 list: [
                                     {
                                         yzxx: wtyzList,          //医嘱信息
                                         lczd: []              //诊断信息（检查，检验的诊断）
                                     }
                                 ]
                             };
                             this.$http.post('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=yzstop', JSON.stringify(tzjson)).then(function (data) {
                                 if (data.body.a == 0) {
                                     malert("医嘱停止申请成功",'top','success');
                                 } else {
                                     malert("医嘱停止申请失败：" + data.body.c, 'top', 'defeadted');
                                 }
                             });
                         }
                     }*/
                    var json = {
                        list: [
                            {
                                hzxx: lgdj.lgdjxx,   //病人基本信息
                                yzxx: yzList,          //医嘱信息
                            }
                        ]
                    };
                    //合理用药
                    this.Hlyy_checkYz(yzList);

                    this.$http.post('/actionDispatcher.do?reqUrl=New1MjzglYzd&types=saveyzd', JSON.stringify(json)).then(function (data) {
                        if (data.body.a == 0) {
                            hzList.ifClick = true;
                            if (yzList[yzList.length - 1] && yzList[yzList.length - 1].rqxgbz) {
                                brzcList.oldRqxgbz = yzList[yzList.length - 1].rqxgbz;
                            }
                            hzList.Wf_selectYZ();  //查询医嘱
                            malert("医嘱保存成功", 'top', 'success');
                        } else {
                            malert("医嘱保存失败" + data.body.c, 'top', 'defeadted');
                            hzList.ifClick = true;
                        }
                    });
                } catch (e) {
                    malert("医嘱保存异常" + e.message, 'top', 'defeadted');
                    hzList.ifClick = true;
                }
            }
            ,
            //新增加医嘱
            Wf_addYZ: function () {
                if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                    malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                    return false;
                }
                var xxssx = null;
                if (hzList.Yzxx_List.length > 0) {
                    xxssx = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].xssx + 1;
                    if (hzList.Yzxx_List[hzList.Yzxx_List.length - 1].ryypmc == '') {
                        malert("数据格式有问题，请正确输入医嘱名称", 'top', 'defeadted');
                        return false;
                    }
                    if (panel.is_csqx.N03003200119 == 1) {
                        hzList.Yzxx_List = [];
                    }
                } else {
                    xxssx = 1;
                }
                var yzlx = '1';
                if (panel.popContent.yzxx == '2') {
                    yzlx = '1';
                } else {
                    yzlx = panel.popContent.yzxx;
                }
                var yzfl = '0';
                var ksrq = getTodayDateTime();
                var fzh = null;
                var yyffmc = null;
                var yyffbm = null;
                var pcmc = null;
                var pcbm = null;
                var pccs = null;
                var gllb = null;
                var rqxgbz = '0';
                yyffcz = false;
                pccz = false;
                if (hzList.Yzxx_List.length > 0 && hzList.Yzxx_List[hzList.Yzxx_List.length - 1].insertBz) {
                    yzlx = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].yzlx;
                    yzfl = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].yzfl;
                    ksrq = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].ksrq;
                    fzh = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].fzh;
                    gllb = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].gllb;
                    rqxgbz = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].rqxgbz;
                }
                if (fzh > 0 && hzList.Yzxx_List.length > 0) {
                    yyffmc = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].yyffmc;
                    yyffbm = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].yyffbm;
                    pcmc = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].pcmc;
                    pcbm = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].pcbm;
                    pccs = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].pccs;
                    gllb = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].gllb;
                    rqxgbz = hzList.Yzxx_List[hzList.Yzxx_List.length - 1].rqxgbz;
                    yyffcz = true;
                    pccz = true;
                }
                var yzxx = {
                    insertBz: true,
                    updateBz: false,
                    readonly: false,
                    xssx: xxssx,
                    yzlx: yzlx,
                    yzfl: yzfl,
                    ksrq: ksrq,
                    fzh: fzh,
                    ysqmks: userNameBg.Brxx_List.ryks,
                    ysqmksmc: userNameBg.Brxx_List.ryksmc,
                    xmbm: '',
                    xmmc: '',
                    yyffbm: yyffbm,
                    yyffmc: yyffmc,
                    pcmc: pcmc,
                    pcbm: pcbm,
                    pccs: pccs,
                    gllb: gllb,
                    zt: '4',
                    rqxgbz: rqxgbz,
                };
                // if (!zdtj) {
                //赋值
                if (fzh > 0) {
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                            yzxx.sysddw = this.Yzxx_List[i].sysddw;
                            yzxx.sysd = this.Yzxx_List[i].sysd;
                        }
                    }
                }
                // if(hzList.isChecked.length>0){
                //     if(hzList.Yzxx_List[hzList.isChecked.length]['zt']=='4'){
                //         hzList.Yzxx_List.splice(hzList.isChecked.length + 1, 0, yzxx);
                //     }else {
                //         hzList.Yzxx_List.push(yzxx);
                //     }
                // }else {
                hzList.Yzxx_List.push(yzxx);
                // }

                if (yzfl == '3') {
                    $('#green' + hzList.Yzxx_List.length - 1).click();
                    Vue.set(hzList.Yzxx_List[hzList.Yzxx_List.length - 1], 'tsyz', true);
                    Vue.set(hzList.Yzxx_List[hzList.Yzxx_List.length - 1], 'ryypmc', '术后医嘱');
                }

                var cou = hzList.Yzxx_List.length - 1;

                setTimeout(function () {   //延时0.1秒执行
                    if (hzList.Yzxx_List[cou].fzh && hzList.Yzxx_List[cou].fzh != 0 && hzList.Yzxx_List[cou].fzh == hzList.Yzxx_List[hzList.Yzxx_List.length - 2].fzh) {
                        $("#xmmc_" + cou).focus();
                    } else {
                        hzList.$refs.addLx[hzList.$refs.addLx.length - 1].setLiShow(hzList.$refs.addLx[hzList.$refs.addLx.length - 1].$refs.inputFu)
                        hzList.$refs.addLx[hzList.$refs.addLx.length - 1].setLiFirst(hzList.$refs.addLx[hzList.$refs.addLx.length - 1].$refs.inputFu)
                    }

                }, 100);
            },
            showDate: function (index, event) {
                var elm = '.startdate' + index
                this._laydate = {
                    elem: elm
                    , show: true //直接显示
                    , type: 'datetime'
                    , theme: '#1ab394',
                    done: function (value, data) {
                        hzList.Yzxx_List[index]['ksrq'] = value
                        hzList.Yzxx_List[index]['rqxgbz'] = '1';
                    }
                }
                laydate.render(this._laydate)//初始化时间插件
            },


            // 显示医嘱模板
            showYzTem: function () {
                brzcList.title = "医嘱模板";
                brzcList.yzList = [];
                brzcList.sfcy = '0';
                brzcList.getTemData();
                brzcList.num = 0;
                this.$forceUpdate();
            }
            ,


            // 显示历史医嘱
            showLsYz: function (flag) {
                brzcList.title = '复制医嘱';
                brzcList.getLsTemData();
                brzcList.isfzcfMxChecked = [];
                brzcList.num = 0;
                // if(!flag){
                //     brzcList01.sfcy='0'
                // }
                this.$forceUpdate();
            }
            ,
            // 表格的下拉框赋值
            resultChange_item: function (val) {
                Vue.set(this.Yzxx_List[val[2][0]], [val[2][1]], val[0]);
                //调用相同分组号时处理方法
                // , parseInt(val[2][2])
                this.nextFocus(val[1]);
                this.pubFzhsame(val[2][0]);
                this.nextFocusLeft(event, 1)
            }
            ,
            //分组号

            resultChange_item_fzh: function (val) {
                yyffcz = false;
                yyffcz = false;
                pccz = false;
                Vue.set(this.Yzxx_List[val[2][0]], [val[2][1]], val[0]);
                if ([val[2][1]] == 'yzfl') {
                    if (val[0] == '3') {
                        $('#green' + val[2][0]).click();
                        Vue.set(this.Yzxx_List[val[2][0]], 'tsyz', true);
                        Vue.set(this.Yzxx_List[val[2][0]], 'ryypmc', '术后医嘱');
                        // setTimeout(function () {
                        //     hzList.Wf_addYZ();
                        // }, 500)
                    }
                } else {
                    Vue.set(this.Yzxx_List[val[2][0]], 'tsyz', false);
                }
                if ([val[2][1]] == "fzh") {
                    if (val[0] > 0) {
                        var change = false;
                        for (var i = 0; i < this.Yzxx_List.length; i++) {
                            if (val[0] > 0 && this.Yzxx_List[i].fzh > 0 && this.Yzxx_List[i].fzh == val[0]) {
                                this.Yzxx_List[val[2][0]].yyffbm = this.Yzxx_List[i].yyffbm;
                                this.Yzxx_List[val[2][0]].yyffmc = this.Yzxx_List[i].yyffmc;
                                this.Yzxx_List[val[2][0]].gllb = this.Yzxx_List[i].gllb;
                                this.Yzxx_List[val[2][0]].pcbm = this.Yzxx_List[i].pcbm;
                                this.Yzxx_List[val[2][0]].pcmc = this.Yzxx_List[i].pcmc;
                                this.Yzxx_List[val[2][0]].pccs = this.Yzxx_List[i].pccs;
                                this.Yzxx_List[val[2][0]].sysd = this.Yzxx_List[i].sysd;
                                this.Yzxx_List[val[2][0]].sysddw = this.Yzxx_List[i].sysddw;
                            }
                        }
//                    if (!change) {
//                        this.Yzxx_List[val[2][0]].yyff = null;
//                        this.Yzxx_List[val[2][0]].yyffmc = null;
//                        this.Yzxx_List[val[2][0]].pcbm = null;
//                        this.Yzxx_List[val[2][0]].pcmc = null;
//                        this.Yzxx_List[val[2][0]].sysd = null;
//                        this.Yzxx_List[val[2][0]].sysddw = null;
//                    }
                    } else {
                        this.Yzxx_List[val[2][0]].yyffbm = null;
                        this.Yzxx_List[val[2][0]].yyffmc = null;
                        this.Yzxx_List[val[2][0]].gllb = null;
                        this.Yzxx_List[val[2][0]].pcbm = null;
                        this.Yzxx_List[val[2][0]].pcmc = null;
                        this.Yzxx_List[val[2][0]].pccs = null;
                        this.Yzxx_List[val[2][0]].sysd = null;
                        this.Yzxx_List[val[2][0]].sysddw = null;

                    }
                }
                //调用相同分组号时处理方法
                // , parseInt(val[2][2])
                /*
                if([val[2][1]]=='yzlx'){
                    this.nextFocus(val[1],3);
                }else {
                    this.nextFocus(val[1]);
                }*/
                if ([val[2][1]] == 'yzlx') {
                    this.nextSelect(val[1], 3);
                } else {
                    this.nextSelect(val[1]);
                }
            }
            ,
            Wf_DblClick2: function (index) {
                if (this.Yzxx_List[index].sfcy == '1') {
                    brzcList00.initZcyList(userNameBg.Brxx_List.ghxh, this.Yzxx_List[index].yzxh);
                    brzcList00.zcyBrJson = userNameBg.Brxx_List;
                    brzcList00.zcyBrJson.ksmc = userNameBg.Brxx_List.ryksmc;
                    brzcList00.iszcy = false;
                    brzcList00.handover('iszcy');
                    brzcList00.sfxz = false;
                    //brzcList00.isdisabled = true;
                    brzcList00.numOne = 0;
                }
            }
            ,
            //下拉框检索
            //药品
            Wf_changeDown: function (index, event, type, value) {
                // 判断药品有没有值 有值才跳下一个输入框
                if (event.keyCode == 39 && event.target.value != '') {
                    this.prevFocus(event);
                } else if (event.keyCode == 37) {
                    this.prevFocus(event);
                }
                if (hzList.Yzxx_List[index].tsyz == true) {
                    //嘱吒医嘱回车
                    // if(value!=''){
                    if (event.keyCode == 13) {
                        hzList.Wf_addYZ();
                    }
                    return false;
                    // }
                }
                qjIndex = index;
                var yfbm = panel.popContent.yfbm;
                if (!yfbm) {
                    malert("请选择药房!", 'top', 'defeadted');
                    return false
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                if (this.searchCon[this.selSearch] == undefined && value == '') return;
                this.keyCodeFunction(event, 'popContent', 'searchCon');
                //赋值
                if (event.keyCode == 13) {
                    if (this.popContent == undefined) {
                        // Vue.set(hzList.Yzxx_List[index], 'tsyz', true)
                        $('#green' + index).click()
                        $(".selectGroup").hide();
                        return false
                    }
                    if (change) {
                        if (!this.getKssSq(this.popContent)) return; //对抗生素药物使用的判定
                        //下拉框回车后回调.
                        var sfcy = '0';
                        var xmbm = this.popContent.xmbm;
                        var bqdj = this.popContent.bqdj;
                        var hldj = this.popContent.hldj;
                        var xmmc = this.popContent.xmmc;
                        var cklj = this.popContent.cklj;
                        var jbjl = this.popContent.jbjl;
                        var jcfl = this.popContent.jclx;
                        var jldw = this.popContent.jldwbm;
                        var jldwmc = this.popContent.jldwmc;
                        var lx = this.popContent.lx;
                        var yfdw = this.popContent.yfdw;
                        var yfdwmc = this.popContent.yfdwmc;
                        var yyff = this.popContent.yyff;
                        var yyffmc = this.popContent.yyffmc;
                        var zdjl = this.popContent.zdjl;
                        var ypgg = this.popContent.xmgg;
                        var yfbm = panel.popContent.yfbm;
                        var yfmc = panel.popContent.yfmc;
                        var ybtclb = this.popContent.yb;
                        var nbtclb = this.popContent.nh;
                        var ybtclbmc = this.popContent.ybtclbmc;
                        var kssjb = this.popContent.kssjb;
                        var zlbm = this.popContent.zlbm;
                        var kcfbz = this.popContent.kcfbz;
                        var jxbm = this.popContent.jxbm;
                        var psyp = this.popContent.psyp;
                        var psxq = this.popContent.psxq;

                        Vue.set(this.Yzxx_List[index], 'sfcy', sfcy);
                        Vue.set(this.Yzxx_List[index], 'lx', lx);       //类型  药品？诊疗
                        Vue.set(this.Yzxx_List[index], 'ryypbm', xmbm);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'bqdj', bqdj);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'hldj', hldj);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'ryypmc', xmmc);   //项目名称
                        Vue.set(this.Yzxx_List[index], 'ypgg', ypgg);   //药品规格
                        Vue.set(this.Yzxx_List[index], 'dj', cklj);   //价格
                        Vue.set(this.Yzxx_List[index], 'cklj', cklj);   //价格
                        Vue.set(this.Yzxx_List[index], 'jbjl', jbjl);   //基本剂量
                        Vue.set(this.Yzxx_List[index], 'jcfl', jcfl);   //检查分类
                        Vue.set(this.Yzxx_List[index], 'jldw', jldw);   //剂量单位
                        Vue.set(this.Yzxx_List[index], 'jldwmc', jldwmc);  //剂量单位名称
                        Vue.set(this.Yzxx_List[index], 'yfdw', yfdw);      //药房单位
                        Vue.set(this.Yzxx_List[index], 'yfdwmc', yfdwmc);  //药房单位名称
                        Vue.set(this.Yzxx_List[index], 'zdjl', zdjl);      //最大剂量
                        Vue.set(this.Yzxx_List[index], 'zyh', userNameBg.Brxx_List.zyh);  //住院号
                        Vue.set(this.Yzxx_List[index], 'ksbm', userNameBg.Brxx_List.ryks); //科室
                        Vue.set(this.Yzxx_List[index], 'yyts', 1);            //用药天数
                        Vue.set(this.Yzxx_List[index], 'sl', 1);              //数量
                        Vue.set(this.Yzxx_List[index], 'yfbm', panel.popContent.yfbm); //药房编码
                        Vue.set(this.Yzxx_List[index], 'yfmc', panel.popContent.yfmc); //药房名称
                        Vue.set(this.Yzxx_List[index], 'ybtclb', ybtclb);      //医保统筹类别
                        Vue.set(this.Yzxx_List[index], 'ybtclbmc', ybtclbmc);  //医保统筹类别名称
                        Vue.set(this.Yzxx_List[index], 'nbtclb', nbtclb);      //农保统筹类别
                        Vue.set(this.Yzxx_List[index], 'ypzl', zlbm);          //药品种类
                        Vue.set(this.Yzxx_List[index], 'kcfbz', kcfbz);        //可拆分标志
                        Vue.set(this.Yzxx_List[index], 'kssjb', kssjb);        //抗生素级别
                        Vue.set(this.Yzxx_List[index], 'jxbm', jxbm);          //剂型编码
                        Vue.set(this.Yzxx_List[index], 'psyp', psyp);          //皮试药品
                        Vue.set(this.Yzxx_List[index], 'psxq', psxq);          //皮试效期
                        change = false;
                        this.selSearch = -1;
                        console.log(this.Yzxx_List[index], '1')
                    }
                    if (this.Yzxx_List[index].lx == '诊疗') {
                        console.log(this.Yzxx_List[index], '2')
                        Vue.set(this.Yzxx_List[index], 'zlbz', true);        //诊疗标志
                        Vue.set(this.Yzxx_List[index], 'fzh', "");        //诊疗标志
                        //检查或检验
                        pop.Wf_open(this.Yzxx_List[index].jcfl, index);  //弹出检查检验窗口
                        if (this.Yzxx_List[index].jcfl == '1' || this.Yzxx_List[index].jcfl == '2') {
                            pop.isShow = true;   //显示诊断窗口
                            pop.isRow = index;   //设置当前行号
                            pop.jcjybz = this.Yzxx_List[index].jcfl;  //设置检查分类
                            setTimeout(function () {
                                $("#pop_lczd").focus();
                            }, 300);
                        } else {
                            $("#pcmc_" + index).focus();  //诊疗焦点就跳到频次编码
                        }
                        //诊疗焦点就跳到频次编码
                        console.log(this.Yzxx_List[index], '3')
                        this.$nextTick(function () {
                            this.nextSelect(event, 4)
                            // $("#yzhm").focus();
                        })
                    } else {
                        Vue.set(this.Yzxx_List[index], 'zlbz', false);        //诊疗标志
                        $("#dcjl_" + index).focus();  //药品焦点就跳到单次剂量
                    }
                    setTimeout(function () {
                        if (hzList.sfqk == 1) {
                            hzList.Yzxx_List.splice(index, 1);
                            hzList.sfqk = 0;
                        }
                    }, 300);
                }
            },
            //用药方法
            Wf_changeDown2: function (index, event, type) {
                this.prevFocus(event);
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                qjIndex = index;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                // this.keyCodeFunction(event, 'popContent', 'searchCon2');
                this.inputUpDown(event, this.searchCon2, "selSearch2");
                this.popContent = this.searchCon2[this.selSearch2]
                //赋值
                var fzh = this.Yzxx_List[index].fzh;
                if (event.keyCode == 13) {
                    if (this.popContent == undefined) return
                    //下拉框回车后回调.
                    if (!yyffcz && change) {
                        var yyffbm = this.popContent.yyffbm;
                        var yyffmc = this.popContent.yyffmc;
                        var gllb = this.popContent.zxdlx;
                        Vue.set(this.Yzxx_List[index], 'yyffbm', yyffbm);   //项目编码
                        Vue.set(this.Yzxx_List[index], 'yyffmc', yyffmc);   //项目名称
                        Vue.set(this.Yzxx_List[index], 'gllb', gllb);   //关联类别
                        change = false;
                    }
                    if (fzh > 0) {
                        for (var i = 0; i < this.Yzxx_List.length; i++) {
                            if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                this.Yzxx_List[i].yyffbm = this.Yzxx_List[index].yyffbm;
                                this.Yzxx_List[i].yyffmc = this.Yzxx_List[index].yyffmc;
                                this.Yzxx_List[i].gllb = this.Yzxx_List[index].gllb;
                            }
                        }
                    }
                    this.selSearch2 = 0;
                    this.nextSelect(event);
                    $(".selectGroup").hide();
                }
            }
            ,
            //频次
            Wf_changeDown3: function (index, event, type, value) {
                this.prevFocus(event);
                if (this.Yzxx_List[index].tsyz == true) return;
                qjIndex = index;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                // this.keyCodeFunction(event, 'popContent', 'searchCon3');
                this.inputUpDown(event, this.searchCon3, "selSearch3");
                this.popContent = this.searchCon3[this.selSearch3]
                //赋值
                var fzh = this.Yzxx_List[index].fzh;
                if (event.keyCode == 13) {
                    if (hzList.Yzxx_List[index].lx == '药品' && value == undefined) {
                        malert("药品模式下，请完整输入频次", 'top', 'defeadted');
                        return false
                    } else if (hzList.Yzxx_List[index].lx == '诊疗') {
                        this.nextSelect(event)
                    }
                    if (this.popContent == undefined) return
                    if (!pccz && change) {
                        var pcbm = this.popContent.pcbm;
                        var pcmc = this.popContent.pcmc;
                        var pccs = this.popContent.cs;
                        Vue.set(this.Yzxx_List[index], 'pcmc', pcmc);    //频次名称
                        Vue.set(this.Yzxx_List[index], 'pccs', pccs);  //频次次数
                        Vue.set(this.Yzxx_List[index], 'pcbm', pcbm);  //频次次数
                        change = false;
                    }
                    //分组设置
                    if (fzh > 0) {
                        for (var i = 0; i < this.Yzxx_List.length; i++) {
                            if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                this.Yzxx_List[i].pcbm = this.Yzxx_List[index].pcbm;
                                this.Yzxx_List[i].pcmc = this.Yzxx_List[index].pcmc;
                                Vue.set(this.Yzxx_List[i], 'pccs', this.Yzxx_List[index].pccs);
                                //Change事件，用于计算药品总量
                                this.Wf_editChange(i, 'pccs');
                            }
                        }
                    } else {
                        this.Wf_editChange(index, 'pccs');
                    }
                    this.selSearch = 0;
                    this.$nextTick(function () {
                        this.nextSelect(event, 1)
                        // $("#yzhm").focus();
                    })
                    $(".selectGroup").hide();
                }
            }
            ,
            //值改变事件
            //药品
            Wf_change: function (add, index, type, val) {
                if (!add) this.page.page = 1;       // 设置当前页号为第一页
                this.Yzxx_List[index][type] = val;
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                change = true;
                var yfbm = panel.popContent.yfbm;
                if (yfbm == null || yfbm == undefined || yfbm == "") {
                    malert("请选择药房", 'top', 'defeadted');
                    return false
                }
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var pram = this.Yzxx_List[index][type];
                //截取首尾空格
                this.page.parm = trimStr(pram);
                this.page.yfbm = yfbm;
                this.page.jsfw = panel.qhCode;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yzxm&json=' + JSON.stringify(this.page), function (data) {
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            hzList.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        hzList.searchCon = data.d.list;
                    }

                    // 屏蔽的原因是本来重写后search-table自身就处理了位置边界的。暂时没搞懂这里处理这个top是怎么回事。暂时屏蔽
                    // if (543 + 36 + ((index + 1) * 40) > document.body.clientHeight && (index + 1) * 40 > 300) {
                    //     _searchEvent.css("top", "-330px");
                    // }
                    hzList.page.total = data.d.total;
                    hzList.selSearch = 0;
                    if (data.d.list.length != 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });

            },
            resultChangeKss: function (val) { //抗生素使用目的
                Vue.set(this.Yzxx_List[qjIndex], 'kjywsymd', val[0]);
                Vue.set(this.Yzxx_List[qjIndex], 'kjywsymd', val[0]);
                this.kss = false
                $('#dcjl_' + qjIndex).focus()
            },
            //用药方法
            Wf_change2: function (index, type, val) {
                this.Yzxx_List[index][type] = val;
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                yyffcz = false;
                pccz = false;
                change = true;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var pram = this.Yzxx_List[index][type];
                if (pram == null || pram == "") {
                    this.pageStr.parm = "";
                } else {
                    //截取首尾空格
                    pram = trimStr(pram);
                    //截取首尾空格
                    pram = trimStr(pram);
                    this.pageStr.parm = pram;
                }
                var str_param = {parm: this.pageStr.parm, page: this.pageStr.page, rows: this.pageStr.rows};
                $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmyyff&types=query&dg=" + JSON.stringify(str_param), function (data) {
                    if (data.a == 0) {
                        hzList.searchCon2 = data.d.list;
                        hzList.selSearch = 0;
                        if (data.d.list.length > 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show();
                        }
                    } else {
                        malert("查询失败  " + json.c, 'top', 'defeadted');
                    }
                });
            }
            ,
            //频次
            Wf_change3: function (add, index, type, val) {
                this.Yzxx_List[index][type] = val;
                if (this.Yzxx_List[index].tsyz == true) {
                    return;
                }
                yyffcz = false;
                pccz = false;
                change = true;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                var pram = this.Yzxx_List[index][type];
                if (pram == null || pram == "") {
                    this.queryStr.parm = "";
                } else {
                    //截取首尾空格
                    pram = trimStr(pram);
                    //截取首尾空格
                    pram = trimStr(pram);
                    this.queryStr.parm = pram;
                }
                var pc_dg = {
                    parm: this.queryStr.parm,
                    page: this.queryStr.page,
                    rows: this.queryStr.rows,
                    sort: "sor",
                    order: "asc"
                };
                $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yzpc&dg=" + JSON.stringify(pc_dg), function (json) {
                    hzList.searchCon3 = json.d.list;
                    hzList.total = json.d.total;
                    hzList.selSearch = 0;
                    if (json.d.list.length != 0) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    } else {
                        $(".selectGroup").hide();
                    }
                });
            }
            ,
            //双击事件
            //用药方法
            selectOne1: function (item) {
                this.popContent = item;
                if (!yyffcz && change) {
                    var yyffbm = this.popContent.yyffbm;
                    var yyffmc = this.popContent.yyffmc;
                    var gllb = this.popContent.zxdlx;
                    console.log(gllb);
                    Vue.set(this.Yzxx_List[qjIndex], 'yyffbm', yyffbm);   //项目编码
                    Vue.set(this.Yzxx_List[qjIndex], 'yyffmc', yyffmc);   //项目名称
                    Vue.set(this.Yzxx_List[qjIndex], 'gllb', gllb);   //关联类别名称
                    change = false;
                    this.selSearch2 = 0;
                }
                var fzh = this.Yzxx_List[qjIndex].fzh;
                //针对相同分组号处理方法
                if (fzh > 0) {
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                            this.Yzxx_List[i].yyffbm = this.Yzxx_List[qjIndex].yyffbm;
                            this.Yzxx_List[i].yyffmc = this.Yzxx_List[qjIndex].yyffmc;
                            this.Yzxx_List[i].gllb = this.Yzxx_List[qjIndex].gllb;
                        }
                    }
                }

                this.nextFocus(event);
                $(".selectGroup").hide();

            }
            ,
            //药品
            selectOne2: function (item) {
                if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                    this.page.page++;               // 设置当前页号
                    this.Wf_change(true, qjIndex, 'xmmc', this.Yzxx_List[qjIndex]['xmmc']);           // 传参表示请求下一页,不传就表示请求第一页
                } else {
                    if (!this.getKssSq(item)) return; //对抗生素药物使用的判定
                    this.popContent = item;
                    var sfcy = '0';
                    var xmbm = this.popContent.xmbm;
                    var hldj = this.popContent.hldj;
                    var xmmc = this.popContent.xmmc;
                    var cklj = this.popContent.cklj;
                    var jbjl = this.popContent.jbjl;
                    var jcfl = this.popContent.jclx;
                    var jldw = this.popContent.jldwbm;
                    var jldwmc = this.popContent.jldwmc;
                    var lx = this.popContent.lx;
                    var yfdw = this.popContent.yfdw;
                    var yfdwmc = this.popContent.yfdwmc;
                    var yyff = this.popContent.yyff;
                    var yyffmc = this.popContent.yyffmc;
                    var zdjl = this.popContent.zdjl;
                    var ypgg = this.popContent.xmgg;
                    var yfbm = panel.popContent.yfbm;
                    var yfmc = panel.popContent.yfmc;
                    var ybtclb = this.popContent.yb;
                    var nbtclb = this.popContent.nh;
                    var ybtclbmc = this.popContent.ybtclbmc;
                    var kssjb = this.popContent.kssjb;
                    var zlbm = this.popContent.zlbm;
                    var kcfbz = this.popContent.kcfbz;
                    var jxbm = this.popContent.jxbm;
                    if (change) {
                        Vue.set(this.Yzxx_List[qjIndex], 'sfcy', sfcy);       //类型  药品？诊疗
                        Vue.set(this.Yzxx_List[qjIndex], 'lx', lx);       //类型  药品？诊疗
                        Vue.set(this.Yzxx_List[qjIndex], 'ryypbm', xmbm);   //项目编码
                        Vue.set(this.Yzxx_List[qjIndex], 'hldj', hldj);   //项目编码
                        Vue.set(this.Yzxx_List[qjIndex], 'ryypmc', xmmc);   //项目名称
                        Vue.set(this.Yzxx_List[qjIndex], 'ypgg', ypgg);   //药品规格
                        Vue.set(this.Yzxx_List[qjIndex], 'cklj', cklj);   //价格
                        Vue.set(this.Yzxx_List[qjIndex], 'jbjl', jbjl);   //基本剂量
                        Vue.set(this.Yzxx_List[qjIndex], 'jcfl', jcfl);   //检查分类
                        Vue.set(this.Yzxx_List[qjIndex], 'jldw', jldw);   //剂量单位
                        Vue.set(this.Yzxx_List[qjIndex], 'jldwmc', jldwmc);  //剂量单位名称
                        Vue.set(this.Yzxx_List[qjIndex], 'yfdw', yfdw);      //药房单位
                        Vue.set(this.Yzxx_List[qjIndex], 'yfdwmc', yfdwmc);  //药房单位名称
                        Vue.set(this.Yzxx_List[qjIndex], 'zdjl', zdjl);      //最大剂量
                        Vue.set(this.Yzxx_List[qjIndex], 'zyh', userNameBg.Brxx_List.zyh);  //住院号
                        Vue.set(this.Yzxx_List[qjIndex], 'ksbm', userNameBg.Brxx_List.ryks); //科室
                        Vue.set(this.Yzxx_List[qjIndex], 'yyts', 1);            //用药天数
                        Vue.set(this.Yzxx_List[qjIndex], 'sl', 1);              //数量
                        Vue.set(this.Yzxx_List[qjIndex], 'yfbm', panel.popContent.yfbm); //药房编码
                        Vue.set(this.Yzxx_List[qjIndex], 'yfmc', panel.popContent.yfmc); //药房名称
                        Vue.set(this.Yzxx_List[qjIndex], 'ybtclb', ybtclb);      //医保统筹类别
                        Vue.set(this.Yzxx_List[qjIndex], 'ybtclbmc', ybtclbmc);  //医保统筹类别名称
                        Vue.set(this.Yzxx_List[qjIndex], 'nbtclb', nbtclb);      //农保统筹类别
                        Vue.set(this.Yzxx_List[qjIndex], 'ypzl', zlbm);          //药品种类
                        Vue.set(this.Yzxx_List[qjIndex], 'kcfbz', kcfbz);        //可拆分标志
                        Vue.set(this.Yzxx_List[qjIndex], 'kssjb', kssjb);        //抗生素级别
                        Vue.set(this.Yzxx_List[qjIndex], 'jxbm', jxbm);          //剂型编码
                        change = false;
                        this.selSearch = -1;
                    }
                    if (lx == '诊疗') {
                        Vue.set(this.Yzxx_List[qjIndex], 'zlbz', true);        //诊疗标志
                        Vue.set(this.Yzxx_List[qjIndex], 'fzh', "");        //诊疗标志
                        //检查或检验
                        pop.Wf_open(jcfl, qjIndex);  //弹出检查检验窗口

                        if (jcfl == '1' || jcfl == '2') {
                            pop.isShow = true;   //显示诊断窗口
                            pop.isRow = qjIndex;   //设置当前行号
                            pop.jcjybz = this.Yzxx_List[qjIndex].jcfl;  //设置检查分类
                            setTimeout(function () {
                                $("#pop_lczd").focus();
                            }, 300);

                        } else {
                            $("#pcmc_" + qjIndex).focus();  //诊疗焦点就跳到频次编码
                        }
                    } else {
                        Vue.set(this.Yzxx_List[qjIndex], 'zlbz', false);        //诊疗标志
                        $("#dcjl_" + qjIndex).focus();  //药品焦点就跳到单次剂量
                    }
                    $(".selectGroup").hide();
                    setTimeout(function () {
                        if (hzList.sfqk == 1) {
                            hzList.Yzxx_List.splice(qjIndex, 1);
                            hzList.sfqk = 0;
                        }
                    }, 300);

                }
            }
            ,

            //频次
            selectOne3: function (item) {
                this.popContent = item;
                if (!pccz && change) {
                    var pcbm = this.popContent.pcbm;
                    var pcmc = this.popContent.pcmc;
                    var pccs = this.popContent.cs;
                    Vue.set(this.Yzxx_List[qjIndex], 'pcmc', pcmc);    //频次名称
                    Vue.set(this.Yzxx_List[qjIndex], 'pccs', pccs);  //频次次数
                    Vue.set(this.Yzxx_List[qjIndex], 'pcbm', pcbm);  //频次次数
                    change = false;
                }
                var fzh = this.Yzxx_List[qjIndex].fzh;
                //分组设置
                if (fzh > 0) {
                    for (var i = 0; i < this.Yzxx_List.length; i++) {
                        if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                            this.Yzxx_List[i].pcbm = this.Yzxx_List[qjIndex].pcbm;
                            this.Yzxx_List[i].pcmc = this.Yzxx_List[qjIndex].pcmc;
                            Vue.set(this.Yzxx_List[i], 'pccs', this.Yzxx_List[qjIndex].pccs);
                            //Change事件，用于计算药品总量
                            this.Wf_editChange(i, 'pccs');
                        }
                    }
                } else {
                    this.Wf_editChange(qjIndex, 'pccs');
                }
                this.selSearch3 = 0;
                this.nextFocus(event);
                $(".selectGroup").hide();
            }
            ,

            //查询医嘱
            Wf_selectYZ: function () {
                common.openloading('.loadPage ');
                this.isChecked = [];
                var ksbm = userNameBg.Brxx_List.ghks;   //科室
                var zyh = userNameBg.Brxx_List.ghxh;  //住院号
                var yzlx = panel.popContent.yzxx == '2' ? '' : panel.popContent.yzxx;
                var ystzbz = panel.popContent.yzgl;
                var hstzbz = null;
                var zfbz = null;
                var shbz = null;

                /***************************以上为标记代码******************************/

                // if (panel.popContent.yess == '1') {
                //     cfss = '0'
                // } else if (panel.popContent.yess == '2') {
                //     cfss = '1'
                // } else {
                //     cfss = "";
                // }
                if (ystzbz == '%') {//全部
                    zfbz = "";
                    ystzbz = '';
                }
                if (ystzbz == '2') { //作废
                    ystzbz = '';
                    zfbz = '1';
                }
                if (ystzbz == '3') { //停嘱未审
                    ystzbz = '1';
                    hstzbz = '0';
                }
                if (ystzbz == '4') { //未审核
                    zfbz = '0';
                    shbz = '0';
                    ystzbz = '0';
                }
                if (yzlx == '%') yzlx = '';
                if (ksbm == null || ksbm == undefined || ksbm == "") {
                    malert("病人科室不能为空！，请选择科室.", 'top', 'defeadted');
                    return;
                }
                if (zyh == null || zyh == undefined || zyh == "") {
                    malert("请选择病人！", 'top', 'defeadted');
                    return;
                }
                this.Yzxx_List = [];   //先清空再说
                var parm_ksbm = {
                    ksbm: ksbm,
                    yzlx: yzlx,
                    ystzbz: ystzbz,
                    hstzbz: hstzbz,
                    zfbz: zfbz,
                    shbz: shbz,
                    jsfw: panel.qhCode
                };
                var parm_zyh = [{
                    ghxh: zyh
                }];

                this.$http.get("/actionDispatcher.do",{params:{reqUrl:'New1MjzglYzd',types:'queryYzd',parm:JSON.stringify(parm_ksbm),ghxh:JSON.stringify(parm_zyh)}}).then(function (json) {
                    if (json.body.a == 0) {
                        //把时间戳改改时间
                        if (json.body.d.list.length > 0) {
                            for (var i = 0; i < json.body.d.list.length; i++) {
                                json.body.d.list[i].readonly = true;   //只读
                                json.body.d.list[i].ksrq = formatTime(json.body.d.list[i].ksrq, "datetime");
                                if (json.body.d.list[i].xssx == "0") {
                                    json.body.d.list[i].xssx = i + 1;
                                }
                                //特殊医嘱
                                var tsyz = json.body.d.list[i].tsyz;
                                if (tsyz == "1") {
                                    json.body.d.list[i].tsyz = true;
                                } else {
                                    json.body.d.list[i].tsyz = false;
                                }
                                json.body.d.list[i].zt = null;
                                //判断状态
                                /*'0': '待审核','1': '已停嘱','2': '已作废','3': '待停嘱','4'：'草稿'"*/
                                if (json.body.d.list[i].zfbz == '1') {
                                    json.body.d.list[i].zt = '2';
                                } else if (json.body.d.list[i].shbz == '0' && json.body.d.list[i].ystzbz == '0') {
                                    json.body.d.list[i].zt = '0';
                                } else if (json.body.d.list[i].ystzbz == '1') {
                                    json.body.d.list[i].zt = '1';
                                } else if (json.body.d.list[i].shbz == '1' && json.body.d.list[i].ystzbz == '0') {
                                    json.body.d.list[i].zt = '3';
                                }
                                if (json.body.d.list[i].fzh != 0) {
                                    if (i == 0) { // 第一个
                                        if (json.body.d.list[i + 1] != undefined) {
                                            if (json.body.d.list[i].fzh == json.body.d.list[i + 1].fzh && json.body.d.list[i].yzxh == json.body.d.list[i + 1].yzxh) {
                                                json.body.d.list[i]['tzbj'] = 'tz-start';
                                            }
                                        }
                                    } else if (i == json.body.d.list.length - 1) { // 最后一个
                                        if (json.body.d.list[i].fzh == json.body.d.list[i - 1].fzh && json.body.d.list[i].yzxh == json.body.d.list[i - 1].yzxh) {
                                            json.body.d.list[i]['tzbj'] = 'tz-stop';
                                        }
                                    } else {
                                        if ((json.body.d.list[i].fzh != json.body.d.list[i - 1].fzh || json.body.d.list[i].yzxh != json.body.d.list[i - 1].yzxh) && (json.body.d.list[i].fzh == json.body.d.list[i + 1].fzh && json.body.d.list[i].yzxh == json.body.d.list[i + 1].yzxh)) {
                                            json.body.d.list[i]['tzbj'] = 'tz-start';
                                        } else if (json.body.d.list[i].fzh == json.body.d.list[i - 1].fzh && json.body.d.list[i].yzxh == json.body.d.list[i - 1].yzxh && json.body.d.list[i].fzh == json.body.d.list[i + 1].fzh && json.body.d.list[i].yzxh == json.body.d.list[i + 1].yzxh) {
                                            json.body.d.list[i]['tzbj'] = 'tz-center';
                                        } else if ((json.body.d.list[i].fzh == json.body.d.list[i - 1].fzh && json.body.d.list[i].yzxh == json.body.d.list[i - 1].yzxh) && (json.body.d.list[i].fzh != json.body.d.list[i + 1].fzh || json.body.d.list[i].yzxh != json.body.d.list[i + 1].yzxh)) {
                                            json.body.d.list[i]['tzbj'] = 'tz-stop';
                                        }
                                    }
                                }
                            }

                            hzList.Yzxx_List = json.body.d.list;
                            if (hzList.Yzxx_List[hzList.Yzxx_List.length - 1]) {
                                hzList.Yzxx_List[hzList.Yzxx_List.length - 1].rqxgbz = brzcList.oldRqxgbz;
                            }
                            common.closeLoading()
                        } else {
                            hzList.Wf_addYZ();
                            common.closeLoading()
                        }
                    } else {
                        malert('病人列表获取失败', 'top', 'defeadted');
                    }
                }, function (json) {
                })
            },

            //项目改变事件
            Wf_XmChange: function (event, index, type) {
                var obj = event.currentTarget;
                var fzh = this.Yzxx_List[index].fzh;  //分组号
                switch (type) {
                    case "fzh":  //分组号
                        //同一组设置相同属性的值
                        if (fzh > 0 && index > 0 && this.Yzxx_List[index - 1].insertBz == true) {
                            if (fzh == this.Yzxx_List[index - 1].fzh) {
                                Vue.set(this.Yzxx_List[index], 'yyffbm', this.Yzxx_List[index - 1].yyffbm);    //用药方法名称
                                this.Yzxx_List[index].yyffmc = this.Yzxx_List[index - 1].yyffmc;
                                this.Yzxx_List[index].pcbm = this.Yzxx_List[index - 1].pcbm;
                                this.Yzxx_List[index].pcmc = this.Yzxx_List[index - 1].pcmc;
                                this.Yzxx_List[index].pccs = this.Yzxx_List[index - 1].pccs;
                                this.Yzxx_List[index].sysd = this.Yzxx_List[index - 1].sysd;
                                this.Yzxx_List[index].gllb = this.Yzxx_List[index - 1].gllb;
                                Vue.set(this.Yzxx_List[index], 'sysddw', this.Yzxx_List[index - 1].sysddw);    //输液速度编码
                            }
                        }
                        break;
                    case "yyffbm":  //用药方法
                        var selected = $(obj).find("option:selected");
                        var mc = selected.text();
                        var zxdlx = selected.attr('zxdlx');
                        Vue.set(this.Yzxx_List[index], 'yyffmc', mc);   //用法名称
                        Vue.set(this.Yzxx_List[index], 'gllb', zxdlx); //执行单类型
                        //分组设置
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (i != index && fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].yyffbm = this.Yzxx_List[index].yyffbm;
                                    this.Yzxx_List[i].gllb = this.Yzxx_List[index].gllb;   //执行单类型
                                    Vue.set(this.Yzxx_List[i], 'yyffmc', this.Yzxx_List[index].mc);    //用药方法名称
                                }
                            }
                        }
                        break;
                    case "pcbm":    //频次
                        var selected = $(obj).find("option:selected");
                        var mc = selected.text();
                        var pccs = selected.attr('cs');
                        Vue.set(this.Yzxx_List[index], 'pcmc', mc);    //频次名称
                        Vue.set(this.Yzxx_List[index], 'pccs', pccs);  //频次次数
                        //分组设置
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (i != index && fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].pcbm = this.Yzxx_List[index].pcbm;
                                    this.Yzxx_List[i].pcmc = this.Yzxx_List[index].pcmc;
                                    Vue.set(this.Yzxx_List[i], 'pccs', this.Yzxx_List[index].pccs);

                                    //Change事件，用于计算药品总量
                                    this.Wf_editChange(i, 'pccs');
                                }
                            }
                        }
                        break;
                    // case "xz":    //选择
                    //     tjIndex = index;
                    //     if ($(obj).is(':checked')) {
                    //         Vue.set(this.Yzxx_List[index], 'xz', true);
                    //         zdtj = true;
                    //     } else {
                    //         Vue.set(this.Yzxx_List[index], 'xz', false);
                    //         zdtj = false;
                    //     }
                    //     break;
                    case "tsyz":
                        if (this.Yzxx_List[index].insertBz != true) {
                            Vue.set(this.Yzxx_List[index], 'tsyz', false);
                            malert("非新增加医嘱不允许修改为特殊医嘱", 'top', 'defeadted');
                            return;
                        }
                        sfzt = true;
                        if ($(obj).is(':checked')) {
                            this.Yzxx_List[index].ryypbm = this.Yzxx_List[index].ryypmc;
                            this.Yzxx_List[index].ypgg = "";
                            Vue.set(this.Yzxx_List[index], 'ypzl', "");          //药品种类
                            Vue.set(this.Yzxx_List[index], 'kcfbz', "");         //可拆分标志
                            Vue.set(this.Yzxx_List[index], 'ybtclb', "");        //医保统筹类别
                            Vue.set(this.Yzxx_List[index], 'ybtclbmc', "");      //医保统筹类别名称
                            Vue.set(this.Yzxx_List[index], 'nbtclb', "");        //农保统筹类别
                            //Vue.set(this.Yzxx_List[index], 'yfbm', "");          //药房编码
                            Vue.set(this.Yzxx_List[index], 'yfmc', "");           //药房名称
                            Vue.set(this.Yzxx_List[index], 'lx', "诊疗");         //类型  药品？诊疗
                            Vue.set(this.Yzxx_List[index], 'cklj', "");          //价格
                            Vue.set(this.Yzxx_List[index], 'jbjl', 1);           //基本剂量
                            Vue.set(this.Yzxx_List[index], 'jcfl', "0");         //检查分类
                            Vue.set(this.Yzxx_List[index], 'jldw', "");          //剂量单位
                            Vue.set(this.Yzxx_List[index], 'jldwmc', "");        //剂量单位名称
                            Vue.set(this.Yzxx_List[index], 'kssjb', "0");        //抗生素级别
                            Vue.set(this.Yzxx_List[index], 'yyzl', "1");
                            Vue.set(this.Yzxx_List[index], 'tsyz', true);
                        } else {
                            this.Yzxx_List[index].ryypbm = "";
                            this.Yzxx_List[index].ryypmc = "";
                            Vue.set(this.Yzxx_List[index], 'tsyz', false);
                        }
                        break;
                }
            },
            //值改变事件，主要用来计算医嘱总量
            Wf_editChange: function (index, type) {
                if (this.Yzxx_List[index].lx == '诊疗') {
                    var cs = this.Yzxx_List[index].pccs;   //频次次数
                    Vue.set(this.Yzxx_List[index], 'yyzl', cs || 1);     //给总量赋值
                } else {
                    var sl = 1; //数量
                    var cs = this.Yzxx_List[index].pccs;   //频次次数
                    var dcjl = this.Yzxx_List[index].dcjl;   //单次剂量
                    var jbjl = this.Yzxx_List[index].jbjl;   //基本剂量
                    var kcfbz = this.Yzxx_List[index].kcfbz;   //可拆分标志 0-不可拆分 ； 1-可拆分
                    if (cs == null || cs == undefined || cs == "") cs = 1;
                    if (jbjl == null || jbjl == undefined || jbjl == "") jbjl = 1;
                    if (kcfbz == null || kcfbz == undefined || kcfbz == "") kcfbz = 0;
                    switch (type) {
                        case "dcjl":
                            if (isNaN(dcjl)) {
                                malert("第【" + (index + 1) + "】行的单次剂量不是有效数字！", 'top', 'defeadted');
                                return;
                            }
                            break;
                    }
                    //自动给用药问题赋值
                    if (kcfbz == "0") {
                        sl = Math.ceil(dcjl / jbjl) * cs;  //自动算药品总量
                    } else {
                        sl = Math.ceil(dcjl / jbjl * cs);  //自动算药品总量
                    }
                    Vue.set(this.Yzxx_List[index], 'yyzl', sl);     //给总量赋值
                }
            },
            //医嘱类型
            Wf_yzlxClick: function (type) {
                switch (type) {
                    case 'qb':
                        this.is_yzlx = '%';
                        break;
                    case 'cq':
                        this.is_yzlx = '1';
                        break;
                    case 'ls':
                        this.is_yzlx = '0';
                        break;
                }
                this.Wf_selectYZ();  //查询医嘱
            },
            //医嘱过滤
            Wf_yzglClick: function (type) {
                switch (type) {
                    case 'qb':
                        this.is_yzgl = '%';
                        break;
                    case 'wt':
                        this.is_yzgl = '0';
                        break;
                    case 'yt':
                        this.is_yzgl = '1';
                        break;
                    case 'zf':
                        this.is_yzgl = '2';
                        break;
                    case 'wstz':
                        this.is_yzgl = '3';
                        break;
                }
                this.Wf_selectYZ();  //查询医嘱
            },

            nextFocusJl: function (event, index, num, tpye, value, text) {
                console.log("进B");
                if (event.keyCode == 13) {
                    if (hzList.Yzxx_List[index].lx == '药品' && value == undefined) {
                        malert("药品模式下，请完整输入" + text + "", 'top', 'defeadted');
                        return false
                    }
                    if (hzList.Yzxx_List.length > 1 && this.Yzxx_List[index].fzh == this.Yzxx_List[index - 1].fzh && hzList.Yzxx_List[index].yyffbm != null) {
                        hzList.Wf_addYZ();
                    } else {
                        this.nextSelect(event)
                    }
                    return false
                } else if (event.keyCode == 37) {
                    if (hzList.Yzxx_List.length > 1 && hzList.Yzxx_List[index].yyffbm != null) {
                        hzList.Wf_addYZ();
                    } else {
                        this.nextSelect(event)
                    }
                    return false
                } else if (event.keyCode == 39) {
                    if (hzList.Yzxx_List.length > 1 && hzList.Yzxx_List[index].yyffbm != null) {
                        hzList.Wf_addYZ();
                    } else {
                        this.nextSelect(event)
                    }
                    return false
                }
            }
            ,
            //回车事件
            Wf_keyEnter: function (event, index, type) {

                var value;
                var obj = event.currentTarget;
                var lx = this.Yzxx_List[index].lx;
                switch (type) {
                    //                case  "xmmc":
                    //                    if (!sfzt) {
                    //                        sfzt = false;
                    //                        this.Wf_addYZ();
                    //                    }
                    //                    break;
                    //单次剂量
                    case "dcjl":
                        value = $(obj).val();

                        if (!$.isNumeric(value)) {
                            $(obj).focus();
                            return;
                        }
                        //判断是药品还是诊疗
                        if (lx == "药品") {
                            $("#yyffbm_" + index).focus();
                        } else {
                            $("#sl_" + index).focus();
                        }
                        break;
                    //                    }

                    //用药方法
                    case "yyffbm":
                        $("#pcbm_" + index).focus();
                        break;
                    //用药方法
                    case "pcbm":
                        $("#sl_" + index).focus();
                        break;
                    //用药天数
                    case "yyts":
                        value = $(obj).val();
                        if (!$.isNumeric(value)) {
                            $(obj).focus();
                            return;
                        }
                        $("#sl_" + index).focus();
                        break;
                    //数量
                    case "yyzl":
                        console.log($(obj).val());
                        value = $(obj).val();
                        if (value == '' || value == 0) {
                            common.openConfirm('第' + index + '行' + this.Yzxx_List[index].ryypmc + '总量为零，是否继续', function () {
                                //判断是药品还是诊疗
                                if (lx == "药品") {
                                    if (hzList.Yzxx_List[index].gllb == 3) {
                                        $("#sysd_" + index).focus();
                                    } else {
                                        $("#yysm_" + index).focus();
                                    }
                                } else {
                                    if (hzList.Yzxx_List.length <= (index + 1)) {
                                        hzList.Wf_addYZ();   //如果医嘱行数是最后一行就新加一条医嘱
                                    } else {
                                        $("#yysm_" + index).focus();
                                    }
                                }
                            }, function () {
                                $(obj).focus();
                            })
                        } else {
                            //判断是药品还是诊疗
                            if (lx == "药品") {
                                if (hzList.Yzxx_List[index].gllb == 3) {
                                    $("#sysd_" + index).focus();
                                } else {
                                    $("#yysm_" + index).focus();
                                }
                            } else {
                                if (hzList.Yzxx_List.length <= (index + 1)) {
                                    hzList.Wf_addYZ();   //如果医嘱行数是最后一行就新加一条医嘱
                                } else {
                                    $("#yysm_" + index).focus();
                                }
                            }
                        }

                        // if (!$.isNumeric(value)) {
                        //     $(obj).focus();
                        //     return;
                        // }

                        break;
                    //输液速度
                    case "sysd":
                        value = $(obj).val();
                        if (!$.isNumeric(value)) {
                            $(obj).focus();
                            return;
                        }
                        //赋值
                        var fzh = this.Yzxx_List[index - 1].fzh;
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].sysd = this.Yzxx_List[index].sysd;
                                }
                            }
                        }
                        $("#sysddw_" + index).focus();
                        break;
                    //输液速度单位
                    case "sysddw":
                        $("#yysm_" + index).focus();
                        //赋值
                        var fzh = this.Yzxx_List[index - 1].fzh;
                        if (fzh > 0) {
                            for (var i = 0; i < this.Yzxx_List.length; i++) {
                                if (fzh == this.Yzxx_List[i].fzh && this.Yzxx_List[i].insertBz == true) {
                                    this.Yzxx_List[i].sysddw = this.Yzxx_List[index].sysddw;
                                }
                            }
                        }
                        break;
                    //医生说明
                    case "yysm":
                        if (this.Yzxx_List.length <= (index + 1)) {
                            this.Wf_addYZ();   //如果医嘱行数是最后一行就新加一条医嘱
                            //this.nextFocus(event)
                        } else {
                            this.nextFocus(event)
                        }
                        //setTimeout(function () {   //由于新增加一条数据需要延时0.1秒执行
                        // $("#xmmc_" + (index + 1)).focus();
                        // Vue.set(hzList.Yzxx_List[index + 1], 'fzh', fzh);     //分组号
                        // hzList.Wf_XmChange(event, index + 1, "fzh");  //触发fzh的Change事件
                        //   }, //100);
                        break;
                }
            }
            ,
        }
    })
;

//复制医嘱弹框
var brzcList = new Vue({
    el: '#brzcList',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 1,
        mxIndex: undefined,
        title: '复制医嘱',
        popContent: {
            yzlx: '1'
        },  //选中的值
        isShow: false,
        scollType: true,
        lsYzList: [],
        total: null,
        isyz: true,
        param: {
            yzlx: '0',
            beginrq: '',
            endrq: '',
            cxrq: 'ryrq'
        },
        xzsj: '',
        yzList: [],
        yzMxList: [],
        a: undefined,
        isChecked: [],
        YZ_GetRow: 0,
        rqxgbz: 0,
        oldRqxgbz: 0,
        dg: {page: 1, rows: 20, sort: "", order: "asc", parm: ""},//分页信息
        sfcy: null,
        ypbz_tran: {
            'all': '全部',
            '0': '非药品',
            '1': '药品',
        }
    },
    updated: function () {
        changeWin();
    },
    mounted: function () {
        this.param.beginrq = this.$options.filters['formDate'](new Date(new Date().getTime() - (7 * 3600 * 24 * 1000)))
        this.param.endrq = this.$options.filters['formDate'](new Date())
        laydate.render({
            elem: '#startTime'
            , trigger: 'click'
            , theme: '#1ab394'
            , done: function (value, data) {
                brzcList.param.beginrq = value
                brzcList.getIndexData()
            }
        });
        laydate.render({
            elem: '#endTime'
            , trigger: 'click'
            , theme: '#1ab394'
            , done: function (value, data) {
                brzcList.param.endrq = value
                brzcList.getIndexData()
            }
        });
        this.xzsj = getTodayDateTime()
        laydate.render({
            elem: '#xzsj',
            rigger: 'click',
            theme: '#1ab394',
            type: 'datetime',
            done: function (value, data) { //回调方法
                brzcList.rqxgbz = 1
                brzcList.xzsj = value;
            }
        });
    },
    methods: {
        reCheckChange: function (val) {
            var fzh = this.yzMxList[val[1]].fzh;//临时分组号

            for (var i = 0; i < this.yzMxList.length; i++) {
                if (parseInt(fzh) != 0 || parseInt(fzh) != '') {
                    if (fzh == this.yzMxList[i].fzh) {
                        Vue.set(this.isChecked, i, !this.isChecked[i]);
                    }
                } else if (fzh == 0 || fzh == '') {
                    // Vue.set(this.isChecked, i, false);
                    Vue.set(this.isChecked, val[1], val[2]);
                }
            }
            if (!val[2]) this.isCheckAll = false;
        },
        //医嘱模板明细
        getTemMxData: function (index, event) {
            brzcList.yzMxList = []
            this.isCheckAll = false;
            this.isChecked = [];
            this.mxIndex = index;
            var zhyzbm = this.yzList[index].zhyzbm;
            var json = {
                zhyzbm: zhyzbm
            };
            brzcList.dg.rows = 20;
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zhyzmx&dg=' + JSON.stringify(brzcList.dg) + '&json=' + JSON.stringify(json), function (data) {
                if (data.d != null) {
                    brzcList.yzMxList = data.d.list;
                    console.log(brzcList.yzMxList)
                }
            });
        },
        commonResultChange: function (val) {
            var type = val[2][val[2].length - 1];
            switch (type) {
                case "yzlx":
                    Vue.set(this.param, 'yzlx', val[0]);
                    brzcList.getLsTemMxData(brzcList.mxIndex, null);
                    break;
                case "ypbz":
                    Vue.set(this.param, 'ypbz', val[0]);
                    brzcList.getTemData();
                    break;
            }
        },
        //历史医嘱明细
        getLsTemMxData: function (index, event) {
            brzcList.yzMxList = []
            if (this.lsYzList.length == 0) return false
            this.isCheckAll = false;
            this.mxIndex = index;
            var zyh = this.lsYzList[index].zyh;
            var ksbm = this.lsYzList[index].ryks;   //科室
            var yzlx = this.param.yzlx;
            var ystzbz = panel.popContent.yzgl;
            if (yzlx == '%') yzlx = '';
            if (ystzbz == '%') ystzbz = '';
            if (ksbm == null || ksbm == undefined || ksbm == "") {
                malert("病人科室不能为空！，请选择科室.", 'top', 'defeadted');
                return false;
            }
            if (zyh == null || zyh == undefined || zyh == "") {
                malert("请选择病人！", 'top', 'defeadted');
                return false;
            }

            var parm_ksbm = {
                ksbm: ksbm,
                yzlx: yzlx,
                // ystzbz:'0',
                zfbz: '0',
                // sfcy:brzcList01.sfcy,
            };
            var parm_zyh = [{
                zyh: zyh
            }];
            var types = "";
            // if (brzcList01.sfcy == "1"){
            // 	types = "hzyzxxcxfj";
            // }else{
            types = "hzyzxxcx";
            // }
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=' + types + '&parm=' + JSON.stringify(parm_ksbm) + '&zyh=' + JSON.stringify(parm_zyh), function (json) {
                if (json.a == '0') {
                    //把时间戳改改时间
                    if (json.d.list.length > 0) {
                        for (var i = 0; i < json.d.list.length; i++) {
                            json.d.list[i].readonly = true;   //只读
                            json.d.list[i].ksrq = formatTime(json.d.list[i].ksrq, "datetime");
                            if (json.d.list[i].xssx == "0") {
                                json.d.list[i].xssx = i + 1;
                            }
                            //特殊医嘱
                            var tsyz = json.d.list[i].tsyz;
                            if (tsyz == "1") {
                                json.d.list[i].tsyz = true;
                            } else {
                                json.d.list[i].tsyz = false;
                            }
                            json.d.list[i].hstzbz = null;
                            json.d.list[i].hstzsj = null;
                            json.d.list[i].shbz = null;
                            json.d.list[i].shhs = null;
                            json.d.list[i].shhsxm = null;
                            json.d.list[i].shsj = null;
                            json.d.list[i].zxbz = null;
                            json.d.list[i].zxhs = null;
                            json.d.list[i].zxhsxm = null;
                            json.d.list[i].zxks = null;
                            json.d.list[i].zxksmc = null;
                            json.d.list[i].zxkssj = null;
                            json.d.list[i].zxsj = null;
                            json.d.list[i].zxts = null;

                        }
                        brzcList.yzMxList = json.d.list;
                    } else {
                        malert("该病人无历史医嘱！", 'top', 'defeadted');
                    }
                }
            });
        },
        //滚动条
        scroll: function (event) {
            this.$refs.scroll.style.marginLeft = 0 - $(event.target).scrollLeft() + "px";
        },
        //关闭弹框
        closes: function () {
            this.isChecked = [];
            this.num = 1
        },
        //双击全选
        dballCheck: function () {
            //全选
            this.isCheckAll = true;
            for (var i = 0; i < brzcList.yzMxList.length; i++) {
                Vue.set(brzcList.isChecked, i, true);
            }
            this.confirms();
        },
        //保存
        confirms: function () {
            // if(brzcList01.sfcy=='1')return malert('此为中药操作，请双击中药医嘱','top','defeadted')
            if (userNameBg.Brxx_List.zyzt == '1' || userNameBg.Brxx_List.zyzt == '2') {
                malert("病人未在院，无法执行此操作！", 'top', 'defeadted');
                return false;
            }
            if (this.isChecked.length > 0) {
                for (var i = 0; i < this.isChecked.length; i++) {
                    if (this.isChecked[i] == true) {
                        if (brzcList00.numOne != 0) {
                            brzcList00.sfxz = true;
                            delete this.yzMxList[i].shbz
                            delete this.yzMxList[i].shhs
                            delete this.yzMxList[i].shhsxm
                            delete this.yzMxList[i].shsj
                            delete this.yzMxList[i].zxbz
                            delete this.yzMxList[i].zxhs
                            delete this.yzMxList[i].zxhsxm
                            delete this.yzMxList[i].zxks
                            delete this.yzMxList[i].zxksmc
                            delete this.yzMxList[i].zxsj
                            delete this.yzMxList[i].ystzbz
                            delete this.yzMxList[i].tzys
                            delete this.yzMxList[i].tzysxm
                            delete this.yzMxList[i].ystzsj
                            delete this.yzMxList[i].ystzsm
                            delete this.yzMxList[i].hstzbz
                            delete this.yzMxList[i].tzhs
                            delete this.yzMxList[i].tzhsxm
                            delete this.yzMxList[i].hstzsj
                            delete this.yzMxList[i].sqdh
                            delete this.yzMxList[i].ysqm
                            delete this.yzMxList[i].ysqmxm
                            delete this.yzMxList[i].ysqmsj
                            delete this.yzMxList[i].xdys
                            delete this.yzMxList[i].xdysxm
                            this.yzMxList[i].yzlx = this.param.yzlx;
                            this.yzMxList[i].ksrq = this.xzsj;
                            this.yzMxList[i].rqxgbz = this.rqxgbz;
                            this.yzMxList[i].ysqmks = userNameBg.Brxx_List.ryks;
                            this.yzMxList[i].ysqmksmc = userNameBg.Brxx_List.ryksmc;
                            this.yzMxList[i].yfbm = panel.popContent.yfbm;
                            this.yzMxList[i].yfmc = panel.popContent.yfmc;
                            this.yzMxList[i].yzfl = '0';
                            this.yzMxList[i].sfcy = '0';
                            this.yzMxList[i].yyts = 1;
                            this.yzMxList[i].insertBz = true;
                            this.yzMxList[i].updateBz = false;
                            this.yzMxList[i].readonly = false;
                            this.yzMxList[i].xssx = hzList.Yzxx_List.length + 1;
                            this.yzMxList[i].zt = '4';
                            this.yzMxList[i].yzxh = null;
                            if (this.yzMxList[i].sl == null || this.yzMxList[i].sl == '' || this.yzMxList[i].sl == undefined) {
                                this.yzMxList[i].sl = this.yzMxList[i].yyzl;
                            }

                            hzList.Yzxx_List.push(JSON.parse(JSON.stringify(this.yzMxList[i])));
                        } else {
                            var yfbm = panel.popContent.yfbm;
                            if (yfbm == null || yfbm == undefined || yfbm == "") {
                                malert("请选择药房!", 'top', 'defeadted');
                                return false;
                            }
                            if (yfbm != '2') {
                                malert("请选择中药房!", 'top', 'defeadted');
                                return false;
                            }
                            //                                hzList.getKssSq(this.popContentList); //判断该医生是否有权使用抗生素
                            var zcy = {};
                            zcy.sfcy = '1';
                            //下拉框回车后回调.
                            zcy.yzfl = '0';
                            zcy.xssx = brzcList00.zcyList.length + 1;
                            zcy.ksrq = this.xzsj;
                            zcy.rqxgbz = this.rqxgbz;
                            zcy.yzlx = this.param.yzlx;
                            zcy.ysqmks = userNameBg.Brxx_List.ryks;
                            zcy.ysqmksmc = userNameBg.Brxx_List.ryksmc;
                            zcy.xmbm = this.yzMxList[i].xmbm;
                            zcy.xmmc = this.yzMxList[i].xmmc;
                            zcy.jldw = this.yzMxList[i].jldw;
                            zcy.jldwmc = this.yzMxList[i].jldwmc;
                            zcy.yfdw = this.yzMxList[i].yfdw;
                            zcy.yfdwmc = this.yzMxList[i].yfdwmc;
                            zcy.yfbm = panel.popContent.yfbm;
                            zcy.yfmc = panel.popContent.yfmc;
                            zcy.ybtclb = this.yzMxList[i].ybtclb;
                            zcy.nbtclb = this.yzMxList[i].nbtclb;
                            zcy.ybtclbmc = this.yzMxList[i].ybtclbmc;
                            zcy.kssjb = null;
                            zcy.kcfbz = '0';
                            zcy.jxbm = this.yzMxList[i].jxbm;
                            zcy.yyffbm = this.yzMxList[i].yyffbm;
                            zcy.yyffmc = this.yzMxList[i].yyffmc;
                            zcy.yyts = 1;
                            zcy.lx = "药品";
                            zcy.ypzl = '02';
                            zcy.pcbm = "01";
                            zcy.pcmc = "即刻执行";
                            zcy.pccs = 1;
                            zcy.zyh = userNameBg.Brxx_List.ghxh;
                            zcy.ksbm = userNameBg.Brxx_List.ryks;
                            zcy.dcjl = 1;
                            zcy.sl = this.yzMxList[i].dcjl;
                            zcy.yyzl = zcy.sl;
                            zcy.zlbz = false;
                            // zcy.shbz = '0';
                            brzcList00.zcyList.push(zcy);
                        }

                    }
                }
                this.isChecked = []
                this.mxIndex = undefined
            } else {
                malert("请选择需要复制的医嘱", 'top', 'defeadted');
                return false;
            }

            this.num = 1
            this.$nextTick(function () {
                hzList.$refs.body.scrollTop = hzList.$refs.body.scrollHeight
            })
        },
        getIndexData() {
            if (this.isyz) {
                this.getTemData()
            } else {
                this.getLsTemData()
            }
        },
        //所有在院患者
        getLsTemData: function (index) {
            this.isyz = false;
            this.mxIndex = index;
            this.param.ryks = userNameBg.Brxx_List.ryks;
            // 复制医嘱 显示不全，将行数显示为500
            var par = JSON.parse(JSON.stringify(this.param));
            par.rows = 500;
            $.getJSON('/actionDispatcher.do?reqUrl=New1ZyysYsywYzcl&types=zyhzxx&parm=' + JSON.stringify(par) + '', function (json) {
                if (json.a == '0') {
                    brzcList.lsYzList = json.d.list;
                    if (brzcList.lsYzList.length > 0) {
                        for (var i = 0; i < brzcList.lsYzList.length; i++) {
                            if (userNameBg.Brxx_List.ghxh == brzcList.lsYzList[i].zyh) {
                                var str = brzcList.lsYzList.splice(i, 1);
                                brzcList.lsYzList.unshift(str[0]);
                                break;
                            }
                        }
                    }


                    brzcList.getLsTemMxData(0);
                    // $(".content-right-list").uiscroll({ height: '100%', size: '3px', opacity: 1 });
                }
            });
        },
        scrollGata(event) {
            if (event.srcElement.scrollHeight - event.srcElement.scrollTop === event.srcElement.clientHeight) {
                if (event.target.scrollLeft < 0 || this.scrollLeft == event.target.scrollLeft) {
                    if (event.target.scrollTop > this.scrollTop) {
                        if (this.scollType) {
                            this.scollType = false
                            if (this.yzList.length < this.total) {
                                if (this.uilPageBottom() == true) {
                                    this.param.page = this.param.page + 1;
                                    this.getTemData();
                                }
                            } else {
                            }
                        }
                    }
                }
            }
            this.scrollLeft = event.target.scrollLeft
            this.scrollTop = event.target.scrollTop
        },
        //医嘱模板
        getTemData: function () {
            this.isyz = true;
            this.yzMxList = [];
            this.param.rows = 20;
            this.param.sort = 'zhyzbm';
            this.param.order = 'desc';
            this.param.sfcy = brzcList.sfcy;
            this.param.yyz = userId;
            this.param.yyks = userNameBg.Brxx_List.ryks;
            this.param.lx = '2';//住院
            if (!this.param.ypbz) {
                //this.param.ypbz = '0';
                // 默认显示全部 @yqq
                this.param.ypbz = 'all';

            }
            $.getJSON("/actionDispatcher.do?reqUrl=New1MzysZlglZhyz&types=queryCfmb&parm=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) {
                    brzcList.scollType = true;
                    brzcList.total = json.d.total;
                    brzcList.yzList = json.d.list;
                    // brzcList.yzList = brzcList.yzList.concat(json.d.list);
                }
            });
        },
        // getZyyz:function (item) {
        //     if(brzcList01.sfcy=='1'){
        //         brzcList01.num=0
        //         brzcList01.ghtjxData(item.yzxh,item.zyh)
        //     }
        // }
    },
});

//中药医嘱
var brzcList00 = new Vue({
    el: '#brzcList00',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        yfbmObj: 'zyf',
        zcyList: [],
        jsonList: [],
        yfyycflx: [],        //药房拥有处方类型
        yyffList: [],
        ypList: [], //药品
        jsypxx: null, //检索的药品信息
        numOne: 1,
        selectInde: 0,
        sum: 0,
        djsum: 0,
        iszcy: true,
        //isdisabled: false,
        ifClick: true,
        title: '医嘱项目',
        CflxJosn: [],
        popContent: {
            lxyz: '0',
            insertBz: true,
            readonly: false,
            ysqmks: userNameBg.Brxx_List.ryks,
            ysqmksmc: userNameBg.Brxx_List.ryksmc,
        },
        Yf_List: {'zyf': '中药房', 'xyf': '西药房'},
        brlt: userNameBg.Brxx_List,
        popContentList: {},
        selSearch: -1,
        searchCon: [],
        them_tran: {},
        ypyf: '',
        them: {
            '药品名称': 'ypmc', '药品规格': 'ypgg', '库存': 'kcsl', '药房单位': 'yfdwmc',
            '价格': 'yplj', '医保统筹': 'ybtclbmc', '农保统筹': 'nbtclbmc'
        },
        ypInfo: {"yplx": "中草药处方", "ypjs": 1, "ypyf": "", "ypbz": "中草药处方", "fjmc": "", 'yysmbm': ''},
        ksrq: getTodayDateTime(),
        page: {
            page: 1,
            rows: 20,
            total: null
        },
        editIndex: null,
        is_csqx: {},
        yzxh: '',
    },
    created: function () {
        $.getJSON("/actionDispatcher.do?reqUrl=New1xtwhylfwxmzcyyysm&types=query&dg=" + JSON.stringify(this.param), function (json) {
            brzcList00.jsonList = json.d.list;
        });
    },
    components: {
        'search-table': searchTable,
    },
    watch: {
        ypyf: {
            handler: function (newVal) {
                deep: true
                this.ypInfo.ypyf = newVal
            }
        },
        'zcyList': {
            deep: true,
            handler: function (newVal, oldVal) {
                immediate: true
                this.setSum()
            }

        },
        'ypInfo.ypjs': function (newVal, oldVal) {
            this.setSum()
        }

    },
    computed: {
        k: function () {
            this.$nextTick(function () {
                return this.$refs.hzglBtn.style.height = $('.body').outerHeight() - $('.dzcf-list').offset().top - $('.hzgl-btn').outerHeight() + 'px'
            })
        },
        setZyf:function () {
            for (var i = 0; i <this.Yf_List.length ; i++) {
                if(this.Yf_List[i].yfmc=='中药房'){
                    this.popContent.yfbm=this.Yf_List[i].yfbm
                    this.popContent.yfmc=this.Yf_List[i].yfmc
                }
            }
        }
    },
    methods: {
        //药房改变
        resultChange_text: function (val) {
            Vue.set(this[val[2][0]], [val[2][1]], val[0]);
            Vue.set(this[val[2][0]], 'yfmc', val[0]);
            this.zcyList=[{}]
            this.nextFocus(val[1], 1);
            this.cfglData();
        },
        //调用公共方法处理处方过滤问题
        cfglData: function () {
            this.popContent.cflxbm = ''
            //处方类型默认为西药（根据药房过滤出处方集合）
            this.CflxJosn = jsonFilter(this.yfyycflx, "yfbm", this.popContent.yfbm);
            //显示药房关联的处方
            if (this.CflxJosn.length != 0) {
                for (var i = 0; i < this.Yf_List.length; i++) {
                    if (this.popContent.yfbm == this.Yf_List[i].yfbm) {
                        this.popContent.cflxbm = this.Yf_List[i]['qscflx'];
                    }
                }
            }
        },
        //组件选择下拉框之后的回调
        resultLxChange: function (val) {
            //先获取到操作的哪一个
            Vue.set(this.popContent, 'cflxbm', val[0]);
            Vue.set(this.popContent, 'cflxmc', val[4]);
            Vue.set(this.popContent, 'mbmc', val[0]);
            this.$forceUpdate()
        },
        fzzyyz: function () {
            // brzcList01.sfcy=1
            hzList.showLsYz(true)
        },
        setSum: function () {
            for (var i = 0; i < this.zcyList.length; i++) {
                this.djsum += (this.zcyList[i].yyzl == undefined ? 0 : parseInt(this.zcyList[i].yyzl)) * (this.zcyList[i].cklj == undefined ? 0 : this.zcyList[i].cklj)
            }
            this.sum = this.djsum * parseInt(Number(this.ypInfo.ypjs))
            this.djsum = 0
            return
        },
        ypyfFun: function (n) {
            this.ypInfo.ypyf = n
        },
        yyzl: function (mc, index, value) {
            Vue.set(this.zcyList[index], mc, value)
            // this.changeIt(this.zcyList)
        },
        resultChangeMb: function (val) {
            if (val[0] != this.ypInfo.yysmbm) {
                Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                Vue.set(this[val[2][0]], val[3], val[4]);
            }
            if (val[1] != null) {
                this.nextFocus(val[1], 3);
            }
        },
        // 显示医嘱模板
        showYzTem: function () {
            brzcList.yzList = [];
            brzcList.sfcy = '1';
            brzcList.param.page = 1
            brzcList.getTemData();
            brzcList.num = 0;
        },
        //***********************西药start**************************
        //获取药品信息
        Wf_getYpxx: function () {
            var json = {
                page: 1,
                rows: 20,
                sort: "xmbm",
                order: "asc",
                parm: brzcList00.jsypxx,
                yfbm: panel.popContent.yfbm
            };
            console.log("yzxm2964");
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yzxm&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    brzcList00.ypList = [];  //先清空
                    if (data.d.list.length > 0) {
                        brzcList00.ypList = data.d.list;
                    }
                } else {
                    malert("获取项目列表失败！" + data.c, 'top', 'defeadted');
                }
            });
        },

        //添加西药药品的通用方法
        getMessage: function () {
            //此次为添加药品信息
            for (var i = 0; i < this.isChecked.length; i++) {
                if (this.isChecked[i] == true) {
                    hzList.getKssSq(this.ypList[i]); //判断该医生是否有权使用抗生素

                    this.ypList[i].sfcy = '0';
                    this.ypList[i].jcfl = this.ypList[i].jclx;
                    this.ypList[i].jldw = this.ypList[i].jldwbm;
                    this.ypList[i].ypgg = this.ypList[i].xmgg;
                    this.ypList[i].ybtclb = this.ypList[i].yb;
                    this.ypList[i].nbtclb = this.ypList[i].nh;
                    this.ypList[i].ypzl = this.ypList[i].zlbm;
                    this.ypList[i].zyh = userNameBg.Brxx_List.ghxh;
                    this.ypList[i].ksbm = userNameBg.Brxx_List.ryks;
                    this.ypList[i].yfbm = panel.popContent.yfbm;
                    this.ypList[i].yfmc = panel.popContent.yfmc;
                    var yzlx = '1';
                    if (panel.popContent.yzgl == '%' || panel.popContent.yzgl == '1') {
                        yzlx = '1';
                    } else {
                        yzlx = '0';
                    }
                    this.ypList[i].yzlx = yzlx;
                    this.ypList[i].ksrq = getTodayDateTime();
                    this.ypList[i].ysqmks = userNameBg.Brxx_List.ryks;
                    this.ypList[i].ysqmksmc = userNameBg.Brxx_List.ryksmc;
                    this.ypList[i].yzfl = '0';
                    this.ypList[i].yyts = 1;
                    this.ypList[i].insertBz = true;
                    this.ypList[i].updateBz = false;
                    this.ypList[i].readonly = false;
                    this.ypList[i].xssx = hzList.Yzxx_List.length + 1;
                    this.ypList[i].zt = '4';
                    this.ypList[i].yzxh = null;
                    hzList.Yzxx_List.push(JSON.parse(JSON.stringify(this.ypList[i])));
                    setTimeout(function () {
                        if (hzList.sfqk == 1) {
                            for (var j = 0; j < hzList.Yzxx_List.length; j++) {
                                if (hzList.Yzxx_List[j].xmbm = brzcList00.ypList[i].xmbm) {
                                    hzList.Yzxx_List.splice(j, 1);
                                    hzList.sfqk = 0;
                                }
                            }
                        }
                    }, 5000);
                    this.ifClick = true;
                }
            }
        },


        //*********************西药end*****************************************


        //中药药品剂量的回车事件
        nextAdd: function (event, index) {
            if (event.keyCode == 13) {
                // var _input = $(".zcyItem input").not(":disabled,input[type=checkbox],input[type=date]");
                // for (var i = 0; i < _input.length; i++) {
                //     if (_input.eq(i)[0] == event.currentTarget) {
                //         _input.eq(i + 1).focus();
                //     }
                //     if (this.zcyList.length - 1 == event.currentTarget.tabIndex) {
                //         this.add()
                //     }
                // }
                if (this.zcyList[index + 1]) {
                    this.nextFocus(event, 3);
                }
            }
        },
        //添加中药输入框
        add: function () {
            if (this.zcyList.length <= 20) {
                this.zcyList.push({});
                this.editIndex = this.zcyList.length - 1;
                setTimeout(function () {
                    $("#xmsr_" + (brzcList00.zcyList.length - 1)).focus();
                }, 100);
                Vue.set(this.zcyList[this.zcyList.length - 1], 'yyffmc', this.zcyList[0].yyffmc);
                Vue.set(this.zcyList[this.zcyList.length - 1], 'yyff', this.zcyList[0].yyff);
                Vue.set(this.zcyList[this.zcyList.length - 1], 'yyffbm', this.zcyList[0].yyffbm);
                Vue.set(this.zcyList[this.zcyList.length - 1], 'gllb', '0');
            } else {
                malert("一张处方最多允许保存20位草药！", 'top', 'defeadted');
            }
        },
        //最后一个输入框回车添加中药输入框
        autoAdd: function (event, index) {
            if (this.zcyList.length <= 20) {
                if (event.keyCode == 13) {
                    if (this.zcyList.length == index + 1) {
                        setTimeout(function () {
                            $("#xmsr_" + (index + 1)).focus();
                        }, 100);
                    } else {
                        this.editIndex = index + 1;
                        setTimeout(function () {
                            $("#xmsr_" + this.editIndex).focus();
                        }, 100);
                    }
                }
            } else {
                malert("一张处方最多允许保存20位草药！", 'top', 'defeadted');
            }
        },
        //中药处方删除
        remove: function (index) {
            this.zcyList.splice(index, 1)
        },


        searching: function (add, index, type, val) {
            this.selectInde = index;
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            this.zcyList[index][type] = val;
            var yfbm = this.popContent.yfbm;
            if (!yfbm) {
                malert("请选择药房", 'top', 'defeadted');
                return false;
            }
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            var pram = this.zcyList[index][type];
            //截取首尾空格
            this.page.parm = trimStr(pram);
            var json = {
                yfbm: this.popContent.yfbm,
                cflx: this.popContent.cflxbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDownYw&types=yfkc&dg=' + JSON.stringify(this.page) + '&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    if (add) {//不是第一页则需要追加
                        for (var i = 0; i < data.d.list.length; i++) {
                            brzcList00.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        brzcList00.searchCon = data.d.list;
                    }

                    brzcList00.page.total = data.d.total;
                    brzcList00.selSearch = 0;
                    if (data.d.list.length != 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show()
                    }
                }
            });
        },
        changeDown: function (index, event, type, value) {
            // var yfbm = window.J_tabLeft.obj.zyyzbm;
            // if (yfbm == null || yfbm == undefined || yfbm == "") {
            //     malert("请选择药房!", 'top', 'defeadted');
            //     return false;
            // }
            // if (yfbm != '2') {
            //     malert("请选择中药房!", 'top', 'defeadted');
            //     return false;
            // }
            this.keyCodeFunction(event, 'popContentList', 'searchCon');

            if (type == 'xmmc') {
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (this.popContentList && index == this.zcyList.length - 1) {
                        this.add(event, true, 4)
                        // this.nextFocus(event,5);
                    }
                    // else if (!event.srcElement.value == '') {
                    //     this.nextFocus(event, 4);
                    // }
                }
            } else {
                this.nextFocus(event);
            }

            //赋值
            if (event.keyCode == 13 || event.code == 'NumpadEnter ') {
                if (!this.popContentList) return
                if (this.zcyList[index].xmbm != undefined) {
                    this.nextFocus(event, 2);
                    return false;
                }
                hzList.getKssSq(this.popContentList); //判断该医生是否有权使用抗生素
                var sfcy = '1';
                //下拉框回车后回调.
                var yzfl = '0';
                var xssx = this.zcyList.length + 1;
                var ksrq = this.ksrq;
                var yzlx = '0';
                var ysqmks = userNameBg.Brxx_List.ryks;
                var ysqmksmc = userNameBg.Brxx_List.ryksmc;
                var xmbm = this.popContentList.ypbm;
                var xmmc = this.popContentList.ypmc;
                var cklj = this.popContentList.yplj;
                var jbjl = this.popContentList.jbjl;
                var jldw = this.popContentList.jldw;
                var jldwmc = this.popContentList.jldwmc;
                var yfdw = this.popContentList.yfdw;
                var yfdwmc = this.popContentList.yfdwmc;
                var yfbm = panel.popContent.yfbm;
                var yfmc = panel.popContent.yfmc;
                var ybtclb = this.popContentList.ybtclb;
                var nbtclb = this.popContentList.nbtclb;
                var ybtclbmc = this.popContentList.ybtclbmc;
                var kssjb = this.popContentList.kssjb;
                var kcfbz = this.popContentList.kcfbz;
                var jxbm = this.popContentList.jxbm;
                var ypgg = this.popContentList.ypgg;
                var ysts = 1;
                var lx = "药品";
                var ypzl = '02';
                var pcbm = "01";
                var pcmc = "即刻执行";
                var pccs = 1;

                Vue.set(this.zcyList[index], 'sfcy', sfcy);
                Vue.set(this.zcyList[index], 'xssx', xssx);
                Vue.set(this.zcyList[index], 'ysqmks', ysqmks);
                Vue.set(this.zcyList[index], 'ysqmksmc', ysqmksmc);
                Vue.set(this.zcyList[index], 'ksrq', ksrq);
                Vue.set(this.zcyList[index], 'yzfl', yzfl);
                Vue.set(this.zcyList[index], 'yzlx', yzlx);
                Vue.set(this.zcyList[index], 'lx', lx);       //类型  药品？诊疗
                Vue.set(this.zcyList[index], 'zloryp', 'yp');       //类型  药品？诊疗
                Vue.set(this.zcyList[index], 'ryypbm', xmbm);   //项目编码
                Vue.set(this.zcyList[index], 'ryypmc', xmmc);   //项目名称
                Vue.set(this.zcyList[index], 'cklj', cklj);   //价格
                Vue.set(this.zcyList[index], 'jbjl', jbjl);   //基本剂量
                Vue.set(this.zcyList[index], 'jldw', jldw);   //剂量单位
                Vue.set(this.zcyList[index], 'jldwmc', jldwmc);  //剂量单位名称
                Vue.set(this.zcyList[index], 'yfdw', yfdw);      //药房单位
                Vue.set(this.zcyList[index], 'yfdwmc', yfdwmc);  //药房单位名称
                Vue.set(this.zcyList[index], 'ghxh', userNameBg.Brxx_List.ghxh);  //住院号
                Vue.set(this.zcyList[index], 'ksbm', userNameBg.Brxx_List.ksbm); //科室
                Vue.set(this.zcyList[index], 'lgks', userNameBg.Brxx_List.ksbm); //科室
                Vue.set(this.zcyList[index], 'yyts', 1);            //用药天数
                Vue.set(this.zcyList[index], 'dcjl', 1);              //数量
                Vue.set(this.zcyList[index], 'yfbm', panel.popContent.yfbm); //药房编码
                Vue.set(this.zcyList[index], 'yfmc', panel.popContent.yfmc); //药房名称
                Vue.set(this.zcyList[index], 'ybtclb', ybtclb);      //医保统筹类别
                Vue.set(this.zcyList[index], 'ybtclbmc', ybtclbmc);  //医保统筹类别名称
                Vue.set(this.zcyList[index], 'nbtclb', nbtclb);      //农保统筹类别
                Vue.set(this.zcyList[index], 'ypzl', ypzl);          //药品种类
                Vue.set(this.zcyList[index], 'kcfbz', kcfbz);        //可拆分标志
                Vue.set(this.zcyList[index], 'kssjb', kssjb);        //抗生素级别
                Vue.set(this.zcyList[index], 'jxbm', jxbm);          //剂型编码
                Vue.set(this.zcyList[index], 'zlbz', false);        //诊疗标志
                Vue.set(this.zcyList[index], 'pcbm', pcbm);        //抗生素级别
                Vue.set(this.zcyList[index], 'pcmc', pcmc);          //剂型编码
                Vue.set(this.zcyList[index], 'pccs', pccs);
                Vue.set(this.zcyList[index], 'ypgg', ypgg);   //药品规格
                this.selSearch = 0;
                this.nextFocus(event, 2);
                setTimeout(function () {
                    if (hzList.sfqk == 1) {
                        hzList.zcyList.splice(index, 1);
                        hzList.sfqk = 0;
                    }
                }, 300);

            }
        },
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;               // 设置当前页号
                this.searching(true, this.selectInde, 'xmmc', this.zcyList[this.selectInde]['xmmc']);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                hzList.getKssSq(item); //判断该医生是否有权使用抗生素

                this.popContentList = item;
                var sfcy = '1';
                var yzfl = '0';
                var xssx = this.zcyList.length + 1;
                var ksrq = this.ksrq;
                var yzlx = '0';
                var ysqmks = userNameBg.Brxx_List.ryks;
                var ysqmksmc = userNameBg.Brxx_List.ryksmc;
                var xmbm = this.popContentList.ypbm;
                var xmmc = this.popContentList.ypmc;
                var cklj = this.popContentList.yplj;
                var jbjl = this.popContentList.jbjl;
                var jldw = this.popContentList.jldw;
                var jldwmc = this.popContentList.jldwmc;
                var yfdw = this.popContentList.yfdw;
                var yfdwmc = this.popContentList.yfdwmc;
                var yfbm = panel.popContent.yfbm;
                var yfmc = panel.popContent.yfmc;
                var ybtclb = this.popContentList.ybtclb;
                var nbtclb = this.popContentList.nbtclb;
                var ybtclbmc = this.popContentList.ybtclbmc;
                var kssjb = this.popContentList.kssjb;
                var kcfbz = this.popContentList.kcfbz;
                var jxbm = this.popContentList.jxbm;
                var ysts = 1;
                var lx = "药品";
                var ypzl = '02';
                var pcbm = "01";
                var pcmc = "即刻执行";
                var pccs = 1;

                Vue.set(this.zcyList[this.selectInde], 'sfcy', sfcy);
                Vue.set(this.zcyList[this.selectInde], 'xssx', xssx);
                Vue.set(this.zcyList[this.selectInde], 'ysqmks', ysqmks);
                Vue.set(this.zcyList[this.selectInde], 'ysqmksmc', ysqmksmc);
                Vue.set(this.zcyList[this.selectInde], 'ksrq', ksrq);
                Vue.set(this.zcyList[this.selectInde], 'yzfl', yzfl);
                Vue.set(this.zcyList[this.selectInde], 'yzlx', yzlx);
                Vue.set(this.zcyList[this.selectInde], 'lx', lx);       //类型  药品？诊疗
                Vue.set(this.zcyList[this.selectInde], 'zloryp', 'yp');       //类型  药品？诊疗
                Vue.set(this.zcyList[this.selectInde], 'ryypbm', xmbm);   //项目编码
                Vue.set(this.zcyList[this.selectInde], 'ryypmc', xmmc);   //项目名称
                Vue.set(this.zcyList[this.selectInde], 'cklj', cklj);   //价格
                Vue.set(this.zcyList[this.selectInde], 'jbjl', jbjl);   //基本剂量
                Vue.set(this.zcyList[this.selectInde], 'jldw', jldw);   //剂量单位
                Vue.set(this.zcyList[this.selectInde], 'jldwmc', jldwmc);  //剂量单位名称
                Vue.set(this.zcyList[this.selectInde], 'yfdw', yfdw);      //药房单位
                Vue.set(this.zcyList[this.selectInde], 'yfdwmc', yfdwmc);  //药房单位名称
                Vue.set(this.zcyList[this.selectInde], 'ghxh', userNameBg.Brxx_List.ghxh);  //住院号
                Vue.set(this.zcyList[this.selectInde], 'ksbm', userNameBg.Brxx_List.ksbm); //科室
                Vue.set(this.zcyList[this.selectInde], 'lgks', userNameBg.Brxx_List.ksbm); //科室
                Vue.set(this.zcyList[this.selectInde], 'yyts', 1);            //用药天数
                Vue.set(this.zcyList[this.selectInde], 'dcjl', 1);              //数量
                Vue.set(this.zcyList[this.selectInde], 'yfbm', panel.popContent.yfbm); //药房编码
                Vue.set(this.zcyList[this.selectInde], 'yfmc', panel.popContent.yfmc); //药房名称
                Vue.set(this.zcyList[this.selectInde], 'ybtclb', ybtclb);      //医保统筹类别
                Vue.set(this.zcyList[this.selectInde], 'ybtclbmc', ybtclbmc);  //医保统筹类别名称
                Vue.set(this.zcyList[this.selectInde], 'nbtclb', nbtclb);      //农保统筹类别
                Vue.set(this.zcyList[this.selectInde], 'ypzl', ypzl);          //药品种类
                Vue.set(this.zcyList[this.selectInde], 'kcfbz', kcfbz);        //可拆分标志
                Vue.set(this.zcyList[this.selectInde], 'kssjb', kssjb);        //抗生素级别
                Vue.set(this.zcyList[this.selectInde], 'jxbm', jxbm);          //剂型编码
                Vue.set(this.zcyList[this.selectInde], 'zlbz', false);        //诊疗标志
                Vue.set(this.zcyList[this.selectInde], 'pcbm', pcbm);        //抗生素级别
                Vue.set(this.zcyList[this.selectInde], 'pcmc', pcmc);          //剂型编码
                Vue.set(this.zcyList[this.selectInde], 'pccs', pccs);
                this.selSearch = 0;
                $(".selectGroup").hide();
                setTimeout(function () {
                    if (hzList.sfqk == 1) {
                        hzList.zcyList.splice(this.selectInde, 1);
                        hzList.sfqk = 0;
                    }
                }, 300);

            }
        },
        //用药方法改变事件
        resultChange_yyff: function (val) {
            Vue.set(this.zcyList[val[2][1]], [val[2][2]], val[4]);
            Vue.set(this.zcyList[val[2][1]], 'yyff', val[0]);
            Vue.set(this.zcyList[val[2][1]], 'yyffbm', val[0]);
            Vue.set(this.zcyList[val[2][1]], 'gllb', '0');
            if (this.zcyList[parseInt([val[2][1]]) + 1]) {
                this.nextFocus(val[1], 3);
            }
        },

        //获取用药方法
        getYyff: function () {
            var json = {
                sfcy: 1
            };
            $.getJSON("/actionDispatcher.do?reqUrl=GetDropDown&types=yyff" + '&json=' + JSON.stringify(json), function (json) {
                if (json.a == 0) {
                    brzcList00.yyffList = json.d.list;
                } else {
                    malert("用药方法列表查询失败", 'top', 'defeadted');

                }
            });
        },
        //中药医嘱复选框
        zcy: function (val) {
            if (val[0] == 'zyf') {
                this.iszcy = true
            } else {
                this.iszcy = false
            }
            // this[type] = !this[type];
            Vue.set(this, 'yfbmObj', val[0]);
            if (val[0] === 'zyf') {
                this.getYyff();
                this.sfxz = true;
            }
        },
        //查看中药明细
        initZcyList: function (zyh, yzxh) {
            var parm = {
                parm: {
                    ghxh: zyh,
                    yzxh: yzxh,
                    page: 1,
                    rows: 200
                }
            };
            brzcList00.$http.post("/actionDispatcher.do?reqUrl=New1MjzglYzd&types=queryZyyzMx", JSON.stringify(parm)).then(function (data) {

                if (data.body.a == 0) {
                    brzcList00.zcyList = data.body.d.list;
                    for (var i = 0; i < brzcList00.zcyList.length; i++) {
                        brzcList00.zcyList[i].yyff = brzcList00.zcyList[i].yyffbm;
                        brzcList00.zcyList[i].yyzl = brzcList00.zcyList[i].yyzl / brzcList00.zcyList[0].zyjs;
                    }
                    brzcList00.ypInfo.ypjs = brzcList00.zcyList[0].zyjs;
                    brzcList00.ypInfo.ypyf = brzcList00.zcyList[0].yysm;
                    brzcList00.ypInfo.zyzd = brzcList00.zcyList[0].zyzd;
                    brzcList00.ypInfo.zyzh = brzcList00.zcyList[0].zyzh;
                    brzcList00.ypInfo.zyzf = brzcList00.zcyList[0].zyzf;
                    brzcList00.ypInfo.zytt = brzcList00.zcyList[0].zytt;
                    brzcList00.ypInfo.fjmc = '中草药处方   ' + brzcList00.zcyList[0].zyjs + ' ' + brzcList00.zcyList[0].yysm;
                } else {
                    malert("中草药列表查询失败", 'top', 'defeadted');

                }
            });
        },
        //中草药保存
        save: function () {
            if ((Object.keys(lgdj.lgdjxx).length === 0) || lgdj.lgdjxx.zfbz == '1') {
                malert("病人无留观信息，无法执行此操作！", 'top', 'defeadted');
                return false;
            }
            if (brzcList00.sfxz) {
                if (brzcList00.zcyList.length <= 0) return;   //没有数据就直接返回
                if (!this.ifClick) return false
                this.ifClick = false
                var yzList = [];   //用于提交的医嘱信息List
                var ksrq = getTodayDateTime();
                //循环判断每条医嘱是否正常
                for (var i = 0; i < brzcList00.zcyList.length; i++) {
                    brzcList00.zcyList[i].ksrq = ksrq;
                    //医嘱编码为空的直接删除
                    if (brzcList00.zcyList[i].ryypbm == undefined || brzcList00.zcyList[i].ryypbm == null || brzcList00.zcyList[i].ryypbm == "") {
                        brzcList00.zcyList.splice(i, 1);  //直接删除
                        continue;
                    }
                    //医嘱类型判断
                    if (brzcList00.zcyList[i].yzlx == null || brzcList00.zcyList[i].yzlx == undefined || brzcList00.zcyList[i].yzlx == "") {
                        malert("第【" + (i + 1) + "】行【" + brzcList00.zcyList[i].ryypmc + "】的【医嘱类型】不能为空！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //医嘱分类
                    if (brzcList00.zcyList[i].yzfl == null || brzcList00.zcyList[i].yzfl == undefined || brzcList00.zcyList[i].yzfl == "") {
                        malert("第【" + (i + 1) + "】行【" + hzList.Yzxx_List[i].ryypmc + "】的【医嘱分类】不能为空！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //医嘱日期
                    if (brzcList00.zcyList[i].ksrq == null || brzcList00.zcyList[i].ksrq == undefined || brzcList00.zcyList[i].ksrq == "") {
                        malert("第【" + (i + 1) + "】行【" + brzcList00.zcyList[i].ryypmc + "】的【医嘱日期】不能为空！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //医嘱名称
                    if (brzcList00.zcyList[i].ryypmc == null || brzcList00.zcyList[i].ryypmc == undefined || brzcList00.zcyList[i].ryypmc == "") {
                        malert("第【" + (i + 1) + "】行【" + brzcList00.zcyList[i].ryypmc + "】的【医嘱日期】不能为空！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //药品还是诊疗
                    if (brzcList00.zcyList[i].lx == null || brzcList00.zcyList[i].lx == undefined || brzcList00.zcyList[i].lx == "") {
                        malert("第【" + (i + 1) + "】行【" + hzList.Yzxx_List[i].ryypmc + "】的【医嘱分类(药品，诊断)】不能为空！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //药品
                    //单次剂量
                    if (brzcList00.zcyList[i].dcjl == null || brzcList00.zcyList[i].dcjl == undefined || brzcList00.zcyList[i].dcjl <= 0) {
                        malert("第【" + (i + 1) + "】行【" + hzList.Yzxx_List[i].ryypmc + "】的【单次剂量】必需大于0！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //频次
                    if (brzcList00.zcyList[i].pcbm == null || brzcList00.zcyList[i].pcbm == undefined || brzcList00.zcyList[i].pcbm == "") {
                        malert("第【" + (i + 1) + "】行【" + brzcList00.zcyList[i].ryypmc + "】的【频次】不能为空！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }

                    //用药天数
                    if (brzcList00.zcyList[i].yyts == null || brzcList00.zcyList[i].yyts == undefined || brzcList00.zcyList[i].yyts <= 0) {
                        malert("第【" + (i + 1) + "】行【" + brzcList00.zcyList[i].ryypmc + "】的【用药天数】必需大于0！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //医嘱总量
                    if (brzcList00.zcyList[i].yyzl == null || brzcList00.zcyList[i].yyzl == undefined || brzcList00.zcyList[i].yyzl <= 0) {
                        malert("第【" + (i + 1) + "】行【" + brzcList00.zcyList[i].ryypmc + "】的【医嘱总量】必需大于0！", 'top', 'defeadted');
                        this.ifClick = true;
                        return false;
                    }
                    //处理各种标志
                    if (brzcList00.zcyList[i].lx == '药品') {
                        brzcList00.zcyList[i].ypbz = '1';
                    } else {
                        brzcList00.zcyList[i].ypbz = '0';
                    }
                    if (brzcList00.zcyList[i].yyffbm == '' || brzcList00.zcyList[i].yyffbm == null) {
                        brzcList00.zcyList[i].yyffbm = 999
                        brzcList00.zcyList[i].yyffmc = '中药'
                    }
                    brzcList00.zcyList[i].yfmc = panel.popContent.yfmc;

                    yzList.push(brzcList00.zcyList[i]);  //增加到保存医嘱json中
                }  //循环完
                if (yzList.length <= 0) {
                    this.ifClick = true;
                    malert("没有可保存的医嘱项目 ！", 'top', 'defeadted');
                    return false;
                }
                for (var i = 0; i < yzList.length; i++) {
                    if (yzList[i].tsyz) {
                        yzList[i].tsyz = '1';
                    } else {
                        yzList[i].tsyz = '0';
                    }
                    yzList[i].zyjs = brzcList00.ypInfo.ypjs;
                    yzList[i].yysm = brzcList00.ypInfo.ypyf;
                    yzList[i].yyzl = yzList[i].yyzl * brzcList00.ypInfo.ypjs;
                    yzList[i].zyzd = brzcList00.ypInfo.zyzd;
                    yzList[i].zyzh = brzcList00.ypInfo.zyzh;
                    yzList[i].zyzf = brzcList00.ypInfo.zyzf;
                    yzList[i].rqxgbz = "0";
                    yzList[i].cflxbm = this.popContent.cflxbm
                }
                var json = {
                    list: [
                        {
                            // hzxx: brzcList00.brlt,   //病人基本信息
                            hzxx: lgdj.lgdjxx,      //病人留观信息
                            yzxx: yzList,          //医嘱信息
                            lczd: []              //诊断信息（检查，检验的诊断）
                        }
                    ]
                };

                brzcList00.$http.post('/actionDispatcher.do?reqUrl=New1MjzglYzd&types=saveyzd', JSON.stringify(json)).then(function (data) {
                    if (data.body.a == 0) {
                        brzcList00.isShow = false;
                        hzList.Wf_selectYZ();  //查询医嘱
                        malert("医嘱保存成功 ！", 'top', 'success');
                        brzcList00.zcyList = [];
                        brzcList00.ypInfo = {};
                        brzcList00.ypInfo.yplx = "中草药处方";
                        brzcList00.ypInfo.ypjs = 1;
                        brzcList00.ypInfo.ypbz = "中草药处方";
                        brzcList00.ifClick = true;
                        brzcList00.numOne = 1;
                    } else {
                        malert("医嘱保存失败：" + data.body.c, 'top', 'defeadted');
                        brzcList00.ifClick = true;
                    }
                });
            } else {
                malert("已保存中药医嘱不允许修改", 'top', 'defeadted');
                brzcList00.ifClick = true;
            }
        },
        //提交
        confirms: function () {
            if (!this.ifClick) return;
            if (this.iszcy == true) {
                this.save();
            } else {
                this.getMessage();
                this.isChecked = [];
            }
        },
        //关闭
        closes: function () {
            this.numOne = 1;
            brzcList00.zcyList = [];
            brzcList00.ypInfo = {};
            brzcList00.ypInfo.yplx = "中草药处方";
            brzcList00.ypInfo.ypjs = 1;
            brzcList00.ypInfo.ypbz = "中草药处方";
        },
    },
});

//中药明细医嘱
var brzcList01 = new Vue({
    el: '#brzcList01',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        num: 1,
        sfcy: '0',
        jxMxList: [],
        jxList: [],
    },
    methods: {
        ghtjxData: function (yzxh, zyh) {
            this.param.sfcy = '1';
            this.param.yzxh = yzxh;
            this.param.ghxh = zyh;
            $.getJSON("/actionDispatcher.do?reqUrl=New1MjzglYzd&types=queryZyyzMx&parm=" + JSON.stringify(this.param), function (json) {
                if (json.d != null) {
                    brzcList01.jxMxList = json.d.list;
                }
            });
        },
        //草药医嘱复制提交
        confirms: function () {
            for (var i = 0; i < this.jxMxList.length; i++) {
                this.jxMxList[i].yzxh = null;
                this.jxMxList[i].mxxh = null;
                this.jxMxList[i].hstzbz = "0";
                this.jxMxList[i].hstzsj = null;
                this.jxMxList[i].shbz = null;
                this.jxMxList[i].shhs = null;
                this.jxMxList[i].shhsxm = null;
                this.jxMxList[i].shsj = null;
                this.jxMxList[i].sqdh = null;
                this.jxMxList[i].sqsyqz = null;
                this.jxMxList[i].sqsysj = null;
                this.jxMxList[i].sqysxm = null;
                this.jxMxList[i].tsyz = "0";
                this.jxMxList[i].tzhs = null;
                this.jxMxList[i].tzhsxm = null;
                this.jxMxList[i].tzys = null;
                this.jxMxList[i].tzysxm = null;
                this.jxMxList[i].xdys = null;
                this.jxMxList[i].xdysxm = null;
                this.jxMxList[i].ystzbz = null;
                this.jxMxList[i].ystzsj = null;
                this.jxMxList[i].ystzsm = null;
                this.jxMxList[i].zfbz = null;
                this.jxMxList[i].zfsj = null;
                this.jxMxList[i].zfys = null;
                this.jxMxList[i].zfysxm = null;
                this.jxMxList[i].zx_begin = null;
                this.jxMxList[i].zx_end = null;
                this.jxMxList[i].zxbz = null;
                this.jxMxList[i].zxhs = null;
                this.jxMxList[i].zxhsxm = null;
                this.jxMxList[i].zxsj = null;
                this.jxMxList[i].zxts = null;
                this.jxMxList[i].zyh = null;
            }
            brzcList00.zcyList = this.jxMxList;
            brzcList.num = 1
            this.num = 1
        }
    },
})

//检查检验弹框
var pop = new Vue({
    el: '#pop',
    mixins: [dic_transform, baseFunc, tableBase],
    data: {
        isRow: null,        //当前医嘱行号
        jcjybz: '1',        //检查检验标志  1-检查  2-检验
        jcbz: false,        //检查标志
        jybz: false,        //检验标志
        isShow: false,
        lczdJson: {},
        ifClick: true,
        jybbList: [],
    },
    methods: {
        //获取检验标本
        getJybb: function () {
            var param = {
                parm: '',
                rows: 1000,
                page: 1,
            };
            $.getJSON("/actionDispatcher.do?reqUrl=XtwhJyxm&types=jyxmYbSelect&param=" + JSON.stringify(param), function (json) {
                console.log(json);
                if (json.a == "0") {
                    pop.jybbList = json.d.list;
                }
            });
        },

        //检验标本改变事件
        jybbChange: function (val) {
            Vue.set(this.lczdJson, 'jybbbm', val[0]);
            Vue.set(this.lczdJson, 'jybb', val[4]);
            $("#pop_bbsm").focus();
        },

        //打开
        Wf_open: function (jcfl, index) {
            //检查或检验
            // pop.isShow = true;
            if (jcfl == '1' || jcfl == '2') {
                if (index != undefined) {
                    //显示诊断窗口
                    pop.isRow = index;      //设置当前行号
                    pop.jcjybz = hzList.Yzxx_List[index].jcfl;  //设置检查分类
                    if (jcfl == '1') {      //检查
                        pop.jcbz = true;    //检查标志
                        pop.jybz = false;   //检验标志
                        if (hzList.Yzxx_List[pop.isRow].lczd != null && hzList.Yzxx_List[pop.isRow].lczd != undefined && hzList.Yzxx_List[pop.isRow].lczd != "") {
                            pop.lczdJson.lczd = hzList.Yzxx_List[pop.isRow].lczd;     //临床诊断
                            pop.lczdJson.jcms = hzList.Yzxx_List[pop.isRow].jcms;     //检查描述
                            pop.lczdJson.lczz = hzList.Yzxx_List[pop.isRow].lczz;     //临床症状
                            pop.lczdJson.jcbw = hzList.Yzxx_List[pop.isRow].jcbw;     //检查部位
                        } else {
                            pop.lczdJson.lczd = userNameBg.Brxx_List.ryzdmc;     //临床诊断
                        }
                    } else {                //检验
                        pop.jcbz = false;   //检查标志
                        pop.jybz = true;    //检验标志
                        if (hzList.Yzxx_List[pop.isRow].lczd != null && hzList.Yzxx_List[pop.isRow].lczd != undefined && hzList.Yzxx_List[pop.isRow].lczd != "") {
                            pop.lczdJson.lczd = hzList.Yzxx_List[pop.isRow].lczd;     //临床诊断
                            pop.lczdJson.jybb = hzList.Yzxx_List[pop.isRow].jybb;     //检验标本
                            pop.lczdJson.bbsm = hzList.Yzxx_List[pop.isRow].bbsm;     //标本说明
                            pop.lczdJson.jymd = hzList.Yzxx_List[pop.isRow].jymd;     //检验目的
                        } else {
                            pop.lczdJson.lczd = userNameBg.Brxx_List.ryzdmc;     //临床诊断
                        }
                    }
                    setTimeout(function () {
                        $("#pop_lczd").focus();
                    }, 300);
                } else {
                    $("#pcbm_" + index).focus();  //诊疗焦点就跳到频次编码
                }
            } else {

            }
        },
        //保存
        Wf_save: function () {
            if (!pop.ifClick) return; //如果为false表示已经点击了不能再点
            pop.ifClick = false;
            if (pop.jcjybz == '1') {
                if (pop.lczdJson.lczd == null || pop.lczdJson.lczd == undefined || pop.lczdJson.lczd == "") {
                    malert("临床诊断不能为空哟!", 'top', 'defeadted');
                    pop.ifClick = true;
                    return false;
                }
                if (pop.lczdJson.jcms == null || pop.lczdJson.jcms == undefined || pop.lczdJson.jcms == "") {
                    malert("检查描述不能为空哟!", 'top', 'defeadted');
                    pop.ifClick = true;
                    return false;
                }
                if (pop.lczdJson.lczz == null || pop.lczdJson.lczz == undefined || pop.lczdJson.lczz == "") {
                    malert("临床症状不能为空哟!", 'top', 'defeadted');
                    pop.ifClick = true;
                    return false;
                }
                if (pop.lczdJson.jcbw == null || pop.lczdJson.jcbw == undefined || pop.lczdJson.jcbw == "") {
                    malert("检查部位不能为空哟!", 'top', 'defeadted');
                    pop.ifClick = true;
                    return false;
                }
            }
            if (pop.jcjybz == '1') {   //检查
                hzList.Yzxx_List[pop.isRow].lczd = pop.lczdJson.lczd;   //临床诊断
                hzList.Yzxx_List[pop.isRow].jcms = pop.lczdJson.jcms;   //检查描述
                hzList.Yzxx_List[pop.isRow].lczz = pop.lczdJson.lczz;   //临床症状
                hzList.Yzxx_List[pop.isRow].jcbw = pop.lczdJson.jcbw;   //检查部位
                hzList.Yzxx_List[pop.isRow].jybb = "";   //检验标本
                hzList.Yzxx_List[pop.isRow].bbsm = "";   //标本说明
                hzList.Yzxx_List[pop.isRow].jymd = "";   //检验目的
            } else {  //检验
                hzList.Yzxx_List[pop.isRow].lczd = pop.lczdJson.lczd;   //临床诊断
                hzList.Yzxx_List[pop.isRow].jcms = "";   //检查描述
                hzList.Yzxx_List[pop.isRow].lczz = "";   //临床症状
                hzList.Yzxx_List[pop.isRow].jcbw = "";   //检查部位
                hzList.Yzxx_List[pop.isRow].jybb = pop.lczdJson.jybb;   //检验标本
                hzList.Yzxx_List[pop.isRow].bbsm = pop.lczdJson.bbsm;   //标本说明
                hzList.Yzxx_List[pop.isRow].jymd = pop.lczdJson.jymd;   //检验目的
            }
            $("#pcmc_" + pop.isRow).focus();    //诊疗焦点就跳到频次编码
            pop.ifClick = true;
            pop.isShow = false;
            pop.lczdJson = {};
        },

        //回车事件
        //  Wf_KeyEnter: function (type) {
        //      switch (type) {
        //          case "lczd" :
        //              if (pop.jcjybz == '1') {
        //                  $("#pop_jcms").focus();
        //              } else {
        //                  $("#pop_jybb").focus();
        //              }
        //              break;
        //          case "jcms" :
        //              $("#pop_lczz").focus();
        //              break;
        //          case "lczz" :
        //              if (pop.jcjybz == '1') {
        //                  $("#pop_jcbw").focus();
        //              } else {
        //                  $("#pop_jybb").focus();
        //              }
        //              break;
        //          case "jcbw" :
        //              pop.Wf_save();  //保存
        //              break;
        //          case "bbsm" :
        //              $("#pop_jymd").focus();
        //              break;
        //          case "jymd" :
        //              pop.Wf_save();  //保存
        //              break;
        //      }
        //  }
    }
});

//***************************************展示医嘱单start
var toolMenu_yzd = new Vue({
    el: '.toolMenu_yzd',
    data: {
        which: 0,
        pageList: [],
        pageH: 860,

    },
    methods: {
        long: function (num) {
            printGd = 20;
            this.which = num;
            cqyzd.which = num;
            lsyzd.isShow = false;
            cqyzd.isShow = true;
            cqyzd.getData();
        },
        short: function (num) {
            this.which = num;
            cqyzd.which = num;
            cqyzd.isShow = false;
            lsyzd.isShow = true;
            lsyzd.getData();
        },
    }
});
var cqyzd = new Vue({
    el: '.cqyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        list: [],
        jsonList: [],
        isShow: false,
        isGoOn: false,
        param: {},
        BrxxJson: [],
        isGoPrint: false,
        which: 0,
        pageList: [],
        pageH: 790,
    },
    methods: {
        doPrint: function (isGoOn) {
            $('.no-print').html('')
            var cqTr;
            cqyzd.isGoOn = isGoOn
            cqPrint.list = [];
            lsPrint.list = [];
            if (this.which == 0) {
                cqTr = $(".cqyzd tr");
            } else {
                cqTr = $(".lsyzd tr");
            }
            var _height = 0;
            var a = 0, b = -1;
            for (var i = 1; i < cqTr.length - 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height >= cqyzd.pageH) {
                    b++;
                    var as = [];
                    for (var f = a; f < i; f++) {
                        if (this.which == 0) {
                            as.push(cqyzd.jsonList[f]);
                        } else {
                            as.push(lsyzd.jsonList[f]);
                        }
                    }
                    if (this.which == 0) cqPrint.list[b] = as;
                    else lsPrint.list[b] = as;
                    a = i;
                    _height = 0;
                    this.pageList.push(as.length);
                }
            }
            var pp = [];
            if (this.which == 0) {
                for (var p = a; p < cqyzd.jsonList.length; p++) {
                    pp.push(cqyzd.jsonList[p]);
                }
            } else {
                for (var ls = a; ls < lsyzd.jsonList.length; ls++) pp.push(lsyzd.jsonList[ls]);
            }
            for (var l = 0; l < 21; l++) {
                _height += 40;
                if (_height >= cqyzd.pageH) {
                    break;
                }
                pp.push({'psjg': '无'});
            }
            if (this.which == 0) {
                cqPrint.list[b + 1] = pp;
                cqPrint.isShow = true;
                lsPrint.isShow = false;
            } else {
                lsPrint.list[b + 1] = pp;
                lsPrint.isShow = true;
                cqPrint.isShow = false;
            }
            cqyzd.showTable(cqyzd.which);
            if (isGoOn) {
                cqPrint.isGoPrint = true;
                lsPrint.isGoPrint = true;
                setTimeout(function () {
                    cqyzd.hideTable(cqyzd.which);
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '1px solid transparent');
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '1px solid transparent');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '1px solid transparent');
                }, 50);
                setTimeout(function () {
                    window.print();
                    cqPrint.isGoPrint = false;
                    lsPrint.isGoPrint = false;
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("td").css('border-left', '1px solid #999');
                    $('.cqPrint table').eq(cqPrint.pagePrint).find("tr").css('border', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("td").css('border-left', '1px solid #999');
                    $('.lsPrint table').eq(lsPrint.pagePrint).find("tr").css('border', '1px solid #999');
                }, 100);
            } else {
                setTimeout(function () {
                    window.print();
                    printGd = 20
                    cqPrint.isShow = false;
                    lsPrint.isShow = false;
                }, 100);
            }
        },
        hideTable: function (type) {
            var num = 0;
            if (type == 0 && cqyzd.isChecked == cqPrint.isChecked) {
                for (var i = 0; i < cqPrint.pagePrint; i++) {
                    $('.cqPrint .popCenter').eq(i).hide();
                    num += this.pageList[i];
                }
                cqPrint.isChecked = cqPrint.isChecked - num;
                cqPrint.$forceUpdate()
            } else if (type == 0) {
                cqPrint.isChecked = cqyzd.isChecked
                this.hideTable(cqyzd.which)
            }
            if (type == 1 && lsyzd.isChecked == lsPrint.isChecked) {
                for (var j = 0; j < lsPrint.pagePrint; j++) {
                    $('.lsPrint .popCenter').eq(j).hide();
                    num += this.pageList[j];
                }
                lsPrint.isChecked = lsPrint.isChecked - num;
                lsPrint.$forceUpdate()
            } else if (type == 1) {
                lsPrint.isChecked = lsyzd.isChecked
                this.hideTable(cqyzd.which)

            }
        },
        showTable: function (type) {
            if (type == 0) {
                for (var i = 0; i < $('.cqPrint .popCenter').length; i++) {
                    $('.cqPrint .popCenter').eq(i).show();
                }
                cqPrint.$forceUpdate()
            } else {
                for (var j = 0; j < $('.lsPrint .popCenter').length; j++) {
                    $('.lsPrint .popCenter').eq(j).show();
                }
            }
        },
        goPrint: function (index) {
            cqyzd.isChecked = index;
            cqPrint.isChecked = index;
            var cqTr = $(".cqyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > 720) {
                    b++;
                    _height = 0;
                }
            }
            cqPrint.pagePrint = b;
        },
        getData: function () {
            var res = JSON.parse(JSON.stringify(userNameBg.Brxx_List));
            cqyzd.BrxxJson = [];
            cqPrint.BrxxJson = [];
            cqyzd.BrxxJson = res;
            cqPrint.BrxxJson = res;
            if (userNameBg.Brxx_List.ghxh == null || userNameBg.Brxx_List.ghxh == '' || userNameBg.Brxx_List.ghxh == undefined) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }

            this.param = {
                page: 1,
                rows: 10000,
                sort: '',
                order: 'asc',
                ghxh: userNameBg.Brxx_List.ghxh,
                yzlx: '1',
                zfbz:'0',
                zxbz:'0',

            };
			let that = this;
            console.log("ksbm:" + userNameBg.qxks);
            $.getJSON('/actionDispatcher.do?reqUrl=New1MjzglYzd&types=queryYsYzd'+ '&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0'  ) {
					if(json.d && json.d.list.length>0){
						let yzlist = json.d.list[0].yzxx;
						yzlist.sort(that.compare)
						cqyzd.jsonList = json.d.list[0].yzxx;
					}else{
						cqyzd.jsonList = [];
					}
                } else {
                    malert("病人医嘱单信息查询失败！", 'top', 'defeadted');
                }
            });
        },
		compare:function(v1,v2){
			if(v1.ksrq<v2.ksrq){
				return -1;
			}else if(v1.ksrq>v2.ksrq){
				return 1;
			}else{
				return 0;
			}
		},
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal = null, nextVal = null;
            if (index != this.jsonList.length - 1 && index != 0) {
                prvVal = this.jsonList[index - 1][name];
                nextVal = this.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            var reDate = new Date(val);
            if (type == 'ry') {
                return this.Appendzero((reDate.getMonth() + 1)) + '-' + this.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return this.Appendzero(reDate.getHours()) + ':' + this.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }

            }
            if (index == this.jsonList.length - 1) {
                return 'end'
            }
            return 'null'
        },
        isShowItem: function (index) {
            if (this.jsonList[index + 1] == null) {
                return true;
            }
            if (this.jsonList[index]['fzh'] == this.jsonList[index + 1]['fzh'] && this.jsonList[index]['fzh'] != 0) {
                if (this.jsonList[index]['yyffmc'] == this.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var cqPrint = new Vue({
    el: '.cqPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        pagePrint: 0,
        BrxxJson: cqyzd.BrxxJson,
        isGoPrint: false,
        printGd: 20,
    },
    filters: {
        compuGd: function (index) {
            if (!cqyzd.isGoOn) {
                if (index >= 1) {
                    return 'paddingTop:30px'
                }
            } else {
                if (index >= cqPrint.pagePrint + 1) {
                    return 'paddingTop:30px'
                }
            }

        },
    },
    methods: {
        cssFun:function(index){
            if(this.isPrint){
                if(index<cqPrint.pagePrint + 1){
                    return true
                }{
                    return false
                }
            }
        },
        print: function () {
            window.print();
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var val = cqyzd.jsonList[index][name];
            var prvVal = cqyzd, nextVal = null;
            if (index != cqyzd.jsonList.length - 1 && index != 0) {
                prvVal = cqyzd.jsonList[index - 1][name];
                nextVal = cqyzd.jsonList[index + 1][name]
            }
            if (val == null || val == '') return '';
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqPrint.Appendzero((reDate.getMonth() + 1)) + '-' + cqPrint.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqPrint.Appendzero(reDate.getHours()) + ':' + cqPrint.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            var fzh = cqyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == cqyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != cqyzd.jsonList.length - 1) {
                var nextFzh = cqyzd.jsonList[index + 1]['fzh'];
                var prvFzh = cqyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }

            }
            if (index == cqyzd.jsonList.length - 1) {
                return 'end'
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= cqyzd.jsonList.length) return null;
            if (cqyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (cqyzd.jsonList[index]['fzh'] == cqyzd.jsonList[index + 1]['fzh'] && cqyzd.jsonList[index]['fzh'] != 0) {
                if (cqyzd.jsonList[index]['yyffmc'] == cqyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var lsyzd = new Vue({
    el: '.lsyzd',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        jsonList: [],
        isShow: false,
        param: {},
        BrxxJson: [],
        isGoPrint: false,
    },
    methods: {
        goPrint: function (index) {
            lsyzd.isChecked = index;
            lsPrint.isChecked = index;
            var cqTr = $(".lsyzd tr");
            var _height = 0;
            var b = 0;
            for (var i = 2; i < index + 2; i++) {
                _height += cqTr.eq(i).outerHeight();
                if (_height > cqyzd.pageH) {
                    b++;
                    _height = 0;
                }
            }
            lsPrint.pagePrint = b;
        },
        getData: function () {
            var res = JSON.parse(JSON.stringify(userNameBg.Brxx_List));
            lsyzd.BrxxJson = [];
            lsPrint.BrxxJson = [];
            lsyzd.BrxxJson = res;
            lsPrint.BrxxJson = res;
            if (userNameBg.Brxx_List.ghxh == null || userNameBg.Brxx_List.ghxh == '' || userNameBg.Brxx_List.ghxh == undefined) {
                malert("请选择病人后再查看医嘱单！", 'top', 'defeadted');
                return
            }

            this.param = {
                page: 1,
                rows: 10,
                sort: '',
                order: 'asc',
                ghxh: userNameBg.Brxx_List.ghxh,
                yzlx: '0',
                zfbz:'0',
                shbz:'1'
            };
            console.log("ksbm：" + userNameBg.qxks);
			var that = this;
            $.getJSON('/actionDispatcher.do?reqUrl=New1MjzglYzd&types=queryYsYzd'+ '&parm=' + JSON.stringify(this.param), function (json) {
                if (json.a == '0') {
					let yzlist = json.d.list[0].yzxx;
					let min ,temp;
					for(let i =0;i<yzlist.length;i++){
						min = i;
						for(let j =i+1;j<yzlist.length;j++){
							if(yzlist[j].ksrq<yzlist[min].ksrq){
								min = j;
							}
						}
						temp = yzlist[i]
						yzlist[i]=yzlist[min];
						yzlist[min] = temp;
					}
                    lsyzd.jsonList = yzlist;
					that.$forceUpdate();
                } else {
                    malert("查询临时医嘱失败！", 'top', 'defeadted');
                }
            });
        },
		compare:function(v1,v2){
			if(v1.ksrq<v2.ksrq){
				return -1;
			}else if(v1.ksrq>v2.ksrq){
				return 1;
			}else{
				return 0;
			}
		},
        sameDate: function (name, index, type) {
            var val = this.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = this.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            
            var reDate = new Date(val);
            if (type == 'ry') {
                return cqyzd.Appendzero((reDate.getMonth() + 1)) + '-' + cqyzd.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return cqyzd.Appendzero(reDate.getHours()) + ':' + cqyzd.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index) {
            var fzh = this.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == this.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != this.jsonList.length - 1) {
                var nextFzh = this.jsonList[index + 1]['fzh'];
                var prvFzh = this.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index) {
            if (this.jsonList[index + 1] == null) {
                return true;
            }
            if (this.jsonList[index]['fzh'] == this.jsonList[index + 1]['fzh']) {
                if (this.jsonList[index]['yyffmc'] == this.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});

var lsPrint = new Vue({
    el: '.lsPrint',
    mixins: [tableBase, baseFunc, mformat],
    data: {
        isShow: false,
        list: [],
        itemList: [],
        pagePrint: null,
        BrxxJson: lsyzd.BrxxJson,
        isGoPrint: false
    },
    filters: {
        compuGd: function (index) {
            if (!cqyzd.isGoOn) {
                if (index >= 1) {
                    return 'paddingTop:30px'
                }
            } else {
                if (index >= lsPrint.pagePrint + 1) {
                    return 'paddingTop:30px'
                }
            }

        },
    },
    methods: {
        cssFun:function(index){
            if(this.isPrint){
                if(index<cqPrint.pagePrint + 1){
                    return true
                }{
                    return false
                }
            }
        },
        print: function () {
            window.print();
        },
        Appendzero: function (obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        },
        toIndex: function (index, num) {
            for (var i = 0; i < num; i++) {
                index += this.list[i].length;
            }
            return index;
        },
        sameDate: function (name, index, num, type) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            var val = lsyzd.jsonList[index][name];
            var prvVal;
            if (index != 0) prvVal = lsyzd.jsonList[index - 1][name];
            if (val == null || val == '') {
                return '';
            }
            var reDate = new Date(val);
            if (type == 'ry') {
                return this.Appendzero((reDate.getMonth() + 1)) + '-' + this.Appendzero(reDate.getDate());
            } else if (type == 'sj') {
                return this.Appendzero(reDate.getHours()) + ':' + this.Appendzero(reDate.getMinutes());
            }
        },
        sameSE: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) {
                return null;
            }
            var fzh = lsyzd.jsonList[index]['fzh'];
            if (fzh == 0) return false;
            if (index == 0 && fzh == lsyzd.jsonList[index + 1]['fzh']) {
                return 'start';
            }
            if (index != 0 && index != lsyzd.jsonList.length - 1) {
                var nextFzh = lsyzd.jsonList[index + 1]['fzh'];
                var prvFzh = lsyzd.jsonList[index - 1]['fzh'];
                if (fzh == null || fzh != nextFzh && fzh != prvFzh) {
                    return 'null';
                }
                if (fzh == nextFzh && fzh != prvFzh) {
                    return 'start';
                }
                if (fzh != nextFzh && fzh == prvFzh) {
                    return 'end';
                }
                if (fzh == nextFzh && fzh == prvFzh) {
                    return 'all';
                }
            }
            return 'null'
        },
        isShowItem: function (index, num) {
            index = this.toIndex(index, num);
            if (index >= lsyzd.jsonList.length) return null;
            if (lsyzd.jsonList[index + 1] == null) {
                return true;
            }
            if (lsyzd.jsonList[index]['fzh'] == lsyzd.jsonList[index + 1]['fzh']) {
                if (lsyzd.jsonList[index]['yyffmc'] == lsyzd.jsonList[index + 1]['yyffmc']) {
                    return false;
                }
            }
            return true;
        }
    }
});
cqyzd.getData();
pop.getJybb();


var a = 0;

function xyz() {
    if (a == 0) {
        hzList.Yzxx_List = [];
        a = 1
    }
    brzcList00.numOne = 0;
    laydate.render({
        elem: '.ksrq'
        , trigger: 'click'
        , theme: '#1ab394'
        , done: function (value, data) {
            brzcList00.popContent.ksrq = value
        }
    });
}

var check = '', copy = [];
document.onkeydown = function (event) {
    if (event.ctrlKey && event.keyCode === 65) {
        console.log('ctrl + a');
        hzList.checkAll = true;
        hzList.isChecked = [];
        for (var init in hzList.Yzxx_List) {
            Vue.set(hzList.isChecked, parseInt(init), true)
        }
    }
    if (event.ctrlKey && event.keyCode === 67) {
        console.log('ctrl + c');
        if (hzList.isChecked.length > 0) {
            for (var i = 0; i < hzList.isChecked.length; i++) {
                if (hzList.isChecked[i] == true) copy.push(JSON.parse(JSON.stringify(hzList.Yzxx_List[i])))
            }
            check = copy;
            copy = []
        }
    }
    if (event.ctrlKey && event.keyCode === 86) {
        console.log('ctrl + v');
        if (check != '') {
            hzList.Yzxx_List = hzList.Yzxx_List.concat(JSON.parse(JSON.stringify(check)))
        }
    }
    if (event.keyCode == 115 && event.code == 'F4') {
        panel.qhCode = panel.qhCode == '0' ? '1' : panel.qhCode == '1' ? '2' : panel.qhCode == '2' ? '0' : ''
        //hzList.Wf_selectYZ()
    }
};

//针对下拉table
$('body').click(function () {
    $(".selectGroup").hide();
});

$(".selectGroup").click(function (e) {
    hzList.searchCon = [];
    e.stopPropagation();
});

var toolH = 0;


// 检查检验
var jcjyfy = new Vue({
    el: '#jcjyfy',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifShow: false,
        title: '1检查/检验费用',
        jcjydList: {},
        mbbm: null,
        zhList: [],
        fymxList: [],
        djxxContent: {},
        userName: '', //登录的人得名字
    },
    computed: {
        brInfo: function () {
            return userNameBg.Brxx_List;
        }
    },
    watch: {
        "jcjyd": function (newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getZhList(newVal);
            }
        }
    },
    mounted: function () {
        this.userName = sessionStorage.getItem("userName" + userId);
    },
    updated: function () {
        changeWin();
    },
    methods: {
        print: function () {
            printJcjyfy.print();
        },
        resultChangeMb: function (val) {
            if (val[2].length > 1) {
                if (Array.isArray(this[val[2][0]])) {
                    Vue.set(this[val[2][0]][val[2][1]], val[2][val[2].length - 1], val[0]);
                    jcjyfy.getDjContent(val[0]);
                } else {
                    Vue.set(this[val[2][0]], val[2][val[2].length - 1], val[0]);
                    if (val[3] != null) {
                        Vue.set(this[val[2][0]], val[3], val[4]);
                    }
                    jcjyfy.getDjContent(val[0]);
                }
            } else {
                this[val[2][0]] = val[0];
                jcjyfy.getDjContent(val[0]);
            }
            if (val[1] != null) {
                this.nextFocus(val[1]);
            }
        },
        show: function () {
            this.ifShow = true;
        },
        closes: function () {
            this.ifShow = false;
        },
        //获取单据模板下拉框
        getJcjyd: function () {
            var parm = {
                page: 1,
                rows: 20000,
                mzorzy: '1',
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymb&parm=' + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list.length > 0) {
                        jcjyfy.jcjydList = data.d.list;
                        jcjyfy.mbbm = jcjyfy.jcjydList[0].mbbm;
                        jcjyfy.djxxContent = jcjyfy.jcjydList[0];
                        jcjyfy.getDjMx(jcjyfy.mbbm);
                    }
                } else {
                    malert('获取列表失败', 'top', 'defeadted');
                }
            });
        },

        getDjContent: function (mbbm) {
            var parm = {
                page: 1,
                rows: 20000,
                mzorzy: '0',
                mbbm: mbbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymb&parm=' + JSON.stringify(parm),
                function (json) {
                    if (json.a == 0) {
                        if (json.d.list.length > 0) {
                            jcjyfy.mbbm = json.d.list[0].mbbm;
                            jcjyfy.djxxContent = json.d.list[0];
                            jcjyfy.getDjMx(jcjyfy.mbbm);
                        }
                    } else {
                        malert('获取列表失败', 'top', 'defeadted');
                    }
                });
        },

        //获取模板内容
        getDjMx: function (mbbm) {
            var parm = {
                page: 1,
                rows: 20000,
                mbbm: mbbm,
            };
            $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymbxm&parm=' + JSON.stringify(parm), function (data) {
                if (data.a == 0) {
                    if (data.d.list.length > 0) {
                        jcjyfy.djxxContent.zhList = data.d.list;
                        var parm = {
                            page: 1,
                            rows: 20000,
                            mbbm: jcjyfy.mbbm,
                        };
                        $.getJSON('/actionDispatcher.do?reqUrl=New1JcjymbXgcl&types=selectJcjymbmxxm&parm=' + JSON.stringify(parm), function (data) {
                            if (data.a == 0) {
                                if (data.d.list.length > 0) {
                                    for (var i = 0; i < jcjyfy.djxxContent.zhList.length; i++) {
                                        var mxlist = [];
                                        for (var j = 0; j < data.d.list.length; j++) {
                                            if (data.d.list[j].mbxmbm == jcjyfy.djxxContent.zhList[i].mbxmbm) {
                                                mxlist.push(data.d.list[j]);
                                            }
                                        }
                                        jcjyfy.djxxContent.zhList[i].mxList = mxlist;
                                    }
                                    jcjyfy.djxxContent = Object.assign({}, jcjyfy.djxxContent);
                                    console.log("这个是你最后得到的，帅得一批的东西！");
                                    console.log(jcjyfy.djxxContent);
                                }
                            } else {
                                malert('获取列表失败', 'top', 'defeadted');
                            }
                        });
                    }
                } else {
                    malert('获取列表失败', 'top', 'defeadted');
                }
            });
        },

        dbladd: function () {
            for (var i = 0; i < jcjyfy.fymxList.length; i++) {
                this.isChecked.push(true);
            }
            jcjyfy.add();
        },
        //添加
        add: function () {
            var realList = [];
            for (var i = 0; i < jcjyfy.djxxContent.zhList.length; i++) {
                for (var j = 0; j < jcjyfy.djxxContent.zhList[i].mxList.length; j++) {
                    if (jcjyfy.djxxContent.zhList[i].mxList[j].czbw == '0') {
                        if (jcjyfy.djxxContent.zhList[i].mxList[j].fymx) {
                            realList.push(jcjyfy.djxxContent.zhList[i].mxList[j]);
                        }
                    } else {
                        var mxxmmc = jcjyfy.djxxContent.zhList[i].mxList[j].mxxmmc;
                        if (jcjyfy.djxxContent.zhList[i].mxList[j].bwLeft) {
                            var obj = JSON.parse(JSON.stringify(jcjyfy.djxxContent.zhList[i].mxList[j]));
                            obj.mxxmmc = obj.mxxmmc + '(左)';
                            realList.push(obj);
                        }
                        if (jcjyfy.djxxContent.zhList[i].mxList[j].bwRight) {
                            var obj = JSON.parse(JSON.stringify(jcjyfy.djxxContent.zhList[i].mxList[j]));
                            obj.mxxmmc = obj.mxxmmc + '(右)';
                            realList.push(obj);
                        }
                    }
                }
            }
            for (var i = 0; i < realList.length; i++) {
                var xzxm = {};
                xzxm.cklj = 0;
                xzxm.fzh = "";
                xzxm.insertBz = true;
                xzxm.jbjl = 1;
                xzxm.kcfbz = '0';
                xzxm.ksrq = getTodayDateTime();
                xzxm.readonly = false;
                xzxm.sfcy = '0';
                xzxm.yyzl = realList[i].xmsl;
                //                    xzxm.tsyz = '0';
                xzxm.updateBz = false;
                xzxm.ryypbm = realList[i].mxxmbm;
                xzxm.ryypmc = realList[i].mxxmmc;
                xzxm.mbxmbm = realList[i].mbxmbm;
                xzxm.xssx = hzList.Yzxx_List.length;
                xzxm.yfbm = panel.popContent.yfbm;
                xzxm.yfmc = panel.popContent.yfmc;
                xzxm.ypbz = '0';
                xzxm.ysqmks = userNameBg.Brxx_List.ryks;
                xzxm.ysqmksmc = userNameBg.Brxx_List.ryksmc;
                xzxm.yyts = 1;
                xzxm.yzfl = '0'
                xzxm.yzlx = '0';
                xzxm.zdjl = 100;
                xzxm.zlbz = true;
                xzxm.zt = '4';
                xzxm.zyh = userNameBg.Brxx_List.ghxh;
                xzxm.jcfl = realList[i].jclx;
                /*if (realList[i].ffylb != null) {
                    if (realList[i].ffylb.indexOf("30") >= 0) {
                        xzxm.jcfl = '1'
                    } else if (realList[i].ffylb == '4') {
                        xzxm.jcfl = '2'
                    } else {
                        xzxm.jcfl = '0'
                    }
                } else {
                    xzxm.jcfl = '0'
                }*/
                xzxm.jcms = jcjyfy.djxxContent.jcms;
                xzxm.lczd = jcjyfy.djxxContent.lczd;
                xzxm.lczz = jcjyfy.djxxContent.lczz;
                xzxm.jcbw = jcjyfy.djxxContent.jcbw;
                xzxm.jybb = jcjyfy.djxxContent.jybb;
                xzxm.bbsm = jcjyfy.djxxContent.bbsm;
                xzxm.jymd = jcjyfy.djxxContent.jymd;
                xzxm.yysm = jcjyfy.djxxContent.wxts;
                xzxm.lx = '诊疗';
                xzxm.ksbm = userNameBg.Brxx_List.ryks;
                hzList.Yzxx_List.push(JSON.parse(JSON.stringify(xzxm)));

            }
            this.$nextTick(function () {
                hzList.Wf_saveYZ();
                hzList.$refs.body.scrollTop = hzList.$refs.body.scrollHeight
            })
        },
    },
});

var printJcjyfy = new Vue({
    el: '#print-jcjyfy',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        ifShow: false,
        brInfo: [],
        userName: {},
        djxxContent: []
    },
    methods: {
        print: function () {
            this.ifShow = true;
            this.brInfo = userNameBg.Brxx_List;
            this.userName = jcjyfy.userName;
            this.djxxContent = jcjyfy.djxxContent;
            var _no_print = $(".no-print-jcjyfy");
            _no_print.addClass("no");
            // $('.header-item').css({
            //     "padding-top": "0px"
            // });
            this.$forceUpdate();
            this.$nextTick(function () {
                window.print();
                this.ifShow = false;
                _no_print.removeClass("no");
                // $('.header-item').css({
                //     "padding-top": "125.58px"
                // });
            });
        },
    }
});
