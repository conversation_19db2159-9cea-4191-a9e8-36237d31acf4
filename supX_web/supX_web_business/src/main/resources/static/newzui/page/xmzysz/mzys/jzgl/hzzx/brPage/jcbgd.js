var jcbgd = new Vue({
    el: "#jcbgd",
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    data: {
        djList: []
    },
    mounted: function () {
        this.getData();
    },
    methods: {
        getData: function () {
            var pram = {
                INPATIENTEO: userNameBg.Brxx_List.ghxh
            },
                _url = "/actionDispatcher.do?reqUrl=YzPacsNew&types=yzpacs_queryReport&parm=" + JSON.stringify(pram);
            $.getJSON(_url, function (data) {
                if (data.d) {

                }
            });
        }
    }
});
