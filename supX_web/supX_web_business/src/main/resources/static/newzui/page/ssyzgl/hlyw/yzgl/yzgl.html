<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>医嘱管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link rel="stylesheet" href="/newzui/currentCSS/css/main.css" />
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <link rel="stylesheet" href="/FR/ReportServer?op=emb&resource=finereport.css">
    <link rel="stylesheet" href="yzgl.css">
<!--    <script language="javascript" src="/newzui/pub/js/LodopFuncs.js"></script>-->
    <style type="text/css">
        .N03004200243-sameStart{
            position: absolute;
            height: 50%;
            left: 2%;
            bottom: 0px;
            width: 10px !important;
            border-top: 1px solid rgb(0, 0, 0);
            border-left: 1px solid rgb(0, 0, 0);
            border-right: 0px;
            border-bottom: 0px;
        }
        .N03004200243-sameEnd {
            position: absolute;
            height: 50%;
            left: 2%;
            top: 0px;
            width: 10px !important;
            border-top: 0px;
            border-left: 1px solid rgb(0, 0, 0);
            border-right: 0px;
            border-bottom: 1px solid rgb(0, 0, 0);
        }
        .N03004200243-same {
            position: absolute;
            height: 100%;
            left: 2%;
            top: 0px;
            width: 10px !important;
            border-top: 0px;
            border-left: 1px solid rgb(0, 0, 0);
            border-right: 0px;
            border-bottom: 0px;
        }
        .N0300420024301 {
            height: 50%;
            width: 100%;
            top: 0%;
            position:  relative;
            padding-left: 4%;
            float: left;
        }
        .N0300420024302 {
            height: 50%;
            width: 100%;
            position: relative;
            padding-left: 4%;
            float: left;
        }
        .N0300420024303 {
            height: 100%;
            width: 100%;
            top: 0%;
            position:  relative;
            padding-left: 4%;
            font-size: 13px;
            white-space:normal;
        }
        .N03004200243-arrow-line{
            position: absolute;
            height: 100%;
            left: 50%;
            bottom: 0px;
            /*width: 10px !important;*/
            border-left: 1px solid black;
            border-right: 0px;
            border-bottom: 0px;
        }
        .N03004200243-arrow-head{
            border: 1px solid black;
            border-top: 0px;
            border-left: 0px;
            transform: rotate(45deg);
            width: 7px;
            height: 7px;
            left: 43%;
            position: absolute;
            top: 79%;
        }
        .N03004200243-arrow-head-ls-ksrq{
            border: 1px solid black;
            border-top: 0px;
            border-left: 0px;
            transform: rotate(45deg);
            width: 7px;
            height: 7px;
            left: 46%;
            position: absolute;
            top: 79%;
        }
        .N03004200243-arrow-head-lszx{
            border: 1px solid black;
            border-top: 0px;
            border-left: 0px;
            transform: rotate(45deg);
            width: 7px;
            height: 7px;
            left: 48%;
            position: absolute;
            top: 79%;
        }
        .N03004200243-arrow-head-lszx-print{
            border: 1px solid black;
            border-top: 0px;
            border-left: 0px;
            transform: rotate(45deg);
            width: 7px;
            height: 7px;
            left: 46%;
            position: absolute;
            top: 79%;
        }
        .N03004200243-arrow-head-cq{
            border: 1px solid black;
            border-top: 0px;
            border-left: 0px;
            transform: rotate(45deg);
            width: 7px;
            height: 7px;
            left: 43%;
            position: absolute;
            top: 79%;
        }
		.mycheck{
			color:#000000 !important;
		}
    </style>
</head>

<body class="skin-default">
    <div class="printif"></div>
    <div id="yzgl-content" class="flex-container yzgl">
<!--        :style="dragStyle"-->
        <div  :class="openLeft ?'openLeft':'closeLeft'" class=" printHide    yzclLeft  relative flex-dir-c flex-container     bg-fff" v-cloak >
                <div class="flex-container flex-align-c margin-b-10 padd-r-15 ">
                    <span class="padd-r-5 whiteSpace font-14">搜索</span>
                    <input type="text" placeholder="请输入关键字" class="zui-input wh220" v-model="jsContent.jsVal" @keyup.enter="getHzlb()" />
                </div>
                <div class="flex-container flex-align-c">
                    <div class="flex-container flex-align-c margin-b-10">
                        <span class="padd-r-5 whiteSpace font-14">选择</br>科室</span>
                        <select-input class="wh100 wh85" @change-data="selectCB($event,'ksbm')" :child="kslist"
                                      :index="jsContent.ksbm" :val="jsContent.ksbm" :index_mc="'ksmc'" :name="'jsContent.ksbm'" :search="true">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-15 margin-b-10">
                        <span class="padd-r-5  whiteSpace font-14">&emsp;状态</span>
                        <select-input class="wh100 wh85" @change-data="resultChange,selectCB($event,'zyType')" :child="zyYzclHszType_tran"
                                      :index="jsContent.zyType" :val="jsContent.zyType" :name="'jsContent.zyType'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class="flex-container">
                    <div class="flex-container flex-align-c  margin-b-10">
                        <span class="padd-r-5 whiteSpace font-14">护理</br>等级</span>
                        <select-input class="wh100 wh85" @change-data="resultChange,selectCB($event,'hldj')" :child="hldj_tran"
                                      :index="jsContent.hldj" :val="jsContent.hldj" :name="'jsContent.hldj'" :search="true">
                        </select-input>
                    </div>
                    <div class="flex-container flex-align-c padd-r-15 margin-b-10">
                        <span class="padd-r-5 whiteSpace font-14">&emsp;病况</span>
                        <select-input class="wh100 wh85" @change-data="resultChange,selectCB($event,'bkzt')" :child="bkType_tran"
                                      :index="jsContent.bkzt" :val="jsContent.bkzt" :name="'jsContent.bkzt'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class="zui-table-view flex-one padd-b-40 padd-r-10 flex-dir-c flex-container">
                    <div class="zui-table-header">
                        <table class="zui-table table-width50">
                            <thead>
                            <tr>
                                <th class="cell-m">
                                    <input-checkbox @result="reCheckBoxZyh" :list="'brlist'" :type="'all'" :val="isCheckAll"></input-checkbox>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-m">床号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">姓名</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell cell-s">门特</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell text-left cell-s">住院号</div>
                                </th>
                                <th>
                                    <div class="zui-table-cell text-left cell-l">入院诊断</div>
                                </th>

                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="zui-table-body flex-one over-auto" data-no-change @scroll="scrollTable">
                        <table class="zui-table table-width50">
                            <tbody>
                            <tr @click="switchIndex('activeIndex',true,$index)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                @mouseenter="switchIndex('hoverIndex',true,$index)" @mouseleave="switchIndex()" :class="[{'tableTr': $index%2 == 0},{'table-hovers':isChecked[$index]}]"
                                :tabindex="$index" v-for="(item, $index) in brlist">
                                <td class="cell-m">
                                    <input-checkbox @result="reCheckBoxZyh" :list="'brlist'" :type="tabIndex[index]" :which="$index"
                                                    :val="isChecked[$index]">
                                    </input-checkbox>
                                </td>

                                <td>
                                    <div class="zui-table-cell cell-m title position" v-text="item.rycwbh"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.brxm"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s" v-text="item.mtbbz=='1' ? '是':'否'"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-s  text-left" v-text="item.zyh"></div>
                                </td>
                                <td>
                                    <div class="zui-table-cell cell-l  text-left" v-text="item.ryzdmc"></div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="zui-table-fixed table-fixed-l flex-container flex-dir-c" style="bottom: 55px;">
                        <div class="zui-table-header">
                            <table class="zui-table">
                                <thead>
                                <tr>
                                    <th>
                                        <input-checkbox @result="reCheckBoxZyh" :list="'brlist'" :type="'all'" :val="isCheckAll"></input-checkbox>
                                    </th>
                                    <th>
                                        <div class="zui-table-cell  cell-m">床号</div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body flex-one" data-no-change ref="tableBody" style="overflow-y: auto;"
                             @scroll="scrollTableFixed($event)">
                            <table class="zui-table">
                                <tbody>
                                <tr @click="checkSelect([$index,'some','brlist'],$event)" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                    @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" :class="[{'tableTr': $index%2 == 0},{'table-hovers':isChecked[$index]}]"
                                    :tabindex="$index" v-for="(item, $index) in brlist" class="tableTr2">
                                    <td class="cell-m">
                                        <input-checkbox @result="reCheckBoxZyh" :list="'brlist'" :type="tabIndex[index]" :which="$index"
                                                        :val="isChecked[$index]"></input-checkbox>
                                    </td>
                                    <td>
                                        <div class="zui-table-cell cell-m  " v-text="item.rycwbh"></div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div ref="yzclBtoom" class="yzcl-table-left flex-align-c zui-border-bottom flex-container font-14-654">
                    共<span v-text="zrs"></span>人
                </div>
        </div>
        <div :class="openLeft ?'yzcl-792':'yzcl-100'"  class=" yzclRight relative bg-fff  flex-dir-c flex-container" v-cloak>
            <div class="flex-container printHide yzcl-right-tab">
                <div v-for="(item,$index) in tabParentText" class="whiteSpace" :class="{'active':$index==index}" @click="tabPage(item.onclick,$index,item.openUrl)">{{item.text}}</div>
            </div>

            <div class=" margin-r-10 padd-t-10 flex-one flex-dir-c flex-container">
                <div v-if="index==0" class="printHide flex-one flex-dir-c flex-container">
                    <div class="flex-container flex-align-c" style="min-height: 36px;">
                        <tabs :num="num" :tab-child="tabArr"   @tab-active="tabBg" @click.native="queryYzxx()"></tabs>
                        <div class="zui-inline flex-container flex-align-c" v-show="num==1||num==5">
                            <label class="zui-form-label">{{tabArr[num].text}}时间</label>
                            <div class="zui-input-inline wh180">
                                <input  class="zui-input wh180" data-select="no" type="text" @click="showDate1($event)" @keyup="setTime($event)" id="timeTabArr" v-model="timeTabArr">
                            </div>
                        </div>
						<div class="zui-inline flex-container flex-align-c" v-show="false">
						    <label class="zui-form-label">时间</label>
						    <div class="zui-input-inline wh180">
						        <input  class="zui-input wh180" data-select="no" type="text" @click="shDate($event)" @keyup="setTime($event)" id="shtime" v-model="shtime">
						    </div>
						</div>
                        <div :id="yfbm" class=" flex-container flex-align-c   " v-if="num==3">
                            <label class="whiteSpace margin-r-5 ft-14">药房选择</label>
                            <select-input :search="true" :disable="!parseInt(yfpz)" class="wh120" @change-data="commonResultChange" :not_empty="false" :child="Yf_List"
                                          :index="'yfmc'" :index_val="'yfbm'" :val="popContent.yfbm" :name="'popContent.yfbm'">
                            </select-input>
                        </div>
                        <div class=" flex-container flex-align-c" v-if="num==3">
                            <label class="whiteSpace margin-r-5 ft-14">药房</label>
                            <select-input  class="wh120" @change-data="resultChange" :not_empty="false" :child="yfpz_tran"
                                          :index="yfpz" :val="yfpz" :name="'yfpz'">
                            </select-input>
                        </div>
                        <div class="zui-inline flex-container flex-align-c" v-show="num==0">
                            <label class="font-14" style="margin-left: 20px;">类型</label>
                            <select-input class="wh85 padd0" @change-data="commonResultChange" :child="yzlx_tran" :index="yzlx"
                                          :val="yzlx" :name="'yzlx'" :search="true">
                            </select-input>
                        </div>
                        <div class="zui-inline flex-container flex-align-c padd0" v-show="num==0 || num==2">
                            <label class="font-14" style="margin-left: 20px;padding: 0px">标志</label>
                            <select-input class="wh85" @change-data="commonResultChange" :child="ypfy_tran" :index="ypbz"
                                          :val="ypbz" :name="'ypbz'" :search="true">
                            </select-input>
                        </div>
                        <div class="zui-inline flex-container flex-align-c" v-show="num==0">
                            <label class="font-14" style="margin-left: 20px;padding: 0px">状态</label>
                            <select-input class="wh85 padd0" @change-data="commonResultChange" :child="ystzbz_tran" :index="ystzbz"
                                          :val="ystzbz" :name="'ystzbz'" :search="true">
                            </select-input>
                        </div>
						<div class="zui-inline flex-container flex-align-c" v-show="(num==2) && shyzlxz" >
						    <label class="zui-form-label" style="width: 66px;">执行天数</label>
							<label class="sjsb-label"><input type="radio" name="zxyzts" @click="zxyztsClick('-2')" ><i class="padd-l-10">前天</i></label>
							<label class="sjsb-label"><input type="radio" name="zxyzts" @click="zxyztsClick('-1')" ><i class="padd-l-10">昨天</i></label>
							<label class="sjsb-label"><input type="radio" name="zxyzts" @click="zxyztsClick('0')" checked="checked"><i class="padd-l-10">今天</i></label>
							<label v-show="num==2" class="sjsb-label"><input type="radio" name="zxyzts" @click="zxyztsClick('1')" ><i class="padd-l-10">明天</i></label>
							<label v-show="num==2" class="sjsb-label"><input type="radio" name="zxyzts" @click="zxyztsClick('2')" ><i class="padd-l-10">后天</i></label>
						    
						</div>
						<div class="zui-inline flex-container flex-align-c" v-show="num==8">
						    <label class="font-14" style="margin-left: 20px;">类型</label>
						    <select-input class="wh85 padd0" @change-data="commonResultChange" :child="shyzlx_tran" :index="shyzlx"
						                  :val="shyzlx" :name="'shyzlx'">
						    </select-input>
						</div>
                        <div class="zui-inline flex-container flex-align-c" v-show="num==8">
                            <label class="zui-form-label" style="width: 60px;">时间</label>
                            <div class="zui-input-inline wh180">
                                <input  class="zui-input wh180" data-select="no" type="text" @click="zxyzDate($event)" @keyup="setTime($event)" id="zxyztime" v-model="zxyztime">
                            </div>
                        </div>
                    </div>
                    <div id="wrapper" @scroll="scrollFn(true)" class="wh100 position flex-one" :class="{'over-auto padd-b-68':rightJsonList.length>=2}">
                        <div :style="{'height': brItemListSumHeight + 'px'}">
                            <div class="wh100" :style="{'top': brPosition[brListIndex] + 'px'}" style="position: absolute;left:0;"
                                v-for="(brListItem,brListIndex) in rightJsonList">
                                <div v-if="(brListWinSize.top < brPosition[brListIndex] && brPosition[brListIndex] < brListWinSize.bottom) || (brListWinSize.top < brPosition[brListIndex + 1] && brPosition[brListIndex] < brListWinSize.bottom)">
                                    <div class="jbxx-size margin-t-5">
                                        <div class="jbxx-box padd-l-12">
                                            科别：<span class="font-14-654 padd-r-18" v-text="brListItem.ryksmc"></span>
                                            床号：<span class="font-14-654 padd-r-18" v-text="brListItem.rycwbh"></span>
                                            姓名：<span class="font-14-654 padd-r-18" v-text="brListItem.brxm"></span>
                                            性别：<span class="font-14-654 padd-r-18" v-text="brxb_tran[brListItem.brxb]"></span>
                                            年龄：<span class="font-14-654 padd-r-18" v-text="brListItem.nl?(brListItem.nl + nldw_tran[brListItem.nldw]):'' + brListItem.nl2?(brListItem.nl2 + nldw_tran[brListItem.nldw2]):''"></span>
                                            住院号：<span class="font-14-654 padd-r-18" v-text="brListItem.brzyh==null?brListItem.zyh:brListItem.brzyh"></span>
                                            <span v-if="num == 3 || num == 4">

                                            </span>
                                            
                                        </div>
                                    </div>
                                    <div class="zui-table-view flex-one  flex-dir-c flex-container">
                                        <div class="zui-table-header">
                                            <table class="zui-table table-width50 ">
                                                <thead>
                                                    <tr>
                                                        <th class="cell-m" v-if="brListItem.yzxx.length>=1">
                                                            <div class="zui-table-cell cell-m">
                                                                <input class="green" type="checkbox" v-model="brListItem.isCheckAll">
                                                                <label @click="reCheckBoxSon(brListIndex)"></label>
                                                            </div>
                                                        </th>

                                                        <!-- 医嘱查询列表 -->
                                                        <th class="cell-m" v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>类型</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-s"><span>下嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-xxl text-left"><span>医嘱内容</span></div>
                                                        </th>
														<th v-if="num==0">
														    <div class="zui-table-cell cell-m"><span>打印次数</span></div>
														</th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>总量</span></div>
                                                        </th>
														<th v-if="num==0">
														    <div class="zui-table-cell cell-m"><span>用量</span></div>
														</th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-s"><span>用法</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                                        </th>
														<th v-if="num==0">
														    <div class="zui-table-cell cell-xl"><span>执行科室</span></div>
														</th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>扣费</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>护士签名</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-xl text-left "><span>说明</span></div>
                                                        </th>
                                                        
                                                        <th v-if="num==0 && caqxContent.N03004200708 == '0'">
                                                            <div class="zui-table-cell cell-l"><span>医生签名时间</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-s"><span>申领时间</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>首日次数</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>输液速度</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-m"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-l"><span>医生停嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-l"><span>护士停嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==0">
                                                            <div class="zui-table-cell cell-xl text-left "><span>备注说明</span></div>
                                                        </th>

                                                        <!-- 医嘱审核列表 -->
                                                        <th class="cell-s" v-if="num==1">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-m"><span>类型</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-m"><span>分类</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-m"><span>嘱托</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                                        </th>
                                                        <th v-if="num==1 && caqxContent.N03004200708 == '0'">
                                                            <div class="zui-table-cell cell-l"><span>医生签名时间</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-xl"><span>医嘱内容</span></div>
                                                        </th>
														<th v-if="num==1">
														    <div class="zui-table-cell cell-xl"><span>执行科室</span></div>
														</th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-l"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>首日次数</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>药房</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>用药方法</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-xl"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>输液速度</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-xl "><span>说明</span></div>
                                                        </th>


                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==1">
                                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                                        </th>

                                                        <!-- 执行医嘱列表 -->
                                                        <th class="cell-s" v-if="num==2">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th class="cell-s" v-if="num==2">
                                                            <div class="zui-table-cell cell-m"><span>已发送</span></div>
                                                        </th>
                                                        <th class="cell-s" v-if="num==2">
                                                            <div class="zui-table-cell cell-m"><span>嘱托</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-m"><span>发送时间</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-xxxl text-left"><span>医嘱内容</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-xl"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-m"><span>剂量</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-m"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-s text-left"><span>用药方法</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-xl text-left"><span>说明</span></div>
                                                        </th>
														<th v-if="num==2">
														    <div class="zui-table-cell cell-xl"><span>执行科室</span></div>
														</th>

                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-l"><span>输液速度</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==2">
                                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                                        </th>


                                                        <!-- 药品申领列表 -->
                                                        <th class="cell-m" v-if="num==3">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>类型</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-m"><span>分组号</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-xxxl text-left"><span>医嘱内容</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>药房名称</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>用药日期</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>用药时间</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>备注</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-l"><span>用药方法</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-l"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-l"><span>护士签名</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-l"><span>执行到</span></div>
                                                        </th>
                                                        <th v-if="num==3">
                                                            <div class="zui-table-cell cell-xxl text-left"><span>说明</span></div>
                                                        </th>

                                                        <!--取消申领药品（查询）  -->
                                                        <th class="cell-m" v-if="num==4">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th class="cell-m" v-if="num==4">
                                                            <div class="zui-table-cell cell-l"><span>申领单号</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-m"><span>类型</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>药房</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                                        </th>
                                                        <th v-if="num==4 && caqxContent.N03004200245 == '1'">
                                                            <div class="zui-table-cell cell-l"><span>用药日期</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-xxxl text-left"><span>医嘱内容</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-m"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>用药方法</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-s"><span>执行到</span></div>
                                                        </th>
                                                        <th v-if="num==4">
                                                            <div class="zui-table-cell cell-xl text-left"><span>说明</span></div>
                                                        </th>


                                                        <!-- 停嘱审核列表 -->
                                                        <th class="cell-m" v-if="num==5">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-s"><span>执行时间</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-s"><span>医生停嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-xxxl text-left"><span>医嘱内容</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-l"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-l"><span>用药方法</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-l"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-xxl text-left"><span>说明</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==5">
                                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                                        </th>

                                                        <!-- 取消 执行列表@yqq -->
                                                        <th class="cell-m" v-if="num==6">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-m"><span>类型</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-s"><span>下嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-s"><span>发送时间</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-xxxl text-left"><span>医嘱内容</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-s"><span>用法</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-l"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-l"><span>护士签名</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-l"><span>医生停嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-l"><span>护士停嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==6">
                                                            <div class="zui-table-cell cell-xl text-left "><span>说明</span></div>
                                                        </th>

                                                        

                                                        <!-- 审核撤消医嘱 -->
                                                        <th class="cell-m" v-if="num==7">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-m"><span>类型</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-s"><span>下嘱时间</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-s"><span>发送时间</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-xxxl text-left"><span>医嘱内容</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-s"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-s"><span>用法</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-s"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-s"><span>剂量</span></div>
                                                        </th>

                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-l"><span>撤销医生</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-l"><span>撤销时间</span></div>
                                                        </th>
                                                        <th v-if="num==7">
                                                            <div class="zui-table-cell cell-xl text-left "><span>说明</span></div>
                                                        </th>
														
														<!-- 执行医嘱列表 -->
                                                        <th class="cell-s" v-if="num==8">
                                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                                        </th>
                                                        
                                                        <th class="cell-s" v-if="num==8">
                                                            <div class="zui-table-cell cell-m"><span>嘱托</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-s"><span>开始时间</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-m"><span>发送时间</span></div>
                                                        </th>
														<th v-if="num==8">
														    <div class="zui-table-cell cell-m"><span>发送人</span></div>
														</th>
														<th v-if="num==8">
														    <div class="zui-table-cell cell-s"><span>执行时间</span></div>
														</th>
														<th v-if="num==8">
														    <div class="zui-table-cell cell-m"><span>执行人</span></div>
														</th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-xxxl text-left"><span>医嘱内容</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-xl"><span>药品规格</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-m"><span>剂量</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-m"><span>用量</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-s text-left"><span>用药方法</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-s"><span>频次</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-s"><span>输液速度</span></div>
                                                        </th>
														<th v-if="num==8">
														    <div class="zui-table-cell cell-xl"><span>执行科室</span></div>
														</th>

                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-xl text-left"><span>说明</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-s"><span>医生签名</span></div>
                                                        </th>
                                                        <th v-if="num==8">
                                                            <div class="zui-table-cell cell-s"><span>护士签名</span></div>
                                                        </th>

                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                        <div class="zui-table-body  over-auto" :data-no-change="rightJsonList.length>=2?'true':undefined"
                                            @scroll="scrollTable">
                                            <table class="zui-table table-width50">
                                                <tbody>
                                                        <!-- yzlx_tran[yzListItem.yzlx] -->
                                                    <tr :data-fzh="yzListItem.fzh" :data-ksrq="yzListItem.ksrq" @dblclick="queryFyjl(yzListItem)" v-for="(yzListItem, $index) in brListItem.yzxx" :tabindex="$index"
                                                        :class="[{'table-hovers':$index===activeIndex&&brListIndex===activeBrListIndex,'table-hover':$index === hoverIndex&&brListIndex===hoverBrListIndex},{'stop': yzListItem.hstzbz == '1' && yzListItem.ystzbz == '1'},{'yzxyz':yzListItem.numb>0&&num==2},{'yeBack':yzListItem.yebh&&num==1}]"
                                                        @mouseenter="hoverMouse(true,$index),switchIndex('hoverBrListIndex',true,brListIndex)"
                                                        @mouseleave="hoverMouse(),switchIndex()" @click="switchIndex('activeBrListIndex',true,brListIndex),switchIndex('activeIndex',true,$index)">
                                                        <td class="cell-m" v-if="brListItem.yzxx.length>=1">
                                                            <div class="zui-table-cell cell-m">
                                                                <input class="green" type="checkbox" v-model="yzListItem.isChecked">
                                                                <label @click="checkSelectSon(brListIndex,$index)"></label>
                                                            </div>
                                                        </td>
                                                        <!-- 医嘱查询列表 -->
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==0" :class="[ yzListItem.tzbj ]" >
                                                            <div class="zui-table-cell cell-xxl flex-container  title text-left">
                                                                <div class="padd-l-10 padd-r-10" style="color:blue">{{yzListItem.sfjz == 1 ? '急' : '' }}</div>
                                                                {{yzListItem.xmmc}}&nbsp;{{yzListItem.ypgg}}
                                                            </div>
                                                        </td>
														<td v-if="num==0">
														    <div class="zui-table-cell cell-m" v-text="yzListItem.dycs"></div>
														</td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
														<td v-if="num==0">
														    <div class="zui-table-cell cell-m" v-text="yzListItem.dcjl+yzListItem.jldwmc"></div>
														</td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yyffmc"></div>
                                                        </td>
														<td v-if="num==0">
														    <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
														</td>
														<td v-if="num==0">
														    <div class="zui-table-cell cell-xl" v-text="yzListItem.zxksmc"></div>
														</td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" >{{yzListItem.sfjj == '1' ? '是' : '否'}}</div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.zxhsxm"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yssm"></div>
                                                        </td>
                                                        
                                                        <td v-if="num==0 && caqxContent.N03004200708 == '0'">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ysqmsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.slsj,'time')"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.srcs + '次'"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.sysd?(yzListItem.sysd + ' ' + yzListItem.sysddw):''"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.ysqmxm"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ystzsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.hstzsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==0">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.bzsm"></div>
                                                        </td>

                                                        <!--医嘱审核列表  -->
                                                        <td  class="cell-s" v-if="num==1">
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-m" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-m" v-text="yzfl_tran[yzListItem.yzfl]"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-m" v-text="istrue_tran[yzListItem.tsyz]"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==1 && caqxContent.N03004200708 == '0'">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ysqmsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                                        </td>
                                                        <td v-if="num==1" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xl text-over-2 text-left "
                                                                v-text="yzListItem.xmmc"></div>
                                                        </td>
														<td v-if="num==1">
														    <div class="zui-table-cell cell-xl" v-text="yzListItem.zxksmc"></div>
														</td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl+yzListItem.jldwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.srcs + '次'"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sysd?(yzListItem.sysd + ' ' + yzListItem.sysddw):''"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yysm"></div>
                                                        </td>
<!--                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.sysd?(yzListItem.sysd + ' ' + yzListItem.sysddw):''"></div>
                                                        </td>-->
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysqmxm"></div>
                                                        </td>
                                                        <td v-if="num==1">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                                        </td>

                                                        <!-- 执行医嘱列表 -->
                                                        <td class="cell-s" v-if="num==2" >
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td class="cell-s" v-if="num==2">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.numb"></div>
                                                        </td>
                                                        <td class="cell-s" v-if="num==2">
                                                            <div class="zui-table-cell cell-m" v-text="istrue_tran[yzListItem.tsyz]"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-m" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                                        </td>
                                                        <td v-if="num==2" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xxxl text-over-2 text-left "
                                                                v-text="yzListItem.xmmc"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.dcjl+yzListItem.jldwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==2">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==2">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-s text-left " v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yysm"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.zxksmc"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.sysd?(yzListItem.sysd + ' ' + yzListItem.sysddw):''"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysqmxm"></div>
                                                        </td>
                                                        <td v-if="num==2">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                                        </td>

                                                        <!-- 申领药品列表 -->
                                                        <td class="cell-m" v-if="num==3">
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.fzh"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==3" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xxxl text-left"
                                                                v-text="yzListItem.ryypmc"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s title position" v-text="yzListItem.yfmc"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s title position" v-text="fDate(yzListItem.yyrq,'date')"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s title position" v-text="yzListItem.yysj"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s title position" v-text="yzListItem.bz"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==3">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.ysxm"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.zxhsxm"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.zxEnd,'datetime')"></div>
                                                        </td>
                                                        <td v-if="num==3">
                                                            <div class="zui-table-cell cell-xxl text-left " v-text="yzListItem.yssm"></div>
                                                        </td>

                                                        <!--取消申领（查询）  -->
                                                        <td class="cell-m" v-if="num==4">
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td class="cell-m" v-if="num==4">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.sldh"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-m" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                                        </td>
                                                        <td class="cell-m" v-if="num==4">
                                                            <div class="zui-table-cell cell-s title position" v-text="yzListItem.yfmc"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==4 && caqxContent.N03004200245 == '1'">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.yyrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==4" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xxxl text-left " v-text="yzListItem.ryypmc"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==4">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.yfdwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysxm"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxEnd,'datetime')"></div>
                                                        </td>
                                                        <td v-if="num==4">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yssm"></div>
                                                        </td>

                                                        <!-- 停嘱审核列表 -->
                                                        <td class="cell-m" v-if="num==5">
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ystzsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==5" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xxxl text-over-2 text-left "
                                                                v-text="yzListItem.xmmc"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl+yzListItem.jldwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-xxl text-left " v-text="yzListItem.yssm"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysqmxm"></div>
                                                        </td>
                                                        <td v-if="num==5">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                                        </td>


                                                        <!-- 取消执行列表@yqq -->
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-m" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                                        </td>
                                                        <td v-if="num==6" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xxxl text-over-2 text-left "
                                                                v-text="yzListItem.xmmc"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl+yzListItem.jldwmc"></div>
                                                        </td>

                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.ysqmxm"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.zxhsxm"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.ystzsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.hstzsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==6">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yssm"></div>
                                                        </td>

                                                        <!-- 撤消医嘱审核列表@lwh -->
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-m" v-text="yzlx_tran[yzListItem.yzlx]"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                                        </td>
                                                        <td v-if="num==7" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xxxl text-over-2 text-left "
                                                                 v-text="yzListItem.xmmc"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.dcjl+yzListItem.jldwmc"></div>
                                                        </td>

                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-l" v-text="yzListItem.yzcxysxm"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-l" v-text="fDate(yzListItem.yzcxsj,'shortY')"></div>
                                                        </td>
                                                        <td v-if="num==7">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yssm"></div>
                                                        </td>
														
														<!-- 执行医嘱列表 -->
                                                        <td class="cell-s" v-if="num==8" >
                                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                                        </td>
                                                        <td class="cell-s" v-if="num==8">
                                                            <div class="zui-table-cell cell-m" v-text="istrue_tran[yzListItem.tsyz]"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.ksrq,'short')"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-m" v-text="fDate(yzListItem.zxsj,'time')"></div>
                                                        </td>
														<td v-if="num==8">
														    <div class="zui-table-cell cell-m" v-text="yzListItem.zxhsxm"></div>
														</td>
														<td v-if="num==8">
														    <div class="zui-table-cell cell-s" v-text="fDate(yzListItem.sjzxsj,'time')"></div>
														</td>
														<td v-if="num==8">
														    <div class="zui-table-cell cell-m" v-text="yzListItem.sjzxhsxm"></div>
														</td>
                                                        <td v-if="num==8" :class="[ yzListItem.tzbj ]">
                                                            <div class="zui-table-cell cell-xxxl text-over-2 text-left "
                                                                v-text="yzListItem.xmmc"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.ypgg"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.dcjl+yzListItem.jldwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==8">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.jldwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-m" v-text="yzListItem.sl+yzListItem.yfdwmc"></div>
                                                        </td>
                                                        <!--<td v-if="num==8">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.yfdwmc"></div>
                                                        </td>-->
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-s text-left " v-text="yzListItem.yyffmc"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.pcmc"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.sysd?(yzListItem.sysd + ' ' + yzListItem.sysddw):''"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-xl" v-text="yzListItem.zxksmc"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-xl text-left " v-text="yzListItem.yysm"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.ysqmxm"></div>
                                                        </td>
                                                        <td v-if="num==8">
                                                            <div class="zui-table-cell cell-s" v-text="yzListItem.zxhsxm"></div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <p v-if="brListItem.yzxx.length==0" class="  noData text-center zan-border">暂无数据...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="zui-table-tool padd-r-10 flex-align-c zui-border-bottom flex-container font-14-654">
                            <span style="flex: 1;">
                                <input class="green" type="checkbox" v-model="isOver">
                                <label @click.stop="isOverClick(!isOver)">&ensp;全选</label>
                            </span>
							    
							<button v-waves class="root-btn  btn-parmary-f2a" v-if="num==1" @click="confirm(1)">审核医嘱</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==2 && shyzlxz" @click="auditing(3)">发送医嘱</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==3" @click="auditing(5)">药品申领</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==5" @click="auditing(7)">停嘱审核</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==6" @click="auditing(8)">取消执行</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==6" @click="auditing(9)">审核作废</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==6" @click="auditing(12)">驳回作废</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==7" @click="auditing(10)">撤消审核</button>
							<button v-waves class="root-btn btn-parmary" v-if="num==8" @click="auditing(11)">执行医嘱</button>
							<button v-waves class="root-btn  btn-parmary-f2a" v-if="num==2" @click="auditing(2)">取消审核</button>
							<button v-waves class="tong-btn btn-parmary-b   position   paddr-r5 " v-if="num==0"  @click="printJcJy()"><span class="title" data-gettitle="同一申请单号一起打印">打印申请单</span></button>
							<button v-waves class="tong-btn btn-parmary-b paddr-r5 " @click="tsypPrint()" v-if="num==0 ||num==4" >特殊药品打印</button>
							<button v-waves class="root-btn btn-parmary-f2a" v-if=" num==4" @click="printYpsld()">药品申领单打印</button>
							<button v-waves class="root-btn  btn-parmary-f2a" v-if="num==3 && caqxContent.N03004200250 != '1'" @click="auditing(4)">取消发送医嘱</button>
							<button v-waves class="root-btn  btn-parmary-f2a" v-if="num==3 && caqxContent.N03004200250 == '1'" @click="auditing(4)">取消发送医嘱</button>
							<button v-waves class="root-btn  btn-parmary-f2a" v-if="num==4" @click="auditing(6)">取消申领</button>
							<!--<button v-waves class="root-btn btn-parmary" @click="bqcy()">病区出院</button>
							<button v-waves class="root-btn  btn-parmary-d2" @click="qxbqcy()">取消病区出院</button>
                            <button v-waves class="tong-btn btn-parmary-f2a " @click="xtbgUrl">血糖报告</button>
                            <button v-waves class="tong-btn btn-parmary-f2a " @click="hzPrint()">汇总打印</button>
                            <button v-waves class="tong-btn btn-parmary-f2a " @click="openLeftFun()">隐藏患者菜单</button>
                            <button v-waves class="tong-btn btn-parmary-f2a " @click="openUrl">检查报告单</button>-->
                            <button v-waves class="root-btn btn-parmary-f2a" @click="ypztcx()">药品状态查询</button>
							
                            
                            <!-- <button v-waves class="root-btn btn-parmary" @click="qccl()">迁床处理</button>
                            <button v-waves class="root-btn btn-parmary" @click="gdfy()">固定费用</button>
                            <button v-waves class="root-btn btn-parmary-71" @click="ghys">更换医生</button>-->
                            
                            
                        </div>
                    </div>
                </div>
                <!-- 去掉height-100的原因是因为有这个class的时候会引起医嘱管理版面的医嘱被遮挡一部分 -->
                <!-- <div id="loadingPage001" class="loadingPage height-100 flex-container flex-dir-c"> -->
                <div id="loadingPage001" :class="{'loadingPage':index!=0}" class=" flex-container flex-dir-c"></div>

            </div>

            <div v-if="rightJsonList.length < 2" class="bg-fiexd printHide" @click="qtflj"></div>

            <div @click="refresh()" class="sx-bottom printHide"></div>
            <model :s="'保存'" :c="'关闭'" class="wjzDjpop" @default-click="errBr=false" @result-clear="errBr=false"
                   :model-show="true" @result-close="errBr=false" v-if="errBr" :title="title">
                <div class="bqcydj_model flex-container flex-align-c flex-wrap-w">
                    <p v-for="item in errorText">{{item}}</p>
                </div>
            </model>

<model  :model-show="false" @result-close="yzzxsjszClose"
                v-if="yszxsjType" :title="'医嘱执行时间设置'">
            <div class="bqcydj_model">
                <div class="flex-container flex-wrap-w">
					<div class="flex-container flex-align-c padd-r-20 padd-b-10">
					    <span class="padd-r-5">医嘱执行护士</span>
					    <select-input :search="true"  class="wh120" @change-data="commonResultChange" :not_empty="false" :child="zxhslist"
					                  :index="'ryxm'" :index_val="'rybm'" :val="rybm" :name="'rybm'">
					    </select-input>
					</div>
					
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <span class="padd-r-5">医嘱执行时间</span>
                        <div class="zui-input-inline wh180">
                            <input @click="showZxUpdateDate($event)" v-model="yszxTime" id="yszxTime" class="zui-input wh180" type="text" readonly>
                        </div>
                    </div>
                </div>
                <div class="flex-container flex-wrap-w">
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                        <div class="zui-input-inline wh180">
                            <button v-waves style="margin-left: 200px;" class="tong-btn btn-parmary btn-parmary-not
		                    	yellow-bg" @click="saveEndTime">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </model>



            <model :s="'确认'" :c="'关闭'" class="wjzDjpop" @default-click="submit" @result-clear="passwordModel=false"
                   :model-show="true" @result-close="passwordModel=false" v-if="passwordModel" :title="'密码验证'">
                <div class="bqcydj_model flex-container flex-align-c flex-wrap-w" style="width:300px;">
                    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
                    <span class="padd-r-5">密&emsp;&emsp;码</span>
                    <div class="zui-input-inline wh160">
                        <input class="zui-input" autofocus id="mmyz" v-model="password" @keydown.13="submit()" type="password"/>
                    </div>
                </div>
                </div>
            </model>
            <model style="max-width: 990px;z-index: 112;" :s="'确认'" :c="'关闭'" class="wjzDjpop" @default-click="fyjl=false" @result-clear="fyjl=false"
                   :model-show="true" @result-close="fyjl=false" v-if="fyjl" :title="'查看费用记录'">
                <div class="bqcydj_model  fyjlWidth" >
                    <div class="zui-table-view" >
                        <div class="zui-table-header">
                            <table class="zui-table">
                                <thead>
                                <tr>
                                    <th >
                                        <div class="zui-table-cell cell-l">收费日期</div>
                                    </th>
                                    <th >
                                        <div class="zui-table-cell cell-l">项目名称</div>
                                    </th>
                                    <th  >
                                        <div class="zui-table-cell cell-s">费用规格</div>
                                    </th>
                                    <th  >
                                        <div class="zui-table-cell cell-s">费用单价</div>
                                    </th>
                                    <th  >
                                        <div class="zui-table-cell cell-s">费用数量</div>
                                    </th>
                                    <th  >
                                        <div class="zui-table-cell cell-s">费用总价</div>
                                    </th>
                                    <th  >
                                        <div class="zui-table-cell cell-s">费用类别</div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                        <div class=" zui-table-body fyjlModel" data-no-change  @scroll="scrollTable($event)"  role="treeitem">
                            <table class="zui-table" >
                                <tbody>
                                <tr v-if="fyjlList.list"
                                    v-for="(item, index) in fyjlList.list">
                                    <td><div class="zui-table-cell cell-l" v-text="item.djrq"></div></td>
                                    <td><div class="zui-table-cell cell-l" v-text="item.xmmc"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.fygg"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.fydj"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.fysl"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.fyje"></div></td>
                                    <td><div class="zui-table-cell cell-s" v-text="item.fylbmc"></div></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="flex-container flex-jus-e color-wtg">
                        总数量：{{fyjlList.jzrqfysl}}    记录费用：{{fyjlList.jzrqfyhj}}元
                    </div>
                </div>
            </model>
			
			
			
			
<model class="zxlr printHide"  style="height: 340px;" :model-show="false" @result-close="wzxfy=false" v-if="wzxfy"  :title="'未计费项目'">
	<div style="height: 280px;" class="bqcydj_model">
	    <div class="zui-table-view hzList hzList-border flex-container flex-dir-c">
	        <div class="zui-table-header">
	            <table class="zui-table table-width50">
	                <thead>
	                <tr>
	                    <th class="wh50 text-center">
	                        <input-checkbox @result="reZxCheckBox" :list="'fyxm_List'" :type="'all'" :val="isZxCheckAll">
	                        </input-checkbox>
	                    </th>
	                    <th class=" cell-xxl text-center">
	                        <div class="zui-table-cell  cell-xxl"><span>项目名称</span></div>
	                    </th>
	                    
	                </tr>
	                </thead>
	            </table>
	        </div>
	        <div class="zui-table-body flex-one over-auto"  data-no-change @scroll="scrollTable($event)">
	            <table class="zui-table ">
	                <tbody>
	                <tr :id="'tr'+$index" v-for="(item, $index) in fyxm_List">
	                    <td class="wh50 text-center">
	                        <input-checkbox @result="reZxCheckChange" :list="'fyxm_List'" :type="'some'" :which="$index" :val="isZxChecked[$index]">
	                        </input-checkbox>
	                    </td>
	                    <td class="cell-xxl text-center">
	                        <div class=" zui-table-cell  cell-xxl">
	                            {{item.yzxmmc}}
	                        </div>
	                    </td>
	                    
	                </tr>
	                </tbody>
	            </table>
	        </div>
			<div class="flex-container flex-wrap-w">
			    <div class="flex-container flex-align-c padd-r-20 padd-b-10">
			        <div class="zui-input-inline wh180">
			            <button v-waves style="margin-top: 5px;" class="tong-btn btn-parmary btn-parmary-not
			            	yellow-bg" @click="saveZxFy('1')">保存</button>
							<button v-waves style="margin-top: 5px;" class="tong-btn btn-parmary btn-parmary-not
								yellow-bg" @click="saveZxFy('0')">作废</button>
			        </div>
			    </div>
			</div>
	    </div>
	</div>
        </model>

            <!-- 申领单查询start -->
            <model style="max-width: 990px;z-index: 112;" :s="'确认'" :c="'关闭'" class="wjzDjpop" @default-click="mxcx=false" @result-clear="mxcx=false"
                   :model-show="true" @result-close="mxcx=false" v-if="mxcx" :title="'科室明细查询'">
                <div style="width: 970px;" class="bqcydj_model  fyjlWidth" >
                    <div class="flex-container flex-align-c padd-b-5">

                        <input type="text"  class="zui-input wh182 margin-l-5" @click="showDate('#timeVal','beginrq')"
                        :value="beginrq" placeholder="请选择开始日期" id="timeVal">

                        <span class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh182 " placeholder="请选择结束日期" @click="showDate('#timeVal1','endrq')"
                        :value="endrq" id="timeVal1" />

                        <span v-if="N03003200154 == '0'" class="padd-r-5 padd-l-5 font14">发药状态</span>
                        <select-input  v-if="N03003200154 == '0'" class="wh80" @change-data="getChange" :not_empty="false"
                                      :child="fymx_tran" :index="fymxid" :val="fymxid"
                                      :name="'fymxid'">
                        </select-input>
                        <span v-if="N03003200154 == '1'" class="padd-r-5 padd-l-5 font14">摆药状态</span>
                        <select-input v-if="N03003200154 == '1'" class="wh70" @change-data="getChange" :not_empty="false"
                                      :child="bydh_tran" :index="bydh" :val="bydh"
                                      :name="'bydh'">
                        </select-input>
                        <select-input class="wh70 margin-l-5" @change-data="getChange" :not_empty="false"
                                      :child="type_tran" :index="type" :val="type"
                                      :name="'type'">
                        </select-input>

                        <input class="zui-input wh160 margin-l-5" placeholder="住院号/发药单号" @keydown.enter="getDataChange()" v-model="zyh" type="text" />
                    </div>

                   <div class="flex-container" style="height: 410px;">

                           <div  class="zui-table-view wh20" style="border-right: 1px solid;">
                               <div class="zui-table-header">
                                   <table class="zui-table">
                                       <thead>
                                       <tr>
                                           <th class="cell-m" >
                                               <input-checkbox  @result="reCheckBoxByd" :list="'bydList'"
                                                                :type="'all'" :val="isCheckBydAll">
                                               </input-checkbox>
                                           </th>
                                           <th >
                                               <div class="zui-table-cell cell-l">申领单号</div>
                                           </th>
                                           <th >
                                               <div class="zui-table-cell cell-l">申领时间</div>
                                           </th>
                                           <th >
                                               <div class="zui-table-cell cell-s">申领人</div>
                                           </th>
                                           <th >
                                               <div class="zui-table-cell cell-s">药房名称</div>
                                           </th>
                                       </tr>
                                       </thead>
                                   </table>
                               </div>

                               <div class=" zui-table-body fyjlModel_l" data-no-change  @scroll="scrollTable($event)"  role="treeitem">
                                   <table class="zui-table" >
                                       <tbody>
                                       <tr v-for="(item, $index) in bydList">

                                           <td class="cell-m" >
                                               <input-checkbox @result="reCheckBoxByd" :list="'bydList'"
                                                               :type="'some'" :which="$index"
                                                               :val="isCheckBoxByd[$index]">
                                               </input-checkbox>
                                           </td>

                                           <td><div class="zui-table-cell cell-l" v-text="item.sldh"></div></td>
                                           <td><div class="zui-table-cell cell-l" v-text="fDate(item.slsj,'datetime')"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="item.slhsxm"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="item.yfmc"></div></td>
                                       </tr>
                                       </tbody>
                                   </table>
                               </div>

                           </div>

                       <div class="wh_80">
                           <div class="zui-table-view " >
                               <div class="zui-table-header">
                                   <table class="zui-table">
                                       <thead>
                                       <tr>
                                       	<th  >
                                               <div class="zui-table-cell cell-m">序号</div>
                                           </th>
                                           <th >
                                               <div class="zui-table-cell cell-m">床号</div>
                                           </th>
                                           <th >
                                               <div class="zui-table-cell cell-m">分组</div>
                                           </th>
                                           <th  >
                                               <div class="zui-table-cell cell-m">姓名</div>
                                           </th>

                                           <th  >
                                               <div class="zui-table-cell cell-xl">药品名称</div>
                                           </th>
                                           <th  ><div class="zui-table-cell cell-s">药品规格</div></th>
                                           <th  ><div class="zui-table-cell cell-s">用量</div></th>
                                           <th  ><div class="zui-table-cell cell-s">频次</div></th>
                                           <th  ><div class="zui-table-cell cell-l">发药单号</div></th>
                                           <th  ><div class="zui-table-cell cell-l">摆药单号</div></th>
                                           <th  ><div class="zui-table-cell cell-s">服药日期</div></th>
                                           <th  ><div class="zui-table-cell cell-s">服药时间</div></th>
                                           <th  ><div class="zui-table-cell cell-s">备注</div></th>
                                           <th  ><div class="zui-table-cell cell-s">用药方法</div></th>
                                           <th  ><div class="zui-table-cell cell-s">剂量</div></th>
                                           <th  ><div class="zui-table-cell cell-s">参考零价</div></th>
                                           <th  ><div class="zui-table-cell cell-s">参考金额</div></th>
                                           <th  ><div class="zui-table-cell cell-s">医生</div></th>
                                           <th  ><div class="zui-table-cell cell-s">药品编码</div></th>
                                       </tr>
                                       </thead>
                                   </table>
                               </div>
                               <div class=" zui-table-body fyjlModel" data-no-change  @scroll="scrollTable($event)"  role="treeitem">
                                   <table class="zui-table" >
                                       <tbody>
                                       <tr v-if="sldList"
                                           v-for="(item, index) in sldList">
                                            <td><div class="zui-table-cell cell-m" v-text="index + 1"></div></td>
                                           <td><div class="zui-table-cell cell-m" v-text="item.rycwbh"></div></td>
                                           <td><div class="zui-table-cell cell-m" v-text="item.fzh"></div></td>
                                           <td><div class="zui-table-cell cell-m" v-text="item.brxm"></div></td>
                                           <td><div class="zui-table-cell cell-xl" v-text="item.ryypmc"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="item.ypgg"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="fDec(item.sl,2)+item.yfdwmc"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="item.pcmc"></div></td>
                                           <td ><div class="zui-table-cell cell-l" v-text="item.fydh"></div></td>
                                           <td ><div class="zui-table-cell cell-l" v-text="item.bydh"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="fDate(item.yyrq,'date')"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="item.yysj"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="item.bz"></div></td>
                                           <td><div class="zui-table-cell cell-s" v-text="item.yyffmc"></div></td>
                                       </tr>
                                       </tbody>
                                   </table>
                               </div>
                           </div>
                       </div>
                   </div>
                </div>
            </model>
            <!-- 申领单查询end -->
<!--            医嘱撤销-->
            <model :s="'保存'" :c="'关闭'" class="yzcxShow" @default-click="cxSuccess" @result-clear="yzcxShow=false"
                   :model-show="true" @result-close="errBr=false" v-if="yzcxShow" :title="'医嘱撤销确认'">
                <div class="bqcydj_model ">
                    <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
                        <span class=" whiteSpace ft-14 padd-r-5">撤销意见</span>
                        <textarea  rows="3" cols="100" class="padd-t-10 padd-b-10 padd-l-10 padd-r-10 wh100MAx" type="text"  v-model="cxObj.cxyj" data-value="同意撤销"></textarea>
                    </div>
                    <div class="flex-container padd-r-10 padd-b-10 flex-align-c">
                        <span class="whiteSpace ft-14 padd-r-5">撤&emsp;&emsp;销</span>
                        <select-input   class="wh122"
                                        @change-data="resultChange"
                                        :not_empty="false"
                                        :child="cx_tran"
                                        :val="cxObj.cx"
                                        :name="'cxObj.cx'"
                                        >
                        </select-input>
                    </div>
                </div>
            </model>


        </div>
    </div>
    <div class="side-form printHide flex-container flex-dir-c pop-width" :class="{'ng-hide':index==0}" v-cloak ref="brzcList" id="UserHzxx" role="form">
        <div v-if="index==1" class="flex-container hh100MAx flex-dir-c">
            <div class="fyxm-side-top flex-between">
                <span>患者信息</span>
                <span class="fr closex ti-close" @click="index=0"></span>
            </div>
            <div class="ksys-side flex-container flex-dir-c flex-one">
                <ul class="tab-edit-list1 useritem flex-start over-auto">
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e  flex-align-c">住院号</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.zyh"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">病人姓名</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.brxm"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">性别</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxb_tran[brxxContent.brxb]"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">年龄</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.nl"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">身份证号码</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.sfzjhm"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">工作单位</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.gzdw"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">家庭住址</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.jzdmc"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">联系电话</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.sjhm"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">入院费别</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.brfbmc"></span></li>




                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">入院时间</span><span
                            class="userValue flex-container  flex-align-c" v-text="fDate(brxxContent.ryrq,'yyyy-MM-dd')"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">出院时间</span><span
                            class="userValue flex-container  flex-align-c" v-text="fDate(brxxContent.cyrq,'yyyy-MM-dd')"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">住院天数</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.zyts"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">入院诊断</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.ryzdmc"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">护理等级</span><span
                            class="userValue flex-container  flex-align-c" v-text="brxxContent.hldj"></span></li>
                            <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">门诊医生</span><span
                            class="userValue flex-container  flex-align-c font-14-654" v-text="brxxContent.mzysxm"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">主治医生</span><span
                            class="userValue flex-container  flex-align-c font-14-654" v-text="brxxContent.zyysxm"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">费用合计</span><span
                            class="userValue flex-container  flex-align-c font-14-654" v-text="brxxContent.fyhj"></span></li>
                    <li class="userlist flex-container"><span class="userKey flex-container flex-jus-e flex-align-c">预交合计</span><span
                            class="userValue flex-container  flex-align-c font-14-654" v-text="brxxContent.yjhj"></span></li>
                    <li class="userlist flex-container"  v-if="caqxContent.N03004200709== '1'">
                        <span class="userKey flex-container flex-jus-e flex-align-c">担保人</span>
                        <span class="userValue flex-container  flex-align-c"><input v-model="brxxContent.dbr" class=" zui-input flex-container flex-jus-c flex-align-c"/></span></li>
                    <li class="userlist flex-container"  v-if="caqxContent.N03004200709== '1'">
                        <span class="userKey flex-container flex-jus-e flex-align-c">担保金额</span>
                        <span class="userValue flex-container  flex-align-c"><input v-model="brxxContent.dbje" class=" zui-input flex-container flex-jus-c flex-align-c"/></span></li>
                    <li class="userlist flex-container">
                            <span class="userKey flex-container flex-jus-e flex-align-c">费&emsp;&emsp;别</span>
                        <select-input class="userValue flex-container  flex-align-c" @change-data="resultzcChange"
                                      :child="brFbList" :index="'fbmc'"
                                      :index_val="'fbbm'" :val="brxxContent.fbbm" :name="'brxxContent.fbbm'" :search="true"
                                      :disable="false"
                                      >
                        </select-input>
                    </li>
                    <li class="userlist flex-container">
                        <span class="userKey flex-container flex-jus-e flex-align-c">保险类别</span>
                        <select-input class=" userValue flex-container  flex-align-c" :disable="true" @change-data="resultChange" :not_empty="false"
                                      :child="bxLbList" :index="'bxlbmc'"
                                      :index_val="'bxlbbm'" :val="brxxContent.bxlbbm" :name="'brxxContent.bxlbbm'" :search="true"
                                      :disable="false"
                                      >
                        </select-input>
                    </li>
                    <li class="userlist flex-container xunhuanlist" :class="{'userlength':item==xhitem}" v-for="item in xhitem"><span
                            class="userKey flex-container  flex-align-c"></span><span class="userValue flex-container flex-jus-c flex-align-c"></span></li>
                </ul>
            </div>
            <div class="ksys-btn" style="position: absolute">
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="save">确认</button>
            </div>
        </div>
    </div>

    <div class="side-form printHide pop-width" v-cloak style="padding-top: 0;" id="ghys" role="ghys" :class="{'ng-hide': !isShow}">
        <div v-if="isShow">
            <div class="fyxm-side-top">
                <span>更换医生</span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="ksys-side">
                <div>
                    <i>原主管医生</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                            :index_val="'rybm'" :val="brInfo.zyys" :name="'brInfo.zyys'" :search="true" :disable="true"></select-input>
                    </div>
                </div>
                <div>
                    <i>新主管医生</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                            :index_val="'rybm'" :val="doctor" :name="'doctor'" :search="true"></select-input>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button class="zui-btn btn-primary xmzb-db" @click="saveData()">确定</button>
            </div>
        </div>
    </div>

    <div class="side-form  pop-width" :class="{'ng-hide':Class}" v-cloak style="padding-top: 0;" id="jkac" role="form">
        <div v-if="!Class">
            <div class="fyxm-side-top">
                <span v-text=""></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="ksys-side">
                <div>
                    <i>主管医生</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                            :index_val="'rybm'" :val="jkacContent.zyys" :name="'jkacContent.zyys'" :search="true"></select-input>
                    </div>
                </div>
                <div>
                    <i>床位</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="false" :child="cwList" :index="'cwbh'"
                            :index_val="'cwid'" :val="jkacContent.cwid" :name="'jkacContent.cwid'" :search="true"></select-input>
                    </div>
                </div>
                <div>
                    <i>西医诊断</i>
                    <div class="margin-top-5 margin-b-20">
                        <textarea type="text" class="zui-textarea border-r4" @keyup="nextFocus($event)" placeholder="请输入西医诊断"
                            v-text='jkacContent.xyryzd'></textarea>
                    </div>
                </div>
                <div>
                    <i>中医诊断</i>
                    <div class="margin-top-5 margin-b-20">
                        <textarea type="text" class="zui-textarea border-r4" @keyup="nextFocus($event)" placeholder="请输入中医诊断"
                            v-text='jkacContent.zyryzd'></textarea>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button class="zui-btn btn-primary xmzb-db" @click="saveJkac()">确定</button>
            </div>
        </div>
    </div>

    <div class="side-form  pop-width" v-cloak :class="{'ng-hide':Class}" style="padding-top: 0;" id="zkac" role="form">
        <div v-if="!Class">
            <div class="fyxm-side-top">
                <span v-text=""></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="ksys-side">
                <div>
                    <i>主管医生</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                            :index_val="'rybm'" :val="zkhcContent.zyys" :name="'zkhcContent.zyys'" :search="true"></select-input>
                    </div>
                </div>
                <div>
                    <i>原科室</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :not_empty="false" :child="Kslist" :index="'ksmc'"
                            :index_val="'ksbm'" :val="zkhcContent.ryks" :name="'zkhcContent.ryks'" :search="true"
                            disabled="disabled"></select-input>
                    </div>
                </div>
                <div>
                    <i>转往科室</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :not_empty="false" :child="Kslist" :index="'ksmc'"
                            :index_val="'ksbm'" :val="zkhcContent.zrksbm" :name="'zkhcContent.zrksbm'" :search="true"></select-input>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button class="zui-btn btn-primary xmzb-db" @click="saveZkhc()">确定</button>
            </div>
        </div>
    </div>
    <div class="side-form  pop-width" v-cloak :class="{'ng-hide':Class}" style="padding-top: 0;" id="qccl" role="form">
        <div v-if="!Class">
            <div class="fyxm-side-top">
                <span v-text=""></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="ksys-side">
                <div>
                    <i>病人姓名</i>
                    <div class="margin-top-5 margin-b-20">
                        <input type="text" class="zui-input" disabled v-model="brInfo.brxm" />
                    </div>
                </div>
                <div>
                    <i>主管医生</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="true" :child="zgysList" :index="'ryxm'"
                            :index_val="'rybm'" :val="brInfo.zyys" :name="'brInfo.zyys'" disable></select-input>
                    </div>
                </div>
                <div>
                    <i>原床位</i>
                    <div class="margin-top-5 margin-b-20">
                        <input type="text" class="zui-input" disabled v-model="brInfo.rycwbh" />
                    </div>
                </div>
                <div>
                    <i>新床位</i>
                    <div class="margin-top-5 margin-b-20">
                        <select-input @change-data="resultChange" :data-notEmpty="false" :child="cwList" :index="'cwbh'"
                            :index_val="'cwid'" :val="qcContent.cwid" :name="'qcContent.cwid'" :search="true"></select-input>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button class="zui-btn btn-primary xmzb-db" @click="saveQccl">确定</button>
            </div>
        </div>
    </div>
    <div class="side-form  pop-width" :class="{'ng-hide':Class}" v-cloak style="padding-top: 0;" id="qxbqcy" role="form">
        <div class="fyxm-side-top">
            <span v-text="title"></span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side">
            <div>
                <i>床位</i>
                <div class="margin-top-5 margin-b-20">
                    <select-input @change-data="commonResultChange" :data-notEmpty="false" :child="cwList" :index="'cwbh'"
                                  :index_val="'cwid'" :val="jkacContent.cwid" :name="'jkacContent.cwid'" :search="true"></select-input>
                </div>
            </div>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="saveJkac()">确定</button>
        </div>
    </div>
</body>
<script type="text/javascript" src="yzgl.js"></script>
<style type="text/css" media="print">
    .yzcl-792,
    .dataCon {
        width: 100%;
    }

</style>

</html>
