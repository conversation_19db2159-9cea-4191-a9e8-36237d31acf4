<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>退货查询</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
</head>

<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box">
<div class="wrapper" id="wrapper">
    <div class="panel ">
        <div class="tong-top">
            <button class="tong-btn btn-parmary icon-width icon-dc-b">导出</button>
            <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
        </div>
        <div class="tong-search">
            <div class="zui-form">
                <div class="zui-inline padd-l-40">
                    <label class="zui-form-label">药库</label>
                    <div class="zui-input-inline wh122">
                        <select-input @change-data="resultRydjChange"
                                      :child="yfkfList" :index="'kfmc'" :index_val="'kfbm'" :val="param.kfbm"
                                      :name="'param.kfbm'" :search="true" :index_mc="'kfmc'" >
                        </select-input>
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label margin-f-l10">时间段</label>
                    <div class="zui-input-inline margin-f-l20">
                        <input class="zui-input todate wh240 " placeholder="请选择申请日期" id="timeVal"  />
                    </div>
                </div>
                <div class="zui-inline">
                    <label class="zui-form-label margin-f-l10">检索</label>
                    <div class="zui-input-inline margin-f-l35">
                        <input class="zui-input wh180" placeholder="请输入关键字" type="text" @keydown.13="goToPage(1)"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="zui-table-view " >
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>种类编码</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>种类名称</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>期初金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>开单入库</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>退库入库</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>盘点入库</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>领用出库</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>退货出库</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>报损出库</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>盘点出库</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>损溢金额</span></div></th>
                    <th><div class="zui-table-cell cell-s"><span>结余金额</span></div></th>
                </tr>
                </thead>
            </table>
        </div>
        <div class="zui-table-body " @scroll="scrollTable($event)">
            <table class="zui-table table-width50">
                <tbody>
                <tr v-for="(item, $index) in jsonList" :class="[{'table-hovers':$index===activeIndex}]" >
                    <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1">001</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlbm">西药收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.ypzlmc">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.qcje">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.kdrk">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.tkrk">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pdrk">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.lyck">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.thck">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.bsck">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.pdck">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.syje">材料收入</div></td>
                    <td><div class="zui-table-cell cell-s" v-text="item.jyje">材料收入</div></td>
                    <p v-if="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                </tr>
                </tbody>
            </table>

        </div>

        <div class="zui-table-fixed table-fixed-l">
            <div class="zui-table-header">
                <table class="zui-table">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span> <em></em></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item, $index) in jsonList" :tabindex="$index" :class="[{'table-hovers':$index===activeIndex}]" class="tableTr2">
                        <td class="cell-m"><div class="zui-table-cell cell-m">{{$index+1}}</div></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>

    </div>

</div>
</div>
<script src="ykybb.js"></script>

</body>

</html>
