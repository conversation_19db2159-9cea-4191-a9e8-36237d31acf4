    var wrapper = new Vue({
        el: '#wrapper',
        mixins: [dic_transform, tableBase, mConfirm, baseFunc, mformat],
        data: {
            popContent:{},
            jsonList: [],
            yfkfList: [],
            yfkf: 0, //药房库房信息
            param: {
                page: 1,
                rows: 10,
                sort: '',
                order: 'desc',
                shzfbz: 1,
                beginrq: null,
                endrq: null,
                yfbm:'',
                parm:''
            }
        },
        updated:function (){
            changeWin();
        },
        mounted:function () {
            this.getKf();
            var myDate=new Date();
            this.param.beginrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
            this.param.endrq = this.fDate(new Date(), 'date')+' 23:59:59';
            laydate.render({
                elem: '#timeVal'
                , trigger: 'click',
                type: 'datetime'
                , theme: '#1ab394'
                , done: function (value, data) {
                    wrapper.param.beginrq = value;
                    wrapper.getData();
                }
            });
            laydate.render({
                elem: '#timeVal1'
                , trigger: 'click'
                , theme: '#1ab394',
                type: 'datetime'
                , done: function (value, data) {
                    wrapper.param.endrq =value;
                    wrapper.getData();
                }
            });
        },

        methods: {
            //获取数据
            getData: function() {
                if (!this.popContent.yfbm) {
                    malert('请选择药房','top','defeadted');
                    return;
                }
                common.openloading('.zui-table-body');
                $.getJSON("/actionDispatcher.do?reqUrl=YfbCxtjAll&types=lskc&parm=" + JSON.stringify(wrapper.param), function(json) {
                    if(json.a == "0") {
                        wrapper.totlePage = Math.ceil(json.d.total / wrapper.param.rows);
                        wrapper.jsonList = json.d.list;
                    }
                });
                common.closeLoading()
            },
            getKf: function() {
                //库房列表
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=yf',
                    function(data) {
                        if(data.a == 0) {
                            wrapper.yfkfList = data.d.list;
                            Vue.set(wrapper.param,'yfbm',wrapper.yfkfList[0].yfbm);
                            wrapper.getData();
                            console.log(wrapper.yfkfList)
                        } else {
                            malert(data.c,'top','defeadted');
                        }
                    });

            },
            //组件选择下拉框之后的回调
            resultRydjChange: function (val) {
                Vue.set(this.param, 'yfbm', val[0]);
                Vue.set(this.param, 'yfmc', val[4]);
                wrapper.getData();

            },
        },


    });
