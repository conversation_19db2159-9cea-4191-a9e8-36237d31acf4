<div class="sqKss kss" id="sqKss" ref="sqKss">
    <div class="flex-container flex-align-c padd-b-10 padd-t-10">
        <span class="whiteSpace ft-14">当前药品：</span>
        <span class="whiteSpace ft-14">{{popContent.ypmc || popContent.xmmc}}</span>
    </div>
    <p class="font-18 font-weight color-wtg padd-l-70">您无权限使用该抗生素!</p>
    <p class="font-18 font-weight color-wtg padd-l-70 padd-b-20">您可以向医务科申请提升权限,或者临时向上级医生申请使用该药品</p>
    <div class="flex-container flex-align-c padd-l-70">
        <label for="four2" class="flex-container cursor flex-align-c">
            <div class="position padd-r-5">
                <input type="radio" id="four2" name="two" v-model="popContent.sfsy" value="0" class="zui-radio" >
                <label for="four2" class="padd-r-5"></label>
            </div>
            <span class="padd-r-5">放弃使用</span>
        </label>
        <label for="four3" class="flex-container cursor flex-align-c">
            <div class="position padd-r-5">
                <input type="radio" id="four3" name="two" v-model="popContent.sfsy" value="1" class="zui-radio" >
                <label for="four3" class="padd-r-5"></label>
            </div>
            <span class="padd-r-5">申请使用该药品</span>
        </label>
    </div>
    <div v-if="popContent.sfsy==1" class="flex-container flex-wrap-w">
        <div class="flex-container flex-align-c padd-b-10 padd-t-10 padd-r-10">
            <span class="whiteSpace ft-14">申请药品：</span>
            <p>{{popContent.ypmc || popContent.xmmc}}</p>
        </div>
        <div class="flex-container flex-align-c padd-b-10 padd-r-10 padd-t-10">
            <span class="whiteSpace ft-14">药品规格：</span>
            <p>{{popContent.ypgg || popContent.xmgg}}</p>
        </div>
        <div class="flex-container flex-align-c  padd-b-10 padd-r-10 padd-t-10">
            <span class="whiteSpace ft-14">申请用量：</span>
            <div class="relative">
                <input class="zui-input wh120" data-notEmpty="true" v-model="popContent.sl"/>
                <span class="cm">支</span>
            </div>
        </div>
        <div class="flex-container flex-align-c padd-b-10 padd-r-10 padd-t-10">
            <span class="whiteSpace ft-14">使用日期：</span>
            <input class="zui-input wh120" data-notEmpty="true" id="time" v-model="popContent.syrq"/>
        </div>
        <div class="flex-container flex-align-c padd-b-10 padd-r-10 padd-t-10">
            <span class="whiteSpace ft-14">上级医师：</span>
            <select-input class="wh120" :data-notEmpty="true" @change-data="resultChange" :not_empty="true" :child="ysData"
                          :index="'ryxm'" :index_val="'rybm'" :index_mc="'shrmc'" :val="popContent.shrbm" :name="'popContent.shrbm'"
                          :search="true" :phd="''">
            </select-input>
        </div>
        <div class="flex-container padd-b-10 padd-t-10 wh100MAx">
            <span class="whiteSpace ft-14">申请原因：</span>
            <textarea class="ypStyle " data-notEmpty="true" rows="3" cols="100" v-model="popContent.sqyy"></textarea>
        </div>
    </div>
</div>
<script type="text/javascript">
    var  sqKss= new Vue({
        el: '#sqKss',
        mixins: [baseFunc,tableBase],
        components: {
            'search-table': searchTable,
        },
        watch:{
            'popContent.sfsy':function () {
                this.$forceUpdate()
            }
        },
        data: {
            ysData:[],
            Content:{},
            popContent:{
                sfsy:1,
            },
            changeVal:false,
            selectContent:{},
            searchCon: [],
            them_tran: {'jb': dic_transform.data.ssjb_tran},
            them: {'手术编码': 'ssbm', '手术名称': 'ssmc', '拼音代码': 'pydm', '手术级别': 'jb'},
            selSearch:-1,
            page: {
                page: 1,
                rows: 20,
                total: null
            },
            json: {
                ysbz: '1',
                tybz: '0',
            },
            dg: {
                page: '1',
                rows: '',
                parm: '',
            },
        },
        mounted:function(){
            var a=typeof(zcy) == "undefined" ? hzList: zcy
            this.popContent=Object.assign(this.popContent,a.popContent,userNameBg.Brxx_List)
            this.popContent.zyh=this.popContent.ghxh || this.popContent.zyh
            this.popContent.ypmc=this.popContent.ypmc || this.popContent.xmmc
            this.popContent.ypbm=this.popContent.ypbm || this.popContent.xmbm
            this.$forceUpdate()
            this.getYs();
            laydate.render({
                elem: '#time',
                rigger: 'click',
                theme: '#1ab394',
                done: function (value, data) {
                    sqKss.popContent.syrq = value;
                }
            });
        },
        created: function () {
        },

        methods: {
            getYs: function () {
                this.$http.get('/actionDispatcher.do', {
                    params: {
                        reqUrl: 'New1YlfwptGssgl',
                        types: 'sjysList',
                        page: 1,
                        pageSize: 10000,
                        ksbm:userId
                    }
                }).then(function (json) {
                    if (json.body.a == 0 && json.body.d && json.body.d.list.length != 0) {
                        sqKss.ysData = json.body.d.list;
                    } else {
                        malert(json.body.c, 'top', 'defeadted');
                    }
                })
            },
            savaData:function(){
                var a=typeof(zcy) == "undefined" ? hzList: zcy;
                if(this.popContent.sfsy =='0'){
                    a.kssModelShow = false;
                    return false
                }
                this.$http.post('/actionDispatcher.do?reqUrl=New1YlfwptGssgl&types=sqsykss', JSON.stringify(this.popContent)).then(function (data) {
                    if (data.body.a == 0) {
                        malert(data.body.c);
                        a.kssModelShow = false
                    } else {
                        malert(data.body.c, 'top', 'defeadted');
                    }
                }, function (error) {
                    malert(data.body.c, 'top', 'defeadted');
                    console.log(error);
                });
            },
            reCheckOne: function (val) {
                this.popContent.lhyyCk=val[1];
                this.$forceUpdate()
            },
            //当输入值后才触发
            change1: function (add, val,mc) {
                if (!add) this.page.page = 1;
                var _searchEvent = $(event.target.nextElementSibling).eq(0);
                this.page.parm = val;
                var str_param = {parm: this.page.parm, page: this.page.page, rows: 30};
                //手术编码
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ssbm' + '&json=' + JSON.stringify(str_param), function (data) {
                    if (add) {
                        for (var i = 0; i < data.d.list.length; i++) {
                            sqKss.searchCon.push(data.d.list[i]);
                        }
                    } else {
                        sqKss.searchCon = data.d.list;
                    }
                    sqKss.changeVal = true;
                    sqKss.page.total = data.d.total;
                    sqKss.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
            },
            //检索
            changeDown: function (event, searchCon,mc,bm,index) {
                this.inputUpDown(event, 'searchCon', 'selSearch');
                this.Content = this[searchCon][this.selSearch]
                //选中之后的回调操作
                if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                    if (this.changeVal) {
                        Vue.set(this.popContent, bm, this.Content['ssbm']);
                        Vue.set(this.popContent, mc, this.Content['ssmc']);
                        this.nextFocus(event);
                        $(".selectGroup").hide();
                        this.searchCon=[];
                        this.selSearch = -1;
                        this.$forceUpdate()
                    } else {
                        this.nextFocus(event);
                    }
                }
            },
            selectOne: function (item) {
                if (item == null) {
                    this.page.page++;
                    this.change1(true, this.popContent['ssmc']);
                } else {
                    Vue.set(this.popContent, 'ssbm', item['ssbm']);
                    Vue.set(this.popContent, 'ssmc', item['ssmc']);
                    this.$forceUpdate()
                    this.searchCon=[];
                    this.selSearch = -1
                    $(".selectGroup").hide();
                }
            },
        },
    });
</script>

