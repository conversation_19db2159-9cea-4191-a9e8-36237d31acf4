<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>入库管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="newpdgl.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-b-10 padd-r-10">
<div class="background-box  flex-one flex-dir-c flex-container">
    <div class="wrapper flex-one wh1000 flex-dir-c flex-container" id="wrapper" v-cloak>
        <div class="panel ">
            <div class="tong-top">
                <button class="tong-btn btn-parmary iconfont font-14 icon-iocn44 padd-r-10 paddr-r5" v-show="pdscShow" @click="add">生成</button>
                <button class="tong-btn btn-parmary-b iconfont font-14 padd-r-10 icon-iocn4 paddr-r5" v-show="pdscShow" @click="save" >保存</button>
                <button class="tong-btn btn-parmary iconfont font-14 padd-r-10 icon-iocn45 paddr-r5" v-show="passShow" @click="confirm">审核</button>
                <button class="tong-btn btn-parmary iconfont font-14 padd-r-10 icon-iocn42 paddr-r5"  v-show="num==1" @click="addpdlr">盘点录入</button>
                <button class="tong-btn btn-parmary iconfont font-14 padd-r-10 icon-iocn42 paddr-r5"  v-show="num==1" @click="savepdlr">盘点新增</button>
                <button class="tong-btn btn-parmary-b  iconfont font-14 padd-r-10 icon-iocn56 paddr-r5 " @click="getPdSC" >刷新</button>

            </div>
            <div class="tong-search flex-container">
                <div class="top-form flex-container flex-align-c padd-r-20">
                    <label class="top-label font-14 padd-r-5">二级库房</label>
                    <div class="top-zinle">
                        <div class="top-zinle">
                            <select-input @change-data="yfChange"
                                          :child="YFSelect" :index="'yfmc'" :index_val="'yfbm'" :val="yfSelected"
                                          :name="'yfSelected'" :search="true">
                            </select-input>
                        </div>
                    </div>
                </div>
                <div class="top-form flex-container flex-align-c padd-r-20" v-if="ypzlShow">
                    <label class="top-label font-14 padd-r-5">种类名称</label>
                    <div class="top-zinle">
                        <select-input @change-data="resultChange"
                                      :child="YPZLSelect" :index="'ypzlmc'" :index_val="'ypzlbm'" :val="popContent.ypzl"
                                      :name="'popContent.ypzl'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class="top-form flex-container  flex-align-c padd-r-20" v-if="ypmcShow">
                    <label class="top-label font-14 padd-r-5">材料名称</label>
                    <div class="top-zinle">
                        <input class="zui-input" :value="ypmcInput" @keydown="changeDown($event,'text')"
                               @input="change(false,$event.target.value)">
                        <search-table :message="searchCon" :selected="selSearch"
                                      :them="them" :them_tran="them_tran" :page="page"
                                      @click-one="checkedOneOut" @click-two="selectOne">
                        </search-table>
                    </div>
                </div>
                <div class="top-form flex-container  flex-align-c padd-r-20" >
                    <label class="top-label font-14 padd-r-5">材料检索</label>
                    <div class="top-zinle">
                        <input class="zui-input" placeholder="材料编码/名称" @input="ypjsData" v-model="ypjsValue">
                    </div>
                </div>

                <div v-show="num==2" class="top-form flex-container  flex-align-c padd-r-20" >
                        <label class="top-label font-14 padd-r-5">零价预盈亏:</label>
                        <div class="top-zinle">
                            <span  v-text="ljyyk"></span>
                        </div>
                </div>

                <div v-show="num==2" class="top-form flex-container  flex-align-c padd-r-20" >
                        <label class="top-label font-14 padd-r-5">进价预盈亏: </label>
                        <div class="top-zinle">
                            <span v-text="jjyyk"></span>
                        </div>
                </div>


                <div v-show="num==3" class="top-form flex-container  flex-align-c padd-r-20" >
                        <label class="top-label font-14 padd-r-5">已盘点零价盈亏: </label>
                        <div class="top-zinle">
                            <span v-text="ljyyk"></span>
                        </div>
                </div>

                <div v-show="num==3" class="top-form flex-container  flex-align-c padd-r-20" >
                        <label class="top-label font-14 padd-r-5">已盘点进价盈亏:  </label>
                        <div class="top-zinle">
                            <span v-text="jjyyk"></span>
                        </div>
                </div>
            </div>
        </div>
        <div class=" flex-container flex-align-c flex-jus-s padd-l-10">
            <tabs :num="num" :tab-child="[{text:'盘点生成'},{text:'盘点录入'},{text:'预完成'},{text:'盘点完成'}]" @tab-active="tabBg"></tabs>

        </div>
        <div key="a" class="zui-table-view flex-one flex-dir-c flex-container wh1000 padd-r-10 padd-l-10"  v-if="num==0" >
            <!--未核盘点列表-->
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-l text-left"><span>材料名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>材料编号</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>单位</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>材料批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>

                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body padd-b-10 over-auto flex-one" data-no-change   @scroll="scrollTable($event)">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in pdscJsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdscJsonList'],$event)" :tabindex="$index">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-l text-left" v-text="item.ypmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.ypgg"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yplj"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.yfdwmc"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.xtph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.scph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.cdmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc"></div></td>

                        <p v-show="pdscJsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
           <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>
        </div>

        <!--盘点录入-->
        <div key="b" class="flex-container zui-table-view flex-one flex-dir-c wh1000 padd-r-10 padd-l-10"  v-if="num==1" >
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-l text-left"><span>材料名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>材料编号</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>账存数量</span></div></th>
                        <th><div class="zui-table-cell cell-l"><span>实存数量</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>单位</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>材料批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body" @scroll="scrollTable($event)"  >
                <table class="zui-table">
                    <tbody>
                    <tr v-for="(item,$index) in pdscJsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @dblclick="edit($index,index)"
                        @click="checkSelect([$index,'one','pdscJsonList'],$event)" :tabindex="$index">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-l text-left" v-text="item.ypmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.pdzcsl"></div></td>
                        <td><div class="zui-table-cell cell-l" v-text="item.pdsjkc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypgg"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yplj"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yfdwmc"></div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.xtph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.scph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.cdmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc"></div></td>

                        <p v-show="pdscJsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="zui-table-fixed table-fixed-r background-f">
                <div class="zui-table-header">
                    <table class="zui-table zui-collapse">
                        <thead>
                        <tr>
                            <th>
                                <div class="zui-table-cell cell-s"><span>操作</span></div>
                            </th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                    <table class="zui-table">
                        <tbody>
                        <tr v-for="(item, $index) in pdscJsonList"
                            :tabindex="$index"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                            @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()">
                            <td>
                                <div class="zui-table-cell cell-s">
                                     <span  class="icon-sc icon-font" data-title="删除" @click="deleteFun(item)">
                                </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
           <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>
        </div>

        <!--预完成-->
        <div key="b" class="flex-container zui-table-view flex-one flex-dir-c wh1000 padd-r-10 padd-l-10"  v-if="num==2" >
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-l text-left"><span>材料名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>材料编号</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>账存</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>实存</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>单位</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>材料批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body"  @scroll="scrollTable($event)"  >
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in pdscJsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                        @mouseenter="hoverMouse(true,$index)"
                        @mouseleave="hoverMouse()"
                        @click="checkSelect([$index,'one','pdscJsonList'],$event)" :tabindex="$index">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-l text-left" v-text="item.ypmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.pdzcsl"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.pdsjkc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypgg"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yplj"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.yfdwmc"></div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.xtph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.scph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.cdmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc"></div></td>

                        <p v-show="pdscJsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
           <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>
        </div>

        <!--盘点完成-->
        <div key="b" class="flex-container zui-table-view flex-one flex-dir-c  padd-r-10 padd-l-10"  v-if="num==3" >
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell cell-m"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-l text-left"><span>材料名称</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>材料编号</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>账存</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>实存</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>盈亏</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>规格</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>进价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>进价盈亏</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>零价盈亏</span></div></th>
                        <th><div class="zui-table-cell cell-m"><span>单位</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>材料批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>生产批号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>有效期至</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>产地</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>供货单位</span></div></th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="zui-table-body padd-b-10" data-no-change @scroll="scrollTable($event)"  >
                <table class="zui-table ">
                    <tbody>
                    <tr v-for="(item,$index) in pdscJsonList"  :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex},{'redClass':parseInt(item.pdyk) < 0}]"
                        @click="checkSelect([$index,'one','pdscJsonList'],$event)" :tabindex="$index">
                        <td class="cell-m"><div class="zui-table-cell cell-m" v-text="$index+1"></div></td>
                        <td><div class="zui-table-cell cell-l text-left" v-text="item.ypmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypbm"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.pdzcsl"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.pdsjkc"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.pdyk"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypgg"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypjj"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.yplj"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypjjyk"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ypljyk"></div></td>
                        <td><div class="zui-table-cell cell-m" v-text="item.yfdwmc"></div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.xtph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.scph"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="fDate(item.yxqz,'date')"></div></td>
                        <td><div class="zui-table-cell cell-xl" v-text="item.cdmc"></div></td>
                        <td><div class="zui-table-cell cell-s" v-text="item.ghdwmc"></div></td>

                        <p v-show="pdscJsonList.length==0" class="  noData text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
           <page @go-page="goPage" :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore":next-more="nextMore"></page>
        </div>
    </div>
    <!-- :class="{'ng-hide':index==0}" -->

   <!--盘点录入-->
   <div class="side-form pop-548  pop-width" id="pdlrSide" v-cloak :class="{'ng-hide':type}" role="form">
        <div class="fyxm-side-top flex-between">
            <span>盘点录入</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side">
          <ul class="tab-edit-list tab-edit2-list">
            <li>
                <i>材料名称</i>
                <input ref="pdlrYpmc" id="ypmc" class="zui-input" @input="change($event,$event.target.value)" :value="popContent.ypmc" @keydown="changeDown($event,'ypmc','searchCon')">
                <search-table :message="searchCon" :selected="selSearch" :total="total" :them="them" :them_tran="them_tran"
                    @click-one="checkedOneOut" @click-two="selectOne">
                </search-table>
            </li>
            <li>
                <i>材料规格</i>
                <input type="text" class="zui-input" v-model="popContent.ypgg" @keydown="nextFocus($event)"
                    disabled />
            </li>
            <li>
                <i>分装比例</i>
                <input type="text" class="zui-input" v-model="popContent.fzbl" @keydown="nextFocus($event)"
                    disabled />
            </li>
            <li>
                <i>材料批号</i>
                <input type="text" id="xtph" class="zui-input" v-model="popContent.xtph" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                <i>生产批号</i>
                <input type="text" id="scph" class="zui-input" v-model="popContent.scph" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                <i>二级库房单位</i>
                <input type="text" id="yfdwmc" class="zui-input" v-model="popContent.yfdwmc" @keydown="nextFocus($event)" disabled />
            </li>
            <li>
                <i>生产日期</i>
                <input v-model="fDate(popContent.scrq,'date')" class="zui-input text-indent20" id="_scrq"
                    @blur="dateForVal($event, 'popContent.scrq')"  disabled>
            </li>
            <li>
                <i>材料零价</i>
                <input type="text" id="yplj" class="zui-input" v-model="popContent.yplj" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                <i>账存数量</i>
                <input type="text" id="pdzcsl" class="zui-input" v-model="popContent.pdzcsl" @keydown="nextFocus($event)" disabled/>
            </li>
            <li>
                <i>实存数量</i>
                <input type="text" id="pdsjkc" class="zui-input" v-model="popContent.pdsjkc"  @keydown="changeDownDate($event,'pdsjkc')"/>
            </li>
        </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="confirms">保存</button>
        </div>
    </div>

    <div class="side-form  pop-548 pop-width" id="pdxzSide" v-cloak :class="{'ng-hide':type}" role="form">
        <div class="fyxm-side-top flex-between">
            <span>盘点新增</span>
            <span class="fr closex ti-close" @click="closes"></span>
        </div>
        <div class="ksys-side">
            <ul class="tab-edit-list tab-edit2-list">
                <li>
                    <i>材料名称</i>
                    <input ref="pdxzYpmc" class="zui-input" :value="ypmcInput" @keydown="changeDown($event,'ypmc'),nextFocus($event)"
                           @input="change(false,$event.target.value)">
                    <search-table :message="searchCon" :selected="selSearch"
                                  :them="them" :them_tran="them_tran" :page="page"
                                  @click-one="checkedOneOut" @click-two="selectOne">
                    </search-table>
                </li>
                <li>
                    <i>材料编码</i>
                    <input type="text" class="label-input background-h zui-input _addData" disabled="disabled" v-model="popContent.ypbm" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>材料规格</i>
                    <input type="text" class="label-input background-h zui-input _addData" disabled="disabled" v-model="popContent.ypgg" @keydown="nextFocus($event)"/>
                </li>
                <li>
                    <i>材料进价</i>
                    <input type="text" class="label-input zui-input _addData" v-model="popContent.ypjj"/>
                </li>
                <li class="auto-focus">
                    <i>材料零价</i>
                    <input type="text" class="label-input zui-input _addData" v-model="popContent.yplj" @keydown="changeDownDate($event,'yplj')"/>
                </li>
                <li>
                    <i>分装比例</i>
                    <input class="label-input background-h zui-input _addData" disabled="disabled" v-model="popContent.fzbl" @keydown="nextFocus($event)"/>
                </li>
                <li class="auto-focus">
                    <i>生产批号</i>
                    <input class="label-input zui-input _addData" v-model="popContent.scph" @keydown="nextFocus($event)" data-notEmpty="true"/>
                </li>
                <li class="auto-focus">
                    <i>产品标准号</i>
                    <input class="label-input zui-input _addData" v-model="popContent.cpbzh" @keydown="nextFocus($event)" data-notEmpty="true"/>
                </li>
                <li class="auto-focus">
                    <i>批准文号</i>
                    <input class="label-input zui-input _addData" v-model="popContent.pzwh" @keydown="nextFocus($event)" data-notEmpty="true"/>
                </li>
                <li>
                    <i>生产日期</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input v-model="fDate(popContent.scrq,'date')" class="zui-input text-indent20 times1" id="_scrq"
                        @blur="dateForVal($event, 'popContent.scrq')" @keydown="changeDownDate($event,'_scrq')" >
                </li>
                <li>
                    <i>有效期至</i>
                    <em class="icon-position icon-rl" style="left: 74px;"></em>
                    <input v-model="fDate(popContent.yxqz,'date')" class="zui-input text-indent20 times2" id="_yxqz"
                        @keydown="changeDownDate($event,'_yxqz')" >
                </li>
                <li>
                    <i>供货单位</i>
                    <select-input @change-data="resultChange" :not_empty="true" :child="ghdwList" :index="'dwmc'"
                                  :index_val="'dwbm'" :val="popContent.ghdw" :search="true" :name="'popContent.ghdw'">
                    </select-input>
                </li>
                <li>
                    <i>产地</i>
                    <select-input ref="cdbm" @change-data="resultChange" :not_empty="true" :child="ypcdList" :index="'cdmc'"
                                    :index_val="'cdbm'" :val="popContent.cdbm" :search="true" :name="'popContent.cdbm'" :index_mc="'cdmc'">
                    </select-input>
                </li>
                <li class="auto-focus">
                    <i>实存数量</i>
                    <input type="text" id="pdsjkc" class="zui-input" v-model="popContent.pdsjkc"  @keydown="changeDownDate($event,'pdsjkc')"/>
                </li>
            </ul>
        </div>
        <div class="ksys-btn">
            <button class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
            <button class="zui-btn btn-primary xmzb-db" @click="addNewYp">保存</button>
        </div>
    </div>
</div>
<script src="newpdgl.js"></script>

</body>

</html>
