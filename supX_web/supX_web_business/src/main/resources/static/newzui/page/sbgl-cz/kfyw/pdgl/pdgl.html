<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>盘点管理</title>
    <script type="application/javascript" src="/newzui/pub/top.js"></script>
    <link type="text/css" href="pdgl.css" rel="stylesheet"/>
</head>
<body class="skin-default padd-l-10 padd-t-10 padd-r-10">
<div class="background-box" id="wrapper" v-cloak>
    <div class="wrapper"  >
        <div class="panel">
            <div class="tong-top">
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="Verify(undefined,false)">手动盘点</button>
                <button class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="Verify(undefined,true)">自动盘点</button>
                <button class="tong-btn btn-parmary-b icon-sx paddr-r5" @click="goToPage(1)">刷新</button>
            </div>
            <div class="tong-search">
                <div class="zui-form">
<!--                    <div class="zui-inline padd-l-40">-->
<!--                        <label class="zui-form-label">盘点凭证号</label>-->
<!--                        <div class="zui-input-inline wh122" style="margin-left: 40px;">-->
<!--                            &lt;!&ndash;下拉组件,绑数据放开&ndash;&gt;-->
<!--                            <select-input style="width: 180px;" @change-data="resultRydjChange"-->
<!--                            :child="KFList" :index="'kfmc'" :index_val="'kfbm'" :val="popContent.kfbm"-->
<!--                            :name="'popContent.kfbm'" :search="true" :index_mc="'kfmc'" >-->
<!--                            </select-input>-->

<!--                        </div>-->
<!--                    </div>-->
                    <div class="zui-inline">
                        <label class="zui-form-label">时间段</label>
                        <div class="zui-input-inline margin-f-l10  flex-container flex-align-c">
                            <i class="icon-position icon-rl"></i>
                            <input class="zui-input todate wh200 text-indent20" placeholder="请选择申请开始日期" id="timeVal"/><span class="padd-l-5 padd-r-5">~</span>
                            <input class="zui-input todate wh200 " placeholder="请选择申请结束日期" id="timeVal1" />
                        </div>
                    </div>
                    <div class="zui-inline">
                        <label class="zui-form-label">检索</label>
                        <div class="zui-input-inline margin-f-l25">
                            <input class="zui-input wh180" placeholder="请输入关键字" type="text" @keyup.enter="goToPage(1)" v-model="param.parm"/>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="zui-table-view  "   >
            <!--入库列表-->
            <div class="zui-table-header">
                <table class="zui-table table-width50">
                    <thead>
                    <tr>
                        <th class="cell-m"><div class="zui-table-cell"><span>序号</span></div></th>
                        <th><div class="zui-table-cell cell-xl"><span>盘点录入单号</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>盘点日期</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>制单人</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>备注</span></div></th>
                        <th><div class="zui-table-cell cell-s"><span>状态</span></div></th>
                        <th class=""><div class="zui-table-cell cell-s"><span>操作</span></div></th>
                    </tr>
                    </thead>
                </table>

            </div>
            <div class="zui-table-body ">
                <table class="zui-table table-width50">
                    <tbody>
                    <tr v-for="(item,$index) in jsonList" :tabindex="$index">
                        <td class=" cell-m">
                            <div class="zui-table-cell" v-text="$index+1">序号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-xl" v-text="item.pdpzh">盘点录入单号</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s " v-text="fDate(item.pdrq,'date')">盘点日期</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.zdrmc">制单人</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s" v-text="item.bzms">备注</div>
                        </td>
                        <td>
                            <div class="zui-table-cell cell-s">
                                <i v-text="zhuangtai[item.qrzfbz]" :class="item.qrzfbz=='0' ? 'color-dsh':item.qrzfbz=='1' ? 'color-ysh' : item.qrzfbz=='2' ? 'color-yzf':'' "></i>
                                <!---->
                            </div>
                        </td>
                        <td class="">
                            <div class="zui-table-cell cell-s">
<!--                                <span  class="icon-sh" v-if="item.qrzfbz=='0'" data-title="审核" @click="Verify($index,true)"></span>-->
<!--                                <span  class="icon-js" v-if="item.qrzfbz == 0" data-title="拒绝" @click="Refuse($index)"></span>-->
                                <span class="flex-center padd-t-5">
                                 <em class="width30" v-if="item.qrzfbz == 0">
                                    <i class="icon-sh" data-title="审核"  @click="Verify($index,false)"></i>
                                </em>
                                <em  class="width30" v-if="item.qrzfbz==0">
                                    <i class="icon-js" data-title="作废"  @click="Refuse($index)"></i>
                                </em>
                                <em class="width30" v-if="item.shzfbz !=0">
                                    <i class="icon-yl" data-title="明细"  @click="Verify($index,false)"></i>
                                </em>
                               </span>
                            </div>
                        </td>
                        <!--暂无数据提示,绑数据放开-->
                        <p v-show="jsonList.length==0" class="  noData  text-center zan-border">暂无数据...</p>
                    </tr>
                    </tbody>
                </table>
            </div>
            <page @go-page="goPage"  :totle-page="totlePage" :page="page" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>
        </div>
    </div>
</div>
<script src="pdgl.js" type="text/javascript"></script>
</body>
</html>
