//主体部分
var wrapper = new Vue({
    el: '#wrapper',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        //*****************公共
        num: 0,
        YFSelect: [],//二级库房列表
        editPdlr: false,
        pzhList: [], //凭证号集合
        YPZLSelect: [],//材料种类列表
        pdscJsonList: [],//材料列表
        yfSelected: '',//选中的二级库房
        pzNum: '', //选中的凭证号
        qxksbm: '', //权限科室编码
        csqxContent: {},//获取的参数权限对象
        popContent: { //判断方式选择
            pdWay: 1,
            ypzl: 0,
        },
        ypjgshow: true, //材料价格显示
        pdscShow: true, //盘点生成需要显示
        deleteShow: false,
        passShow: false,
        ypzlShow: false, //材料种类显示
        ypmcShow: false, //材料名称
        pzhShow: false, //凭证号
        pdlrShow: false,
        ypjsShow: false, //材料检索
        ypjsValue: '', //材料检索内容
        isSubmited: false,//是否禁止保存

        //******************************盘点生成
        fyContent: {},
        searchCon: [],
        selSearch: -1,
        jjyyk: '',
        ljyyk: '',
        wpjjyyk: '',
        wpljyyk: '',
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        param: {
            page: 1,
            rows: 50,
            sort: '',
            order: 'asc'
        },
        totlePage: 0,
        them_tran: {},

        them: {
            '材料编号': 'ypbm',
            '材料名称': 'ypmc',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'ypjj',
            '零价': 'yplj',
            '库房单位': 'kfdwmc',
            '二级库房单位': 'yfdwmc',
            '二级库房种类': 'ypzlmc',
            '材料剂型': 'jxmc'
        },

        //********************************未核盘点
        whpdList: [], //未核盘点集合
        whpdmxList: [], //未核盘点明细集合
        whpdbSelected: {}, //已选盘点表

        //*******************************盘点录入
        pdlrList: [], //盘点录入列表

        //******************************录入审核
        pdlrShList: [],
        pdlrShmxList: [],
        pdblrSelected: {},

        //******************************盘点完成
        pdwcPddList: [],
        shState: 0,
        json: {
            jjzj: 0,
            ljzj: 0,
        },
        isChecked: false,
    },
    updated: function () {
        changeWin()
    },
    mounted: function () {
    },
    computed: {
        money: function () {
            var reducers = {
                totalInEuros: function (state, item) {
                    return state.jjzj += parseFloat(item.ypjjje);
                },
                totalInYen: function (state, item) {
                    return state.ljzj += parseFloat(item.ypljje);
                }
            };
            var manageReducers = function (reducers) {
                return function (state, item) {
                    return Object.keys(reducers).reduce(function (nextState, key) {
                        reducers[key](state, item);
                        return state;
                    }, {})
                }
            }
            var bigTotalPriceReducer = manageReducers(reducers);
            var totals = this.pdscJsonList.reduce(bigTotalPriceReducer, this.json = {
                jjzj: 0,
                ljzj: 0,
            });
        }
    },
    methods: {
		save:function () {
				//是否禁止保存
				if (this.isSubmited) {
					malert('数据提交中，请稍候！', "top", 'defeadted');
					return;
				}
				if (this.pdscJsonList.length == 0) {
					malert('没有可以提交的数据！', "top", 'defeadted');
					return;
				}
				//准备参数
				var pdb = {
					'yfbm': this.yfSelected,
					'qrzfbz': '0',
				};

				var json = {
					'list': {
						'pdb': pdb,
						'pdbmx': this.pdscJsonList,
					},
				}
				//是否禁止保存
				this.isSubmited = true;

				//发送请求保存数据
				this.$http.post('/actionDispatcher.do?reqUrl=New1YfbKcglPdgl&types=savePd',
					JSON.stringify(json))
					.then(function (data) {
						if (data.body.a == 0) {
							wrapper.pdscJsonList = [];
							malert("提交成功", 'top', 'success');
						} else {
							malert(data.body.c, "top", 'defeadted');
						}
						//是否禁止保存
						wrapper.isSubmited = false;
					}, function (error) {
						//是否禁止保存
						wrapper.isSubmited = false;
					});
			},
        deleteFun(item) {
            var parm = {
                id: item.id
            };
            this.$http.post("/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=deletePdmx",
                JSON.stringify(parm)).then(function (data) {
                if (data.body.a == 0) {
                    wrapper.getPdSC();
                    malert(data.body.c, 'top', 'defeadted');
                } else {
                    malert(data.body.c, 'top', 'defeadted');
                }
            }, function (error) {
                console.log(error);
            });
        },
        //切换菜单栏
        tabBg: function (n) {
            wrapper.param.page = '1';
            this.num = n;
            wrapper.getPdSC();
            //根据菜单栏的切换隐藏显示
            //0盘点生成,1未核盘点表,2盘点录入,3录入审核,4盘点完成,

            if (this.num != 1) {
                wapAddmx.closes();
                wap.closes();
                wapAddmx.popContent = {};
                wap.popContent = {};
                wapAddmx.ypmcInput = "";
                wap.inputYpmc = '';
            }

            if (this.num == 0) {
                this.pdscShow = true;
                this.passShow = false;
                this.ypzlShow = false;
                this.ypmcShow = false;
            } else if (this.num == 1) {
                this.pdscShow = false;
                this.passShow = false;
                this.ypzlShow = false;
                this.ypmcShow = false;
            } else if (this.num == 2) {
                this.pdscShow = false;
                ;
                this.passShow = false;
                this.ypzlShow = false;
                this.ypmcShow = false;
            } else if (this.num == 3) {
                this.pdscShow = false;
                this.passShow = true;
                this.ypzlShow = false;
                this.ypmcShow = false;
            }
        },
        //获取材料种类集合
        getYpzlData: function () {
            //加载材料种类列表
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzl', function (json) {
                if (json.a == '0' && json.d) {
                    wrapper.YPZLSelect = json.d.list;
                } else {
                    malert(json.c, 'top', 'defeadted');
                }
            });
        },
        reCheckBox: function (val) {
            wrapper.isChecked = val[2];
        },

        //格式数据值小位数，d-输入参数，len-小数位数

        fDec: function (d, len) {
            var f = parseFloat(d);
            if (isNaN(f)) {
                return "";
            }
            var f = Math.round(d * 100) / 100;
            var s = f.toString();
            var rs = s.indexOf('.');
            if (rs < 0) {
                rs = s.length;
                s += '.';
            }
            while (s.length <= rs + len) {
                s += '0';
            }
            if (s == 0.00) {
                s = "";
            }
            return s;
        },

        //获取二级库房权限
        getYFData: function () {
            //获取二级库房列表
            var parm = {
                "ylbm": 'N040030020022006',
            };
            $.getJSON("/actionDispatcher.do?reqUrl=CsqxAction&types=qxyf&parm=" + JSON.stringify(parm),
                function (data) {
                    if (data.a == 0 && data.d) {
                        wrapper.YFSelect = data.d.list;
                        wrapper.qxksbm = data.d.list[0].ksbm;
                        wrapper.yfSelected = data.d.list[0].yfbm;
                        wrapper.getPdSC();
                    } else {
                        malert("二级库房获取失败", 'top', 'defeadted');
                    }
                });
        },


        //二级库房改变
        yfChange: function (val) {
            wrapper.yfSelected = val[0];
            this.qxksbm = wrapper.listGetName(wrapper.YFSelect, wrapper.yfSelected, 'yfbm', 'ksbm');
            ;
            wrapper.getPdSC();
        },
        //材料下拉检索
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'fyContent', 'searchCon');
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {
                if (type == 'text') {
                    Vue.set(this.popContent, 'ypmc', this.fyContent.ypmc);
                    Vue.set(this.popContent, 'ypbm', this.fyContent.ypbm);
                    this.ypmcInput = this.fyContent.ypmc;
                }
            }
        },

        //当输入值后才触发
        change: function (add, val) {
            this.ypmcInput = val;
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = this.ypmcInput;
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(str_param),
                function (data) {
                    if (data.d.list.length > 0) {
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                wrapper.searchCon.push(data.d.list[i]);
                            }
                        } else {
                            wrapper.searchCon = data.d.list;
                        }
                    }
                    wrapper.page.total = data.d.total;
                    wrapper.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
        },

        //鼠标双击
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;  // 设置当前页号
                this.change(true, this.ypmcInput);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                Vue.set(this.popContent, 'ypbm', item.ypbm);
                Vue.set(this.popContent, 'ypmc', item.ypmc);
                this.ypmcInput = item.ypmc;
                $(".selectGroup").hide();
            }
        },

        //生成盘点表
        add: function () {
            //非空判断
            if (!this.yfSelected) {
                malert('请选择二级库房！', "top", 'defeadted');
                return;
            }
            //设置参数
            var json = {
                'yfbm': this.yfSelected,
                'ifgx': wrapper.isChecked == true ? '1' : ''
            };
            common.openloading()
            //发送请求，查询盘点表
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=updatePd&parm=' + JSON.stringify(json),
                function (data) {
                    if (data.a == 0) {
                        common.closeLoading()
                        malert(data.c, "top", 'success');
                        wrapper.getPdSC();
                    } else {
                        common.closeLoading()
                        malert(data.c, "top", 'defeadted');
                    }
                });
        },
        //盘点生成
        getPdSC: function () {
            //非空判断
            if (!this.yfSelected) {
                malert('请选择二级库房！', "top", 'defeadted');
                return;
            }
            wrapper.pdscJsonList = [];
            wrapper.param['yfbm'] = this.yfSelected;

            common.openloading()

            if (this.num == 1 || this.num == 2 || this.num == 3) {
                if (this.num == 1) {
                    wrapper.param['pdlc'] = '1';
                } else if (this.num == 2) {
                    wrapper.param['pdlc'] = '2';
                } else {
                    wrapper.param['pdlc'] = '3';
                }
                //发送请求，查询盘点表
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=selectPdMxlb&parm=' + JSON.stringify(wrapper.param),
                    function (data) {
                        if (data.a == 0 && data.d) {
                            common.closeLoading()
                            if (wrapper.num == 2 || wrapper.num == 3) {
                                wrapper.jjyyk = wrapper.fDec(data.d.jjyyk, 4);
                                wrapper.ljyyk = wrapper.fDec(data.d.ljyyk, 4);
                                wrapper.wpjjyyk = wrapper.fDec(data.d.wpjjyyk, 4);
                                wrapper.wpljyyk = wrapper.fDec(data.d.wpljyyk, 4);
                                for (var i = 0; i < data.d.list.length; i++) {
                                    var ypTmp = data.d.list[i];
                                    data.d.list[i].ypljyk = wrapper.fDec(ypTmp.yplj * ypTmp.pdyk, 4);
                                    data.d.list[i].ypjjyk = wrapper.fDec(ypTmp.ypjj * ypTmp.pdyk, 4);
                                }
                            }
                            wrapper.pdscJsonList = data.d.list;
                            wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
                        } else {
                            common.closeLoading()
                            malert(data.c, "top", 'defeadted');
                        }
                    });

            } else {

                wrapper.param['pdlc'] = '';
                //发送请求，查询盘点表
                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=selectPdlb&parm=' + JSON.stringify(wrapper.param),
                    function (data) {
                        if (data.a == 0) {
                            common.closeLoading()
                            wrapper.pdscJsonList = data.d.list;
                            wrapper.totlePage = Math.ceil(data.d.total / wrapper.param.rows);
                        } else {
                            common.closeLoading()
                            malert(data.c, "top", 'defeadted');
                        }
                    });

            }
        },
        //双击修改有效期和批次停用
        edit: function ($index, index) {

            wrapper.editPdlr = true;
            var json = JSON.parse(JSON.stringify(wrapper.pdscJsonList[$index]));
            if (json.ifpdlr == 1) { //新增

                wapAddmx.open();
                wapAddmx.popContent = json;
                wapAddmx.ypmcInput = wapAddmx.popContent.ypmc;
            } else {
                wap.open();
                wap.popContent = json;
                wap.inputYpmc = wap.popContent.ypmc;
            }

        },
        //****************************************未核盘点
        goPage: function (val) {
            wrapper.param.page = val;
            wrapper.getPdSC();
        },

        //盘点录入
        addpdlr: function () {
            wrapper.editPdlr = false;
            wap.open();
        },
        //盘点新增
        savepdlr: function () {
            wapAddmx.open();
        },
        //**************************************公用提交方法
        ypjsData: function () {
            wrapper.param["parm"] = this.ypjsValue;
            wrapper.getPdSC();
        },
        //审核
        confirm: function () {
                common.openConfirm("是否确定审核？", sccf);

                function sccf() {
                    //非空判断
                    if (!wrapper.yfSelected) {
                        malert('请选择二级库房！', "top", 'defeadted');
                        return;
                    }
                    //设置参数
                    var json = {
                        'yfbm': wrapper.yfSelected,
                    };
                    common.openloading()
                    //发送请求，查询盘点表
                    $.getJSON('/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=updateMxPdwc&parm=' + JSON.stringify(json),
                        function (data) {
                            if (data.a == 0) {
                                common.closeLoading()
                                wrapper.getPdSC();
                                malert('审核成功', "top", 'success');

                            } else {
                                common.closeLoading()
                                malert(data.c, "top", 'defeadted');
                            }
                        });
                }
        },
    },
});

//右侧弹窗 盘点录入
var wap = new Vue({
    el: '#pdlrSide',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        type: true,
        isShowpopL: false,
        iShow: false,
        isTabelShow: false,
        flag: false,
        jsShow: false,
        centent: '',

        yfbm: null,
        inputYpmc: '', //材料名称
        isFold: false,
        ksList: [],
        hszList: [],
        ywckList: [],
        title: '',
        ifClick: true,
        num: 0,
        csContent: {},
        YFList: [],
        jsonList: [],
        popContents: {},
        cgryList: [],//采购员
        csqxContent: {}, //参数权限对象
        csqx: null,
        ckdContent: {}, //出库单对象
        KSList: [], //领用科室
        zbfsList: [],//招标方式
        ghdwList: [],//供货单位
        KFList: [], //库房
        ryList: [], //领用人
        glryList: [], //过滤领用人
        ypcdList: [], //材料产地
        rkd: {}, //入库单对象
        popContent: {},
        popContent1: {},
        dg: {
            page: 1,
            rows: 25,
            sort: "",
            order: "asc",
            parm: ""
        },
        them_tran: {},
        them: {
            '材料编号': 'ypbm',
            '材料名称': 'ypmc',
            '生产批号': 'scph',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'ypjj',
            '零价': 'yplj',
            '库房单位': 'kfdwmc',
            '二级库房单位': 'yfdwmc',
            '二级库房种类': 'ypzlmc',
        },

    },
    mounted: function () {

    },
    methods: {
        //关闭
        closes: function () {
            wrapper.editPdlr = false;
            wap.popContent = [];
            wap.inputYpmc = '';
            wap.type = true
        },
        open: function () {
            wapAddmx.type = true
            wap.type = false
        },


        //确定
        confirms: function () {
            wap.addData();
        },

        //材料名称下拉table检索数据
        changeDown: function (event) {
            this.keyCodeFunction(event, 'popContent1', 'searchCon');
            //选中之后的回调操作
            if (event.keyCode == 13) {
                this.popContent = this.popContent1
                this.nextFocus(event);
            }
        },

        changeDownDate: function (event, type) {
            //跳转下一个输入框
            if (event.keyCode == 13) {
                if (type != 'pdsjkc') {
                    this.nextFocus(event);
                } else {
                    this.addData();
                }
            }
        },
        //当输入值后才触发
        change: function (event, val) {
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.popContent.ypmc = val;
            this.dg.page = 1;
            this.dg.rows = 100;
            this.dg.parm = val;
            this.dg['yfbm'] = wrapper.yfSelected;
            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=selectPdlb&parm=' + JSON.stringify(wap.dg),
                function (data) {
                    if (data.a == 0) {
                        wap.searchCon = data.d.list;
                        wap.total = data.d.total;
                        wap.selSearch = 0;
                        if (data.d.list.length != 0) {
                            $(".selectGroup").hide();
                            _searchEvent.show()
                        } else {
                            $(".selectGroup").hide();
                        }
                    } else {
                        malert("材料检索失败!", 'top', 'defeadted');
                    }

                });
        },

        //双击选中下拉table
        selectOne: function (item) {
            //查询下页
            if (item == null) {
                //分页操作
                wap.dg.page++;
                wap.dg.parm = this.popContent.ypmc;
                $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(wap.dg),
                    function (data) {
                        if (data.a == 0) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                wap.searchCon.push(data.d.list[i]);
                            }
                            wap.total = data.d.total;
                            wap.selSearch = 0;
                        } else {
                            malert('材料检索失败', 'top', 'defeadted')
                        }

                    });
                return;
            }

            this.popContent = item;
            //此处材料价格为库房单位价格， 需要显示二级库房单位价格，所以处以分装比例
            // this.popContent.ypjj = this.popContent.ypjj/this.popContent.fzbl;
            // this.popContent.yplj = this.popContent.yplj/this.popContent.fzbl;
            this.inputYpmc = this.popContent.ypmc;
            $(".selectGroup").hide();

        },
        //盘点录入
        addData: function () {

            if (wrapper.yfSelected == undefined || wrapper.yfSelected == null || wrapper.yfSelected == "") {
                malert("二级库房不能为空!", 'top', 'defeadted');
                return;
            }

            if (this.popContent['ypmc'] == undefined || this.popContent['ypmc'] == null || this.popContent['ypmc'] == "") {
                malert("材料名称不能为空!", 'top', 'defeadted');
                return;
            }

            if (this.popContent['ypbm'] == undefined || this.popContent['ypbm'] == null || this.popContent['ypbm'] == "") {
                malert("材料编码不能为空!", 'top', 'defeadted');
                return;
            }

            if (this.popContent['ypgg'] == undefined || this.popContent['ypgg'] == null || this.popContent['ypgg'] == "") {
                malert("材料规格不能为空!", 'top', 'defeadted');
                return;
            }

            if (this.popContent['yplj'] == undefined || this.popContent['yplj'] == null || this.popContent['yplj'] == "") {
                malert("材料零价不能为空!", 'top', 'defeadted');
                return;
            }

            if (this.popContent['ypjj'] == undefined || this.popContent['ypjj'] == null || this.popContent['ypjj'] == "") {
                malert("材料进价不能为空!", 'top', 'defeadted');
                return;
            }

            // if (this.popContent['scph'] == undefined || this.popContent['scph'] == null || this.popContent['scph'] == "") {
            //     malert("生产批号不能为空!", 'top', 'defeadted');
            //     return;
            // }

            // if (this.popContent['cpbzh'] == undefined || this.popContent['cpbzh'] == null || this.popContent['cpbzh'] == "") {
            //     malert("产品标准号不能为空!", 'top', 'defeadted');
            //     return;
            // }

            // if (this.popContent['pzwh'] == undefined || this.popContent['pzwh'] == null || this.popContent['pzwh'] == "") {
            //     malert("批准文号不能为空!", 'top', 'defeadted');
            //     return;
            // }

            if (this.popContent['scrq'] == undefined || this.popContent['scrq'] == null || this.popContent['scrq'] == "") {
                malert("生产日期不能为空!", 'top', 'defeadted');
                return;
            }

            if (this.popContent['yxqz'] == undefined || this.popContent['yxqz'] == null || this.popContent['yxqz'] == "") {
                malert("有效期至不能为空!", 'top', 'defeadted');
                return;
            }

            // if (this.popContent['ghdw'] == undefined || this.popContent['ghdw'] == null || this.popContent['ghdw'] == "") {
            //     malert("供货单位不能为空!", 'top', 'defeadted');
            //     return;
            // }

            // if (this.popContent['cdbm'] == undefined || this.popContent['cdbm'] == null || this.popContent['cdbm'] == "") {
            //     malert("产地不能为空!", 'top', 'defeadted');
            //     return;
            // }

            if (this.popContent['pdzcsl'] == undefined || this.popContent['pdzcsl'] == null) {
                malert("账存数量不能为空!", 'top', 'defeadted');
                return;
            }

            if (this.popContent['pdsjkc'] == undefined || this.popContent['pdsjkc'] == null || this.popContent['pdsjkc'] == "" || this.popContent['pdsjkc'] <= 0) {
                malert("实存数量不能为空!", 'top', 'defeadted');
                return;
            }
			var json = {
				ypbm:this.popContent['ypbm'],
				yfbm:wrapper.yfSelected,
				xtph:this.popContent['xtph'],
				scph:this.popContent['scph'],
				scrq:this.popContent['scrq'],
				yxqz:this.popContent['yxqz'],
				yplj:this.popContent['yplj'],
				ypjj:this.popContent['ypjj'],
				cdbm:this.popContent['cdbm'],
				ghdw:this.popContent['ghdw'],
				kfdw:this.popContent['kfdw'],
				yfdw:this.popContent['yfdw'],
				fzbl:this.popContent['fzbl'],
				cpbzh:this.popContent['cpbzh'],
				pzwh:this.popContent['pzwh'],
				pdzcsl:this.popContent['pdzcsl'],
				pdsjkc:this.popContent['pdsjkc'],
				yfyppcid:this.popContent['yfyppcid'],
				ifpdlr:0
			}

            if (!wrapper.editPdlr) {
                json.lrty = '1';
            }

            if (wrapper.editPdlr) {
                json.id = this.popContent['id'];
            }

            $.getJSON('/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=updatePdmxYpKc&parm=' + JSON.stringify(json),
                function (data) {
                    if (data.a == 0) {
                        common.closeLoading()

                        if (wrapper.editPdlr) {
                            wap.closes();
                            wapAddmx.closes();
                        }
                        wrapper.getPdSC();
                        wap.popContent = [];
                        wap.inputYpmc = '';
                        wap.$refs.pdlrYpmc.focus();
                        // malert(data.c, "top",'success');

                    } else {
                        common.closeLoading()
                        malert(data.c, "top", 'defeadted');
                    }
                });
        },

    }
});

//在当前盘点单上新增明细
var wapAddmx = new Vue({
    el: '#pdxzSide',
    mixins: [dic_transform, baseFunc, tableBase, mformat],
    components: {
        'search-table': searchTable
    },
    data: {
        type: true,
        ghdwList: [], //供货单位
        ypmcInput: '',
        popContent: {},
        searchCon: [],
        selSearch: -1,
        page: {
            page: 1,
            rows: 10,
            total: null
        },
        them_tran: {},
        them: {
            '生产批号': 'scph',
            '材料编号': 'ypbm',
            '材料名称': 'ypmc',
            '规格': 'ypgg',
            '分装比例': 'fzbl',
            '进价': 'ypjj',
            '零价': 'yplj',
            '库房单位': 'kfdwmc',
            '二级库房单位': 'yfdwmc',
            '材料剂型': 'jxmc'
        }

    },
    mounted: function () {
    },
    watch: {},
    methods: {
        //关闭
        closes: function () {
            wrapper.editPdlr = false;
            wapAddmx.popContent = [];
            wapAddmx.ypmcInput = '';
            wapAddmx.type = true

        },
        open: function () {
            wap.type = true;
            wapAddmx.type = false
        },

        //获取供货单位
        getGhdw: function () {
            //初始化页面记载供货单位
            var GhdwDg = {
                page: 1,
                rows: 500,
                sort: "dwbm",
                order: "asc",
                parm: ''
            };
            $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhGhdw&types=query&json=" + JSON.stringify(GhdwDg),
                function (json) {
                    if (json.a == 0) {
                        wapAddmx.ghdwList = json.d.list;
                    } else {
                        malert("供货单位获取失败", 'top', 'defeadted');
                    }
                });
        },

        getcdData: function () {
            //初始化页面加载产地编码
            var obj = {
                rows: 3000,
                sort: 'cdbm',
                tybz: '0',
                page: 1,
                parm: '',
            }
            this.$nextTick(function () {
                $.getJSON("/actionDispatcher.do?reqUrl=New1YkglKfwhCdbm&types=query&dg=" + JSON.stringify(obj),
                    function (data) {

                        if (data.a == 0) {
                            wapAddmx.ypcdList = Object.freeze(data.d.list);
                            wapAddmx.$forceUpdate()
                            setTimeout(function () {
                                wapAddmx.$refs.cdbm.setLiShow(wapAddmx.$refs.cdbm.$refs.inputFu)
                            }, 100)
                        } else {
                            malert("材料产地获取失败!", 'top', 'defeadted');
                        }
                    });
            })
        },


        //盘点新增
        addNewYp: function () {
            if (wrapper.editPdlr) {
                wap.popContent = wapAddmx.popContent;
                wap.addData();
                wapAddmx.popContent = [];
                wapAddmx.ypmcInput = '';

            } else {
                if (wrapper.yfSelected == undefined || wrapper.yfSelected == null || wrapper.yfSelected == "") {
                    malert("二级库房不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['ypmc'] == undefined || this.popContent['ypmc'] == null || this.popContent['ypmc'] == "") {
                    malert("材料名称不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['ypbm'] == undefined || this.popContent['ypbm'] == null || this.popContent['ypbm'] == "") {
                    malert("材料编码不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['ypgg'] == undefined || this.popContent['ypgg'] == null || this.popContent['ypgg'] == "") {
                    malert("材料规格不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['yplj'] == undefined || this.popContent['yplj'] == null || this.popContent['yplj'] == "") {
                    malert("材料零价不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['ypjj'] == undefined || this.popContent['ypjj'] == null || this.popContent['ypjj'] == "") {
                    malert("材料进价不能为空!", 'top', 'defeadted');
                    return;
                }

                // if (this.popContent['scph'] == undefined || this.popContent['scph'] == null || this.popContent['scph'] == "") {
                // 	malert("生产批号不能为空!", 'top', 'defeadted');
                // 	return;
                // }

                // if (this.popContent['cpbzh'] == undefined || this.popContent['cpbzh'] == null || this.popContent['cpbzh'] == "") {
                //     malert("产品标准号不能为空!", 'top', 'defeadted');
                //     return;
                // }

                // if (this.popContent['pzwh'] == undefined || this.popContent['pzwh'] == null || this.popContent['pzwh'] == "") {
                //     malert("批准文号不能为空!", 'top', 'defeadted');
                //     return;
                // }

                // if (this.popContent['scrq'] == undefined || this.popContent['scrq'] == null || this.popContent['scrq'] == "") {
                //     malert("生产日期不能为空!", 'top', 'defeadted');
                //     return;
                // }

                if (this.popContent['yxqz'] == undefined || this.popContent['yxqz'] == null || this.popContent['yxqz'] == "") {
                    malert("有效期至不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['ghdw'] == undefined || this.popContent['ghdw'] == null || this.popContent['ghdw'] == "") {
                    malert("供货单位不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['cdbm'] == undefined || this.popContent['cdbm'] == null || this.popContent['cdbm'] == "") {
                    malert("产地不能为空!", 'top', 'defeadted');
                    return;
                }

                if (this.popContent['pdsjkc'] == undefined || this.popContent['pdsjkc'] == null || this.popContent['pdsjkc'] == "") {
                    malert("实存数量不能为空!", 'top', 'defeadted');
                    return;
                }

                var json = {
                    ypbm: this.popContent['ypbm'],
                    yfbm: wrapper.yfSelected,
                    xtph: this.popContent['xtph'],
                    scph: this.popContent['scph'],
                    scrq: this.popContent['scrq'],
                    yxqz: this.popContent['yxqz'],
                    yplj: this.popContent['yplj'],
                    ypjj: this.popContent['ypjj'],
                    cdbm: this.popContent['cdbm'],
                    ghdw: this.popContent['ghdw'],
                    kfdw: this.popContent['kfdw'],
                    yfdw: this.popContent['yfdw'],
                    fzbl: this.popContent['fzbl'],
                    cpbzh: this.popContent['cpbzh'],
                    pzwh: this.popContent['pzwh'],
                    pdsjkc: this.popContent['pdsjkc'],
                    yfyppcid: this.popContent['yfyppcid'],
                    ifpdlr: 1
                }

                $.getJSON('/actionDispatcher.do?reqUrl=New1YfbCxtjAll&types=insertPdmx&parm=' + JSON.stringify(json),
                    function (data) {
                        if (data.a == 0) {
                            common.closeLoading()
                            wrapper.getPdSC();
                            wapAddmx.popContent = [];
                            wapAddmx.ypmcInput = '';
                            wapAddmx.$refs.pdxzYpmc.focus();

                        } else {
                            common.closeLoading()
                            malert(data.c, "top", 'defeadted');
                        }
                    });
            }
        },

        //材料下拉检索
        changeDown: function (event, type) {
            if (this['searchCon'][this.selSearch] == undefined) return;
            this.keyCodeFunction(event, 'popContent', 'searchCon');
            //选中之后的回调操作
            if (event.code == 'Enter' || event.code == 13 || event.code == 'NumpadEnter') {

                if (type == 'ypmc') {
                    $('#scsl').focus();
                }
            }
        },

        //当输入值后才触发
        change: function (add, val) {
            this.ypmcInput = val;
            if (!add) this.page.page = 1;       // 设置当前页号为第一页
            var _searchEvent = $(event.target.nextElementSibling).eq(0);
            this.page.parm = this.ypmcInput;
            var str_param = {parm: this.page.parm, page: this.page.page, rows: this.page.rows};
            var kfbm = '';
            if (wrapper.yfSelected == '1' || wrapper.yfSelected == '3') {
                kfbm = '1';
            } else if (wrapper.yfSelected == '2') {
                kfbm = '2';
            } else if (wrapper.yfSelected == '4') {
                kfbm = '6';
            } else if (wrapper.yfSelected == '5') {
                kfbm = '7';
            }

            var bean = {
                'pdpzh': wrapper.pzNum,
                'kfbm': kfbm
            };
            $.getJSON('/actionDispatcher.do?reqUrl=GetDropDown&types=ypzd&dg=' + JSON.stringify(str_param) + "&parm=" + JSON.stringify(bean),
                function (data) {
                    if (data.d.list.length > 0) {
                        if (add) {
                            for (var i = 0; i < data.d.list.length; i++) {
                                data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                                data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                                wapAddmx.searchCon.push(data.d.list[i]);
                            }
                        } else {
                            for (var i = 0; i < data.d.list.length; i++) {
                                data.d.list[i]['yxqz'] = formatTime(data.d.list[i]['yxqz'], 'date');
                                data.d.list[i]['scrq'] = formatTime(data.d.list[i]['scrq'], 'date');
                            }
                            wapAddmx.searchCon = data.d.list;
                        }
                    }
                    wapAddmx.page.total = data.d.total;
                    wapAddmx.selSearch = 0;
                    if (data.d.list.length > 0 && !add) {
                        $(".selectGroup").hide();
                        _searchEvent.show();
                    }
                });
        },

        //鼠标双击
        selectOne: function (item) {
            if (item == null) {                 // 如果item的值为null,就表示加载更多（请求下一页的内容、page++）
                this.page.page++;  // 设置当前页号
                this.change(true, this.ypmcInput);           // 传参表示请求下一页,不传就表示请求第一页
            } else {
                this.popContent = item;
                this.ypmcInput = item.ypmc;
                $(".selectGroup").hide();
            }
        },
        changeDownDate: function (event, type) {
            // if (!event.target.value) {
            //     malert('请输入' + (type == '_yxqz' ? '有效期' : '生产日期'), 'top', 'defeadted');
            //     //触发时间控件
            //     event.target.click();
            //     return;
            // } else {
            //     //激活插件检测校验时间
            //     event.target.blur();
            // }
            // //判断格式
            // var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
            // var regExp = new RegExp(reg);
            // if (!regExp.test(event.target.value)) {
            //     malert("日期格式不正确，正确格式为：YYYY-MM-HH", 'top', 'defeadted');
            //     return;
            // }
            //跳转下一个输入框
            if (event.keyCode == 13) {

                if (type != 'pdsjkc') {
                    this.nextFocus(event);
                } else {
                    wapAddmx.addNewYp();
                }
                if (type == 'yplj') {

                    wapAddmx.popContent.ypjj = wrapper.fDec(wapAddmx.popContent.yplj / wapAddmx.popContent.jcbl * wapAddmx.popContent.fzbl, 4);
                }
            }
        },
        changeDate: function (event, type) {

            //选中之后的回调操作
            if (window.event.keyCode == 13) {
                //获取时间控件对象
                var dateObj = document.getElementById(type);
                //获取日期
                var tempDate = dateObj.value;
                //非空判断
                if (tempDate == null || tempDate == undefined || tempDate == '') {
                    malert('请输入' + (type == '_yxqz' ? '有效期' : '生产日期'), 'top', 'defeadted');
                    //触发时间控件
                    dateObj.click();
                    return;
                } else {
                    //激活插件检测校验时间
                    dateObj.blur();
                }
                //判断格式
                var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
                var regExp = new RegExp(reg);
                if (!regExp.test(tempDate)) {
                    malert("日期格式不正确，正确格式为：2017-01-01", 'top', 'defeadted');
                    return;
                }
                if (type != 'pdsjkc') {
                    //跳转下一个输入框
                    this.nextFocus(event);
                } else {
                    wapAddmx.addNewYp();
                }
            }
        },
    }


});

laydate.render({
    elem: '.times1'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        wapAddmx.popContent.scrq = value
    }
});
laydate.render({
    elem: '.times2'
    , trigger: 'click'
    , theme: '#1ab394',
    done: function (value, data) {
        wapAddmx.popContent.yxqz = value
    }
});

//改变vue异步请求传输的格式
Vue.http.options.emulateJSON = true;
wrapper.getYpzlData();
wrapper.getYFData();
wapAddmx.getGhdw();
wapAddmx.getcdData();

//监听记账项目检索外的点击事件 ，自动隐藏.selectGroup
$(document).mouseup(function (e) {
    var bol = $(e.target).parents().is(".selectGroup");
    if (!bol) {
        $(".selectGroup").hide();
    }

});



