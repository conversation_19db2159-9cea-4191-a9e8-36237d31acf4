<!DOCTYPE html>
<html lang="en">

<head>
    <link href="brPage/user.css" rel="stylesheet">
    <link rel="stylesheet" href="/pub/css/print.css" media="print" />
    <style>
        .zui-select-inline, .zui-table-view .zui-table-cell {
            height: unset;
            vertical-align: top;
        }
        .hzList input{
            height: 28px;
        }
        .border-none{
            border: none;
        }
        .disabled-btn {
            background-color: #ccc !important;  /* 变灰 */
            color: #666 !important;  /* 文字变浅 */
            cursor: not-allowed !important;  /* 禁用鼠标 */
        }
    </style>
</head>

<body>
    <div class="printArea printShow"></div>
    <div class="wrapper printHide background-f border-none" >
        <div class="wrapper border-none" id="yzcl">
            <div class="tong-search">
                <div class="zui-form">

                    <div class="zui-inline">
                        <div style="float:left;" v-if='Brxx_List.jbbm'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;"  :type="'some'" @result="reCheckBoxZyh" :which="90" :val="iszdchecked['90']"></input-checkbox>{{Brxx_List.jbmc}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" class="padd-r-10 padd-b-10 cursor" :type="'some'" @result="reCheckBoxZyh" :which="'91'" :val="iszdchecked['91']"></input-checkbox>{{Brxx_List.qtzdmc}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm1'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'92'" :val="iszdchecked['92']"></input-checkbox>{{Brxx_List.qtzdmc1}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm2'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'93'" :val="iszdchecked['93']"></input-checkbox>{{Brxx_List.qtzdmc2}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm3'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;"  :type="'some'" @result="reCheckBoxZyh" :which="'94'" :val="iszdchecked['94']"></input-checkbox>{{Brxx_List.qtzdmc3}}</div>
                        <div style="float:left;" v-if='Brxx_List.qtzdbm4'><input-checkbox style="float: left;padding-top: 1px;padding-right: 5px;" :type="'some'" @result="reCheckBoxZyh" :which="'95'" :val="iszdchecked['95']"></input-checkbox>{{Brxx_List.qtzdmc4}}</div>
                        <template v-for="(item, $index) in fjzdlist">
                            <div style="float:left;" ><input-checkbox style="float: left; margin-left: 17px; padding-top: 1px;padding-right: 5px;" style="padd-r-10 padd-b-10 cursor" :type="'some'" @result="reCheckBoxZyh" :which="$index" :val="iszdchecked[$index]"></input-checkbox>{{item.jbmc}}</div>
                        </template>
                        <div class="flex-container flex-align-c padd-r-10 padd-b-10">
                            <button v-waves class="tong-btn btn-parmary height-28  margin-l-10" style="margin-top: -4px;" @click="openFjzd()">添加诊断</button>
                            <div style="float:left; margin-left: 17px;" v-if="Brxx_List.ghksmc =='急诊科'">
                                <input  type="checkbox" style="float: left;padding-top: 1px;padding-right: 5px; width: 16px; height: 16px; font-size: 12px; margin-right: 2px;"
                                        :checked="isHff" @change="isXzlhff($event.target.checked)"/>
                                先诊疗后付费
                            </div>
                        </div>
                    </div>

                    <template v-if="Brxx_List.fbbm=='40' || Brxx_List.fbbm=='41'">

                        <div class="zui-inline">
                            <span class="zui-form-label">病种编码</span>
                            <div class="zui-input-inline wh45" style="width: 18em;">
                                <input class="zui-input" data-notEmpty="false" v-model="popContent.mtbzmc"
                                       @keydown="mtchangeDown($event,'jbMT','mContent','msearchCon','mtbzbm','mtbzbm','mtbzmc','mtselSearch4')"
                                       @input="mtchange(false,'jbMT',$event.target.value,'mtselSearch4','mtbzmc','mtbzbm',$event)">
                                <jbsearch-table :message="msearchCon" :selected="mtselSearch4" :page="page" :them="mthem"
                                                @click-one="checkedOneOut"
                                                @click-two="mselectOne">
                                </jbsearch-table>
                                <!--<input type="text" class="zui-input background-h" v-model="lczd" disabled v-if="InShow==false"/>-->
                            </div>
                        </div>
                    </template>




                </div>
            </div>
            <div class="dzcf brjz-bottom" :data-money="money">
                <!--style="position: fixed;background: #fff;z-index: 111;left: 20px;right: 10px;width: inherit"-->
                <div class="flex-container flex-jus-sb" >
                    <div class="scrollpic flex-container">
                        <i class="tab-left" @click="move('right')" v-if="jcxm_List.length>=6" id="left"><<</i>
                        <div id="myscroll">
                            <div id="myscrollbox" class="over-auto flex-container">
                                <ul >
                                    <li class="padd-r-5 padd-l-5" v-for="(item,$index) in jcxm_List" :data-id="JSON.stringify(item)" :class="{'active':$index==num}"
                                    @click="Wf_selectCFJcxmMx(item.yzhm,$index)">
                                    <a class="flex-container flex-jus-c flex-align-c">{{$index + 1}}检查检验
                                        <i class="icon-iocn26 iconfont  padd-l-5" v-if="item.kfbz==1" data-title="已收费"></i>
                                        <i v-if="item.zfbz==1" data-title="已作废" class="icon-iocn15 iconfont  padd-l-5 padd-r-5"></i>
                                    </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <span class="brjz-add" @click="addCf($event)">+</span>
                        <i class="tab-right" v-if="jcxm_List.length>=6" @click="move('left')" id="right">>></i>
                    </div>
                    <ul class="brjz-right-list"  style="width: 200px;">
                        <li @click="addYh"><i class="icon-width icon-tops icon-xz-h"></i><i class="">新增一行</i></li>
                        <!-- <li @click="Cfdy"><i class="icon-width icon-top icon-kz"></i><i class="padd-l-10">添加项目</i></li>-->
                        <li class="cfdyhover"><i class="icon-width icon-top icon-kz"></i><i class="">处方调用</i>
                            <span class="dzcf-erj">
                                <a @click="Cfdy">处方模板</a>
                                <a @click="Lscf">历史处方</a>
                            </span>

                        </li>
                    </ul>

                </div>
                <div class="fyxm-size flex-container">
                    <div class="zui-table-view flex-one">
                        <div class="zui-table-header">
                            <table class="zui-table table-width50" :style="{backgroundColor: ishff === '1' ? '#8de4ce' : '' }">
                                <thead>
                                    <tr>
                                        <th class="cell-m">
                                            <div class="zui-table-cell cell-m"><span>序号</span></div>
                                        </th>
                                        <th class=" " v-if="csqxContent.N03001200140 == '1'">
                                            <div class="zui-table-cell  cell-s"><span>是否急诊</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell text-left" style="min-width: 376px"><span>项目名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell"  style="width: 150px"><span>组合名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>数量</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>单价</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>金额</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>已优惠金额</span></div>
                                        </th>
                                        <th v-if="sfxxjzyy">
                                            <div class="zui-table-cell cell-l"><span>开嘱时间</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>执行科室</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl"><span>描述说明</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>备注说明</span></div>
                                        </th>
                                        <th class="cell-m" v-show="addShow[num]">
                                            <div class="zui-table-cell cell-m"><span>操作</span></div>
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                        <div class="zui-table-body " @scroll="scrollTable($event)">
                            <table class="zui-table table-width50">
                                <tbody>
                                    <!-- @click="hover($index,$event),checkSelect([$index,'some','jcxmMxJson'],$event)" -->
                                    <tr v-for="(item, $index) in jcxmMxJson" v-if="getIfShow(item.sftf)" class="tableTr2" :tabindex="$index"
                                        :data-change="checkAll" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'some','jcxmMxJson'],$event)"
                                        ref="list">
                                        <td class="cell-m">
                                            <div class="zui-table-cell cell-m" v-text="$index+1"></div>
                                        </td>
                                        <td  v-if="csqxContent.N03001200140 == '1'">
                                            <div class=" yzgl-no-pad cell-s">
                                                <select-input @change-data="commonResultChange" :not_empty="false" :child="istrue_tran" :id="'sfjz' + $index"
                                                              :index="'sfjz'" :val="item.sfjz" :name="'jcxmMxJson.' + $index + '.sfjz'" readonly="readonly"
                                                              :search="false">
                                                </select-input>
                                            </div>
                                        </td>
                                        <td :class="$index | zhfybm( jcxmMxJson )" style="min-width: 376px">
                                            <div class="zui-table-cell text-over-2 text-left flex-container" v-if="!addShow[num]"
                                                v-text="item.mxfyxmmc" ></div>
                                            <div over-auto  class="zui-table-cell flex-container  text-left"
                                                v-if="addShow[num]">
                                                <input :disabled="item.disabled" class="zui-input" style="width: 276px" v-model="item.mxfyxmmc"
                                                    @keyup="changeDown($index,$event,'mxfyxmmc','searchCon')" :id="'mxfyxmmc' + $index"
                                                    @input="Wf_change(false,$index,'mxfyxmmc', $event.target.value)" />
                                                <search-table :message="searchCon" :selected="selSearch" :page="page"
                                                    :them="them" :them_tran="them_tran" @click-one="checkedOneOut"
                                                    @click-two="selectOne"></search-table>
                                                <span class="title-width">{{item.fygg}}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div  style="width: 150px" class="zui-table-cell dzcf-cell ">
                                                <span>
                                                    <div>{{$index | zhfybm( jcxmMxJson )  == 'tz-start' ? item.zhfymc : ''}}</div>
                                                    <div>{{$index | zhfybm( jcxmMxJson )  == 'tz-single' ? item.zhfymc : ''}}</div>
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                        	<div class="zui-table-cell dzcf-cell cell-s">
                                                <span>
<!--                                                    nextYhFocus-->
                                                    <div v-if="!addShow[num]" v-text="item.fysl"></div>
                                                    <input  v-if="addShow[num]"  v-model="item.fysl" @mousewheel.prevent @input="Wf_fyjeChange($index)"
                                                    type="number" @keydown="nextFocus($event)" class="zui-input " name="text" />
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell dzcf-cell cell-s">
                                                <span>
                                                    <div v-if="!addShow[num]" v-text="item.fydj"></div>
                                                    <input v-model="item.fydj" @mousewheel.prevent @input="Wf_fyjeChange($index)"
                                                        type="number" @keydown="nextYhFocus($event,$index)" class="zui-input "
                                                        v-if="addShow[num]" :name="item.sfgd == '1' && csqxContent.N03001200139 == '1' ? '' : 'text'"
                                                        :disabled="item.sfgd == '1' && csqxContent.N03001200139 == '1' ? true : false"/>
                                                </span>
                                                <span>元</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell dzcf-cell cell-s">
                                                <span>
<!--                                                    <div v-text="item.fyje"></div>-->
                                                    <div v-text="(item.fysl) * (item.fydj)"></div>
                                                </span>
                                                <span>元</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell dzcf-cell cell-s">
                                                <span>
                                                    <div v-text="item.yhje"></div>
                                                </span>
                                                <span>元</span>
                                            </div>
                                        </td>
                                        <td  v-if="sfxxjzyy">
                                            <div class="zui-table-cell cell-l">
                                                <input autocomplete="off" style="text-indent: 0;" type="text" class="zui-input" :class="'jzyyrq'+$index" :id="'jzyyrq'+$index"
                                                       v-model="fDate(item.jzyyrq,'AllDate')" data-select="no" @click="showJzyyDate($index,$event)"    />
                                            </div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-if="!addShow[num]" v-text="item.zxksmc"></div>
                                            <div over-auto class="zui-table-cell cell-l" v-if="addShow[num]">
                                                <select-input @change-data="resultChange_zxks" :not_empty="false"
                                                    :child="zxksList" :index="'ksmc'" :index_val="'ksbm'" :val="item.zxks"
                                                    :name="'jcxmMxJson.'+$index+'.zxks'" :index_mc="'zxksmc'" :search="true">
                                                </select-input>

                                            </div>
                                        </td>
                                        <td>
<!--                                            v-show="addShow==false"-->
<!--                                            onclick="Djck(this)"-->
                                            <div class="zui-table-cell cell-xl " @click="fybzClick($index)" >点击查看</div>
<!--                                            <div class="zui-table-cell cell-xl " v-show="addShow">-->
<!--                                                <input type="text"-->
<!--                                                    class="zui-input wh150 " @click="fybzClick($index)" onclick="Djck(this)"-->
<!--                                                    name="text" @keydown="nextFocus($event)" /></div>-->
                                            <!--<fyp-input v-show="fybzShow[$index]" :text="'text'" :list="objData" :type="'type'" :yfmc="'yfmc'" :json="jsonList"  :name="'popConetent.yfbm'" @change="resultchange"   @goBack="goBack"></fyp-input>-->
                                            <fyp-input v-if="fybzShow[$index]" :text="'text'" :list="objData" :type="'type'" @savego="savego"></fyp-input>
                                            <!--<fyp-input v-show="fybzShow[$index]" :text="'text'" :list="objData" @goBack="goBack"></fyp-input>-->
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-if="!addShow[num]" v-text="item.bzsm"></div>
                                            <div class="zui-table-cell cell-l" v-if="addShow[num]">
                                                <input type="text" class="zui-input  " v-model="item.bzsm" name="text"
                                                    @keydown.13="hcpd($event,$index)" data-notEmpty="false" :id="'bzsm' + $index" />
                                            </div>
                                        </td>
                                        <td v-show="addShow[num]" class="cell-m">
                                            <div class="zui-table-cell cell-m">
                                                <i class="icon-sc icon-font" @click="removeNow($index,item.zhfybm)"
                                                    data-title="删除"></i>
                                            </div>
                                        </td>
                                        <p class="noData  text-center zan-border" v-if="jcxmMxJson.length==0"> 暂无数据</p>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="zui-table-fixed table-fixed-l" v-if="addShow">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                        <tr>
                                            <th class="cell-m">
                                                <div class="zui-table-cell cell-m"><span>序号</span> <em></em></div>
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                                <table class="zui-table">
                                    <tbody>
                                        <tr v-for="(item, $index) in jcxmMxJson" v-if="getIfShow(item.sftf)" :tabindex="$index" class="tableTr2"
                                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                            @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'one','jcxmMxJson'],$event)"
                                            ref="list">
                                            <td class="cell-m">
                                                <div class="zui-table-cell cell-m">{{$index+1}}</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="zui-table-fixed table-fixed-r" v-if="addShow">
                            <div class="zui-table-header">
                                <table class="zui-table">
                                    <thead>
                                        <tr>
                                            <th class="cell-m">
                                                <div class="zui-table-cell cell-m"><span>操作</span></div>
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTableFixed($event)">
                                <table class="zui-table">
                                    <tbody>
                                        <tr v-for="(item, $index) in jcxmMxJson" v-if="getIfShow(item.sftf)" :tabindex="$index" class="tableTr2"
                                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                            @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" @click="checkSelect([$index,'one','jcxmMxJson'],$event)"
                                            ref="list">
                                            <td class="cell-m">
                                                <div class="zui-table-cell cell-m">
                                                    <i class="icon-sc icon-font" @click="removeNow($index,item.zhfybm)"
                                                        data-title="删除"></i>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="action-bar fixed brjz-foot">
                <div class="flex-container">
                    <!--<button class="tong-btn btn-parmary-d9">取消</button>-->
                    <!--<button class="tong-btn  btn-parmary-not feise-bg"  @click="saveShow?edit():save()" v-text="editTitle"></button>-->
                    <button v-waves class="tong-btn btn-parmary  xmzb-db" v-if="isBshow" @click="removeCf">作废</button>
                    <button v-waves class="tong-btn btn-parmary xmzb-db" @click="printJc(1)">打印/检验/检查/治疗</button>
                    <button v-waves class="tong-btn btn-parmary xmzb-db" v-if="isBshow" @click="saveModel">保存为模板</button>
                    <button v-waves v-if="N03001200143 == '1' && isBshow" class="tong-btn btn-parmary xmzb-db" @click="editFypcf()">编辑</button>
                    <button v-waves class="tong-btn btn-parmary xmzb-db" v-if="isBshow" @click="save()">保存</button>
                    <button v-waves class="tong-btn btn-parmary  xmzb-db" @click="jcjyOpen">检查/检验</button>
                    <!--<button v-waves class="tong-btn btn-parmary  xmzb-db" @click="openUrl()">检查报告单查看</button>-->
                    <button v-waves class="tong-btn btn-parmary  xmzb-db" @click="openPacsBG">PACS</button>
					<button v-waves class="tong-btn btn-parmary  xmzb-db" @click="openLisBG">LIS结果</button>
                    <button v-waves class="tong-btn btn-parmary xmzb-db"  :class="{'disabled-btn': !hasHrxxData}" :disabled="!hasHrxxData" @click="openHrxx()">互认信息</button>
                    <button v-waves class="tong-btn btn-parmary  xmzb-db" v-if="N03001200164" @click="openUrl(true)">影像查看</button>
                    <button v-waves class="tong-btn btn-parmary  xmzb-db" v-if="showECG" @click="openUrlECG">心电报告查看</button>
<!--                    <button v-waves class="tong-btn  btn-parmary  xmzb-db" @click="ybjsd">医保结算单</button>-->
<!--                    <button v-waves class="tong-btn  btn-parmary  xmzb-db" @click="ybmxcx">医保明细查询</button>-->
                </div>
                <div>
                    <i>处方医生：<em class="color-dsh" v-text="msgContent.cfysxm"></em> </i>
                    <i>处方编号：<em class="color-dsh" v-text="msgContent.cfh"></em> </i>
                    <i class="padd-l-15" >费用金额:<em class="color-dsh" v-text="fDec(msgContent.fyhj,2)"></em>元</i>
                </div>
            </div>

        </div>
        <div class="side-form pop-width pop-805" :class="{'ng-hide':nums==1}" id="brzcList" role="form">
            <div class="fyxm-side-top">
                <span v-text="title"></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>

            <div class="ksys-side" v-show="false">
                <div class="zui-form">
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label">项目大类</label>
                        <div class="zui-input-inline padd-l-25 wh22">
                            <input class="zui-input  " type="text" />
                        </div>
                    </div>
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label">项目类型</label>
                        <div class="zui-input-inline padd-l-25 wh22">
                            <input class="zui-input  wh240 " type="text" />
                            <!--<select-input @change-data="xmlxchange"
                                      :child="fzh_tran" :index="ffylb" :val="ffylb"
                                      :name="'ffylb'">
                        </select-input>-->

                        </div>

                    </div>
                    <div class="zui-inline padd-l-40">
                        <label class="zui-form-label">诊断</label>
                        <div class="zui-input-inline padd-l-25">
                            <input class="zui-input  wh240 " type="text" />
                        </div>
                    </div>
                </div>
                <div class="zui-form" style="border-top: 1px #1abc9c dashed; padding-top: 15px;">
                    <div class="zui-inline padd-l-40" style="width: 100%">
                        <label class="zui-form-label">检查目的</label>
                        <div class="zui-input-inline padd-l-25">
                            <input class="zui-input  " type="text" />
                        </div>

                    </div>
                    <div class="zui-inline padd-l-40" style="width: 100%">
                        <label class="zui-form-label">部位</label>
                        <div class="zui-input-inline padd-l-25">
                            <input class="zui-input  " type="text" />
                        </div>

                    </div>
                    <div class="zui-inline padd-l-40" style="width: 100%">
                        <label class="zui-form-label">备注</label>
                        <div class="zui-input-inline padd-l-25">
                            <input class="zui-input  " type="text" />
                        </div>

                    </div>
                </div>

                <div class="fypcf-list" v-show="false">
                    <div class="fypcf-gou">检验项目（请在申请检验的项目前打“ <i class="color-green">√</i>”）</div>
                    <div class="fypcf-check">
                        <ul>
                            <li v-for="(item,$index) in 10">
                                <span>
                                    <i class="wh40">
                                        <input type="checkbox" id="check1_0" class="zui-checkbox"><label for="check1_0"></label>

                                    </i>
                                    <i>c-态势放食盐</i>
                                </span>
                                <span class="color-dsh">¥88元</span>
                            </li>
                        </ul>
                        <ul class="fr">
                            <li v-for="(item,$index) in 10">
                                <span>
                                    <i class="wh40">
                                        <input type="checkbox" id="check1_1" class="zui-checkbox"><label for="check1_1"></label>

                                    </i>
                                    <i>c-态势放食盐</i>
                                </span>
                                <span class="color-dsh">¥88元</span>
                            </li>
                        </ul>
                    </div>

                </div>

            </div>

            <!--过滤条件-->
            <div style="padding:15px 10px 5px" class="flex-container padd-l-10">
                <div class="zui-inline" v-show="zdShow" style="padding: 0 20px 0 20px;">
                    <button v-waves class="tong-btn btn-parmary icon-xz1 paddr-r5" @click="addcfZd">新增</button>
                </div>
                <div class="flex-container flex-align-c"  v-show="lsShow && lscfcxqx==0">
                    <span class="whiteSpace ft-14 padd-r-5">时间</span>
                    <div class="flex-container position  flex-align-c">
                        <i class="icon-position icon-rl"></i>
                        <input class="zui-input todate wh180 text-indent20" placeholder="请选择处方开始时间" id="timeVal"/><span
                            class="padd-l-5 padd-r-5">~</span>
                        <input class="zui-input todate wh150 " placeholder="请选择处方结束时间" id="timeVal1"/>
                    </div>
                </div>
                <div class="flex-container flex-align-c padd-l-5" v-if="lsShow">
                    <span class="whiteSpace ft-14 padd-r-5">个人</span>
                    <select-input style="width: 70px;" @change-data="getCxqxChange" :not_empty="false"
                                  :child="istrue_tran" :index="lscfcxqx" :val="lscfcxqx" :name="'lscfcxqx'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-l-5" v-if="cfShow">
                    <span class="whiteSpace ft-14 padd-r-5">来源</span>
                    <select-input style="width: 70px;" @change-data="resultlyChange" :not_empty="false"
                                  :child="cfyyly_tran" :index="cfyylyidx" :val="cfyylyidx" :name="'cfyylyidx'">
                    </select-input>
                </div>
                <div class="flex-container flex-align-c padd-l-30">
                    <span class=" whiteSpace ft-14 padd-r-5">搜索</span>
                    <input class="zui-input wh182" v-if="cfShow" placeholder="请输入关键字" type="text" v-model="cfdyjs"
                           @keyup.13="getTemData($event)"/>
                    <input class="zui-input wh182" v-if="ypShow" placeholder="请输入关键字" @keyup="Wf_YpChange($event)"
                           v-model="jsypxx"
                           type="text" id="searchYp"/>
                    <input class="zui-input wh120" v-if="lsShow" placeholder="请输入关键字" type="text" v-model="lscfjs"
                           @keyup.13="getLsTemData($event)"/>
                    <input class="zui-input wh182" v-if="zdShow" placeholder="请输入关键字" type="text" v-model="lczdValue"
                           @keyup.13="getLczdDate($event)"/>
                </div>
            </div>

            <!--处方调用-->
            <div v-if="cfShow" class="ksys-side hzgl-height flex-container">
                <div class="col-x-12 flex-one">
                    <div class="col-x-5 hzgl-wiwidth-one flex-one flex-container">
                        <div class="zui-table-view over-auto zui-item " v-cloak style="border:none;padding: 0 10px; ">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>编码</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>治法</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>主治</span></div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                                <table class="zui-table table-width50">
                                    <tbody>
                                    <tr v-for="(item, $index) in cfList" :tabindex="$index" ref="list"
                                        :class="[{'table-hovers':$index===activeIndex1,'table-hover':$index === hoverIndex1}]"
                                        @mouseenter="hoverMouse1(true,$index)" @mouseleave="hoverMouse1()"
                                        @click="getTemMxData($index)"
                                        @dblclick="addCfMb($index)">
                                        <td>
                                            <div class="zui-table-cell cell-s " v-text="item.zhyzbm"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l text-over-2" v-text="item.zhyzmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s text-over-2" v-text="item.zf"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s text-over-2" v-text="item.zz"></div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <!--<p v-if="yzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                            </div>
                        </div>

                    </div>
                    <div class="col-x-7 margin-l-10 hzgl-wiwidth-two">
                        <div class="zui-table-view">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-m">
                                                <input-checkbox @result="reCheckBox" :list="'cfMxList'" :type="'all'"
                                                                :val="isCheckAll">
                                                </input-checkbox>
                                            </div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-xl text-left"><span>项目名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>组合名称</span></div>
                                        </th>

                                        <th>
                                            <div class="zui-table-cell cell-s"><span>单价</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>数量</span></div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTable">
                                <table class="zui-table table-width50">
                                    <tbody>
                                    <tr v-for="(item, $index) in cfMxList" :tabindex="$index" ref="list"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()"
                                        @click="checkSelect([$index,'some','cfMxList'],$event)">
                                        <td>
                                            <div class="zui-table-cell cell-m">
                                                <input-checkbox @result="reCheckBox" :list="'cfMxList'" :type="'some'"
                                                                :which="$index" :val="isChecked[$index]">
                                                </input-checkbox>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-xl text-left" v-text="item.mxzlxmmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" v-text="item.zhfymc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.dj"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.sl"></div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--历史处方-->
            <div v-show="lsShow" class="ksys-side hzgl-height flex-container flex-dir-c">
                <div class="col-x-12 flex-one">
                    <div class="col-x-5 hzgl-wiwidth-one flex-one flex-container" style="width: 50.3%;">
                        <div class="zui-table-view over-auto zui-item " v-cloak style="border:none;padding: 0 10px; ">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>诊断</span></div>
                                        </th>
                                        <!-- <th>
                                             <div class="zui-table-cell cell-l"><span>处方日期</span></div>
                                         </th> -->
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>姓名</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>医生</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l"><span>挂号序号</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>类型</span></div>
                                        </th>


                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTable($event),scrollGata($event)">
                                <table class="zui-table table-width50">
                                    <tbody>
                                    <tr v-for="(item, $index) in lsCfList" :tabindex="$index" ref="list"
                                        :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                                        @click="getLsTemMxData($index)"
                                        @dblclick="addLsCfMb($index)">
                                        <td>
                                            <div class="zui-table-cell text-over-2 cell-s"
                                                 :class="item.zfbz==1?'jizheng':''" v-text="item.rymzzd"></div>
                                        </td>
                                        <!-- <td>
                                            <div class="zui-table-cell cell-l" :class="item.zfbz==1?'jizheng':''" v-text="fDate((item.cfrq,'formatTime'))"> </div>
                                        </td> -->
                                        <td>
                                            <div class="zui-table-cell text-over-2 cell-s"
                                                 :class="item.zfbz==1?'jizheng':''" v-text="item.brxm"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" :class="item.zfbz==1?'jizheng':''"
                                                 v-text="item.mzysxm"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l" :class="item.zfbz==1?'jizheng':''"
                                                 v-text="item.ryghxh"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" :class="item.zfbz==1?'jizheng':''"
                                                 v-text="item.cflxmc"></div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <!--<p v-if="yzList.length==0" class="  noData text-center zan-border">暂无数据...</p>-->
                            </div>
                        </div>
                    </div>
                    <div class="col-x-7 margin-l-10 hzgl-wiwidth-two" style="width: 48%;">
                        <div class="zui-table-view">
                            <div class="zui-table-header">
                                <table class="zui-table table-width50">
                                    <thead>
                                    <tr>
                                        <th>
                                            <div class="zui-table-cell cell-m">
                                                <input-checkbox @result="reCheckBox" :list="'lsCfMxList'" :type="'all'"
                                                                :val="isCheckAll">
                                                </input-checkbox>
                                            </div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l text-left"><span>项目名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-l text-left"><span>组合名称</span></div>
                                        </th>
                                        <th>
                                            <div class="zui-table-cell cell-s"><span>数量</span></div>
                                        </th>

                                        <th>
                                            <div class="zui-table-cell cell-s"><span>单价</span></div>
                                        </th>

                                        <th>
                                            <div class="zui-table-cell cell-s"><span>执行科室</span></div>
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="zui-table-body" @scroll="scrollTable">
                                <table class="zui-table table-width50">
                                    <tbody>
                                    <tr v-for="(item, $index) in lsCfMxList" :tabindex="$index"
                                        :class="[{'table-hovers':$index === activeIndex1,'table-hover':$index === hoverIndex1}]"
                                        @mouseenter="hoverMouse1(true,$index)" @click="activeMousel($index)"
                                        @mouseleave="hoverMouse1()">
                                        <td>
                                            <div class="zui-table-cell cell-m">
                                                <input-checkbox @result="reCheckBox" :list="'lsCfMxList'" :type="'some'"
                                                                :which="$index" :val="isChecked[$index]">
                                                </input-checkbox>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l text-left" v-text="item.mxfyxmmc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-l text-left" v-text="item.zhfymc"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.fysl"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.fydj"></div>
                                        </td>
                                        <td>
                                            <div class="zui-table-cell cell-s" v-text="item.zxksmc"></div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
<!--                <page @go-page="goPage" class="addList" :totle-page="totlePage" :page="page" v-if="lsShow" :param="param" :prev-more="prevMore" :next-more="nextMore"></page>-->
            </div>

            <div class="ksys-btn">
                <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="save(saveTitle)" v-text="saveTitle"></button>
            </div>

        </div>

        <!--弹窗展示另存模板 -->
        <div id="pop" class="pophide" :class="{'show':popShow}">
            <div class="pop-width460 bcsz-layer"
                 style="height: max-content;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative">
                <div class="layui-layer-title " style="padding: 0 0 0 20px;">
                    <span class="dzcf-fl" v-text="bcTitle"></span>
                    <span class="dzcf-fr closex ti-close" @click="cancel"></span>
                </div>
                <div class="layui-layer-content">
                    <div class=" layui-mad layui-height">
                        <div class="flex-container  flex-align-c flex-wrap-w" v-if="bcShow">
                            <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                                <label class="padd-r-5">医嘱名称</label>
                                <input class="zui-input wh150" placeholder="请输入医嘱名称" type="text"
                                       v-model="mbZhyzContent.zhyzmc"
                                       @keydown="nextFocus($event)" data-notEmpty="false"
                                       @blur="setPYDM(mbZhyzContent.zhyzmc,'mbZhyzContent','pydm')"/>
                            </div>
                            <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                                <label class="padd-r-5">拼音代码</label>
                                <input class="zui-input wh150" placeholder="请输入模板名称" type="text"
                                       v-model="mbZhyzContent.pydm"
                                       @keydown="nextFocus($event)" data-notEmpty="false" disabled="disabled"/>
                            </div>
                            <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                                <label class="padd-r-5">医嘱类型</label>
                                <select-input class="wh150" @change-data="resultChange" :not_empty="false" :child="zhyzlx_tran"
                                              :index="mbZhyzContent.zhyzlx"
                                              :val="mbZhyzContent.zhyzlx" :name="'mbZhyzContent.zhyzlx'">
                                </select-input>
                            </div>

                            <div class="flex-container flex-jus-c flex-align-c padd-b-10" >
                                <label class="padd-r-5" style="left: 35px;">治&emsp;&emsp;法</label>
                                <input class="zui-input wh150 " placeholder="请输入治法" type="text"
                                       v-model="mbZhyzContent.zf"
                                       @keydown="nextFocus($event)" data-notEmpty="false"/>
                            </div>
                            <div class="flex-container flex-jus-c flex-align-c padd-r-20 padd-b-10" >
                                <label class="padd-r-5" style="left: 35px;">主&emsp;&emsp;治</label>
                                <input class="zui-input wh150 " placeholder="请输入请输入主治" type="text"
                                       v-model="mbZhyzContent.zz"
                                       @keyup.13="saveMb()" data-notEmpty="false"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="zui-row buttonbox">
                    <button v-waves class="zui-btn btn-default xmzb-db margin-r-15 height36" @click="cancel">取消</button>
                    <button  v-waves class="zui-btn btn-primary xmzb-db margin-r-15 height36" @click="saveMb">确定</button>
                </div>
            </div>

        </div>
        <!-- 简单弹窗 -->
        <div id="popHrxx" class="pophide"  :class="{'show':popShow}">
            <div class="pop-width460 bcsz-layer" style="height: max-content;padding-bottom: 20px;margin: 180px auto auto auto;background: #fff; position: relative">
                <div class="layui-layer-title" style="padding: 0 20px;">
                    <span class="dzcf-fl">提示信息</span>
                    <span class="dzcf-fr closex ti-close" @click="closePopup"></span>
                </div>
                <div class="layui-layer-content" style="padding: 20px;">
                    <p>提醒内容：当前病人有外院可互认项目,请点击链接查看!</p></br>
                    <a :href="hrxxContent.url" target="_blank" style="color: #007bff;">点击查看详情</a>
                </div>
                <div class="zui-row buttonbox" style="text-align: right; padding: 10px 20px;">
                    <button v-waves class="zui-btn btn-default height36" @click="closePopup">关闭</button>
                </div>
            </div>
        </div>
        <!-- 检查/检验费用模板begin -->
        <div class="side-form  pop-width pop-805" v-cloak :class="{'ng-hide':!ifShow}" id="jcjyfy" role="jcjyfy">
            <div class="fyxm-side-top">
                <span v-text="title"></span>
                <span class="fr closex ti-close" @click="closes"></span>
            </div>
            <div class="grid-box flex-container">
                <div class=" flex margin-l-10">
                    <div class="flex margin-b-10 margin-top-10 margin-l-10">
                        <label class="whiteSpace margin-r-5 ft-14">检查检验单</label>
                        <select-input @change-data="resultChangeMb" :not_empty="false" :child="jcjydList" :index="'mbmc'"
                            :index_val="'mbbm'" :val="mbbm" :name="'mbbm'" :index_mc="'mbmc'" :search="true">
                        </select-input>
                    </div>
                </div>
                <div class=" flex margin-l-10" v-if="N03001200140 == '1'">
                    <div class="flex margin-b-10 margin-top-10 margin-l-10">
                        <label class="whiteSpace margin-r-5 ft-14">是否急诊</label>
                        <select-input class="wh70" @change-data="resultChange" :not_empty="false" :child="istrue_tran" :index="'sfjz'"
                                      :index_val="'sfjz'" :val="sfjz" :name="'sfjz'" :index_mc="'sfjz'" readonly="readonly">
                        </select-input>
                    </div>
                </div>
                <div class=" flex margin-l-10" >
                    <div class="flex margin-b-10 margin-top-10 margin-l-10">
                        <span class="whiteSpace margin-r-5 ft-14">查询项目[<span class="color-wtg">中文</span>]</span>
                        <input type="text" class="zui-input wh122"  v-model="cxSqd"/>
                    </div>
                </div>
            </div>
            <div class="ksys-side hzgl-height">
                <div class="col-x-12">
                    <div style="padding-bottom: 45px;">
                        <h1 class="text-center margin-b-10" v-text="djxxContent.mbmc"></h1>
                        <div class="flex-container flex-align-c margin-b-10">
                            <span>病人姓名：{{brInfo.brxm}}</span>
                            <span>性别：{{brxb_tran[brInfo.brxb]}}</span>
                            <span>年龄：{{brInfo.brnl + nldw_tran[brInfo.nldw]}}</span>
                            <span>科室：{{brInfo.ghksmc}}</span>
                            <span>挂号序号：{{ brInfo.ghxh }}</span>
                        </div>
                        <div class="flex-container margin-b-10" v-if="djxxContent.havebt == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">病史及查体</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" class="zui-input" />
                            </div>
                        </div>
                        <div class="flex-container margin-b-10" v-if="djxxContent.havets == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">临床诊断</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havezd == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">摘要</span>
                            </div>
                            <div class="flex-one">
                                <input type="text" class="zui-input" />
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havejybb == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">检验标本</span>
                            </div>
                            <div class="flex-one">
                                <!-- <input type="text" v-model="djxxContent.jybb" class="zui-input" /> -->
                                <select-input @change-data="resultChangeJybb" :not_empty="false" :child="ybList" :index="'ybmc'"
                                              :index_val="'ybbm'" :val="jybb" :name="'jybb'" :index_mc="'jybb'" :search="false">
                                </select-input>
                            </div>
                        </div>
                        <div class=" flex-container margin-b-10" v-if="djxxContent.havebz == '1'">
                            <div class="flex-container flex-align-c" style="width: 80px;height: 36px;">
                                <span class="text-over-2">备注</span>
                            </div>
                            <div class="flex-one" v-html="djxxContent.wxts">
                                <!-- <input type="text" class="zui-input" v-model="djxxContent.wxts" /> -->
                            </div>
                        </div>
                        <div v-for="(zh,index) in djxxContent.zhList">
                            <h2 class=" margin-b-10">{{index+1}}、{{zh.mbxmmc}}</h2>
                            <div class="grid-box">
                                <div v-for="(fymx,fyindex) in zh.mxList" class="col-x-4 flex-container flex-align-c padd-r-5 padd-l-5 margin-b-10" v-if="fymx.indexShow" >
                                    <div v-if="fymx.czbw =='0'"><input type="checkbox" :id="'jcjy-zh'+ index +'-fymx-' + fyindex"
                                            class="h-checkbox" v-model="fymx.fymx">
                                        <label :for="'jcjy-zh'+ index +'-fymx-' + fyindex"></label></div>
                                    <div v-if="fymx.czbw =='1'">
                                        <div><input type="checkbox" v-model="fymx.bwLeft" :id="'jcjy-zh'+ index +'-fymx-' + fyindex +'-left'" class="h-checkbox">
                                            <label :for="'jcjy-zh'+ index +'-fymx-' + fyindex +'-left'">左</label></div>
                                        <div><input type="checkbox" v-model="fymx.bwRight" :id="'jcjy-zh'+ index +'-fymx-' + fyindex +'-right'" class="h-checkbox">
                                            <label :for="'jcjy-zh'+ index +'-fymx-' + fyindex +'-right'">右</label></div>
                                    </div>
                                    <div class="flex-one text-over-2 margin-l-5 margin-r-5">{{fymx.mxxmmc}}<span class="color-wtg">({{fymx.xmdj}}¥)</span></div>
                                </div>
                            </div>
                        </div>
                        <div class="flex-container flex-jus-sp">
                            <span>送检医生：{{userName}}</span><span>日期：{{ fDate(new Date().getTime(),"datetime") }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ksys-btn">
                <button v-waves class="zui-btn table_db_esc btn-default xmzb-db" @click="closes">取消</button>
                <button v-waves class="zui-btn btn-primary xmzb-db" @click="add()">保存</button>
            </div>
        </div>
        <!-- 检查/检验费用模板 end -->
        <model :s="'保存'" :c="'退出'" class="fjzdPop" @default-click="saveData" @result-clear="fjzdShow=false"
               :model-show="true" @result-close="fjzdShow=false" v-if="fjzdShow" :title="'附加诊断'">
            <div class="zui-table-view  bqcydj_model_width hzList-border flex-container flex-dir-c">
                <div class="zui-table-header">
                    <table class="zui-table table-width50">
                        <thead>
                        <tr>
                            <th class="cell-m">
                                <div class="zui-table-cell cell-m">序号</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">诊断名称</div>
                            </th>
                            <th>
                                <div class="zui-table-cell cell-s">诊断编码</div>
                            </th>
                        </tr>
                        <tr @mouseenter="hoverMouse(true,$index)"
                            @mouseleave="hoverMouse()" v-for="(item, $index) in fjzd"
                            :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]">
                            <td>
                                <div class="zui-table-cell cell-s">
                                    <span v-text="$index+1"></span>
                                </div>
                            </td>
                            <td class="">
                                <div class="zui-table-cell flex-container flex-align-c">
                                    <input class="zui-input height-input wh100MAx" data-notEmpty="false" v-model="item.jbmc"
                                           @keydown="changeDown($event,$index,'jbmc','jbmb','qtzd')"
                                           @input="change(false,$index,'jbmc','jbbm',$event.target.value,'jbbm')">
                                    <search-table :message="searchCon" :selected="selSearch" :page="queryObj" :them="them"
                                                  @click-one="checkedOneOut"
                                                  @click-two="checkedTwoOut">
                                    </search-table>
                                </div>
                            </td>
                            <td>
                                <div class="zui-table-cell cell-s">
                                    {{item.jbmb}}
                                </div>
                            </td>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <button v-waves slot="footer" class="tong-btn btn-parmary  xmzb-db margin-r-10 sizewidth88 font-auto" @click="addFun()">新增一行</button>
        </model>
    </div>

    <script type="text/javascript" src="brPage/fypcf.js"></script>
</body>

</html>
