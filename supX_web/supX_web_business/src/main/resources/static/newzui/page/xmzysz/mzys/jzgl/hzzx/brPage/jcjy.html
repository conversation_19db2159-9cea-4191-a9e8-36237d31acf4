<link rel="stylesheet" href="/pub/css/print.css" media="print">
<link rel="stylesheet" href="brPage/jcjy.css">
<div class="wrapper flex-container" >

    <div  id="bgdList" class="zui-table-view flex-container flex-dir-c margin-r-10 printHide">
        <div class="flex-container flex-align-c padd-b-5 padd-t-5">
            <span class="ft-14 padd-r-5">是否打印</span>
            <select-input class="wh122" @change-data="resultChangeData" :not_empty="false"
                          :child="IsPrint_tran" :index="hisbgdybz" :val="hisbgdybz"
                          :name="'hisbgdybz'">
            </select-input>
        </div>
        <div class="zui-table-header">
            <table class="zui-table table-width50">
                <thead>
                <tr>
                    <th  class="cell-m">
                        <input-checkbox @result="getReCheckBox" :list="'djList'"
                                        :type="'all'" :val="isCheckAll">
                        </input-checkbox>
                    </th>
                    <th class="cell-m">
                        <div class="zui-table-cell cell-m">类型</div>
                    </th>
                    <th class="cell-l">
                        <div class="zui-table-cell cell-l">项目名称</div>
                    </th>
                    <th class="cell-l">
                        <div class="zui-table-cell cell-l">审核时间</div>
                    </th>
                </tr>
                </thead>
            </table>
        </div>

        <div class="zui-table-body flex-one" @scroll="scrollTable($event)">
            <table class="zui-table table-width50" v-if="djList.length!=0">
                <tbody>
                <tr ref="list" v-for="(item, $index) in djList" :class="[{'table-hovers':$index===activeIndex,'table-hover':$index === hoverIndex}]"
                    @mouseenter="hoverMouse(true,$index)" @mouseleave="hoverMouse()" :tabindex="$index"
                    @click="checkSelect([$index,'one','djList'],$event),showDj($index)">
                    <td  >
                        <input-checkbox :class="item.ifJcbgd ?'h-visibility':''" @result="getReCheckBox" :list="'djList'"
                                        :type="'some'" :which="$index"
                                        :val="isChecked[$index]">
                        </input-checkbox>
                    </td>
                    <td class="cell-m">
                        <div class="zui-table-cell cell-m">{{ item.ifJcbgd ? "检查" : "检验" }}</div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="item.fymc"></div>
                    </td>
                    <td>
                        <div class="zui-table-cell cell-l" v-text="fDate(item.shrq,'AllDate')"></div>
                    </td>
                </tr>
                </tbody>
            </table>
            <p v-if="djList.length==0" class="noData text-center zan-border">暂无数据...</p>
        </div>
    </div>

    <div id="jcbgd" v-cloak v-if="ifShow" class="flex-one flex-container flex-dir-c printHide">
        <div v-if="ifYzPacs">
            <a :href="yzPacsBgUrl" target="_blank">点击查看报告</a>
            <a :href="yzPacsTpUrl" target="_blank">点击查看图片</a>
        </div>
        <div v-if="!ifYzPacs" class="margin-b-10 butt-box">
            <a :href="bgdImageHref" target="_blank">点击查看图片</a>
        </div>
        <!-- <div class="flex-one">
            <iframe width='100%' height='100%' :src='iframeSrc'></iframe>
        </div> -->
        <object   width="100%" height="100%" type="application/pdf"  :data='iframeSrc'></object>
        <div class="flex-one">
            <table class=" jybgd-table">
                <thead>
                <tr>
                    <th  style="width:150px;">检查日期</th>
                    <th style="width:60px;">名称</th>
                    <th style="width:200px;">内容</th>
                    <th style="width:50px;">检查医生</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in jcbgdxq">
                    <td>{{ fDate( item.reportDate , "datetime" )}}</td>
                    <td>{{ item.labelname }}</td>
                    <td>{{ item.labelvalue}}</td>
                    <td>{{ item.reportDoctor}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="jydy" v-cloak v-if="ifShow"  class="over-auto">
        <div class="">
            <div  class="flex-jc-c">{{yljgmc}}</div>
            <div class="printDom" v-for="(item,index) in xqxx">
            <div  class="flex-container flex-align-c flex-jus-sp">
                <span class="flex1">姓名：{{jbxx[0].brxm}}</span>
                <span class="flex1">性别：{{jbxx[0].xb == '1' ? '男' : '女'}}</span>
                <span class="flex1">年龄：{{jbxx[0].nl}}{{nldw_tran[jbxx[0].nldw]}}</span>
            </div>
            <div  class="flex-container flex-align-c flex-jus-sp">
                <span class="flex1">挂号序号：{{jbxx[0].bah}}</span>
                <span class="flex1">临床诊断：{{jbxx[0].lczd}}</span>
                <span class="flex1">检验序号：{{jbxx[0].jyxh}}</span>
            </div>
            <div class="">
                <div class="flex-container flex-align-c flex-jus-sp ">
                    <span class="flex1">样本类型：{{item.jydj.ybbm}}</span>
                    <span class="flex1">样本号：{{item.jydj.bbbh}}</span>
                    <span class="flex1">检验目的：{{item.jydj.fymc}}</span>
                </div>
                <div >
                    <table class=" jybgd-table" style="width: 100%;">
                        <thead>
                        <tr>
                            <th class="text-left ">检验项目</th>
                            <th class="text-center">结果</th>
                            <th class="text-center">状态</th>
                            <th class="text-center">单位</th>
                            <th class="text-center">参考值</th>

                            <th class="text-left ">检验项目</th>
                            <th class="text-center">结果</th>
                            <th class="text-center">状态</th>
                            <th class="text-center">单位</th>
                            <th class="text-center">参考值</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(mx,index) in item.jymx">
                            <td class="text-left 1">{{mx.zwmc}}{{mx.valueT}}</td>

                            <td class="2 text-center"  v-if="(mx.sjlx == '1' || mx.sjlx == '4')">{{ mx.sjlx == '1' || mx.sjlx == '4' ? mx.valueN : mx.valueT }}</td>
                            <td class="2 text-center" v-if="mx.sjlx == '3' || mx.sjlx == '2' || mx.sjlx == '999' ">{{ (mx.sjlx == '3' && mx.xzjgmc) || mx.sjlx == '999' ? mx.xzjgmc : mx.valueT }}</td>

                            <td class="8 text-center":class="[mx.valueN < mx.zxz ?'green' :'red',mx.sjlx == '1' || mx.sjlx == '4' ? '':'visibility']">
                                {{mx.valueN > mx.zxz && mx.valueN < mx.zdz ? '' : mx.valueN < mx.zxz ? '↓' : mx.valueN > mx.zdz ? '↑' : ''}}
                            </td>
                            <td class="3 text-center">{{mx.dw}}</td>
                            <td class="4 text-center">{{mx.ckzT}}</td>

                            <td class="text-left 1">{{mx.zwmcL}}</td>

                            <td class="2 text-center"  v-if="(mx.sjlxL == '1' || mx.sjlxL == '4')">{{ mx.sjlxL == '1' || mx.sjlxL == '4' ? mx.valueNL : mx.valueTL }}</td>
                            <td class="2 text-center" v-if="mx.sjlxL == '3' || mx.sjlxL == '2'">{{ (mx.sjlxL == '3' && mx.xzjgmcL) ? mx.xzjgmcL  :  mx.valueTL }}</td>

                            <td class="8 text-center"  :class="[mx.valueNL < mx.zxzL ?'green' :'red',mx.sjlxL == '1' || mx.sjlxL == '4' ? '':'visibility']">
                                {{mx.valueNL > mx.zxzL && mx.valueNL < mx.zdzL ? '' : mx.valueNL < mx.zxzL ? '↓' : mx.valueNL > mx.zdzL ? '↑' : ''}}
                            </td>
                            <td class="3 text-center">{{mx.dwL}}</td>
                            <td class="4 text-center">{{mx.ckzTL}}</td>
                        </tr>

                        </tbody>
                    </table>
                </div>
                <div class="">
                    <div class="flex-container flex-align-c flex-jus-sp">
                        <span class="flex1">送检医生：{{sjys(jbxx[index].sqys)}}</span>
                        <span class="flex1">检验者：
                             <img v-if="csqxContent.N03001200131" :src="setImg(jbxx[index].zxys)" height="32"/>
                                <span>{{jbxx[index].jyrxm}}</span>
                            </span>
                        <span class="flex1">审核者：
                            <img  v-if="csqxContent.N03001200131" :src="setImg(jbxx[index].shry)" height="32"/>
                                 <span>{{jbxx[index].shryxm}}</span>
                            </span>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-sp-r-sa">
                        <span class="flex1">采集时间：{{fDate(item.jydj.cyrq,'AllDate')}}</span>
                        <span class="flex1">接收时间：{{fDate(item.jydj.ybhsrq,'AllDate')}}</span>
                        <span class="flex1">报告时间：{{fDate(item.jydj.shrq,'AllDate')}}</span>
                    </div>
                    <div class="flex-container flex-align-c flex-jus-sp">
                        <span class="flex1">备注：{{jbxx.bz}}</span>
                        <span class="flex1"></span>
                        <span class="flex1"></span>
                    </div>
                </div>
            </div>
            </div>
            <button class="zui-btn zui-88 buttond absolute-right btn-primary xmzb-db" @click="print()">检验打印</button>
        </div>
    </div>
    <div id="jybgd" v-cloak v-if="ifShow" class="flex-one over-auto printHide">
        <div class="jybgd">
            <h1 class="text-center">{{yljgmc}}</h1>
            <div class="grid-box">
                <div class="col-x-2">姓&emsp;&emsp;名：<em>{{ jybgd.brxm || '' }}</em></div>
                <div class="col-x-3">住&ensp;院&ensp;号：{{ jybgd.bah || '' }}</div>
                <div class="col-x-3">送检医师：{{ 无字段 }}</div>
                <div class="col-x-4">申请日期：{{ fDate( jybgd.sqrq , "datetime" ) }}</div>
                <div class="col-x-2">性&emsp;&emsp;别：{{ brxb_tran[ jybgd.xb ] }}</div>
                <div class="col-x-3">床&ensp;位&ensp;号：{{ jybgd.cwh || '' }}</div>
                <div class="col-x-3">样本类型：{{ jybgd.yblx || '' }}</div>
                <div class="col-x-4">采集日期：{{ fDate( jybgd.cyrq , "datetime" ) }}</div>
                <div class="col-x-2">年&emsp;&emsp;龄：{{ jybgd.nl }}{{ nldw_tran[ jybgd.nldw ] }}</div>
                <div class="col-x-3">科&emsp;&emsp;室：<em>{{ jybgd.ksmc }}</em></div>
                <div class="col-x-3">条&ensp;形&ensp;码：{{ jybgd.jyxh }}</div>
                <div class="col-x-4">接收日期：{{ fDate( jybgd.ybhsrq , "datetime" ) }}</div>
                <div class="col-x-2">仪&ensp;检&ensp;号：<em>{{ jybgd.bbbh || ''}}</em></div>
                <div class="col-x-3">类&emsp;&emsp;型：{{ jybgd.yblx || ''}}</div>
                <div class="col-x-3">临床诊断：{{jybgd.lczd || ''}}</div>
                <div class="col-x-4">备&emsp;&emsp;注：{{ jybgd.bz || '' }}</div>
            </div>
            <table class=" jybgd-table">
                <thead>
                <tr>
                    <th class="text-center" style="width:50px;">序号</th>
                    <th class="text-center" style="width:150px;">项目名称</th>
                    <th class="text-center" style="width:60px;">简称</th>
                    <th class="text-center" style="width:150px;">结果</th>
                    <th class="text-center" style="width:50px;">状态</th>
                    <th class="text-center" style="width:150px;">参考范围</th>
                    <th class="text-center" style="width:100px;">单位</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in jybgdmx">
                    <td class="text-center 1">{{ item.xh }}</td>
                    <td class="2 text-center">{{ item.zwmc }}</td>
                    <td class="3 text-center">{{ item.ywmc }}</td>
                    <td class="4 text-center" style="color:green;" v-if="(item.sjlx == '1' || item.sjlx == '4') &&  item.valueN < item.zxz">{{ item.sjlx == '1' || item.sjlx == '4' ? item.valueN : item.valueT }}</td>
                    <td class="5 text-center" style="color:red;" v-if="(item.sjlx == '1' || item.sjlx == '4') &&  item.valueN > item.zdz">{{ item.sjlx == '1' || item.sjlx == '4' ? item.valueN : item.valueT }}</td>
                    <td class="6 text-center" v-show="(item.sjlx == '1' || item.sjlx == '4') &&  item.valueN <= item.zdz && item.valueN >= item.zxz">{{ item.sjlx == '1' || item.sjlx == '4' ? item.valueN : item.valueT }}</td>
                    <td class="7 text-center" v-show="item.sjlx == '3' || item.sjlx == '2'">{{ item.sjlx == '3' ? item.xzjgmc : item.valueT }}</td>
                    <td class="8 text-center" :class="[item.valueN < item.zxz ?'green' :'red',item.sjlx == '1' || item.sjlx == '4' ? '':'visibility']">
                        {{item.valueN > item.zxz && item.valueN < item.zdz ? '' : item.valueN < item.zxz ? '↓' : item.valueN > item.zdz ? '↑' : ''}}
                    </td>

                    <!--                        <td class="zwmc " v-show="(item.sjlx == '1' || item.sjlx == '4') &&  item.valueN <= item.zdz && item.valueN >= item.zxz"></td>-->
                    <td class="ckzT text-center">{{ item.ckzT }}</td>
                    <td class="dw text-center">{{ item.dw }}</td>
                </tr>

                </tbody>
            </table>
            <div class="grid-box">
                <div class="col-x-4">审核时间：{{ fDate( jybgd.shrq , "datetime" ) }}</div>
                <div class="col-x-4">打印时间：{{ fDate( jybgd.djrq , "datetime" ) }}</div>
                <div class="col-x-2">检验者：{{ jybgd.jyrxm }}</div>
                <div class="col-x-2">审核者：{{ jybgd.shryxm }}</div>
                <div class="col-x-12">本测试结果仅对本样本负责！</div>

            </div>
        </div>
    </div>

</div>
<script type="text/javascript" src="brPage/jcjy.js"></script>
