@font-face {
  font-family: 'icomoonnew';
  src:  url('../fonts/icomoon.eot?v3mght');
  src:  url('../fonts/icomoon.eot?v3mght#iefix') format('embedded-opentype'),
  url('../fonts/icomoon.ttf?v3mght') format('truetype'),
  url('../fonts/icomoon.woff?v3mght') format('woff'),
  url('../fonts/icomoon.svg?v3mght#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}


.iconfont[class^="icon-iocn"], .iconfont[class*="icon-iocn"],.iconfont[class^="icon-icon"],.iconfont[class*="icon-icon"],.iconfont[class^="icon-"], .iconfont[class*=" icon-"]{
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoonnew' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  font-size:18px;
  padding-right: 2px;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}