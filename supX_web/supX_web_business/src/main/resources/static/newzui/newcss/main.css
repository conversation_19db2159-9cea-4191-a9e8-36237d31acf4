input[type="submit"],
input[type="reset"],
input[type="button"],
input[type="text"],
input[type="password"] {
  -webkit-appearance: none;
  outline: 0;
  background: none;

}
.icon-icon76:before {
  content: "\e94f";
  color: #757c83;
}
.icon-icon75:before {
  content: "\e94e";
  color: #757c83;
}
.icon-icon74:before {
  content: "\e94c";
  color: #757c83;
}
.icon-icon69:before {
  content: "\e94d";
  color: #757c83;
}
.icon-icon73:before {
  content: "\e949";
  color: #757c83;
}
.icon-icon72:before {
  content: "\e94a";
  color: #757c83;
}
.icon-icon71:before {
  content: "\e94b";
  color: #757c83;
}
.icon-icon70:before {
  content: "\e948";
  color: #fff;
}
.icon-icon63:before {
  content: "\e945";
  color: #757c83;
}
.icon-icon64:before {
  content: "\e946";
  color: #757c83;
}
.icon-icon65:before {
  content: "\e947";
  color: #757c83;
}
.icon-icon68:before {
  content: "\e942";
  color: #7f8fa4;
}
.icon-icon67:before {
  content: "\e943";
  color: #7f8fa4;
}
.icon-icon66:before {
  content: "\e944";
  color: #7f8fa4;
}
.icon-icon60:before {
  content: "\e93c";
  color: #5e5e5e;
}
.icon-icon61:before {
  content: "\e93a";
  color: #5f5f5f;
}
.icon-icon62:before {
  content: "\e93b";
  color: #5f5f5f;
}
.icon-iocn-58:before {
  content: "\e939";
  color: #5f5f5f;
}
.icon-iocn1:before {
  content: "\e900";
  color: #5f5f5f;
}
.icon-iocn2:before {
  content: "\e901";
  color: #5f5f5f;
}
.icon-iocn3:before {
  content: "\e902";
  color: #5e5e5f;
}
.icon-iocn4:before {
  content: "\e903";
  color: #5f5f5f;
}
.icon-iocn5:before {
  content: "\e904";
  color: #5f5f5f;
}
.icon-iocn6:before {
  content: "\e905";
  color: #5e5e5f;
}
.icon-iocn7:before {
  content: "\e906";
  color: #5f5f5f;
}
.icon-iocn8:before {
  content: "\e907";
  color: #5e5e5f;
}
.icon-iocn9:before {
  content: "\e908";
  color: #5e5e5f;
}
.icon-iocn10:before {
  content: "\e909";
  color: #5f5f5f;
}
.icon-iocn11:before {
  content: "\e90a";
  color: #5f5f5f;
}
.icon-iocn12:before {
  content: "\e90b";
  color: #5f5f5f;
}
.icon-iocn13:before {
  content: "\e90c";
  color: #5f5f5f;
}
.icon-iocn14:before {
  content: "\e90d";
  color: #5f5f5f;
}
.icon-iocn15:before {
  content: "\e90e";
  color: #5f5f5f;
}
.icon-iocn16:before {
  content: "\e90f";
  color: #5f5f5f;
}
.icon-iocn17:before {
  content: "\e910";
  color: #5f5f5f;
}
.icon-iocn18:before {
  content: "\e911";
  color: #5e5e5f;
}
.icon-iocn19:before {
  content: "\e912";
  color: #5f5f5f;
}
.icon-iocn20:before {
  content: "\e913";
  color: #5f5f5f;
}
.icon-iocn21:before {
  content: "\e914";
  color: #5e5e5f;
}
.icon-iocn22:before {
  content: "\e915";
  color: #5f5f5f;
}
.icon-iocn23:before {
  content: "\e916";
  color: #5f5f5f;
}
.icon-iocn24:before {
  content: "\e917";
  color: #5f5f5f;
}
.icon-iocn25:before {
  content: "\e918";
  color: #5f5f5f;
}
.icon-iocn26:before {
  content: "\e919";
  color: #5f5f5f;
}
.icon-iocn27:before {
  content: "\e91a";
  color: #5f5f5f;
}
.icon-iocn28:before {
  content: "\e91b";
  color: #5f5f5f;
}
.icon-iocn29:before {
  content: "\e91c";
  color: #5f5f5f;
}
.icon-iocn30:before {
  content: "\e91d";
  color: #5f5f5f;
}
.icon-iocn31:before {
  content: "\e91e";
  color: #5f5f5f;
}
.icon-iocn32:before {
  content: "\e91f";
  color: #5f5f5f;
}
.icon-iocn33:before {
  content: "\e920";
  color: #5e5e5f;
}
.icon-iocn34:before {
  content: "\e921";
  color: #5f5f5f;
}
.icon-iocn35:before {
  content: "\e922";
  color: #5f5f5f;
}
.icon-iocn36:before {
  content: "\e923";
  color: #5f5f5f;
}
.icon-iocn37:before {
  content: "\e924";
  color: #5f5f5f;
}
.icon-iocn38:before {
  content: "\e925";
  color: #5f5f5f;
}
.icon-iocn39:before {
  content: "\e926";
  color: #5f5f5f;
}
.icon-iocn40:before {
  content: "\e927";
  color: #5f5f5f;
}
.icon-iocn41:before {
  content: "\e928";
  color: #5f5f5f;
}
.icon-iocn42:before {
  content: "\e929";
  color: #5f5f5f;
}
.icon-iocn43:before {
  content: "\e92a";
  color: #5f5f5f;
}
.icon-iocn44:before {
  content: "\e92b";
  color: #5f5f5f;
}
.icon-iocn45:before {
  content: "\e92c";
  color: #5f5f5f;
}
.icon-iocn46:before {
  content: "\e92d";
  color: #5f5f5f;
}
.icon-iocn47:before {
  content: "\e92e";
  color: #5f5f5f;
}
.icon-iocn48:before {
  content: "\e92f";
  color: #5f5f5f;
}
.icon-iocn49:before {
  content: "\e930";
  color: #5f5f5f;
}
.icon-iocn50:before {
  content: "\e931";
  color: #5f5f5f;
}
.icon-iocn51:before {
  content: "\e932";
  color: #5f5f5f;
}
.icon-iocn52:before {
  content: "\e933";
  color: #5f5f5f;
}
.icon-iocn53:before {
  content: "\e934";
  color: #5f5f5f;
}
.icon-iocn54:before {
  content: "\e935";
  color: #5f5f5f;
}
.icon-iocn55:before {
  content: "\e936";
  color: #5f5f5f;
}
.icon-iocn56:before {
  content: "\e937";
  color: #5f5f5f;
}
.icon-iocn57:before {
  content: "\e938";
  color: #5f5f5f;
}
.icon-Artboard-12 .path1:before {
  content: "\e93e";
  color: rgb(95, 95, 95);
}
.icon-Artboard-12 .path2:before {
  content: "\e93f";
  margin-left: -0.9443359375em;
  color: rgb(94, 94, 94);
}
.icon-Artboard-1:before {
  content: "\e93d";
  color: #5f5f5f;
}
.icon-icon58:before {
  content: "\e940";
  color: #5f5f5f;
}
.icon-icon59:before {
  content: "\e941";
  color: #5f5f5f;
}

* {
  margin: 0;
  padding: 0;
}
body {
  font-size: 14px;
}
button {
  cursor: pointer;
  border: none;
}
ul,
li,
i,
em {
  list-style: none;
  font-style: normal;
}
.text-center {
  text-align: center !important;
}
.text-left {
  text-align: left !important;
}
.text-right {
  text-align: right !important;
}
.text-justify {
  text-align: justify !important;
}
.text-indent-5 {
  text-indent: 5px;
}
.text-indent-9 {
  text-indent: 9px;
}
.text-indent-10 {
  text-indent: 10px;
}
.text-indent-15 {
  text-indent: 15px;
}
.text-indent-20 {
  text-indent: 20px !important;
}
.text-line {
  text-decoration: underline;
}
.padd10 {
  padding: 10px;
}
.padd15 {
  padding: 15px;
}
.padd20 {
  padding: 15px;
}
.padd-l-5 {
  padding-left: 5px;
}
.padd-l-10 {
  padding-left: 10px;
}
.padd-l-15 {
  padding-left: 15px;
}
.padd-l-20 {
  padding-left: 20px;
}
.padd-l-25 {
  padding-left: 25px;
}
.padd-l-30 {
  padding-left: 30px;
}
.padd-l-35 {
  padding-left: 35px;
}
.padd-l-40 {
  padding-left: 40px;
}
.padd-l-45 {
  padding-left: 45px;
}
.padd-r-5 {
  padding-right: 5px;
}
.padd-r-10 {
  padding-right: 10px !important;
}
.padd-r-15{
  padding-right: 15px;
}
.padd-r-20 {
  padding-right: 20px;
}
.padd-r-25 {
  padding-right: 25px;
}
.padd-r-30 {
  padding-right: 30px;
}
.padd-r-35 {
  padding-right: 35px;
}
.padd-r-40 {
  padding-right: 40px;
}
.padd-r-45 {
  padding-right: 45px;
}
.padd-b-5 {
  padding-bottom: 5px;
}
.padd-b-10 {
  padding-bottom: 10px;
}
.padd-b-20 {
  padding-bottom: 20px;
}
.padd-b-25 {
  padding-bottom: 25px;
}
.padd-b-30 {
  padding-bottom: 30px;
}
.padd-b-35 {
  padding-bottom: 35px;
}
.padd-b-40 {
  padding-bottom: 40px;
}
.padd-b-45 {
  padding-bottom: 45px;
}
.padd-t-2 {
  padding-top: 2px;
}
.padd-t-3 {
  padding-top: 3px;
}
.padd-t-5 {
  padding-top: 5px;
}
.padd-t-10 {
  padding-top: 10px;
}
.padd-t-20 {
  padding-top: 20px;
}
.padd-t-25 {
  padding-top: 25px;
}
.padd-t-30 {
  padding-top: 30px;
}
.padd-t-35 {
  padding-top: 35px;
}
.padd-t-40 {
  padding-top: 40px;
}
.padd-t-45 {
  padding-top: 45px;
}
.margin10 {
  margin: 10px;
}
.margin15 {
  margin: 15px;
}
.margin20 {
  margin: 20px;
}
.margin-t-5 {
  margin-top: 5px;
}
.margin-t-10 {
  margin-top: 10px;
}
.margin-t-15 {
  margin-top: 15px;
}
.margin-t-20 {
  margin-top: 20px;
}
.margin-t-25 {
  margin-top: 25px;
}
.margin-t-30 {
  margin-top: 30px;
}
.margin-t-35 {
  margin-top: 35px;
}
.margin-t-40 {
  margin-top: 40px;
}
.margin-t-45 {
  margin-top: 45px;
}
.margin-b-5 {
  margin-bottom: 5px;
}
.margin-b-10 {
  margin-bottom: 10px;
}
.margin-b-15 {
  margin-bottom: 15px;
}
.margin-b-20 {
  margin-bottom: 20px;
}
.margin-b-25 {
  margin-bottom: 25px;
}
.margin-b-30 {
  margin-bottom: 30px;
}
.margin-b-35 {
  margin-bottom: 35px;
}
.margin-b-40 {
  margin-bottom: 40px;
}
.margin-b-45 {
  margin-bottom: 45px;
}
.border {
  border: 1px solid #eee;
}
.border-r {
  border-right: 1px solid #eee;
}
.border-l {
  border-left: 1px solid #eee;
}
.border-b {
  border-bottom: 1px solid #eee;
}
.border-t {
  border-top: 1px solid #eee;
}
.fr {
  float: right !important;
}
.fl {
  float: left !important;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
}
.icon-c5:before {
  color: #c5d0de;
  font-size: 16px;
}
.icon-cf:before {
  color: #fff;
}
.icon-cf08:before {
  color: rgba(255, 255, 255, 0.6);
  font-size: 22px;
}
.icon-c1:before {
  color: #1abc9c;
}
.icon-c4:before {
  color: #cccfd4;
}
.icon-c75:before {
  color: #757c83;
}
.icon-font14:before {
  font-size: 14px;
}
.icon-font15:before {
  font-size: 15px;
}
.icon-font16:before {
  font-size: 16px;
}
.icon-font17:before {
  font-size: 17px;
}
.icon-font18:before {
  font-size: 18px;
}
.icon-font19:before {
  font-size: 19px;
}
.icon-font20:before {
  font-size: 20px;
}
.icon-font25:before {
  font-size: 25px;
}
.icon-position {
  position: absolute;
  top: 8px;
  left: 10px;
  width: 16px;
  background: #fff;
}
.icon-cf056:before {
  color: rgba(255, 255, 255, 0.56);
}
.icon-cf056:hover:before {
  color: rgba(255, 255, 255, 0.36);
}
.icon-hover:hover:before{
  color:#1abc9c;
}
.width30 {
  width: 25px !important;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 26px;
}
.width30 i {
  display: block;
  width: 30px;
  font-size: 20px;
  height: 20px;
}
.tong-btn {
  width: auto;
  /*min-width: 72px;*/
  padding: 5px 11px;
  border-radius: 4px;
  float: left;
  border: none;
  font-size: 14px;
  height: 32px;
  background: none;
  margin-right: 10px;
  outline: none;
}
.btn-parmary-b {
  border: 1px solid #1abc9c;
  color: #1abc9c;
  position: relative;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-parmary-b:hover {
  color: rgba(26, 188, 156, 0.6);
}
.btn-parmary {
  background: #1abc9c;
  color: #fff;
  position: relative;
}
.btn-ce4 {
  background: #e4eaec;
  position: relative;
}
.btn-ce4:hover {
  opacity: 0.6;
}
.btn-parmary:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-f2a {
  background: #f2a654;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a1{
  background-color: #f2a654 !important;
  color: #fff;
  position: relative;
}
.btn-parmary-d2 {
  background: #d25747;
  color: #fff;
  position: relative;
}
.btn-parmary-f2a:hover,.btn-parmary-f2a1:hover,
.btn-parmary-d2:hover {
  color: rgba(255, 255, 255, 0.6);
}
.btn-parmary-d9 {
  background: #d9dddc;
  color: #8e9694;
  position: relative;
}
.btn-parmary-9e{
  background: #9E28E2;
  color: #fff;
}
.btn-parmary-d9:hover {
  color: rgba(142, 150, 148, 0.6);
}

.btn-parmary-71 {
  background: #718dc7;
  color: #fff;
}
.btn-parmary-71:hover {
  opacity: 0.9;
}
.color-c1 {
  color: #1abc9c;
}
.color-cfa {
  color: #fafafa;
}
.color-c3 {
  color: #333333;
}
.color-c7 {
  color: #757c83;
}
.color-cf9 {
  color: #f9fbfc;
}
.color-ce {
  color: #eee;
}
.color-cf3 {
  color: #f3b169;
}
.color-f2 {
  color: #f2a654;
}
.color-c45 {
  color: #45e135;
}
.color-cfb8 {
  color: #fb86000f;
}
.color-cfb86 {
  color: rgba(251, 134, 0, 0.06);
}
.color-cf0 {
  color: #f0fafe;
}
.color-c04 {
  color: #04a9f5;
}
.color-cff5 {
  color: #ff5c63;
}
.color-e9{
  color:#E96509;
}
.color-ca3 {
  color: #a389d4;
}
.color-9e{
color: #9E28E2;
}
.color-c00 {
  color: #000;
}
.color-dsh {
  color: #1abc9c;
}
.color-wtg {
  color: #ff5c63;
}
.color-yzf {
  color: #7d848a;
}
.color-dlr {
  color: #2e88e3;
}
.color-wc {
  color: #354052;
}
.color-008 {
  color: #00B83F;
}
.color-393f {
  color: #393f45;
}
.background-f {
  background: #fff !important;
}
.background-h {
  background: #f9f9f9 !important;
}
.background-rgb9 {
  background: rgba(0, 0, 0, 0.9);
}
.background-rgb8 {
  background: rgba(0, 0, 0, 0.8);
}
.background-rgb7 {
  background: rgba(0, 0, 0, 0.7);
}
.background-rgb6 {
  background: rgba(0, 0, 0, 0.6);
}
.background-rgb5 {
  background: rgba(0, 0, 0, 0.5);
}
.background-rgb4 {
  background: rgba(0, 0, 0, 0.4);
}
.background-rgb3 {
  background: rgba(0, 0, 0, 0.3);
}
.background-rgb2 {
  background: rgba(0, 0, 0, 0.2);
}
.background-rgb1 {
  background: rgba(0, 0, 0, 0.1);
}
.tong-top {
  width: 100%;
  min-height: 52px;
  background: #fafafa;
  padding: 10px 0 10px 12px;
  box-sizing: border-box;
}
.font-weight {
  font-weight: 600;
}
.zui-input,
.zui-textarea {
  display: block;
  width: 100%;
  height: 36px;
  line-height: 36px;
  border: 1px solid #d7dbe1;
  background-color: #fff;
  color: #354052;
  font-size: 14px;
  text-indent: 5px;
  border-radius: 4px;
}
.fyxm-side-top {
  height: 46px;
  background: #1abc9c;
  width: 100%;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-sizing: border-box;
  font-size: 16px;
}
.ksys-side {
  width: 100%;
  padding: 15px 20px 15px 20px;
  float: left;
  box-sizing: border-box;
}
.ksys-side i {
  display: block;
  width: 88px;
  color: #7f8fa4;
}
.tab-edit-list1 li {
  display: block;
  width: 100%;
  position: relative;
  padding-bottom: 10px;
  justify-content: space-evenly;
}
.tab-edit-list1 li i {
  padding-bottom: 5px;
}
.tab-edit-list {
  width: 100%;
  box-sizing: border-box;
}
.tab-edit-list li {
  width: calc((100% - 40px)/3);
  margin-right: 20px;
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.tab-edit-list li:nth-child(3n) {
  margin-right: 0;
}
.tab-edit-list i {
  width: auto;
  display: block;
  text-align: right;
  font-style: normal;
  margin-right: 5px;
  min-width: 60px;
}
.label-time,
.label-time1,
.label-time2,
.label-time3 {
  background: #00B83F;
}
.tab-edit2-list li {
  width: calc((100% - 20px)/2);
}
.tab-edit2-list li:nth-child(3n) {
  margin-right: 20px;
}
.tab-edit2-list li:nth-child(2n) {
  float: right;
  margin-right: 0;
}
.tab-edit2-list .label-time,
.tab-edit2-list .label-time1,
.tab-edit2-list .label-time2,
.tab-edit2-list .label-time3 {
  background: #00B83F;
}
.pop-548 {
  width: 548px;
}
.pop-805 {
  width: 805px;
}
.pop-width {
  width: 320px;
}
.pop-width .ksys-side {
  padding: 15px 14px;
}
.relative {
  position: relative;
}
.ksys-btn {
  position: fixed;
  z-index: 11;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  border-top: 1px solid #eee;
  background-color: #fff;
}
.root-btn {
  height: 36px;
  min-width: 88px;
  padding: 0 12px;
  border-radius: 4px;
  font-size: 14px;
  margin-right: 10px;
}
.fyxm-tab {
  float: left;
  width: 100%;
  height: 36px;
}
.fyxm-tab div {
  float: left;
  width: auto;
  padding: 0 20px;
  height: 36px;
  line-height: 36px;
  border: 1px solid #eee;
  border-left: none;
}
.fyxm-tab div:first-child {
  border-left: 1px solid #eee;
}
.fyxm-tab div span {
  width: auto;
  padding: 0;
  cursor: pointer;
  display: block;
  height: 34px;
}
.fyxm-tab div span.active {
  font-weight: 600;
  color: #1abc9c;
  border-bottom: 2px solid #1abc9c;
}
.fyxm-tab div.active span {
  color: #1abc9c;
  font-weight: bold;
  border-bottom: 2px solid #1abc9c;
}
.fyxm-size {
  width: 100%;
}
.fyxm-hide,
.btn-hide {
  display: none;
}
.btn-show {
  display: block;
}
.fyxm-show {
  display: table;
}
.tong-search {
  width: 100%;
  padding: 13px 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.top-form {
  width: auto;
  margin-right: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
}
.top-form .top-label {
  width: auto;
  margin-right: 5px;
  color: #7f8fa4;
  font-size: 14px;
}
.top-form .top-zinle {
  width: auto;
  position: relative;
}
.jbxx {
  width: 100%;
  min-height: 100px;
  box-sizing: border-box;
  position: relative;
}
.jbxx-size {
  width: 100%;
  box-sizing: border-box;
  border: 1px dashed rgba(26, 188, 156, 0.3) !important;
  background: rgba(26, 188, 156, 0.03) !important;
  display: inline-table;
  padding: 10px 20px 15px 15px;
}
.jbxx-position {
  position: absolute;
  left: 10px;
  top: 0;
  height: 40px;
  min-width: 120px;
}
.jbxx-position span {
  display: block;
}
.jbxx-position .jbxx-top {
  width: auto;
  background: #fff;
  height: 1px;
}
.jbxx-position .jbxx-text {
  width: auto;
  position: absolute;
  left: 20px;
  top: -10px;
  color: #1abc9c;
  font-size: 16px;
}
.jbxx-box {
  width: 100%;
  padding: 10px 0 0;
}
.wh122 {
  width: 122px;
}
.wh140 {
  width: 140px;
}
.wh182 {
  width: 182px;
}
.wh240 {
  width: 240px;
}
.font14 {
  font-size: 14px;
}
.wxdj-star {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 18px;
}
.wxdj-star span {
  width: 60px;
  display: flex;
  justify-content: center;
  margin-right: 33px;
  flex-wrap: wrap;
  text-align: center;
  color: #bbc3cd;
}
.wxdj-star span em {
  display: block;
  width: 60px;
  height: 48px;
  padding-top: 6px;
}
.wxdj-star span .star-item {
  display: block;
  width: 51px;
  height: 48px;
}
.zui-table-view {
  color: #767d85;
}
.zui-table-view .zui-table-tool {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #eee;
  border-bottom: none;
  height: 66px;
  overflow: hidden;
  white-space: nowrap;
  bottom: 10px !important;
  position: fixed;
  right: 10px;
  left: 10px;
  background: #fff;
  z-index: 111;
}
.zui-table-view .zui-table-tool .page-count {
  font-size: 14px;
  color: #8a94a0;
  line-height: 36px;
  padding: 0 6px;
  margin-right: 0;
}
.zui-table-page a,
.zui-table-page span {
  float: left;
  display: inline-block;
  vertical-align: middle;
  height: 36px;
  border: 0;
  border-radius: 2px;
}
.zui-table-page .disabled,
.zui-table-page .dis-next {
  background-image: linear-gradient(0deg, #f2f4f7 0%, #fff 100%);
  border-right: 1px solid #dfe3e9;
  color: #313131 !important;
  cursor: pointer !important;
}
.zui-table-view .zui-table-tool .zui-table-page {
  margin-top: 6px !important;
}
.c_radio {
  float: left;
  color: #354052;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-right: 20px;
}
.c_radio .lb_text {
  padding-left: 5px;
}
.c_radio > input {
  display: none;
}
.c_radio label {
  vertical-align: middle;
  line-height: 16px;
  display: inline-block;
  height: 16px;
  font-size: 14px;
}
.c_radio > input:checked + label::before,
.c_radio > input:checked + label::after {
  display: block;
}
.c_radio > input + label {
  position: relative;
  cursor: pointer;
  vertical-align: middle;
}
.c_radio > input + label::before {
  position: relative;
  top: 0;
  left: 0;
  display: inline-block;
  width: 16px;
  height: 16px;
  content: '';
  background: url("/newzui/css/images/dx.png") center no-repeat;
  background-size: 16px 16px;
}
.c_radio > input + label::after {
  content: "";
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 16px;
  height: 16px;
  background: url("/newzui/css/images/dx_h.png") center no-repeat;
  background-size: 16px 16px;
}
.zui-left .zui-input {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: left;
}

.wj_x{
  width: 10px;
  height: 14px;
  background: url("/newzui/css/images/wj_x.png") center no-repeat;
  background-size: 10px 14px;
}
.wj_s{
  width: 10px;
  height: 14px;
  background: url("/newzui/css/images/wj_s.png") center no-repeat;
  background-size: 10px 14px;
}

/*图片上传样式*/
.vue-uploader .file-list {
  padding: 10px 0px;
}
.vue-uploader .file-list:after {
  content: '';
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
  font-size: 0;
}
.vue-uploader .file-list .file-item {
  float: left;
  position: relative;
  width: 100px;
  text-align: center;
}
.vue-uploader .file-list .file-item img{
  width: 80px;
  height: 80px;
  border: 1px solid #ececec;
}
.vue-uploader .file-list .file-item .file-remove {
  position: absolute;
  right: 12px;
  display: none;
  top: 4px;
  width: 14px;
  height: 14px;
  color: white;
  cursor: pointer;
  line-height: 12px;
  border-radius: 100%;
  transform: rotate(45deg);
  background: rgba(0, 0, 0, 0.5);
}
.vue-uploader .file-list .file-item:hover .file-remove {
  display: inline;
}
.vue-uploader .file-list .file-item .file-name {
  margin: 0;
  height: 40px;
  word-break: break-all;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.vue-uploader .add {
  width: 80px;
  height: 80px;
  margin-left: 10px;
  float: left;
  text-align: center;
  line-height: 80px;
  border: 1px dashed #ececec;
  font-size: 30px;
  cursor: pointer;
}

.vue-uploader > input[type="file"] {
  display: none;
}
/*end*/

.flex-none{
display: inherit;
overflow: hidden;
}
.fr-right{
float: right;
}
.percent100{
width: 100% !important;
}
