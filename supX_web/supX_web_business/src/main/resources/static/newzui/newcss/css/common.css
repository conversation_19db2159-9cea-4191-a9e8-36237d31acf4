@import url(complete.min.css);
.zui-select-group {
    display: none;
}


#loginPop{
    z-index: 1111111;
    position: fixed;;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: #fff;
}
.login {
    position: absolute;
    margin: 0 auto;
    width: 400px;
    height: 280px;
    border-radius: 4px;
    box-shadow: #bbb 0 0 10px;
    background-color: #FFFFFF;
    font-size: 14px;
    padding: 10px 40px;
    left: 50%;
    transform: translate(-50%,-50%);
    top: 50%;
}
.login p {
    font-size: 14px;
    margin-bottom: 30px;
}
.loginContent {
    width: 210px;
    margin: 0 auto;
    text-align: right;
}
.login button {
    float: right;
}
.loginDiv button {
    padding: 6px 20px;
    border: 0;
    border-radius: 2px;
    cursor: pointer;
    margin: 3px;
    background-color: #1AB394;
    color: #fff;
}
.loginContent input {
    width: 150px;
    border:1px solid #eee;
    height: 30px;
}

/*淇℃伅鎻愮ず鐨勬牱寮�*/
.promptDiv {
    position: fixed;
    width: 100%;
    z-index: 999999;
    text-align: center;
    pointer-events: none;
    margin: 0 auto;
    height: 100%;
    top: 0;
}

.icon_success:before{
    content: '';
    background-image: url("/newzui/pub/image/<EMAIL>");
    width: 20px;
    color: rgba(4, 77, 63, .5);
    vertical-align: middle;
    display: inline-block;
    height: 20px;
    border-radius: 100%;
    margin-right: 10px;
    background-position: center center;
    background-size: contain;
    /*padding:0 10px 0 22px;*/
}
.icon_defeadted:before{
    content: "";
    background-image: url("/newzui/pub/image/close.png");
    width: 20px;
    background-position: center center;
    background-size: contain;
    vertical-align: middle;
    border-radius: 100%;
    color: rgba(78, 43, 5, .5);
    height: 20px;
    display: inline-block;
    margin-right: 10px;

    /*padding:0 10px 0 22px;*/
}
.promptDiv .prop_left {
    position: absolute;
    left: 20px;
    top: 50%;
}

.promptDiv .prop_right {
    position: absolute;
    right: 20px;
    top: 50%;
}

.promptDiv .prop_bottom {
    position: absolute;
    bottom: 20px;
}

.promptDiv .prop_top {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translate(-50%, 0px);
}

.prompt {
    min-width: 100px;
    color: #FFFFFF;
    padding: 11px 20px;
    position: relative;
    border-radius: 4px;
    opacity: 1;
    transition: opacity 1s ease;
}

.prompt.success {
    box-shadow: 0 0 10px 3px rgba(4, 77, 63, .3);
    background: #1abc9c;
    color: #FFFFFF;
}

.prompt.defeadted {
    box-shadow: 0 0 10px 3px rgba(78, 43, 5, .3);
    background: #f2a654;
    color: #FFFFFF;
}

.zui-form .select-right .zui-select-none {
    position: absolute;
    z-index: 100;
    zoom: 1;
    top: 0;
    right: 12px;
    width: auto;
    max-width: 48px;
}

.zui-table-view .zui-table-header {
    width: 97.1% !important;
}

/*.zui-table-view .fieldlist{*/
/*position: fixed!important;*/
/*}*/
.zui-table-view .zui-table-header .zui-table-cell {
    text-align: center;
}

.zui-table-view table th, .zui-table-view table td {
    border-bottom: 1px #eee solid;
    border-left: 1px #eee solid;
    border-right: 1px #eee solid;
}

.center {
    text-align: center;
}

.optionBtu {
    position: relative;
    float: left;
    text-align: center;
    border-width: 0;
    border-style: initial;
    border-color: initial;
    border-image: initial;
    display: inline-block;
    padding: 4px 12px;
    font-size: 14px;
    line-height: 1.6;
    border-radius: 3px;
    white-space: nowrap;
    vertical-align: middle;
    font-weight: 400;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #fff;
    color: #767d85;
}

.toDayTable tbody td:hover {
    background-color: #fff5ea;
    cursor: pointer;
}

.bgColor {
    border: 1px #ffa847 dashed;
    background-color: #fff5ea;
}

.addPb {
    display: block;
    line-height: 24px;
    border-radius: 2px;
    margin: 4px 0;
    position: relative;
}

.addPb span {
    display: none;
    position: absolute;
    z-index: 1;
    zoom: 1;
    cursor: pointer;
    top: -4px;
    right: -4px;
    width: 14px;
    height: 14px;
    line-height: 14px;
    color: #fff;
    background-color: red;
    border-radius: 100%;
    text-align: center;
}

.option > div > span {
    display: block;
    float: left;
    margin-top: 6px;
}

.option > div {
    float: left;
    width: 250px;
    text-align: left;
}

.option {
    width: 100%;
    display: inline-block;
    padding: 20px 0 20px 45px;
}

.toolDate {
    text-align: center;
    font-size: 18px;
    padding: 3px 0;
    margin-bottom: 20px;
    color: #676767;
}

.toDayBg {
    background-color: #E8F7F4;
}

.f-hzgl ul.tabs li:active {
    background: #ffffff;
}

.showC {
    display: block !important;
}

.toolBtu {
    float: right;
    cursor: pointer;
}

.drag {
    position: absolute !important;
    z-index: 900;
}

.dragCSS {
    cursor: -webkit-grab;
}

.toDayTable .optionBtu {
    float: none;
    margin: 6px auto;
}

/*.optionBtu span {*/
/*display: none;*/
/*}*/

/*.optionBtu > span {*/
/*position: absolute;*/
/*right: 0;*/
/*top: -8px;*/
/*color: red;*/
/*cursor: pointer;*/
/*}*/

/*.uitable_0 .cell-0-0 {*/
/*width: 80px*/
/*}*/

/*.uitable_0 .zui-table-cell {*/
/*width: 100px*/
/*}*/
/*.uitable_0 .zui-table-body tr:hover, .uitable_0 .zui-table-body tr.table-hover {*/
/*background-color: #fff5ea;*/
/*color: #f07666;*/
/*}*/

.zui-table-view .zui-table-cell {

}

/*::-webkit-scrollbar{*/
/*height: 0px;*/
/*width: 0px;*/
/*border-radius: 10px;*/
/*background: #eee;*/
/*}*/

::-webkit-scrollbar-track-piece {
    background-color: transparent;
}

::-webkit-scrollbar {
    width: 6px;
    height: 5px;
}
.no-scrollbar::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

::-webkit-scrollbar-thumb {
    background-color: transparent;
    background-clip: padding-box;
    height: 100px;
}

.no-user-select{
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-user-select: none;
    user-select: none;
}

.btn-parmary-b:hover i{
    opacity: .6;
}

.btn-parmary:hover i{
    opacity:  0.6;
}

/*::-webkit-scrollbar-thumb:hover {*/
/*background-color:rgba(0,0,0,0.5);*/
/*}*/
.xmzb-content-left:hover ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.5);
}

.xmzb-content-right:hover ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.5);
}

.zui-table-tool {
    /*width: 100%;*/
    height: 66px;
    /*border-top: 1px solid #eee;*/
    background-color: #ffffff;
    border: 1px solid #eee;
    overflow: hidden;
    white-space: nowrap;
    position: fixed;
    bottom: 10px;
    z-index: 111;
    right: 10px;
    left: 10px;
}

.background {
    position: fixed;
    top: 10px;
    width: 98.1%;
    z-index: 999;
}

.backgroundpb {
    position: fixed;
    top: 0;
    width: 96.4%;
    z-index: 111;
    margin: 0 15px;
}

.pbwh {
    margin: 39px 15px 12px;
}

.zui-table-tool .zui-table-page {
    margin: 8px 0;
}

.zui-table-page {
    display: inline-block;
    height: 26px;
    vertical-align: middle;
}

.zui-table-page .disabled, .zui-table-page .disabled:hover {
    color: #d2d2d2 !important;
    cursor: pointer !important;
}

input, button, textarea, select, optgroup, option {
    color: #767d85;
    resize: none;
}

.pophide.show {
    display: block;
}

.pophide {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.498039);
    transition: opacity 0.3s ease;
    z-index: 111111;
}

.podrag.show {
    display: block;
}

.podrag {
    position: fixed;
    z-index: 10100111;
    top: 50%;
    width: 540px;
    display: none;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #ffffff;
    height: max-content;
}

.pop-width {
    width: 380px;
}

.pop-540 {
    width: 540px;
}

.pop-850 {
    width: 850px;
}

.bcsz-layer .layui-layer-title {
    background-color: #1abc9c;
    font-size: 16px;
    color: #fff;
}

.layui-mad {
    padding: 15px 10px 10px;
    min-height: 200px;
}

.layui-height {
    /*line-height: 100px;*/
    text-align: center;
    min-height: 100px;
}

.buttonbox {
    text-align: center;
    display: flex;
    justify-content: flex-end;

}

.zui_none input {
    border: none;
    border-bottom: 1px solid #dddddd;
}

.zui-no-border {
    border-radius: inherit;
    border: 1px solid #eeeeee;
    width: 94px;
    border-left: none;
    border-bottom: none;
}

.zui-no-border:nth-child(1) {
    border-left: 1px solid #eeeeee;
}

.rysx_bottom {
    margin: 12px 15px;
}

.rysx_bottom_list {
    margin: 15px 25px 13px;
    /*margin: 12px 15px;*/
}

.btn-bottom {
    position: relative;
    color: #1ABC9C !important;
}

.btn-bottom:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 0);
    width: 80%;
    height: 1px;
    background-color: #1ABC9C !important;

}

.panel-body {
    margin: 57px 15px 12px;
    padding: 0;
    /*margin: 12px 15px;*/
}

.hideList {
    display: none;
}

.showList {
    display: block;
}

/*.zui-table-view table tr{*/
/*border-left: 1px #eee solid;*/
/*}*/
/*.zui-table-view table th{*/
/*border: 1px #eee solid;*/
/*}*/
.zui-table-view table th:nth-child(1), .zui-table-view table td:nth-child(1) {
    border-left: 1px #eee solid !important;
}

/** checkbox **/
input[type=radio].zui-radio {
    display: none;
}

input[type=radio].zui-radio + label {
    display: inline-block;
    padding-left: 18px;
    height: 22px;
    line-height: 22px;
    cursor: pointer;
    vertical-align: middle;
    padding-right: 5px;
}
input[type=radio].zui-radio + label:after{
    border: 1px solid #1ABC9C;
    content: '';
    position: absolute;
    z-index: 2;
    zoom: 1;
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
    width: 16px;
    height: 16px;
    border-radius: 100%;
}
input[type=radio].zui-radio + label:before {
    content: " ";
    width: 8px;
    height: 8px;
    line-height: 16px;
    font-family: 'WebIcons';
    position: absolute;
    z-index: 2;
    zoom: 1;
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
    /*margin-left: 6px;*/
    border: 1px #1ABC9C solid;
    background-color: #fff;
    font-size: 12px;
    border-radius: 50%;
    text-align: center;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

input[type=radio].zui-radio + label:hover:before {
    border-color: #1ABC9C;
}

input[type=radio].zui-radio:checked + label:before {
    content: "";
    background-color: #1ABC9C;
    border-color: #1ABC9C;
    color: #fff;
}

.bottom {
    position: fixed;
    bottom: 0;
    width: 97%;
    z-index: 11;
    left: 15px;
    text-align: right;
    background: #fff;
}

#tplink {
    position: absolute;
    margin-top: 5px;
    z-index: 111;
    font-size: 13px;
    padding: 5px;
    border-radius: 4px;
    background: #333333;
    color: #ffffff;
}

#tplink:before {
    position: absolute;
    left: 0;
    top: -5px;
    content: '';
    margin: 0 5px;
    width: 0;
    height: 0;
    right: 0;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
    border-bottom: 5px solid #333333;
}

.qiehuan {
    margin-bottom: 29px;
}

.wrapper {
    height: 100%;
    overflow: hidden;
    padding: 5px;
    border: 1px solid #eee;
}
.container{
    background-color: #ffffff;
}
.zui-border-bottom{
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.zui-table-fixed-header {
    position: fixed !important;
    background: #fff;
    z-index: 11;
    margin-top: -15px
}

.zui-table-body-top {
    margin-top: 39px
}

.btn-primary-b {
    color: #1abc9c;
    background: #ffffff;
    border: 1px solid #1abc9c;
}

.bg-fff {
    background-color: #ffffff !important;
}

.bg-f9f9f9 {
    background-color: #f9f9f9;
}

.top-xia {
    margin-top: 20px;
    padding: 0 15px;
}

.zui-table-view .zui-table-body tr td {
    position: relative;
}

.isactive {
    display: block !important;
}

.linkleft {
    position: absolute;
    left: 0;
    z-index: 111;
    top: 0;
    font-size: 13px;
    margin-left: 103%;
    padding: 10px;
    background: #ffffff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.19);
    border-radius: 4px;
    color: #757c83;
    /*height: 100%;*/
    display: flex;
    align-items: center;
    white-space: nowrap;
    text-align: left;
}
.linkdrop {
    position: absolute;
    left: 0;
    z-index: 111;
    top: 0;
    margin-top: 41px;
    font-size: 13px;
    padding: 10px;
    background: #ffffff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.19);
    border-radius: 4px;
    color: #757c83;
    /*height: 100%;*/
    display: inline-block;
    align-items: center;
    /*white-space: normal;*/
    /*text-align: left;*/
}

.linkdrop:before {
    position: absolute;
    left: 50%;
    top: -6px;
    content: '';
    width: 0;
    height: 0;
    transform: translate(-50%,0);
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #ffffff;
}
.linkleft:before {
    position: absolute;
    left: -5px;
    top: 50%;
    content: '';
    width: 0;
    height: 0;
    transform: translate(0, -50%);
    border-right: 5px solid #ffffff;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
}

.zui_bottom {
    /*margin-bottom: 20px;*/
    margin-bottom: 10px;
}

.showRize {
    position: absolute;
    background: #fff;
    width: 100%;
    left: 0;
    padding: 12px 15px;
}

.hide {
    display: none;
}

.show {
    display: inline-block !important;
}

.zui-auto {
    margin-left: 20px;
    width: auto !important;
}

.zui-btn.color-primary {
    background-color: initial;
    color: #1abc9c;
}

.zui-btn.color-primary:hover {
    background-color: inherit;
}

.color-primary:after {
    left: -3px;
    color: #1ABC9C;
    transform: translateY(-50%);
    margin-top: -2px;
}

.icon-font:last-child {
    /*margin-right: inherit;*/
}
.icon-bj_parent{
    display: flex;
    justify-content: center;
    align-items: center;
}
.icon-font {
    /*margin-right: 10px;*/
    vertical-align: middle;
}

.icon-font:before {
    font-size: 15px;
}

.zui-date .datenox {
    /*left:66px;*/
    /*margin-top: -10px;*/
    color: #c5d0de;;
}

.zui-hover:hover i {
    color: #1abc9c;
}

.zui-table-view .zui-table-body tr td {
    text-align: center;
}

.jizheng {
    color: #ff4532;
}

.yiban {
    color: #757c83;
}

.table {
    width: 850px;
    position: fixed;
    background: #fff;
    right: 0;
    z-index: 1111;
    bottom: 0;
}

.table .header {
    border: 1px solid #e9eee6;
    background: #1abc9c;
    height: 46px;
    line-height: 46px;
    padding-left: 18px;
    color: #ffffff;

}

.table .header .titel {
    position: relative;
}

.icon-shanchu {
    width: 47px;
    height: 47px;
    position: absolute;
    top: 0;
    right: 30px;
    cursor: pointer;
}

.icon-shanchu:after {
    right: 0;
    position: absolute;
    font-size: 20px;
    content: '';
    width: 22px;
    top: 44%;
    background: #fff;
    cursor: pointer;
    height: 3px;
    border: 1px solid #fff;
    transform: translate(-50%, 0%);
}

.edf2f1 {
    background: #edf2f1;
}

.min-chuangkou {
    background: #1abc9c;
    width: 303px;
    height: 46px;
    position: fixed;
    right: 0;
    bottom: 46px;
    line-height: 46px;
}

.min-chuangkou p {
    position: relative;
}

.min-chuangkou p .text-min {
    font-size: 16px;
    color: #ffffff;
    padding-left: 18px;
    text-align: left;
}

.min-chuangkou p .icon-min:before {
    position: absolute;
    content: '';
    top: 50%;
    width: 13px;
    height: 13px;
    right: 30px;
    margin-top: -4px;
    cursor: pointer;
    color: #ffffff;
    border-top: solid 2px currentColor;
    border-right: solid 2px currentColor;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.pop_jiaofeo {
    display: flex;
    background: #edf2f1;
    border: 1px solid #e9eee6;
    height: 34px;
}

.pop_jiaofeo li {
    text-align: center;
    width: calc((100%) / 6);
    line-height: 34px;
}

.pop-jiaofei-height {
    height: auto;
    background-color: #ffffff;
}

.pop-jiaofei-height li {
    height: 54px;
    line-height: 54px;

}

.color-btn {
    color: rgba(255, 255, 255, 0.56);
    font-size: 30px;
    cursor: pointer;
}

.guolv-header {

    background-image: linear-gradient(0deg, #f4f7fa 0%, #ffffff 100%);
    height: 34px;
    line-height: 34px;
}

.guolv-style {
    display: flex;
    width: 100%;
}

.guolv-bottom {
    /*margin-bottom: 20px;*/
}

.guolv-style .line {
    text-align: center;
    border: 1px solid #e6eaee;
    width: calc((100%) / 5);
    list-style: none;
}

.guolv-style .line:nth-child(1) {
    border-right: none;
}

.guolv-style .line:nth-child(2) {
    border-right: none;
}

.guolv-style .line:nth-child(3) {
    border-right: none;
    border-bottom: 1px solid #e6eaee;
}

.guolv-style .line:nth-child(4) {
    border-right: none;
}

.guolv-xinzeng {
    position: absolute;
    right: 15px;
    top: 15px;
}

.guolv-style .line span {
    cursor: pointer;
}

.layui-not-absolute {
    position: static !important;
}

.layui-txt {
    font-size: 16px;
    color: #ffffff;
    opacity: 0.56;
    float: left;
    margin-top: -4px;
    cursor: pointer;

}

.guolv-content .zui-input {
    height: 50px;
    margin-left: auto;
}

.guolv-content {
    float: left;
    width: 100%;
    max-height: 400px;
    height: 400px;
    overflow: auto;
    margin-bottom: 20px;
}

#isTabel .zui-800 {
    width: 800px;
}

#isTabel .zui-select-inline:after {
    margin-top: -26px;
}

.guolv-content .line {
    border: none;
    float: left;
    height: 50px;
    line-height: 50px;
}

.guolv-content .line:nth-child(5) {
    border: 1px solid #e6eaee;
    border-top: none;
}

.guolv-content .zui-select-inline {
    vertical-align: baseline;
}

#isTabel .layui-layer-title {
    height: 46px;
}

.zui-top {
    margin-bottom: 3px;
    font-size: 14px;
    color: #7f8fa4;
}

.zui-bottom {
    margin-bottom: 15px;
}

.jyxm .zui-form .zui-inline {
    padding: initial;
    width: 100%;
}

#jyxm_icon .podrag_right {
    right: 0;
    left: auto;
    transform: translate(0%, -50%);
}

.color-btn {
    color: rgba(255, 255, 255, 0.56);
    font-size: 30px;
    cursor: pointer;
}

i, em {
    font-style: normal;
}

.xmzb-db {
    min-width: 88px !important;
    padding: 0 12px;
    height: 36px !important;
}

.tab-message {
    width: 100%;
    height: 46px;
    background: #1abc9c;
    line-height: 46px;
    padding: 0 20px;
    color: #fff;
}

.fr {
    color: #ffffff;
    font-size: 18px!important;
    cursor: pointer;
    float: right;
}

.jszb .width-170 {
    width: 170px;
}

.col-width-3 {
    width: 30%;
    margin-bottom: 20px;
}

.height-72 {
    height: 72px;
}

.xuxian {
    position: relative;
    color: #1abc9c;
}

.xuxian:after {
    position: absolute;
    width: 94%;
    border-top: 1px dotted #1abc9c;
    content: '';
    left: 50px;
    top: 50%;
    transform: translate(0%, -50%);
}

.span-input {
    float: left;
    width: 59px;
    margin-right: 4px;
}

.jszb .zui-input-inline {
    display: flex;
    align-items: center;
}

.zui-input-inline span {
    /*color:#7f8fa4;*/
    /*padding-right: 3px;*/
}

.cankaozhi {
    margin-top: 17px;
}

.middle {
    vertical-align: middle;
}

.top-28 {
    margin-top: 28px;
    max-height: 36vh;
    height: 36vh;
    overflow: scroll;
}

.zui-input-72 {
    width: 72%;
}

.width-81 {
    width: 81%;
}

.left-20 {
    margin-right: 20px;
}

.bottom-4 {
    margin-bottom: 4px;
}

.max-height {
    max-height: 450px;
    overflow-x: auto;
}

.absolate {
    position: absolute;
    right: 28px;
    color: #1abc9c !important;
    top: 50%;
    transform: translate(0, -50%);
}

.abs {
    position: absolute;
    right: 8px;
    color: #1abc9c !important;
    top: 50%;
    transform: translate(0, -50%);
}

.left-right {
    margin: 0 10px;
    display: flex;
    align-items: center;
}

.cankao4 .buttom, .cankao5 .buttom, .cankao6 .buttom, .cankao8 .buttom, .cankao9 .buttom, .cankao10 .buttom {
    background-image: linear-gradient(0deg, #f2f4f7 0%, #ffffff 100%);
    border: 1px solid #dfe3e9;
    border-radius: 4px;
    width: 118px;
    line-height: 34px;
    text-align: center;
    height: 34px;
    display: inline-block;
    cursor: pointer;
    margin: 15px 0 11px;
}

.cankao4 .ck-header, .cankao5 .ck-header, .cankao6 .ck-header, .cankao8 .ck-header, .cankao9 .ck-header, .cankao10 .ck-header {
    border: 1px solid #e9eee6;
    width: 100%;

}

.cankao4 .ck-header .line, .cankao5 .ck-header .line, .cankao6 .ck-header .line, .cankao8 .ck-header .line, .cankao9 .ck-header .line {
    display: flex;
    white-space: nowrap;

}

.cankao4 .ck-header .line {
    flex-wrap: nowrap;
    height: 36px;

}

.cankao10 .ck-header .line {
    display: flex;
}

.cankao4 .ck-header .line .text {
    width: calc((100%) / 6);
    height: 34px;
    line-height: 34px;
    text-align: center;
    display: block;
    font-size: 14px;
    color: #333333;
}

.cankao4 .ck-header .line .text-calc {
    width: calc((100%) / 6);
    display: flex;
    align-items: center;
    justify-content: center;
}

.bottom-border {
    margin-bottom: 18px;
    /*border-bottom: 1px solid #e9eee6;*/
    padding-bottom: 18px;
}

.cankao5 .ck-header .line .text {
    width: calc((100%) / 3);
    height: 34px;
    line-height: 34px;
    text-align: center;
    display: block;
    margin-bottom: 8px;
    background: #edf2f1;
    font-size: 14px;
    color: #333333;
}

.cankao5 .ck-header .line .text-calc {
    width: calc((100%) / 3);
    display: flex;
    margin: 0 18px;
    align-items: center;
    justify-content: center;
}

.cankao6 .ck-header .line .text {
    width: calc((100%) / 4);
    height: 34px;
    line-height: 34px;
    text-align: center;
    display: block;
    margin-bottom: 8px;
    background: #edf2f1;
    font-size: 14px;
    color: #333333;
}

.cankao6 .ck-header .line .text-calc {
    width: calc((100%) / 4);
    display: flex;
    margin: 0 18px;
    align-items: center;
    justify-content: center;
}

.cankao8 .ck-header .line .text {
    width: calc((100%) / 4);
    height: 34px;
    line-height: 34px;
    text-align: center;
    display: block;
    margin-bottom: 8px;
    background: #edf2f1;
    font-size: 14px;
    color: #333333;
}

.cankao8 .ck-header .line .text-calc {
    width: calc((100%) / 4);
    display: flex;
    margin: 0 18px;
    align-items: center;
    justify-content: center;
}

.cankao9 .ck-header .line .text {
    width: calc((100%) / 6);
    height: 34px;
    line-height: 34px;
    float: left;
    text-align: center;
    display: block;
    margin-bottom: 8px;
    background: #edf2f1;
    font-size: 14px;
    color: #333333;
}

.cankao9 .ck-header .line .text-calc {
    width: calc((100%) / 6);
    display: flex;
    margin: 0 18px;
    align-items: center;
    justify-content: center;
}

@media only screen and (min-width: 1024px) and (max-width: 1024px) {
    .sjks-content-right .content-right-top i {
        width: calc((100% / 7));
        text-align: center;
        font-size: 12px !important;
        line-height: 16px !important;
    }
}

.cankao10 .ck-header .line .text {
    height: 34px;
    line-height: 34px;
    text-align: center;
    display: block;
    background: #edf2f1;
    cursor: pointer;
    float: left;
    width: 100%;
    font-size: 14px;
    color: #333333;
}

.cankao10 .ck-header .line .text .txt {
    /*margin: 0 18px;*/
    /*width: 100%;*/
    /*display: block;*/
}

.cankao10 .ck-header .line .text-calc {
    display: flex;
    /*margin: 0 18px;*/
    align-items: center;
    justify-content: center;
}

.cankao10 .ck-header .line .text.absol, .cankao10 .ck-header .line .text-calc.absol {
    position: absolute;
    right: 1px;
    z-index: 1;
    height: 32px;
    margin: 0;
    padding: 0 10px;
}

.cankao10 {
    width: 100%;
    position: relative;
}

.cankao10 .ck-header {
    width: 100%;
    overflow: auto;
    min-height: 26vh;
}

/**/
.cssz-list {
    width: 100%;

}

.cssz-list li {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.cssz-list li i {

}

.bg-f2 {
    background: #edf2f1;
}

.color-zy {
    color: #13a950 !important;
}

.color-ty {
    color: #ff4532;
}

.bg-1abc9c {
    background: #1abc9c;
    color: #ffffff;
    overflow: hidden;
    height: 46px;
}

.zui-active {
    background: #ffffff;
    color: #1abc9c;
    border-radius: inherit;
    border-top: 2px solid #1abc9c;
}

.botom-29 {
    margin-bottom: 29px;
}

.width-50 {
    width: 52% !important;
    margin-bottom: 20px;
}

.width-59 {
    width: 59% !important;
    margin-bottom: 20px;
}

.width-56 {
    width: 59% !important;
    margin-bottom: 20px;
}

.width-botton {
    margin-bottom: 20px;
}

.width-20 {
    width: 21% !important;
    margin-bottom: 20px;
}

.left-right {
    min-width: 182px;
    max-width: 266px;
    float: left;
    text-align: right;
    margin-right: 10px;
    display: flex;
    color: #7f8fa4;
    align-items: center;
    justify-content: flex-end;
    align-self: center;
    height: 32px;
}

.width-226 {
    width: 266px;
}

.cm {
    position: absolute;
    right: 9px;
    top: 50%;
    transform: translate(0, -50%);
    color: #1abc9c !important;
}

.lansexuxiantxt {
    position: relative;
    float: left;
    padding: 27px 0 0 0;
}

.pa-l-20 {
    padding-left: 20px !important;

}

.bgcolor {
    background: rgba(26, 188, 156, 0.06);
}

/*.lansexuxian:after{*/
/*position: absolute;*/
/*border: 1px dashed #c9ece5;*/
/*content: '';*/
/*top: 0;*/
/*left: 0;*/
/*!*padding: 27px 0 0 0;*!*/
/*width: 73%;*/
/*height: 100%;*/
/*}*/
.lansexuxiantxt .t {
    position: absolute;
    top: 16px;
    z-index: 1;
    width: auto;
    left: 30px;
    text-align: center;
    min-width: 100px;
    color: #1abc9c;
    height: 45px;
}

.lansexuxian {
    border-right: 1px dashed #c9ece5;
    border-left: 1px dashed #c9ece5;
}

.lansexuxian:nth-child(1) {

}

.lansexuxianfist {
    border-right: 1px dashed #c9ece5;
    border-left: 1px dashed #c9ece5;
    border-top: 1px dashed #c9ece5;
    padding-top: 20px;
}

.lansexuxianlast {
    border-bottom: 1px dashed #c9ece5;
    border-right: 1px dashed #c9ece5;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-left: 1px dashed #c9ece5;
}

.zui-88 {
    width: 88px;
}

.btn-f2a654 {
    color: #ffffff;
    background-color: #f2a654;
}

.text-right {
    text-align: right;
}

.padd-r5 {
    padding: 4px 11px 4px 5px;
}

.ybhsgl-height {
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
}

.ybhsgl-height button {
    height: 100%;
}

input[type=checkbox].green + label {
    display: inline-block;
    padding-left: 18px;
    height: 18px;
    line-height: 18px;
    position: relative;
    cursor: pointer;
    color: #a7b1c2;
    vertical-align: middle;
}

input[type=checkbox].green + label:before {
    content: " ";
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-family: 'WebIcons';
    position: absolute;
    z-index: 2;
    zoom: 1;
    top: 50%;
    left: 0;
    margin-top: -8px;
    border: 1px #d2d2d2 solid;
    background-color: #fff;
    font-size: 12px;
    border-radius: 2px;
    text-align: center;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

input[type=checkbox].green:checked + label:before {
    content: "\f192";
    background-color: #1ABC9C;
    border-color: #1ABC9C;
    color: #fff;
}

input[type=checkbox].green {
    display: none;
}

.uitable_1 .cell-1-0 {
    /*width: auto !important;*/
}

.pc-select {
    width: 150px;
    height: 36px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.label-em {
    position: absolute;
    top: 10px;
    left: 96px;
    z-index: 999;
}

.fs-select {
    width: 70%;
}

.left-right-no {
    min-width: auto;
    max-width: inherit;
}

.zui-height-36 {
    height: 36px;
    line-height: 36px;
}

.ksys-side span i {
    color: #7f8fa4;
    text-indent: 6px;
}

.overflow1 {
    height: 53px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: 100%;
    line-height: 53px;
}

.zui-table-cell {
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap
}

.xmzb-top-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.xmzb-top-left i {
    margin-right: 10px;
    color: #7f8fa4;
}

::placeholder {
    color: rgba(53, 64, 82, .3) !important;
}

.zui-input::placeholder {
    color: rgba(53, 64, 82, .3) !important;
}

.icon-bgzdgl {
    margin-left: 10px;
    vertical-align: middle;
}

.cell-1-10 {
    display: flex;
    justify-content: center;
    align-items: center;
}

.guolv-bottom .zui-input {
    border-top: none;
}

.buttond {
    position: absolute;
    bottom: 10px;
}

.popShow {
    position: fixed;
    z-index: 1111;
    top: 0;
    width: 100%;
    height: 100%;
}

.zui-table-body {
    /*margin-bottom: 66px;*/
    /*height: 74.7vh;*/
    /*min-height: 74.7vh;*/
}

.icon-ms {
    position: relative;
    margin: -14px 22px 0 7px;
}

.icon-tz:after {
    position: static;
    content: '';
    left: 0;
    background-image: url("../pub/image/tz.png");
    width: 17px;
    height: 16px;
    vertical-align: text-top;
    background-position: center center;
    background-size: 127% 139%;
    background-repeat: no-repeat;
    display: inline-block;
}
.icon-tz:hover:after{
    content: '';
    background-image:url("/newzui/pub/image/tzfter.png");
}
.icon-ms:after {
    position: absolute;
    content: '';
    left: 0;
    margin-right: 10px;
    background-image: url("../pub/image/<EMAIL>");
    width: 17px;
    height: 16px;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
}

.icon-tzfter:before {
    content: '';
    left: 0;
    background-image: url("../pub/image/tzfter.png");
    width: 17px;
    height: 16px;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    display: inline-block;
}

.icon-mb:before {
    content: '';
    left: 0;
    background-image: url("../pub/image/mb.png");
    width: 17px;
    height: 16px;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    display: inline-block;
}

.icon-fzyz:before {
    content: '';
    left: 0;
    background-image: url("../pub/image/fzyz.png");
    width: 17px;
    height: 16px;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    display: inline-block;
}

.icon-ms:hover {
    background-image: url("../pub/image/<EMAIL>");
}

.icon-bj {
    /*margin: -14px 40px 0 -7px;*/
    /*position: relative;*/
}

.icon-bj:before {
    content: '';
    background-image: url("../pub/image/msx.png");
    width: 17px;
    height: 16px;
    background-position: center center;
    background-size: 20px 20px;
    background-repeat: no-repeat;
    display: inline-block;
    margin-right: 10px;
    vertical-align: text-top;
}

.icon-bj:hover:before {
    content: '';
    background-image: url("../pub/image/msx_h.png");
}
.icon-bg {
    background-image: url("../pub/image/<EMAIL>");

}

.icon-bg-style {
    position: relative;
    width: 15px;
    height: 15px;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
    vertical-align: text-top;
}

.cursor-bg {
    position: relative;
}

.icon-tx {
    position: relative;
    background-image: url("../pub/image/<EMAIL>");
    width: 15px;
    height: 15px;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
    vertical-align: text-top;
}

.icon-tx:hover {

}

.ysb-red {
    color:#ff5c63;
}
.ysb-black{
    color:#757c83;
}
.ysb-green {
    color: #1abc9c;
}
.ysb_zs {
    color:#a389d4;
}
.ysb-blue {
    color: #04a9f5;
}
.ysb-yellow{
    color:#f3a74f;
}
.cursor {
    font-size: 12px;
    position: relative;
    cursor: pointer;
}

.cursor-bg-active .text {
    color: #1abc9c;
}

.cursor-bg-active .cursor-active-bg {

    background-image: url("../pub/image/<EMAIL>");
}

.cursor-tx-active {
    background-image: url("../pub/image/<EMAIL>");
}

.cursor-bg:after {
    position: absolute;
    right: 0;
    top: 50%;
    content: '';
    transform: translate(0, -50%);
    width: 1px;
    height: 70%;
    background-color: #9fa9ba;
}

.icon-abs {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translate(0, -50%);
}

.position {
    position: relative;
}

.bgfdgl-right {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translate(0, -50%);
}

.left-15 {
    padding: 0 15px;
}

.table-hovers {
    background: rgba(26, 188, 156, 0.08) !important;
    border: 1px solid #1abc9c !important;
    box-shadow: 0 0 6px 0 rgba(26, 188, 156, 0.45);
}

.icon-plyr:before {
    content: '';
    background: url(./../pub/image/<EMAIL>) center left no-repeat;
    background-size: 20px 20px;
    position: absolute;
    width: 20px;
    height: 20px;
    left: 6px;
    top: 6px;
}

.icon-plyc:before {
    content: '';
    background: url(./../pub/image/<EMAIL>) center left no-repeat;
    background-size: 20px 20px;
    position: absolute;
    width: 20px;
    height: 20px;
    left: 6px;
    top: 6px;
}

.padd-l28 {
    padding-left: 28px !important;
}

.ck-header::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, .12);
}

.ck-header::-webkit-scrollbar {
    width: 2px;
    height: 10px;
    border-radius: inherit;
}

.tree_text1 {
    display: inline-block;
    padding: 0 7px;
    cursor: pointer;
    border-radius: 4px;
}
/*.tree_text1:hover{*/
/*color: #1abc9c;*/
/*}*/
.toggleIMg {
    width: 21px;
    height: 18px;
    vertical-align: sub;
}

.tree_tem1 ul {
    padding-left: 22px;
}

.tree_tem1 label {
    float: left;
    margin: 7px 6px 0 0;
}

#xtmktree {
    width: 20%;
    padding-top: 13px;
    border: 1px solid #eee;
    margin: 0;
}

.bold {
    color: rgba(51,51,51,0.86);
}

.xtmktreediv {
    cursor: pointer;
    /*height: 31px;*/
    line-height: 31px;
}
body .selectGroup table tr:hover {
    background: #edfaf7 !important;
    background-color: #edfaf7;
}
.zui-select-group .fixed {
    background: none !important;
    overflow: auto;
}
.zui-select-group table tr:first-child {
    background: #edf2f1;
    color: #333333;
}
.zui-select-group .fixed table {
    background: none !important;
}

.zui-select-group table {
    overflow: auto;
}
.zui-select-group:hover ::-webkit-scrollbar {
    background: rgba(47, 64, 80, 0.46);
}
body .selectGroup table tr.tableTrSelect {
    /*background-color: #ffe48d !important;*/
    /*background-color: #1abc9c !important;*/
    color: #000000 !important;
}

.patientTable tr {
    width: auto;
}

.left-radio {
    margin-left: 24px;
}

.left-radio input {
    margin-right: 9px;
    vertical-align: unset
}

.left-radio p {
    font-size: 14px;
    color: #9fa9ba;
    margin-bottom: 3px;
}

.green-radius + label {
    background: #ffffff;
    border: 1px solid #1abc9c;
    height: 20px;
    width: 20px;
    line-height: 14px;
    display: inline-block;
    margin-right: 9px;
    border-radius: 100%;
    position: relative;
}

input[type=radio] + i {
    border-radius: 100%;
    margin-right: 9px;
}

label {
    font-size: 12px;
    cursor: pointer;
}

.green-radio i {
    font-size: 12px;
    font-style: normal;
    display: inline-block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    vertical-align: middle;
    margin: -2px 2px 1px 0;
    border: #1abc9c 1px solid;
}

.green-radio input[type=radio] {
    display: none;
}

.green-radio input[type=radio]:checked + i {
    background-color: #1abc9c;
}

.green-radio input[type=radio]:disabled + i {
    border-color: #ccc;
}

.green-radio input[type=radio]:checked:disabled + i {
    background-color: #ccc;
}

.printArea {
    display: none;
}

.trendDiv {
    position: relative;
    width: calc(100% - 2px);
    height: 30px;
}

/* flex布局方案begin */
.flex-container{
    display: -ms-flexbox;
    display: -ms-flex;
    display: -moz-box;
    display: -moz-flex;
    display: -o-flex;
    display: -webkit-flex;
    display: -webkit-box;
    display: flex;
}
.flex-dir-rr{ /*主轴为横轴 顺序与文档流相反*/
    -webkit-flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse;
}
.flex-dir-c{ /*主轴为纵轴 顺序与文档流相同*/
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -moz-box-orient: vertical;
    -webkit-box-orient: vertical;
}
.flex-dir-cr{ /*主轴为纵轴 顺序与文档流相反*/
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
}
.flex-wrap-w{ /*允许子项目折行显示 顺序与文档流相同*/
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}
.flex-wrap-wr{ /*允许子项目折行显示 顺序与文档流相反*/
    -ms-flex-wrap: wrap-reverse;
    -webkit-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse;
}
.flex-jus-e{ /*子项目在主轴方向上 尾部对齐*/
    -moz-box-pack: end;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
}
.flex-jus-c{ /*子项目在主轴方向上 居中对齐*/
    -moz-box-pack: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}
.flex-jus-sp{ /*子项目在主轴方向上 均匀分布 首尾子项到父容器的距离是子项间距的一半*/
    -moz-box-pack: space-around;
    -webkit-box-pack: space-around;
    -webkit-justify-content: space-around;
    justify-content: space-around;
}
.flex-jus-sp{ /*子项目在主轴方向上 均匀分布 首尾子项到父容器的距离为0*/
    -moz-box-pack: space-between;
    -webkit-box-pack: space-between;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}
.flex-align-s{ /*子项目在侧轴上 头部对齐（宽高不被拉伸）*/
    -moz-box-align: start;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start;
}
.flex-align-e{ /*子项目在侧轴上 尾部对齐（宽高不被拉伸）*/
    -moz-box-align: end;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    align-items: flex-end;
}
.flex-align-c{ /*子项目在侧轴上 居中对齐（宽高不被拉伸）*/
    -moz-box-align: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
}
.flex-align-b{ /*子项目在侧轴上 首行文字的基线对齐（宽高不被拉伸）*/
    -moz-box-align: baseline;
    -webkit-box-align: baseline;
    -webkit-align-items: baseline;
    align-items: baseline;
}
.flex-one{
    -webkit-flex: 1;
    -ms-flex: 1;
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    flex: 1;
}
.flex-tow{
    -webkit-flex: 2;
    -ms-flex: 2;
    -moz-box-flex: 2;
    -webkit-box-flex: 2;
    flex: 2;
}
.flex-three{
    -webkit-flex: 3;
    -ms-flex: 3;
    -moz-box-flex: 3;
    -webkit-box-flex: 3;
    flex: 3;
}
.flex-four{
    -webkit-flex: 4;
    -ms-flex: 4;
    -moz-box-flex: 4;
    -webkit-box-flex: 4;
    flex: 4;
}
.flex-five{
    -webkit-flex: 5;
    -ms-flex: 5;
    -moz-box-flex: 5;
    -webkit-box-flex: 5;
    flex: 5;
}
.flex-six{
    -webkit-flex: 6;
    -ms-flex: 6;
    -moz-box-flex:6;
    -webkit-box-flex: 6;
    flex: 6;
}
.flex-seven{
    -webkit-flex: 7;
    -ms-flex: 7;
    -moz-box-flex: 7;
    -webkit-box-flex: 7;
    flex: 7;
}
.flex-eight{
    -webkit-flex: 8;
    -ms-flex: 8;
    -moz-box-flex: 8;
    -webkit-box-flex: 8;
    flex: 8;
}
.flex-nine{
    -webkit-flex: 9;
    -ms-flex: 9;
    -moz-box-flex: 9;
    -webkit-box-flex: 9;
    flex: 9;
}
/* flex布局方案end */


/*栅格系统 begin*/
/* 示例代码 没有行的概念  当一行放不下的时候会自动换行排列
<div class="grid-box">
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
</div>
*/
.grid-box:after,
.grid-box:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}

.grid-box [class*='col-'] {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    float: left;
}

.grid-box .col-m-1,
.grid-box .col-s-1,
.grid-box .col-l-1,
.grid-box .col-xl-1,
.grid-box .col-xxl-1 {
    width: 8.33333333%;
}

.grid-box .col-m-2,
.grid-box .col-s-2,
.grid-box .col-l-2,
.grid-box .col-xl-2,
.grid-box .col-xxl-2 {
    width: 16.6666667%;
}

.grid-box .col-m-3,
.grid-box .col-s-3,
.grid-box .col-l-3,
.grid-box .col-xl-3,
.grid-box .col-xxl-3 {
    width: 25%;
}
.grid-box .col-mx-5,
.grid-box .col-sx-5,
.grid-box .col-lx-5,
.grid-box .col-xlx-5,
.grid-box .col-xxlx-5 {
    width:20%;
}
.grid-box .col-m-4,
.grid-box .col-s-4,
.grid-box .col-l-4,
.grid-box .col-xl-4,
.grid-box .col-xxl-4 {
    width: 33.33333333%;
}

.grid-box .col-m-5,
.grid-box .col-s-5,
.grid-box .col-l-5,
.grid-box .col-xl-5,
.grid-box .col-xxl-5 {
    width: 41.66666666666667%;
}

.grid-box .col-m-6,
.grid-box .col-s-6,
.grid-box .col-l-6,
.grid-box .col-xl-6,
.grid-box .col-xxl-6 {
    width: 50%;
}

.grid-box .col-m-7,
.grid-box .col-s-7,
.grid-box .col-l-7,
.grid-box .col-xl-7,
.grid-box .col-xxl-7 {
    width: 58.33333333333333%;
}

.grid-box .col-m-8,
.grid-box .col-s-8,
.grid-box .col-l-8,
.grid-box .col-xl-8,
.grid-box .col-xxl-8 {
    width: 66.66666666666667%;
}

.grid-box .col-m-9,
.grid-box .col-s-9,
.grid-box .col-l-9,
.grid-box .col-xl-9,
.grid-box .col-xxl-9 {
    width: 75%;
}

.grid-box .col-m-10,
.grid-box .col-s-10,
.grid-box .col-l-10,
.grid-box .col-xl-10,
.grid-box .col-xxl-10 {
    width: 83.33333333333333%;
}

.grid-box .col-m-11,
.grid-box .col-s-11,
.grid-box .col-l-11,
.grid-box .col-xl-11,
.grid-box .col-xxl-11 {
    width: 91.66666666666667%;
}

.grid-box .col-m-12,
.grid-box .col-s-12,
.grid-box .col-l-12,
.grid-box .col-xl-12,
.grid-box .col-xxl-12 {
    width: 100%;
}

@media only screen and (min-width: 1600px) {
    .grid-box .col-xxl-1 {
        width: 8.33333333%;
    }

    .grid-box .col-xxl-2 {
        width: 16.6666667%;
    }

    .grid-box .col-xxl-3 {
        width: 25%;
    }

    .grid-box .col-xxl-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xxlx-5 {
        width: 20%;
    }

    .grid-box .col-xxl-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-xxl-6 {
        width: 50%;
    }

    .grid-box .col-xxl-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-xxl-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-xxl-9 {
        width: 75%;
    }

    .grid-box .col-xxl-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-xxl-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-xxl-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1600px) {
    .grid-box .col-xl-1 {
        width: 8.33333333%;
    }

    .grid-box .col-xl-2 {
        width: 16.6666667%;
    }

    .grid-box .col-xl-3 {
        width: 25%;
    }

    .grid-box .col-xl-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xlx-5 {
        width: 20%;
    }
    .grid-box .col-xl-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-xl-6 {
        width: 50%;
    }

    .grid-box .col-xl-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-xl-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-xl-9 {
        width: 75%;
    }

    .grid-box .col-xl-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-xl-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-xl-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1366px) {
    .grid-box .col-x-1 {
        width: 8.33333333%;
    }

    .grid-box .col-x-2 {
        width: 16.6666667%;
    }

    .grid-box .col-x-3 {
        width: 25%;
    }

    .grid-box .col-x-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xx-5 {
        width: 20%;
    }
    .grid-box .col-x-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-x-6 {
        width: 50%;
    }

    .grid-box .col-x-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-x-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-x-9 {
        width: 75%;
    }

    .grid-box .col-x-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-x-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-x-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1024px) {
    .grid-box .col-s-1 {
        width: 8.33333333%;
    }

    .grid-box .col-s-2 {
        width: 16.6666667%;
    }

    .grid-box .col-s-3 {
        width: 25%;
    }

    .grid-box .col-s-4 {
        width: 33.33333333%;
    }
    .grid-box .col-sx-5 {
        width: 20%;
    }
    .grid-box .col-s-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-s-6 {
        width: 50%;
    }

    .grid-box .col-s-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-s-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-s-9 {
        width: 75%;
    }

    .grid-box .col-s-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-s-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-s-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 768px) {
    .grid-box .col-m-1 {
        width: 8.33333333%;
    }

    .grid-box .col-m-2 {
        width: 16.6666667%;
    }

    .grid-box .col-m-3 {
        width: 25%;
    }

    .grid-box .col-m-4 {
        width: 33.33333333%;
    }
    .grid-box .col-mx-5 {
        width: 20%;
    }
    .grid-box .col-m-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-m-6 {
        width: 50%;
    }

    .grid-box .col-m-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-m-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-m-9 {
        width: 75%;
    }

    .grid-box .col-m-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-m-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-m-12 {
        width: 100%;
    }
}

/*栅格系统end*/

/*定义绿色虚线边框的卡片式ui结构*/
/* 示例代码
<div class="tab-card">
	<div class="tab-card-header">
		<div class="tab-card-header-title">卡片信息</div>
	</div>
	<div class="tab-card-body">
		这里放内容即可
	</div>
</div>
*/
.tab-card {
    padding: 10px;
    position: relative;
    background-color: #fff;
    margin-bottom: 2px;
}

.tab-card .tab-card-header {
    position: absolute;
    top: 10px;
    left: 20px;
    border-top: 1px solid #fff;
}

.tab-card .tab-card-header .tab-card-header-title {
    padding: 0 15px;
    font-size: 16px;
    margin-top: -11px;
    color: #1abc9c;
}

.tab-card .tab-card-body {
    border: 1px dashed rgba(26, 188, 156, 0.3) !important;
    background: rgba(26, 188, 156, 0.018) !important;
    padding: 20px 10px 10px;
}

/*定义绿色虚线边框的卡片式ui结构end*/

.left-radio {
    margin-left: 24px;
}

.left-radio input {
    margin-right: 9px;
    vertical-align: unset
}

.left-radio p {
    font-size: 14px;
    color: #9fa9ba;
    margin-bottom: 3px;
}

.green-radius + label {
    background: #ffffff;
    border: 1px solid #1abc9c;
    height: 20px;
    width: 20px;
    line-height: 14px;
    display: inline-block;
    margin-right: 9px;
    border-radius: 100%;
    position: relative;
}

input[type=radio] + i {
    border-radius: 100%;
    margin-right: 9px;
}

label {
    font-size: 12px;
    cursor: pointer;
}

.green-radio i {
    font-size: 12px;
    font-style: normal;
    display: inline-block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    vertical-align: middle;
    margin: -2px 2px 1px 0;
    border: #1abc9c 1px solid;
}

.green-radio input[type=radio] {
    display: none;
}

.green-radio input[type=radio]:checked + i {
    background-color: #1abc9c;
}

.green-radio input[type=radio]:disabled + i {
    border-color: #ccc;
}

.green-radio input[type=radio]:checked:disabled + i {
    background-color: #ccc;
}

.printArea {
    display: none;
}

.trendDiv {
    position: relative;
    width: calc(100% - 2px);
    height: 22px;
}

.iocn-top:after {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    right: 1px;
    top: 1px;
    border-radius: 2px;
}

.iocn-top:before {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    left: 1px;
    top: 1px;
    border-radius: 2px;
}

.iocn-bottom:after {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    background: #1abc9c;
    right: 1px;
    bottom: 1px;
    border-radius: 2px;
}

.iocn-bottom:before {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;

    left: 1px;
    bottom: 1px;
    border-radius: 2px;
}

.icon-table-top {
    width: 16px;
    height: 2px;
    margin-top: 1px;
    margin-bottom: 1px;
}

.icon-table-center {
    width: 16px;
    height: 2px;
    margin-bottom: 1px;
}

.icon-table-bottom {
    width: 16px;
    height: 2px;

}
.user-footer-img:hover{
    opacity: .4;
}
.margin-r-35 {
    margin-right: 35px;
}

.icon-bg-hs:after {
    background: #dfe3e9;
}

.icon-bg-hs:before {
    background: #dfe3e9;
}

.icon-bg-le:before {
    background: #1abc9c;
}

.icon-bg-le:after {
    background: #1abc9c;
}

.icon-bg-hs1 {
    background: #dfe3e9;
}

.icon-bg-le1 {
    background: #1abc9c;
}

.pop-page-content {
    width: 391px;
    height: 279px;
    background-color: #ffffff;
    position: absolute;
    right: 110px;
    z-index: 10;
}

.pop-page-content .header-page {
    background-color: #FAFAFA;
    height: 42px;
    position: relative;
    padding-left: 12px;
    font-size: 14px;
    line-height: 42px;
    text-align: left;
}

.pop-page-content .header-page:after {
    position: absolute;
    right: 50px;
    top: -10px;
    content: '';
    font-size: 0;
    line-height: 0;
    border-width: 10px;
    border-color: #ffffff;
    border-top-width: 0;
    border-style: dashed;
    border-bottom-style: solid;
    border-left-color: transparent;
    border-right-color: transparent;
}

.pop-page-content .pop-item {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
    width: 391px;
    height: 234px;
    overflow: auto;
}

.pop-page-content .pop-item .pop-list {
    color: #1abc9c;
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-se {
    background-color: #1abc9c;
    color: #ffffff;
    width: 54px;
    font-size: 12px;
    height: 23px;
    margin-left: 10px;
    cursor: pointer;
    border-radius: 4px;
    display: inline-block;
    line-height: 23px;
    text-align: center;
}

.pop-page-content .pop-item .pop-list .pop-icon {
    background-image: linear-gradient(-1deg, #1ebe9e 2%, #30edc7 98%);
    width: 16px;
    height: 16px;
    margin-right: 9px;
    display: inline-block;
    border-radius: 100%;
    margin-left: 12px;
    vertical-align: text-top;
    margin-top: 1px;
}

.pop-page-content .pop-item .pop-list {
    display: flex;
    align-items: center;
    width: 100%;
    padding-top: 15px;
    position: relative;

}

.bggGradientm {
    background: linear-gradient(to bottom, #F2F3F5 0%, #F2F3F5 100%) 20px 0px/ 1px calc((100% + 15px)) no-repeat;
}

.bgGradientGreen {
    background: linear-gradient(to bottom, #1abc9c70 0%, #F2F3F5 100%) 20px 0px/ 1px calc((100% + 15px)) no-repeat;
}

/*.pop-page-content .pop-item .pop-list:after{*/
/*position: absolute;*/
/*left: 19px;*/
/*bottom: -50%;*/
/*height: 74%;*/
/*width: 2px;*/
/*content: '';*/
/*background-color: #F2F3F5;*/
/*}*/
.pop-page-content .pop-item .pop-list .icon-not-active {
    background: #c2cad4;
    width: 16px;
    display: inline-block;
    font-size: 10px;
    text-align: center;
    margin-left: 12px;
    margin-right: 9px;
    color: #E7E9ED;
    height: 16px;
    border-radius: 100%;
}

.pop-page-content .pop-item .pop-list .pop-date {
    margin-right: 25px;
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-se, .pop-page-content .pop-item .pop-list .pop-text {
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-text {
    width: 126px;
    display: inline-block;
}

.font-14 {
    font-size: 14px;
}

/* 单独定义table列表组件单元格宽度 begin
 * why？
 * 因为table列表组件每次更新数据需要重新跑js计算单元格宽度效率低，容易造成死循环！所以放弃组件自己计算而改为手动预设！
 */
.zui-table-view .cell-m {
    width: 50px!important;
}

.zui-table-view .cell-s {
    width: 100px!important;
}

.zui-table-view .cell-l {
    width: 150px!important;
}

.zui-table-view .cell-xl {
    width: 200px!important;
}

.zui-table-view .cell-xxl {
    width: 300px!important;
}


/*单独定义table列表组件单元格宽度 end*/
.vertical-sub {
    vertical-align: sub;
}

.confirm-title {
    background-color: #1abc9c;
    font-size: 16px;
    color: #fff;
    height: 42px;
    padding-left: 20px;
    line-height: 42px;
    border-bottom: 1px solid #eee;
    overflow: hidden;
    border-radius: 2px 2px 0 0;
}

.confirm-content {
    position: relative;
}

.confirm-mad {
    padding: 15px 10px 10px;
    text-align: center;
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.confirm-row {
    width: 100%;
    text-align: center;
    display: flex;
    margin-bottom: 15px;
    justify-content: flex-end;
}

.confirm-btn {
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid transparent;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    white-space: nowrap;
    vertical-align: middle;
    text-align: center;
    font-weight: 400;
    touch-action: manipulation;
    margin: 0 10px;
    cursor: pointer;
    user-select: none;
    background-color: #fff;
    color: #767d85;
    position: relative;
    width:88px;
    height:36px;
}
.confirm-btn.confirm-primary {
    color: #fff;
    background-color: #1ABC9C;
}

.confirm-btn.confirm-primary-b {
    color: #1ABC9C;
    border: 1px solid #1ABC9C;
}
.confirm-height{
    height: auto;
}
.table-hovers-filexd-r{
    /*border-left: none !important;*/
}
.table-hovers-filexd-l{
    /*border-right: none !important;*/
}
.zui-table-view .zui-table-fixed .zui-table-header{
    /*border: none;*/
}

/*同组标记 begin*/
.tz-start,
.tz-center,
.tz-stop{
    position: relative;
}
.tz-start::before,
.tz-center::before,
.tz-stop::before{
    position: absolute;
    content: '';
    right: 5px;
    width: 5px;
    border-right: 1px solid #000000;
}
.tz-start::before{
    bottom: -7px;
    top: 14px;
    border-top: 1px solid #000000;
}
.tz-center::before{
    bottom: -7px;
    top: -7px;
}
.tz-stop::before{
    bottom: 14px;
    top: -7px;
    border-bottom: 1px solid #000000;
}
/*同组标记 end*/
/*.loadingPage {*/
/*position: absolute;*/
/*top: 50%;*/
/*left: 50%;*/
/*transform: translate(-50%,-50%);*/
/*}*/
/*.loading-page{*/
/*position: fixed;*/
/*top: 0;*/
/*left: 0;*/
/*width: 100%;*/
/*height: 100%;*/
/*background-color: #fff;*/
/*z-index: 11111111111;*/
/*}*/
[v-cloak] {  display: none;}
.ft-14{
    font-size: 14px;
}
.font-12{
    font-size: 12px;
}
.font-16{
    font-size: 16px;
}
.font-18{
    font-size: 18px;
}


/*  */
.position_block{
    position: relative;
    width: 100%;
    display: block;
}

.ft-14{
    font-size: 14px;
}
.font-12 {
    font-size: 12px;
}
.zan-border {
    border: 1px solid #eee;
    line-height: 40px;
}
.font-16{
    font-size: 16px;
}
.font-18{
    font-size: 18px;
}
.justifyAround{
    justify-content: space-around;
}
.whiteSpace{
    white-space: nowrap;
}
input:-webkit-autofill{
    -webkit-box-shadow: 0 0 0px 50px #ffffff inset !important;
}
@media only screen and (max-width: 1024px) {
    .zui-table-view tr th div,.zui-table-view tr td div{
        overflow: hidden !important;
    }
}
.xtmktreediv:hover,.tree_tem1 .tree_text1:hover{
    color: #1abc9c;
    background: rgb(230,246,243);
}

.zui-textarea{
    line-height: 20px;
    padding: 7px 10px;
    resize: none;
}
.imgHover:hover{
    opacity: .9;
}
.flex_items {
    align-items: center;
}
.bdfp:before{
    content: '';
    width: 21px;
    height: 21px;
    background-image: url("/newzui/pub/image/bdfp.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
}
.butt-hover:hover *,
.butt-hover:hover::after,
.butt-hover:hover::before{
    opacity: .6;
}
.tong-search .padd-l-40{
    padding-left: 33px!important;
}
.tong-search .padd-l-54{
    padding-left: 47px!important;
}

.alignItems{
    align-items: center;
}
/*公用组件表格布局*/
/*jieduan-box*/
/*作用域父级名称*/
.jieduan-box .icon-border {
    position: absolute;
    width: 100%;
    height: 1px;
    background: #ffffff;
    transform: rotate(9deg);
    bottom: 15px;
}
.jieduan-box .fenlei {
    position: absolute;
    left: 22px;
    bottom: 0;
    z-index: 11;
    font-size:12px;
    color:#333333;
    text-align:center;
}
.jieduan-box .jieduan {
    position: absolute;
    right: 22px;
    z-index: 11;
    top: 0;
    font-size:12px;
    color:#333333;
    text-align:center;
}
.flex-box{
    display: flex;
}
.flex-box-b{
    display: flex;
    flex-direction: column;
}
.flex-one{
    flex: 1;
}
.jieduan-box .item{
    width:298px;
    padding: 13px;
    min-height:45px;
    border-left:1px solid transparent;
    border-bottom: 1px solid transparent;
    border-right:1px solid #e9eee6;
}
.jieduan-box .item-border{
    border-top:1px solid #e9eee6;
}

.bg-box{
    border-left:1px solid #e9eee6;
}
.rol-bg{
    background: #edf2f1;

}
.jieduan-box .border-bottom-b{
    border-bottom:1px solid #e9eee6;
}
.jieduan-box .item-header{
    background:#edf2f1;
    height: 36px;
    padding: 0;
    line-height: 36px;
    min-height: 36px;
}
/*临床路径业务组件css代码*/
.jieduan-box .absolute {
    position: absolute;
    background: #ffffff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.19);
    border-radius: 4px;
    width: 140px;
    z-index: 1111;
}
.jieduan-box li.active{
    background: rgba(26,188,156,0.08);
    border: 1px solid #1abc9c;
    box-shadow: 0 0 6px 0 rgba(26,188,156,0.45);
}
.jieduan-box .absolute li {
    border-top: 1px solid #e7eaee;
    width: 100%;
    height: 37px;
    display: flex;
    align-items: center;
    padding-left: 13px;
    cursor: pointer;
    font-size: 14px;
    color: #2f4050;
}
.jieduan-box .absolute li:hover{
    opacity: .4;
}
.jieduan-box .absolute li .icon-Image {
    width: 15px;
    height: 16px;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    display: inline-block;
    margin-right: 5px;
}

.icon-border {
    position: absolute;
    width: 100%;
    height: 1px;
    background: #ffffff;
    transform: rotate(9deg);
    bottom: 15px;
}

.fenlei {
    position: absolute;
    left: 22px;
    bottom: 0;
    z-index: 11;
    font-size: 12px;
    color: #333333;
    text-align: center;
}

.jieduan {
    position: absolute;
    right: 22px;
    z-index: 11;
    top: 0;
    font-size: 12px;
    color: #333333;
    text-align: center;
}

.flex-box {
    display: flex;
}

.flex-box-b {
    display: flex;
    flex-direction: column;
}

.flex-one {
    flex: 1;
}

/*.item {*/
/*width: 298px;*/
/*padding: 13px;*/
/*min-height: 45px;*/
/*border-right: 1px solid #e9eee6;*/
/*padding-left: 7px;*/
/*}*/

.item-border {
    border-top: 1px solid #e9eee6;
}

.rol-title {
    width: 140px;
    display: flex;
    min-width: 140px;
    position: relative;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #e9eee6;
    border-top: 1px solid #e9eee6;
}

.bg-box {
    border-left: 1px solid #e9eee6;
}

.rol-bg {
    background: #edf2f1;

}

.border-bottom-b {
    border-bottom: 1px solid #e9eee6;
}

.item-header {
    background: #edf2f1;
    height: 36px;
    padding: 0;
    line-height: 36px;
    min-height: 36px;
}

.item-backgroundImage {
    width: 17px;
    height: 16px;
    background-position: center center;
    background-size: contain;
    display: inline-block;
    vertical-align: bottom;
    float: left;
    margin-right: 10px;
    margin-top: 4px;
}

.vertical-align {
    vertical-align: bottom;
}

.flex-checked {
    width: 10%;
    display: inline-block;
    padding-left: 13px;
}

.flex-day {
    display: inline-block;
    width: 90%;
    float: right;
    vertical-align: middle;
}

.item-text {
    display: inline-block;
    width: 75%;
}

.item-checked {
    display: inline-block;
    text-align: center;
    float: left;
    margin-right: 8px;
}

.box-hover {
    background: #ffffff;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.30);
    border-radius: 4px;
    width: 650px;
    max-height: 340px;
}

.header-cqyz {
    position: absolute;
    right: -42px;
    top: 1px;
    box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.20);
    transform: rotate(36deg);
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    overflow: hidden;
    width: 130px;
    height: 35px;
    line-height: 35px;
    background-color: #4B9BFD;
}

.title-bold {
    font-size: 16px;
    color: #3a3a3a;
    text-align: center;
    font-weight: bolder;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 34px;
}

.title-color {
    color: #333333;
    margin-right: 20px;
    margin-bottom: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.flex-icon{
    background: #eeeeee;;
    border: 2px solid #ffffff;
    width: 26px;
    height: 26px;
    z-index: 10;
    position: absolute;
    left: 0;
    border-radius: 100%;
    color:#d5d5d5;
    display: flex;
    justify-content: center;
    align-items: center;
}
.flex-icon-active{
    background: #1abc9c;
    color: #fff;
}
.flex-icon-done{
    background:#72ddc8;
}
.flex-icon-bg:before{
    content: '';
    background-image: url("/newzui/pub/image/dgk.png");
    width: 14px;
    height: 14px;
    display: inline-block;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
}
.flex-box-icon{
    width: 26px;
    cursor: pointer;
    height: 26px;
    display: inline-block;
    position: relative;
}

.flex-text {
    font-size: 14px;
    color:#c7c7c7;
}
.flex-text-active{
    color: #1abc9c;
}
.flex-text-done{
    color: #72ddc8;
}

.flex-date {
    opacity: 0.5;
    font-size: 10px;
    color: #3a3a3a;
}
.flex-zx-active{
    font-size:10px;
    color:#ff4532;
}
.flex-border-radius{
    position: relative;
}
.flex-border-radius:before{
    position: absolute;
    left: -50%;
}
.flex-border-radius:after{
    position: absolute;
    right:50%;
}
.flex-border-radius:before,.flex-border-radius:after{
    width: 100%;
    top: 18%;
    height:4px;
    position: absolute;
    display: inline-block;
    background:#eeeeee;
    content: '';
}
.border-radius-active:before,.border-radius-active:after{
    background:#1abc9c;
    z-index: 3;
    opacity: .4;
}
.roll-title-type{

    height: 36px;
    line-height: 36px;
    min-height: 36px;
}
.flex-box-title-type{
    padding: 0;
    width: 45px;
    min-width: 45px;
    border-top: 1px solid #e9eee6;
    border-bottom: 1px solid #e9eee6;
    text-align: center;
    border-right: 1px solid #e9eee6;
}
.template-over{
    flex: 1;
    overflow: auto;
}
.roll-title-bg-ed{
    background: #edf2f1;
}
.roll-title-bg-ff{
    background-color: #ffffff;
}
.roll-title-bor-bottom{
    border-bottom: 1px solid #e9eee6;
}
.roll-title-padd{
    padding: 13px;
}
.zt-background-image{
    background-image: url("/newzui/pub/image/dgh.png");
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    width: 20px;
    margin-top: 6px;
    height: 14px;
}
.roll-green{
    margin-top: 3px;
}
.lj-yw-icon{
    position: relative;
}
.lj-yw-icon:before,.lj-yw-icon-zk:before{
    width: 20px;
    height: 17px;
    display: inline-block;
    margin-right: 6px;
    background-image: url("/newzui/pub/image/zlj.png");
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    content: '';
}
.lujing-box{
    position: absolute;
    width: 99%;
    top: 0;
    z-index: 111;
}
.lj-yw-icon:after{
    width: 20px;
    height: 17px;
    display: inline-block;
    margin-right: 6px;
    vertical-align: sub;
    float: right;
    background-image: url("/newzui/pub/image/zk.png");
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
    content: '';
}
.lj-yw-box{
    background:#fdfbf8;
    border:1px solid #e8e2dc;
    box-shadow:0 0 8px 0 rgba(15,33,29,0.15);
    border-radius:2px;
    padding: 6px;
    border-bottom: 1px dashed  #e8e2dc;
    border-top: none;
}
.lj-ye-text{
    font-size:16px;
    color:#757c83;
    padding-left: 36px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    height: 26px;
    align-items: center;
    margin-bottom: 4px;
    line-height:19px;
}
.lj-yw-title{
    font-size:18px;
    cursor: pointer   ;
    color:#f2a654;
    font-weight: 600;
}
.li-yw-active:after{
    animation-name: icon;
    animation-iteration-count: 1;
    animation-timing-function:linear;
    animation-duration: 300ms;
    animation-fill-mode: forwards;
}
.icon-active:after{
    animation-name: icon-active;
    animation-iteration-count: 1;
    animation-timing-function:linear;
    animation-duration: 0s;
    animation-fill-mode: forwards;
}
@keyframes icon {
    from{
        transform: rotate(0deg)
    }
    to{
        transform: rotate(90deg)
    }
}
@keyframes icon-active {
    from{
        transform: rotate(0deg)
    }
    to{
        transform: rotate(90deg)
    }
}
.doActive{
    background: rgba(191, 199, 195, .2);
}
.lj-zx-icon:after{
    content: '';
    background-image: url("/newzui/pub/image/dqzx.png");
    width:62px;
    height:14px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
}
/*滚动区域*/
.over-auto{
    overflow: auto;
}


#jyxm_icon .switch {
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    margin: 0 22%;
}
#jyxm_icon .switch>input {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 45px;
    height: 25px;
    margin: 0;
    cursor: pointer;
    opacity: 0;
}
#jyxm_icon .switch>label {
    display: block;
    padding: 5px 0 5px 35px;
    margin: 0;
    font-weight: 400;
    line-height: 20px;
}
#jyxm_icon .switch>label:after {
    position: absolute;
    top: 5px;
    left: 0;
    display: block;
    width: 40px;
    margin-top: auto;
    z-index: auto;
    text-align: left;
    text-indent: 3px;
    background: #dfe1e5;
    height: 20px;
    pointer-events: none;
    content: ' ';
    border: 1px solid #ddd;
    border-radius: 10px;
    -webkit-transition: all .4s cubic-bezier(.175,.885,.32,1);
    -o-transition: all .4s cubic-bezier(.175,.885,.32,1);
    transition: all .4s cubic-bezier(.175,.885,.32,1);
}
#jyxm_icon .switch>label:before{
    position: absolute;
    top: 5px;
    left: 0;
    display: block;
    line-height: 22px;
    width: 40px;
    margin-top: auto;
    z-index: auto;
    background: #dfe1e5;
    height: 20px;
    text-align: left;
    text-indent: 23px;
    pointer-events: none;
    content: '关';
    color: #fff;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 10px;
    -webkit-transition: all .4s cubic-bezier(.175,.885,.32,1);
    -o-transition: all .4s cubic-bezier(.175,.885,.32,1);
    transition: all .4s cubic-bezier(.175,.885,.32,1);
}

#jyxm_icon .switch>label:after {
    top: 6px;
    width: 18px;
    height: 18px;
    background-color: #fff;
    border-color: #ccc;
    border-radius: 9px;
    -webkit-box-shadow: rgba(0,0,0,.05) 0 1px 4px, rgba(0,0,0,.12) 0 1px 2px;
    box-shadow: rgba(0,0,0,.05) 0 1px 4px, rgba(0,0,0,.12) 0 1px 2px;
}
#jyxm_icon .switch>input:checked+label:after {
    left: 21px;
    border-color: #fff;
}
#jyxm_icon .switch>input:checked+label:before {
    background-color: #1abc9c;
    border-color: #1abc9c;
    color: #fff;
    content:'开';
    text-align: left;
    text-indent: 3px;
    line-height: 22px;
    font-size: 12px;
}

/*下拉菜单 .dropdown*/
/** 表单样式 from: **/
.zui-row {
    width: 100%;
}
.zui-row:after,
.zui-row:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}
.zui-inline,
.zui-input-inline,
.zui-select-inline {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    /*margin-right: 10px;*/
}
.zui-form-label {
    display: block;
    padding: 6px 10px 6px 0;
    line-height: 20px;
    width: 110px;
    font-weight: 400;
    text-align: right;
    color: #a7b1c2;
    font-size: 14px;
}
.zui-input,
.zui-textarea {
    display: block;
    width: 100%;
    /*padding: 0 10px;*/
    height: 36px;
    line-height: 36px;
    border: 1px solid #e6e6e6;
    background-color: #fff;
    color: #354052;
    font-size: 14px;
    border-radius: 4px;
    /*margin-left: 5px;*/
}
.zui-input.isError,
.zui-textarea.isError {
    background-color: #fbe2e2;
    border-color: #e84d3d;
}
.zui-input.border-none,
.zui-textarea.border-none {
    border-color: transparent;
}
.zui-input:focus,
.zui-textarea:focus {
    border-color: #1ABC9C;
    background-color: #fff;
}
.zui-input:focus.border-none,
.zui-textarea:focus.border-none {
    border-color: transparent;
}
.zui-input:disabled,
.zui-textarea:disabled {
    background-color: #f8f8f8;
    color:#757c83;
}
.zui-input::-webkit-input-placeholder,
.zui-textarea::-webkit-input-placeholder {
    color: #a7b1c2;
}
.zui-input:-moz-placeholder,
.zui-textarea:-moz-placeholder {
    color: #a7b1c2;
}
.zui-input::-moz-placeholder,
.zui-textarea::-moz-placeholder {
    color: #a7b1c2;
}
.zui-input:-ms-input-placeholder,
.zui-textarea:-ms-input-placeholder {
    color: #a7b1c2;
}
.zui-input[readonly],
.zui-textarea[readonly] {
    cursor: pointer;
}
.zui-select-inline {
    width: 100%;
    /*width: 120px;*/
}
.zui-select-inline .zui-input {
    /*padding-right: 20px;*/
    cursor: pointer;
}
.iconClass{
    position: absolute;
    z-index: 20;
    zoom: 1;
    top:0;
    right: 0;
    width: 20px;
    height: 100%;
    text-align: center;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
}
.iconClass:after {
    content: "\f0d7";
    font-family: 'FontAwesome';
    z-index: 20;
    zoom: 1;
    text-align: center;
    color: #a7b1c2;
}
.zui-date .datenox {
    display: inline-block;
    position: absolute;
    z-index: 20;
    zoom: 1;
    top: 50%;
    left: 6px;
    width: 16px;
    height: 16px;
    margin-top: -8px;
    text-align: center;
    color: #1ABC9C;
    cursor: pointer;
}
.zui-date .zui-input {
    padding-left: 24px;
}
.zui-form:after,
.zui-form:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}
.zui-form .zui-inline {
    float: left;
    margin-bottom: 8px;
    padding: 0 20px 0 110px;
}
.zui-form .zui-input-inline {
    width: 100%;
}
.zui-form .zui-form-label {
    position: absolute;
    z-index: 10;
    zoom: 1;
    left: 0;
    top: 0;
}
.zui-form .select-right .zui-input-inline {
    padding-right: 50px;
}
.zui-form .select-right .zui-select-inline {
    position: absolute;
    z-index: 100;
    zoom: 1;
    top: 0;
    right: 12px;
    max-width: 48px;
}
.zui-input-item .zui-select-inline {
    float: left;
    padding-left: 2px;
}
.zui-input-item .zui-select-inline:nth-child(1) {
    padding-left: 0;
}
.validate,
.tipError {
    position: absolute;
    z-index: 10;
    zoom: 1;
    display: inline-block;
    right: -20px;
    top: 50%;
    color: #f00;
    width: 20px;
    text-align: center;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    margin-top: -10px;
}
.tipError {
    position: absolute;
    z-index: 20;
    zoom: 1;
    right: -24px;
    font-size: 14px;
    color: #f96868;
    background-color: #fff;
    cursor: pointer;
}





input[type=checkbox].green + label {
    display: inline-block;
    padding-left: 18px;
    height: 18px;
    line-height: 18px;
    position: relative;
    cursor: pointer;
    color: #a7b1c2;
    vertical-align: middle;
}

input[type=checkbox].green + label:before {
    content: " ";
    width: 16px;
    height: 16px;
    line-height: 16px;
    position: absolute;
    z-index: 2;
    zoom: 1;
    top: 50%;
    left: 0;
    margin-top: -8px;
    border: 1px #d2d2d2 solid;
    background-color: #fff;
    font-size: 12px;
    border-radius: 2px;
    text-align: center;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

input[type=checkbox].green:checked + label:before {
    content: "\f192";
    background-color: #1ABC9C;
    border-color: #1ABC9C;
    color: #fff;
}

input[type=checkbox].green {
    display: none;
}

::placeholder {
    color: rgba(53, 64, 82, .3) !important;
}
.tree_text1 {
    display: inline-block;
    padding: 0 7px;
    cursor: pointer;
    border-radius: 4px;
}
/*.tree_text1:hover{*/
    /*color: #1abc9c;*/
/*}*/
.toggleIMg {
    width: 21px;
    height: 18px;
}

.tree_tem1 ul {
    padding-left: 22px;
}

.tree_tem1 label {
    float: left;
    margin: 7px 6px 0 0;
}

#xtmktree {
    width: 20%;
    padding-top: 13px;
    border: 1px solid #eee;
    margin: 0;
}

.bold {
    color: rgba(51,51,51,0.86);
}

.xtmktreediv {
    cursor: pointer;
    /*height: 31px;*/
    line-height: 31px;
}
body .selectGroup table tr:hover {
    background: #edfaf7 !important;
    background-color: #edfaf7;
}
.zui-select-group .fixed {
    background: none !important;
    overflow: auto;
}
.zui-select-group table tr:first-child {
    background: #edf2f1;
    color: #333333;
}
.zui-select-group .fixed table {
    background: none !important;
}

.zui-select-group table {
    overflow: auto;
}
.zui-select-group:hover ::-webkit-scrollbar {
    background: rgba(47, 64, 80, 0.46);
}
body .selectGroup table tr.tableTrSelect {
    /*background-color: #ffe48d !important;*/
    /*background-color: #1abc9c !important;*/
    color: #000000 !important;
}

.patientTable tr {
    width: auto;
}

.left-radio {
    margin-left: 24px;
}

.left-radio input {
    margin-right: 9px;
    vertical-align: unset
}

.left-radio p {
    font-size: 14px;
    color: #9fa9ba;
    margin-bottom: 3px;
}

.green-radius + label {
    background: #ffffff;
    border: 1px solid #1abc9c;
    height: 20px;
    width: 20px;
    line-height: 14px;
    display: inline-block;
    margin-right: 9px;
    border-radius: 100%;
    position: relative;
}

input[type=radio] + i {
    border-radius: 100%;
    margin-right: 9px;
}

label {
    font-size: 12px;
    cursor: pointer;
}

.green-radio i {
    font-size: 12px;
    font-style: normal;
    display: inline-block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    vertical-align: middle;
    margin: -2px 2px 1px 0;
    border: #1abc9c 1px solid;
}

.green-radio input[type=radio] {
    display: none;
}

.green-radio input[type=radio]:checked + i {
    background-color: #1abc9c;
}

.green-radio input[type=radio]:disabled + i {
    border-color: #ccc;
}

.green-radio input[type=radio]:checked:disabled + i {
    background-color: #ccc;
}

.printArea {
    display: none;
}

.trendDiv {
    position: relative;
    width: calc(100% - 2px);
    height: 30px;
}

/* flex布局方案begin */
.flex-container{
    display: -ms-flexbox;
    display: -ms-flex;
    display: -moz-box;
    display: -moz-flex;
    display: -o-flex;
    display: -webkit-flex;
    display: -webkit-box;
    display: flex;
}
.flex-dir-rr{ /*主轴为横轴 顺序与文档流相反*/
    -webkit-flex-direction: row-reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse;
}
.flex-dir-c{ /*主轴为纵轴 顺序与文档流相同*/
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -moz-box-orient: vertical;
    -webkit-box-orient: vertical;
}
.flex-dir-cr{ /*主轴为纵轴 顺序与文档流相反*/
    -webkit-flex-direction: column-reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
}
.flex-wrap-w{ /*允许子项目折行显示 顺序与文档流相同*/
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}
.flex-wrap-wr{ /*允许子项目折行显示 顺序与文档流相反*/
    -ms-flex-wrap: wrap-reverse;
    -webkit-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse;
}
.flex-jus-e{ /*子项目在主轴方向上 尾部对齐*/
    -moz-box-pack: end;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
}
.flex-jus-c{ /*子项目在主轴方向上 居中对齐*/
    -moz-box-pack: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}
.flex-jus-sp{ /*子项目在主轴方向上 均匀分布 首尾子项到父容器的距离是子项间距的一半*/
    -moz-box-pack: space-around;
    -webkit-box-pack: space-around;
    -webkit-justify-content: space-around;
    justify-content: space-around;
}
.flex-jus-sp{ /*子项目在主轴方向上 均匀分布 首尾子项到父容器的距离为0*/
    -moz-box-pack: space-between;
    -webkit-box-pack: space-between;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}
.flex-align-s{ /*子项目在侧轴上 头部对齐（宽高不被拉伸）*/
    -moz-box-align: start;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start;
}
.flex-align-e{ /*子项目在侧轴上 尾部对齐（宽高不被拉伸）*/
    -moz-box-align: end;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    align-items: flex-end;
}
.flex-align-c{ /*子项目在侧轴上 居中对齐（宽高不被拉伸）*/
    -moz-box-align: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
}
.flex-align-b{ /*子项目在侧轴上 首行文字的基线对齐（宽高不被拉伸）*/
    -moz-box-align: baseline;
    -webkit-box-align: baseline;
    -webkit-align-items: baseline;
    align-items: baseline;
}
.flex-one{
    -webkit-flex: 1;
    -ms-flex: 1;
    -moz-box-flex: 1;
    -webkit-box-flex: 1;
    flex: 1;
}
.flex-tow{
    -webkit-flex: 2;
    -ms-flex: 2;
    -moz-box-flex: 2;
    -webkit-box-flex: 2;
    flex: 2;
}
.flex-three{
    -webkit-flex: 3;
    -ms-flex: 3;
    -moz-box-flex: 3;
    -webkit-box-flex: 3;
    flex: 3;
}
.flex-four{
    -webkit-flex: 4;
    -ms-flex: 4;
    -moz-box-flex: 4;
    -webkit-box-flex: 4;
    flex: 4;
}
.flex-five{
    -webkit-flex: 5;
    -ms-flex: 5;
    -moz-box-flex: 5;
    -webkit-box-flex: 5;
    flex: 5;
}
.flex-six{
    -webkit-flex: 6;
    -ms-flex: 6;
    -moz-box-flex:6;
    -webkit-box-flex: 6;
    flex: 6;
}
.flex-seven{
    -webkit-flex: 7;
    -ms-flex: 7;
    -moz-box-flex: 7;
    -webkit-box-flex: 7;
    flex: 7;
}
.flex-eight{
    -webkit-flex: 8;
    -ms-flex: 8;
    -moz-box-flex: 8;
    -webkit-box-flex: 8;
    flex: 8;
}
.flex-nine{
    -webkit-flex: 9;
    -ms-flex: 9;
    -moz-box-flex: 9;
    -webkit-box-flex: 9;
    flex: 9;
}
/* flex布局方案end */


/*栅格系统 begin*/
/* 示例代码 没有行的概念  当一行放不下的时候会自动换行排列
<div class="grid-box">
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
	<div class="col-xxl-6"></div>
</div>
*/
.grid-box:after,
.grid-box:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}

.grid-box [class*='col-'] {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    float: left;
}

.grid-box .col-m-1,
.grid-box .col-s-1,
.grid-box .col-l-1,
.grid-box .col-xl-1,
.grid-box .col-xxl-1 {
    width: 8.33333333%;
}

.grid-box .col-m-2,
.grid-box .col-s-2,
.grid-box .col-l-2,
.grid-box .col-xl-2,
.grid-box .col-xxl-2 {
    width: 16.6666667%;
}

.grid-box .col-m-3,
.grid-box .col-s-3,
.grid-box .col-l-3,
.grid-box .col-xl-3,
.grid-box .col-xxl-3 {
    width: 25%;
}
.grid-box .col-mx-5,
.grid-box .col-sx-5,
.grid-box .col-lx-5,
.grid-box .col-xlx-5,
.grid-box .col-xxlx-5 {
    width:20%;
}
.grid-box .col-m-4,
.grid-box .col-s-4,
.grid-box .col-l-4,
.grid-box .col-xl-4,
.grid-box .col-xxl-4 {
    width: 33.33333333%;
}

.grid-box .col-m-5,
.grid-box .col-s-5,
.grid-box .col-l-5,
.grid-box .col-xl-5,
.grid-box .col-xxl-5 {
    width: 41.66666666666667%;
}

.grid-box .col-m-6,
.grid-box .col-s-6,
.grid-box .col-l-6,
.grid-box .col-xl-6,
.grid-box .col-xxl-6 {
    width: 50%;
}

.grid-box .col-m-7,
.grid-box .col-s-7,
.grid-box .col-l-7,
.grid-box .col-xl-7,
.grid-box .col-xxl-7 {
    width: 58.33333333333333%;
}

.grid-box .col-m-8,
.grid-box .col-s-8,
.grid-box .col-l-8,
.grid-box .col-xl-8,
.grid-box .col-xxl-8 {
    width: 66.66666666666667%;
}

.grid-box .col-m-9,
.grid-box .col-s-9,
.grid-box .col-l-9,
.grid-box .col-xl-9,
.grid-box .col-xxl-9 {
    width: 75%;
}

.grid-box .col-m-10,
.grid-box .col-s-10,
.grid-box .col-l-10,
.grid-box .col-xl-10,
.grid-box .col-xxl-10 {
    width: 83.33333333333333%;
}

.grid-box .col-m-11,
.grid-box .col-s-11,
.grid-box .col-l-11,
.grid-box .col-xl-11,
.grid-box .col-xxl-11 {
    width: 91.66666666666667%;
}

.grid-box .col-m-12,
.grid-box .col-s-12,
.grid-box .col-l-12,
.grid-box .col-xl-12,
.grid-box .col-xxl-12 {
    width: 100%;
}

@media only screen and (min-width: 1600px) {
    .grid-box .col-xxl-1 {
        width: 8.33333333%;
    }

    .grid-box .col-xxl-2 {
        width: 16.6666667%;
    }

    .grid-box .col-xxl-3 {
        width: 25%;
    }

    .grid-box .col-xxl-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xxlx-5 {
        width: 20%;
    }

    .grid-box .col-xxl-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-xxl-6 {
        width: 50%;
    }

    .grid-box .col-xxl-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-xxl-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-xxl-9 {
        width: 75%;
    }

    .grid-box .col-xxl-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-xxl-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-xxl-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1600px) {
    .grid-box .col-xl-1 {
        width: 8.33333333%;
    }

    .grid-box .col-xl-2 {
        width: 16.6666667%;
    }

    .grid-box .col-xl-3 {
        width: 25%;
    }

    .grid-box .col-xl-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xlx-5 {
        width: 20%;
    }
    .grid-box .col-xl-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-xl-6 {
        width: 50%;
    }

    .grid-box .col-xl-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-xl-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-xl-9 {
        width: 75%;
    }

    .grid-box .col-xl-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-xl-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-xl-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1366px) {
    .grid-box .col-x-1 {
        width: 8.33333333%;
    }

    .grid-box .col-x-2 {
        width: 16.6666667%;
    }

    .grid-box .col-x-3 {
        width: 25%;
    }

    .grid-box .col-x-4 {
        width: 33.33333333%;
    }
    .grid-box .col-xx-5 {
        width: 20%;
    }
    .grid-box .col-x-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-x-6 {
        width: 50%;
    }

    .grid-box .col-x-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-x-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-x-9 {
        width: 75%;
    }

    .grid-box .col-x-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-x-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-x-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 1024px) {
    .grid-box .col-s-1 {
        width: 8.33333333%;
    }

    .grid-box .col-s-2 {
        width: 16.6666667%;
    }

    .grid-box .col-s-3 {
        width: 25%;
    }

    .grid-box .col-s-4 {
        width: 33.33333333%;
    }
    .grid-box .col-sx-5 {
        width: 20%;
    }
    .grid-box .col-s-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-s-6 {
        width: 50%;
    }

    .grid-box .col-s-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-s-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-s-9 {
        width: 75%;
    }

    .grid-box .col-s-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-s-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-s-12 {
        width: 100%;
    }
}

@media only screen and (max-width: 768px) {
    .grid-box .col-m-1 {
        width: 8.33333333%;
    }

    .grid-box .col-m-2 {
        width: 16.6666667%;
    }

    .grid-box .col-m-3 {
        width: 25%;
    }

    .grid-box .col-m-4 {
        width: 33.33333333%;
    }
    .grid-box .col-mx-5 {
        width: 20%;
    }
    .grid-box .col-m-5 {
        width: 41.66666666666667%;
    }

    .grid-box .col-m-6 {
        width: 50%;
    }

    .grid-box .col-m-7 {
        width: 58.33333333333333%;
    }

    .grid-box .col-m-8 {
        width: 66.66666666666667%;
    }

    .grid-box .col-m-9 {
        width: 75%;
    }

    .grid-box .col-m-10 {
        width: 83.33333333333333%;
    }

    .grid-box .col-m-11 {
        width: 91.66666666666667%;
    }

    .grid-box .col-m-12 {
        width: 100%;
    }
}

/*栅格系统end*/

/*定义绿色虚线边框的卡片式ui结构*/
/* 示例代码
<div class="tab-card">
	<div class="tab-card-header">
		<div class="tab-card-header-title">卡片信息</div>
	</div>
	<div class="tab-card-body">
		这里放内容即可
	</div>
</div>
*/
.tab-card {
    padding: 10px;
    position: relative;
    background-color: #fff;
    margin-bottom: 2px;
}

.tab-card .tab-card-header {
    position: absolute;
    top: 10px;
    left: 20px;
    border-top: 1px solid #fff;
}

.tab-card .tab-card-header .tab-card-header-title {
    padding: 0 15px;
    font-size: 16px;
    margin-top: -11px;
    color: #1abc9c;
}

.tab-card .tab-card-body {
    border: 1px dashed rgba(26, 188, 156, 0.3) !important;
    background: rgba(26, 188, 156, 0.018) !important;
    padding: 20px 10px 10px;
}

/*定义绿色虚线边框的卡片式ui结构end*/

.left-radio {
    margin-left: 24px;
}

.left-radio input {
    margin-right: 9px;
    vertical-align: unset
}

.left-radio p {
    font-size: 14px;
    color: #9fa9ba;
    margin-bottom: 3px;
}

.green-radius + label {
    background: #ffffff;
    border: 1px solid #1abc9c;
    height: 20px;
    width: 20px;
    line-height: 14px;
    display: inline-block;
    margin-right: 9px;
    border-radius: 100%;
    position: relative;
}

input[type=radio] + i {
    border-radius: 100%;
    margin-right: 9px;
}

label {
    font-size: 12px;
    cursor: pointer;
}

.green-radio i {
    font-size: 12px;
    font-style: normal;
    display: inline-block;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    vertical-align: middle;
    margin: -2px 2px 1px 0;
    border: #1abc9c 1px solid;
}

.green-radio input[type=radio] {
    display: none;
}

.green-radio input[type=radio]:checked + i {
    background-color: #1abc9c;
}

.green-radio input[type=radio]:disabled + i {
    border-color: #ccc;
}

.green-radio input[type=radio]:checked:disabled + i {
    background-color: #ccc;
}

.printArea {
    display: none;
}

.trendDiv {
    position: relative;
    width: calc(100% - 2px);
    height: 22px;
}

.iocn-top:after {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    right: 1px;
    top: 1px;
    border-radius: 2px;
}

.iocn-top:before {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    left: 1px;
    top: 1px;
    border-radius: 2px;
}

.iocn-bottom:after {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;
    background: #1abc9c;
    right: 1px;
    bottom: 1px;
    border-radius: 2px;
}

.iocn-bottom:before {
    position: absolute;
    content: '';
    width: 8px;
    height: 8px;

    left: 1px;
    bottom: 1px;
    border-radius: 2px;
}

.icon-table-top {
    width: 16px;
    height: 2px;
    margin-top: 1px;
    margin-bottom: 1px;
}

.icon-table-center {
    width: 16px;
    height: 2px;
    margin-bottom: 1px;
}

.icon-table-bottom {
    width: 16px;
    height: 2px;

}
.user-footer-img:hover{
    opacity: .4;
}
.margin-r-35 {
    margin-right: 35px;
}

.icon-bg-hs:after {
    background: #dfe3e9;
}

.icon-bg-hs:before {
    background: #dfe3e9;
}

.icon-bg-le:before {
    background: #1abc9c;
}

.icon-bg-le:after {
    background: #1abc9c;
}

.icon-bg-hs1 {
    background: #dfe3e9;
}

.icon-bg-le1 {
    background: #1abc9c;
}

.pop-page-content {
    width: 391px;
    height: 279px;
    background-color: #ffffff;
    position: absolute;
    right: 110px;
    z-index: 10;
}

.pop-page-content .header-page {
    background-color: #FAFAFA;
    height: 42px;
    position: relative;
    padding-left: 12px;
    font-size: 14px;
    line-height: 42px;
    text-align: left;
}

.pop-page-content .header-page:after {
    position: absolute;
    right: 50px;
    top: -10px;
    content: '';
    font-size: 0;
    line-height: 0;
    border-width: 10px;
    border-color: #ffffff;
    border-top-width: 0;
    border-style: dashed;
    border-bottom-style: solid;
    border-left-color: transparent;
    border-right-color: transparent;
}

.pop-page-content .pop-item {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.25);
    width: 391px;
    height: 234px;
    overflow: auto;
}

.pop-page-content .pop-item .pop-list {
    color: #1abc9c;
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-se {
    background-color: #1abc9c;
    color: #ffffff;
    width: 54px;
    font-size: 12px;
    height: 23px;
    margin-left: 10px;
    cursor: pointer;
    border-radius: 4px;
    display: inline-block;
    line-height: 23px;
    text-align: center;
}

.pop-page-content .pop-item .pop-list .pop-icon {
    background-image: linear-gradient(-1deg, #1ebe9e 2%, #30edc7 98%);
    width: 16px;
    height: 16px;
    margin-right: 9px;
    display: inline-block;
    border-radius: 100%;
    margin-left: 12px;
    vertical-align: text-top;
    margin-top: 1px;
}

.pop-page-content .pop-item .pop-list {
    display: flex;
    align-items: center;
    width: 100%;
    padding-top: 15px;
    position: relative;

}

.bggGradientm {
    background: linear-gradient(to bottom, #F2F3F5 0%, #F2F3F5 100%) 20px 0px/ 1px calc((100% + 15px)) no-repeat;
}

.bgGradientGreen {
    background: linear-gradient(to bottom, #1abc9c70 0%, #F2F3F5 100%) 20px 0px/ 1px calc((100% + 15px)) no-repeat;
}

/*.pop-page-content .pop-item .pop-list:after{*/
/*position: absolute;*/
/*left: 19px;*/
/*bottom: -50%;*/
/*height: 74%;*/
/*width: 2px;*/
/*content: '';*/
/*background-color: #F2F3F5;*/
/*}*/
.pop-page-content .pop-item .pop-list .icon-not-active {
    background: #c2cad4;
    width: 16px;
    display: inline-block;
    font-size: 10px;
    text-align: center;
    margin-left: 12px;
    margin-right: 9px;
    color: #E7E9ED;
    height: 16px;
    border-radius: 100%;
}

.pop-page-content .pop-item .pop-list .pop-date {
    margin-right: 25px;
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-se, .pop-page-content .pop-item .pop-list .pop-text {
    font-size: 14px;
}

.pop-page-content .pop-item .pop-list .pop-text {
    width: 126px;
    display: inline-block;
}

.font-14 {
    font-size: 14px;
}

/* 单独定义table列表组件单元格宽度 begin
 * why？
 * 因为table列表组件每次更新数据需要重新跑js计算单元格宽度效率低，容易造成死循环！所以放弃组件自己计算而改为手动预设！
 */
.zui-table-view .cell-m {
    width: 50px!important;
}

.zui-table-view .cell-s {
    width: 100px!important;
}

.zui-table-view .cell-l {
    width: 150px!important;
}

.zui-table-view .cell-xl {
    width: 200px!important;
}

.zui-table-view .cell-xxl {
    width: 300px!important;
}


/*单独定义table列表组件单元格宽度 end*/
.vertical-sub {
    vertical-align: sub;
}

.confirm-title {
    background-color: #1abc9c;
    font-size: 16px;
    color: #fff;
    height: 42px;
    padding-left: 20px;
    line-height: 42px;
    border-bottom: 1px solid #eee;
    overflow: hidden;
    border-radius: 2px 2px 0 0;
}

.confirm-content {
    position: relative;
}

.confirm-mad {
    padding: 15px 10px 10px;
    text-align: center;
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.confirm-row {
    width: 100%;
    text-align: center;
    display: flex;
    margin-bottom: 15px;
    justify-content: flex-end;
}

.confirm-btn {
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid transparent;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    white-space: nowrap;
    vertical-align: middle;
    text-align: center;
    font-weight: 400;
    touch-action: manipulation;
    margin: 0 10px;
    cursor: pointer;
    user-select: none;
    background-color: #fff;
    color: #767d85;
    position: relative;
    width:88px;
    height:36px;
}
.confirm-btn.confirm-primary {
    color: #fff;
    background-color: #1ABC9C;
}

.confirm-btn.confirm-primary-b {
    color: #1ABC9C;
    border: 1px solid #1ABC9C;
}
.confirm-height{
    height: auto;
}
.table-hovers-filexd-r{
    border-left: none !important;
}
.table-hovers-filexd-l{
    border-right: none !important;
}
.zui-table-view .zui-table-fixed .zui-table-header{
    /*border: none;*/
}

/*同组标记 begin*/
.tz-start,
.tz-center,
.tz-stop{
    position: relative;
}
.tz-start::before,
.tz-center::before,
.tz-stop::before{
    position: absolute;
    content: '';
    right: 5px;
    width: 5px;
    border-right: 1px solid #000000;
}
.tz-start::before{
    bottom: -7px;
    top: 14px;
    border-top: 1px solid #000000;
}
.tz-center::before{
    bottom: -7px;
    top: -7px;
}
.tz-stop::before{
    bottom: 14px;
    top: -7px;
    border-bottom: 1px solid #000000;
}
/*同组标记 end*/
/*.loadingPage {*/
    /*position: absolute;*/
    /*top: 50%;*/
    /*left: 50%;*/
    /*transform: translate(-50%,-50%);*/
/*}*/
/*.loading-page{*/
    /*position: fixed;*/
    /*top: 0;*/
    /*left: 0;*/
    /*width: 100%;*/
    /*height: 100%;*/
    /*background-color: #fff;*/
    /*z-index: 11111111111;*/
/*}*/
[v-cloak] {  display: none;}
.ft-14{
    font-size: 14px;
}
.font-13 {
    font-size: 13px;
}
.font-12{
    font-size: 12px;
}
.font-16{
    font-size: 16px;
}
.font-18{
    font-size: 18px;
}


/*  */
.position_block{
    position: relative;
    width: 100%;
    display: block;
}

.ft-14{
    font-size: 14px;
}
.font-12 {
    font-size: 12px;
}
.zan-border {
    border: 1px solid #eee;
    line-height: 40px;
}
.font-16{
    font-size: 16px;
}
.font-18{
    font-size: 18px;
}
.justifyAround{
    justify-content: space-around;
}
.whiteSpace{
    white-space: nowrap;
}
input:-webkit-autofill{
    -webkit-box-shadow: 0 0 0px 50px #ffffff inset !important;
}
@media only screen and (max-width: 1024px) {
    .zui-table-view tr th div,.zui-table-view tr td div{
        overflow: hidden !important;
    }
}
.xtmktreediv:hover,.tree_tem1 .tree_text1:hover{
    color: #1abc9c;
    background: rgb(230,246,243);
}

.zui-textarea{
    line-height: 20px;
    padding: 7px 10px;
    resize: none;
}
.imgHover:hover{
    opacity: .9;
}
.flex_items {
    align-items: center;
}
.bdfp:before{
    content: '';
    width: 24px;
    height: 24px;
    background-image: url("/newzui/pub/image/bdfp.png");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
}
.butt-hover:hover *,
.butt-hover:hover::after,
.butt-hover:hover::before{
    opacity: .6;
}
.tong-search .padd-l-40{
    padding-left: 33px!important;
}
.tong-search .padd-l-54{
    padding-left: 47px!important;
}

.alignItems{
    align-items: center;
}
/*公用组件表格布局*/
/*jieduan-box*/
/*作用域父级名称*/
.jieduan-box .icon-border {
    position: absolute;
    width: 100%;
    height: 1px;
    background: #ffffff;
    transform: rotate(9deg);
    bottom: 15px;
}
.jieduan-box .fenlei {
    position: absolute;
    left: 22px;
    bottom: 0;
    z-index: 11;
    font-size:12px;
    color:#333333;
    text-align:center;
}
.jieduan-box .jieduan {
    position: absolute;
    right: 22px;
    z-index: 11;
    top: 0;
    font-size:12px;
    color:#333333;
    text-align:center;
}
.flex-box{
    display: flex;
}
.flex-box-b{
    display: flex;
    flex-direction: column;
}
.flex-one{
    flex: 1;
}
.jieduan-box .item{
    width:298px;
    padding: 13px;
    min-height:45px;
    border-right:1px solid #e9eee6;
}
.jieduan-box .item-border{
    border-top:1px solid #e9eee6;
}
.rol-title{
    width:138px;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    border-right:1px solid #e9eee6;
    border-top:1px solid #e9eee6;
}
.bg-box{
    border-left:1px solid #e9eee6;
}
.rol-bg{
    background: #edf2f1;

}
.jieduan-box .border-bottom-b{
    border-bottom:1px solid #e9eee6;
}
.jieduan-box .item-header{
    background:#edf2f1;
    height: 36px;
    padding: 0;
    line-height: 36px;
    min-height: 36px;
}
