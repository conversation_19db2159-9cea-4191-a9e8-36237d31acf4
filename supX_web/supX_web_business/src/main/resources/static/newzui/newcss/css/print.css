.printShow {
    display: block !important;
}

.printHide {
    display: none !important;
}

.t-values {
    border: 0;
}

.tem {
    border: 0;
}

.drag {
    display: none;
}

/*设置纸张大小和横向*/
@page {
    size: A4;
}

body{
    height: auto;
}

.pop{
    position: absolute;
    height: auto;
}

/*隐藏页脚和页眉*/
nav, aside {
    display: none;
}

button {
    display: none !important;
}

/*.cqyzd, .lsyzd {*/
    /*width: 800px;*/
    /*height: auto;*/
/*}*/
