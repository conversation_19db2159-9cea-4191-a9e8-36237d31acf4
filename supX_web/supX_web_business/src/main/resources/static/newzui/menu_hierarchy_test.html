<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单层级测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-title {
            color: #1abc9c;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin: 8px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        button {
            background: #1abc9c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #16a085;
        }
        
        .menu-preview {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background: #f9f9f9;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .menu-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .menu-level-1 {
            background: #e8f5e8;
            font-weight: bold;
            border-left: 4px solid #1abc9c;
        }
        
        .menu-level-2 {
            background: #f0f8ff;
            margin-left: 20px;
            border-left: 2px solid #3498db;
        }
        
        .menu-level-3 {
            background: #fff8dc;
            margin-left: 40px;
            border-left: 1px solid #f39c12;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #2c3e50;">菜单层级结构测试</h1>
        
        <div class="test-section">
            <div class="test-title">🎯 修复说明</div>
            <div class="info status">
                <strong>问题：</strong>之前所有菜单都混在一起，没有一级菜单的分组，无法区分不同模块的菜单。<br>
                <strong>解决方案：</strong>恢复一级菜单显示，每个模块作为一级菜单，其子菜单作为二级菜单。
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 菜单结构检查</div>
            <button onclick="checkMenuHierarchy()">检查菜单层级</button>
            <button onclick="showMenuPreview()">预览菜单结构</button>
            <button onclick="checkMenuData()">检查菜单数据</button>
            <div id="hierarchyOutput"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 菜单预览</div>
            <div id="menuPreview" class="menu-preview">
                <div class="info status">点击"预览菜单结构"按钮查看菜单层级</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🛠 技术实现</div>
            <div class="code-block">
修改内容：

1. HTML结构修改：
   - 将 moduleMenu 本身作为一级菜单传递给 tree_tem2
   - 不再遍历 moduleMenu.item，而是让 tree_tem2 组件处理子菜单

2. CSS样式优化：
   - 一级菜单：背景色 #fafafa，字体加粗，左边框高亮
   - 二级菜单：左缩进 35px，字体较小，颜色较淡
   - 悬停效果：背景变色，左移动画

3. 数据流程：
   - allModuleMenus 包含所有模块的根菜单
   - 每个模块的 item 数组包含子菜单
   - tree_tem2 组件递归显示菜单层级
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ 预期效果</div>
            <div class="success status">
                <strong>修复后的菜单应该显示为：</strong>
                <ul>
                    <li><strong>一级菜单</strong>：各个功能模块名称（如：门诊管理、住院管理等）</li>
                    <li><strong>二级菜单</strong>：具体功能菜单（如：挂号管理、病历管理等）</li>
                    <li><strong>默认折叠</strong>：一级菜单默认折叠，点击展开二级菜单</li>
                    <li><strong>层级清晰</strong>：通过缩进和样式区分不同层级</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function checkMenuHierarchy() {
            const output = document.getElementById('hierarchyOutput');
            let html = '<div class="code-block">';
            
            try {
                if (typeof window.menuBar !== 'undefined' && window.menuBar.allModuleMenus) {
                    const menus = window.menuBar.allModuleMenus;
                    html += `✅ 找到 ${menus.length} 个模块菜单\n\n`;
                    
                    menus.forEach((menu, index) => {
                        html += `模块 ${index + 1}:\n`;
                        html += `  名称: ${menu.mkmc || '未知'}\n`;
                        html += `  编码: ${menu.mkbm || '未知'}\n`;
                        html += `  类型: ${menu.lx || '未知'}\n`;
                        html += `  子菜单数: ${menu.item ? menu.item.length : 0}\n`;
                        
                        if (menu.item && menu.item.length > 0) {
                            html += `  子菜单示例:\n`;
                            for (let i = 0; i < Math.min(3, menu.item.length); i++) {
                                const subMenu = menu.item[i];
                                html += `    - ${subMenu.mkmc || subMenu.ylmc || '未知'} (${subMenu.lx})\n`;
                            }
                            if (menu.item.length > 3) {
                                html += `    ... 还有 ${menu.item.length - 3} 个子菜单\n`;
                            }
                        }
                        html += '\n';
                    });
                } else {
                    html += '❌ 菜单数据不存在或未加载\n';
                }
            } catch (e) {
                html += `❌ 检查过程出错: ${e.message}\n`;
            }
            
            html += '</div>';
            output.innerHTML = html;
        }
        
        function showMenuPreview() {
            const preview = document.getElementById('menuPreview');
            let html = '';
            
            try {
                if (typeof window.menuBar !== 'undefined' && window.menuBar.allModuleMenus) {
                    const menus = window.menuBar.allModuleMenus;
                    
                    menus.forEach((menu, index) => {
                        // 一级菜单
                        html += `<div class="menu-item menu-level-1" onclick="toggleMenu(${index})">`;
                        html += `📁 ${menu.mkmc || '未知模块'} (${menu.item ? menu.item.length : 0}个子菜单)`;
                        html += `</div>`;
                        
                        // 二级菜单
                        if (menu.item && menu.item.length > 0) {
                            html += `<div id="submenu-${index}" style="display: none;">`;
                            menu.item.forEach((subMenu, subIndex) => {
                                html += `<div class="menu-item menu-level-2">`;
                                html += `📄 ${subMenu.mkmc || subMenu.ylmc || '未知菜单'}`;
                                if (subMenu.item && subMenu.item.length > 0) {
                                    html += ` (${subMenu.item.length}个子项)`;
                                }
                                html += `</div>`;
                                
                                // 三级菜单（如果有）
                                if (subMenu.item && subMenu.item.length > 0) {
                                    subMenu.item.forEach((thirdMenu) => {
                                        html += `<div class="menu-item menu-level-3">`;
                                        html += `📝 ${thirdMenu.mkmc || thirdMenu.ylmc || '未知菜单'}`;
                                        html += `</div>`;
                                    });
                                }
                            });
                            html += `</div>`;
                        }
                    });
                } else {
                    html = '<div class="error status">菜单数据不存在，请确保已登录并加载菜单</div>';
                }
            } catch (e) {
                html = `<div class="error status">预览菜单出错: ${e.message}</div>`;
            }
            
            preview.innerHTML = html;
        }
        
        function toggleMenu(index) {
            const submenu = document.getElementById(`submenu-${index}`);
            if (submenu) {
                submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
            }
        }
        
        function checkMenuData() {
            const output = document.getElementById('hierarchyOutput');
            let html = '<div class="code-block">';
            
            try {
                if (window.menuBar && window.menuBar.allModuleMenus) {
                    html += JSON.stringify(window.menuBar.allModuleMenus, null, 2);
                } else {
                    html += '菜单数据不存在';
                }
            } catch (e) {
                html += `显示菜单数据出错: ${e.message}`;
            }
            
            html += '</div>';
            output.innerHTML = html;
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', function() {
            setTimeout(function() {
                checkMenuHierarchy();
                showMenuPreview();
            }, 1000);
        });
    </script>
</body>
</html>
