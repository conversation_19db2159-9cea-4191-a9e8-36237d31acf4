@CHARSET "UTF-8";


body{
	width: 100%;
	margin: 0;
	padding: 0;
	display: inline-block;
	overflow-x: hidden;
	font-size: 12px;
}

.headMenu{
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #bbb;
    background: linear-gradient(to bottom,#eeeeee,#cccccc 100%);
    margin-bottom: -3px;
}

.headMenu span{
	display: block;
    float: left;
    width: 72px;
    margin: 5px;
    padding: 5px;
    cursor: pointer;
    border: 1px solid #bbb;
    border-radius: 15px;
    background-color: #fff;
    text-align: center;
}

.timeMenu{
    display: inline-block;
    width: 93%;
    margin-top: 10px;
    margin-bottom: -2px;
    padding-left: 20px;
}

.timeMenu div{
    float: left;
    font-size: 12px;
    border-bottom: 0;
}

.bookMarkDiv{
     position: relative;
     height: 20px;
     float: left;
     margin-right: 3px;
     cursor: pointer;
 }

.bookMark{
    transform: scale(1.1, 1.3) perspective(.5em) rotateX(4deg);
    transform-origin: bottom;
    width: 100%;
    height: 20px;
    z-index: 1;
    /*background: linear-gradient(to bottom,#fff,#eee 100%);*/
    /*border-top-left-radius: 5px;*/
    /*border-top-right-radius: 5px;*/
    border: 1px solid #bbb;
}

.bookMarkCon{
    position: relative;
    padding: 1px 5px;
    color: #000000;
    text-align: center;
    z-index: 2;
    margin-top: -20px;
    font-size: 12px;
}

.bookMarkDiv_selected div:first-child{
    background: #1AB394 !important;
    border: 1px solid #1AB394;
}

.bookMarkDiv_selected div:last-child{
    color: #FFFFFF !important;
}

.sortWay{
	display: inline-block;
    border: 1px solid #bbb;
    border-radius: 10px;
    background-color: #fff;
    width: 93%;
    margin-left: 3%;
}

.sortWay>div{
	font-size: 12px;
    float: left;
    padding: 5px;
    margin: 2px 0;
}

.sortWay>div>h2{
	font-size: 12px;
    float: left;
    margin: 0 15px 0 5px;
}

.rightMenu{
	position: relative;
	float: left;
	width: calc(100% - 260px);
}

.patientList{
	min-height: 450px;
	display: inline-block;
    width: 93%;
    margin-top: 5px;
}

#baseInfo{
	width: 80%;
}

#patientInfoTable{
	margin-top: -2px;
}

#patientInfoTable table{
	display: none;
    margin-bottom: 8px;
}

button span{
	font-size: 14px !important;
    margin-right: 4px;
}

.toolMenuDiv_selected{
    background: linear-gradient(to bottom,#ccc,#bbb 100%);
}

.menuBtuIcon{
	margin-left: 2px;
	color: #aaa;
}

.hrDiv{
	width: 100%;
    position: absolute;
    margin-top: 12px;
    z-index: -1;
}

table input{
    width: 100%;
    margin: 0;
    border: 0;
}
