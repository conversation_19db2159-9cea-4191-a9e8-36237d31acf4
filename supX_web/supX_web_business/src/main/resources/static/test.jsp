<%@ page contentType="text/html; charset=UTF-8" isELIgnored="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>test</title>
<meta content="" name="keywords" />
<meta content="" name="description" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link href="../scripts/plugin/easyui/themes/${skin}/easyui.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../scripts/jquery-1.7.1.min.js"></script>
<script type="text/javascript" src="../scripts/plugin/easyui/jquery.easyui.min.js"></script>
<script type="text/javascript" src="../scripts/plugin/easyui/easyui-lang-zh_CN.js"></script>
<script type="text/javascript" src="../scripts/ie-placeholder.js"></script>
</head>
<body class="easyui-layout" id="filter">
	<div data-options="region:'north',border:false" style="height: 77px;">
		<div class="searchColumn">
			<div class="keySearch">
				<dl class="keyShow">
					<dt>会员名</dt>
					<dd>
						<input type="text" name="fName" id="fName" />
					</dd>
				</dl>
				<dl class="keyShow">
					<dt>状态</dt>
					<dd>
						<select name="fStatus" id="fStatus" class="easyui-combobox" style="width: 200px; height: 27px;">
							<option value="">全部</option>
							<option value="0">正常</option>
							<option value="1">停用</option>
						</select>
					</dd>
				</dl>
				<button class="fl grayBtn" onclick="memberInfoComponent.userDataGrid.formQry()">查询</button>
				<br class="cb" />
			</div>
			<p class="shrink" onClick="resizeNorth();">收起搜索栏</p>
		</div>
	</div>
	<div data-options="region:'center',border:false">
		<table id="userDataGrid">
		</table>
	</div>
</body>
<script type="text/javascript" src="../scripts/ds_main.js"></script>
<script type="text/javascript" src="../scripts/ds_util.js"></script>
<script>
	var ajaxTools = new QM.ajax();
	var memberInfoComponent = {
		userDataGrid : null,
		getQueryParams : function() {
			var fName = $("#fName").val();
			var fStatus = $("#fStatus").combobox("getValue");
			return {
				"reqUrl" : "memberInfo",
				"reqMethod" : "queryMemberInfoList",
				"fName" : fName,
				"fStatus" : fStatus
			}
		},
		init : function() {
			var columns = [ {
				field : 'fName',
				title : '会员名',
				width : 100
			},{
				field : 'fPhone',
				title : '手机号',
				width : 100
			},{
				field : 'fPassword',
				title : '密码',
				width : 100
			},{
				field : 'fEmail',
				title : '邮箱',
				width : 100
			},{
				field : 'fLoginTime',
				title : '上次登录时间',
				width : 140,
				formatter : function(value, row, index) {
					return formatDate14(value);
				}
			},{
				field : 'fRegisterTime',
				title : '注册时间',
				width : 140,
				formatter : function(value, row, index) {
					return formatDate14(value);
				}
			},{
				field : 'fStatus',
				title : '状态',
				width : 120,
				formatter : function(value, row, index) {
					if (value == "0")
						return "<font color='green'>正常</font>";
					else
						return "<font color='red'>已停用</font>";
				}
			}];
			var toolbars = {};
			toolbars.newBtns = [
					{
						iconCls : 'icon-ok',
						text : '启用',
						handler : function() {
							var selRows = $("#userDataGrid").datagrid("getSelections");
							if (!selRows || selRows.length != 1) {
								QM.dialog.showFailedDialog("请选择要启用的记录！");
								return;
							} else if (selRows[0].fStatus == 0) {
								QM.dialog.showFailedDialog("该记录已经是启用状态！");
								return;
							} else {
								top.QM.dialog.showConfirmDialog("是否启用?",
									function(flg) {
										if (flg) {
											memberInfoComponent.updateState("0",selRows[0].fMemId);
										}
									});
							}
						}
					},
					'-',
					{
						iconCls : 'icon-no',
						text : '停用',
						handler : function() {
							var selRows = $("#userDataGrid").datagrid(
									"getSelections");
							if (!selRows || selRows.length != 1) {
								QM.dialog.showFailedDialog("请选择要停用的记录！");
								return;
							} else if (selRows[0].fStatus == 1) {
								QM.dialog.showFailedDialog("该记录已经是停用状态！");
								return;
							} else {
							  top.QM.dialog.showConfirmDialog("是否停用?",
								function(flg) {
									if (flg) {
										memberInfoComponent.updateState("1",selRows[0].fMemId);
								}
							  });
							}
						}
					} ];
			toolbars.btns = [ "ADD", "EDIT", "DELETE" ];
			toolbars.urls = {
				"ADD" : GLOBAL_INFO.CONTEXTPATH
						+ "/dsomp/member/member_add.jsp",
				"EDIT" : GLOBAL_INFO.CONTEXTPATH
						+ "/dsomp/member/member_edit.jsp"
			};
			toolbars.getPKConds = this.getPKConds;
			this.userDataGrid = new QM.dataGrid("userDataGrid", {
				remoteSort : false,
				sortOrder : 'asc',
				columns : [ columns ]
			}, toolbars, this.getQueryParams);
			this.userDataGrid.init();
		},
		getPKConds : function(selRow) {
			return {
				"queryStr" : "fMemId="+ encodeURI(selRow.fMemId),
				"queryJson" : {
					"fMemId" : selRow.fMemId,
					"reqUrl" : "memberInfo",
					"reqMethod" : "deleteMemberInfo"
				}
			};
		},
		updateState : function(state, id) {
			ajaxTools.singleReq({
				data : {
					"reqUrl" : "memberInfo",
					"reqMethod" : "updateMemberInfo",
					"fMemId" : id,
					"fStatus" : state
				},
				success : function(ret) {
					if (ret.a == GLOBAL_INFO.SUCCESS_CODE) {
						if (state == 0) {
							top.QM.dialog.showSuccessDialog("启用成功!");
						} else {
							top.QM.dialog.showSuccessDialog("停用成功!");
						}
						top.QM.dialog.winCallback = function() {
							memberInfoComponent.userDataGrid.formQry();
						}
						top.QM.dialog.doWinCallback(true);
					} else {
						if (state == 0)
							top.QM.dialog.showFailedDialog("启用失败!");
						else
							top.QM.dialog.showFailedDialog("停用失败!");
					}
				}.bind(this)
			});
		}
	}
	$(document).ready(function() {
		memberInfoComponent.init();
	});
</script>
</html>
