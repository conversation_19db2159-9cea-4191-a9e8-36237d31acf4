//    var testWs="能获取这个！";
window.header = new Vue({
    mixins: [dic_transform,baseFunc,tableBase,mformat],
    el: ".header",
    data: {
        userInfo: {},
        userList:[],
        userId: null,
        menuTitle: null,
        showSearch: false,
        searchUrl: "pub/image/search_g.png",
        showMod: false,
        name: true,
        zzbr: true,
        noticeShow: false,
        remainShow: false,
        noticeList: [],
        heartTime: null,
        userName: null,
        socket: null,
        wsurl: null,
        ksbm:null,
        jszgdm:null,
        ksrq:'',
        jsrq:'',
        timeID:'',
    },
    mounted:function () {
        var myDate=new Date();
        this.ksrq = this.fDate(myDate.setDate(myDate.getDate()-7), 'date')+' 00:00:00';
        this.jsrq = this.fDate(new Date(), 'date')+' 23:59:59';
    },
    watch:{
        menuTitle:function(){
            if(this.menuTitle=='门诊医生站'){
                this.initPopData()
            }else{
                clearInterval(this.timeID)
            }
        },
    },
    created: function () {
        // z
//        	this.getWsconfig();
//        	//webSocket
//            setTimeout(function(){
//            	if(window.WebSocket == "undefined"){
//                    malert('您的浏览器不支持webSocket，会影响消息推送功能，建议更换更高版本的浏览器');
//                    return false;
//                }else{
//                	header.createWebSocket();
//                }
//            },1000);
    },
    methods: {
        initPopData:function(){
            if(!this.timeID){
                header.getPopData()
            }
            this.timeID=setInterval(function(){
                header.getPopData()
            },1800000)
        },
        sxzz12: function(){
            var  url='http://44.18.0.77:8888/TransferServer/saveVerifyAndTriageAndDirect';

        },
        //   getOne:function(){
        getOne:function(index,item){
            var urlToke ='http://44.18.0.77:8888/sxzz-api/TransferServer/EncString';
            var pra={encstrencryption:jgbm+new Date().getTime().toString()};
            this.$http.post(urlToke,JSON.stringify(pra)).then(function (data) {
                if(data.status == 200){
                    var param={
                        timestamp:new Date().getTime().toString(),
                        token:data.data.data,
                        accesscode:jgbm,
                        trans_date:getTodayDate(),
                        orgcode:jgbm,
                        serialnumber:item.serialnumber,
                        deptcode:this.userInfo.ksbm,
                        deptname:this.userInfo.ksmc,
                        doctorcode:this.userInfo.czybm,
                        doctorname:this.userInfo.czyxm,
                        appointmenttype:'0'
                    };
                                        url='http://44.18.0.77:8888/sxzz-api/TransferServer/saveVerifyAndTriageAndDirect';
                    this.$http.post(url,JSON.stringify(param)).then(function (data) {
                        if(data.status==200){
                            malert("接诊成功");
                            this.getPopData();
                        };
                    });

                }
            });
        },


        getPopData: function(){
            var  url='http://44.18.0.77:8888/sxzz-api/TransferServer/selectQueryVerify';

            var urlToke ='http://44.18.0.77:8888/sxzz-api/TransferServer/EncString';
            var tokenn='';
            var pra={encstrencryption:jgbm+new Date().getTime().toString()};
            this.$http.post(urlToke,JSON.stringify(pra)).then(function (data) {

                if (data.status == 200) {

                    var param={
                        timestamp:new Date().getTime().toString(),
                        token:data.data.data,
                        accesscode:jgbm,
                        trans_date:getTodayDate(),
                        starttime:this.ksrq,
                        endtime :this.jsrq,
                        orgcode:jgbm
                    };
                    var json = JSON.stringify(param);
                    this.$http.post(url,json).then(function (data) {
                                                if (data.body.status == 200) {
                            header.userList=data.body.data;
                        } else {
                            // malert("上传数据失败" + data.body.c);
                        }
                    })



                } else {
                    // malert("上传数据失败" + data.body.c);
                }
            })



        },
        getJsgn:function(){
            var json={
                rybm:userId,
                ksbm:this.ksbm,
            }
            $.getJSON('/actionDispatcher.do?reqUrl=New1XtwhQxwhJs&types=queryGnByczy&json=' + JSON.stringify(json), function (data) {
                if (data.a == 0) {
                    console.log("角色加载成功！");
                    console.log(data.d);
                    console.log(data.d.list);
                } else {
                    console.log("角色加载失败！");
                }
            });
        },
        tagShowOne:function () {
            this.remainShow = !this.remainShow;
            this.noticeShow=false
        },
        tagShow: function () {
            this.noticeShow = !this.noticeShow;
            this.remainShow=false
        },
        getUserInfo: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=GetUserInfoAction')
                .then(function (json) {
                    this.userInfo = json.body.d;
                    this.userId = json.body.d.czybm;
                    this.userName = json.body.d.czyxm;
                    this.ksbm=json.body.d.ksbm;
                    this.jszgdm=json.body.d.jszgdm;
                    loadModular.loadModule();
                    header.getJsgn();
                });

        },
        showModular: function () {
            loadModular.isShow = !loadModular.isShow;
        },
        changePsw: function () {
            window.location = "page/changePsw/changePsw.html?czybm=" + this.userId + "&yqbm=" + yqbm + "&yljgbm=" + jgbm
        },
        themStyle: function () {
            window.location = "theme/themeStyle.html?czybm=" + this.userId + "&yqbm=" + yqbm + "&yljgbm=" + jgbm
        },
        toSearch: function () {
            this.showSearch = true;
            this.searchUrl = "pub/image/search.png"
        },
        hideSearch: function () {
            this.showSearch = false;
            this.searchUrl = "pub/image/search_g.png"
        },
        showNotice: function () {

        },
        skipWs: function (item) {
            console.log("进入删除");
            var sendmsg = {
                msgtype: '9',
                ksbm: item.ksbm,
                yljgbm: jgbm,
                yqbm: yqbm,
                msg: "delete",
                toaddress: item.url,
                sbid: item.sbid,
                ylbm: item.ylbm,
                pagename:item.pagename,
            }
            window.top.header.socket.send(JSON.stringify(sendmsg));
            header.topNewPage(item.pagename,item.url)
            sessionStorage.setItem('zyh',item.sbid.split('_')[0])
        },
        createWebSocket: function () {
            $.ajaxSettings.async = false;
            var wsurl = null;
            var parm = {
                page: 1,
                rows: 20,
            }
            $.getJSON("/actionDispatcher.do?reqUrl=Wsconfig&types=query&json=" + JSON.stringify(parm), function (json) {
                if (json.a == "0") {
                    wsurl = json.d.list[0];
                } else {
                    malert("消息通知未配置！");
                    return
                }
            });
            var ver='0';
            this.socket = new WebSocket(wsurl.address + "?userId=" + this.userId + "&jgbm=" + jgbm + "&yqbm=" + yqbm+"&ver="+ver);
            this.socket.onopen = function () {
//                	header.socket.send("start: " + new Date());
                console.log("webSocket已连接");
                // 激活心跳包
                header.heartPack();
            };
            this.socket.onmessage = function (msg) {
                var noticeobj = JSON.parse(msg.data);
                if (noticeobj.type == 'add') {
                    console.log(noticeobj)
                    window.top.header.noticeList.push(noticeobj);
                    window.top.header.noticeShow = true;
                } else {
                    var length = 0;
                    var listbak = window.top.header.noticeList;
                    for (var i = 0; i < listbak.length; i++) {
                        if (listbak[i].sbid == noticeobj.sbid) {
                            window.top.header.noticeList.splice(i - length, 1);
                            length = length + 1;
                            break;
                        }
                    }
                }

            };
            this.socket.onclose = function () {
                header.socket.close();
                header.stopHeartBeat();
                console.log("webSocket已关闭");
            };
            this.socket.onerror = function () {
                header.socket.close();
                header.stopHeartBeat();
                console.log("webSocket已断开");
            };
        },
//            心跳包发送（保活）--每15秒发送一次心跳
        heartPack: function () {
            this.heartTime = setInterval(function () {
                header.socket.send("heartBeat: " + new Date());
            }, 15000)
        },
        //停止心跳包
        stopHeartBeat: function () {
            clearInterval(this.heartTime);
        }
    }
});

var loadModular = new Vue({
    el: '.modularDiv',
    data: {
        isShow: false,
        moduleList: [],
        iconUrl: 'pub/image/menuIcon/'
    },
    methods: {
        loadModule: function () {
            this.$http.get('/actionDispatcher.do?reqUrl=MainAction&czybm=' + header.userId +
                '&types=QueryXtmkCzy').then(function (json) {
                if (json.body.a == 1) {
                    malert("系统模块加载失败");
                } else {
                    // 加载模块
                    this.moduleList = json.body.d;
                    // 加载菜单
                    this.loadMenu(this.moduleList[0].mkbm, this.moduleList[0].mkmc, this.moduleList[0].iconame);
                }
            }, function (error) {
                console.log(error);
            });
        },
        loadMenu: function (id, title, url) {
            leftMenu.showFa = {};
            header.menuTitle = title;
            leftMenu.iconName = this.iconUrl + url;
            this.$http.get('/actionDispatcher.do?reqUrl=MainAction&czybm=' + header.userId + '&types=QueryMkqxCzy&mkbm=' + id).then(function (json) {
                leftMenu.menuData = json.body.d;
                // 加载菜单图标
                for (var i = 0; i < leftMenu.menuData.length; i++) {
                    if (leftMenu.menuData[i].iconame != null && leftMenu.menuData[i].iconame.indexOf('&') != -1) {
                        leftMenu.showFa[i] = true;
                    } else {
                        leftMenu.showFa[i] = false;
                    }
                }
            }, function (error) {
                console.log(error);
            });
            if (leftMenu.isFold == true) leftMenu.menuFolding();
        }
    }
});

window.rightMenu = new Vue({
    el: '.rightMenu',
    mixins: [baseFunc],
    components: {
        'calendar': calendar,
    },
    data: {
        pageLists:[],
        transitionName: 'expand',
        isSelect: null,
        isDone: false,
        isFold: false,
        conTitle: "首页",
        testDate: null,
        isStop: false,
        rSocket: null,
    },
    created: function () {
        //webSocket
        if (window.WebSocket == "undefined") {
            malert('您的浏览器不支持webSocket，会影响消息推送功能，建议更换更高版本的浏览器');
            return false;
        } else {
            setTimeout(function () {
                this.rSocket = header.socket;
                console.log("赋值成功");
                console.log(this.rSocket);
            }, 5000);
        }
    },
    methods: {
        removePage: function (index) {
            this.pageLists.splice(index, 1);
            if (index <= this.isSelect && this.isSelect != 0) this.isSelect--;
        },
        move: function (direction) {
            if (this.isStop) return;
            this.isStop = true;
            var width = $(".titleCon")['0'].offsetWidth;
            var _div = $(".titleCon > div");
            var _last = _div.find("div:last");
            var left = _div['0'].offsetLeft;
            var right = _last['0'].offsetLeft + _last['0'].offsetWidth;
            if (direction == 'left' && right + left > width) {
                _div.animate({left: "-=100px"}, function () {
                    rightMenu.isStop = false;
                });
            } else if (direction == 'right' && left < 0) {
                _div.animate({left: "+=100px"}, function () {
                    rightMenu.isStop = false;
                });
            } else {
                this.isStop = false;
            }
        },
        refresh: function () {
            if (this.isSelect != null) {
                $('#iframeCon iframe').eq(rightMenu.isSelect).attr('src', $('#iframeCon iframe').eq(rightMenu.isSelect).attr('src'));
            }
        }
    }
});

var leftMenu = new Vue({
    el: '.leftMenu',
    data: {
        isPastDay: false,
        isLiSelect: false,
        menuTitle: null,
        iconName: null,
        menuData: [],
        menuLiData: [],
        isFold: false,
        iconNameUrl: 'pub/image/menuIcon/',
        showFa: {}
    },
    methods: {
        loadChildMenu: function (id) {
            var _id = $("#" + id);
            if (_id.css("display") == "none") {
                $(".menu .arrowIcon").removeClass("fa-angle-down").addClass("fa-angle-right");
                _id.slideDown(200).parent().siblings().find("ul").slideUp(200);
                _id.prev('div').addClass("menuSelected").parent().siblings().find("div").removeClass("menuSelected");
                _id.parent().find(".arrowIcon").removeClass("fa-angle-right").addClass("fa-angle-down");
            } else {
                _id.slideUp(200);
                $(".menu div").removeClass("menuSelected");
                _id.parent().find(".arrowIcon").removeClass("fa-angle-down").addClass("fa-angle-right");
            }
        },
        menuFolding: function () {
            this.isFold = !this.isFold;
            var name = $(".header"), mod = $(".modularDiv"), icon = $(".menu img"), indexCon = $("body"),
                menuIcon = $(".menuIcon");
            // var clientWidth = 10400 / document.body.clientWidth + "%";

            if (this.isFold == true) {
                header.name = false;
                $(".leftMenu").animate({left: "-=104px"});
                $(".rightMenu").animate({left: "-=104px"});
                name.animate({left: "-=104px"});
                mod.animate({left: "-=104px"});
                indexCon.animate({width: "+=104px"}, function () {
                    indexCon.css("width", "calc(100% + 104px)");
                });
                icon.css("float", "right").css("margin-right", "20px").css("position", "relative");
                menuIcon.css("float", "right").css("margin-right", "20px").css("position", "relative");
            } else {
                header.name = true;
                icon.css("float", "left");
                $(".leftMenu").animate({left: "+=104px"});
                $(".rightMenu").animate({left: "+=104px"});
                name.animate({left: "+=104px"});
                mod.animate({left: "+=104px"});
                indexCon.animate({width: "+100%"});
                icon.css("margin-right", "10px").css("position", "absolute");
                menuIcon.css("float", "left").css("margin-right", "10px").css("position", "absolute");
            }
        },
        loadCon: function (id, i, name, url, moduleItem) {
                        rightMenu.conTitle = name;
            $("#" + id).find("li").eq(i).addClass("menuLiSelected").siblings().removeClass("menuLiSelected");
            for (var j = 0; j < rightMenu.pageLists.length; j++) {
                if (rightMenu.pageLists[j].url.indexOf(url) != -1) {
                    rightMenu.isSelect = j;
                    return false;
                }
            }
            if( moduleItem && moduleItem.fr == 1 ){
                rightMenu.pageLists.push({"name": name, "url": url+ '&yljgbm=' + this.getQueryVariable('yljgbm') + '&czybm=' + this.getQueryVariable('czybm')+'&ksbm='+header.ksbm});
            }else{
                rightMenu.pageLists.push({"name": name, "url": "page" + url + ".html"});
            }
            rightMenu.isSelect = rightMenu.pageLists.length - 1;
            rightMenu.move("left");
        },
        getQueryVariable: function (variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return (false);
        },
    }
});
header.getUserInfo();
//
//header.createWebSocket();
setTimeout(function () {
    if (window.WebSocket == "undefined") {
        malert('您的浏览器不支持webSocket，会影响消息推送功能，建议更换更高版本的浏览器');
        return false;
    } else {
        header.createWebSocket();
    }
}, 1000);
$(document).click(function () {
    loadModular.isShow = false;
    header.noticeShow = false;
    header.remainShow = false;
});
window.getTime = function (event, type) {
    if (type == 'star') {
        header.ksrq = $(event).val();
    } else if (type == 'end') {
        header.jsrq = $(event).val();
    }
};
