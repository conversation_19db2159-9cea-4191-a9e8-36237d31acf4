<!DOCTYPE html1>
<html>
	<head>
		<meta charset="UTF-8">
		<title></title>
		<iframe id="reportFrame" width="900" height="400" src="http://**************:9081/FR/ReportServer?reportlet=%5B95e8%5D%5B8bca%5D%5B533b%5D%5B751f%5D%5B76f8%5D%5B5173%5D%2F%5B533b%5D%5B751f%5D%5B63a5%5D%5B8bca%5D%5B4eba%5D%5B6b21%5D%5B7edf%5D%5B8ba1%5D.cpt"></iframe>  
		<meta http-equiv="Content-Type" content="text/html" charset="UTF-8" />
		<!--<script type="text/javascript" src="http://*************:9081/FR/ReportServer?op=resource&resource=/com/fr/web/jquery.js"></script>-->   
        <script type="text/javascript" src="http://**************:9081/FR/ReportServer?op=emb&resource=finereport.js"></script>  
	</head>
	<body>
		<script>
			function init(){
				document.getElementById('reportFrame').contentWindow.contentPane.printPreview();
				console.log("init");
			}
			function test(){
				//var printurl = "http://**************:9081/FR/ReportServer?reportlet=%5B95e8%5D%5B8bca%5D%5B53d1%5D%5B7968%5D.cpt&yljgbm=000001&fphm=0000010714";
				var printurl = "http://**************:9081/FR/ReportServer";
				var reportlets="[{reportlet: '%5B95e8%5D%5B8bca%5D%5B533b%5D%5B751f%5D%5B76f8%5D%5B5173%5D%2F%5B533b%5D%5B751f%5D%5B63a5%5D%5B8bca%5D%5B4eba%5D%5B6b21%5D%5B7edf%5D%5B8ba1%5D.cpt'}"
				var config = {
					printUrl : printurl,  
					isPopUp : true, // 是否弹出设置窗口，true为弹出，false为不弹出  
					data :{   
					    reportlets: reportlets  // 需要打印的模版列表  
					},  
					printType : 1, // 打印类型，0为零客户端打印，1为本地打印  
					// 以下为零客户端打印的参数，仅当 printType 为 0 时生效  
					ieQuietPrint : false,// IE静默打印设置 true为静默，false为不静默  
					// 以下为本地打印的参数，仅当 printType 为 1 时生效  
					printerName : 'Microsoft XPS Document Writer v4', // 打印机名  
					pageType: 2, // 打印页码类型：0：所有页，1：当前页，2：指定页  
					pageIndex: '1-3', // 页码范围。当 pageType 为 2 时有效  
					copy: 1, // 打印份数  
				};  
				FR.doURLPrint(config);  
				
				
				/*FR.doNativePrint({
				//是否弹窗    
				isPopUp:false,    
				//打印指定页面1,3,5-6    
				index:0,    
				//打印份数    
				copies:1,    
				//指定打印机    
				printerName:"Adobe PDF",    
				//需要打印的报表url    
				url:printurl    
				});    */
			}
		</script>
		<input type="button" value="init" onclick="init()" />
		<input type="button" value="PDF测试打印" onclick="test()"/>
		
	</body>
</html>

