<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <!--<link rel="shortcut icon" href="pub/image/logo.ico">-->
    <link rel="shortcut icon" href="/login/favicon.ico">
    <link rel="stylesheet" href="pub/css/head.css">
    <link rel="stylesheet" href="pub/css/date.css">
    <script type="application/javascript" src="pub/top.js"></script>
    <title>院级信息管理平台</title>
</head>

<body>
<!-- 头部 -->
<div class="header">
    <div class="hosName">
        <div v-show="name" v-text="userInfo.yljgmc" style="margin-top: 10px;"></div>
        <img v-show="!name" src="pub/image/logo.png">
    </div>

    <div class="modTitle" @click.stop="showModular()">
        <div v-text="menuTitle"></div>
        <div v-show="showMod" class="fa fa-sort-desc"></div>
        <div v-show="!showMod" class="fa fa-sort-desc"></div>
    </div>
    <div class="account">
        <div class="scheduleDiv" @click.stop="tagShowOne">
            <div class="schedule"></div>
            <span>待办事项</span>
            <div class="msg">3</div>
            <div class="triangle-notice" v-show="remainShow"></div>
            <transition name="notice-fade">
                <div class="notice" v-show="remainShow">
                    <p class="noticeTitle">
                        <img src="pub/image/notice.png">待办事项
                        <span>更多</span>
                    </p>
                    <div v-for="item in noticeList" v-text="item.title"></div>
                    <img v-show="noticeList.length == 0" src="/pub/image/notNotice.png">
                </div>
            </transition>
        </div>
        <div class="messageDiv" @click.stop="tagShow">
            <div class="message"></div>
            <span>通知</span>
            <div class="msg">1</div>
            <div class="triangle-notice" v-show="noticeShow"></div>
            <transition name="notice-fade">
                <div class="notice" v-show="noticeShow">
                    <p class="noticeTitle">
                        <img src="pub/image/notice.png">通知
                        <span>更多</span>
                    </p>
                    <div v-for="item in noticeList" v-text="item.title" @click="skipWs(item)"></div>
                    <img v-show="noticeList.length == 0" src="/pub/image/notNotice.png">
                </div>
            </transition>
        </div>
        <div class="user">
            <div class="imgDiv">
                <img src="pub/image/girl.png">
            </div>
            <span v-text="userInfo.czyxm"></span>
            <div class="triangle"></div>
            <div class="userInfo">
                <div><img src="pub/image/person.png">个人资料</div>
                <div @click="changePsw()"><img src="pub/image/password.png">密码管理</div>
                <div @click="themStyle()"><span class="fa fa-photo" style="margin-right: 8px"></span>主题风格</div>
                <div><img src="pub/image/loginOut.png">退出</div>
            </div>
        </div>
    </div>
    <div class="cut"></div>
    <div class="con-search">
        <transition name="left-fade">
            <input :autofocus="showSearch" @blur="hideSearch()" v-if="showSearch" placeholder="请输入搜索内容…">
        </transition>
        <div @click="toSearch()">
            <img class="searchIcon" :src="searchUrl">
            <span v-show="!showSearch">搜索</span>
        </div>
    </div>

    <model  :model-show="false"
            v-if="menuTitle=='门诊医生站'" :title="'转诊病人列表'">
        <div class="bqcydj_model flex-container flex-align-c flex-wrap-w">
            <span>日期：</span>
            <input  v-model="ksrq" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" style="height: 28px;width: 90px;margin: 0 3px;" onchange="getTime(this, 'star')" id="startTime"/>
            至
            <input v-model="jsrq" onclick="WdatePicker({ dateFmt: 'yyyy-MM-dd HH:mm:ss' })" style="height: 28px;width: 90px;margin: 0 3px;"  onchange="getTime(this, 'end')" id="endTime"/>
            <button @click="getPopData()"><span class="fa fa-refresh"></span>查询</button>
            <div class="" >
                <table class="patientTable" cellspacing="0" cellpadding="0">
                    <thead >
                    <tr>
                        <th class="">序号</th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>年龄</th>
                        <th>转诊申请号</th>
                    </tr>
                    </thead>
                    <tr v-for="(item, $index) in userList" @dblclick="getOne($index,item)"
                        :class="[{'tableTrSelect':isChecked[$index]},{'tableTr': $index%2 == 0}]">
                        <th class="" v-text="$index+1"></th>
                        <td v-text="item.patientname"></td>
                        <td v-text="brxb_tran[item.gender]"></td>
                        <td v-text="item.age"></td>
                        <td v-text="item.serialnumber"></td>
                    </tr>
                </table>
            </div>
        </div>
    </model>
</div>

<!-- 主菜单 -->
<div class="modularDiv" v-show="isShow">
    <div v-for="item in moduleList" :key="item.mkbm" @click="loadMenu(item.mkbm, item.mkmc, item.iconame)">
        <!--<img :src="iconUrl+item.iconame" />-->
        <span style="font-size: 16px" v-text="item.mkmc"></span>
    </div>
</div>

<!-- 正文 -->
<div id="indexCon">
    <div class="leftMenu">
        <div class="menu">
            <li style="height: 42px">
                <i :class="{'foldIcon_green': isFold}" @click="menuFolding" class="fa fa-reorder foldIcon"></i>
            </li>
            <li v-for="(item, $index) in menuData" :key="item.mkbm">
                <div @click="loadChildMenu(item.mkbm)">
                    <img :src="iconNameUrl+item.iconame" v-if="showFa[$index] == false" />
                    <span class="menuIcon fa" :class="item.iconame.split('&')[0]" :style="{color: item.iconame.split('&')[1]}"
                          v-if="showFa[$index]"></span>
                    <span v-show="!isFold" class="menu-font" v-text="item.mkmc"></span>
                    <span v-show="!isFold" class="fa fa-angle-right arrowIcon"></span>
                </div>
                <ul :id="item.mkbm" v-show="!isFold">
                    <li v-for="(itemLi, $index) in item.userYlqxModel" :key="item.mkbm" @click="loadCon(item.mkbm,$index,itemLi.ylmc,itemLi.url,itemLi)"
                        v-text="itemLi.ylmc">
                    </li>
                </ul>
            </li>
        </div>
    </div>

    <div class="rightMenu">
        <div class="contextTitle">
            <div class="conTitle" v-text="conTitle"></div>
            <div class="titleList">
                <div class="leftHide" @click="move('right')" onselectstart="return false;"><img src="pub/image/pve.png">
                </div>
                <div class="titleCon">
                    <div>
                        <div class="tabCon homePage">首页</div>
                        <transition-group name="expand">
                            <div class="tabCon" v-for="(item, $index) in pageLists" :key="item" :transition="'expand'"
                                 :class="{'bgChange':isSelect == $index}" @click="isSelect = $index">
                                <span class="fa fa-times-circle bookMarkRemove" @click.stop="removePage($index)"></span>
                                <span v-text="item.name"></span>
                            </div>
                        </transition-group>
                    </div>
                </div>
                <div @click="refresh()" class="refresh" title="刷新">
                    <img src="/pub/image/refresh.png">
                </div>
                <div class="rightHide" @click="move('left')" onselectstart="return false;"><img src="pub/image/next.png"></div>
            </div>
        </div>

        <div id="iframeCon">
            <!--:model为要操作的值，:model_name为要操作值得名称，:time为是否要操作时分秒，-->
            <!--:section为是否要选择时间段，@result为返回的方法特殊操作可自定义方法-->
            <!--<calendar :model="testDate" :model_name="'testDate'" :time="false"-->
            <!--:section="false" @result="resultFun">-->
            <!--</calendar>-->
            <input id="this_user_id" style="display: none" value="">
            <iframe v-show="isSelect == $index" v-for="(item,$index) in pageLists" :key="item.url" :src="item.url">
                <script type="application/javascript" src="/pub/top.js"></script>
            </iframe>
        </div>
    </div>
</div>

<!--尾部-->
<!--<div class="tail"></div>-->
</body>
<script type="text/javascript" src="index.js"></script>
<script>
    $("#this_user_id").val(window.location.search);
</script>
<script type="text/javascript">
    var offlinePram = null;
    // 监听断网事件
    window.addEventListener("offline", function (e) {
        offlinePram = his2vMalert("本机网络连接已断开！请检查本机网线是否插好！", 'top', 'defeadted');
    });
    // 监听联网事件
    window.addEventListener("online", function (e) {
        if (offlinePram !== null) {
            his2vDeleteMalert(offlinePram.sid, offlinePram.type);
            offlinePram = null;
        }
    });
</script>

</html>
