package com.supx.web;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Duration;
import java.time.Instant;

/**
 * Web业务服务启动类
 * <AUTHOR>
 * @date 2024-01-01
 */
@EnableDubbo
@ServletComponentScan
@EnableTransactionManagement
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.supx.web"}, exclude = {RedisAutoConfiguration.class}) // 只扫描实际存在的包，提高启动速度
public class WebBusinessApplication {
    public static void main(String[] args) {
        // 设置系统属性，优化启动性能
        System.setProperty("zookeeper.jmx.log4j.disable", "true");
        System.setProperty("spring.jmx.enabled", "false");           // 禁用JMX，减少启动时间
        System.setProperty("spring.main.lazy-initialization", "false"); // 确保非懒加载

        Instant start = Instant.now();  // 记录启动时间
        SpringApplication app = new SpringApplication(WebBusinessApplication.class);
        app.setAllowCircularReferences(true);
        // 设置启动优化参数
        app.setLazyInitialization(false);                           // 禁用懒初始化
        app.run(args);
        Instant end = Instant.now();    // 记录结束时间
        long duration = Duration.between(start, end).getSeconds();
        printStartupBanner(duration);
    }

    private static void printStartupBanner(long durationMillis) {
        final String RESET = "\u001B[0m";
        final String GREEN = "\u001B[32m";
        final String BLUE = "\u001B[34m";
        String line = "============================================================";
        System.out.println("\n" + line);
        System.out.printf("                 🚀 SupX - %-10s 启动成功\n", "WEB服务");
        System.out.println(line);
        System.out.println(GREEN + " ✅ 应用状态： " + BLUE + "RUNNING" + RESET);
        System.out.println(GREEN + " 🌐 访问地址： " + BLUE + "http://localhost:9082/login.html" + RESET);
        System.out.println(GREEN + " 🧩 当前环境： " + BLUE + "Development" + RESET);
        System.out.println(GREEN + " 🕒 启动耗时： " + BLUE + durationMillis + " s" + RESET);
    }
}
