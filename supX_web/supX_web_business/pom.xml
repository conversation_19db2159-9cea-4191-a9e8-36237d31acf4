<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>supX_web</artifactId>
		<groupId>com.supx.web</groupId>
		<version>0.0.1</version>
	</parent>
	<artifactId>supX_web_business</artifactId>
	<packaging>jar</packaging>
	<name>supX_web_business</name>

	<dependencies>
		<!-- 依赖公共API模块 -->
		<dependency>
			<groupId>com.supx.comm</groupId>
			<artifactId>supX_comm_api</artifactId>
		</dependency>

		<!-- 依赖公共工具模块 -->
		<dependency>
			<groupId>com.supx.comm</groupId>
			<artifactId>supX_comm_util</artifactId>
		</dependency>

		<dependency>
			<groupId>com.supx.web</groupId>
			<artifactId>supX_web_api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.supx.csp</groupId>
			<artifactId>supX_csp_api</artifactId>
		</dependency>
		<!-- Spring Boot Starters -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.tomcat.embed</groupId>
					<artifactId>tomcat-embed-websocket</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<!-- Dubbo Spring Boot Starter -->
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
		</dependency>

		<!-- ZooKeeper Client -->
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-x-discovery</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi</artifactId>
		</dependency>
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi-ooxml</artifactId>
		</dependency>
		<dependency>
		    <groupId>org.apache.directory.studio</groupId>
		    <artifactId>org.dom4j.dom4j</artifactId>
		</dependency>

	    <dependency>
	      <groupId>org.apache.commons</groupId>
	      <artifactId>commons-pool2</artifactId>
	    </dependency>

		<!-- iReport JasperReports -->
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
		</dependency>

		<dependency>
    		<groupId>org.fusesource</groupId>
    		<artifactId>sigar</artifactId>
	   </dependency>

		<!-- tike解析 -->
		<dependency>
    		<groupId>org.apache.tika</groupId>
    		<artifactId>tika-parsers</artifactId>
    		<exclusions>
    			<exclusion>
    				<groupId>org.bouncycastle</groupId>
    				<artifactId>bcprov-jdk15on</artifactId>
    			</exclusion>
    			<exclusion>
    				<groupId>org.bouncycastle</groupId>
    				<artifactId>bcmail-jdk15on</artifactId>
    			</exclusion>
    			<exclusion>
    				<groupId>org.bouncycastle</groupId>
    				<artifactId>bcpkix-jdk15on</artifactId>
    			</exclusion>
    		</exclusions>
		</dependency>

		 <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
        </dependency>

        <!-- 显式添加新版本BouncyCastle依赖 -->
        <dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
		</dependency>

        <!-- 转换文件依赖 -->
        <dependency>
   			<groupId>org.apache.pdfbox</groupId>
    		<artifactId>jbig2-imageio</artifactId>
		</dependency>

		<dependency>
    		<groupId>com.github.jai-imageio</groupId>
    		<artifactId>jai-imageio-core</artifactId>
		</dependency>

		<dependency>
    		<groupId>com.github.jai-imageio</groupId>
    		<artifactId>jai-imageio-jpeg2000</artifactId>
		</dependency>

		  <dependency>
		      <groupId>com.levigo.jbig2</groupId>
		      <artifactId>levigo-jbig2-imageio</artifactId>
		  </dependency>

		<dependency>
   			 <groupId>org.xerial</groupId>
   			 <artifactId>sqlite-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
	</dependencies>

	<build>
		<finalName>supX_web_business</finalName>
		<plugins>
			<!-- Spring Boot Maven Plugin -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.supx.web.WebBusinessApplication</mainClass>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
